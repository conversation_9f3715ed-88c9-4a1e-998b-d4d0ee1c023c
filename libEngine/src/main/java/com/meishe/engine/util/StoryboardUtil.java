package com.meishe.engine.util;

import android.text.TextUtils;
import android.util.Log;

import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsSize;
import com.meicam.sdk.NvsStreamingContext;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.bean.CutData;

import org.w3c.dom.Document;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import java.io.StringReader;
import java.util.HashMap;
import java.util.Map;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

/**
 * author：yangtailin on 2020/6/23 11:00
 */
public class StoryboardUtil {
    private final static String TAG = "StoryboardUtil";
    public final static String STORYBOARD_KEY_SCALE_X = "scaleX";
    public final static String STORYBOARD_KEY_SCALE_Y = "scaleY";
    public final static String STORYBOARD_KEY_ROTATION_Z = "rotationZ";
    public final static String STORYBOARD_KEY_TRANS_X = "transX";
    public final static String STORYBOARD_KEY_TRANS_Y = "transY";

    public static String getImageBackgroundStory(String source, int timelineWidth, int timelineHeight, Map<String, Float> clipTransData) {
        int imageSize = timelineWidth;
        if (imageSize < timelineHeight) {
            imageSize = timelineHeight;
        }
        return"<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<storyboard sceneWidth=\"" + timelineWidth + "\" sceneHeight=\"" + timelineHeight + "\">\t\n" +
                "<track source=\"" + source + "\" width=\"" + imageSize + "\" height=\"" + imageSize + "\" " +
                "clipStart=\"0\" clipDuration=\"1\" repeat=\"true\">\n" +
                "</track>\n" +
                "<track source=\":1\" clipStart=\"0\" clipDuration=\"1\" repeat=\"true\">\n" +
                "<effect name=\"transform\">\n" +
                "<param name=\"scaleX\" value=\"" + clipTransData.get(STORYBOARD_KEY_SCALE_X) + "\"/>\n" +
                "<param name=\"scaleY\" value=\"" + clipTransData.get(STORYBOARD_KEY_SCALE_Y) + "\"/>\n" +
                "<param name=\"rotationZ\" value=\"" + clipTransData.get(STORYBOARD_KEY_ROTATION_Z) + "\"/>\n" +
                "<param name=\"transX\" value=\"" + clipTransData.get(STORYBOARD_KEY_TRANS_X) + "\"/>\n" +
                "<param name=\"transY\" value=\"" + clipTransData.get(STORYBOARD_KEY_TRANS_Y) + "\"/>\n" +
                "</effect>\n" +
                "</track>\n" +
                "</storyboard>";
    }

    /**
     * 图片背景的storybord的拼写操作
     *
     * @param source         图片资源的路径
     * @param timelineWidth  时间线的宽度
     * @param timelineHeight 时间线的高度
     * @return 拼写好的storybord xml
     */
    public static String getImageBackgroundStory(String source, int timelineWidth, int timelineHeight) {
        int imageSize = timelineWidth;
        if (imageSize < timelineHeight) {
            imageSize = timelineHeight;
        }
        return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<storyboard sceneWidth=\"" + timelineWidth + "\" sceneHeight=\"" + timelineHeight + "\">\t\n" +
                "<track source=\"" + source + "\" width=\"" + imageSize + "\" height=\"" + imageSize + "\" " +
                "clipStart=\"0\" clipDuration=\"1\" repeat=\"true\">\n" +
                "</track>\n" +
                "<track source=\":1\" clipStart=\"0\" clipDuration=\"1\" repeat=\"true\">\n" +
                "<effect name=\"transform\">\n" +
                "<param name=\"scaleX\" value=\"" + 1 + "\"/>\n" +
                "<param name=\"scaleY\" value=\"" + 1 + "\"/>\n" +
                "<param name=\"rotationZ\" value=\"" + 0 + "\"/>\n" +
                "<param name=\"transX\" value=\"" + 0 + "\"/>\n" +
                "<param name=\"transY\" value=\"" + 0 + "\"/>\n" +
                "</effect>\n" +
                "</track>\n" +
                "</storyboard>";
    }

    /**
     * 模糊背景的storybord拼写
     *
     * @param timelineWidth
     * @param timelineHeight
     * @param clipPath
     * @param strength
     * @param clipTransData
     * @return
     */
    public static String getBlurBackgroundStory(int timelineWidth, int timelineHeight, String clipPath, float strength,
                                                Map<String, Float> clipTransData) {

        NvsAVFileInfo avFileInfo = NvsStreamingContext.getInstance().getAVFileInfo(clipPath);
        NvsSize dimension;
        if (avFileInfo != null) {
            dimension = avFileInfo.getVideoStreamDimension(0);
        } else {
            dimension = new NvsSize(720, 720);
        }
        Map<String, Float> blurTransData = getBlurTransData(timelineWidth, timelineHeight, dimension.width, dimension.height);
        return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<storyboard sceneWidth=\"" + timelineWidth + "\" sceneHeight=\"" + timelineHeight + "\">\t\n" +
                "<track source=\":1\" clipStart=\"0\" clipDuration=\"1\" repeat=\"true\">\n" +
                "<effect name=\"fastBlur\">\n" +
                "<param name=\"radius\" value=\"" + strength + "\"/>\n" +
                "</effect>\n" +
                "<effect name=\"transform\">\n" +
                "<param name=\"scaleX\" value=\"" + blurTransData.get(STORYBOARD_KEY_SCALE_X) + "\"/>\n" +
                "<param name=\"scaleY\" value=\"" + blurTransData.get(STORYBOARD_KEY_SCALE_Y) + "\"/>\n" +
                "<param name=\"rotationZ\" value=\"" + blurTransData.get(STORYBOARD_KEY_ROTATION_Z) + "\"/>\n" +
                "<param name=\"transX\" value=\"" + blurTransData.get(STORYBOARD_KEY_TRANS_X) + "\"/>\n" +
                "<param name=\"transY\" value=\"" + blurTransData.get(STORYBOARD_KEY_TRANS_Y) + "\"/>\n" +
                "</effect>\n" +
                "</track>\n" +
                "<track source=\":1\" clipStart=\"0\" clipDuration=\"1\" repeat=\"true\">\n" +
                "<effect name=\"transform\">\n" +
                "<param name=\"scaleX\" value=\"" + clipTransData.get(STORYBOARD_KEY_SCALE_X) + "\"/>\n" +
                "<param name=\"scaleY\" value=\"" + clipTransData.get(STORYBOARD_KEY_SCALE_Y) + "\"/>\n" +
                "<param name=\"rotationZ\" value=\"" + clipTransData.get(STORYBOARD_KEY_ROTATION_Z) + "\"/>\n" +
                "<param name=\"transX\" value=\"" + clipTransData.get(STORYBOARD_KEY_TRANS_X) + "\"/>\n" +
                "<param name=\"transY\" value=\"" + clipTransData.get(STORYBOARD_KEY_TRANS_Y) + "\"/>\n" +
                "</effect>\n" +
                "</track>\n" +
                "</storyboard>";
    }

    /**
     * 裁剪的storybord 通过maskGenerator实现
     *
     * @param timelineWidth
     * @param timelineHeight
     * @param regionData     区域数据
     * @return
     */
    public static String getCropperStory(int timelineWidth, int timelineHeight, float[] regionData) {
        if (regionData == null || regionData.length < 8) {
            return null;
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < regionData.length; i++) {
            stringBuilder.append(regionData[i]);
            if (i < regionData.length - 1) {
                stringBuilder.append(",");
            }
        }
        String regionString = stringBuilder.toString();
        return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<storyboard sceneWidth=\"" + timelineWidth + "\" sceneHeight=\"" + timelineHeight + "\">\n" +
                "    <track source=\":1\" clipStart=\"0\" clipDuration=\"1\" repeat=\"true\">\n" +
                "        <effect name=\"maskGenerator\">\n" +
                "            <param name=\"keepRGB\" value=\"true\"/>\n" +
                "            <param name=\"featherWidth\" value=\"0\"/>\n" +
                "            <param name=\"region\" value=\"" + regionString + "\"/>\n" +
                "        </effect>\n" +
                "    </track>\n" +
                "</storyboard>";
    }

    /**
     * Transform2D的storybord拼写。可控制的数据为缩放，旋转，偏移。
     *
     * @param timelineWidth
     * @param timelineHeight
     * @param clipTransData
     * @return
     */
    public static String getTransform2DStory(int timelineWidth, int timelineHeight, Map<String, Float> clipTransData) {
        return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<storyboard sceneWidth=\"" + timelineWidth + "\" sceneHeight=\"" + timelineHeight + "\">\t\n" +
                "<track source=\":1\" clipStart=\"0\" clipDuration=\"1\" repeat=\"true\">\n" +
                "<effect name=\"transform\">\n" +
                "<param name=\"scaleX\" value=\"" + clipTransData.get(STORYBOARD_KEY_SCALE_X) + "\"/>\n" +
                "<param name=\"scaleY\" value=\"" + clipTransData.get(STORYBOARD_KEY_SCALE_Y) + "\"/>\n" +
                "<param name=\"rotationZ\" value=\"" + (-clipTransData.get(STORYBOARD_KEY_ROTATION_Z)) + "\"/>\n" +
                "<param name=\"transX\" value=\"" + clipTransData.get(STORYBOARD_KEY_TRANS_X) + "\"/>\n" +
                "<param name=\"transY\" value=\"" + (-clipTransData.get(STORYBOARD_KEY_TRANS_Y)) + "\"/>\n" +
                "</effect>\n" +
                "</track>\n" +
                "</storyboard>";
    }

    /**
     * 拼写模糊的数据
     * Get blur trans data
     *
     *
     * @param timelineWidth the timeline width
     * @param timelineHeight the timeline height
     * @param width the width
     * @param height the height
     * @return the data
     */
    private static Map<String, Float> getBlurTransData(int timelineWidth, int timelineHeight, int width, int height) {
        Map<String, Float> transData = new HashMap<>();
        float timelineRatio = timelineWidth * 1.0F / timelineHeight;
        float fileRatio = width * 1.0F / height;
        float scale;
        if (fileRatio > timelineRatio) {
            //此时是宽对齐，需要高对齐
            // At this point, it is wide alignment and requires high alignment.
            float scaleBefore = timelineWidth * 1.0F / width;
            scale = timelineHeight * 1.0F / (height * scaleBefore);
        } else {
            //此时是高对齐，需要宽对齐
            // At this point, it is high alignment and requires wide alignment.
            float scaleBefore = timelineHeight * 1.0F / height;
            scale = timelineWidth * 1.0F / (width * scaleBefore);
        }
        transData.put(STORYBOARD_KEY_SCALE_X, scale);
        transData.put(STORYBOARD_KEY_SCALE_Y, scale);
        transData.put(STORYBOARD_KEY_ROTATION_Z, 0F);
        transData.put(STORYBOARD_KEY_TRANS_X, 0F);
        transData.put(STORYBOARD_KEY_TRANS_Y, 0F);
        return transData;
    }

    /**
     * 从模糊的storybord中获取模糊数据
     * @param data storybord xml
     * @return
     */
    public static float getBlurStrengthFromStory(String data) {
        Document document = getDocument(data);
        if (document == null) {
            return -1;
        }
        NodeList effect = document.getElementsByTagName("param");
        if (effect.getLength() == 0) {
            return -1;
        }
        for (int index = 0; index < effect.getLength(); index++) {
            Node item = effect.item(index);

            NamedNodeMap childNodeAttributes = item.getAttributes();
            if (childNodeAttributes == null) {
                continue;
            }
            if (childNodeAttributes.getNamedItem("name") != null && "radius".equals(childNodeAttributes.getNamedItem("name").getNodeValue())) {
                return Float.parseFloat(childNodeAttributes.getNamedItem("value").getNodeValue());
            }
        }
        return -1;
    }

    /**
     * 从storybord中获取资源路径，对应xml中的source字段中的值。
     * @param data storybord xml
     * @return
     */
    public static String getSourcePathFromStory(String data) {
        Document document = getDocument(data);
        if (document == null) {
            return null;
        }
        NodeList track = document.getElementsByTagName("track");
        if (track.getLength() == 0) {
            return null;
        }
        for (int index = 0; index < track.getLength(); index++) {
            Node item = track.item(index);
            NamedNodeMap attributes = item.getAttributes();
            Node name = attributes.getNamedItem("source");
            if (name == null || (":1".equals(name.getNodeValue()))) {
                continue;
            }
            return name.getNodeValue();
        }
        return null;
    }

    /**
     * 裁剪的storybord转化为上层的CatData数据
     * @param cropperStory   storybord xml
     * @param transformStory
     * @param relativeSize
     * @return
     */
    public static CutData parseStoryToCatData(String cropperStory, String transformStory, float[] relativeSize) {

        Document transformDocument = getDocument(transformStory);
        if (transformDocument == null) {
            return null;
        }
        CutData cutData = new CutData();
        NodeList params = transformDocument.getElementsByTagName("param");

        NodeList transStoryboard = transformDocument.getElementsByTagName("storyboard");

        NamedNodeMap transAttributes = transStoryboard.item(0).getAttributes();
        int transSceneWidth = Integer.parseInt(transAttributes.getNamedItem("sceneWidth").getNodeValue());
        int transSceneHeight = Integer.parseInt(transAttributes.getNamedItem("sceneHeight").getNodeValue());

        if (params.getLength() == 0) {
            return null;
        }
        for (int index = 0; index < params.getLength(); index++) {
            Node item = params.item(index);

            NamedNodeMap childNodeAttributes = item.getAttributes();
            if (childNodeAttributes == null) {
                continue;
            }
            if (childNodeAttributes.getNamedItem("name") != null) {
                if (STORYBOARD_KEY_ROTATION_Z.equals(childNodeAttributes.getNamedItem("name").getNodeValue())) {
                    cutData.putTransformData(childNodeAttributes.getNamedItem("name").getNodeValue(),
                            -Float.parseFloat(childNodeAttributes.getNamedItem("value").getNodeValue()));
                } else if (STORYBOARD_KEY_TRANS_Y.equals(childNodeAttributes.getNamedItem("name").getNodeValue())) {
                    cutData.putTransformData(childNodeAttributes.getNamedItem("name").getNodeValue(),
                            -Float.parseFloat(childNodeAttributes.getNamedItem("value").getNodeValue()));
                } else {
                    cutData.putTransformData(childNodeAttributes.getNamedItem("name").getNodeValue(),
                            Float.parseFloat(childNodeAttributes.getNamedItem("value").getNodeValue()));
                }
            }
        }

        Document document = getDocument(cropperStory);
        if (document == null) {
            return cutData;
        }
        NodeList storyboard = document.getElementsByTagName("storyboard");

        NamedNodeMap attributes = storyboard.item(0).getAttributes();
        int sceneWidth = Integer.parseInt(attributes.getNamedItem("sceneWidth").getNodeValue());
        int sceneHeight = Integer.parseInt(attributes.getNamedItem("sceneHeight").getNodeValue());

        NodeList effect = document.getElementsByTagName("param");
        if (effect.getLength() > 0) {
            for (int index = 0; index < effect.getLength(); index++) {
                Node item = effect.item(index);

                NamedNodeMap childNodeAttributes = item.getAttributes();
                if (childNodeAttributes == null) {
                    continue;
                }
                Log.e(TAG, "parseStoryToCatData: value = " + childNodeAttributes.getNamedItem("name").getNodeValue());
                if (childNodeAttributes.getNamedItem("name") != null && "region".equals(childNodeAttributes.getNamedItem("name").getNodeValue())) {
                    String region = childNodeAttributes.getNamedItem("value").getNodeValue();
                    cutData.setRatio(getRationFromRegion(region, sceneWidth, sceneHeight, relativeSize));
                    cutData.setRatioValue(getRatioValueFromRegion(region, sceneWidth, sceneHeight, relativeSize));
                }
            }
        }

        cutData.setIsOldData(transSceneWidth == sceneWidth && transSceneHeight == sceneHeight);

        return cutData;
    }

    private static int getRationFromRegion(String region, int sceneWidth, int sceneHeight, float[] relativeSize) {
        if (TextUtils.isEmpty(region)) {
            return 0;
        }
        String[] split = region.split(",");
        if (split.length != 8) {
            return NvsConstants.AspectRatio.AspectRatio_NoFitRatio;
        }
        float height = (Float.parseFloat(split[3]) - Float.parseFloat(split[5])) / relativeSize[1];
        float width = (Float.parseFloat(split[2]) - Float.parseFloat(split[0])) / relativeSize[0];
        float ratio = (sceneWidth * width) / (sceneHeight * height);
        return CommonData.AspectRatio.getAspect(ratio);
    }

    private static float getRatioValueFromRegion(String region, int sceneWidth, int sceneHeight, float[] relativeSize) {
        if (TextUtils.isEmpty(region)) {
            return 0;
        }
        String[] split = region.split(",");
        float height = (Float.parseFloat(split[3]) - Float.parseFloat(split[5])) / relativeSize[1];
        float width = (Float.parseFloat(split[2]) - Float.parseFloat(split[0])) / relativeSize[0];
        return (sceneWidth * width) / (sceneHeight * height);
    }

    private static Document getDocument(String content) {
        if (TextUtils.isEmpty(content)) {
            return null;
        }
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        Document document = null;
        try {
            DocumentBuilder db = dbf.newDocumentBuilder();
            InputSource is = new InputSource(new StringReader(content));
            document = db.parse(is);
        } catch (Exception e) {
            LogUtils.e("getDocument error:" + e.getMessage());
        }
        return document;
    }
}
