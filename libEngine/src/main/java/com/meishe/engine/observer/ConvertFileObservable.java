package com.meishe.engine.observer;

import android.database.Observable;
import android.os.Looper;

import com.meishe.base.utils.ThreadUtils;
import com.meishe.engine.util.ConvertFileManager;


/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2021/6/11 10:30
 * @Description: 倒放视频片段
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class ConvertFileObservable extends Observable<ConvertFileObserver> {

    /**
     * 转换进度
     * On convert progress.
     *
     * @param progress the progress
     */
    public void onConvertProgress(final float progress) {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            for (int i = mObservers.size() - 1; i >= 0; i--) {
                mObservers.get(i).onConvertProgress(progress);
            }
        } else {
            ThreadUtils.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    for (int i = mObservers.size() - 1; i >= 0; i--) {
                        mObservers.get(i).onConvertProgress(progress);
                    }
                }
            });
        }
    }

    /**
     * 转换完成
     * On convert finish.
     *
     * @param convertParam           the convert param 转化参数
     * @param convertSuccess the convert success  是否成功
     */
    public void onConvertFinish(final ConvertFileManager.ConvertParam convertParam, final boolean convertSuccess) {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            for (int i = mObservers.size() - 1; i >= 0; i--) {
                mObservers.get(i).onConvertFinish(convertParam, convertSuccess);
            }
        } else {
            ThreadUtils.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    for (int i = mObservers.size() - 1; i >= 0; i--) {
                        mObservers.get(i).onConvertFinish(convertParam, convertSuccess);
                    }
                }
            });
        }
    }

    /**
     * 取消转换
     * On convert cancel.
     */

    public void onConvertCancel() {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            for (int i = mObservers.size() - 1; i >= 0; i--) {
                mObservers.get(i).onConvertCancel();
            }
        } else {
            ThreadUtils.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    for (int i = mObservers.size() - 1; i >= 0; i--) {
                        mObservers.get(i).onConvertCancel();
                    }
                }
            });
        }
    }

    @Override
    public void registerObserver(ConvertFileObserver observer) {
        if (!mObservers.contains(observer)) {
            super.registerObserver(observer);
        }
    }

    @Override
    public void unregisterObserver(ConvertFileObserver observer) {
        if (mObservers.contains(observer)) {
            super.unregisterObserver(observer);
        }
    }
}
