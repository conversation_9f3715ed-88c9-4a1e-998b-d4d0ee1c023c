package com.meishe.user.view.iview;

import com.meishe.base.model.IBaseView;
import com.meishe.user.bean.VideoCompileBean;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/8 18:09
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface CloudDraftView extends IBaseView {
    /**
     * On new data back.
     *
     * @param data the data
     */
    void onNewDataBack(List<VideoCompileBean> data);

    /**
     * On more data back.
     *
     * @param data the data
     */
    void onMoreDataBack(List<VideoCompileBean> data);

    /**
     * On data error.
     */
    void onDataError();

    /**
     * On download progress.
     * 下载进度回调
     * @param tag      the tag
     * @param progress the progress
     */
    void onDownloadProgress(String tag, int progress);

    /**
     * Refresh data.
     * 刷新数据
     */
    void refreshData(boolean isByUser);

    /**
     * On delete.
     * 删除回调
     * @param jobId the job id
     */
    void onDelete(String jobId);
}
