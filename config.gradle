ext {

    config = [
            extVersionCode: 68,
            extVersionName: "1.5.1"
    ]
    android = [
            extCompileSdkVersion: 33,
            extBuildToolsVersion: "33.0.1",
            extMinSdkVersion    : 24,
            extTargetSdkVersion : 33,
    ]
    dependencies = [
            //android
            extAndroidTestRunner: 'androidx.test.ext:junit:1.1.1',
            extTestJunit        : 'junit:junit:4.12',
            extTestEspresso     : 'androidx.test.espresso:espresso-core:3.2.0',
            extConstraintLayout : 'androidx.constraintlayout:constraintlayout:1.1.3',
            extAppcompat        : 'androidx.appcompat:appcompat:1.1.0',
            extAnnotation       : 'androidx.annotation:annotation:1.1.0',
            extAppcompatRecycler: 'androidx.recyclerview:recyclerview:1.1.0',
            extSupportDesign    : 'com.google.android.material:material:1.1.0',
            extSwiperefreshlayout          : 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0',
            extMutilDex         : 'androidx.multidex:multidex:2.0.1',
            //google
            room                : 'androidx.room:room-runtime:2.4.0',
            roomComplier        : 'androidx.room:room-compiler:2.4.0',
            extGoogleGson       : 'com.google.code.gson:gson:2.8.5',
            //图片加载库
            extBumptechGlide    : 'com.github.bumptech.glide:glide:4.12.0',
            extGlideAnnotation  : 'com.github.bumptech.glide:compiler:4.12.0',
            //阿里云
            extAliyun           : 'com.aliyun.dpa:oss-android-sdk:+',
            extAliVideo         : 'com.aliyun.video.android:upload:1.6.0',
            //网络库
            extOkhttp           : 'com.squareup.okhttp3:okhttp:3.11.0',
            //友盟
            umengCommon         : 'com.umeng.umsdk:common:9.4.4',
            umengAsms           : 'com.umeng.umsdk:asms:1.4.1',
            umengCrash          : 'com.umeng.umsdk:apm:1.5.2',
            //bugly 其中latest.release指代最新Bugly SDK版本号，也可以指定明确的版本号，例如2.2.0
            buglyCrashSDK       : 'com.tencent.bugly:crashreport:latest.release',
            //其中latest.release指代最新Bugly NDK版本号，也可以指定明确的版本号，例如3.0
            buglyCrashNDK       : 'com.tencent.bugly:nativecrashreport:latest.release',
            //exoplayer
            exoplayer            : 'androidx.media3:media3-exoplayer:1.1.1',
            exoplayerUI          : 'androidx.media3:media3-ui:1.1.1',
    ]
}