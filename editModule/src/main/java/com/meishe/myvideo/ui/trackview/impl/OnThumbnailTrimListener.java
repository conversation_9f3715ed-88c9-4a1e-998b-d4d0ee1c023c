package com.meishe.myvideo.ui.trackview.impl;

import com.meishe.myvideo.ui.bean.ITrackClip;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/4/26 10:04
 * @Description :主轨道缩略图剪裁监听 Main track thumbnail trim listener
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public abstract class OnThumbnailTrimListener {
    /**
     * 缩略图剪裁开始
     * Start trim thumbnail
     *
     * @param trackClip  the trim clip 剪裁的片段
     * @param changeLeft true is change left  ,false change right  真则是剪裁左边，假则是剪裁右边
     */
    public void onThumbnailTrimStart(ITrackClip trackClip, boolean changeLeft) {

    }

    /**
     * 缩略图剪裁中
     * On trim thumbnail trimming
     *
     * @param trackClip  the trim clip 剪裁的片段
     * @param changeLeft true is change left  ,false change right  真则是剪裁左边，假则是剪裁右边
     */
    public void onThumbnailTrim(ITrackClip trackClip, boolean changeLeft) {

    }

    /**
     * 一次缩略图剪裁完成
     * A thumbnail clipping completed
     *
     * @param trackClip  the trim clip 剪裁的片段
     * @param changeLeft true is change left  ,false change right  真则是剪裁左边，假则是剪裁右边
     */
    public void onThumbnailTrimComplete(ITrackClip trackClip, boolean changeLeft) {

    }
}
