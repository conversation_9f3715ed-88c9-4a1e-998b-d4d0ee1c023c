<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView
        android:contentDescription="@null"
        android:id="@+id/iv_cover"
        android:layout_width="@dimen/dp_px_270"
        android:layout_height="@dimen/dp_px_270"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:scaleType="centerCrop" />

    <com.meishe.user.view.CustomProgress
        android:id="@+id/progress_bar"
        android:layout_width="@dimen/dp_px_72"
        android:layout_height="@dimen/dp_px_72"
        app:layout_constraintTop_toTopOf="@+id/iv_cover"
        app:layout_constraintBottom_toBottomOf="@+id/iv_cover"
        app:layout_constraintLeft_toLeftOf="@+id/iv_cover"
        app:layout_constraintRight_toRightOf="@+id/iv_cover"
        android:max="100"
        android:progress="50"
        app:progressWidth="@dimen/dp_px_6"
        app:progressBackgroundColor="@color/white_5"
        app:progressColor="@color/color_ffff365e"
        android:visibility="gone"/>
    <TextView
        android:id="@+id/tv_title"
        app:layout_constraintTop_toTopOf="@+id/iv_cover"
        app:layout_constraintLeft_toRightOf="@+id/iv_cover"
        android:layout_width="wrap_content"
        android:layout_marginStart="@dimen/dp_px_30"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp_px_39"
        android:textColor="@color/white"/>

    <TextView
        android:id="@+id/tv_create_at"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:layout_constraintLeft_toLeftOf="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_marginTop="@dimen/dp_px_30"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp_px_33"
        android:textColor="@color/color_80ffffff"/>

    <TextView
        android:id="@+id/tv_size"
        app:layout_constraintBottom_toTopOf="@+id/tv_duration"
        app:layout_constraintLeft_toLeftOf="@+id/tv_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_create_at"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp_px_33"
        android:textColor="@color/color_80ffffff"/>

    <TextView
        android:id="@+id/tv_duration"
        app:layout_constraintLeft_toLeftOf="@+id/tv_title"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp_px_33"
        android:textColor="@color/color_ccffffff"/>
    
    <ImageView
        android:contentDescription="@null"
        android:id="@+id/iv_download"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginEnd="@dimen/dp_px_45"
        android:padding="@dimen/dp_px_15"
        android:src="@mipmap/ic_cloud_compile_download_select"
        android:layout_width="@dimen/dp_px_75"
        android:layout_height="@dimen/dp_px_75"/>

    <ImageView
        android:contentDescription="@null"
        android:id="@+id/iv_delete"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:padding="@dimen/dp_px_15"
        android:layout_marginEnd="@dimen/dp_px_135"
        android:layout_width="@dimen/dp_px_75"
        android:src="@mipmap/ic_cloud_compile_delete_select"
        android:layout_height="@dimen/dp_px_75"/>

    <TextView
        android:id="@+id/tv_download"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/iv_delete"
        android:layout_marginStart="@dimen/dp_px_45"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_27"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>
</androidx.constraintlayout.widget.ConstraintLayout>