package com.meishe.myvideo.view;

import android.content.Context;
import android.widget.TextView;

import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.adapter.BaseSelectAdapter;
import com.meishe.myvideo.adapter.ThemeAdapter;
import com.meishe.myvideo.view.base.BaseConfirmMenuView;
import com.meishe.myvideo.view.presenter.BaseConfirmPresenter;
import com.meishe.myvideo.view.presenter.ThemePresenter;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/30 14:36
 * @Description :主题菜单 The theme menu.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MYThemeMenu extends BaseConfirmMenuView {

    public MYThemeMenu(Context context) {
        super(context);
        mAssetsTypeTab.setVisibility(GONE);
    }

    @Override
    public BaseSelectAdapter<IBaseInfo> getAdapter() {
        if (mAdapter == null) {
            mAdapter = new ThemeAdapter();
        }
        return mAdapter;
    }

    @Override
    protected void onItemClicked(IBaseInfo baseInfo, boolean isSelected) {
        mPresenter.onItemClicked(baseInfo, isSelected);
    }


    @Override
    protected void setContentText(TextView textView) {
        textView.setText(R.string.main_menu_name_theme);
    }

    @Override
    protected BaseConfirmPresenter<? extends BaseConfirmMenuView> getPresenter() {
        ThemePresenter presenter = new ThemePresenter();
        presenter.attachView(this);
        return presenter;
    }

    /**
     * Update view.
     * 更新视图
     *
     * @param data the data
     */
    public void updateView(List<IBaseInfo> data) {
        mAdapter.setNewData(data);
    }
}
