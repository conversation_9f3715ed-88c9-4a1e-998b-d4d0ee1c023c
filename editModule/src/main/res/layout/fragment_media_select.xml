<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tl="http://schemas.android.com/apk/res-auto"
    android:background="@color/black"
    android:orientation="vertical">

    <com.meishe.third.tablayout.SlidingTabLayout
        android:id="@+id/tl_select_media"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_100"
        tl:tl_indicator_height="@dimen/dp_px_4"
        tl:tl_indicator_width="@dimen/dp_px_30"
        tl:tl_tab_space_equal="true"
        tl:tl_textSelectColor="@color/white"
        tl:tl_textSize="@dimen/sp_px_36"
        tl:tl_textUnselectedColor="@color/white_5" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/vp_select_media"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />


    <TextView
        android:id="@+id/tv_start_edit"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_113"
        android:layout_gravity="bottom"
        android:layout_margin="@dimen/dp_px_45"
        android:background="@drawable/bg_round_corners_solid_red_fc"
        android:gravity="center"
        android:text="@string/start_marking"
        android:textColor="@color/start_edit_text"
        android:textSize="@dimen/sp_px_48"
        android:visibility="gone" />

</LinearLayout> 