<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_speed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_px_45"
        android:layout_marginRight="@dimen/dp_px_45"
        android:layout_marginBottom="@dimen/dp_px_15"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_36" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <View
            android:id="@+id/view_background"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_px_6"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="@dimen/dp_px_45"
            android:layout_marginRight="@dimen/dp_px_45"
            android:background="@color/color_ff363636" />

        <LinearLayout
            android:id="@+id/view_mask"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_px_24"
            android:layout_gravity="center_vertical"
            android:orientation="horizontal" />


        <View
            android:id="@+id/view_shadow"
            android:layout_width="@dimen/dp_px_45"
            android:layout_height="@dimen/dp_px_45"
            android:background="@drawable/nv_compile_progress_selected" />
    </FrameLayout>

    <LinearLayout
        android:id="@+id/data"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_px_30"
        android:orientation="horizontal" />

</merge>