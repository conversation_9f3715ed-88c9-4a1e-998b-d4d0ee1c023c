package com.meishe.myvideo.downLoad;

import android.content.Intent;
import android.os.Bundle;
import android.widget.LinearLayout;

import com.meishe.base.interfaces.OnTitleBarClickListener;
import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseActivity;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.base.utils.SizeUtils;
import com.meishe.base.view.CustomTitleBar;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.myvideo.R;

import static com.meishe.logic.constant.PagerConstants.ASSET_TYPE;
import static com.meishe.logic.constant.PagerConstants.RATIO;
import static com.meishe.logic.constant.PagerConstants.TITLE_ID;


/**
 * The type Asset download activity.
 * 资源下载
 */
public class AssetDownloadActivity extends BaseActivity {

    private CustomTitleBar mTitleBar;
    /**
     * 默认设置为主题title
     * The default setting is the theme title
     */
    private int mTitleResId = R.string.moreTheme;
    /**
    * 默认设置为主题素材类型
    * The default setting is the theme material type
    */
    private int mAssetType = AssetInfo.ASSET_THEME;

    private void initTitle() {
        mTitleBar = findViewById(R.id.title_bar);
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, SizeUtils.dp2px(64));
        layoutParams.setMargins(0, ScreenUtils.getStatusBarHeight(),0,0);
        mTitleBar.setLayoutParams(layoutParams);
        mTitleBar.setTextCenter(mTitleResId);
    }

    protected void initListener() {
        mTitleBar.setOnTitleBarClickListener(new OnTitleBarClickListener() {
            @Override
            public void onBackImageClick() {
                Intent intent = new Intent();
                setResult(RESULT_OK, intent);
            }

            @Override
            public void onCenterTextClick() {

            }

            @Override
            public void onRightTextClick() {

            }
        });
    }

    @Override
    public void onBackPressed() {
        Intent intent = new Intent();
        setResult(RESULT_OK, intent);
        AppManager.getInstance().finishActivity();
        super.onBackPressed();
    }
    private void initAssetFragment() {
        Bundle bundle = new Bundle();
        bundle.putInt(RATIO, EditorEngine.getInstance().getMakeRatio());
        bundle.putInt(ASSET_TYPE, mAssetType);
        AssetListFragment assetListFragment = new AssetListFragment();
        assetListFragment.setArguments(bundle);
        getSupportFragmentManager().beginTransaction()
                .add(R.id.spaceLayout, assetListFragment)
                .show(assetListFragment)
                .commitAllowingStateLoss();
    }

    @Override
    protected int bindLayout() {
        return R.layout.activity_asset_download;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        Intent intent = getIntent();
        if(intent != null){
            Bundle bundle = intent.getExtras();
            if(bundle != null){
                mTitleResId = bundle.getInt(TITLE_ID,R.string.moreTheme);
                mAssetType = bundle.getInt(ASSET_TYPE,AssetInfo.ASSET_THEME);

            }
        }
    }

    @Override
    protected void initView() {
        initTitle();
        initAssetFragment();
        initListener();
    }
}
