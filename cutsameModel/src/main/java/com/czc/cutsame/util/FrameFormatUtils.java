package com.czc.cutsame.util;

import android.text.TextUtils;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/11/11 14:48
 * @Description :帧时间和字符串格式化 The frame format utils
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class FrameFormatUtils {

   private static final int HOUR = 3600;
   private static final int MINUTE = 60;
   private static final int MILLISECOND = 1000;
   private static final int MICROSECOND = 1000000;

   /**
    * 将时间转化成00:00:00:00的格式
    * Convert the time to a 00:00:00:00 format
    *
    * @param time long 需要转化的微秒数 The microsecond  need to convert
    * @return the string
    */
   public static String timeToFormatString(long time) {
      String timeStr = "00:00:00:00";
      if (time > 0) {
         long microTime = time / MILLISECOND;
         long secondLong = time / MICROSECOND;
         int hour = (int) (secondLong / HOUR);
         int minute = (int) ((secondLong % HOUR) / MINUTE);
         int second = (int) (secondLong % (MINUTE));
         int microSecond = (int) ((microTime % MILLISECOND) / 40F);
         timeStr = getTime(hour) + ":" + getTime(minute) + ":" + getTime(second) + ":"+ getTime(microSecond);
      }
      return timeStr;
   }

   /**
    * 将时间转化成时分秒的格式
    * Convert the time to hour, minute or second format
    *
    * @param time long 需要转化的微秒数 The microsecond  need to convert
    * @return the string
    */
   public static String timeToFirstString(long time) {
      String timeStr = "0s";
      if (time > 0) {
         long secondLong = time / MICROSECOND;
         int hour = (int) (secondLong / HOUR);
         if (hour > 0) {
            return hour + "h";
         }
         int minute = (int) ((secondLong % HOUR) / MINUTE);
         if (minute > 0) {
            return minute + "m";
         }
         int second = (int) (secondLong % (MINUTE));
         return second + "s";
      }
      return timeStr;
   }

   /**
    * String to time long.
    *
    * @param timeFormatString the time format string
    * @return the long
    */
   public static long stringToTime(String timeFormatString){
      if (TextUtils.isEmpty(timeFormatString)) {
         return 0;
      }
      String[] split = timeFormatString.split(":");
      if (split.length < 4) {
         return 0;
      }
      long hour  = Long.parseLong(split[0]);
      long minute  = Long.parseLong(split[1]);
      long second  = Long.parseLong(split[2]);
      long frame  = Long.parseLong(split[3]);
      return hour * HOUR * MICROSECOND + minute * MINUTE * MICROSECOND + second * MICROSECOND + frame * 40 * MILLISECOND ;
   }

   private static String getTime(int i) {
      return i >= 0 && i < 10 ? "0" + i : Integer.toString(i);
   }
}
