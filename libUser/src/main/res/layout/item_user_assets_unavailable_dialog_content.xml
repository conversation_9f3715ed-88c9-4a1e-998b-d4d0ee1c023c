<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:orientation="horizontal"
    android:paddingBottom="@dimen/dp_px_30"
    android:layout_height="wrap_content">
    <TextView
        android:id="@+id/tv_key"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp_px_33"
        android:layout_marginEnd="@dimen/dp_px_15"
        android:layout_marginRight="@dimen/dp_px_15"
        android:textColor="@color/color_ff363636"/>
    <TextView
        android:id="@+id/tv_value"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp_px_33"
        android:textColor="@color/color_ff363636"/>
</LinearLayout>
