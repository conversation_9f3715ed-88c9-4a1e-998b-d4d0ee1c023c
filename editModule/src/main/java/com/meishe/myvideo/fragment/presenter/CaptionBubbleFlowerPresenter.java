package com.meishe.myvideo.fragment.presenter;

import com.meicam.sdk.NvsCaption;
import com.meishe.base.utils.ResourceUtils;
import com.meishe.base.utils.StringUtils;
import com.meishe.business.assets.presenter.FlowPresenter;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.engine.bean.KeyFrameProcessor;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.engine.command.CaptionCommand;
import com.meishe.engine.command.KeyFrameHolderCommand;
import com.meishe.myvideo.R;
import com.meishe.myvideo.event.MessageEvent;

import java.util.List;

import static com.meishe.engine.asset.bean.AssetInfo.ASSET_CUSTOM_CAPTION_BUBBLE;
import static com.meishe.engine.asset.bean.AssetInfo.ASSET_CUSTOM_CAPTION_FLOWER;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_REFRESH_CAPTION_RANGE;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/8/4 17:38
 * @Description :字幕气泡花字表示类 The caption bubble flower presenter
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CaptionBubbleFlowerPresenter extends FlowPresenter {
    private int mAssetsType = -1;
    private MeicamCaptionClip mCaptionClip;
    private MeicamCaptionClip mOldCaptionClip;

    @Override
    protected List<AssetInfo> handleDataInFirstPage(List<AssetInfo> list) {
        AssetInfo nullInfo = new AssetInfo();
        nullInfo.setType(ASSET_CUSTOM_CAPTION_BUBBLE);
        nullInfo.setEffectMode(BaseInfo.EFFECT_MODE_PACKAGE);
        nullInfo.setName(StringUtils.getString(R.string.no));
        nullInfo.setCoverPath(ResourceUtils.getMipmapToUri(R.mipmap.icon_none));
        nullInfo.setHadDownloaded(true);
        list.add(0, nullInfo);
        return list;
    }

    public void initData(int type, MeicamCaptionClip captionClip) {
        mAssetsType = type;
        updateCaptionClip(captionClip);
    }

    /**
     * 更新字幕片段
     * Update caption clip
     *
     * @param captionClip the new caption clip
     */
    public void updateCaptionClip(MeicamCaptionClip captionClip) {
        if (captionClip == null) {
            mOldCaptionClip = null;
        } else {
            if (!captionClip.equals(mCaptionClip)) {
                mOldCaptionClip = (MeicamCaptionClip) captionClip.clone();
            }
        }
        mCaptionClip = captionClip;
    }


    /**
     * 应用花字或者气泡
     * Apply caption bubble or flower
     *
     * @param info the asset info
     */
    public void applyBubbleOrFlower(AssetInfo info) {
        boolean isSuccess;
        if (mCaptionClip == null) {
            return;
        }
        if (mAssetsType == ASSET_CUSTOM_CAPTION_FLOWER) {
            String oldUuid = mOldCaptionClip == null ? null : mOldCaptionClip.getRichWordUuid();
            isSuccess = EditorEngine.getInstance().changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_RICH_WORD, oldUuid, info.getPackageId());
        } else {
            String oldUuid = mOldCaptionClip == null ? null : mOldCaptionClip.getBubbleUuid();
            isSuccess = EditorEngine.getInstance().changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_BUBBLE, oldUuid, info.getPackageId());
            /*设置气泡之后重置关键帧，要不然有些机型显示操作框位置不对*/
            KeyFrameProcessor<NvsCaption> frameProcessor = mCaptionClip.keyFrameProcessor();
            if (frameProcessor.getKeyFrameCount() > 0) {
                KeyFrameHolderCommand.resetKeyFrame(mCaptionClip, false);
            }
        }
        if (isSuccess) {
            if (EditorEngine.getInstance().isPlaying()) {
                EditorEngine.getInstance().stop();
            }
            MessageEvent.sendEvent(MESSAGE_TYPE_REFRESH_CAPTION_RANGE);
        }
    }

    /**
     * 获取当前设置的资源包id
     * Get current package id
     *
     * @return package id
     */
    public String getCurrentPackageId() {
        String uuid = "";
        if (mCaptionClip != null) {
            if (mAssetsType == ASSET_CUSTOM_CAPTION_BUBBLE) {
                uuid = mCaptionClip.getBubbleUuid();
            } else {
                uuid = mCaptionClip.getRichWordUuid();
            }
        }
        return uuid;
    }
}
