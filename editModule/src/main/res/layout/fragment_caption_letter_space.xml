<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_marginTop="@dimen/dp_px_30"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_word_space"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_120"
        android:layout_marginStart="@dimen/dp_px_51"
        android:gravity="center"
        android:text="@string/tv_text_margin"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_33" />

    <com.meishe.myvideo.view.MYSeekBarTextView
        app:layout_constraintTop_toTopOf="@+id/tv_word_space"
        app:layout_constraintBottom_toBottomOf="@+id/tv_word_space"
        app:layout_constraintStart_toEndOf="@+id/tv_word_space"
        app:layout_constraintEnd_toEndOf="parent"
        android:id="@+id/sb_word_space"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_px_75"
        android:layout_marginStart="@dimen/dp_px_30" />

    <TextView
        android:id="@+id/tv_line_space"
        app:layout_constraintTop_toBottomOf="@+id/tv_word_space"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintVertical_weight="1"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_120"
        android:layout_marginTop="@dimen/dp_px_30"
        android:gravity="center"
        android:layout_marginStart="@dimen/dp_px_51"
        android:text="@string/tv_raw_margin"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_33" />

    <com.meishe.myvideo.view.MYSeekBarTextView
        app:layout_constraintTop_toTopOf="@+id/tv_line_space"
        app:layout_constraintBottom_toBottomOf="@+id/tv_line_space"
        app:layout_constraintStart_toEndOf="@+id/tv_line_space"
        app:layout_constraintEnd_toEndOf="parent"
        android:id="@+id/sb_line_space"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_px_75"
        android:layout_marginStart="@dimen/dp_px_30"/>

</androidx.constraintlayout.widget.ConstraintLayout>