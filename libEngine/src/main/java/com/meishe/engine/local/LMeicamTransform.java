package com.meishe.engine.local;


import java.io.Serializable;

/**
 * author：yangtailin on 2020/7/6 20:38
 */
public class LMeicamTransform implements Serializable, Cloneable{
    private float transformX;
    private float transformY;
    private float scaleX = 1.0F;
    private float scaleY = 1.0F;
    private float rotationZ;

    public float getTransformX() {
        return transformX;
    }

    public void setTransformX(float transformX) {
        if (Float.isNaN(transformX)) {
            return;
        }
        this.transformX = transformX;
    }

    public float getTransformY() {
        return transformY;
    }

    public void setTransformY(float transformY) {
        if (Float.isNaN(transformY)) {
            return;
        }
        this.transformY = transformY;
    }

    public float getScaleX() {
        return scaleX;
    }

    public void setScaleX(float scaleX) {
        if (Float.isNaN(scaleX)) {
            return;
        }
        this.scaleX = scaleX;
    }

    public float getScaleY() {
        return scaleY;
    }

    public void setScaleY(float scaleY) {
        if (Float.isNaN(scaleY)) {
            return;
        }
        this.scaleY = scaleY;
    }

    public float getRotationZ() {
        return rotationZ;
    }

    public void setRotationZ(float rotationZ) {
        if (Float.isNaN(rotationZ)) {
            return;
        }
        this.rotationZ = rotationZ;
    }

   /* @Override
    public boolean parseToTimelineData(MeicamTransform meicamTransform) {
        MeicamTransform timelineData = new MeicamTransform();
        timelineData.setRotationZ(getRotationZ());
        timelineData.setScaleX(getScaleX());
        timelineData.setScaleY(getScaleY());
        timelineData.setTransformX(getTransformX());
        timelineData.setTransformY(getTransformY());
        return true;
    }*/
}
