package com.meishe.myvideo.ui.trackview.bean;

import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.MeicamAudioClip;
import com.meishe.engine.command.AudioCommand;
import com.meishe.myvideo.R;
import com.meishe.myvideo.ui.bean.BaseUIClip;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/5/25 11:04
 * @Description :音频代理 Audio proxy
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class AudioClipProxy extends BaseUIClip {

    private MeicamAudioClip mAudioClip;
    private float[] mtRecordArray = new float[0];
    private long mRecordingDuration;

    public AudioClipProxy(MeicamAudioClip audioClip, int trackIndex) {
        super(CommonData.CLIP_AUDIO, trackIndex);
        this.mAudioClip = audioClip;
        if (audioClip != null) {
            super.setInPoint(audioClip.getInPoint());
            super.setOutPoint(audioClip.getOutPoint());
            super.setTrimOut(audioClip.getTrimOut());
            super.setTrimIn(audioClip.getTrimIn());
            super.setSpeed(audioClip.getSpeed());
        }
    }

    public void setAudioClip(MeicamAudioClip audioClip) {
        this.mAudioClip = audioClip;
        if (audioClip != null) {
            super.setInPoint(audioClip.getInPoint());
            super.setOutPoint(audioClip.getOutPoint());
            super.setTrimOut(audioClip.getTrimOut());
            super.setTrimIn(audioClip.getTrimIn());
            super.setSpeed(audioClip.getSpeed());
        }
    }

    @Override
    public int getSubType() {
        if (mAudioClip != null) {
            return mAudioClip.getAudioType();
        }
        return MeicamAudioClip.AUDIO_RECORD_ING;
    }

    @Override
    public int getClipIndexInTrack() {
        if (mAudioClip != null) {
            return mAudioClip.getIndex();
        }
        return INVALID;
    }

    @Override
    public void setInPoint(long inPoint) {
        if (mAudioClip != null) {
            mAudioClip.setInPoint(inPoint);
        }
        super.setInPoint(inPoint);
    }

    @Override
    public void setOutPoint(long outPoint) {
        if (mAudioClip != null) {
            mAudioClip.setOutPoint(outPoint);
        }
        super.setOutPoint(outPoint);
    }

    @Override
    public long getInPoint() {
        if (mAudioClip != null) {
            return mAudioClip.getInPoint();
        }
        return super.getInPoint();
    }

    @Override
    public long getOutPoint() {
        if (mAudioClip != null) {
            return mAudioClip.getOutPoint();
        }
        return super.getOutPoint();
    }

    @Override
    public double getSpeed() {
        if (mAudioClip != null) {
            mAudioClip.getSpeed();
        }
        return super.getSpeed();
    }

    public void setSpeed(long speed) {
        super.setSpeed(speed);
    }

    @Override
    public String getIconFilePath() {
        return "";
    }

    @Override
    public String getFilePath() {
        if (mAudioClip != null) {
            return mAudioClip.getFilePath();
        }
        return super.getFilePath();
    }

    @Override
    public long getTrimIn() {
        if (mAudioClip != null) {
            return mAudioClip.getTrimIn();
        }
        return super.getTrimIn();
    }

    @Override
    public void setTrimIn(long trimIn) {
        if (mAudioClip != null) {
            AudioCommand.setTrimIn(mAudioClip, trimIn);
        }
        super.setTrimIn(trimIn);
    }

    @Override
    public long getTrimOut() {
        if (mAudioClip != null) {
            return mAudioClip.getTrimOut();
        }
        return super.getTrimOut();
    }

    @Override
    public void setTrimOut(long trimOut) {
        if (mAudioClip != null) {
            AudioCommand.setTrimOut(mAudioClip, trimOut);
        }
        super.setTrimOut(trimOut);
    }

    @Override
    public float[] getRecordArray() {
        if (mAudioClip != null) {
            return mAudioClip.getRecordArray();
        }
        return mtRecordArray;
    }

    @Override
    public void setRecordArray(float[] data) {
        if (mAudioClip != null) {
            mAudioClip.setRecordArray(data);
        }
        mtRecordArray = data;
    }

    @Override
    public boolean canExceedLength() {
        return true;
    }

    @Override
    public boolean canDrag() {
        return true;
    }

    @Override
    public int getBackGroundColor() {
        return R.color.track_background_color_audio;
    }

    @Override
    public void setDuration(long duration) {
        mRecordingDuration = duration;
        if (mAudioClip != null) {
            mAudioClip.setOriginalDuration(duration);
        }
    }

    @Override
    public void setOriginalDuration(long duration) {
        if (mAudioClip != null) {
            mAudioClip.setOriginalDuration(duration);
        }
    }

    @Override
    public long getOriginalDuration() {
        if (mAudioClip != null) {
            return mAudioClip.getOriginalDuration();
        }
        return 0;
    }

    @Override
    public long getDuration() {
        if (mAudioClip != null) {
            return mAudioClip.getOutPoint() - mAudioClip.getInPoint();
        } else {
            return mRecordingDuration;
        }
    }

    @Override
    public long getFadeIn() {
        if (mAudioClip != null) {
            return mAudioClip.getFadeInDuration();
        }
        return 0;
    }

    @Override
    public long getFadeOut() {
        if (mAudioClip != null) {
            return mAudioClip.getFadeOutDuration();
        }
        return 0;
    }

    @Override
    public String getDisplayName() {
        if (mAudioClip != null) {
            return mAudioClip.getDrawText();
        }
        return super.getDisplayName();
    }

    @Override
    public void setDisplayName(String name) {
        if (mAudioClip != null) {
            mAudioClip.setDrawText(name);
        }
        super.setDisplayName(name);
    }

    @Override
    public String getLeftChannelDataPath() {
        if (mAudioClip != null) {
           return mAudioClip.getLeftChannelUrl();
        }
        return super.getLeftChannelDataPath();
    }

    @Override
    public void setTrackIndex(int trackIndex) {
        super.setTrackIndex(trackIndex);
    }

    @Override
    public int getTrackIndex() {
        if (mAudioClip != null) {
            return mAudioClip.getTrackIndex();
        }
        return super.getTrackIndex();
    }
}
