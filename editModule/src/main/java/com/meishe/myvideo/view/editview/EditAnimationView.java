package com.meishe.myvideo.view.editview;

import android.content.Context;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.model.BaseMvpView;
import com.meishe.base.model.NvsError;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.NetUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.business.assets.iview.AssetsView;
import com.meishe.business.assets.view.AssetsTypeTabView;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.bean.AssetsConstants;
import com.meishe.engine.bean.AnimationData;
import com.meishe.myvideo.R;
import com.meishe.myvideo.adapter.AnimationAdapter;
import com.meishe.myvideo.event.MessageEvent;
import com.meishe.myvideo.fragment.presenter.AnimationPresenter;
import com.meishe.myvideo.interfaces.BottomEventListener;
import com.meishe.myvideo.util.ConfigUtil;
import com.meishe.third.adpater.BaseQuickAdapter;

import java.util.ArrayList;
import java.util.List;

import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_ADD_ANIMATION_GROUP;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_ADD_ANIMATION_IN;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_ADD_ANIMATION_OUT;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_ANIMATION_CONFIRM_EFFECT;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_CHANGE_ANIMATION_GROUP_DURATION;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_CHANGE_ANIMATION_IN_DURATION;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_CHANGE_ANIMATION_OUT_DURATION;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_REMOVE_ANIMATION_GROUP_DURATION;

/**
 * author：yangtailin on 2020/8/31 17:19
 * 视频的开场、入场、组合动画视图
 * Video opening entry combo animation view
 */
public class EditAnimationView extends BaseMvpView<AnimationPresenter> implements AssetsView {
    private static final int CATEGORY_ID_IN = 8;
    private static final int CATEGORY_ID_OUT = 9;
    private static final int CATEGORY_ID_GROUP = 10;
    private final int MIN_PROGRESS = 100;
    private long mClipDuration;
    private long mAnimationDuration;
    private String mAnimationId;
    private RecyclerView mRvAnimationList;
    private SeekBar mSeekBar;
    private AnimationAdapter mAnimationAdapter;
    private View mIvConform;
    private TextView mTvAnimationType;
    private TextView mTvDuration;
    private TextView mTvDurationHint;
    private int mAnimationType = AssetInfo.ASSET_ANIMATION_IN;
    private int mAnimationSubType = 0;
    private int mAnimationKind = -1;
    private int mCategoryId = 8;
    private AssetInfo mSelectData;
    private int mAnimationProgress = -1;
    private BottomEventListener mEventListener;
    private AssetsTypeTabView mTabType;
    private TextView mHintText;
    private String mDownloadTag;

    public EditAnimationView(Context context) {
        super(context);
        initListener();
    }

    @Override
    protected AnimationPresenter createPresenter() {
        return new AnimationPresenter();
    }

    @Override
    public void initView() {
        View rootView = LayoutInflater.from(getContext()).inflate(R.layout.layout_animation_view, this);
        mRvAnimationList = rootView.findViewById(R.id.rv_animation_list);
        mSeekBar = rootView.findViewById(R.id.seek_bar);
        mIvConform = rootView.findViewById(R.id.iv_confirm);
        mTvAnimationType = rootView.findViewById(R.id.tv_content);
        mTvDuration = rootView.findViewById(R.id.tv_duration);
        mTvDurationHint = rootView.findViewById(R.id.tv_duration_hint);
        mTabType = rootView.findViewById(R.id.ttv_tab_type);
        mHintText = rootView.findViewById(R.id.tv_hint);
    }

    private void initListener() {
        mAnimationAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                if (!loadMore()) {
                    mAnimationAdapter.loadMoreEnd(true);
                }
            }
        }, mRvAnimationList);
        mAnimationAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (position == 0) {
                    mAnimationAdapter.selected(position);
                    MessageEvent.sendEvent(MESSAGE_TYPE_REMOVE_ANIMATION_GROUP_DURATION);
                    displaySeekBar(false);
                    mAnimationProgress = -1;
                    mAnimationId = "";
                    return;
                }
                AssetInfo item = mAnimationAdapter.getItem(position);
                if (item != null) {
                    mSelectData = item;
                    displaySeekBar(true);
                    if (!item.isHadDownloaded()) {
                        downloadAsset(item, position);
                    } else {
                        updateSeekView(item.getPackageId(), item.isPostPackage());
                        mPresenter.clickAssetItem(item);
                        mAnimationAdapter.selected(position);
                    }
                }
            }
        });
        mSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (!fromUser) {
                    return;
                }
                if (progress <= MIN_PROGRESS) {
                    progress = MIN_PROGRESS;
                }
                if (mAnimationProgress == progress) {
                    return;
                }
                float durationText = progress * 1.0F / 1000;
                String format = String.format(getContext().getString(R.string.animation_view_during), durationText);
                mTvDuration.setText(format);
                mAnimationProgress = progress;
                AnimationData event = new AnimationData();
                event.setPackageFxPath(mSelectData.getPackageId());
                event.setIsPostPackage(mSelectData.isPostPackage());
                if (isAnimationIn()) {
                    event.setInPoint(0);
                    event.setOutPoint(progress * 1000L);
                    MessageEvent.sendEvent(event, MESSAGE_TYPE_CHANGE_ANIMATION_IN_DURATION);
                } else if (isAnimationOut()) {
                    event.setInPoint(mClipDuration - progress * 1000L);
                    event.setOutPoint(mClipDuration);
                    MessageEvent.sendEvent(event, MESSAGE_TYPE_CHANGE_ANIMATION_OUT_DURATION);
                } else {
                    event.setInPoint(0);
                    event.setOutPoint(progress * 1000L);
                    MessageEvent.sendEvent(event, MESSAGE_TYPE_CHANGE_ANIMATION_GROUP_DURATION);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                int progress = seekBar.getProgress();
                if (progress <= MIN_PROGRESS) {
                    progress = MIN_PROGRESS;
                    seekBar.setProgress(progress);
                }
                AnimationData event = new AnimationData();
                event.setPackageFxPath(mSelectData.getPackageId());
                event.setIsPostPackage(mSelectData.isPostPackage());
                if (isAnimationIn()) {
                    event.setInPoint(0);
                    event.setOutPoint(progress * 1000L);
                    MessageEvent.sendEvent(event, MESSAGE_TYPE_ADD_ANIMATION_IN);
                } else if (isAnimationOut()) {
                    event.setInPoint(mClipDuration - progress * 1000L);
                    event.setOutPoint(mClipDuration);
                    MessageEvent.sendEvent(event, MESSAGE_TYPE_ADD_ANIMATION_OUT);
                } else {
                    event.setInPoint(0);
                    event.setOutPoint(progress * 1000L);
                    MessageEvent.sendEvent(event, MESSAGE_TYPE_ADD_ANIMATION_GROUP);
                }
                mAnimationDuration = progress * 1000L;
            }
        });
        mIvConform.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mEventListener != null) {
                    mEventListener.onDismiss(true);
                    MessageEvent.sendEvent(MESSAGE_TYPE_ANIMATION_CONFIRM_EFFECT);
                }
            }
        });
        mTabType.setItemClickedListener(new AssetsTypeTabView.ItemClickedListener() {
            @Override
            public void onItemClicked(int position) {
                mAnimationSubType = position;
                mAnimationAdapter.setAssetSubType(position);
                requestAnimationList(false);
            }
        });
    }

    /**
     * 下载特效资源包
     * Download the effects resource pack
     *
     * @param assetInfo asset info
     * @param position  the index of asset info int the list
     */
    private void downloadAsset(final AssetInfo assetInfo, final int position) {
        assetInfo.setDownloadProgress(0);
        mAnimationAdapter.notifyItemChanged(position);
        mDownloadTag = assetInfo.getPackageId();
        mPresenter.downloadAsset(assetInfo, position);
    }

    @Override
    public void initData() {
        mAnimationAdapter = new AnimationAdapter();
        LinearLayoutManager layoutManager = new LinearLayoutManagerWrapper(getContext(), LinearLayoutManager.HORIZONTAL, false);
        mRvAnimationList.setLayoutManager(layoutManager);
        mRvAnimationList.setAdapter(mAnimationAdapter);
        mRvAnimationList.addItemDecoration(new ItemDecoration(5, 5));
    }

    /**
     * 展示进度条相关视图
     * Show a view of the progress bar
     */
    private void displaySeekBar(boolean display) {
        int visibility = display ? VISIBLE : INVISIBLE;
        mTvDurationHint.setVisibility(visibility);
        mTvDuration.setVisibility(visibility);
        mSeekBar.setVisibility(visibility);
    }

    /**
     * 初始化数据
     * Init data
     *
     * @param animationId       the animation id 动画id
     * @param animationDuration the animation duration 动画周期
     * @param clipDuration      the video clip duration 视频时间
     * @param animationTypeName the animation type name 动画类型名称
     */
    public void setData(String animationId, long animationDuration, long clipDuration, String animationTypeName) {
        mTvAnimationType.setText(animationTypeName);
        if (!ConfigUtil.isNewAssets()) {
            if (getContext().getString(R.string.sub_menu_animation_in).equals(animationTypeName)) {
                mAnimationType = AssetInfo.ASSET_ANIMATION_IN;
                mCategoryId = CATEGORY_ID_IN;
            } else if (getContext().getString(R.string.sub_menu_animation_out).equals(animationTypeName)) {
                mAnimationType = AssetInfo.ASSET_ANIMATION_OUT;
                mCategoryId = CATEGORY_ID_OUT;
            } else {
                mAnimationType = AssetInfo.ASSET_ANIMATION_GROUP;
                mCategoryId = CATEGORY_ID_GROUP;
            }
        } else {
            if (getContext().getString(R.string.sub_menu_animation_in).equals(animationTypeName)) {
                mAnimationType = AssetInfo.ASSET_ANIMATION_IN;
                mCategoryId = AssetsConstants.AssetsTypeData.ANIMATION_IN.category;
                mAnimationKind = AssetsConstants.AssetsTypeData.ANIMATION_IN.kind;
            } else if (getContext().getString(R.string.sub_menu_animation_out).equals(animationTypeName)) {
                mAnimationType = AssetInfo.ASSET_ANIMATION_OUT;
                mCategoryId = AssetsConstants.AssetsTypeData.ANIMATION_OUT.category;
                mAnimationKind = AssetsConstants.AssetsTypeData.ANIMATION_OUT.kind;
            } else {
                mAnimationType = AssetInfo.ASSET_ANIMATION_GROUP;
                mCategoryId = AssetsConstants.AssetsTypeData.ANIMATION_COMP.category;
                mAnimationKind = AssetsConstants.AssetsTypeData.ANIMATION_COMP.kind;
            }
        }
        updateAnimation(animationId, animationDuration, clipDuration);
        requestAnimationList(true);
    }

    private boolean loadMore() {
        return mPresenter.loadMoreData(2, mAnimationSubType, mCategoryId, mAnimationKind, false);
    }

    private void requestAnimationList(final boolean needUpdate) {
        mPresenter.setPageSize(10);
        mPresenter.loadData(2, mAnimationSubType, mCategoryId, mAnimationKind, needUpdate);
    }

    /**
     * 更新视图
     * Update the view
     *
     * @param animationId       the animation id 动画id
     * @param animationDuration the animation duration 动画周期
     * @param clipDuration      the video clip duration 视频时间
     */
    public void updateAnimation(String animationId, long animationDuration, long clipDuration) {
        mAnimationDuration = animationDuration;
        mAnimationId = animationId;
        if (mClipDuration != clipDuration) {
            mClipDuration = clipDuration;
            mSeekBar.setMax((int) (clipDuration / 1000));
        }
        if (TextUtils.isEmpty(animationId)) {
            mAnimationProgress = -1;
            mSeekBar.setProgress(MIN_PROGRESS);
            mAnimationAdapter.selected(0);
        } else {
            if (mAnimationAdapter.getData().size() > 0) {
                mAnimationAdapter.selected(animationId);
                int currentPosition = mAnimationAdapter.getSelectedPosition();
                if (currentPosition > 0) {
                    displaySeekBar(true);
                    String format = String.format(getContext().getString(R.string.animation_view_during), animationDuration * 1.0F / 1000 / 1000);
                    mTvDuration.setText(format);
                    mSeekBar.setProgress((int) (animationDuration / 1000));
                    mAnimationProgress = (int) (animationDuration / 1000);
                    mSelectData = mAnimationAdapter.getItem(currentPosition);
                    return;
                }
            }
        }
        displaySeekBar(false);
    }

    /**
     * 设置事件监听
     * Sets the listener
     *
     * @param listener the listener 监听
     */
    public void setListener(BottomEventListener listener) {
        mEventListener = listener;
    }

    /**
     * 检查选中
     * Check selected item
     */
    private void checkSelected() {
        if (!TextUtils.isEmpty(mAnimationId)) {
            mAnimationAdapter.selected(mAnimationId);
            if (mAnimationAdapter.getSelectedPosition() > 0) {
                displaySeekBar(true);
                String format = String.format(getContext().getString(R.string.animation_view_during), mAnimationDuration * 1.0F / 1000 / 1000);
                mTvDuration.setText(format);
                mSeekBar.setProgress((int) (mAnimationDuration / 1000));
                mAnimationProgress = (int) (mAnimationDuration / 1000);
                mSelectData = mAnimationAdapter.getItem(mAnimationAdapter.getSelectedPosition());
            } else {
                displaySeekBar(false);
            }

        }
    }

    private void updateSeekView(String uuid, int isPostPackage) {
        mAnimationId = uuid;
        AnimationData event = new AnimationData();
        event.setPackageFxPath(uuid);
        event.setIsPostPackage(isPostPackage);
        if (isAnimationIn()) {
            event.setInPoint(0);
            if (mAnimationProgress == -1) {
                event.setOutPoint(500 * 1000);
                mSeekBar.setProgress(500);
            } else {
                event.setOutPoint(mAnimationProgress * 1000L);
                mSeekBar.setProgress(mAnimationProgress);
            }

            MessageEvent.sendEvent(event, MESSAGE_TYPE_ADD_ANIMATION_IN);
        } else if (isAnimationOut()) {
            if (mAnimationProgress == -1) {
                event.setInPoint(mClipDuration - 500 * 1000);
                mSeekBar.setProgress(500);
            } else {
                event.setInPoint(mClipDuration - mAnimationProgress * 1000L);
                mSeekBar.setProgress(mAnimationProgress);
            }
            event.setOutPoint(mClipDuration);

            MessageEvent.sendEvent(event, MESSAGE_TYPE_ADD_ANIMATION_OUT);
        } else {
            event.setInPoint(0);
            event.setOutPoint(mClipDuration);
            mSeekBar.setProgress((int) (mClipDuration / 1000));
            MessageEvent.sendEvent(event, MESSAGE_TYPE_ADD_ANIMATION_GROUP);
        }
        mAnimationDuration = mSeekBar.getProgress() * 1000L;
        float durationText = mSeekBar.getProgress() * 1.0F / 1000;
        String format = String.format(getContext().getString(R.string.animation_view_during), durationText);
        mTvDuration.setText(format);
    }

    private boolean isAnimationIn() {
        return mAnimationType == AssetInfo.ASSET_ANIMATION_IN;
    }

    private boolean isAnimationOut() {
        return mAnimationType == AssetInfo.ASSET_ANIMATION_OUT;
    }

    @Override
    public boolean isActive() {
        return isShown();
    }

    @Override
    public void onNewDataBack(List<AssetInfo> list, int subType, boolean needUpdate) {
        if (!CommonUtils.isEmpty(list)) {
            mAnimationAdapter.setNewData(list);
        }
        updateViewState(subType, needUpdate);
    }

    private void updateViewState(int subType, boolean needUpdate) {
        if (getItemCount() <= 0) {
            mHintText.setVisibility(View.VISIBLE);
            mRvAnimationList.setVisibility(View.INVISIBLE);
            displaySeekBar(false);
            if (!NetUtils.isNetworkAvailable(getContext())) {
                mHintText.setText(R.string.user_hint_assets_net_error_refresh);
                Drawable drawable = getResources().getDrawable(R.mipmap.ic_assets_data_update);
                drawable.setBounds(new Rect(0, 4, drawable.getIntrinsicHeight(), drawable.getIntrinsicHeight() + 4));
                mHintText.setCompoundDrawables(null, null, drawable, null);
                mHintText.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        initData();
                    }
                });
            } else {
                mHintText.setText(AssetsManager.get().getErrorMsg(getContext(), subType));
            }
        } else {
            mHintText.setVisibility(View.GONE);
            mRvAnimationList.setVisibility(View.VISIBLE);
            if (!NetUtils.isNetworkAvailable(getContext()) && needUpdate) {
                ToastUtils.showShort(Utils.getApp().getResources().getString(R.string.user_hint_assets_net_error));
            }
        }
        //按照网络资源排序，第N页并不一定会有对应的资源，所以每次都要检查
        // Sorted by network resources, page N does not necessarily have corresponding resources,
        // so check every time.
        checkSelected();
    }

    @Override
    public void onMoreDataBack(List<AssetInfo> list, int subType, boolean needUpdate) {
        if (!CommonUtils.isEmpty(list)) {
            mAnimationAdapter.addData(list);
        }
        mAnimationAdapter.loadMoreComplete();
        updateViewState(subType, needUpdate);
    }

    @Override
    public void onDataError(int subType, boolean needUpdate) {
        mAnimationAdapter.setNewData(new ArrayList<>());
        mAnimationAdapter.loadMoreComplete();
        updateViewState(subType, needUpdate);
    }

    @Override
    public int getItemCount() {
        //最少数量是2，包括null和加载更多item
        //The minimum number is 2, including null and loading more items.
        return mAnimationAdapter == null ? 0 : mAnimationAdapter.getItemCount() - 2;
    }

    @Override
    public void onDownloadProgress(int position) {
        mAnimationAdapter.notifyItemChanged(position);
    }

    @Override
    public void onDownloadFinish(int position, AssetInfo assetInfo) {
        if (TextUtils.equals(mDownloadTag, assetInfo.getPackageId())) {
            mAnimationAdapter.selected(position);
            updateSeekView(assetInfo.getPackageId(), assetInfo.isPostPackage());
        } else {
            mAnimationAdapter.notifyItemChanged(position);
        }
    }

    @Override
    public void onDownloadError(int position) {
        mAnimationAdapter.notifyItemChanged(position);
    }

    @Override
    public void onShowDialog() {

    }

    @Override
    public void onDismissDialog() {

    }

    @Override
    public void onError(NvsError error) {

    }
}
