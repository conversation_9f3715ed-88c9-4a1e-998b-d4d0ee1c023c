<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:orientation="vertical">

    <com.meishe.base.view.CustomTitleBar
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_192"
        android:layout_marginTop="@dimen/dp_px_96"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_sticker_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@null"
        android:scaleType="fitCenter"
        app:layout_constraintBottom_toTopOf="@+id/v_bottom_line"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title_bar" />

    <com.meishe.myvideo.view.CustomStickerDrawRect
        android:id="@+id/customDrawRect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title_bar" />

    <View
        android:id="@+id/v_bottom_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_1"
        android:layout_marginBottom="@dimen/dp_px_36"
        android:background="@color/menu_divide_color"
        app:layout_constraintBottom_toTopOf="@+id/iv_freedom"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />


    <ImageView
        android:id="@+id/iv_freedom"
        android:layout_width="@dimen/dp_px_135"
        android:layout_height="@dimen/dp_px_135"
        android:layout_marginBottom="@dimen/dp_px_24"
        android:contentDescription="@null"
        android:src="@mipmap/custom_free_select"
        app:layout_constraintBottom_toTopOf="@+id/tv_freedom"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/iv_circle" />

    <TextView
        android:id="@+id/tv_freedom"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_81"
        android:text="@string/free"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_30"
        app:layout_constraintBottom_toTopOf="@+id/tv_confirm"
        app:layout_constraintLeft_toLeftOf="@+id/iv_freedom"
        app:layout_constraintRight_toRightOf="@+id/iv_freedom" />


    <ImageView
        android:id="@+id/iv_circle"
        android:layout_width="@dimen/dp_px_135"
        android:layout_height="@dimen/dp_px_135"
        android:layout_marginBottom="@dimen/dp_px_24"
        android:contentDescription="@null"
        android:src="@mipmap/custom_circle"
        app:layout_constraintBottom_toTopOf="@+id/tv_circle"
        app:layout_constraintLeft_toRightOf="@+id/iv_freedom"
        app:layout_constraintRight_toLeftOf="@+id/iv_square" />

    <TextView
        android:id="@+id/tv_circle"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_81"
        android:text="@string/circle"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_30"
        app:layout_constraintBottom_toTopOf="@+id/tv_confirm"
        app:layout_constraintLeft_toLeftOf="@+id/iv_circle"
        app:layout_constraintRight_toRightOf="@+id/iv_circle" />


    <ImageView
        android:id="@+id/iv_square"
        android:layout_width="@dimen/dp_px_135"
        android:layout_height="@dimen/dp_px_135"
        android:layout_marginBottom="@dimen/dp_px_24"
        android:contentDescription="@null"
        android:src="@mipmap/custom_square"
        app:layout_constraintBottom_toTopOf="@+id/tv_square"
        app:layout_constraintLeft_toRightOf="@+id/iv_circle"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/tv_square"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_81"
        android:text="@string/square"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_30"
        app:layout_constraintBottom_toTopOf="@+id/tv_confirm"
        app:layout_constraintLeft_toLeftOf="@+id/iv_square"
        app:layout_constraintRight_toRightOf="@+id/iv_square" />


    <TextView
        android:id="@+id/tv_confirm"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_108"
        android:layout_margin="@dimen/dp_px_45"
        android:background="@drawable/bg_confirm"
        android:gravity="center"
        android:text="@string/tv_sticker_make"
        android:textColor="@color/start_edit_text"
        android:textSize="@dimen/sp_px_42"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@+id/iv_sticker_image"
        app:layout_constraintRight_toRightOf="@+id/iv_sticker_image" />

</androidx.constraintlayout.widget.ConstraintLayout>
