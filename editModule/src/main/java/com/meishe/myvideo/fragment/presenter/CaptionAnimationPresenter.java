package com.meishe.myvideo.fragment.presenter;

import com.meicam.sdk.NvsCaption;
import com.meishe.base.utils.ResourceUtils;
import com.meishe.base.utils.StringUtils;
import com.meishe.business.assets.iview.AssetsView;
import com.meishe.business.assets.presenter.AssetsPresenter;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.bean.AssetsConstants;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.engine.bean.KeyFrameProcessor;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.engine.command.CaptionCommand;
import com.meishe.engine.command.KeyFrameHolderCommand;
import com.meishe.myvideo.R;
import com.meishe.myvideo.util.ConfigUtil;

import java.util.List;

import static com.meishe.engine.EditorEngine.ANIMATION_DEFAULT_DURATION;
import static com.meishe.engine.EditorEngine.IN_OUT_ANIMATION_DEFAULT_DURATION;
import static com.meishe.engine.asset.bean.AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_IN;
import static com.meishe.engine.asset.bean.AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_OUT;

import android.text.TextUtils;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/8/17 11:40
 * @Description :字幕动画presenter The caption animation presenter.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CaptionAnimationPresenter extends AssetsPresenter<AssetsView> {
    private int mAssetsType = -1;
    private int mAssetKind = -1;
    private MeicamCaptionClip mCaptionClip;
    private MeicamCaptionClip mOldCaptionClip;

    @Override
    protected List<AssetInfo> handleDataInFirstPage(List<AssetInfo> list) {
        AssetInfo nullInfo = new AssetInfo();
        nullInfo.setType(ASSET_CUSTOM_CAPTION_ANIMATION_IN);
        nullInfo.setName(StringUtils.getString(R.string.no));
        nullInfo.setEffectMode(BaseInfo.EFFECT_MODE_PACKAGE);
        nullInfo.setCoverPath(ResourceUtils.getMipmapToUri(R.mipmap.icon_none));
        nullInfo.setHadDownloaded(true);
        list.add(0, nullInfo);
        return list;
    }

    public void initData(int type, int kinType, MeicamCaptionClip captionClip) {
        mAssetsType = type;
        mAssetKind = kinType;
        updateCaptionClip(captionClip);
    }

    /**
     * 更新字幕片段
     * Update caption clip
     *
     * @param captionClip the new caption clip
     */
    public void updateCaptionClip(MeicamCaptionClip captionClip) {
        if (captionClip == null) {
            mOldCaptionClip = null;
        } else {
            if (!captionClip.equals(mCaptionClip)) {
                mOldCaptionClip = (MeicamCaptionClip) captionClip.clone();
            }
        }
        mCaptionClip = captionClip;
    }

    /**
     * 应用字幕动画
     * Apply the caption animation
     */
    public void applyCaptionAnimation(AssetInfo info) {
        if (isAnimationIn()) {
            handleCaptionAnimationIn(info.getPackageId());
        } else if (isAnimationOut()) {
            handleCaptionAnimationOut(info.getPackageId());
        } else {
            handleCaptionAnimationCombination(info.getPackageId());
        }
    }

    /**
     * Handle caption animation in.
     *
     * @param uuid the uuid
     */
    public void handleCaptionAnimationIn(String uuid) {
        if (mCaptionClip == null) {
            return;
        }
        String oldPackageId;
        Integer duration;
        if (!TextUtils.isEmpty(mCaptionClip.getCombinationAnimationUuid())) {
            oldPackageId = mOldCaptionClip == null ? null : mOldCaptionClip.getCombinationAnimationUuid();
            duration = mOldCaptionClip == null ? null : mOldCaptionClip.getCombinationAnimationDuration();
            EditorEngine.getInstance().changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_COMP_ANIMATION, oldPackageId, "");
            EditorEngine.getInstance().changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_COMP_ANIMATION_DURATION, duration, 0);
        }
        oldPackageId = mOldCaptionClip == null ? null : mOldCaptionClip.getMarchInAnimationUuid();
        EditorEngine.getInstance().changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_IN_ANIMATION, oldPackageId, uuid);
        if (mCaptionClip.getMarchInAnimationDuration() == 0) {
            /*
             * 如果切换的时候，设置的动画时长是0，则恢复默认,产品需求。
             * If the animation duration is set to 0 when switching, then the default, product requirement, is restored
             * */
            duration = mOldCaptionClip == null ? null : mOldCaptionClip.getMarchInAnimationDuration();
            EditorEngine.getInstance().changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_IN_ANIMATION_DURATION, duration, IN_OUT_ANIMATION_DEFAULT_DURATION);
        }
        if (TextUtils.isEmpty(uuid)) {
            duration = mOldCaptionClip == null ? null : mOldCaptionClip.getMarchInAnimationDuration();
            EditorEngine.getInstance().changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_IN_ANIMATION_DURATION, duration, 0);
        }
        resetCaptionKeyFrame(mCaptionClip);
        EditorEngine.getInstance().playVideoRollBack(mCaptionClip.getInPoint(), mCaptionClip.getInPoint() + mCaptionClip.getMarchInAnimationDuration() * 1000);
    }

    /**
     * 组合字幕动画
     * Handle animation of combined caption.
     *
     * @param uuid 动画的uuid uuid of animation.
     */
    public void handleCaptionAnimationCombination(String uuid) {
        if (mCaptionClip == null) {
            return;
        }
        String oldPackageId;
        Integer duration;
        if (!TextUtils.isEmpty(mCaptionClip.getMarchOutAnimationUuid())) {
            oldPackageId = mOldCaptionClip == null ? null : mOldCaptionClip.getMarchOutAnimationUuid();
            duration = mOldCaptionClip == null ? null : mOldCaptionClip.getMarchOutAnimationDuration();
            EditorEngine.getInstance().changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_OUT_ANIMATION, oldPackageId, "");
            /*
             * 恢复默认值
             * Restore Defaults
             * */
            EditorEngine.getInstance().changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_OUT_ANIMATION_DURATION, duration, 0);
        }
        if (!TextUtils.isEmpty(mCaptionClip.getMarchInAnimationUuid())) {
            oldPackageId = mOldCaptionClip == null ? null : mOldCaptionClip.getMarchInAnimationUuid();
            duration = mOldCaptionClip == null ? null : mOldCaptionClip.getMarchInAnimationDuration();
            EditorEngine.getInstance().changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_IN_ANIMATION, oldPackageId, "");
            EditorEngine.getInstance().changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_IN_ANIMATION_DURATION, duration, 0);
        }
        oldPackageId = mOldCaptionClip == null ? null : mOldCaptionClip.getCombinationAnimationUuid();
        EditorEngine.getInstance().changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_COMP_ANIMATION, oldPackageId, uuid);

        if (mCaptionClip.getCombinationAnimationDuration() == 0) {
            /*
             * 如果切换的时候，设置的动画时长是0，则恢复默认，产品需求。
             * If the animation duration is set to 0 when switching, then the default, product requirement, is restored
             * */
            duration = mOldCaptionClip == null ? null : mOldCaptionClip.getCombinationAnimationDuration();
            EditorEngine.getInstance().changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_COMP_ANIMATION_DURATION, duration, ANIMATION_DEFAULT_DURATION);
        }
        resetCaptionKeyFrame(mCaptionClip);
        EditorEngine.getInstance().playVideoRollBack(mCaptionClip.getInPoint(), mCaptionClip.getOutPoint());
    }

    /**
     * Reset keyframes, if there is no keyframe reset, marquee rotation, zoom, pan values
     * 重置关键帧，如果没有关键帧重置，字幕旋转，缩放，平移值
     *
     * @param meicamCaptionClip the meicam caption clip
     */
    private void resetCaptionKeyFrame(MeicamCaptionClip meicamCaptionClip) {
        /*设置动画之后重置关键帧，要不然有些动画效果不对以及操作框位置不对*/
        KeyFrameProcessor<NvsCaption> frameProcessor = meicamCaptionClip.keyFrameProcessor();
        if (frameProcessor.getKeyFrameCount() > 0) {
            KeyFrameHolderCommand.resetKeyFrame(meicamCaptionClip, false);
        } else {
            //设置字幕效果以后要重置，如果不重置，效果不正确，例如动画里的，上滚屏在移动字幕以后添加效果不正确
            //After setting the subtitle effect, reset it. If not reset, the effect is not correct.
            // For example, in animation, scroll up does not add the effect correctly after moving the subtitle
            EditorEngine.getInstance().changeCaptionParam(meicamCaptionClip, CaptionCommand.PARAM_TRANS_X, null, meicamCaptionClip.getTranslationX());
            EditorEngine.getInstance().changeCaptionParam(meicamCaptionClip, CaptionCommand.PARAM_TRANS_Y, null, meicamCaptionClip.getTranslationY());
            EditorEngine.getInstance().changeCaptionParam(meicamCaptionClip, CaptionCommand.PARAM_SCALE_X, null, meicamCaptionClip.getScaleX());
            EditorEngine.getInstance().changeCaptionParam(meicamCaptionClip, CaptionCommand.PARAM_SCALE_Y, null, meicamCaptionClip.getScaleY());
            EditorEngine.getInstance().changeCaptionParam(meicamCaptionClip, CaptionCommand.PARAM_ROTATION, null, meicamCaptionClip.getRotation());
        }
    }

    /**
     * Handle caption animation out.
     *
     * @param uuid the uuid
     */
    public void handleCaptionAnimationOut(String uuid) {
        if (mCaptionClip == null) {
            return;
        }
        String oldPackageId;
        Integer duration;
        if (!TextUtils.isEmpty(mCaptionClip.getCombinationAnimationUuid())) {
            oldPackageId = mOldCaptionClip == null ? null : mOldCaptionClip.getCombinationAnimationUuid();
            duration = mOldCaptionClip == null ? null : mOldCaptionClip.getCombinationAnimationDuration();
            EditorEngine.getInstance().changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_COMP_ANIMATION, oldPackageId, "");
            EditorEngine.getInstance().changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_COMP_ANIMATION_DURATION, duration, ANIMATION_DEFAULT_DURATION);
        }
        oldPackageId = mOldCaptionClip == null ? null : mOldCaptionClip.getMarchOutAnimationUuid();
        EditorEngine.getInstance().changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_OUT_ANIMATION, oldPackageId, uuid);
        if (mCaptionClip.getMarchOutAnimationDuration() == 0) {
            /*
             * 如果切换的时候，设置的动画时长是0，则恢复默认，产品需求
             * If the animation duration is set to 0 when switching, then the default, product requirement, is restored
             * */
            duration = mOldCaptionClip == null ? null : mOldCaptionClip.getMarchOutAnimationDuration();
            EditorEngine.getInstance().changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_OUT_ANIMATION_DURATION, duration, IN_OUT_ANIMATION_DEFAULT_DURATION);
        }
        if (TextUtils.isEmpty(uuid)) {
            duration = mOldCaptionClip == null ? null : mOldCaptionClip.getMarchOutAnimationDuration();
            EditorEngine.getInstance().changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_OUT_ANIMATION_DURATION, duration, 0);
        }
        resetCaptionKeyFrame(mCaptionClip);
        EditorEngine.getInstance().playVideoRollBack(mCaptionClip.getOutPoint() - mCaptionClip.getMarchOutAnimationDuration() * 1000, mCaptionClip.getOutPoint());
    }

    /**
     * 获取动画id
     * Get animation id
     */
    public String getAnimationId() {
        String animationId = "";
        if (mCaptionClip != null) {
            if (isAnimationIn()) {
                animationId = mCaptionClip.getMarchInAnimationUuid();
            } else if (isAnimationOut()) {
                animationId = mCaptionClip.getMarchOutAnimationUuid();
            } else {
                animationId = mCaptionClip.getCombinationAnimationUuid();
            }
        }
        return animationId;
    }

    private boolean isAnimationIn() {
        if (ConfigUtil.isNewAssets()) {
            return mAssetKind == AssetsConstants.AssetsTypeData.CAPTION_ANIMATION_IN.kind;
        }
        return mAssetsType == AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_IN;
    }

    private boolean isAnimationOut() {
        if (ConfigUtil.isNewAssets()) {
            return mAssetKind == AssetsConstants.AssetsTypeData.CAPTION_ANIMATION_OUT.kind;
        }
        return mAssetsType == ASSET_CUSTOM_CAPTION_ANIMATION_OUT;
    }
}
