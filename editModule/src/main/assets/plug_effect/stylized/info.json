[{"name": "线性", "enName": "line", "plugName": "Line Effect", "coverPath": "file:///android_asset/plug_effect/stylized/line/line.png", "plugDesc": "可对图像添加模拟毛刺的效果", "plugDescEn": "The image can be added to simulate the burr effect", "effectPath": "plug_effect/stylized/line/info.json"}, {"name": "边缘增强", "enName": "edge", "plugName": "Edge", "coverPath": "file:///android_asset/plug_effect/stylized/edge/edge.png", "plugDesc": "可对图像实现显示物体边缘的效果", "plugDescEn": "The image can be realized to display the effect of the object edge", "effectPath": "plug_effect/stylized/edge/info.json"}, {"name": "格栅", "enName": "grid", "plugName": "Grid", "coverPath": "file:///android_asset/plug_effect/stylized/grid/grid.png", "plugDesc": "可对图像进行顶点坐标的挤压来实现变形效果", "plugDescEn": "The image can be extruded by vertex coordinates to achieve deformation effect", "effectPath": "plug_effect/stylized/grid/info.json"}, {"name": "色差分离", "enName": "chromaticAberration", "plugName": "Chromatic Aberration", "coverPath": "file:///android_asset/plug_effect/stylized/chromaticAberration/chromaticAberration.png", "plugDesc": "可调节图像红绿蓝通道来实现颜色偏离效果", "plugDescEn": "Adjustable image red, green and blue channels to achieve color deviation effect", "effectPath": "plug_effect/stylized/chromaticAberration/info.json"}]