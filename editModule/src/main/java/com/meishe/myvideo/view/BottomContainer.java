package com.meishe.myvideo.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Build;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.meishe.base.model.IBaseView;
import com.meishe.base.model.NvsError;
import com.meishe.myvideo.R;
import com.meishe.myvideo.interfaces.BottomEventListener;
import com.meishe.myvideo.view.interf.IBottomFragment;
import com.meishe.myvideo.view.interf.IBottomView;

import java.util.ArrayDeque;


/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2020/12/28 18:18
 * @Description :底部视图容器 Bottom view container
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class BottomContainer extends FrameLayout implements IBaseView {
    private final ArrayDeque<View> mViewStack = new ArrayDeque<>();
    private final ArrayDeque<Fragment> mFragmentStack = new ArrayDeque<>();
    private Fragment mShowFragment;
    private View mShowView;
    private FragmentManager mFragmentManager;

    public BottomContainer(@NonNull Context context) {
        this(context, null);
    }

    public BottomContainer(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BottomContainer(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public BottomContainer(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init();
    }

    private void init() {
        setOnTouchListener(new OnTouchListener() {
            @SuppressLint("ClickableViewAccessibility")
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                //拦截点击事件
                return v.getId() == getId();
            }
        });
    }

    /**
     * Set fragment manager
     *
     * @param fragmentManager the fragmentManager
     */
    public void setFragmentManager(FragmentManager fragmentManager) {
        mFragmentManager = fragmentManager;
    }

    /**
     * 展示视图，可以同时加入多个视图，每次只显示后添加的。
     * Show view ,You can add more than one view at a time, displaying only those added later at a time.
     */
    public void showPushView(View view) {
        if (!mViewStack.isEmpty()) {
            View first = mViewStack.getFirst();
            if (first != null) {
                checkNotify(first);
                first.setVisibility(GONE);
            }
        }
        show(true);
        addView(view);
        mViewStack.push(view);
    }

    /**
     * 销毁视图，每次销毁最后添加的视图，并显示它前边的视图。
     * Dismiss view,Each time the last view added is destroyed, and the view preceding it is displayed.
     */
    public void dismissPopView() {
        if (mViewStack.isEmpty()) {
            dismissView();
        } else {
            View pop = mViewStack.pop();
            removeView(pop);
            checkNotify(pop);
            if (mViewStack.isEmpty()) {
                dismissView();
                return;
            }
            pop = mViewStack.getFirst();
            if (pop != null) {
                pop.setVisibility(VISIBLE);
            }
        }
    }


    /**
     * 显示视图view
     * Show the view
     */
    public void showView(View view) {
        showView(view, true);
    }

    /**
     * 显示视图view
     * Show the view
     *
     * @param showAnimation true show animation 显示动画，false not 不显示动画
     */
    public void showView(View view, boolean showAnimation) {
        show(showAnimation);
        mShowView = view;
        addView(view);
    }

    private void show(boolean hasAnimation) {
        if (hasAnimation) {
            Animation animation = AnimationUtils.loadAnimation(getContext(), R.anim.view_enter);
            setAnimation(animation);
        }
        setVisibility(View.VISIBLE);
    }

    /**
     * 销毁视图view
     * Dismiss the view
     */
    public void dismissView() {
        dismissView(true);
    }

    /**
     * 销毁视图view
     * Dismiss the view
     *
     * @param showAnimation true show animation 显示动画，false not 不显示动画
     */
    public void dismissView(boolean showAnimation) {
        if (showAnimation) {
            Animation animation = AnimationUtils.loadAnimation(getContext(), R.anim.view_exit);
            setAnimation(animation);
        }
        setVisibility(View.INVISIBLE);
        removeAllViews();
        if (mShowView != null) {
            checkNotify(mShowView);
        }
        while (!mViewStack.isEmpty()) {
            mShowView = mViewStack.pop();
            if (mShowView != null) {
                checkNotify(mShowView);
                removeView(mShowView);
            }
        }
        mShowView = null;
    }

    private void checkNotify(View view) {
        if (view instanceof IBottomView) {
            BottomEventListener listener = ((IBottomView) view).getListener();
            listener.onDismiss(false);
        }
    }

    private void checkNotify(Fragment fragment) {
        if (fragment instanceof IBottomView) {
            BottomEventListener listener = ((IBottomFragment) fragment).getListener();
            listener.onDismiss(false);
        }
    }

    /**
     * 获取正在显示的视图view
     * Gets the show view
     */
    public View getShowView() {
        if (mShowView != null) {
            return mShowView;
        }
        if (mViewStack.isEmpty()) {
            return null;
        }
        return mViewStack.getFirst();
    }

    /**
     * 销毁所有视图
     * Dismiss all
     */
    public void dismissAll() {
        if (mShowFragment != null) {
            dismissFragment();
        }

        removeAllFragment();

        if (mShowView != null) {
            dismissView();
        }

        while (!mViewStack.isEmpty()) {
            mShowView = mViewStack.pop();
            if (mShowView != null) {
                removeView(mShowView);
            }
        }
    }

    private void removeAllFragment() {
        if (!mFragmentStack.isEmpty()) {
            FragmentTransaction transaction = mFragmentManager.beginTransaction();
            while (!mFragmentStack.isEmpty()) {
                mShowFragment = mFragmentStack.pop();
                if (mShowFragment != null) {
                    transaction.remove(mShowFragment);
                }
            }
            transaction.commitAllowingStateLoss();
        }
    }

    /**
     * 替换显示目标fragment页面
     * Show and replace the fragment
     *
     * @param fragment the fragment 碎片页面
     */
    public void showReplaceFragment(Fragment fragment) {
        setVisibility(VISIBLE);
        mShowFragment = fragment;
        FragmentTransaction transaction = mFragmentManager.beginTransaction();
        transaction.replace(getId(), fragment);
        transaction.show(fragment);
        transaction.commitAllowingStateLoss();
    }

    /**
     * 显示目标fragment页面
     * Show the fragment
     *
     * @param fragment    the fragment 碎片页面
     * @param transaction the transaction 碎片页面事务
     */
    private void showFragment(Fragment fragment, FragmentTransaction transaction) {
        setVisibility(VISIBLE);
        if (!fragment.isAdded()) {
            transaction.add(getId(), fragment);
        }
        transaction.show(fragment);
        transaction.commitAllowingStateLoss();
    }

    /**
     * 显示目标fragment页面
     * Show the fragment
     *
     * @param fragment the fragment 碎片页面
     */
    public void showFragment(Fragment fragment) {
        mShowFragment = fragment;
        FragmentTransaction transaction = mFragmentManager.beginTransaction();
        showFragment(fragment, transaction);
    }

    /**
     * 不显示fragment页面
     * Dismiss the fragment
     */
    public void dismissFragment() {
        setVisibility(INVISIBLE);
        if (mShowFragment != null) {
            FragmentTransaction transaction = mFragmentManager.beginTransaction();
            transaction.remove(mShowFragment);
            transaction.commitAllowingStateLoss();
        }
        mShowFragment = null;
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        return super.onInterceptTouchEvent(ev);
    }

    /**
     * 显示目标fragment页面,可以同时加入多个fragment，每次只显示后添加的
     * Show the fragment，You can add more than one fragment at a time, displaying only those added later at a time.
     *
     * @param fragment the fragment 碎片页面
     */
    public void showPushFragment(Fragment fragment) {
        FragmentTransaction transaction = mFragmentManager.beginTransaction();
        if (!mFragmentStack.isEmpty()) {
            Fragment first = mFragmentStack.getFirst();
            if (first != null) {
                transaction.hide(first);
            }
        }
        showFragment(fragment, transaction);
        mFragmentStack.push(fragment);
    }

    /**
     * 销毁视图，每次销毁最后添加的视图，并显示它前边的视图。
     * Dismiss view,Each time the last fragment added is destroyed, and the view preceding it is displayed.
     */
    public void dismissPopFragment() {
        if (mFragmentStack.isEmpty()) {
            dismissFragment();
        } else {
            Fragment pop = mFragmentStack.pop();
            FragmentTransaction transaction = mFragmentManager.beginTransaction();
            transaction.remove(pop);
            if (mFragmentStack.isEmpty()) {
                dismissFragment();
                return;
            }
            pop = mFragmentStack.getFirst();
            showFragment(pop, transaction);
        }
    }

    /**
     * 获取正在显示的fragment
     * Gets the show fragment
     */
    public Fragment getShowFragment() {
        if (mShowFragment != null) {
            return mShowFragment;
        }
        if (mFragmentStack.isEmpty()) {
            return null;
        }
        return mFragmentStack.getFirst();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (mShowFragment != null) {
            dismissFragment();
        }
        removeAllFragment();
    }

    @Override
    public void onShowDialog() {

    }

    @Override
    public void onDismissDialog() {

    }

    @Override
    public void onError(NvsError error) {

    }
}
