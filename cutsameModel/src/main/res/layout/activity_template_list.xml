<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/black"
    android:orientation="vertical"
    tools:context=".TemplateListActivity">


    <ImageView
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_search_hint"
        android:id="@+id/iv_back"
        android:layout_width="@dimen/dp_px_70"
        android:layout_height="@dimen/dp_px_70"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/dp_px_34"
        android:layout_marginTop="@dimen/dp_px_141"
        android:contentDescription="@null"
        android:padding="@dimen/dp_px_11"
        android:scaleType="fitXY"
        android:src="@mipmap/ic_back_white"
        app:layout_constraintHorizontal_weight="1" />


    <EditText
        app:layout_constraintTop_toTopOf="@+id/iv_back"
        app:layout_constraintBottom_toBottomOf="@+id/iv_back"
        app:layout_constraintLeft_toRightOf="@+id/iv_back"
        app:layout_constraintRight_toRightOf="parent"
        android:id="@+id/tv_search_hint"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_px_100"
        android:background="@drawable/bg_rectangle_round_black_282828"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp_px_114"
        android:paddingEnd="@dimen/dp_px_50"
        android:layout_marginStart="@dimen/dp_px_30"
        android:layout_marginEnd="@dimen/dp_px_48"
        android:maxLines="1"
        android:inputType="text"
        android:hint="@string/hint_input_keyword_to_search_template"
        android:textColorHint="@color/gray_a4a"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_33"
        app:layout_constraintHorizontal_weight="1" />

    <ImageView
        app:layout_constraintTop_toTopOf="@+id/tv_search_hint"
        app:layout_constraintBottom_toBottomOf="@+id/tv_search_hint"
        app:layout_constraintLeft_toLeftOf="@+id/tv_search_hint"
        android:layout_width="@dimen/dp_px_40"
        android:layout_height="@dimen/dp_px_40"
        android:layout_marginStart="@dimen/dp_px_40"
        android:contentDescription="@null"
        android:src="@mipmap/ic_search" />

    <FrameLayout
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginTop="@dimen/dp_px_265"
        android:id="@+id/fl_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
