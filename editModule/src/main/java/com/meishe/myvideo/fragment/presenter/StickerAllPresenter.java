package com.meishe.myvideo.fragment.presenter;

import com.meishe.business.assets.presenter.FlowPresenter;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/8/4 17:38
 * @Description :GradView展示形式的资源管理页面的逻辑处理类 The sticker all presenter
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class StickerAllPresenter extends FlowPresenter {
    /*@Override
    protected List<AssetInfo> handleDataInFirstPage(List<AssetInfo> list) {
        //TODO 演示用方案，需要添加本地素材
        if (NetUtils.isNetworkAvailable(Utils.getApp().getApplicationContext())) {
            List<AssetInfo> assetList = AssetsManager.get().getPackageAssetList(AssetInfo.ASSET_ANIMATED_STICKER);
            if (!CommonUtils.isEmpty(assetList)) {
                NvsAssetPackageManager manager = NvsStreamingContext.getInstance().getAssetPackageManager();
                for (AssetInfo assetInfo : assetList) {
                    StringBuilder packageId = new StringBuilder();
                    int error = manager.installAssetPackage(assetInfo.getAssetPath(), null, NvsAssetPackageManager.ASSET_PACKAGE_TYPE_ANIMATEDSTICKER, true, packageId);
                    assetInfo.setEffectMode(BaseInfo.EFFECT_MODE_PACKAGE);
                }
                assetList.addAll(list);
                return assetList;
            }
        }
        return list;
    }*/
}
