package com.meishe.draft.db;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/5/18 15:56
 * @Description :文件信息dao.The dao of file information.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
@Dao
public interface FileInfoDao {
   @Insert
   void insertDraft(FileInfoEntity... info);

   @Update
   void updateDraft(FileInfoEntity... info);

   @Delete
   void deleteDraft(FileInfoEntity... info);

   @Query("DELETE  FROM FileInfoEntity")
   void deleteAll();

   @Query("SELECT * FROM FileInfoEntity WHERE md5 =  :md5 AND userId = :userId")
   FileInfoEntity getFile(String md5, String userId);

   @Query("SELECT * FROM FileInfoEntity WHERE url =  :url AND userId = :userId")
   FileInfoEntity getFileByUrl(String url, String userId);

   @Query("SELECT * FROM FileInfoEntity WHERE localPath =  :localPath")
   FileInfoEntity getFileByLocalPath(String localPath);

   @Query("SELECT * FROM FileInfoEntity WHERE url =  :url")
   FileInfoEntity getFileByUrl(String url);

   @Query("SELECT * FROM FileInfoEntity")
   List<FileInfoEntity> getFile();
}
