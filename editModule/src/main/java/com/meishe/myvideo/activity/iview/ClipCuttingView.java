package com.meishe.myvideo.activity.iview;

import com.meishe.base.model.IBaseView;
import com.meishe.player.view.cutregion.ICutRegionFragment;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/22 11:27
 * @Description :区域裁剪View. View about rect cutting.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface ClipCuttingView extends IBaseView {

    /**
     * 获取裁剪交互view，Get the view about rect cutting.
     *
     * @return view
     */
    ICutRegionFragment getCutUiView();

    /**
     * 退出. exit
     */
    void exit();
}
