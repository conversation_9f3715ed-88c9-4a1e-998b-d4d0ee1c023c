package com.meishe.business.assets;

import android.content.Context;
import android.widget.TextView;

import com.meishe.business.R;
import com.meishe.engine.asset.bean.AssetsConstants;
import com.meishe.engine.asset.bean.RequestParam;
import com.meishe.engine.asset.bean.TabParam;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2021/3/30 10:48
 * @Description:资源工具类 The utils for assets
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class AssetUtils {

    /**
     * 已购素材UI是否显示
     * Asset purchased visible boolean.
     *
     * @param tvAssetPurchased the tv asset purchased
     * @param assetSubType     the asset sub type
     * @return the boolean
     */
    public static boolean assetPurchasedVisible(TextView tvAssetPurchased, int assetSubType, boolean authed) {
      /*  if (!ConfigUtil.isToC() && tvAssetPurchased != null && assetSubType == 0 && authed) {
            if (!Utils.isZh()) {
                tvAssetPurchased.setSelected(true);
            }
            return true;
        }*/
        return false;
    }

    public static List<TabParam> getTabList(Context context, int type) {
        if (type == AssetsConstants.AssetsTypeData.EFFECT.type) {
            List<TabParam> params = new ArrayList<>();
            String[] tabs = context.getResources().getStringArray(R.array.tab_effect);
            if (tabs != null && tabs.length > 0) {
                for (int index = 0; index < tabs.length; index++) {
                    params.add(new TabParam(tabs[index], new RequestParam(AssetsConstants.AssetsTypeData.EFFECT.type, -1, AssetsConstants.AssetsTypeData.EFFECT.category, index + 1)));
                }
            }
            return params;
        } else if (type == AssetsConstants.AssetsTypeData.STICKER.type) {
            List<TabParam> params = new ArrayList<>();
            String[] tabs = context.getResources().getStringArray(R.array.tab_sticker);
            if (tabs != null && tabs.length > 0) {
                for (int index = 0; index < tabs.length; index++) {
                    params.add(new TabParam(tabs[index], new RequestParam(type, -1, index + 1, -1)));
                }
            }
            return params;
        } else if (type == AssetsConstants.AssetsTypeData.TRANSITION.type) {
            List<TabParam> params = new ArrayList<>();
            String[] tabs = context.getResources().getStringArray(R.array.tab_transition);
            if (tabs != null && tabs.length > 0) {
                for (int index = 0; index < tabs.length; index++) {
                    params.add(new TabParam(tabs[index], new RequestParam(type, -1, index + 1, -1)));
                }
            }
            return params;
        } else if (type == AssetsConstants.AssetsTypeData.PROP.type) {
            List<TabParam> params = new ArrayList<>();
            String[] tabs = context.getResources().getStringArray(R.array.tab_prop);
            if (tabs != null && tabs.length > 0) {
                for (int index = 0; index < tabs.length; index++) {
                    params.add(new TabParam(tabs[index], new RequestParam(type, -1, index + 1, AssetsConstants.AssetsTypeData.PROP.kind)));
                }
            }
            return params;
        }
        return new ArrayList<>();
    }

    public static List<TabParam> getCaptureTabList(Context context, int type) {
        if (type == AssetsConstants.AssetsTypeData.EFFECT.type) {
            List<TabParam> params = new ArrayList<>();
            String[] tabs = context.getResources().getStringArray(R.array.tab_filter);
            if (tabs != null && tabs.length > 0) {
                for (int index = 0; index < tabs.length; index++) {
                    params.add(new TabParam(tabs[index], new RequestParam(AssetsConstants.AssetsTypeData.FILTER.type, -1, AssetsConstants.AssetsTypeData.FILTER.category, index + 1)));
                }
            }
            tabs = context.getResources().getStringArray(R.array.tab_effect);
            if (tabs != null && tabs.length > 0) {
                for (int index = 0; index < tabs.length; index++) {
                    params.add(new TabParam(tabs[index], new RequestParam(AssetsConstants.AssetsTypeData.EFFECT.type, -1, AssetsConstants.AssetsTypeData.EFFECT.category, index + 1)));
                }
            }
            return params;
        } else if (type == AssetsConstants.AssetsTypeData.STICKER.type) {
            List<TabParam> params = new ArrayList<>();
            String[] tabs = context.getResources().getStringArray(R.array.tab_sticker);
            if (tabs != null && tabs.length > 0) {
                for (int index = 0; index < tabs.length; index++) {
                    params.add(new TabParam(tabs[index], new RequestParam(type, -1, index + 1, -1)));
                }
            }
            return params;
        } else if (type == AssetsConstants.AssetsTypeData.TRANSITION.type) {
            List<TabParam> params = new ArrayList<>();
            String[] tabs = context.getResources().getStringArray(R.array.tab_transition);
            if (tabs != null && tabs.length > 0) {
                for (int index = 0; index < tabs.length; index++) {
                    params.add(new TabParam(tabs[index], new RequestParam(type, -1, index + 1, -1)));
                }
            }
            return params;
        } else if (type == AssetsConstants.AssetsTypeData.PROP.type) {
            List<TabParam> params = new ArrayList<>();
            String[] tabs = context.getResources().getStringArray(R.array.tab_prop);
            if (tabs != null && tabs.length > 0) {
                for (int index = 0; index < tabs.length; index++) {
                    params.add(new TabParam(tabs[index], new RequestParam(type, -1, index + 1, AssetsConstants.AssetsTypeData.PROP.kind)));
                }
            }
            return params;
        }
        return new ArrayList<>();
    }
}
