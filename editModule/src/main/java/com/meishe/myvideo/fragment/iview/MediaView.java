package com.meishe.myvideo.fragment.iview;



import com.meishe.base.bean.MediaSection;
import com.meishe.base.model.IBaseView;

import java.util.List;


/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2022 /10/31 14:18
 * @Description: 媒体列表View The media view
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public interface MediaView extends IBaseView {

    /**
     * 媒体数据获取
     * On media back.
     *
     * @param mediaData the media data
     */
    void onMediaBack(List<MediaSection> mediaData);

    /**
     * 媒体数据项改变
     * On item change.
     *
     * @param position the position
     */
    void onItemChange(int position);
}
