package com.meishe.user.view;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.KeyboardUtils;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.libplugin.user.UserAssetsInfo;
import com.meishe.third.pop.XPopup;
import com.meishe.third.pop.core.CenterPopupView;
import com.meishe.user.R;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/3/24 10:29
 * @Description :资源不可用提示弹窗 Pop for unAvailable assets
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class AssetsUnAvailablePop extends CenterPopupView implements View.OnClickListener {
    private final IUserPlugin.OnEventListener mOnEventListener;
    private List<UserAssetsInfo> mAssetsData;
    private boolean mHaveNoBuyAssets = false;

    private ContentAdapter mAdapter;
    private View mCancelView;

    public AssetsUnAvailablePop(@NonNull Context context,  List<UserAssetsInfo> assetsData, IUserPlugin.OnEventListener listener) {
        super(context);
        mAssetsData = assetsData;
        mOnEventListener = listener;
    }
    private TextView mTvUpload;

    public static AssetsUnAvailablePop create(Context context, List<UserAssetsInfo> assetsData, IUserPlugin.OnEventListener listener) {
        return ((AssetsUnAvailablePop) new XPopup
                .Builder(context)
                .asCustom(new AssetsUnAvailablePop(context, assetsData, listener)));
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_assets_unavailable;
    }

    @Override
    protected int getPopupWidth() {
        return (int) (ScreenUtils.getScreenWidth() * 19 / 20f);
    }


    @Override
    protected void onDismiss() {
        super.onDismiss();
        KeyboardUtils.hideSoftInput(this);
    }


    @Override
    protected void onCreate() {
        super.onCreate();
        mTvUpload = findViewById(R.id.tv_upload);
        mCancelView = findViewById(R.id.tv_cancel);
        RecyclerView recyclerView = findViewById(R.id.rc_content);
        LinearLayoutManager layoutManager = new LinearLayoutManagerWrapper(getContext(), LinearLayoutManager.VERTICAL, false);
        recyclerView.setLayoutManager(layoutManager);
        mAdapter = new ContentAdapter();
        mAdapter.setData(parseData(mAssetsData));
        recyclerView.setAdapter(mAdapter);
        if (mHaveNoBuyAssets) {
            mCancelView.setVisibility(VISIBLE);
            mTvUpload.setText(R.string.user_dialog_assets_unavailable_submit);
        } else {
            mCancelView.setVisibility(GONE);
            mTvUpload.setText(R.string.user_dialog_assets_unavailable_i_see);
            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) mTvUpload.getLayoutParams();
            layoutParams.bottomMargin = getResources().getDimensionPixelOffset(R.dimen.dp_px_75);
            mTvUpload.setLayoutParams(layoutParams);
        }
        initListener();
    }

    private List<DataItem> parseData( List<UserAssetsInfo> assetList) {
        if (assetList == null) {
            return null;
        }
        if (CommonUtils.isEmpty(assetList)) {
            return null;
        }
        List<UserAssetsInfo> currentPossessor = new ArrayList<>();
        List<UserAssetsInfo> otherPossessor = new ArrayList<>();
        for (UserAssetsInfo assetInfo : assetList) {
            int possessor = assetInfo.getPossessor();
            if (possessor == 1) {
                currentPossessor.add(assetInfo);
            } else {
                otherPossessor.add(assetInfo);
            }
        }
        List<DataItem> result = new ArrayList<>();
        if (!currentPossessor.isEmpty()) {
            result.add(new DataItem("", getResources().getString(R.string.user_dialog_assets_unavailable_type_no_purchased), DataItem.TYPE_TITLE_NO_BUY, false));
            mHaveNoBuyAssets = true;
        }
        getDataSectionItems(result, currentPossessor, true);
        if (!otherPossessor.isEmpty()) {
            result.add(new DataItem("", getResources().getString(R.string.user_dialog_assets_unavailable_type_not_current_account), DataItem.TYPE_TITLE_NOT_ACCOUNT, false));
            getDataSectionItems(result, otherPossessor, false);
        }
        return result;
    }

    private List<DataItem> getDataSectionItems(List<DataItem> result, List<UserAssetsInfo> currentPossessor, boolean isCurrentAccount) {
        Map<String, List<DataItem>> dataMap = new HashMap<>();
        for (UserAssetsInfo assetInfo : currentPossessor) {
            String type = assetInfo.getTag();
            List<DataItem> dataItems = dataMap.get(type);
            if (dataItems == null) {
                dataItems = new ArrayList<>();
                dataMap.put(type, dataItems);
            }
            dataItems.add(new DataItem(assetInfo.getName(), assetInfo.getContent(), DataItem.TYPE_CONTENT, isCurrentAccount));
        }
        Set<String> keys = dataMap.keySet();
        for (String key : keys) {
            result.add(new DataItem(key, "", DataItem.TYPE_TITLE, isCurrentAccount));
            result.addAll(dataMap.get(key));
        }
        return result;
    }

    private void initListener() {
        mTvUpload.setOnClickListener(this);
        mCancelView.setOnClickListener(this);
        findViewById(R.id.iv_cancel).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.tv_upload) {
            if (mHaveNoBuyAssets) {
                commitAssets(mAdapter.dataItemList);
                if (mOnEventListener != null) {
                    if (CommonUtils.isEmpty(mAdapter.dataItemList)) {
                        return;
                    }
                    List<String> param = new ArrayList<>();
                    for (DataItem dataItem : mAdapter.dataItemList) {
                        if (dataItem.isCurrentAccount) {
                            param.add(dataItem.value);
                        }
                    }
                    mOnEventListener.onConfirm(param);
                }
            }
        } else if (id == R.id.tv_cancel || id == R.id.iv_cancel) {
            if (mOnEventListener != null) {
                mOnEventListener.onCancel();
            }
        }
        dismiss();
    }

    public void commitAssets(List<DataItem> dataItemList) {
        if (CommonUtils.isEmpty(dataItemList)) {
            return;
        }
        List<String> param = new ArrayList<>();
        for (DataItem dataItem : dataItemList) {
            if (dataItem.isCurrentAccount) {
                param.add(dataItem.value);
            }
        }

    }

    static class ContentAdapter extends RecyclerView.Adapter {

        private List<DataItem> dataItemList;

        public void setData(List<DataItem> dataItemList) {
            this.dataItemList = dataItemList;
        }

        @NonNull
        @Override
        public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            if (viewType == DataItem.TYPE_TITLE) {
                View view = LayoutInflater.from(parent.getContext())
                        .inflate(R.layout.item_user_assets_unavailable_dialog_title, null);
                ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                view.setLayoutParams(layoutParams);
                return new TitleHolder(view);
            } else if (viewType == DataItem.TYPE_TITLE_NO_BUY) {
                View view = LayoutInflater.from(parent.getContext())
                        .inflate(R.layout.item_user_assets_unavailable_dialog_type_no_buy, null);
                ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                view.setLayoutParams(layoutParams);
                return new TitleHolder(view);
            }  else if (viewType == DataItem.TYPE_TITLE_NOT_ACCOUNT) {
                View view = LayoutInflater.from(parent.getContext())
                        .inflate(R.layout.item_user_assets_unavailable_dialog_type_not_account, null);
                ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                view.setLayoutParams(layoutParams);
                return new TitleHolder(view);
            } else {
                return new ContentHolder(LayoutInflater.from(parent.getContext())
                        .inflate(R.layout.item_user_assets_unavailable_dialog_content, null));
            }
        }

        @Override
        public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
            int itemViewType = getItemViewType(position);
            if (itemViewType == DataItem.TYPE_TITLE) {
                ((TitleHolder) holder).content.setText(dataItemList.get(position).key);
            } if (itemViewType == DataItem.TYPE_TITLE_NO_BUY || itemViewType == DataItem.TYPE_TITLE_NOT_ACCOUNT) {
                ((TitleHolder) holder).content.setText(dataItemList.get(position).value);
            } else {
                if (holder instanceof ContentHolder) {
                    ((ContentHolder) holder).key.setText(dataItemList.get(position).key+":");
                    ((ContentHolder) holder).value.setText(dataItemList.get(position).value);
                }
            }
        }

        @Override
        public int getItemCount() {
            return dataItemList == null? 0: dataItemList.size();
        }

        @Override
        public int getItemViewType(int position) {
            return dataItemList == null? 0: dataItemList.get(position).type;
        }

        class TitleHolder extends RecyclerView.ViewHolder{
            private final TextView content;

            public TitleHolder(@NonNull View itemView) {
                super(itemView);
                content = itemView.findViewById(R.id.tv_text);
            }
        }

        class ContentHolder extends RecyclerView.ViewHolder{
            private final TextView key;
            private final TextView value;
            public ContentHolder(@NonNull View itemView) {
                super(itemView);
                key = itemView.findViewById(R.id.tv_key);
                value = itemView.findViewById(R.id.tv_value);
            }
        }


    }

    public static class DataItem{
        public static final int TYPE_TITLE = 1;
        public static final int TYPE_CONTENT = 2;
        public static final int TYPE_TITLE_NO_BUY = 3;
        public static final int TYPE_TITLE_NOT_ACCOUNT = 4;
        public String key;
        public String value;
        public int type;
        public boolean isCurrentAccount;

        public DataItem(String key, String value, int type, boolean isCurrentAccount) {
            this.key = key;
            this.value = value;
            this.type = type;
            this.isCurrentAccount = isCurrentAccount;
        }
    }
}
