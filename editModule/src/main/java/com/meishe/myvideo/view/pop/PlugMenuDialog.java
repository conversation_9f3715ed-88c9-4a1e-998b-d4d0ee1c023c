package com.meishe.myvideo.view.pop;

import android.content.Context;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.myvideo.R;
import com.meishe.myvideo.adapter.BaseSelectAdapter;
import com.meishe.myvideo.adapter.PlugMenuAdapter;
import com.meishe.engine.bean.PlugDetail;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.pop.XPopup;
import com.meishe.third.pop.core.PositionPopupView;

import java.util.List;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2022/4/21 14:43
 * @Description:
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class PlugMenuDialog extends PositionPopupView {
    private RecyclerView mMenuRecyclerview;
    private OnItemClickListener mOnItemClickListener;
    protected BaseSelectAdapter<PlugDetail.Param> mMenuAdapter;
    private List<PlugDetail.Param> params;

    public PlugMenuDialog(@NonNull Context context) {
        super(context);
    }

    public static PlugMenuDialog create(Context context, int offX, int offY, OnItemClickListener listener) {
        return ((PlugMenuDialog) new XPopup
                .Builder(context).dismissOnBackPressed(true)
                .dismissOnTouchOutside(true)
                .offsetX(offX).offsetY(offY).hasShadowBg(false)
                .asCustom(new PlugMenuDialog(context)
                        .setOnItemClickListener(listener)));
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        mMenuRecyclerview = findViewById(R.id.recyclerview);
        LinearLayoutManager layoutManager = new LinearLayoutManagerWrapper(getContext(), LinearLayoutManager.HORIZONTAL, false);
        mMenuRecyclerview.setLayoutManager(layoutManager);
        mMenuAdapter = new PlugMenuAdapter(getContext());
        mMenuRecyclerview.setAdapter(mMenuAdapter);
        mMenuRecyclerview.addItemDecoration(new ItemDecoration((int) getResources().getDimension(R.dimen.dp20),
                (int) getResources().getDimension(R.dimen.dp20)));

        mMenuAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (mOnItemClickListener != null) {
                    dismiss();
                    mOnItemClickListener.onItemClick(params.get(position));
                }
            }
        });
    }

    public void setNewData(List<PlugDetail.Param> params) {
        this.params = params;
    }

    @Override
    protected void doAfterShow() {
        super.doAfterShow();
        if (mMenuAdapter != null) {
            mMenuAdapter.setNewData(params);
        }
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_plug_menu;
    }

    public interface OnItemClickListener {
        void onItemClick(PlugDetail.Param param);
    }

    public PlugMenuDialog setOnItemClickListener(OnItemClickListener listener) {
        this.mOnItemClickListener = listener;
        return this;
    }
}
