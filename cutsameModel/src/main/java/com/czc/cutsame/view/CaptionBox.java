package com.czc.cutsame.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.DashPathEffect;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PointF;
import android.graphics.RectF;
import android.graphics.Region;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.Nullable;

import com.czc.cutsame.R;
import com.czc.cutsame.fragment.interf.CaptionOperationListener;
import com.meishe.base.utils.CommonUtils;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/3/10 16:28
 * @Description :字幕框 Caption box
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CaptionBox extends View {

    private final static int ONE_FINGER = 1;
    private Paint mRectPaint = new Paint();
    /**
     * 虚线画笔
     * The paint for drawing dash path
     */
    private Paint mInsideRectPaint = new Paint();
    private List<PointF> mListPointF;
    private List<List<PointF>> mChildPointF;
    private float targetX;
    private float targetY;
    private CaptionOperationListener mOperationListener;
    private boolean mIsInsideBox;
    private int mClickedCaptionIndex;

    public CaptionBox(Context context) {
        this(context, null);
    }

    public CaptionBox(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CaptionBox(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initRectPaint();
        initInsideRectPaint();
    }

    public void setOperationListener(CaptionOperationListener listener) {
        this.mOperationListener = listener;
    }

    private void initRectPaint() {
        // 设置颜色 Set color.
        mRectPaint.setColor(getResources().getColor(R.color.color_ffff365e));
        // 设置抗锯齿 Set anti alias.
        mRectPaint.setAntiAlias(true);
        // 设置线宽 Set stroke width.
        mRectPaint.setStrokeWidth(3);
        // 设置非填充 Set style.
        mRectPaint.setStyle(Paint.Style.STROKE);
    }

    private void initInsideRectPaint() {
        int dashWidth = 2;
        int dashGap = 2;
        // 设置颜色 Set color.
        mInsideRectPaint.setColor(getResources().getColor(R.color.color_ffff365e));
        // 设置抗锯齿 Set anti alias.
        mInsideRectPaint.setAntiAlias(true);
        // 设置线宽 Set stroke width.
        mInsideRectPaint.setStrokeWidth(dashWidth);
        // 设置非填充 Set style.
        mInsideRectPaint.setStyle(Paint.Style.STROKE);
        //设置虚线效果 Set path effect.
        mInsideRectPaint.setPathEffect(new DashPathEffect(new float[]{dashWidth, dashGap}, 0));
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        canvas.drawPath(getRectPath(mListPointF), mRectPaint);
        drawInsidePath(canvas, mChildPointF);
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {

        if (mOperationListener == null) {
            return false;
        }
        int pointerCount = event.getPointerCount();
        if (pointerCount > ONE_FINGER) {
            return false;
        }

        targetX = event.getX();
        targetY = event.getY();
        oneFingerTouch(event);
        return true;
    }

    private void oneFingerTouch(MotionEvent event) {
        int action = event.getAction();
        if (action == MotionEvent.ACTION_DOWN) {
            //是否在操作框内 Is it in the operation box.
            if (!CommonUtils.isEmpty(mChildPointF)) {
                mIsInsideBox = false;
                for (int index = 0; index < mChildPointF.size(); index++) {
                    if (insideOperationBox(mChildPointF.get(index), (int) targetX, (int) targetY)) {
                        mIsInsideBox = true;
                        mClickedCaptionIndex = index;
                        break;
                    }
                }
            } else {
                if (!CommonUtils.isEmpty(mListPointF)) {
                    mIsInsideBox = insideOperationBox(mListPointF, (int) targetX, (int) targetY);
                    mClickedCaptionIndex = 0;
                }
            }
        } else if (action == MotionEvent.ACTION_UP) {
            if (mOperationListener != null && mIsInsideBox) {
                mOperationListener.onCaptionClicked(mClickedCaptionIndex);
            }
        }
    }


    /**
     * 绘制内部边框路径
     * Draw the internal border path
     */
    private void drawInsidePath(Canvas canvas, List<List<PointF>> insidePointList) {
        Path path;
        if (insidePointList != null) {
            int subCount = insidePointList.size();
            for (int idx = 0; idx < subCount; idx++) {
                List<PointF> listPointF = insidePointList.get(idx);
                if (listPointF == null || listPointF.size() != 4) {
                    continue;
                }
                path = getRectPath(listPointF);
                canvas.drawPath(path, mInsideRectPaint);
            }
        }
    }


    /**
     * 设置边框的四个点
     * Sets the four points of the border
     */
    public void setPointFList(List<PointF> listPointF) {
        mListPointF = listPointF;
        mChildPointF = null;
        invalidate();
    }

    /**
     * 设置边框的四个点
     * Sets the four points of the border
     */
    public void setPointFList(List<PointF> listPointF, List<List<PointF>> childPointF) {
        mListPointF = listPointF;
        mChildPointF = childPointF;
        invalidate();
    }

    /**
     * 获取边框路径
     * Gets the path of border
     */
    private Path getRectPath(List<PointF> pointFList) {
        Path path = new Path();
        if (pointFList != null && pointFList.size() >= 4) {
            path.moveTo(pointFList.get(0).x, pointFList.get(0).y);
            path.lineTo(pointFList.get(1).x, pointFList.get(1).y);
            path.lineTo(pointFList.get(2).x, pointFList.get(2).y);
            path.lineTo(pointFList.get(3).x, pointFList.get(3).y);
            path.close();
        }
        return path;
    }

    /**
     * 所给点的集合是否在操作框内部
     * Whether the set of points given is inside the operation box
     *
     * @return true is inside ,false not
     */
    public boolean insideOperationBox(List<PointF> pointList, int x, int y) {
        if (pointList == null || pointList.size() != 4) {
            return false;
        }
        // 判断手指是否在编辑框内 Determine if the finger is in the edit box.
        RectF r = new RectF();
        Path path = new Path();
        path.moveTo(pointList.get(0).x, pointList.get(0).y);
        path.lineTo(pointList.get(1).x, pointList.get(1).y);
        path.lineTo(pointList.get(2).x, pointList.get(2).y);
        path.lineTo(pointList.get(3).x, pointList.get(3).y);
        path.close();
        path.computeBounds(r, true);
        Region region = new Region();
        region.setPath(path, new Region((int) r.left, (int) r.top, (int) r.right, (int) r.bottom));
        return region.contains(x, y);
    }
}
