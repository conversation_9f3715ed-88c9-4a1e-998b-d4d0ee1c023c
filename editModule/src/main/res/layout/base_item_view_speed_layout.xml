<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="11dp"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="@dimen/dp3"
        android:layout_marginTop="@dimen/dp_px_3"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="3dp"
        android:paddingRight="3dp">

        <ImageView
            android:id="@+id/base_item_view_prop_icon"
            android:layout_width="@dimen/dp_px_30"
            android:layout_height="@dimen/dp_px_30"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="2dp"
            android:background="@drawable/bg_audio_draw_text"
            android:padding="@dimen/dp_px_3"
            android:src="@mipmap/icon_face" />

        <ImageView
            android:id="@+id/base_item_view_volume_icon"
            android:layout_width="@dimen/dp_px_30"
            android:layout_height="@dimen/dp_px_30"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="2dp"
            android:background="@drawable/bg_audio_draw_text"
            android:padding="@dimen/dp_px_3"
            android:src="@mipmap/icon_volume" />

        <LinearLayout
            android:id="@+id/ll_speed"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="2dp"
            android:background="@drawable/bg_audio_draw_text"
            android:paddingStart="@dimen/dp_px_6"
            android:paddingLeft="@dimen/dp_px_6"
            android:paddingEnd="@dimen/dp_px_6"
            android:paddingRight="@dimen/dp_px_6">

            <ImageView
                android:id="@+id/image_speed"
                android:layout_width="7dp"
                android:layout_height="7dp"
                android:layout_gravity="center_vertical"
                android:background="@mipmap/sub_menu_icon_edit_speed" />

            <TextView
                android:id="@+id/base_item_view_speed_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:textColor="@color/white_8"
                android:textSize="@dimen/sp_px_24" />
        </LinearLayout>

        <TextView
            android:id="@+id/base_item_view_time_text"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:background="@drawable/bg_audio_draw_text"
            android:gravity="center"
            android:paddingStart="@dimen/dp_px_6"
            android:paddingLeft="@dimen/dp_px_6"
            android:paddingEnd="@dimen/dp_px_6"
            android:paddingRight="@dimen/dp_px_6"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_24"
            android:visibility="gone" />
    </LinearLayout>

</LinearLayout>