package com.meishe.myvideoapp.activity;

import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsLiveWindow;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsVideoResolution;
import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseActivity;
import com.meishe.base.utils.BarUtils;
import com.meishe.base.utils.GsonUtils;
import com.meishe.base.utils.PermissionUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.logic.bean.SettingParameter;
import com.meishe.logic.constant.PagerConstants;
import com.meishe.logic.manager.PreferencesManager;
import com.meishe.myvideo.view.MYSeekBarTopTextView;
import com.meishe.myvideoapp.R;
import com.meishe.myvideoapp.util.NetConstants;

import java.util.List;

import static com.meishe.base.utils.PermissionConstants.STORAGE;

/**
 * * All rights reserved,Designed by www.meishesdk.com
 * 版权所有www.meishesdk.com设计
 * <AUTHOR> LiHangZhou
 * @CreateDate : 2020/12/09 9:50
 * @Description :设置页面 Setting page
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class SettingActivity extends BaseActivity implements View.OnClickListener {
    private ImageView mIvBack;
    private RadioButton mRb1080, mRb720;
    private TextView mTvFeedback;
    private TextView mTvUserAgreements;
    private TextView mTvPrivacyPolicy;
    private TextView mTvVersionCode;
    private SettingParameter mSettingParameter;
    private MYSeekBarTopTextView mColorGainSeekBar;
    private RadioButton mPreviewModeSdr, mPreviewModeDevice, mPreviewAuto;
    private RadioButton mBitDepth8, mBitDepth16, mBitDepthAudo;

    @Override
    protected int bindLayout() {
        return R.layout.activity_setting;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        mSettingParameter = GsonUtils.fromJson(PreferencesManager.get().getSettingParams(), SettingParameter.class);
    }

    @Override
    protected void initView() {
        BarUtils.setStatusBarColor(this, getResources().getColor(R.color.menu_bg));
        mIvBack = findViewById(R.id.iv_back);
        mRb1080 = findViewById(R.id.rb_ratio_1080);
        mRb720 = findViewById(R.id.rb_ratio_720);
        mTvFeedback = findViewById(R.id.tv_feed_back);
        mTvUserAgreements = findViewById(R.id.tv_user_agreements);
        mTvPrivacyPolicy = findViewById(R.id.tv_privacy_policy);
        mTvVersionCode = findViewById(R.id.tv_version_code_value);
        TextView sdkTvVersionCode = findViewById(R.id.tv_sdk_version_code_value);
        sdkTvVersionCode.setText(NvsConstants.getSDKVersion());
        View hdrImportTextView = findViewById(R.id.tv_hdr_import);
        View hdrPreviewModeTextView = findViewById(R.id.tv_preview_mode);
        View hdrPreviewModeRadioGroup = findViewById(R.id.rg_preview_mode);
        View hdrColorGainTextView = findViewById(R.id.tv_color_gain);
        View bitDepthView = findViewById(R.id.tv_bit_depth);
        View bitDepthRadioGroup = findViewById(R.id.rg_bit_depth);
        View line5 = findViewById(R.id.v_line5);
        mColorGainSeekBar = findViewById(R.id.seek_bar_color_gain);

        NvsStreamingContext instance = NvsStreamingContext.getInstance();
        int engineHDRCaps = 0;
        if (instance != null) {
            engineHDRCaps = instance.getEngineHDRCaps();
        }

        if ((engineHDRCaps & NvsStreamingContext.HDR_CAPABILITY_FLAG_SUPPORTED_BY_IMPORTER) == 0
                || (engineHDRCaps & NvsStreamingContext.HDR_CAPABILITY_FLAG_SUPPORTED_BY_EDITING) == 0
                || (engineHDRCaps & NvsStreamingContext.HDR_CAPABILITY_FLAG_SUPPORTED_BY_LIVEWINDOW) == 0){
            hideView(hdrImportTextView, hdrPreviewModeTextView, hdrPreviewModeRadioGroup, hdrColorGainTextView,
                    mColorGainSeekBar, bitDepthView, bitDepthRadioGroup, line5);
        }

        initListener();
        if (mSettingParameter.getCompileResolution() == NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_720) {
            mRb1080.setChecked(false);
            mRb720.setChecked(true);
        } else {
            mRb1080.setChecked(true);
            mRb720.setChecked(false);
        }

        float hdrColorGain = mSettingParameter.getHdrColorGain();
        if (hdrColorGain > 0) {
            mColorGainSeekBar.setProgress((int) ((hdrColorGain - 1) / 9F * 100F));
        }

        int hdrBitDepth = mSettingParameter.getHdrBitDepth();
        if (hdrBitDepth == NvsVideoResolution.VIDEO_RESOLUTION_BIT_DEPTH_8_BIT) {
            setCheck(mBitDepth8, true);
        } else if (hdrBitDepth == NvsVideoResolution.VIDEO_RESOLUTION_BIT_DEPTH_16_BIT_FLOAT) {
            setCheck(mBitDepth16, true);
        } else if (hdrBitDepth == NvsVideoResolution.VIDEO_RESOLUTION_BIT_DEPTH_AUTO) {
            setCheck(mBitDepthAudo, true);
        }

        int mode = mSettingParameter.getHdrPreViewMode();
        if (mode == NvsLiveWindow.HDR_DISPLAY_MODE_SDR) {
            setCheck(mPreviewModeSdr, true);
        } else if (mode == NvsLiveWindow.HDR_DISPLAY_MODE_DEPEND_DEVICE) {
            setCheck(mPreviewModeDevice, true);
        } else if (mode == NvsLiveWindow.HDR_DISPLAY_MODE_TONE_MAP_SDR) {
            setCheck(mPreviewModeSdr, true);
        }
        mTvVersionCode.setText(Utils.getAppVersionName());
    }

    private void hideView(View ... views){
        for (View view : views) {
            view.setVisibility(View.GONE);
        }
    }

    private void initListener() {
        mIvBack.setOnClickListener(this);
        mRb1080.setOnClickListener(this);
        mRb720.setOnClickListener(this);
        mTvFeedback.setOnClickListener(this);
        mTvUserAgreements.setOnClickListener(this);
        mTvPrivacyPolicy.setOnClickListener(this);
        mTvVersionCode.setOnClickListener(this);
        mPreviewModeSdr = findViewById(R.id.rb_preview_mode_sdr);
        mPreviewModeSdr.setOnClickListener(this);
        mPreviewModeDevice = findViewById(R.id.rb_preview_mode_device);
        mPreviewModeDevice.setOnClickListener(this);
        mPreviewAuto = findViewById(R.id.rb_preview_mode_auto);
        mPreviewAuto.setOnClickListener(this);
        mBitDepth8 = findViewById(R.id.rb_bit_depth_8);
        mBitDepth8.setOnClickListener(this);
        mBitDepth16 = findViewById(R.id.rb_bit_depth_16);
        mBitDepth16.setOnClickListener(this);
        mBitDepthAudo = findViewById(R.id.rb_bit_depth_auto);
        mBitDepthAudo.setOnClickListener(this);
        mColorGainSeekBar.setIntToTextFunction(new MYSeekBarTopTextView.IntToTextFunction() {
            @Override
            public String parseIntToText(int value) {
                return String.valueOf(value / 10F);
            }
        });
        mColorGainSeekBar.setOnSeekBarChangeListener(new MYSeekBarTopTextView.OnSeekBarListener() {
            @Override
            public void onStopTrackingTouch(int progress, String name) {

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                float realProgress = progress / 100F * 9F + 1F;
                mSettingParameter.setHdrColorGain(realProgress);
            }
        });
    }

    @Override
    public void onBackPressed() {
        PreferencesManager.get().setSettingParams(GsonUtils.toJson(mSettingParameter));
        NvsConstants.sHdrBitDepth = mSettingParameter.getHdrBitDepth();
        NvsConstants.sHdrPreviewMode = mSettingParameter.getHdrPreViewMode();
        NvsConstants.sHdrColorGain = mSettingParameter.getHdrColorGain();
        super.onBackPressed();
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.iv_back) {
            onBackPressed();
        } else if (id == R.id.rb_ratio_1080) {
            mRb1080.setChecked(true);
            mRb720.setChecked(false);
            mSettingParameter.setCompileResolution(NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_1080);
        } else if (id == R.id.rb_ratio_720) {
            mRb1080.setChecked(false);
            mRb720.setChecked(true);
            mSettingParameter.setCompileResolution(NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_720);
        } else if (id == R.id.tv_feed_back) {
            if (Utils.isFastClick()) {
                return;
            }
            if (!PermissionUtils.isGroupGranted(STORAGE)) {
                PermissionUtils.permission(STORAGE).callback(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(@NonNull List<String> granted) {
                        if (!PermissionUtils.isGroupGranted(STORAGE)) {
                            ToastUtils.make()
                                    .setGravity(Gravity.CENTER, 0, 0)
                                    .setDurationIsLong(false)
                                    .show(R.string.lack_of_authority);
                        }
                    }

                    @Override
                    public void onDenied(@NonNull List<String> deniedForever, @NonNull List<String> denied) {
                        if (deniedForever.size() > 0) {
                            ToastUtils.make()
                                    .setGravity(Gravity.CENTER, 0, 0)
                                    .setDurationIsLong(false)
                                    .show(R.string.lack_of_authority);
                        }
                    }
                }).request();
            } else {
            AppManager.getInstance().jumpActivity(this, FeedBackActivity.class);
            }
        } else if (id == R.id.tv_user_agreements) {
            gotoWebPage(true);
        } else if (id == R.id.tv_privacy_policy) {
            gotoWebPage(false);
        } else if (id == R.id.rb_preview_mode_sdr) {
            mSettingParameter.setHdrPreViewMode(NvsLiveWindow.HDR_DISPLAY_MODE_SDR);
            setCheck(mPreviewModeDevice, false);
            setCheck(mPreviewAuto, false);
        } else if (id == R.id.rb_preview_mode_device) {
            mSettingParameter.setHdrPreViewMode(NvsLiveWindow.HDR_DISPLAY_MODE_DEPEND_DEVICE);
            setCheck(mPreviewModeSdr, false);
            setCheck(mPreviewAuto, false);
        } else if (id == R.id.rb_preview_mode_auto) {
            mSettingParameter.setHdrPreViewMode(NvsLiveWindow.HDR_DISPLAY_MODE_TONE_MAP_SDR);
            setCheck(mPreviewModeSdr, false);
            setCheck(mPreviewModeDevice, false);
        } else if (id == R.id.rb_bit_depth_8) {
            mSettingParameter.setHdrBitDepth(NvsVideoResolution.VIDEO_RESOLUTION_BIT_DEPTH_8_BIT);
            setCheck(mBitDepth16, false);
            setCheck(mBitDepthAudo, false);
        } else if (id == R.id.rb_bit_depth_16) {
            mSettingParameter.setHdrBitDepth(NvsVideoResolution.VIDEO_RESOLUTION_BIT_DEPTH_16_BIT_FLOAT);
            setCheck(mBitDepth8, false);
            setCheck(mBitDepthAudo, false);
        } else if (id == R.id.rb_bit_depth_auto) {
            mSettingParameter.setHdrBitDepth(NvsVideoResolution.VIDEO_RESOLUTION_BIT_DEPTH_AUTO);
            setCheck(mBitDepth8, false);
            setCheck(mBitDepth16, false);
        }
    }

    private void setCheck(RadioButton button, boolean checked){
        button.setChecked(checked);
    }

    /**
     * 跳转到web页面
     * goto web page
     */
    private void gotoWebPage(boolean isUserAgreements) {
        if (Utils.isFastClick()) {
            return;
        }
        Bundle bundle = new Bundle();
        if (Utils.isZh()) {
            bundle.putString(PagerConstants.URL, isUserAgreements ? NetConstants.USER_AGREEMENTS : NetConstants.PRIVACY_POLICY_URL);
        } else {
            bundle.putString(PagerConstants.URL, isUserAgreements ? NetConstants.USER_AGREEMENTS_EN : NetConstants.PRIVACY_POLICY_URL_EN);
        }
        AppManager.getInstance().jumpActivity(this, WebViewActivity.class, bundle);
    }
}
