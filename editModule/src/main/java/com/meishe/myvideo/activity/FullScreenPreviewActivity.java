package com.meishe.myvideo.activity;

import android.annotation.SuppressLint;
import android.content.pm.ActivityInfo;
import android.os.Bundle;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.SeekBar;
import android.widget.TextView;

import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsVideoResolution;
import com.meishe.base.model.BaseActivity;
import com.meishe.base.utils.FormatUtils;

import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.logic.utils.UMengUtils;
import com.meishe.myvideo.R;
import com.meishe.player.fragment.PlayerFragment;
import com.meishe.base.utils.LogUtils;

import androidx.fragment.app.FragmentManager;

import static com.meishe.logic.constant.PagerConstants.START_TIME;

/**
 * 全屏预览页面
 * Full Screen preview page
 */
public class FullScreenPreviewActivity extends BaseActivity implements View.OnClickListener {
    private FrameLayout mFlFragmentContainer;
    private ImageView mIvPlay;
    private ImageView mIvClose;
    private TextView mTvPlayTime;
    private SeekBar mPlaySeekBar;
    private PlayerFragment mPlayerFragment;
    private MeicamTimeline mTimeline;
    private EditorEngine mEditorEngine;
    private long startTime;
    private boolean mTouchToChangeProgress = false;

    private final PlayerFragment.PlayEventListener listener = new PlayerFragment.PlayEventListener() {
        @Override
        public void onPlayBackPrepare() {

        }

        @Override
        public void playBackEOF(NvsTimeline timeline) {
            mPlaySeekBar.setProgress(0);
            mEditorEngine.seekTimeline(0, 0);
        }

        @Override
        public void playStopped(NvsTimeline timeline) {

        }

        @Override
        public void playbackTimelinePosition(NvsTimeline timeline, long stamp) {
            mTvPlayTime.setText(FormatUtils.microsecond2Time(stamp));
            if (!mTouchToChangeProgress) {
                mPlaySeekBar.setProgress((int) (stamp / 1000));
            }
        }

        @Override
        public void streamingEngineStateChanged(int state) {
            boolean isPlaying = state == NvsStreamingContext.STREAMING_ENGINE_STATE_PLAYBACK;
            if (isPlaying) {
                mIvPlay.setImageResource(R.mipmap.control_bar_ic_pause);
            } else {
                mIvPlay.setImageResource(R.mipmap.control_bar_ic_play);
            }
        }
    };

    @Override
    protected int bindLayout() {
        return R.layout.activity_full_preview;
    }

    @SuppressLint("SourceLockedOrientationActivity")
    @Override
    protected void initData(Bundle savedInstanceState) {
        if (getIntent() != null && null != getIntent().getExtras()) {
            Bundle extras = getIntent().getExtras();
            startTime = extras.getLong(START_TIME);
        }
        mEditorEngine = EditorEngine.getInstance();
        mTimeline = mEditorEngine.getCurrentTimeline();
        if (mTimeline == null) {
            UMengUtils.generateCustomLog("FullScreenPreviewActivity mTimeline==null");
            throw new NullPointerException("FullScreenPreviewActivity mTimeline==null");
        }
        NvsVideoResolution videoResolution = mTimeline.getVideoResolution();
        LogUtils.d("startTime=" + startTime + ",width=" + videoResolution.imageWidth + ",height=" + videoResolution.imageHeight);
        if (videoResolution.imageWidth - videoResolution.imageHeight > 0) {
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
        }
    }

    @Override
    protected void initView() {
        mFlFragmentContainer = findViewById(R.id.fl_fragment_container);
        mIvPlay = findViewById(R.id.iv_play);
        mIvClose = findViewById(R.id.iv_close);
        mTvPlayTime = findViewById(R.id.tv_play_time);
        TextView tvPlayDuration = findViewById(R.id.tv_play_duration);
        mPlaySeekBar = findViewById(R.id.seek_bar);

        mTvPlayTime.setText(FormatUtils.microsecond2Time(startTime));
        tvPlayDuration.setText(FormatUtils.microsecond2Time(mTimeline.getDuration()));
        mPlaySeekBar.setMax((int) (mTimeline.getDuration() / 1000));
        mPlaySeekBar.setProgress((int) (startTime / 1000));


        initVideoFragment();
        initListener();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (mPlayerFragment != null) {
            mPlayerFragment.setPlayListener(listener);
        }
    }

    private void initVideoFragment() {
        FragmentManager fragmentManager = getSupportFragmentManager();
        mPlayerFragment = PlayerFragment.create();
        mPlayerFragment.setTimelineAndContext(mTimeline, mEditorEngine.getStreamingContext());
        mPlayerFragment.setPlayListener(listener);
        fragmentManager.beginTransaction().add(R.id.fl_fragment_container, mPlayerFragment).commitAllowingStateLoss();
        fragmentManager.beginTransaction().show(mPlayerFragment);
        /*
         * 此处是等页面加载完毕后再seek
         * Here is seek after the page loads
         * */
        mFlFragmentContainer.post(new Runnable() {
            @Override
            public void run() {
               /* mPlayerFragment.seekTimeline(startTime, NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER
                        | NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_ANIMATED_STICKER_POSTER);*/

                /*
                 * 贴纸添加动画后，需要去掉NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_ANIMATED_STICKER_POSTER标记
                 * After adding animation to the sticker, you need to remove NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_ANIMATED_STICKER_POSTER tag.
                 */
                mPlayerFragment.seekTimeline(startTime, 0);
            }
        });
    }

    private void initListener() {
        mIvPlay.setOnClickListener(this);
        mIvClose.setOnClickListener(this);
        mPlaySeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    mEditorEngine.seekTimeline(progress * 1000L, 0);
                    mTvPlayTime.setText(FormatUtils.millisecond2Time(progress));
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                mEditorEngine.stop();
                mTouchToChangeProgress = true;
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                mTouchToChangeProgress = false;
            }
        });
    }

    @Override
    protected void onStop() {
        super.onStop();
        mPlayerFragment.stopVideo();
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.iv_play) {
            int state = NvsStreamingContext.getInstance().getStreamingEngineState();
            if (state == NvsStreamingContext.STREAMING_ENGINE_STATE_PLAYBACK) {
                mPlayerFragment.stopVideo();
            } else {
                long currentPosition =mTimeline.getCurrentPosition();
                if (currentPosition == mTimeline.getDuration()) {
                    currentPosition = 0;
                }
                mPlayerFragment.playVideo(currentPosition, mTimeline.getDuration(), 0);
            }
        } else if (v.getId() == R.id.iv_close) {
            finish();
        }
    }
}
