package com.meishe.draft.db;

import androidx.room.Database;
import androidx.room.RoomDatabase;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/2/8 15:39
 * @Description :草稿数据库 Database
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
@Database(entities = {LocalDraftEntity.class, UserDraftEntity.class, FileInfoEntity.class, ResourceEntity.class, ProjectEntity.class, JobInfoEntity.class}, version = 3, exportSchema = false)
public abstract class DraftDatabase extends RoomDatabase {
    public abstract LocalDraftDao getLocalDraftDao();
    public abstract UserDraftDao getUserDraftDao();
    public abstract FileInfoDao getFileInfoDao();
    public abstract ResourceDao getResourceDao();
    public abstract ProjectDao getProjectDao();
    public abstract JobInfoDao getJobInfoDao();
}
