package com.meishe.user.bean;

import java.io.Serializable;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/5/13 13:42
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class ResourceInfo implements Serializable {
   private int resourceCount;
   private List<Resource> resourceList;

   public int getResourceCount() {
      return resourceCount;
   }

   public void setResourceCount(int resourceCount) {
      this.resourceCount = resourceCount;
   }

   public List<Resource> getResourceList() {
      return resourceList;
   }

   public void setResourceList(List<Resource> resourceList) {
      this.resourceList = resourceList;
   }

   public static class Resource implements Serializable{
      public static int MEDIA_TYPE_VIDEO = 1;
      public static int MEDIA_TYPE_AUDIO = 2;
      public static int MEDIA_TYPE_IMAGE = 3;
      private String alphaM3u8Url;
      private String alphaUrl;
      private String coverUrl;
      private String m3u8Url;
      private String url;
      /**
       * 右声道波形
       * The url of right channel
       */
      private String rightChannelUrl;
      /**
       * 左声道波形
       *  The url of left channel
       */
      private String leftChannelUrl;

      /**
       * 文件类型：1视频,2音频,3图片
       * Media type: 1 video, 2 audio, 3 pictures
       */
      private int mediaType;
      private long duration;
      private int height;
      private int width;
      private String realId;
      private String id;

      private long fileSize;

      private ThumbnailInfo thumbnailInfo;

      private List<ThumbnailItem> thumbnails;

      public String getAlphaM3u8Url() {
         return alphaM3u8Url;
      }

      public void setAlphaM3u8Url(String alphaM3u8Url) {
         this.alphaM3u8Url = alphaM3u8Url;
      }

      public String getAlphaUrl() {
         return alphaUrl;
      }

      public void setAlphaUrl(String alphaUrl) {
         this.alphaUrl = alphaUrl;
      }

      public String getCoverUrl() {
         return coverUrl;
      }

      public void setCoverUrl(String coverUrl) {
         this.coverUrl = coverUrl;
      }

      public String getM3u8Url() {
         return m3u8Url;
      }

      public void setM3u8Url(String m3u8Url) {
         this.m3u8Url = m3u8Url;
      }

      public long getDuration() {
         return duration;
      }

      public void setDuration(long duration) {
         this.duration = duration;
      }

      public int getHeight() {
         return height;
      }

      public void setHeight(int height) {
         this.height = height;
      }

      public int getWidth() {
         return width;
      }

      public void setWidth(int width) {
         this.width = width;
      }

      public String getUrl() {
         return url;
      }

      public void setUrl(String url) {
         this.url = url;
      }

      public String getRightChannelUrl() {
         return rightChannelUrl;
      }

      public void setRightChannelUrl(String rightChannelUrl) {
         this.rightChannelUrl = rightChannelUrl;
      }

      public String getLeftChannelUrl() {
         return leftChannelUrl;
      }

      public void setLeftChannelUrl(String leftChannelUrl) {
         this.leftChannelUrl = leftChannelUrl;
      }

      public int getMediaType() {
         return mediaType;
      }

      public void setMediaType(int mediaType) {
         this.mediaType = mediaType;
      }

      public ThumbnailInfo getThumbnailInfo() {
         return thumbnailInfo;
      }

      public void setThumbnailInfo(ThumbnailInfo thumbnailInfo) {
         this.thumbnailInfo = thumbnailInfo;
      }

      public List<ThumbnailItem> getThumbnails() {
         return thumbnails;
      }

      public void setThumbnails(List<ThumbnailItem> thumbnails) {
         this.thumbnails = thumbnails;
      }

      public String getRealId() {
         return realId;
      }

      public void setRealId(String realId) {
         this.realId = realId;
      }

      public String getId() {
         return id;
      }

      public void setId(String id) {
         this.id = id;
      }

      public long getFileSize() {
         return fileSize;
      }

      public void setFileSize(long fileSize) {
         this.fileSize = fileSize;
      }
   }

   public static class ThumbnailInfo implements Serializable{
      public String extension;
      public String urlPrefix;
      public long interval;
   }

   public static class ThumbnailItem implements Serializable{
      public long time;
      public String url;
   }
}
