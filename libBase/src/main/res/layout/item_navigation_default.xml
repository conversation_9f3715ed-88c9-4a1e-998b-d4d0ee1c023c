<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp_px_186"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/ll_item_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical">
        <!--加一层布局是为了点击范围-->
        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="@dimen/dp_px_52"
            android:layout_height="@dimen/dp_px_52"
            android:layout_gravity="center_horizontal"
            android:contentDescription="@null" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:ellipsize="end"
            android:paddingTop="@dimen/dp_px_15"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_px_30" />
    </LinearLayout>

</FrameLayout>
