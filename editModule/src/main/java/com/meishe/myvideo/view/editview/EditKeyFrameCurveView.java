package com.meishe.myvideo.view.editview;

import android.content.Context;
import android.graphics.PointF;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.model.BaseMvpView;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.engine.bean.CurveAdjustData;
import com.meishe.engine.view.NvBezierFrameView;
import com.meishe.myvideo.R;
import com.meishe.myvideo.adapter.KeyFrameCurveAdapter;
import com.meishe.myvideo.interfaces.BottomEventListener;
import com.meishe.myvideo.view.interf.CurveKeyFrameView;
import com.meishe.myvideo.view.interf.IBottomView;
import com.meishe.myvideo.view.presenter.KeyFrameCurvePresenter;
import com.meishe.third.adpater.BaseQuickAdapter;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/8/9 13:41
 * @Description :曲线关键帧列表View Curve keyframe list view
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class EditKeyFrameCurveView extends BaseMvpView<KeyFrameCurvePresenter> implements CurveKeyFrameView, IBottomView, View.OnClickListener {
    protected RecyclerView mRecyclerView;
    private ImageView mIvConfirm;
    protected KeyFrameCurveAdapter mAdapter;
    private OnEventListener mEventListener;
    private NvBezierFrameView mBezierView;
    private int mCurrentPosition = 0;
    private View mBezierViewLayout;

    public EditKeyFrameCurveView(Context context) {
        super(context);
    }

    @Override
    protected KeyFrameCurvePresenter createPresenter() {
        return new KeyFrameCurvePresenter();
    }

    @Override
    public void initView() {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.view_menu_key_frame_curve, this);
        mRecyclerView = view.findViewById(R.id.width_confirm_menu_recycleView);
        mIvConfirm = view.findViewById(R.id.iv_confirm);
        mBezierView = view.findViewById(R.id.bezier_view);
        mBezierViewLayout = view.findViewById(R.id.bezier_view_layout);
        mBezierView.setTouchPointCallback(new NvBezierFrameView.OnTouchPointCallback() {
            @Override
            public void onPointsChanged(PointF leftBottomPoint, PointF rightTopPoint) {
                CurveAdjustData item = mAdapter.getItem(mCurrentPosition);
                if (item != null) {
                    item.setFrontControlPointF(leftBottomPoint);
                    item.setBackControlPointF(rightTopPoint);
                }
                if (mEventListener != null) {
                    mEventListener.onItemClick(item, 0);
                }
            }
        });
        initRecyclerView();
        initListener();

    }

    @Override
    protected void initData() {
        mPresenter.loadData();
    }

    public void initRecyclerView() {
        mAdapter = new KeyFrameCurveAdapter();
        LinearLayoutManager layoutManager = new LinearLayoutManagerWrapper(getContext(), LinearLayoutManager.HORIZONTAL, false);
        mRecyclerView.setLayoutManager(layoutManager);
        mRecyclerView.setAdapter(mAdapter);
        mAdapter.setSelectPosition(mCurrentPosition);
        mRecyclerView.addItemDecoration(new ItemDecoration(7, 7));
    }

    public void initListener() {
        mIvConfirm.setOnClickListener(this);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                mCurrentPosition = position;
                CurveAdjustData item = mAdapter.getItem(position);
                setSelection(position);
                if (item.isCustom()) {
                    mBezierViewLayout.setVisibility(VISIBLE);
                    mRecyclerView.setVisibility(GONE);
                } else {
                    if (mEventListener != null) {
                        mEventListener.onItemClick(item, position);
                    }
                }
            }
        });
    }


    private void hide() {
        if (mEventListener != null) {
            mEventListener.dismiss();
        }
    }

    public void setSelection(int selection) {
        mAdapter.setSelectPosition(selection);
        mRecyclerView.scrollToPosition(selection);
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.iv_confirm) {
            if (mBezierViewLayout.getVisibility() == VISIBLE) {
                mBezierViewLayout.setVisibility(GONE);
                mRecyclerView.setVisibility(VISIBLE);
            } else {
                hide();
                mPresenter.confirm();
            }
        }
    }

    @Override
    public void onDataBack(List<CurveAdjustData> list) {
        mAdapter.setNewData(list);
    }

    public void setPointsInView(Pair<PointF, PointF> keyFramePointsInView) {
        if (keyFramePointsInView != null) {
            mBezierView.updateControlPoint(keyFramePointsInView.first, keyFramePointsInView.second);
        }
    }

    @Override
    public void setListener(BottomEventListener listener) {
        mEventListener = (OnEventListener) listener;
    }

    @NonNull
    @Override
    public BottomEventListener getListener() {
        return mEventListener;
    }

    public static abstract class OnEventListener extends BottomEventListener {
        /**
         * 某一项被点击
         * A certain item is clicked
         *
         * @param adjustData 曲线条件信息 the curve adjust data
         * @param position   位置 the position
         */
        public void onItemClick(CurveAdjustData adjustData, int position) {

        }
    }
}
