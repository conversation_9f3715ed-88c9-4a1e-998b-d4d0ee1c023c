package com.meishe.myvideo.view.interf;

import androidx.annotation.NonNull;

import com.meishe.myvideo.interfaces.BottomEventListener;


/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>Z<PERSON>
 * @CreateDate :2023/2/14 17:24
 * @Description :底部视图接口，放入底部视图容器中必须要实现
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface IBottomFragment {
    void setListener(BottomEventListener listener);

    @NonNull
    BottomEventListener getListener();
}
