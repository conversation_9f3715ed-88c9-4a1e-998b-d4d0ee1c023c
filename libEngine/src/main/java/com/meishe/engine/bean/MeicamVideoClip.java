package com.meishe.engine.bean;

import android.annotation.SuppressLint;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsARSceneManipulate;
import com.meicam.sdk.NvsAudioFx;
import com.meicam.sdk.NvsClipCaption;
import com.meicam.sdk.NvsClipCompoundCaption;
import com.meicam.sdk.NvsObject;
import com.meicam.sdk.NvsPanAndScan;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsVideoClip;
import com.meicam.sdk.NvsVideoFx;
import com.meishe.base.constants.Constants;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.adapter.parser.IResourceParser;
import com.meishe.engine.bean.bridges.FileInfoBridge;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.local.LMeicamAdjustData;
import com.meishe.engine.local.LMeicamAudioFx;
import com.meishe.engine.local.LMeicamKeyFrame;
import com.meishe.engine.local.LMeicamVideoClip;
import com.meishe.engine.local.LMeicamVideoFx;
import com.meishe.engine.local.background.LMeicamStoryboardInfo;
import com.meishe.engine.util.DeepCopyUtil;
import com.meishe.engine.util.gson.GsonContext;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import static com.meishe.engine.bean.CommonData.ATTACHMENT_KEY_VIDEO_CLIP_AR_SCENE;
import static com.meishe.engine.bean.CommonData.VIDEO_FLAG_IS_NEW_BEAUTY_SHAPE_DATA;
import static com.meishe.engine.bean.CommonData.VIDEO_FX_AR_SCENE;
import static com.meishe.engine.bean.CommonData.VIDEO_FX_BEAUTY_EFFECT;
import static com.meishe.engine.bean.CommonData.VIDEO_FX_BEAUTY_SHAPE;
import static com.meishe.engine.bean.CommonData.VIDEO_FX_BEAUTY_SHAPE_MESH;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_FLOAT;
import static com.meishe.engine.bean.MeicamVideoFx.INVALID_VALUE;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_ADJUST;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_ALPHA;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_AR_SCENE;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_CLIP_EFFECT;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_CLIP_FILTER;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_CROPPER;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_CROPPER_EXT;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_CROPPER_TRANSFORM;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_MASK;
import static com.meishe.engine.constant.NvsConstants.PROPERTY_FX;
import static com.meishe.engine.constant.NvsConstants.TYPE_BUILD_IN;
import static com.meishe.engine.constant.NvsConstants.TYPE_PACKAGE;
import static com.meishe.engine.constant.NvsConstants.TYPE_PROPERTY;
import static com.meishe.engine.constant.NvsConstants.TYPE_RAW_BUILTIN;
import static com.meishe.engine.constant.NvsConstants.TYPE_RAW_PACKAGE;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 版权所有:www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/05/20 10:07
 * @Description : 视频/图片片段 The video clip
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MeicamVideoClip extends ClipInfo<NvsVideoClip> implements Cloneable, Serializable, IResourceParser, TimelineDataParserAdapter<LMeicamVideoClip> {
    /**
     * 对应资源id 和resources对应
     * The id
     */
    private String id;

    /**
     * 远程路径
     * The remote path of video clip
     */
    private String remotePath;

    /**
     * 左声道波形
     * The url for left channel
     */
    private String leftChannelUrl;

    /**
     * 右声道波形
     * The url for right channel
     */
    private String rightChannelUrl;

    /**
     * 片段的视频或者图片路径
     * The video or picture  path of video clip
     */
    private String filePath;
    /**
     * 片段的倒放视频路径
     * The video played backwards  path of video clip
     */
    String reverseFilePath;
    /**
     * 片段类型
     * The video clip type
     */
    private String videoType;
    /**
     * 裁入点
     * the trim in point of video clip
     */
    long trimIn;
    /**
     * 裁出点
     * the trim out point of video clip
     */
    long trimOut;

    /**
     * move trimIn和trimOut时的偏移量
     * The offset when moving trimIn and trimOut
     */
    transient long trimOffSet;
    /**
     * 片段原始时长
     * the original duration of video clip
     */
    private long orgDuration;
    /**
     * 片段音量值
     * The clip volume
     */
    private float volume = 1.0f;
    /**
     * 片段变速值
     * The clip speed
     */
    private double speed = 1.0f;
    /**
     * 片段曲线变速值
     * The curve speed value of clip
     */
    String curveSpeed = "";
    /**
     * 片段曲线变速名称
     * The curve speed name of clip
     */
    private String curveSpeedName = "";
    /**
     * 是否倒放
     * Whether this backwards
     */
    private boolean isVideoReverse = false;

    /**
     * 是否转码成功，视频默认未转码成功
     * Whether this convert success
     */
    private boolean isConvertSuccess = false;

    /**
     * 图片展示模式
     * Picture display mode
     */
    private int mImageMotionMode = 0;

    private boolean imageMotionAnimationEnabled;

    /**
     * 视频横向裁剪，纵向平移
     * Video cropped horizontally, panned vertically
     */
    private float mSpan = 0;
    private float mScan = 0;

    /**
     * 透明度
     * The clip opacity
     */
    private float opacity = 1f;
    /**
     * 旋转角度
     * to-left-90  to-right-90  horizontal vertical
     */
    private int extraRotation;
    /**
     * 镜像
     * The mirror
     */
    private boolean reverse;

    /**
     * 特效集合clip设置的VideoFx 如滤镜 transform 2D 等
     * Some video fx list of clip
     */
    private final List<MeicamVideoFx> videoFxs = new ArrayList<>();
    /**
     * 调节特效
     * Adjust effect
     */
    private final Map<String, MeicamVideoFx> mAdjustData = new HashMap<>();

    /**
     * 在主题中的成分。片头或者片尾
     * The role in theme
     */
    private int mRoleInTheme;

    /**
     * 美颜（美肤）美型特效参数集合
     * Beauty and beauty shape param map
     */
    private final Map<String, MeicamStoryboardInfo> mStoryboardMap = new TreeMap<>();
    private String resourceId;

    /**
     * 变调
     * Keep audio pitch or not
     */
    private boolean keepAudioPitch = true;
    /**
     * 原始宽度
     * The clip original width
     */
    private int originalWidth;
    /**
     * 原始高度
     * The clip original height
     */
    private int originalHeight;

    private float rectWidth;

    private float rectHeight;

    /**
     * 设置过的曲线变速的集合
     * The set of curve speed changes that have been set
     */
    private List<CurveSpeed> curveSpeedList = new ArrayList<>();

    /**
     * 合成模式
     * mixed mode
     */
    private int blendingMode;
    /**
     * mask Data
     * 蒙版数据
     */
    public NvMaskModel maskModel = new NvMaskModel();


    /**
     * 音频特效集合
     * The audio fx list
     */
    private final List<MeicamAudioFx> audioFxList = new ArrayList<>();


    /**
     * clip里的timeline
     *
     * The timeline in clip
     */
    private MeicamTimeline internalTimeline;

    protected List<MeicamCaptionClip> captionList = new ArrayList<>();

    protected List<MeicamCompoundCaptionClip> compoundCaptionList = new ArrayList<>();

    private ThumbNailInfo thumbNailInfo;

    /**
     * 原始内建特效
     * rawBuildInFx list
     */
    private transient List<MeicamVideoFx> rawBuildInFx;
    /**
     * 音频淡入的时长
     * The fadeIn duration
     */
    private long fadeInDuration;
    /**
     * 音频淡出的时长
     * The fade out duration
     */
    private long fadeOutDuration;
    private boolean enableRawSourceMode;

    MeicamVideoClip(NvsVideoClip videoClip, String videoType) {
        super(videoClip, CommonData.CLIP_VIDEO);
        this.videoType = videoType;
        if (videoClip != null) {
            this.filePath = videoClip.getFilePath();
            super.inPoint = videoClip.getInPoint();
            this.trimIn = videoClip.getTrimIn();
            this.trimOut = videoClip.getTrimOut();
        }
    }


    /**
     * 注意：此方法仅用来刷新和native层关联的属性
     * Note: This method is only used to refresh the attributes associated with the native layer
     */
    @Override
    void loadData() {
        NvsVideoClip videoClip = getObject();
        if (videoClip == null) {
            return;
        }
        setObject(videoClip);
        /*
         * 恢复片段字幕
         * Restore clip caption
         */
        NvsClipCaption nvsClipCaption = videoClip.getFirstCaption();
        while (nvsClipCaption != null) {
            MeicamCaptionClip meicamCaptionClip = new MeicamCaptionClip(nvsClipCaption,
                    nvsClipCaption.getText(), nvsClipCaption.getInPoint(), nvsClipCaption.getOutPoint());
            meicamCaptionClip.loadData();
            captionList.add(meicamCaptionClip);
            nvsClipCaption = videoClip.getNextCaption(nvsClipCaption);
        }

        /*
         * 恢复轨道组合字幕
         * Restore clip compound caption
         */
        NvsClipCompoundCaption nvsClipCompoundCaption = videoClip.getFirstCompoundCaption();
        while (nvsClipCompoundCaption != null) {
            MeicamCompoundCaptionClip meicamCompoundCaptionClip
                    = new MeicamCompoundCaptionClip(nvsClipCompoundCaption);
            meicamCompoundCaptionClip.loadData();
            compoundCaptionList.add(meicamCompoundCaptionClip);
            nvsClipCompoundCaption = videoClip.getNextCaption(nvsClipCompoundCaption);
        }

        setInPoint(videoClip.getInPoint());
        setOutPoint(videoClip.getOutPoint());
        isVideoReverse = videoClip.getPlayInReverse();
        if (isVideoReverse) {
            reverseFilePath = videoClip.getFilePath();
        } else {
            filePath = videoClip.getFilePath();
        }
        internalTimeline = getInternalTimeline();
        FileInfoBridge.FileInfo fileInfo = FileInfoBridge.getFileInfo(getFilePath());
        if (fileInfo != null) {
            orgDuration = fileInfo.duration;
            originalHeight = fileInfo.height;
            originalWidth = fileInfo.width;
            remotePath = fileInfo.remotePath;
            leftChannelUrl = fileInfo.getLeftChannelUrl();
            rightChannelUrl = fileInfo.getRightChannelUrl();
            FileInfoBridge.ThumbNailInfo thumbNailInfo = fileInfo.getThumbNailInfo();
            if (thumbNailInfo != null) {
                this.thumbNailInfo = new ThumbNailInfo(thumbNailInfo.urlPrefix, thumbNailInfo.interval, thumbNailInfo.extension);
            }
        }
        volume = videoClip.getVolumeGain().leftVolume;
        trimIn = videoClip.getTrimIn();
        trimOut = videoClip.getTrimOut();
        speed = videoClip.getSpeed();
        fadeInDuration = videoClip.getAudioFadeInDuration();
        fadeOutDuration = videoClip.getAudioFadeOutDuration();
        extraRotation = videoClip.getExtraVideoRotation();
        mRoleInTheme = videoClip.getRoleInTheme();
        opacity = videoClip.getOpacity();
        imageMotionAnimationEnabled = videoClip.getImageMotionAnimationEnabled();
        mImageMotionMode = videoClip.getImageMotionMode();
        NvsPanAndScan panAndScan = videoClip.getPanAndScan();
        this.mSpan = panAndScan.pan;
        this.mScan = panAndScan.scan;
        this.curveSpeed = videoClip.getClipVariableSpeedCurvesString();
        this.blendingMode = videoClip.getBlendingMode();
        MeicamVideoFx videoFx = findPropertyVideoFx();
        if (videoFx != null) {
            videoFx.setInPoint(getInPoint());
            videoFx.setOutPoint(getOutPoint());
            NvsVideoFx object = videoFx.getObject();
            if (object != null) {
                opacity = (float) object.getFloatVal(NvsConstants.PROPERTY_OPACITY);
            }
            videoFx.recoverFromTimelineData(object);
        }

        int rawFxCount = videoClip.getRawFxCount();
        int maskIndex = -1;
        int cropTransIndex = -1;
        int cropIndex = -1;
        if (rawFxCount > 0) {
            for (int i = 0; i < rawFxCount; i++) {
                NvsVideoFx nvsVideoFx = videoClip.getRawFxByIndex(i);
                int videoFxType = nvsVideoFx.getVideoFxType();
                if (videoFxType == NvsVideoFx.VIDEOFX_TYPE_BUILTIN) {
                    String videoFxName = nvsVideoFx.getBuiltinVideoFxName();
                    boolean isMask = NvsConstants.KEY_MASK_GENERATOR.equals(videoFxName);
                    boolean isCrop = NvsConstants.Crop.NAME.equals(videoFxName);
                    if (isMask || isCrop) {
                        int preIndex = i - 1;
                        if (preIndex >= 0 && preIndex < rawFxCount) {
                            NvsVideoFx preVideoFx = videoClip.getRawFxByIndex(preIndex);
                            //这个Mask Generator特效是用来裁剪的
                            //This mask generator special effect is used for tailoring
                            if (NvsConstants.FX_TRANSFORM_2D.equals(preVideoFx.getBuiltinVideoFxName())) {
                                cropIndex = i;
                                cropTransIndex = preIndex;
                                continue;
                            }
                        }
                        maskIndex = i;
                    }
                }
            }
        }

        if (rawFxCount > 0) {
            for (int i = 0; i < rawFxCount; i++) {
                NvsVideoFx nvsVideoFx = videoClip.getRawFxByIndex(i);
                int videoFxType = nvsVideoFx.getVideoFxType();
                if (videoFxType == NvsVideoFx.VIDEOFX_TYPE_PACKAGE) {
                    videoFx = appendVideoFx(nvsVideoFx, CommonData.TYPE_PACKAGE, SUB_TYPE_CLIP_FILTER,
                            nvsVideoFx.getVideoFxPackageId(), true);
                } else {
                    String videoFxName = nvsVideoFx.getBuiltinVideoFxName();
                    String subType;
                    if (i == maskIndex) {
                        subType = SUB_TYPE_MASK;
                    } else if (i == cropTransIndex) {
                        subType = SUB_TYPE_CROPPER_TRANSFORM;
                    } else if (i == cropIndex) {
                        subType = NvsConstants.Crop.NAME.equals(videoFxName) ? SUB_TYPE_CROPPER_EXT : SUB_TYPE_CROPPER;
                    } else {
                        subType = getSubType(videoFxName);
                    }
                    videoFx = appendVideoFx(nvsVideoFx, CommonData.TYPE_RAW_BUILTIN, subType,
                            videoFxName, true);
                }
                videoFx.recoverFromTimelineData(nvsVideoFx);
                videoFx.setInPoint(getInPoint());
                videoFx.setOutPoint(getOutPoint());
            }
        }
        //普通特效,目前有滤镜和特效两种类型
        //There are two types of ordinary special effects: filter and special effects
        int fxCount = videoClip.getFxCount();
        if (fxCount > 0) {
            for (int index = 0; index < fxCount; index++) {
                NvsVideoFx nvsVideoFx = videoClip.getFxByIndex(index);
                String packageId = nvsVideoFx.getVideoFxPackageId();
                boolean isBuildIn = TextUtils.isEmpty(packageId);
                String type = isBuildIn ? CommonData.TYPE_BUILD_IN : CommonData.TYPE_PACKAGE;
                String desc = isBuildIn ? nvsVideoFx.getBuiltinVideoFxName() : packageId;
                //
                String subType = SUB_TYPE_CLIP_FILTER;
                if (CommonData.TYPE_PACKAGE.equals(type)) {
                    FileInfoBridge.FileInfo effectFileInfo = FileInfoBridge.getFileInfo(packageId);
                    if (effectFileInfo != null) {
                        if (effectFileInfo.category == 2) {
                            //特效 effect
                            subType = SUB_TYPE_CLIP_EFFECT;
                        }
                    }
                }
                videoFx = appendVideoFx(nvsVideoFx, type, subType, desc, true);
                videoFx.recoverFromTimelineData(nvsVideoFx);
                videoFx.setInPoint(getInPoint());
                videoFx.setOutPoint(getOutPoint());
            }
        }

        int audioFxCount = videoClip.getAudioFxCount();
        if (audioFxCount > 0) {
            for (int index = 0; index < audioFxCount; index++) {
                NvsAudioFx nvsAudioFx = videoClip.getAudioFxByIndex(index);
                audioFxList.add(createAudioFx(nvsAudioFx.getBuiltinAudioFxName(), nvsAudioFx));
            }
        }
    }

    private String getSubType(String fxId) {
        if (NvsConstants.VIDEO_FX_AR_SCENE.equals(fxId)) {
            return SUB_TYPE_AR_SCENE;
        } else if (NvsConstants.SET_ALPHA.equals(fxId)) {
            return SUB_TYPE_ALPHA;
        } else if (NvsConstants.ADJUST_TYPE_BASIC_IMAGE_ADJUST.equals(fxId)
                || NvsConstants.FX_SHARPEN.equals(fxId)
                || NvsConstants.FX_VIGNETTE.equals(fxId)
                || NvsConstants.ADJUST_TINT.equals(fxId)) {
            return SUB_TYPE_ADJUST;
        } else if (NvsConstants.KEY_MASK_GENERATOR.equals(fxId)) {
            return SUB_TYPE_MASK;
        }
        return "";
    }

    /**
     * 移除旧版本背景
     * Remove old version background
     */
    public void removeOldBackground() {
        if (mStoryboardMap.isEmpty()) {
            return;
        }
        MeicamStoryboardInfo backgroundInfo = mStoryboardMap.get(MeicamStoryboardInfo.SUB_TYPE_BACKGROUND);
        if (backgroundInfo == null) {
            return;
        }
        NvsVideoFx nvsVideoFx = backgroundInfo.getObject();
        if (nvsVideoFx == null) {
            return;
        }
        NvsVideoClip object = getObject();
        if (object != null) {
            object.removeFx(nvsVideoFx.getIndex());
            mStoryboardMap.remove(MeicamStoryboardInfo.SUB_TYPE_BACKGROUND);
        }
    }

    /**
     * 获取旧版本的storyboard 特效
     * Get old version storyboard fx
     *
     * @param fxKey the fxKey 特效键值
     * @return MeicamVideoFx 特效
     */
    public MeicamVideoFx getOldStoryboardFx(String fxKey) {
        if (mStoryboardMap.size() > 0) {
            return mStoryboardMap.get(fxKey);
        }
        return null;
    }

    private MeicamStoryboardInfo getBackgroundInfo() {
        return mStoryboardMap.get(MeicamStoryboardInfo.SUB_TYPE_BACKGROUND);
    }

    @Deprecated
    public Map<String, MeicamStoryboardInfo> getStoryboardInfos() {
        return mStoryboardMap;
    }


    public int getRoleInTheme() {
        return mRoleInTheme;
    }

    public void setRoleInTheme(int roleInTheme) {
        mRoleInTheme = roleInTheme;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Override
    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public boolean changeFilePath(String filePath) {
        NvsVideoClip videoClip = getObject();
        if (videoClip == null) {
            return false;
        }
        this.filePath = filePath;
        return videoClip.changeFilePath(filePath);
    }

    public String getReverseFilePath() {
        return reverseFilePath;
    }

    public void setReverseFilePath(String reverseFilePath) {
        this.reverseFilePath = reverseFilePath;
    }

    public String getVideoType() {
        return videoType;
    }

    public void setVideoType(String videoType) {
        this.videoType = videoType;
    }

    @Override
    public long getInPoint() {
        return super.getInPoint();
    }

    @Override
    public void setInPoint(long inPoint) {
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            super.setInPoint(videoClip.getInPoint());
        }
    }

    @Override
    public long getOutPoint() {
        return super.getOutPoint();
    }

    @Override
    public void setOutPoint(long outPoint) {
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            super.setOutPoint(videoClip.getOutPoint());
        }
    }

    public void updateInAndOutPoint() {
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            super.setInPoint(videoClip.getInPoint());
            super.setOutPoint(videoClip.getOutPoint());
        }
    }

    public int getBlendingMode() {
        return blendingMode;
    }

    /**
     * 设置合成模式
     * set mixed mode
     *
     * @param blendingMode the blending mode
     */
    public void setBlendingMode(int blendingMode) {
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            videoClip.setBlendingMode(blendingMode);
            this.blendingMode = blendingMode;
        }

    }

    @Override
    public long getTrimIn() {
        return trimIn;
    }

    /**
     * 设置裁入点
     * Set trim in point
     *
     * @param trimIn        the trim in point 裁入点
     * @param affectSibling true affects other clip in the same track,false not 是否影响同轨道上其他片段(true/false)
     */
    public void setTrimIn(long trimIn, boolean affectSibling) {
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            this.trimIn = videoClip.changeTrimInPoint(trimIn, affectSibling);
        } else {
            this.trimIn = trimIn;
        }
    }

    public long getTrimOut() {
        return trimOut;
    }

    /**
     * 设置裁出点
     * Set trim out point
     *
     * @param trimOut       the trim out point 裁出点
     * @param affectSibling true affects other clip in the same track,false not 是否影响同轨道上其他片段(true/false)
     */
    public void setTrimOut(long trimOut, boolean affectSibling) {
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            this.trimOut = videoClip.changeTrimOutPoint(trimOut, affectSibling);
        } else {
            this.trimOut = trimOut;
        }
    }

    public long getOrgDuration() {
        return orgDuration;
    }

    public void setOrgDuration(long orgDuration) {
        this.orgDuration = orgDuration;
    }


    public float getVolume() {
        return volume;
    }

    public void setVolume(float volume) {
        if (invalidFloat(volume)) {
            return;
        }
        if (getObject() != null) {
            getObject().setVolumeGain(volume, volume);
            this.volume = volume;
        }
    }

    public String getCurveSpeedName() {
        return curveSpeedName;
    }

    public void setCurveSpeedName(String curveSpeedName) {
        this.curveSpeedName = curveSpeedName;
    }

    @Override
    public double getSpeed() {
        return speed;
    }

    public void setSpeed(double speed) {
        if (invalidDouble(speed)) {
            return;
        }
        this.speed = speed;
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            videoClip.changeSpeed(speed, isKeepAudioPitch());
        }
    }

    public void setSpeed(double speed, boolean isKeepAudioPitch) {
        if (invalidDouble(speed)) {
            return;
        }
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            videoClip.changeSpeed(speed, isKeepAudioPitch);
            this.speed = speed;
            this.keepAudioPitch = isKeepAudioPitch;
        }
    }

    public boolean isKeepAudioPitch() {
        return keepAudioPitch;
    }

    public void setKeepAudioPitch(boolean keepAudioPitch) {
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            videoClip.changeSpeed(speed, keepAudioPitch);
            this.keepAudioPitch = keepAudioPitch;
        }
    }

    public int getExtraRotation() {
        return extraRotation;
    }

    public void setExtraRotation(int extraRotation) {
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            videoClip.setExtraVideoRotation(extraRotation);
        }
        this.extraRotation = extraRotation;
    }

    public boolean isReverse() {
        return reverse;
    }

    public void setReverse(boolean reverse) {
        this.reverse = reverse;
    }


    public int getOriginalWidth() {
        return originalWidth;
    }

    public void setOriginalWidth(int originalWidth) {
        this.originalWidth = originalWidth;
    }

    public int getOriginalHeight() {
        return originalHeight;
    }

    public void setOriginalHeight(int originalHeight) {
        this.originalHeight = originalHeight;
    }

    public List<CurveSpeed> getCurveSpeedList() {
        return curveSpeedList;
    }

    public void setCurveSpeedList(List<CurveSpeed> curveSpeedList) {
        this.curveSpeedList = curveSpeedList;
    }

    public float getRectWidth() {
        return rectWidth == 0 ? originalWidth : rectWidth;
    }

    public void setRectWidth(float rectWidth) {
        this.rectWidth = rectWidth;
    }

    public float getRectHeight() {
        return rectHeight == 0 ? originalHeight : rectHeight;
    }

    public void setRectHeight(float rectHeight) {
        this.rectHeight = rectHeight;
    }

    /**
     * 视频特效的数量
     * Get video fx count
     */
    public int getVideoFxCount() {
        return videoFxs.size();
    }

    public float getOpacity() {
        return opacity;
    }

    public void setOpacity(float opacity) {
        if (invalidFloat(opacity)) {
            return;
        }
        MeicamVideoFx propertyVideoFx = findPropertyVideoFx();
        if (propertyVideoFx != null) {
            propertyVideoFx.setFloatVal(NvsConstants.PROPERTY_OPACITY, opacity);
            this.opacity = opacity;
        }
    }

    public long getFadeInDuration() {
        return fadeInDuration;
    }

    public void setFadeInDuration(long fadeInDuration) {
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            videoClip.setAudioFadeInDuration(fadeInDuration);
            this.fadeInDuration = fadeInDuration;
        }
    }

    public long getFadeOutDuration() {
        return fadeOutDuration;
    }

    public void setFadeOutDuration(long fadeOutDuration) {
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            videoClip.setAudioFadeOutDuration(fadeInDuration);
            this.fadeOutDuration = fadeOutDuration;
        }
    }

    /**
     * Move trim point.
     * 移动片段的裁剪入出点
     *
     * @param offset the offset 偏移值
     */
    public boolean moveTrimPoint(long offset) {
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            boolean success = videoClip.moveTrimPoint(offset);
            trimOffSet = offset;
            if (success) {
                this.trimIn = videoClip.getTrimIn();
                this.trimOut = videoClip.getTrimOut();
            }
            return success;
        }
        return false;
    }

    public boolean getVideoReverse() {
        return isVideoReverse;
    }

    public void setVideoReverse(boolean videoReverse) {
        isVideoReverse = videoReverse;
//        if (getObject() != null) {
//            getObject().setPlayInReverse(isVideoReverse);
//        }
    }

    public boolean isConvertSuccess() {
        return isConvertSuccess;
    }

    public void setConvertSuccess(boolean convertSuccess) {
        isConvertSuccess = convertSuccess;
    }


    public String getCurveSpeed() {
        return curveSpeed;
    }

    public boolean setCurveSpeed(String curveSpeed) {
        NvsVideoClip videoClip = getObject();
        this.curveSpeed = curveSpeed;
        if (videoClip != null) {
            //double tempDuration = (getOutPoint() - getInPoint()) / getSpeed();
            boolean success = videoClip.changeCurvesVariableSpeed(curveSpeed, true);
            if (success) {
                if (TextUtils.isEmpty(curveSpeed)) {
                    this.speed = 1.0f;
                } else {
                    //this.speed = (videoClip.getOutPoint() - videoClip.getInPoint()) / tempDuration;
                    this.speed = videoClip.getSpeed();
                }
                return true;
            }
        }
        return false;
    }

    public int getImageMotionMode() {
        return mImageMotionMode;
    }

    public void setImageMotionMode(int imageMotionMode) {
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            videoClip.setImageMotionMode(imageMotionMode);
            this.mImageMotionMode = imageMotionMode;
        }
    }

    public void setImageMotionAnimationEnabled(boolean animationEnabled) {
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            videoClip.setImageMotionAnimationEnabled(animationEnabled);
            this.imageMotionAnimationEnabled = animationEnabled;
        }
    }

    public boolean isImageMotionAnimationEnabled() {
        return imageMotionAnimationEnabled;
    }

    public void setScan(float scan) {
        if (invalidFloat(scan)) {
            return;
        }
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            this.mScan = scan;
            videoClip.setPanAndScan(mSpan, scan);
        }
    }

    public void setSpan(float span) {
        if (invalidFloat(span)) {
            return;
        }
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            this.mSpan = span;
            videoClip.setPanAndScan(span, mScan);
        }
    }

    /**
     * Gets nvs video type.
     * 获取片段类型
     *
     * @return the nvs video type
     * static final int 	VIDEO_CLIP_TYPE_AV  = 0
     * 视频
     * <p>
     * static final int 	VIDEO_CLIP_TYPE_IMAGE  = 1
     * 图片
     */
    public int getNvsVideoType() {
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            return videoClip.getVideoType();
        }
        return 0;
    }

    /**
     * 获取字幕列表
     *
     * @return The list of caption clip 字幕列表
     */
    public List<MeicamCaptionClip> getCaptionList() {
        return captionList;
    }


    /**
     * 获取组合字幕列表
     *
     * @return The list of compound caption 组合字幕列表
     */
    public List<MeicamCompoundCaptionClip> getCompoundCaptionList() {
        return compoundCaptionList;
    }

    public float getScan() {
        return mScan;
    }

    public float getSpan() {
        return mSpan;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public String getResourceId() {
        return resourceId;
    }

    private void setAdjustEffects() {
        if (mAdjustData == null || mAdjustData.isEmpty()) {
            return;
        }
        Set<Map.Entry<String, MeicamVideoFx>> entries = mAdjustData.entrySet();
        for (Map.Entry<String, MeicamVideoFx> entry : entries) {
            MeicamVideoFx videoFx = entry.getValue();
            NvsVideoFx nvsVideoFx = appendNvsVideoFx(TYPE_RAW_BUILTIN, videoFx.getDesc());
            if (nvsVideoFx != null) {
                videoFx.setObject(nvsVideoFx);
                videoFx.setBooleanVal(NvsConstants.KEY_VIDEO_MODE, true);
                videoFx.bindToTimeline();
            }
        }
    }

    public String getLeftChannelUrl() {
        return leftChannelUrl;
    }

    public void setLeftChannelUrl(String leftChannelUrl) {
        this.leftChannelUrl = leftChannelUrl;
    }

    public String getRightChannelUrl() {
        return rightChannelUrl;
    }

    public void setRightChannelUrl(String rightChannelUrl) {
        this.rightChannelUrl = rightChannelUrl;
    }

    public String getRemotePath() {
        return remotePath;
    }

    public void setRemotePath(String remotePath) {
        this.remotePath = remotePath;
    }

    @NonNull
    @Override
    public Object clone() {
        MeicamVideoClip clone = (MeicamVideoClip) DeepCopyUtil.deepClone(this);
        if (clone == null) {
            String json = GsonContext.getInstance().toJson(this);
            if (!TextUtils.isEmpty(json)) {
                clone = GsonContext.getInstance().fromJson(json, MeicamVideoClip.class);
            }
        }
        if (clone == null) {
            clone = new MeicamVideoClip(null, getVideoType());
        }
        return clone;
    }


    private void setAdjustEffect(NvsVideoClip videoClip, float adjustData, String adjustKey, String attachmentKey) {
        NvsVideoFx nvsVideoFx = null;
        int fxCount = videoClip.getFxCount();
        for (int i = 0; i < fxCount; i++) {
            NvsVideoFx videoFx = videoClip.getFxByIndex(i);
            if (videoFx != null) {
                Object attachment = videoFx.getAttachment(attachmentKey);
                if (attachmentKey.equals(attachment)) {
                    nvsVideoFx = videoFx;
                    break;
                }
            }
        }
        if (nvsVideoFx == null) {
            nvsVideoFx = videoClip.appendBuiltinFx(attachmentKey);
            nvsVideoFx.setAttachment(attachmentKey, attachmentKey);
        }
        nvsVideoFx.setFloatVal(adjustKey, adjustData);
        nvsVideoFx.setBooleanVal(NvsConstants.KEY_VIDEO_MODE, true);
        setRegionData(nvsVideoFx, 0, 0);
    }

    private void setRegionData(NvsVideoFx arSceneFx, int timelineWidth, int timelineHeight) {
        if (arSceneFx == null) {
            return;
        }

        float[] region = getRegionData(originalWidth, originalHeight, timelineWidth, timelineHeight);
        if (region == null || region.length % 2 != 0) {
            return;
        }
        arSceneFx.setRegion(region);
        arSceneFx.setRegional(true);
    }

    private float[] getRegionData(int originalWidth, int originalHeight, int timelineWidth, int timelineHeight) {
        if (originalWidth == 0 || originalHeight == 0 || timelineWidth == 0 || timelineHeight == 0) {
            LogUtils.e("setRegionData originalWidth =" + originalWidth + ", originalHeight ="
                    + originalHeight + ", timelineWidth = " + timelineWidth + ", timelineHeight = " + timelineHeight);
            return null;
        }

        float timelineRatio = timelineWidth * 1.0F / timelineHeight;
        float fileRatio = originalWidth * 1.0F / originalHeight;
        float x;
        float y;
        if (fileRatio > timelineRatio) {
            //宽对齐 Wide alignment
            x = 1.0F;
            float ratio = timelineWidth * 1.0F / originalWidth;
            float imageHeightInTimeline = originalHeight * ratio;
            y = imageHeightInTimeline / timelineHeight;
        } else {
            //高对齐 High alignment
            y = 1.0F;
            float ratio = timelineHeight * 1.0F / originalHeight;
            float imageWidthInTimeline = originalWidth * ratio;
            x = imageWidthInTimeline / timelineWidth;
        }
        float[] region = new float[8];
        region[0] = -x;
        region[1] = y;
        region[2] = x;
        region[3] = y;

        region[4] = x;
        region[5] = -y;
        region[6] = -x;
        region[7] = -y;
        return region;
    }

    /**
     * 获取片段的曲线变速的字符串
     * Get clip variable speed curves string
     */
    public String getClipVariableSpeedCurvesString() {
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            return videoClip.getClipVariableSpeedCurvesString();
        }
        return "";
    }

    /**
     * 通过曲线变速片段的时间点获得时间轴位置的时间点
     * Get timeline position by clip position curves variable speed
     *
     * @param position the time point时间点
     */
    public long getTimelinePosByClipPosCurvesVariableSpeed(long position) {
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            return videoClip.GetTimelinePosByClipPosCurvesVariableSpeed(position);
        }
        return position;
    }

    /**
     * 通过时间轴位置的时间点获得曲线变速片段的时间点
     * Get the curves variable speed clip position by timeline position
     *
     * @param position the time position
     */
    public long getClipPosByTimelinePosCurvesVariableSpeed(long position) {
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            return videoClip.GetClipPosByTimelinePosCurvesVariableSpeed(position);
        }
        return position;
    }

    /**
     * 添加滤镜特效
     * Add filter fx
     *
     * @param buildType  the build type 构建类型
     * @param fxId       the fx id 特效标识
     * @param isTimeline is timeline or not 是否是timeline特效
     * @return MeicamVideoFx 特效
     */
    public MeicamVideoFx appendFilter(String buildType, String fxId, boolean isTimeline) {
        String subType = isTimeline ? MeicamVideoFx.SubType.SUB_TYPE_TIMELINE_FILTER
                : SUB_TYPE_CLIP_FILTER;
        removeVideoFx(subType);
        return appendVideoFx(buildType, subType, fxId);
    }


    /**
     * 添加插件特效
     * Add filter fx
     *
     * @param buildType the build type 构建类型
     * @param subType   the fx type 特效类型
     * @param fxId      the fx id 特效标识
     * @return MeicamVideoFx 特效
     */
    public MeicamVideoFx appendFx(String buildType, String subType, String fxId) {
        return appendVideoFx(buildType, subType, fxId);
    }

    /**
     * 添加特效
     * Add fx
     *
     * @param buildType the build type 构建类型
     * @param fxType    the fx type 特效类型
     * @param fxId      the fx id 特效标识
     * @return MeicamVideoFx 特效
     */
    public MeicamVideoFx appendVideoFx(String buildType, String fxType, String fxId) {
        return appendVideoFx(buildType, fxType, fxId, true);
    }

    /**
     * 添加特效
     * Add fx
     *
     * @param buildType   the build type 构建类型
     * @param fxType      the fx type 特效类型
     * @param fxId        the fx id 特效标识
     * @param saveToLocal true  save to local 保存到本地,false not不保存
     * @return MeicamVideoFx 特效
     */
    public MeicamVideoFx appendVideoFx(String buildType, String fxType, String fxId, boolean saveToLocal) {
        NvsVideoFx nvsVideoFx = appendNvsVideoFx(buildType, fxId);
        if (nvsVideoFx != null) {
            MeicamVideoFx videoFx = createVideoFx(buildType, fxType, fxId, nvsVideoFx);
            if (saveToLocal) {
                for (MeicamVideoFx itemFx : videoFxs) {
                    itemFx.setIndex(itemFx.getObject().getIndex());
                }
                videoFxs.add(videoFx);
                if (SUB_TYPE_ADJUST.equals(fxType)) {
                    mAdjustData.put(fxId, videoFx);
                }
            }
            return videoFx;
        }
        return null;
    }

    /**
     * 添加特效
     * Add fx
     *
     * @param buildType   the build type 构建类型
     * @param fxType      the fx type 特效类型
     * @param fxId        the fx id 特效标识
     * @param index       the index 插入的index
     * @param saveToLocal true  save to local 保存到本地,false not不保存
     * @return MeicamVideoFx 特效
     */
    public MeicamVideoFx insertVideoFx(String buildType, String fxType, String fxId, int index, boolean saveToLocal) {
        NvsVideoFx nvsVideoFx = insertNvsVideoFx(buildType, fxId, index);
        if (nvsVideoFx != null) {
            MeicamVideoFx videoFx = createVideoFx(buildType, fxType, fxId, nvsVideoFx);
            if (saveToLocal) {
                for (MeicamVideoFx itemFx : videoFxs) {
                    itemFx.setIndex(itemFx.getObject().getIndex());
                }
                videoFxs.add(videoFx);
               /* if (videoFxs.size() <= realIndex) {
                    videoFxs.add(videoFx);
                } else {
                    videoFxs.add(realIndex, videoFx);
                }*/
            }
            return videoFx;
        }
        return null;
    }

    /**
     * 添加特效
     * Add fx
     *
     * @param nvsVideoFx  the nvsVideoFx NvsObject
     * @param buildType   the build type 构建类型
     * @param fxType      the fx type 特效类型
     * @param fxId        the fx id 特效标识
     * @param saveToLocal true  save to local 保存到本地,false not不保存
     * @return MeicamVideoFx 特效
     */
    public MeicamVideoFx appendVideoFx(NvsVideoFx nvsVideoFx, String buildType, String fxType, String fxId, boolean saveToLocal) {
        if (nvsVideoFx != null) {
            MeicamVideoFx videoFx = createVideoFx(buildType, fxType, fxId, nvsVideoFx);
            if (saveToLocal) {
                for (MeicamVideoFx itemFx : videoFxs) {
                    itemFx.setIndex(itemFx.getObject().getIndex());
                }
                videoFxs.add(videoFx);
            }
            return videoFx;
        }
        return null;
    }

    /**
     * 添加特效
     * Add story board fx
     *
     * @param buildType the build type 构建类型
     * @param fxType    the fx type 特效类型
     * @param fxId      the fx id 特效标识
     * @return MeicamVideoFx 特效
     */
    public MeicamStoryboardInfo appendStoryVideoFx(String buildType, String fxType, String fxId) {
        NvsVideoFx nvsVideoFx = appendNvsVideoFx(buildType, fxId);
        if (nvsVideoFx != null) {
            MeicamStoryboardInfo videoFx = new MeicamStoryboardInfo(nvsVideoFx, buildType, fxType, fxId);
            videoFx.setIndex(nvsVideoFx.getIndex());
            videoFx.setSubType(fxType);
            videoFx.setAttachment();
            mStoryboardMap.put(fxType, videoFx);
            return videoFx;
        }
        return null;
    }

    /**
     * 添加滤镜特效
     * Add  filter fx
     *
     * @param videoFx    the old or clone meicam video fx 旧的或者复制数据
     * @param isTimeline is timeline or not 是否是timeline特效
     * @return MeicamVideoFx 特效
     */
    public MeicamVideoFx appendFilterFromFx(MeicamVideoFx videoFx, boolean isTimeline) {
        String subType = isTimeline ? MeicamVideoFx.SubType.SUB_TYPE_TIMELINE_FILTER
                : SUB_TYPE_CLIP_FILTER;
        removeVideoFx(subType);
        videoFx.setSubType(subType);
        return appendVideoFxFromFx(videoFx, false);
    }


    /**
     * 添加特效
     * Add  fx
     *
     * @param videoFx        the old or clone meicam video fx 旧的或者复制数据
     * @param deleteSameType true delete the same type fx删除同类型的特效，false 不删除
     * @return MeicamVideoFx 特效
     */
    public MeicamVideoFx appendVideoFxFromFx(MeicamVideoFx videoFx, boolean deleteSameType) {
        if (videoFx == null || TextUtils.isEmpty(videoFx.getDesc())) {
            return null;
        }
        if (deleteSameType) {
            removeVideoFx(videoFx.getType(), videoFx.getSubType());
        }
        NvsVideoFx nvsVideoFx = appendNvsVideoFx(videoFx.getType(), videoFx.getDesc());
        if (nvsVideoFx != null) {
            videoFx.setObject(nvsVideoFx);
            videoFx.setIndex(nvsVideoFx.getIndex());
            videoFx.setInPoint(getInPoint());
            videoFx.setOutPoint(getOutPoint());
            videoFx.bindToTimeline();
            videoFxs.add(videoFx);
        }
        return videoFx;
    }

    /**
     * 插入特效
     * insert  fx
     *
     * @param videoFx        the old or clone meicam video fx 旧的或者复制数据
     * @param index          index 要插入的index,注意此处的index属于MeicamVideoFx而非videoFxs
     * @param deleteSameType true delete the same type fx删除同类型的特效，false 不删除
     * @return MeicamVideoFx 特效
     */
    public MeicamVideoFx insertVideoFxFromFx(MeicamVideoFx videoFx, int index, boolean deleteSameType) {
        if (videoFx == null || TextUtils.isEmpty(videoFx.getDesc())) {
            return null;
        }
        if (deleteSameType) {
            removeVideoFx(videoFx.getType(), videoFx.getSubType());
        }
        NvsVideoFx nvsVideoFx = insertNvsVideoFx(videoFx.getType(), videoFx.getDesc(), index);
        if (nvsVideoFx != null) {
            videoFx.setObject(nvsVideoFx);
            videoFx.setIndex(nvsVideoFx.getIndex());
            videoFx.setOutPoint(getOutPoint());
            videoFx.setInPoint(getInPoint());
            videoFx.bindToTimeline();
            videoFxs.add(videoFx);
            for (MeicamVideoFx meicamVideoFx : videoFxs) {
                meicamVideoFx.setIndex(meicamVideoFx.getObject().getIndex());
            }
            return videoFx;
        }
        return null;
    }

    /**
     * 获取调节特效,如果没有，则创建
     * Get adjust adjust videoFx.If there is no one, create.
     *
     * @param fxId the fx id 特效标识
     * @return MeicamVideoFx 特效
     */
    public MeicamVideoFx getAdjustVideoFx(String fxId) {
        if (TextUtils.isEmpty(fxId)) {
            LogUtils.e("param error: fxId or resolution is null");
            return null;
        }
        MeicamVideoFx meicamVideoFx = mAdjustData.get(fxId);
        if (meicamVideoFx != null) {
            return meicamVideoFx;
        }
        NvsVideoFx nvsVideoFx = appendNvsVideoFx(TYPE_RAW_BUILTIN, fxId);
        if (nvsVideoFx != null) {
            MeicamVideoFx videoFx = createVideoFx(TYPE_RAW_BUILTIN, SUB_TYPE_ADJUST, fxId, nvsVideoFx);
            nvsVideoFx.setBooleanVal(NvsConstants.KEY_VIDEO_MODE, true);
            mAdjustData.put(fxId, videoFx);
            return videoFx;
        }
        return null;
    }

    /**
     * 获取调节特效
     * find adjust adjust videoFx
     *
     * @param fxId the fx id 特效标识
     * @return MeicamVideoFx 特效
     */
    public MeicamVideoFx findAdjustVideoFx(String fxId) {
        if (TextUtils.isEmpty(fxId)) {
            LogUtils.e("param error: fxId or resolution is null");
            return null;
        }
        return mAdjustData.get(fxId);
    }

    /**
     * 设置调节特效
     * Get adjust adjust videoFx
     *
     * @param fxName the fx id 特效标识
     * @param key    key
     * @param value  value
     */
    public void setAdjustItemValue(@NonNull String fxName, @NonNull String key, float value) {
        MeicamVideoFx adjustVideoFx = findAdjustVideoFx(fxName);
        if (adjustVideoFx == null) {
            return;
        }
        adjustVideoFx.setFloatVal(key, value);
    }

    /**
     * 设置调节特效
     * Get adjust adjust item value
     *
     * @param fxName the fx id 特效标识
     * @param key    key
     * @return value  value
     */
    public Float getAdjustItemValue(@NonNull String fxName, @NonNull String key) {
        MeicamVideoFx adjustVideoFx = findAdjustVideoFx(fxName);
        if (adjustVideoFx == null) {
            return 0F;
        }
        return adjustVideoFx.getFloatVal(key);
    }

    private MeicamVideoFx createVideoFx(String buildType, String fxType, String fxId, NvsVideoFx nvsVideoFx) {
        MeicamVideoFx videoFx = new MeicamVideoFx(nvsVideoFx, buildType, fxType, fxId);
        videoFx.setIndex(nvsVideoFx.getIndex());
        videoFx.setSubType(fxType);
        videoFx.setInPoint(getInPoint());
        videoFx.setOutPoint(getOutPoint());
        videoFx.setAttachment();
        return videoFx;
    }

    /**
     * 添加底层特效
     * Append video fx
     *
     * @param buildType the build type 构建类型
     * @param fxId      the fx id 特效标识
     * @return NvsVideoFx the fx 底层特效
     */
    private NvsVideoFx appendNvsVideoFx(String buildType, String fxId) {
        NvsVideoClip videoClip = getObject();
        if (videoClip == null) {
            return null;
        }
        NvsVideoFx nvsVideoFx = null;
        if (TYPE_BUILD_IN.equals(buildType)) {
            if (NvsConstants.KEY_MASK_GENERATOR.equals(fxId) && Constants.EnableRawFilterMaskRender) {
                nvsVideoFx = videoClip.appendRawBuiltinFx(fxId);
            } else {
                nvsVideoFx = videoClip.appendBuiltinFx(fxId);
                if (nvsVideoFx != null && NvsConstants.MasterKeyer.NAME.equals(fxId)) {
                    nvsVideoFx.setIntVal(NvsConstants.MasterKeyer.REGION_COORDINATE_SYSTEM_TYPE, 0);
                }
            }
        } else if (TYPE_PACKAGE.equals(buildType)) {
            nvsVideoFx = videoClip.appendPackagedFx(fxId);
        } else if (TYPE_RAW_PACKAGE.equals(buildType)) {
            nvsVideoFx = videoClip.appendRawPackagedFx(fxId);
        } else if (TYPE_RAW_BUILTIN.equals(buildType)) {
            if (NvsConstants.SET_ALPHA.equals(fxId)) {
                /*
                alpha特效要在第1层，美颜和美型放在第0位置
                Alpha effects should be on the first floor, and beauty and beauty should be on the 0 position.
                * */
                nvsVideoFx = videoClip.insertRawBuiltinFx(fxId, 1);
            } else if (NvsConstants.VIDEO_FX_AR_SCENE.equals(fxId)) {
                /*
                 * 必须放在第0层，否则会检测不到人脸
                 * It must be placed on layer 0, otherwise the face will not be detected
                 */
                nvsVideoFx = videoClip.insertRawBuiltinFx(fxId, 0);
                if (nvsVideoFx != null) {
                    /*编辑的时候禁用Single buffer mode，原因1：效率低。2、可能会存在缩放等问题*/
                    nvsVideoFx.setBooleanVal(NvsConstants.VIDEO_FX_SINGLE_BUFFER_MODE, false);
                    NvsARSceneManipulate arSceneManipulate = nvsVideoFx.getARSceneManipulate();
                    if (arSceneManipulate != null) {
                        arSceneManipulate.setDetectionMode(NvsStreamingContext.HUMAN_DETECTION_FEATURE_SEMI_IMAGE_MODE);
                    }
                    nvsVideoFx.setAttachment(ATTACHMENT_KEY_VIDEO_CLIP_AR_SCENE, VIDEO_FX_AR_SCENE);
                }
            } else {
                nvsVideoFx = videoClip.appendRawBuiltinFx(fxId);
            }

        } else if (TYPE_PROPERTY.equals(buildType) || PROPERTY_FX.equals(fxId)) {
            nvsVideoFx = videoClip.getPropertyVideoFx();
            if (nvsVideoFx != null) {
                videoClip.enablePropertyVideoFx(true);
            }
        }
        return nvsVideoFx;
    }

    /**
     * 添加底层特效
     * Append video fx
     *
     * @param buildType the build type 构建类型
     * @param fxId      the fx id 特效标识
     * @return NvsVideoFx the fx 底层特效
     */
    private NvsVideoFx insertNvsVideoFx(String buildType, String fxId, int index) {
        NvsVideoClip videoClip = getObject();
        if (videoClip == null) {
            return null;
        }
        NvsVideoFx nvsVideoFx = null;
        if (TYPE_BUILD_IN.equals(buildType)) {
            nvsVideoFx = videoClip.insertBuiltinFx(fxId, index);
        } else if (TYPE_PACKAGE.equals(buildType)) {
            nvsVideoFx = videoClip.insertPackagedFx(fxId, index);
        } else if (TYPE_RAW_BUILTIN.equals(buildType)) {
            nvsVideoFx = videoClip.insertRawBuiltinFx(fxId, index);
        }
        return nvsVideoFx;
    }

    /**
     * 获取属性特效
     * Get property fx
     *
     * @return MeicamVideoFx the fx 特效
     */
    public MeicamVideoFx findPropertyVideoFx() {
        MeicamVideoFx videoFx = getVideoFxById(PROPERTY_FX);
        if (videoFx == null) {
            videoFx = appendVideoFx(TYPE_PROPERTY, TYPE_PROPERTY, PROPERTY_FX);
        }
        if (videoFx != null) {
            videoFx.setExtraTag(getTrackIndex() + "|" + getIndex());
        }
        return videoFx;
    }

    /**
     * 根据类型获取特效
     * Get video fx by type
     *
     * @param buildType the build type 构建类型
     * @param subType   the fx type 特效类型
     * @return MeicamVideoFx the fx 特效
     */
    public MeicamVideoFx getVideoFxByType(String buildType, String subType) {
        if (TextUtils.isEmpty(buildType) || TextUtils.isEmpty(subType)) {
            return null;
        }
        for (MeicamVideoFx videoFx : videoFxs) {
            if (buildType.equals(videoFx.getType()) && subType.equals(videoFx.getSubType())) {
                videoFx.setOutPoint(getOutPoint());
                videoFx.setInPoint(getInPoint());
                videoFx.setExtraTag(getTrackIndex() + "|" + getIndex());
                return videoFx;
            }
        }
        return null;
    }

    /**
     * 根据类型获取特效
     * Get video fx by type
     *
     * @param subType the fx type 特效类型
     * @return MeicamVideoFx the fx 特效
     */
    public MeicamVideoFx getVideoFxByType(String subType) {
        if (TextUtils.isEmpty(subType)) {
            return null;
        }
        for (MeicamVideoFx videoFx : videoFxs) {
            if (subType.equals(videoFx.getSubType())) {
                videoFx.setOutPoint(getOutPoint());
                videoFx.setInPoint(getInPoint());
                videoFx.setExtraTag(getTrackIndex() + "|" + getIndex());
                return videoFx;
            }
        }
        return null;
    }

    /**
     * 根据特效id获取特效
     * Get video fx by type
     *
     * @param fxId the fx id 特效id
     * @return MeicamVideoFx the fx 特效
     */
    public MeicamVideoFx getVideoFxById(String fxId) {
        if (TextUtils.isEmpty(fxId)) {
            return null;
        }
        for (MeicamVideoFx videoFx : videoFxs) {
            if (fxId.equals(videoFx.getDesc())) {
                videoFx.setInPoint(getInPoint());
                videoFx.setOutPoint(getOutPoint());
                videoFx.setExtraTag(getTrackIndex() + "|" + getIndex());
                return videoFx;
            }
        }
        return null;
    }

    /**
     * 根据类型获取特效
     * Get video fx by type
     *
     * @param subType the fx type 特效类型
     * @param fxId    the fx id 特效id
     * @return MeicamVideoFx the fx 特效
     */
    public MeicamVideoFx getVideoFx(String subType, String fxId) {
        if (TextUtils.isEmpty(subType) || TextUtils.isEmpty(fxId)) {
            return null;
        }
        for (MeicamVideoFx videoFx : videoFxs) {
            if (fxId.equals(videoFx.getDesc()) && subType.equals(videoFx.getSubType())) {
                videoFx.setInPoint(getInPoint());
                videoFx.setOutPoint(getOutPoint());
                videoFx.setExtraTag(getTrackIndex() + "|" + getIndex());
                return videoFx;
            }
        }
        return null;
    }

    /**
     * 根据类型获取特效
     * Get video fx by type
     *
     * @param buildType the build type 构建类型
     * @param subType   the fx type 特效类型
     * @param fxId      the fx id 特效id
     * @return MeicamVideoFx the fx 特效
     */
    public MeicamVideoFx getVideoFx(String buildType, String subType, String fxId, int index) {
        if (TextUtils.isEmpty(buildType) || TextUtils.isEmpty(subType) || TextUtils.isEmpty(fxId)) {
            return null;
        }
        for (MeicamVideoFx videoFx : videoFxs) {
            if (buildType.equals(videoFx.getType()) && fxId.equals(videoFx.getDesc())
                    && subType.equals(videoFx.getSubType()) && videoFx.getIndex() == index) {
                videoFx.setInPoint(getInPoint());
                videoFx.setOutPoint(getOutPoint());
                videoFx.setExtraTag(getTrackIndex() + "|" + getIndex());
                return videoFx;
            }
        }
        return null;
    }

    /**
     * 根据类型获取特效
     * Get video fx by type
     *
     * @param subType the fx type 特效类型
     * @param fxId    the fx id 特效id
     * @return MeicamVideoFx the fx 特效
     */
    public MeicamVideoFx getVideoFx(String subType, String fxId, int index) {
        if (TextUtils.isEmpty(subType) || TextUtils.isEmpty(fxId)) {
            return null;
        }
        for (MeicamVideoFx videoFx : videoFxs) {
            if (fxId.equals(videoFx.getDesc()) && subType.equals(videoFx.getSubType())
                    && videoFx.getIndex() == index) {
                videoFx.setInPoint(getInPoint());
                videoFx.setOutPoint(getOutPoint());
                videoFx.setExtraTag(getTrackIndex() + "|" + getIndex());
                return videoFx;
            }
        }
        return null;
    }

    /**
     * 获取视频特效，注意此处的index是相对于videoFxs集合来说的，和MeicamVideoFx.getIndex()不是同一个概念。
     * Get video fx
     *
     * @param index the index 索引
     */
    public MeicamVideoFx getVideoFx(int index) {
        if (index >= 0 && index < videoFxs.size()) {
            MeicamVideoFx videoFx = videoFxs.get(index);
            videoFx.setInPoint(getInPoint());
            videoFx.setOutPoint(getOutPoint());
            videoFx.setExtraTag(getTrackIndex() + "|" + getIndex());
            return videoFx;
        }
        return null;
    }

    /**
     * 获取Raw特效数量
     * Gets raw fx count.
     *
     * @return the raw fx count
     */
    public int getRawFxCount() {
        rawBuildInFx = null;
        addRawFxInTempList();
        return rawBuildInFx.size();
    }

    /**
     * 根据index获取Raw特效
     * Gets raw fx by index.
     *
     * @param index the index
     * @return the raw fx by index
     */
    public MeicamVideoFx getRawFxByIndex(int index) {
        if (rawBuildInFx == null) {
            addRawFxInTempList();
        }
        return rawBuildInFx.get(index);
    }

    private void addRawFxInTempList() {
        rawBuildInFx = new ArrayList<>();
        for (MeicamVideoFx meicamVideoFx : videoFxs) {
            if (NvsConstants.TYPE_RAW_BUILTIN.equals(meicamVideoFx.getType())
                    || TYPE_RAW_PACKAGE.equals(meicamVideoFx.getType())) {
                rawBuildInFx.add(meicamVideoFx);
            }
        }
    }

    /**
     * 移除视频片段特效
     * Remove video fx
     *
     * @param type the fx type 特效类型
     */
    public boolean removeVideoFx(String type) {
        MeicamVideoFx filterFx = getVideoFxByType(type);
        boolean success = filterFx == null;
        try {
            NvsVideoClip videoClip = getObject();
            if (!success && videoClip != null) {
                if (NvsConstants.KEY_MASK_GENERATOR.equals(type) && Constants.EnableRawFilterMaskRender) {
                    success = videoClip.removeRawFx(filterFx.getObject().getIndex());
                } else {
                    success = videoClip.removeFx(filterFx.getObject().getIndex());
                }
                if (success) {
                    videoFxs.remove(filterFx);
                }
            }
        } catch (Exception e) {
            LogUtils.e("removeVideoFx:error:" + e.fillInStackTrace());
        }
        return success;
    }

    /**
     * 删除特效 remove VideoFx
     *
     * @param videoFx the video will delete要删除的特效
     * @return videoFx removed
     */
    public MeicamVideoFx removeVideoFx(MeicamVideoFx videoFx) {
        try {
            if (videoFx != null) {
                if (videoFx.getObject() == null) {
                    // 此处是为了容错以及通过纯上层数据（即无sdk object）进行的删除，
                    for (MeicamVideoFx meicamVideoFx : videoFxs) {
                        if (meicamVideoFx.getIndex() == videoFx.getIndex() && meicamVideoFx.getType().equals(videoFx.getType())
                                && meicamVideoFx.getDesc().equals(videoFx.getDesc())) {
                            videoFx = meicamVideoFx;
                            break;
                        }
                    }
                }
                boolean isSuccess;
                if (TYPE_RAW_BUILTIN.equals(videoFx.getType())
                        || TYPE_RAW_PACKAGE.equals(videoFx.getType())) {
                    isSuccess = getObject().removeRawFx(videoFx.getObject().getIndex());
                } else {
                    isSuccess = getObject().removeFx(videoFx.getObject().getIndex());
                }
                if (isSuccess) {
                    videoFxs.remove(videoFx);
                    for (MeicamVideoFx meicamVideoFx : videoFxs) {
                        meicamVideoFx.setIndex(meicamVideoFx.getObject().getIndex());
                    }
                }
            }
        } catch (Exception e) {
            LogUtils.e("removeVideoFx:error:" + e.fillInStackTrace());
        }
        return videoFx;
    }

    /**
     * 删除特效 remove VideoFx
     *
     * @param buildType the build type构建类型
     * @param subType   subType of videoFx 特效类型
     * @return videoFx removed
     */
    public MeicamVideoFx removeVideoFx(String buildType, String subType) {
        MeicamVideoFx videoFx = getVideoFxByType(buildType, subType);
        removeVideoFx(videoFx);
        return videoFx;
    }

    /**
     * 应用美颜、美型特效
     * Apply beauty fx
     *
     * @param fxName the fx name 特效名
     * @param value  the fx value 特效效果值
     * @return Is success or not. true: yes; false：no.
     */
    public boolean setBeautyAndShapeFxParam(String fxName, float value) {
        MeicamVideoFx arSceneFx = getArSceneFxEx();
        if (arSceneFx != null) {
            openBeautyFx(arSceneFx);
            arSceneFx.setFloatVal(fxName, value);
            return true;
        }
        return false;
    }

    /**
     * 应用美颜、美型特效
     * Apply beauty fx
     *
     * @param faceID     美型ID
     * @param packageID  包ID
     * @param degreeName 调节名称
     * @return Is success or not. true: yes; false：no.
     */
    public boolean applyShapeFx(String faceID, String packageID, String degreeName, float value) {
        MeicamVideoFx arSceneFx = getArSceneFxEx();
        if (arSceneFx != null) {
            openShapeFx(arSceneFx);
            if (faceID != null && packageID != null) {
                arSceneFx.setStringVal(faceID, packageID);
            } else {
                LogUtils.e("The package id is null");
            }
            arSceneFx.setFloatVal(degreeName, value);
            return true;
        }
        return false;
    }

    /**
     * 关闭美型特效
     * Close beauty fx
     *
     * @return Is success or not.true: yes;false：no.
     */
    public boolean closeShapeFx() {
        MeicamVideoFx arSceneFx = getArSceneFxEx();
        if (arSceneFx != null) {
            closeShapeFx(arSceneFx);
            return true;
        }
        return false;
    }

    /**
     * 打开美型特效
     * Open beauty fx
     *
     * @return Is success or not.true: yes;false：no.
     */
    public boolean openShapeFx() {
        MeicamVideoFx arSceneFx = getArSceneFxEx();
        if (arSceneFx != null) {
            openShapeFx(arSceneFx);
            return true;
        }
        return false;
    }

    /**
     * 获取美颜、美型特效的强度值
     * Gets the strength value of the beauty and beauty effect
     *
     * @param fxName the fx name 特效名
     * @return the fx value 特效效果值
     */
    public Float getBeautyOrShapeFxValue(String fxName) {
        MeicamVideoFx arSceneFxEx = getArSceneFxEx();
        if (arSceneFxEx != null) {
            float floatVal = arSceneFxEx.getFloatVal(fxName);
            return floatVal != INVALID_VALUE ? floatVal : 0;
        }
        return null;
    }

    /**
     * 重置美颜（美肤）特效
     * Reset beauty fx
     */
    public void resetBeautyFx() {
        MeicamVideoFx arSceneFx = getArSceneFxEx();
        if (arSceneFx != null) {
            List<String[]> paramList = NvsConstants.getBeautyParam();
            for (String[] param : paramList) {
                if (TYPE_FLOAT.equals(param[0])) {
                    arSceneFx.setFloatVal(param[1], 0F);
                }
            }
        }
    }

    /**
     * 美颜（美肤)是否是默认值
     * Is beauty (skin) the default value
     *
     * @return true yes 是,false not不是
     */
    public boolean isDefaultBeauty() {
        MeicamVideoFx arSceneFx = getArSceneFxEx();
        if (arSceneFx == null) {
            return true;
        }
        for (String[] param : NvsConstants.getBeautyParam()) {
            if (TYPE_FLOAT.equals(param[0])) {
                float strength = arSceneFx.getFloatVal(param[1]);
                if (strength != INVALID_VALUE && Math.abs(strength) > 0) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 美型是否是默认值
     * Is beauty (shape the default value
     *
     * @return true yes 是,false not不是
     */
    public boolean isDefaultBeautyShape() {
        MeicamVideoFx arSceneFxEx = getArSceneFxEx();
        if (arSceneFxEx == null) {
            return true;
        }
        for (String[] param : NvsConstants.getShapeParam()) {
            if (TYPE_FLOAT.equals(param[0])) {
                float floatVal = arSceneFxEx.getFloatVal(param[1]);
                if (floatVal != INVALID_VALUE && Math.abs(floatVal) > 0) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 是否有美型特效
     * Whether there is a beauty effect
     *
     * @return true had 有，false not
     */
    public boolean hasBeautyShape() {
        NvsVideoFx arSceneFx = findSceneFx();
        return arSceneFx != null
                && (arSceneFx.getBooleanVal(VIDEO_FX_BEAUTY_SHAPE)
                || !TextUtils.isEmpty(arSceneFx.getStringVal(NvsConstants.VIDEO_FX_PRAM_SCENE_ID)));
    }

    /**
     * 是否是美颜
     * Is it beauty
     *
     * @param fxName the fx name 特效名
     * @return true is 是，false not 不是
     */
    private boolean isBeautyParam(String fxName) {
        List<String[]> beautyParam = NvsConstants.getBeautyParam();
        for (String[] strings : beautyParam) {
            if (strings[1].equals(fxName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 重置美型特效
     * Reset beauty shape fx
     */
    public void resetShapeFx() {
        MeicamVideoFx arSceneFx = getArSceneFxEx();
        if (arSceneFx != null) {
            for (String[] param : NvsConstants.getShapeParam()) {
                if (TYPE_FLOAT.equals(param[0])) {
                    arSceneFx.setFloatVal(param[1], 0);
                }
            }
        }
    }

    /**
     * 打开美颜美肤的设置
     * Open beauty fx config
     */
    private void openBeautyFx(MeicamVideoFx videoFx) {
        videoFx.setBooleanVal(VIDEO_FX_BEAUTY_EFFECT, true);
    }

    /**
     * 打开美颜美肤的设置
     * Open shape fx config
     */
    private void openShapeFx(MeicamVideoFx videoFx) {
        videoFx.setBooleanVal(VIDEO_FX_BEAUTY_SHAPE, true);
        videoFx.setBooleanVal(VIDEO_FX_BEAUTY_SHAPE_MESH, true);
        videoFx.setBooleanVal("Use Face Extra Info", true);
    }

    /**
     * 关闭美颜美肤的设置
     * Close shape fx config
     */
    private void closeShapeFx(MeicamVideoFx videoFx) {
        videoFx.setBooleanVal(VIDEO_FX_BEAUTY_SHAPE, false);
        videoFx.setBooleanVal(VIDEO_FX_BEAUTY_SHAPE_MESH, false);
    }

    /**
     * 获取美型美颜特效
     * Get ar scene fx of beauty and beauty shape
     *
     * @return NvsVideoFx ar fx 特效
     */
    private NvsVideoFx getArSceneFx() {
        NvsVideoClip videoClip = getObject();
        if (videoClip == null) {
            return null;
        }
        NvsVideoFx arVideoFx;
        int fxCount = videoClip.getRawFxCount();
        for (int i = 0; i < fxCount; i++) {
            NvsVideoFx videoFx = videoClip.getRawFxByIndex(i);
            Object attachment = videoFx.getAttachment(ATTACHMENT_KEY_VIDEO_CLIP_AR_SCENE);
            if (attachment != null) {
                return videoFx;
            }
        }
        /*
         * 必须放在第0层，否则会检测不到人脸
         * It must be placed on layer 0, otherwise the face will not be detected
         */
        arVideoFx = videoClip.insertRawBuiltinFx(VIDEO_FX_AR_SCENE, 0);
        if (arVideoFx != null) {
            /*编辑的时候禁用Single buffer mode，原因1：效率低。2、可能会存在缩放等问题*/
            arVideoFx.setBooleanVal(NvsConstants.VIDEO_FX_SINGLE_BUFFER_MODE, false);
            NvsARSceneManipulate arSceneManipulate = arVideoFx.getARSceneManipulate();
            if (arSceneManipulate != null) {
                arSceneManipulate.setDetectionMode(NvsStreamingContext.HUMAN_DETECTION_FEATURE_SEMI_IMAGE_MODE);
            }
            arVideoFx.setAttachment(ATTACHMENT_KEY_VIDEO_CLIP_AR_SCENE, VIDEO_FX_AR_SCENE);
        }
        return arVideoFx;
    }

    /**
     * 获取美型美颜特效
     * Get ar scene fx of beauty and beauty shape
     *
     * @return NvsVideoFx ar fx 特效
     */
    public MeicamVideoFx getArSceneFxEx() {
        MeicamVideoFx videoFx = getVideoFxById(VIDEO_FX_AR_SCENE);
        if (videoFx == null) {
            videoFx = appendVideoFx(CommonData.TYPE_RAW_BUILTIN, SUB_TYPE_AR_SCENE, VIDEO_FX_AR_SCENE);
        }
        return videoFx;
    }

    /**
     * 查找美型美颜特效
     * find ar scene fx of beauty and beauty shape
     *
     * @return NvsVideoFx ar fx 特效
     */
    public NvsVideoFx findSceneFx() {
        NvsVideoClip videoClip = getObject();
        if (videoClip == null) {
            return null;
        }
        int fxCount = videoClip.getRawFxCount();
        for (int i = 0; i < fxCount; i++) {
            NvsVideoFx videoFx = videoClip.getRawFxByIndex(i);
            if (NvsConstants.VIDEO_FX_AR_SCENE.equals(videoFx.getBuiltinVideoFxName())) {
                return videoFx;
            }
        }
        return null;
    }

    /**
     * 设置模板附件
     * Set template attachment
     *
     * @param key   the key 键
     * @param value the value 值
     */
    public void setTemplateAttachment(String key, String value) {
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            videoClip.setTemplateAttachment(key, value);
        }
    }

    /**
     * 获取模板附件
     * Get template attachment
     *
     * @param key the key 键
     */
    public String getTemplateAttachment(String key) {
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            return videoClip.getTemplateAttachment(key);
        }
        return "";
    }

    /**
     * 加载数据到底层
     * Load data to sdk
     */
    void bindToTimeline() {
        NvsVideoClip videoClip = getObject();
        if (videoClip != null) {
            videoClip.enableRawSourceMode(enableRawSourceMode);
            videoClip.removeAllFx();
            int rawFxCount = videoClip.getRawFxCount();
            if (rawFxCount > 0) {
                for (int index = rawFxCount - 1; index >= 0; index--) {
                    videoClip.removeRawFx(index);
                }
            }
            applyVideoFx();
            int videoType = videoClip.getVideoType();
            //当前片段是图片
            //The clip is image
            if (videoType == NvsVideoClip.VIDEO_CLIP_TYPE_IMAGE) {
                long trimIn = videoClip.getTrimIn();
                long trimOut = getTrimOut();
                if (trimOut > 0 && trimOut > trimIn) {
                    videoClip.changeTrimOutPoint(trimOut, true);
                }
                videoClip.setImageMotionMode(getImageMotionMode());
                videoClip.setImageMotionAnimationEnabled(false);
            } else {
                //当前是视频
                //设置左右声道值 0-8
                //The clip is video.
                //Set the volume gain as 0 to 8.
                videoClip.setVolumeGain(volume, volume);

                videoClip.setAudioFadeInDuration(fadeInDuration);

                videoClip.setAudioFadeOutDuration(fadeOutDuration);
                //设置变速
                //Set speed
                if (TextUtils.isEmpty(getCurveSpeed())) {
                    if (speed > 0) {
                        //设置两遍的原因是，变速，分割的时候，如果只设置videoClip.changeSpeed(speed, isKeepAudioPitch())，达不到预期效果，会只显示最初的音频声音
                        //The reason for setting twice is that when changing speed and splitting, if only videoClip.changeSpeed (speed, isKeepAudioPitch()) is set, the expected effect will not be achieved, and only the original audio sound will be displayed.
                        videoClip.changeSpeed(speed);
                        videoClip.changeSpeed(speed, isKeepAudioPitch());
                        super.setInPoint(videoClip.getInPoint());
                        super.setOutPoint(videoClip.getOutPoint());
                    }
                } else {
                    setCurveSpeed(getCurveSpeed());
                    super.setInPoint(videoClip.getInPoint());
                    super.setOutPoint(videoClip.getOutPoint());
                }
                videoClip.setPanAndScan(getSpan(), getScan());
            }
            //设置旋转角度
            //Set extra rotation
            videoClip.setExtraVideoRotation(extraRotation);
            /*
             * 如果是补黑素材设置透明度为0，让其全黑
             * If it is a black fill, set the opacity to 0 and make it all black
             */
            if (CommonData.CLIP_HOLDER.equals(getVideoType())) {
                videoClip.getPropertyVideoFx().setFloatVal(NvsConstants.PROPERTY_OPACITY, 0);
            } else {
                videoClip.getPropertyVideoFx().setFloatVal(NvsConstants.PROPERTY_OPACITY, getOpacity());
            }
            if (trimIn > 0) {
                videoClip.changeTrimInPoint(trimIn, true);
            }
            if (trimOut > 0 && trimOut > trimIn) {
                videoClip.changeTrimOutPoint(trimOut, true);
            }
            setDefaultBackground();
            videoClip.setBlendingMode(blendingMode);
        }
        if (!CommonUtils.isEmpty(audioFxList)) {
            for (int index = audioFxList.size() - 1; index >= 0; index--) {
                MeicamAudioFx audioFx = audioFxList.get(index);
                NvsAudioFx nvsAudioFx = appendNvsAudioFx(audioFx.getDesc());
                if (nvsAudioFx != null) {
                    audioFx.setObject(nvsAudioFx);
                } else {
                    audioFxList.remove(index);
                }
            }
        }
    }

    /**
     * 应用美颜美型
     * Apply beauty and shape
     */
    private void applyBeautyAndShape(@NonNull Map<String, Float> beautyAndShapeMap) {
        /*
         * 新数据无需再次添加美颜美型，前面已经添加了
         * There is no need to add beauty type to the new data, which has been added earlier
         */
        if (beautyAndShapeMap.get(VIDEO_FLAG_IS_NEW_BEAUTY_SHAPE_DATA) != null) {
            return;
        }
        MeicamVideoFx arSceneFx = getArSceneFxEx();
        if (arSceneFx != null) {
            for (String key : beautyAndShapeMap.keySet()) {
                if (isBeautyParam(key)) {
                    openBeautyFx(arSceneFx);
                    break;
                }
            }
            Float aFloat1 = beautyAndShapeMap.get(VIDEO_FX_BEAUTY_SHAPE);
            if (aFloat1 != null && aFloat1 == 1F) {
                openShapeFx(arSceneFx);
            }

            for (Map.Entry<String, Float> entry : beautyAndShapeMap.entrySet()) {
                if (VIDEO_FX_BEAUTY_SHAPE.equals(entry.getKey())) {
                    continue;
                }
                Float value = entry.getValue();
                if (value != null) {
                    arSceneFx.setFloatVal(entry.getKey(), value);
                }
            }
        }
    }

    /**
     * 应用VideoFx
     * Apply video fx;
     */
    private void applyVideoFx() {
        for (MeicamVideoFx videoFx : videoFxs) {
            NvsVideoFx nvsVideoFx = appendNvsVideoFx(videoFx.getType(), videoFx.getDesc());
            videoFx.setObject(nvsVideoFx);
            videoFx.setAttachment();
            videoFx.setInPoint(getInPoint());
            videoFx.setOutPoint(getOutPoint());
            videoFx.bindToTimeline();
        }
    }

    /**
     * 设置默认背景
     * Set default background
     */
    void setDefaultBackground() {
        NvsVideoClip nvsVideoClip = getObject();
        if (nvsVideoClip != null) {
            MeicamVideoFx propertyVideoFx = getVideoFxById(PROPERTY_FX);
            if (propertyVideoFx == null) {
                propertyVideoFx = appendVideoFx(TYPE_PROPERTY, TYPE_PROPERTY, PROPERTY_FX);
                if (propertyVideoFx != null) {
                    propertyVideoFx.setDefaultBackground();
                }
            } else {
                propertyVideoFx.setBackground();
            }
        }
    }

    @SuppressLint("UseSparseArrays")
    @Override
    public void recoverFromLocalData(LMeicamVideoClip lVideoClip) {
        setId(lVideoClip.getId());
        setFilePath(lVideoClip.getFilePath());
        setReverseFilePath(lVideoClip.getReverseFilePath());
        setRemotePath(lVideoClip.getRemotePath());
        setLeftChannelUrl(lVideoClip.getLeftChannelUrl());
        setRightChannelUrl(lVideoClip.getRightChannelUrl());
        setFadeInDuration(lVideoClip.getFadeInDuration());
        setFadeOutDuration(lVideoClip.getFadeOutDuration());
        enableRawSourceMode(lVideoClip.isEnableRawSourceMode());
        if (!TextUtils.isEmpty(lVideoClip.getVideoType())) {
            setVideoType(lVideoClip.getVideoType());
        }
        setOrgDuration(lVideoClip.getOrgDuration());
        setVolume(lVideoClip.getVolume());
        /*这个地方一定要在设置变速前调用，因为曲线变速会更新speed的值
        * This place must be called before setting the speed change,
        * because the speed change of the curve will update the speed value.
        * */
        setKeepAudioPitch(lVideoClip.isKeepAudioPitch());
        if (TextUtils.isEmpty(lVideoClip.getCurveSpeed())) {
            if (lVideoClip.getSpeed() != 1.0f) {
                setSpeed(lVideoClip.getSpeed());
                updateInAndOutPoint();
            }
        } else {
            setCurveSpeed(lVideoClip.getCurveSpeed());
            setCurveSpeedName(lVideoClip.getCurveSpeedName());
            updateInAndOutPoint();
        }
        setVideoReverse(lVideoClip.getVideoReverse());
        setConvertSuccess(lVideoClip.isConvertSuccess());
        setImageMotionMode(lVideoClip.getImageDisplayMode());
        setSpan(lVideoClip.getSpan());
        setScan(lVideoClip.getScan());
        setOpacity(lVideoClip.getOpacity());
        setExtraRotation(lVideoClip.getExtraRotation());
        setReverse(lVideoClip.isReverse());
        setOriginalWidth(lVideoClip.getOriginalWidth());
        setOriginalHeight(lVideoClip.getOriginalHeight());
        setRoleInTheme(lVideoClip.getRoleInTheme());
        setBlendingMode(lVideoClip.getBlendingMode());
        setRectWidth(lVideoClip.getRectWidth());
        setRectHeight(lVideoClip.getRectHeight());
        if (lVideoClip.getVideoFxs() != null) {
            if (videoFxs.size() > 0) {
                videoFxs.clear();
            }
            for (LMeicamVideoFx lVideoFx : lVideoClip.getVideoFxs()) {
             /*   MeicamVideoFx videoFx = appendVideoFx(lVideoFx.getType(), lVideoFx.getSubType(), lVideoFx.getDesc());
                if (videoFx != null) {
                    videoFx.recoverFromLocalData(lVideoFx);
                }*/
                String subType = lVideoFx.getSubType();
                MeicamVideoFx videoFx;
                if (SUB_TYPE_MASK.equals(subType)) {
                    videoFx = findPropertyVideoFx();
                } else {
                    videoFx = appendVideoFx(lVideoFx.getType(), lVideoFx.getSubType(), lVideoFx.getDesc());
                }
                if (videoFx != null) {
                    videoFx.recoverFromLocalData(lVideoFx);
                }
            }
        }
        LMeicamAdjustData lAdjustData = lVideoClip.getMeicamAdjustData();
        if (lAdjustData != null) {
            Map<String, List<String>> adjustFxAndKeyMap = NvsConstants.getAdjustFxAndKeyMap();
            Set<Map.Entry<String, List<String>>> entries = adjustFxAndKeyMap.entrySet();
            Map<String, Float> keyToValueMap = lAdjustData.getKeyToValueMap();
            for (Map.Entry<String, List<String>> entry : entries) {
                MeicamVideoFx adjustVideoFx = getAdjustVideoFx(entry.getKey());
                if (adjustVideoFx != null) {
                    List<String> value = entry.getValue();
                    for (String key : value) {
                        Float aFloat = keyToValueMap.get(key);
                        if (aFloat != null) {
                            adjustVideoFx.setFloatVal(key, aFloat);
                        } else {
                            adjustVideoFx.setFloatVal(key, 0);
                        }
                    }
                }
            }
        }
        Map<String, Float> faceEffectMap = lVideoClip.getFaceEffectParameter();
        /*
         * 默认添加一个AR Sense 特效
         * Add a default AR Sense effect
         */
        if (faceEffectMap != null) {
            applyBeautyAndShape(faceEffectMap);
        }
        addStoryboards(lVideoClip);
        List<LMeicamKeyFrame> keyFrameList = lVideoClip.getKeyFrameList();
        if (keyFrameList != null) {
            MeicamVideoFx propertyVideoFx = findPropertyVideoFx();
            if (propertyVideoFx != null) {
                for (LMeicamKeyFrame lMeicamKeyFrame : keyFrameList) {
                    MeicamKeyFrame keyFrame = propertyVideoFx.keyFrameProcessor().addKeyFrame(lMeicamKeyFrame.getAtTime());
                    if (keyFrame != null) {
                        keyFrame.recoverFromLocalData(lMeicamKeyFrame);
                    }
                }
                propertyVideoFx.keyFrameProcessor().updateKeyFrameControlPoints();
            }
        }

        List<LMeicamAudioFx> audioFxList = lVideoClip.getAudioFxList();
        if (!CommonUtils.isEmpty(audioFxList)) {
            for (LMeicamAudioFx meicamAudioFx : audioFxList) {
                appendAudioFx(meicamAudioFx.getDesc());
            }
        }
        maskModel.recoverFromLocalData(lVideoClip.maskModel);
        LMeicamVideoClip.ThumbNailInfo thumbNailInfo = lVideoClip.getThumbNailInfo();
        if (thumbNailInfo != null) {
            this.thumbNailInfo = new ThumbNailInfo(thumbNailInfo.urlPrefix, thumbNailInfo.interval, thumbNailInfo.extension);
        }
    }

    @Override
    public void recoverFromTimelineData(NvsObject nvsObject) {
        if (nvsObject instanceof NvsVideoClip) {
            setObject((NvsVideoClip) nvsObject);
            loadData();
        }
    }

    private void addStoryboards(LMeicamVideoClip lVideoClip) {
        MeicamVideoFx transform2dFx = getVideoFxByType(NvsConstants.FX_TRANSFORM_2D);
        //需要最后添加transform2D特效. Transform2D effect need added after storyboard
        removeVideoFx(NvsConstants.FX_TRANSFORM_2D);
        List<LMeicamStoryboardInfo> storyboardInfos = lVideoClip.getStoryboardInfos();
        if (!CommonUtils.isEmpty(storyboardInfos)) {
            for (LMeicamStoryboardInfo lStoryboardInfo : lVideoClip.getStoryboardInfos()) {
                if (MeicamStoryboardInfo.SUB_TYPE_BACKGROUND.equals(lStoryboardInfo.getSubType())) {
                    continue;
                }
                MeicamVideoFx storyboard = appendStoryVideoFx(TYPE_BUILD_IN, lStoryboardInfo.getSubType(), "Storyboard");
                if (storyboard != null) {
                    storyboard.recoverFromLocalData(lStoryboardInfo);
                }
            }
        }
        //最后添加transform2D特效 Adding transform2D effect at last.
        if (transform2dFx != null) {
            MeicamVideoFx meicamVideoFx = appendVideoFx(transform2dFx.getType(), transform2dFx.getSubType(), transform2dFx.getDesc());
            meicamVideoFx.setIntensity(transform2dFx.getIntensity());
            meicamVideoFx.bindToTimeline();
        }

        //添加属性特技 Adding property effects
        LMeicamVideoFx propertyFx = null;
        List<LMeicamVideoFx> list = lVideoClip.getVideoFxs();
        for (LMeicamVideoFx fx : list) {
            if (NvsConstants.PROPERTY_FX.equals(fx.getDesc())) {
                propertyFx = fx;
                break;
            }
        }
        if (propertyFx == null) {
            boolean needAddProperty = true;
            //属性特技不存在，先尝试添加storyboard背景
            // The attribute effect does not exist. First try to add the storyboard background.
            if (!CommonUtils.isEmpty(storyboardInfos)) {
                for (LMeicamStoryboardInfo lStoryboardInfo : lVideoClip.getStoryboardInfos()) {
                    if (MeicamStoryboardInfo.SUB_TYPE_BACKGROUND.equals(lStoryboardInfo.getSubType())) {
                        MeicamVideoFx storyboard = appendStoryVideoFx(TYPE_BUILD_IN, lStoryboardInfo.getSubType(), "Storyboard");
                        if (storyboard != null) {
                            needAddProperty = false;
                            storyboard.recoverFromLocalData(lStoryboardInfo);
                        }
                    }
                }
            }

            //storyboard 背景添加失败，则添加一个默认的property背景特技
            //If the storyboard background fails to be added, a default property background stunt will be added.
            if (needAddProperty) {
                setDefaultBackground();
            }
        } else {
            //属性特技存在，直接恢复属性特技
            //If the attribute effect exists. Restore it directly.
            MeicamVideoFx propertyVideoFx = findPropertyVideoFx();
            if (getObject() != null) {
                getObject().enablePropertyVideoFx(true);
            }
            if (propertyVideoFx != null) {
                propertyVideoFx.recoverFromLocalData(propertyFx);
            }
        }
    }

    /**
     * 设置人脸检测模式
     * <p>
     * Set mode of detection.
     *
     * @param mode the Mode {@code NvsStreamingContext.HUMAN_DETECTION_FEATURE_VIDEO_MODE
     *             and NvsStreamingContext.HUMAN_DETECTION_FEATURE_IMAGE_MODE}
     * @return Is success or not. true：yes； false:no
     */
    public boolean setDetectionMode(int mode) {
        NvsVideoFx arSceneFx = findSceneFx();
        if (arSceneFx != null) {
            NvsARSceneManipulate arSceneManipulate = arSceneFx.getARSceneManipulate();
            if (arSceneManipulate != null) {
                arSceneManipulate.setDetectionMode(mode);
            }
            return true;
        }
        return false;
    }

    /**
     * 删除所有变声特效
     * remove all audio fx
     */
    public void removeAllAudioFx() {
        NvsVideoClip nvsVideoClip = getObject();
        if (nvsVideoClip != null) {
            for (MeicamAudioFx audioFx : audioFxList) {
                NvsAudioFx nvsAudioFx = audioFx.getObject();
                if (nvsAudioFx != null) {
                    nvsVideoClip.removeAudioFx(nvsAudioFx.getIndex());
                }
            }
            audioFxList.clear();
        }
    }

    /**
     * 添加变声特效
     * Add fx
     *
     * @param fxId the fx id 特效标识
     * @return MeicamAudioFx 特效
     */
    public MeicamAudioFx appendAudioFx(String fxId) {
        /*每次添加新的先移除旧的
        * Remove the old one before adding the new one.
        * */
        removeAudioFx(0);
        NvsAudioFx nvsAudioFx = appendNvsAudioFx(fxId);
        if (nvsAudioFx != null) {
            nvsAudioFx.setBooleanVal(NvsConstants.AUDIO_FX_CHANGE_SPEED, false);
            MeicamAudioFx audioFx = createAudioFx(fxId, nvsAudioFx);
            audioFxList.add(audioFx);
            return audioFx;
        }
        return null;
    }

    /**
     * 删除变声特效 remove audio fx
     *
     * @return audioFx removed
     */
    public MeicamAudioFx removeAudioFx(int index) {
        MeicamAudioFx audioFx = getAudioFx(index);
        if (audioFx != null) {
            if (audioFx.getObject() != null) {
                getObject().removeAudioFx(audioFx.getObject().getIndex());
                audioFxList.remove(audioFx);
            } else {
                LogUtils.e("remove audio fx failed!!!");
            }
        }
        return audioFx;
    }

    /**
     * 根据特变声效索引获取特效
     * Get audio fx by index
     *
     * @param index the fx index 特效索引
     * @return MeicamAudioFx the fx 特效
     */
    public MeicamAudioFx getAudioFx(int index) {
        if (!CommonUtils.isIndexAvailable(index, audioFxList)) {
            return null;
        }
        for (MeicamAudioFx audioFx : audioFxList) {
            if (audioFx.getIndex() == index) {
                return audioFx;
            }
        }
        return null;
    }

    public MeicamTimeline getInternalTimeline() {
        NvsVideoClip nvsVideoClip = getObject();
        if (nvsVideoClip == null) {
            return null;
        }
        NvsTimeline nvsTimeline = nvsVideoClip.getInternalTimeline();
        if (nvsTimeline == null) {
            return null;
        }
        if (internalTimeline == null) {
            internalTimeline = new MeicamTimeline.TimelineBuilder
                    (NvsStreamingContext.getInstance(), MeicamTimeline.TimelineBuilder.BUILD_FORM_NVSTIMELINE)
                    .recoverMeicamTimeline(nvsTimeline);
        }
        return internalTimeline;
    }

    public void setInternalTimeline(MeicamTimeline internalTimeline) {
        this.internalTimeline = internalTimeline;
    }

    /**
     * 添加变声特效
     * Append the NvsAudioFx
     *
     * @param fxId the fx id 特效标识
     * @return NvsVideoFx the fx 底层特效
     */
    private NvsAudioFx appendNvsAudioFx(String fxId) {
        NvsVideoClip videoClip = getObject();
        if (videoClip == null) {
            return null;
        }
        return videoClip.appendAudioFx(fxId);
    }

    private MeicamAudioFx createAudioFx(String fxId, NvsAudioFx nvsVideoFx) {
        return new MeicamAudioFx(nvsVideoFx, 0, TYPE_BUILD_IN, fxId);
    }

    /**
     * 获取变声特效数量
     * Get audio fx count
     *
     * @return the audio fx count
     */
    public int getAudioFxCount() {
        return audioFxList == null ? 0 : audioFxList.size();
    }

    public long getTrimOffSet() {
        return trimOffSet;
    }

    @Override
    public LMeicamVideoClip parseToLocalData() {
        LMeicamVideoClip local = new LMeicamVideoClip();
        setCommonData(local);
        local.setId(getId());
        local.setFilePath(getFilePath());
        local.setReverseFilePath(getReverseFilePath());
        local.setVideoType(getVideoType());
        local.setTrimIn(getTrimIn());
        local.setTrimOut(getTrimOut());
        local.setOrgDuration(getOrgDuration());
        local.setVolume(getVolume());
        local.setFadeInDuration(getFadeInDuration());
        local.setFadeOutDuration(getFadeOutDuration());
        local.setSpeed(getSpeed());
        local.setCurveSpeed(getCurveSpeed());
        local.setCurveSpeedName(getCurveSpeedName());
        local.setVideoReverse(getVideoReverse());
        local.setConvertSuccess(isConvertSuccess());
        local.setmImageDisplayMode(getImageMotionMode());
        local.setSpan(getSpan());
        local.setScan(getScan());
        local.setOpacity(getOpacity());
        local.setExtraRotation(getExtraRotation());
        local.setReverse(isReverse());
        local.setResourceId(resourceId);
        local.setKeepAudioPitch(isKeepAudioPitch());
        local.setOriginalWidth(getOriginalWidth());
        local.setOriginalHeight(getOriginalHeight());
        local.setBlendingMode(getBlendingMode());
        local.setImageMotionAnimationEnabled(isImageMotionAnimationEnabled());
        local.setRemotePath(remotePath);
        local.setLeftChannelUrl(leftChannelUrl);
        local.setRightChannelUrl(rightChannelUrl);
        local.setEnableRawSourceMode(enableRawSourceMode);
        local.setRectWidth(rectWidth);
        local.setRectHeight(rectHeight);
        ThumbNailInfo thumbNailInfo = getThumbNailInfo();
        if (thumbNailInfo != null) {
            local.setThumbNailInfo(new LMeicamVideoClip.ThumbNailInfo(thumbNailInfo.urlPrefix, thumbNailInfo.interval, thumbNailInfo.extension));
        }
        for (MeicamVideoFx videoFx : videoFxs) {
            local.getVideoFxs().add(videoFx.parseToLocalData());
        }
        if (mAdjustData != null && !mAdjustData.isEmpty()) {
            MeicamAdjustData adjustData = new MeicamAdjustData();
            Map<String, List<String>> adjustFxAndKeyMap = NvsConstants.getAdjustFxAndKeyMap();
            Set<Map.Entry<String, List<String>>> entries = adjustFxAndKeyMap.entrySet();
            for (Map.Entry<String, List<String>> entry : entries) {
                MeicamVideoFx meicamVideoFx = mAdjustData.get(entry.getKey());
                List<String> value = entry.getValue();
                if (meicamVideoFx != null) {
                    for (String key : value) {
                        float floatVal = meicamVideoFx.getFloatVal(key);
                        if (floatVal == INVALID_VALUE) {
                            floatVal = 0F;
                        }
                        adjustData.setValue(key, floatVal);
                    }
                } else {
                    for (String key : value) {
                        adjustData.setValue(key, 0);
                    }
                }
            }
            local.setMeicamAdjustData(adjustData.parseToLocalData());
        }
        local.setRoleInTheme(getRoleInTheme());
        Set<String> storyKeySet = mStoryboardMap.keySet();
        for (String key : storyKeySet) {
            MeicamStoryboardInfo storyboardInfo = mStoryboardMap.get(key);
            if (storyboardInfo != null) {
                local.getStoryboardInfos().add(storyboardInfo.parseToLocalData());
            }
        }
        if (keyFrameMap != null && keyFrameMap.size() > 0) {
            List<LMeicamKeyFrame> lKeyFrameList = new ArrayList<>();
            for (Map.Entry<Long, MeicamKeyFrame> entry : keyFrameMap.entrySet()) {
                MeicamKeyFrame value = entry.getValue();
                if (value != null) {
                    lKeyFrameList.add(value.parseToLocalData());
                }
            }
            local.setKeyFrameList(lKeyFrameList);
        }
        local.maskModel = maskModel.parseToLocalData();
        if (!CommonUtils.isEmpty(audioFxList)) {
            for (MeicamAudioFx meicamAudioFx : audioFxList) {
                local.getAudioFxList().add(meicamAudioFx.parseToLocalData());
            }
        }
        return local;
    }

    @Override
    public void parseToResourceId(MeicamTimeline timeline) {
        if (timeline == null) {
            return;
        }
        if (!TextUtils.isEmpty(reverseFilePath) || !TextUtils.isEmpty(filePath)) {
            MeicamResource resource = new MeicamResource();
            if (!TextUtils.isEmpty(reverseFilePath)) {
                resource.addPathInfo(new MeicamResource.PathInfo("reversePath", reverseFilePath, true));
            }
            if (!TextUtils.isEmpty(filePath)) {
                resource.addPathInfo(new MeicamResource.PathInfo("path", filePath, true));
            }
            resourceId = timeline.getPlaceId(resource);
        }
    }

    @NonNull
    @Override
    public String toString() {
        return "VideoClip{index=" + getIndex() + ",inPoint=" + inPoint + ",outPoint=" + outPoint + ",trimIn=" + trimIn + ",trimOut=" + trimOut + "}";
    }

    /**
     * set prop.
     * 设置道具
     *
     * @param assetsPath the package path 道具包路径
     * @param packageId  the package id 道具包id
     * @return the boolean 是否添加成功
     */
    public boolean setProp(String assetsPath, String packageId) {
        MeicamVideoFx arSceneFx = getArSceneFxEx();
        if (arSceneFx != null) {
            arSceneFx.setStringVal(NvsConstants.VIDEO_FX_PRAM_SCENE_ID, packageId);
            arSceneFx.setAttachment(CommonData.ATTACHMENT_KEY_PACKAGE_PATH, assetsPath);
            return true;
        }
        return false;
    }

    /**
     * Delete prop.
     * 删除道具
     */
    public void deleteProp() {
        setProp("", null);
    }

    /**
     * Gets prop id.
     * 获取添加的道具Id
     *
     * @return the prop id 道具id
     */
    public String getPropId() {
        MeicamVideoFx arSceneFx = getArSceneFxEx();
        if (arSceneFx != null) {
            return arSceneFx.getStringVal(NvsConstants.VIDEO_FX_PRAM_SCENE_ID);
        }
        return null;
    }

    /**
     * Gets prop path.
     * 获取添加的道具包路径
     *
     * @return the prop path 道具包路径
     */
    public String getPropPath() {
        MeicamVideoFx arSceneFx = getArSceneFxEx();
        if (arSceneFx != null) {
            return (String) arSceneFx.getAttachment(CommonData.ATTACHMENT_KEY_PACKAGE_PATH);
        }
        return null;
    }


    public void enableROI(boolean enable) {
        NvsVideoClip object = getObject();
        if (object != null) {
            object.enableVideoClipROI(enable);
        }
    }

    public float[] getTranslation(long atTime) {
        MeicamVideoFx videoFx = findPropertyVideoFx();
        float[] trans = new float[2];
        if (videoFx != null) {
            trans[0] = videoFx.getFloatVal(NvsConstants.FX_TRANSFORM_2D_TRANS_X, 0);
            trans[1] = videoFx.getFloatVal(NvsConstants.FX_TRANSFORM_2D_TRANS_Y, 0);
        }
        MeicamVideoFx transform2D = getVideoFxById(NvsConstants.FX_TRANSFORM_2D);
        if (transform2D != null) {
            trans[0] += transform2D.getFloatValAtTime(NvsConstants.FX_TRANSFORM_2D_TRANS_X, atTime);
            trans[1] += transform2D.getFloatValAtTime(NvsConstants.FX_TRANSFORM_2D_TRANS_Y, atTime);
        }
        return trans;
    }

    public float getScale() {
        MeicamVideoFx videoFx = findPropertyVideoFx();
        float scale = 1;
        if (videoFx != null) {
            scale *= videoFx.getFloatVal(NvsConstants.FX_TRANSFORM_2D_SCALE_X, 1);
        }
        MeicamVideoFx transform2D = getVideoFxById(NvsConstants.FX_TRANSFORM_2D);
        if (transform2D != null) {
            scale *= transform2D.getFloatVal(NvsConstants.FX_TRANSFORM_2D_SCALE_X, 0);
        }
        return scale;
    }

    public ThumbNailInfo getThumbNailInfo() {
        return thumbNailInfo;
    }

    public void setThumbNailInfo(ThumbNailInfo thumbNailInfo) {
        this.thumbNailInfo = thumbNailInfo;
    }

    public void enableRawSourceMode(boolean enable) {
        NvsVideoClip object = getObject();
        if (object != null) {
            object.enableRawSourceMode(enable);
        }
        enableRawSourceMode = enable;
    }

    public boolean isEnableRawSourceMode() {
        return enableRawSourceMode;
    }

    public void disableAmbiguousCrop(boolean disable) {
        NvsVideoClip object = getObject();
        if (object != null) {
            object.disableAmbiguousCrop(disable);
        }
    }

    public boolean isPropertyVideoFxEnabled() {
        NvsVideoClip object = getObject();
        if (object != null) {
            return object.isPropertyVideoFxEnabled();
        }
        return false;
    }

    public static class ThumbNailInfo {
        public String urlPrefix;
        public long interval;
        public String extension;

        public ThumbNailInfo(String urlPrefix, long interval, String extension) {
            this.urlPrefix = urlPrefix;
            this.interval = interval;
            this.extension = extension;
        }
    }

}
