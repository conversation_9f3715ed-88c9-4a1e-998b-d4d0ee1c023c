package com.meishe.myvideo.manager;

import android.content.Context;
import android.media.AudioFormat;
import android.media.MediaRecorder;

import com.meishe.base.utils.PermissionUtils;
import com.meishe.engine.util.PathUtils;
import com.meishe.myvideo.audio.AudioRecorder;
import com.meishe.myvideo.audio.AudioStatusListener;
import com.meishe.myvideo.manager.observer.AudioRecordObservable;
import com.meishe.myvideo.manager.observer.AudioRecordObserver;
import com.meishe.myvideo.util.PixelPerMicrosecondUtil;

import static com.meishe.base.utils.PermissionConstants.STORAGE;

/**
 * The type Audio record manager.
 * 此类为音频记录管理器
 *
 * @author：chu chenguang on 2020/6/29 16:49
 */
public class AudioRecordManager {


    private static final AudioRecordManager ourInstance = new AudioRecordManager();

    private AudioRecorder mAudioRecorder;

    /**
     * The interface On record listener.
     * 记录监听器上的接口
     */
    public interface OnRecordListener {

    }

    /**
     * Gets instance.
     * 获取实例
     *
     * @return the instance
     */
    public static AudioRecordManager getInstance() {
        return ourInstance;
    }

    private AudioRecordObservable audioRecordObservable;

    private AudioRecordManager() {
        audioRecordObservable = new AudioRecordObservable();
    }

    /**
     * Init audio record manager.
     * 初始化音频记录管理器
     *
     * @return the audio record manager
     */
    public AudioRecordManager init() {
        AudioRecorder.RecordConfig recordConfig = new AudioRecorder.RecordConfig(MediaRecorder.AudioSource.MIC,
                AudioRecorder.RecordConfig.SAMPLE_RATE_44K_HZ, AudioFormat.CHANNEL_IN_MONO,
                AudioFormat.ENCODING_PCM_16BIT);

        //AudioRecorder可以考虑不采用单例模式，毕竟仅仅是一个功能，没必要一直持有资源。
        // AudioRecorder can consider not using singleton mode. After all, it is only a function, and it is not necessary to keep resources.
        mAudioRecorder = AudioRecorder.getInstance()
                .setRecordConfig(recordConfig)
                .setAudioStatusListener(mAudioStatusListener)
                .setMaxRecordTime(Integer.MAX_VALUE)
                .setVolumeInterval(200);
        if (PermissionUtils.isGroupGranted(STORAGE)) {
            mAudioRecorder.setRecordFilePath(PathUtils.getAudioRecordFilePath());
        }
        return this;
    }

    /**
     * 开始录音
     * start recording
     *
     * @param context the context
     */
    public void startRecord(Context context) {
        String fileName = System.currentTimeMillis() + ".wav";
        if (mAudioRecorder != null) {

            mAudioRecorder.setRecordFileName(fileName)
                    .setPerSecondPixel((int) (PixelPerMicrosecondUtil.getPixelPerMicrosecond(context) * 1000 * 1000));
            mAudioRecorder.start(context);
        }

    }

    /**
     * 停止录音
     * Record Audio Stop
     */
    public void stopRecord() {
        if (mAudioRecorder != null) {
            mAudioRecorder.stop();
        }
    }

    /**
     * 录音状态监听回调
     * Recording status listens for callbacks
     */
    private AudioStatusListener mAudioStatusListener = new AudioStatusListener() {
        @Override
        public void onStartRecording(String path) {
            long nowTime = System.currentTimeMillis();
            if (audioRecordObservable != null) {
                audioRecordObservable.onRecordStart(nowTime, path);
            }
        }

        @Override
        public void onRecordData(short[] data, int length) {

        }

        /**
         *
         * @param wave float[] 一组波形数据,波形上边和下边数据依次交替进行
         * @param during 录制时长
         * @param path 路径
         */
        @Override
        public void onWaveDate(float[] wave, int during, String path) {
            if (audioRecordObservable != null) {
                audioRecordObservable.onRecordProgress(wave, during, path);
            }
        }

        @Override
        public void onVoiceVolume(int volume) {

        }

        @Override
        public void onRecordError(int code, String errorMsg) {
            if (audioRecordObservable != null) {
                audioRecordObservable.onRecordFail(errorMsg);
            }
        }

        @Override
        public void onFileSaveFailed(String error) {
            if (audioRecordObservable != null) {
                audioRecordObservable.onRecordFail(error);
            }
        }

        @Override
        public void onFileSaveSuccess(String fileUri, long duration) {

        }

        @Override
        public void onStopRecording() {
            if (audioRecordObservable != null) {
                audioRecordObservable.onRecordEnd();
            }
        }
    };

    /**
     * 注册app进入后台的监听
     * register background observer
     */
    public void registerConvertFileObserver(AudioRecordObserver observer) {
        if (audioRecordObservable != null && observer != null) {
            audioRecordObservable.registerObserver(observer);
        }
    }

    /**
     * 注销app进入后台的监听
     * unregister background observer
     */
    public void unregisterConvertFileObserver(AudioRecordObserver observer) {
        if (audioRecordObservable != null && observer != null) {
            audioRecordObservable.unregisterObserver(observer);
        }
    }
}
