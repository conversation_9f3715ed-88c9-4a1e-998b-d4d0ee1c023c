package com.meishe.user.view.fragment;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;
import com.meishe.base.adapter.CommonFragmentAdapter;
import com.meishe.base.model.BaseMvpFragment;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.draft.data.DraftData;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.user.CloudDraftManager;
import com.meishe.user.R;
import com.meishe.user.bean.CloudDraftData;
import com.meishe.user.bean.VideoCompileBean;
import com.meishe.user.manager.CloudCompileManager;
import com.meishe.user.manager.observer.DataObserver;
import com.meishe.user.view.iview.EditingView;
import com.meishe.user.view.presenter.EditingPresenter;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2020/12/2 20:42
 * @Description :剪辑页面 The page of Edit
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class UserEditingFragment extends BaseMvpFragment<EditingPresenter> implements EditingView, View.OnClickListener {

    private static final String BOTTOM_LAYOUT_ID = "bottomLayoutId";
    private TextView mTvDraftManager;
    private TabLayout mTabLayout;
    private ViewPager mViewPager;
    private List<Fragment> mFragmentList = new ArrayList<>();
    private TextView mUpLoadCountHintView;
    private TextView mUpLoadCompileCountHintView;
    private View mRefreshButton;
    private boolean isEditState;
    private CloudBottomDeleteFragment mBottomDeleteView;
    private DataObserver mDataObserver;
    /**
     * 底部图id，用于Activity的弹窗
     * Bottom figure ID, pop-up window for activity
     */
    private int mBottomLayoutId;
    /**
     * 更新草稿管理
     */
    public static final int MESSAGE_UPDATE_DRAFT_MANAGER = 0;

    public UserEditingFragment() {
    }

    public static UserEditingFragment create(int id) {
        UserEditingFragment editingFragment = new UserEditingFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(BOTTOM_LAYOUT_ID, id);
        editingFragment.setArguments(bundle);
        return editingFragment;
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_user_editing;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EventBus.getDefault().register(this);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

    @Override
    protected void initView(View rootView) {
        mTvDraftManager = rootView.findViewById(R.id.tv_draft_manager);
        mTabLayout = rootView.findViewById(R.id.tab_layout);
        mViewPager = rootView.findViewById(R.id.vp_pager);
        mUpLoadCountHintView = rootView.findViewById(R.id.tv_upload_count_hint);
        mUpLoadCompileCountHintView = rootView.findViewById(R.id.tv_upload_count_hint_compile);
        mRefreshButton = rootView.findViewById(R.id.ib_refresh);
        initViewPager();
        initListener();
    }

    private void initViewPager() {
        try {
            Class<?> aClass = Class.forName(" com.meishe.myvideo.fragment.DraftFragment");
            Method create = aClass.getMethod("create");
            Fragment fragment = (Fragment) create.invoke(null);
            mFragmentList.add(fragment);
        } catch (Exception  e) {
            LogUtils.e(e);
        }
        mFragmentList.add(CloudDraftFragment.create());
        mFragmentList.add(CloudCompileFragment.create());
        String[] tabs = getResources().getStringArray(R.array.tab_draft_b);
        FragmentPagerAdapter adapter = new CommonFragmentAdapter(getChildFragmentManager(), mFragmentList);
        mViewPager.setAdapter(adapter);
        mViewPager.setOffscreenPageLimit(1);
        mTabLayout.setupWithViewPager(mViewPager);
        mTabLayout.removeAllTabs();
        for (String tab : tabs) {
            mTabLayout.addTab(mTabLayout.newTab().setText(tab));
        }
    }

    private void initListener() {
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                mRefreshButton.setVisibility(position == 0 ? View.GONE : View.VISIBLE);
                mTvDraftManager.setVisibility(position == 2 ? View.GONE : View.VISIBLE);
                exitManagerState();

            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
        CloudDraftManager.getInstance().registerDataObserver(mDataObserver = new DataObserver() {
            @SuppressLint("SetTextI18n")
            @Override
            public void onUploadDataChanged(List<DraftData> data) {
                updateHintText();
            }

            @Override
            public void onDownloadDataChanged(List<CloudDraftData> data) {
                updateHintText();
            }
        });
        CloudCompileManager.getInstance().registerUpDownloadDataObserver(new CloudCompileManager.CompileUpDownloadDataObserver() {
            @Override
            public void onDownloadDataChanged(List<VideoCompileBean> data) {
                updateCompileHintText();
            }

            @Override
            public void onUploadDataChanged(List<VideoCompileBean> data) {
                updateCompileHintText();
            }
        });
        mTvDraftManager.setOnClickListener(this);
        mRefreshButton.setOnClickListener(this);
    }

    private void updateHintText() {
        List<CloudDraftData> downloadData = CloudDraftManager.getInstance().getDownloadData();
        List<DraftData> uploadData = CloudDraftManager.getInstance().getUploadData();
        int allCount = uploadData == null ? 0 : uploadData.size() + downloadData.size();
        if (allCount <= 0) {
            mUpLoadCountHintView.setVisibility(View.INVISIBLE);
        } else {
            mUpLoadCountHintView.setVisibility(View.VISIBLE);
            mUpLoadCountHintView.setText(String.valueOf(allCount));
        }
    }

    private void updateCompileHintText() {
        List<VideoCompileBean> downloadData = CloudCompileManager.getInstance().getCloudCompilingData();
        List<VideoCompileBean> uploadData = CloudCompileManager.getInstance().getDownloadingData();
        int allCount = uploadData == null ? 0 : uploadData.size() + downloadData.size();
        if (allCount <= 0) {
            mUpLoadCompileCountHintView.setVisibility(View.INVISIBLE);
        } else {
            mUpLoadCompileCountHintView.setVisibility(View.VISIBLE);
            mUpLoadCompileCountHintView.setText(String.valueOf(allCount));
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        CloudDraftManager.getInstance().unRegisterDataObserver(mDataObserver);
    }

    @Override
    protected void initData() {
        Bundle arguments = getArguments();
        if (arguments != null) {
            mBottomLayoutId = arguments.getInt(BOTTOM_LAYOUT_ID, 0);
        }
    }

    /**
     * Refresh data.
     * 刷新数据
     */
    public void refreshData() {
        for (int index = 0; index < mFragmentList.size(); index++) {
            Fragment fragment = mFragmentList.get(index);
            if (index == 0) {
                Class<? extends Fragment> aClass = fragment.getClass();
                try {
                    Method refreshData = aClass.getMethod("refreshData");
                    refreshData.invoke(fragment);
                } catch (Exception e) {
                    LogUtils.e(e);
                }
            } else {
                if (fragment instanceof CloudDraftFragment) {
                    ((CloudDraftFragment) fragment).refreshData(false);
                } else if (fragment instanceof CloudCompileFragment) {
                    ((CloudCompileFragment) fragment).refreshData(false);
                }
            }
        }
    }


    /**
     * Refresh data.
     * 刷新数据
     *
     * @param index the index 页面的index the index of fragment
     */
    public void refreshData(int index) {
        if (CommonUtils.isIndexAvailable(index, mFragmentList)) {
            Fragment fragment = mFragmentList.get(index);
            if (index == 0) {
                Class<? extends Fragment> aClass = fragment.getClass();
                try {
                    Method refreshData = aClass.getMethod("refreshData");
                    refreshData.invoke(fragment);
                } catch (Exception e) {
                    LogUtils.e(e);
                }
            } else {
                if (fragment instanceof CloudDraftFragment) {
                    ((CloudDraftFragment) fragment).refreshData(false);
                } else if (fragment instanceof CloudCompileFragment) {
                    ((CloudCompileFragment) fragment).refreshData(false);
                }
            }
        }
    }

    private void goManagerState() {
        isEditState = true;
        mTvDraftManager.setText(getResources().getString(R.string.cancel));
        int currentItem = mViewPager.getCurrentItem();
        if (CommonUtils.isIndexAvailable(currentItem, mFragmentList)) {
            Fragment fragment = mFragmentList.get(currentItem);
            if (currentItem == 0) {
                Class<? extends Fragment> aClass = fragment.getClass();
                try {
                    Method refreshData = aClass.getMethod("goManagerState", int.class);
                    refreshData.invoke(fragment, mBottomLayoutId);
                } catch (Exception e) {
                    LogUtils.e(e);
                }
            } else {
                if (fragment instanceof CloudDraftFragment) {
                    ((CloudDraftFragment) fragment).goManagerState(mBottomLayoutId);
                }
            }
        }
    }


    private void exitManagerState() {
        isEditState = false;
        mTvDraftManager.setText(getResources().getString(R.string.draft_manage));
        for (int index = 0; index < mFragmentList.size(); index++) {
            Fragment fragment = mFragmentList.get(index);
            if (index == 0) {
                Class<? extends Fragment> aClass = fragment.getClass();
                try {
                    Method refreshData = aClass.getMethod("exitManagerState");
                    refreshData.invoke(fragment);
                } catch (Exception e) {
                    LogUtils.e(e);
                }
            } else {
                if (fragment instanceof CloudDraftFragment) {
                    ((CloudDraftFragment) fragment).exitManagerState(mBottomLayoutId);
                }
            }
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.tv_draft_manager) {
            if (isEditState) {
                exitManagerState();
            } else {
                goManagerState();
            }
        } else if (id == R.id.ib_refresh) {
            refreshData(mViewPager.getCurrentItem());
        }
    }

    /**
     * On message event.
     * 消息事件
     *
     * @param event the event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(Integer event) {
        if (event == MESSAGE_UPDATE_DRAFT_MANAGER) {
            mTvDraftManager.setText(getResources().getString(R.string.draft_manage));
        }
    }

    public void goToCompilingPage() {
        int index = mFragmentList.size() - 1;
        mViewPager.setCurrentItem(index);
        Fragment fragment = mFragmentList.get(index);
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin != null) {
            userPlugin.goToCompilingPage(fragment);
        }
    }
}
