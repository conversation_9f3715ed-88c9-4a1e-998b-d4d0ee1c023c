package com.meishe.user.manager;

import android.text.TextUtils;

import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ThreadUtils;
import com.meishe.draft.db.DraftDbManager;
import com.meishe.draft.db.FileInfoDao;
import com.meishe.draft.db.FileInfoEntity;
import com.meishe.engine.DownloadManager;
import com.meishe.engine.EngineNetApi;
import com.meishe.engine.bean.bridges.FileInfoBridge;
import com.meishe.engine.observer.ConvertFileObservable;
import com.meishe.engine.observer.ConvertFileObserver;
import com.meishe.engine.observer.DownLoadObserver;
import com.meishe.engine.util.IConvertManager;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;
import com.meishe.user.bean.ReverseFileBean;

import java.io.File;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/6/10 15:36
 * @Description :云倒放文件管理器 The manager of reversing file by cloud.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CloudReverseFileManager implements IConvertManager {
    private static final String TEMP_NAME = "_meicamTemp";
    private static final int MAX_PROGRESS = 99;
    private ConvertParam mConvertParam;
    private ConvertFileObservable convertFileObservable;
    private CloudJobManager mJobManager;
    private String mToken;
    private FileInfoDao mFileInfoDao;

    public CloudReverseFileManager() {
        convertFileObservable = new ConvertFileObservable();
    }


    /**
     * 转换倒放文件,目前仅支持执行完一次convertParam所给的任务后才能继续下一个。
     * Convert file
     *
     * @param convertParam 转换参数
     */
    @Override
    public void convertFile(ConvertParam convertParam) {
        if (convertParam == null || convertParam.paramMap == null || convertParam.paramMap.isEmpty()) {
            LogUtils.e("convertParam== null");
            return;
        }
        mConvertParam = convertParam;
        mJobManager = new CloudJobManager();
        mJobManager.setJobStateChangedListener(new CloudJobManager.JobStateChangedListener() {
            @Override
            public void onChanged(CloudCompileManager.JobItem jobItem) {
                String taskId = mJobManager.getJobUUid(jobItem.jobId);
                if (mConvertParam != null && !CommonUtils.isEmpty(mConvertParam.paramMap)) {
                    int size = mConvertParam.paramMap.size();
                    int progress = jobItem.progress;
                    int p = (int) (progress * 0.45);
                    Set<Map.Entry<String, ConvertParam.Param>> entries = mConvertParam.paramMap.entrySet();
                    if (!entries.isEmpty()) {
                        for (Map.Entry<String, ConvertParam.Param> entry : entries) {
                            ConvertParam.Param param = entry.getValue();
                            if (taskId != null && param.getTaskId() == Integer.valueOf(taskId)) {
                                if (progress == 100 && jobItem.state == 1) {
                                    param.setProgress(50);
                                    param.setFinish(true);
                                    param.setSuccess(true);
                                    reverseFileItem(mToken, param);
                                } else {
                                    param.setProgress((int) ((progress * 0.45) + 5));
                                }
                            } else {
                                p = p + param.getProgress();
                            }
                        }
                    }
                    p = p / size;
                    if (p > MAX_PROGRESS) {
                        p = MAX_PROGRESS;
                    }
                    if (convertFileObservable != null) {
                        convertFileObservable.onConvertProgress(p);
                    }
                }
            }

            @Override
            public void onFailed() {
                if (convertFileObservable != null) {
                    convertFileObservable.onConvertFinish(mConvertParam, false);
                }
            }
        });
        reverseFile();
    }

    private void reverseFile() {
        Set<Map.Entry<String, ConvertParam.Param>> entries = mConvertParam.paramMap.entrySet();
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        mToken = userPlugin.getToken();
        if (TextUtils.isEmpty(mToken)) {
            return;
        }
        for (Map.Entry<String, ConvertParam.Param> entry : entries) {
            ConvertParam.Param param = entry.getValue();
            if (TextUtils.isEmpty(param.getSrcFile()) || TextUtils.isEmpty(param.getDstFile())) {
                continue;
            }
            reverseFileItem(mToken, param);
        }
    }

    private void reverseFileItem(String token, ConvertParam.Param param) {
        String srcFile = param.getSrcFile();
        ThreadUtils.getSinglePool().execute(new Runnable() {
            @Override
            public void run() {
                FileInfoDao fileInfoDao = getFileInfoDao();
                FileInfoEntity fileByLocalPath = fileInfoDao.getFileByLocalPath(srcFile);
                if (fileByLocalPath != null) {
                    String reversePath = fileByLocalPath.getLocalReversePath();
                    if (!TextUtils.isEmpty(reversePath) && new File(reversePath).exists()) {
                        param.setDstFile(reversePath);
                        param.setSuccess(true);
                        param.setFinish(true);

                        if (mConvertParam != null) {
                            int count = mConvertParam.paramMap.size();
                            boolean success = true;
                            Set<Map.Entry<String, ConvertParam.Param>> entries = mConvertParam.paramMap.entrySet();
                            if (!entries.isEmpty()) {
                                for (Map.Entry<String, ConvertParam.Param> entry : entries) {
                                    ConvertParam.Param paramItem = entry.getValue();
                                    if (paramItem.isFinish()) {
                                        count--;
                                    }
                                    if (success) {
                                        success = paramItem.isSuccess();
                                    }
                                }
                            }
                            if (count == 0) {
                                handleConvertResult(success);
                            }
                        }
                        return;
                    }
                }
                ThreadUtils.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        FileInfoBridge.FileInfo fileInfo = FileInfoBridge.getFileInfo(srcFile);
                        if (fileInfo != null) {
                            String taskId = fileInfo.resourceId;
                            if (TextUtils.isEmpty(taskId)) {
                                if (convertFileObservable != null) {
                                    convertFileObservable.onConvertFinish(mConvertParam, false);
                                }
                                return;
                            }
                            mJobManager.createTemporaryJob(taskId);
                            EngineNetApi.reverseFile(token, taskId, new RequestCallback<ReverseFileBean>() {
                                @Override
                                public void onSuccess(BaseResponse<ReverseFileBean> response) {
                                    if (response == null) {
                                        if (convertFileObservable != null) {
                                            convertFileObservable.onConvertFinish(mConvertParam, false);
                                        }
                                        return;
                                    }
                                    ReverseFileBean data = response.getData();
                                    if (data == null) {
                                        if (convertFileObservable != null) {
                                            convertFileObservable.onConvertFinish(mConvertParam, false);
                                        }
                                        return;
                                    }
                                    int jobId = data.getJobId();
                                    if (jobId > 0) {
                                        mJobManager.updateJobInfo(taskId, String.valueOf(jobId));
                                        List<Integer> jobList = new ArrayList<>();
                                        jobList.add(jobId);
                                        mJobManager.getJobInfo(jobList);
                                    } else {
                                        mJobManager.deleteJob(taskId);
                                        String url = data.getUrl();
                                        if (TextUtils.isEmpty(url)) {
                                            if (convertFileObservable != null) {
                                                convertFileObservable.onConvertFinish(mConvertParam, false);
                                            }
                                            return;
                                        }
                                        String oldSrcFile = param.getSrcFile();
                                        param.setSrcFile(url);
                                        donwloadFile(url, taskId, param.getDstFile(), oldSrcFile);
                                    }
                                }

                                @Override
                                public void onError(BaseResponse<ReverseFileBean> response) {
                                    if (convertFileObservable != null) {
                                        convertFileObservable.onConvertFinish(mConvertParam, false);
                                    }
                                }
                            });
                            param.setTaskId(Long.parseLong(taskId));
                        }
                    }
                });
            }
        });
    }

    Set<DownloadManager> mDownloadManagerSet = new HashSet<>();
    private void donwloadFile(String url, String taskId, String destFile, String srcFile) {
        DownloadManager downloadManager = new DownloadManager();
        mDownloadManagerSet.add(downloadManager);
        DownloadManager.DownloadParam<String> downloadParam = new DownloadManager.DownloadParam<>(url);
        downloadParam.appendParam(url,destFile);
        downloadManager.downloadFile(downloadParam, 100, new DownLoadObserver<String>(){
            @Override
            public void onFailed(String tag) {
                if (convertFileObservable != null) {
                    convertFileObservable.onConvertFinish(mConvertParam, false);
                }
            }

            @Override
            public void onSuccess(String tag, DownloadManager.DownloadParam<String> param) {
                super.onSuccess(tag, param);
                if (mConvertParam != null) {
                    int count = mConvertParam.paramMap.size();
                    boolean success = true;
                    Set<Map.Entry<String, ConvertParam.Param>> entries = mConvertParam.paramMap.entrySet();
                    if (!entries.isEmpty()) {
                        for (Map.Entry<String, ConvertParam.Param> entry : entries) {
                            ConvertParam.Param paramItem = entry.getValue();
                            if (paramItem.getTaskId() == Integer.valueOf(taskId)) {
                                DownloadManager.Param downloadParam = param.getParam(paramItem.getSrcFile());
                                if (downloadParam != null) {
                                    paramItem.dstFile = downloadParam.dstFile;
                                }
                                paramItem.setFinish(true);
                                paramItem.setSuccess(true);
                                if (paramItem.isFinish()) {
                                    count--;
                                }
                                if (success) {
                                    success = paramItem.isSuccess();
                                }
                                ThreadUtils.getSinglePool().execute(new Runnable() {
                                    @Override
                                    public void run() {
                                        FileInfoDao fileInfoDao = getFileInfoDao();
                                        FileInfoEntity localPath = fileInfoDao.getFileByLocalPath(srcFile);
                                        String fileMD5 = FileUtils.getFileMD5ToString(srcFile);
                                        if (localPath == null) {
                                            localPath = new FileInfoEntity();
                                            localPath.setMd5(fileMD5);
                                            localPath.setId(fileMD5);
                                            localPath.setLocalPath(srcFile);
                                            localPath.setLocalReversePath(paramItem.dstFile);
                                            localPath.setUrl(url);
                                            fileInfoDao.insertDraft(localPath);
                                        } else {
                                            localPath.setLocalReversePath(paramItem.dstFile);
                                            localPath.setMd5(fileMD5);
                                            fileInfoDao.updateDraft(localPath);
                                        }
                                    }
                                });
                            }
                        }
                    }
                    if (count == 0) {
                        handleConvertResult(success);
                    }
                }
            }

            @Override
            public void onProgress(String tag, int progress) {
                if (mConvertParam != null && !CommonUtils.isEmpty(mConvertParam.paramMap)) {
                    int size = mConvertParam.paramMap.size();
                    int p = (int) (progress * 0.5) + 50;
                    Set<Map.Entry<String, ConvertParam.Param>> entries = mConvertParam.paramMap.entrySet();
                    if (!entries.isEmpty()) {
                        for (Map.Entry<String, ConvertParam.Param> entry : entries) {
                            ConvertParam.Param param = entry.getValue();
                            if (param.getTaskId() == Integer.valueOf(taskId)) {
                                param.setProgress((int) ((progress * 0.5F) + 50));
                            } else {
                                p = p + param.getProgress();
                            }
                        }
                    }
                    p = p / size;
                    if (p > MAX_PROGRESS) {
                        p = MAX_PROGRESS;
                    }
                    if (convertFileObservable != null) {
                        convertFileObservable.onConvertProgress(p);
                    }
                }
            }
        });
    }

    private FileInfoDao getFileInfoDao() {
        if (mFileInfoDao == null) {
            mFileInfoDao = DraftDbManager.get().getFileInfoDao();
        }
        return mFileInfoDao;
    }

    /**
     * Gets dest file path.
     * 获取目标文件的路径
     *
     * @param srcFilePath the src file path 源文件
     * @param destFolder  the dest folder 目标文件夹
     * @return the dest file path 目标文件路径
     */
    public static String getDestFilePath(String srcFilePath, String destFolder) {
        return destFolder + File.separator + FileUtils.getFileMD5ToString(srcFilePath);
    }

    /**
     * Cancel convert.
     * 取消转换
     */
    @Override
    public void cancelConvert() {
        if (mJobManager != null) {
            mJobManager.cancelTask();
        }
        EngineNetApi.cancelTask(mToken);
        if (!CommonUtils.isEmpty(mDownloadManagerSet)) {
            for (DownloadManager downloadManager : mDownloadManagerSet) {
                downloadManager.cancelAllDownload();
            }
        }
        ThreadUtils.getIoPool().execute(new Runnable() {
            @Override
            public void run() {
                if (mConvertParam != null && mConvertParam.paramMap != null && !mConvertParam.paramMap.isEmpty()) {
                    Set<Map.Entry<String, ConvertParam.Param>> entries = mConvertParam.paramMap.entrySet();
                    for (Map.Entry<String, ConvertParam.Param> entry : entries) {
                        FileUtils.delete(entry.getValue().dstFile);
                    }
                    mConvertParam = null;
                }
                ThreadUtils.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (convertFileObservable != null) {
                            convertFileObservable.onConvertCancel();
                        }
                    }
                });
            }
        });
    }


    /**
     * 注册app进入后台的监听
     * register background observer
     */
    @Override
    public void registerConvertFileObserver(ConvertFileObserver observer) {
        if (observer != null) {
            convertFileObservable.registerObserver(observer);
        }
    }

    /**
     * 注销app进入后台的监听
     * unregister background observer
     */
    @Override
    public void unregisterConvertFileObserver(ConvertFileObserver observer) {
        mConvertParam = null;
        if (observer != null) {
            try {
                convertFileObservable.unregisterObserver(observer);
            } catch (Exception e) { }
        }
    }

    private void handleConvertResult(final boolean success) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (convertFileObservable != null) {
                    convertFileObservable.onConvertFinish(mConvertParam, success);
                }
            }
        });
    }

    public interface EventListener {

        /**
         * On convert finish.
         * 转码结束
         *
         * @param convertParam   the convert param 转码后的参数
         * @param convertSuccess the convert success 是否转码成功
         */
        void onConvertFinish(ConvertParam convertParam, boolean convertSuccess);
    }
}
