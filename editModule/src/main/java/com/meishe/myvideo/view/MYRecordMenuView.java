package com.meishe.myvideo.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.meishe.myvideo.R;
import com.meishe.myvideo.interfaces.BottomEventListener;

/**
 * 录制View 包括录制按钮
 * Record View includes a record button
 */
public class MYRecordMenuView extends FrameLayout {

    private ImageView mIvRecord;
    private TextView mTvRecordContent;
    private ImageView mIvRecordConfirm;

    public MYRecordMenuView(Context context) {
        this(context, null);
    }


    public MYRecordMenuView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MYRecordMenuView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        LayoutInflater layoutInflater = LayoutInflater.from(context);
        View view = layoutInflater.inflate(R.layout.record_view, this);
        mIvRecord = view.findViewById(R.id.iv_record);
        mTvRecordContent = view.findViewById(R.id.tv_start_record);
        mIvRecordConfirm = view.findViewById(R.id.iv_record_confirm);
        initListener();
    }

    @SuppressLint("ClickableViewAccessibility")
    private void initListener() {
        mIvRecord.setOnLongClickListener(new OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                mIvRecord.setSelected(true);
                mTvRecordContent.setText(getResources().getString(R.string.voice_recording));
                if (mOnRecordListener != null) {
                    mOnRecordListener.onStartRecord();
                }
                return false;
            }
        });

        mIvRecord.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_UP) {
                    // 放开处理 Let go of the handle
                    mIvRecord.setSelected(false);
                    mTvRecordContent.setText(getResources().getText(R.string.start_record));
                    if (mOnRecordListener != null) {
                        mOnRecordListener.onStopRecord();
                    }
                }
                return false;
            }
        });

        mIvRecordConfirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnRecordListener != null) {
                    mOnRecordListener.onDismiss(true);
                }
            }
        });

    }

    /**
     * Stop record.
     * 停止记录
     */
    public void stopRecord() {
        if (mOnRecordListener != null) {
            mOnRecordListener.onStopRecord();
        }
        if (mIvRecord != null) {
            mIvRecord.setSelected(false);
        }
    }

    private OnRecordListener mOnRecordListener;

    /**
     * 设置事件监听
     * Sets the listener
     *
     * @param listener the listener
     */
    public void setListener(OnRecordListener listener) {
        this.mOnRecordListener = listener;
    }

    /**
     * The type On record listener.
     * 记录监听类
     */
    public static abstract class OnRecordListener extends BottomEventListener {
        /**
         * On start record.
         * 开始记录
         */
        public abstract void onStartRecord();

        /**
         * On stop record.
         * 停止记录
         */
        public abstract void onStopRecord();
    }
}
