package com.deep.foresight.libspi;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业务数据提供者管理器
 * 用于管理和获取业务层提供的数据
 */
public class BusinessDataProviderMgr {
    
    private static volatile BusinessDataProviderMgr instance;
    
    // 存储注册的业务数据提供者
    private final Map<String, IBusinessDataProvider> providerMap = new HashMap<>();
    // 存储每种数据类型的监听器
    private Map<String, List<IDataChangeListener>> dataListeners = new HashMap<>();
    private BusinessDataProviderMgr() {
        // 私有构造函数
    }
    
    /**
     * 获取单例实例
     * @return BusinessDataProviderMgr实例
     */
    public static BusinessDataProviderMgr getInstance() {
        if (instance == null) {
            synchronized (BusinessDataProviderMgr.class) {
                if (instance == null) {
                    instance = new BusinessDataProviderMgr();
                }
            }
        }
        return instance;
    }
    
    /**
     * 注册业务数据提供者
     * @param key 提供者标识
     * @param provider 业务数据提供者
     */
    public void registerProvider(String key, IBusinessDataProvider provider) {
        if (key != null && provider != null) {
            providerMap.put(key, provider);
        }
    }
    
    /**
     * 注销业务数据提供者
     * @param key 提供者标识
     */
    public void unregisterProvider(String key) {
        if (key != null) {
            providerMap.remove(key);
        }
    }

    public void registerDataListener(String key, IDataChangeListener listener) {
        if (key != null && listener != null) {
            List<IDataChangeListener> listeners = dataListeners.get(key);
            if (listeners == null) {
                listeners = new ArrayList<>();
                dataListeners.put(key, listeners);
            }
            listeners.add(listener);
        }
    }

    public void unregisterDataListener(String key, IDataChangeListener listener) {
        if (key != null && listener != null) {
            List<IDataChangeListener> listeners = dataListeners.get(key);
            if (listeners != null) {
                listeners.remove(listener);
            }
        }
    }

    public void notifyDataChanged(String key,Object data) {
        for (IDataChangeListener iDataChangeListener : dataListeners.get(key)) {
            iDataChangeListener.onDataChanged(data);
        }
    }

    public IBusinessDataProvider getProvider(String key) {
        return providerMap.get(key);
    }

    /**
     * 检查是否有注册的业务数据提供者
     * @return 是否有注册的提供者
     */
    public boolean hasProviders() {
        return !providerMap.isEmpty();
    }
}