package com.meishe.engine;

import android.graphics.Bitmap;
import android.graphics.Point;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsAudioClip;
import com.meicam.sdk.NvsMediaFileConvertor;
import com.meicam.sdk.NvsObject;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsVideoClip;
import com.meicam.sdk.NvsVideoFx;
import com.meicam.sdk.NvsVideoResolution;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FileIOUtils;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.FormatUtils;
import com.meishe.base.utils.GsonUtils;
import com.meishe.base.utils.ImageUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ResourceUtils;
import com.meishe.base.utils.StringUtils;
import com.meishe.base.utils.ThreadUtils;
import com.meishe.base.utils.Utils;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.bean.TemplateUploadParam;
import com.meishe.engine.bean.AnimationData;
import com.meishe.engine.bean.ClipInfo;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.MeicamAudioClip;
import com.meishe.engine.bean.MeicamAudioTrack;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.engine.bean.MeicamCompoundCaptionClip;
import com.meishe.engine.bean.MeicamFxParam;
import com.meishe.engine.bean.MeicamStickerCaptionTrack;
import com.meishe.engine.bean.MeicamStickerClip;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamTimelineVideoFilterAndAdjustClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxTrack;
import com.meishe.engine.bean.MeicamTransition;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoFx;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.engine.bean.MeicamWaterMark;
import com.meishe.engine.bean.StickerAnimation;
import com.meishe.engine.bean.bridges.FileInfoBridge;
import com.meishe.engine.bean.template.ExportTemplateClip;
import com.meishe.engine.bean.template.ExportTemplateDescInfo;
import com.meishe.engine.bean.template.ExportTemplateSection;
import com.meishe.engine.bean.template.TemplateInfo;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.observer.DownLoadObserver;
import com.meishe.engine.observer.EngineCallbackObserver;
import com.meishe.engine.util.PathUtils;
import com.meishe.engine.util.RatioUtil;
import com.meishe.engine.util.TimelineUtil;
import com.meishe.engine.util.WhiteList;
import com.meishe.logic.bean.SettingParameter;
import com.meishe.logic.manager.PreferencesManager;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_ALPHA;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_SEGMENT;
import static com.meishe.engine.bean.template.ExportTemplateDescInfo.TYPE_FOOTAGE_INTERNAL;
import static com.meishe.engine.bean.template.ExportTemplateDescInfo.TYPE_FOOTAGE_M3U8;
import static com.meishe.engine.bean.template.ExportTemplateDescInfo.TYPE_FOOTAGE_ORIGINAL;
import static com.meishe.engine.constant.NvsConstants.AspectRatio.AspectRatio_NoFitRatio;
import static com.meishe.engine.constant.NvsConstants.INVERSE_SEGMENT;
import static com.meishe.engine.constant.NvsConstants.KEY_BACKGROUND_MODE;
import static com.meishe.engine.constant.NvsConstants.SEGMENTATION;
import static com.meishe.engine.constant.NvsConstants.SET_ALPHA;
import static com.meishe.engine.constant.NvsConstants.TYPE_RAW_BUILTIN;
import static com.meishe.engine.constant.NvsConstants.VALUE_IMAGE_BACKGROUND_MODE;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/7/16 18:43
 * @Description :导出模板工具类 Manager for exporting template.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class ExportTemplateManager {
    private static final int FOOTAGE_ID_BASE_NUMBER = 1000;
    private final int FOOTAGE_ID_AUDIO_BASE_NUMBER = 2000;
    private static final int MAX_TRACK_COUNT = 30;

    private int PROGRESS_COMPILE_TOTAL = 100;
    private final String FILE_TEMPLATE_INFO_DESC = "info.json";

    private NvsStreamingContext mStreamingContext;
    private MeicamTimeline mTimeline;
    private String mUuid;
    private int mRatio;
    private List<ExportTemplateClip> mTemplateClipList;
    private List<ExportTemplateClip> mLockTemplateClipList;
    private String mRootResourceFilePath;
    private String mVideoSavePath;
    private String mGenerateTemplateFileFolder;
    private String mTemplateName, mTemplateDesc, mCoverPath;
    private TemplateUploadParam mUploadParam;
    private int mFootageIdNumber;
    private int mInnerAssetTotalCount;
    private int mAudioBaseFootageId = FOOTAGE_ID_AUDIO_BASE_NUMBER;

    private static final int TYPE_TRANSITION = 1;
    private static final int TYPE_ANIMATION = 2;
    private static final int TYPE_FILTER = 3;
    private static final int TYPE_EFFECT = 4;
    private static final int TYPE_ANIMATED_STICKER = 5;
    private static final int TYPE_CAPTION_ANIMATION_IN = 6;
    private static final int TYPE_CAPTION_ANIMATION_OUT = 7;
    private static final int TYPE_CAPTION_ANIMATION_COMBINATION = 8;
    private static final int TYPE_CAPTION_BUBBLE = 9;
    private static final int TYPE_CAPTION_FLOWER = 10;
    private static final int TYPE_COMPOUND_CAPTION = 11;
    private static final int TYPE_ANIMATED_STICKER_ANIMATION_IN = 12;
    private static final int TYPE_ANIMATED_STICKER_ANIMATION_OUT = 13;
    private static final int TYPE_ANIMATED_STICKER_ANIMATION_COMP = 14;
    private static final int TYPE_SHAPE = 15;
    private static final int TYPE_CAPTION_STYLE = 16;
    private OnExportListener mOnExportListener;
    private ExportTask mExportTask;
    private List<AssetInfo> mAssetInfos;
    private boolean needPlaceValue;
    private String mAspectRatioStr;

    public void setOnExportListener(OnExportListener listener) {
        this.mOnExportListener = listener;
    }

    public ExportTemplateManager() {
    }

    private boolean initBaseData(MeicamTimeline timeline, NvsStreamingContext streamingContext, List<ExportTemplateSection> templateSectionList) {
        mTimeline = timeline;
        mStreamingContext = streamingContext;
        if (mTimeline == null || mStreamingContext == null) {
            LogUtils.e("timeline or StreamingContext is null !!!");
            return false;
        }
        mRatio = mTimeline.getMakeRatio();
        if (mRatio == AspectRatio_NoFitRatio) {
            mRatio = getTemplateAspectRatio();
        }
        if (mRatio != AspectRatio_NoFitRatio) {
            Point point = TimelineUtil.calculateTimelineSize(mRatio);
            mTimeline.changeVideoSize(point.x, point.y);
        }
        NvsVideoResolution videoResolution = mTimeline.getVideoResolution();
        //String defaultValue = videoResolution.imageWidth+":"+videoResolution.imageHeight;
        mAspectRatioStr = RatioUtil.getAspectRatioStr(mRatio, "16:9");
        if (!TextUtils.isEmpty(mAspectRatioStr)) {
            mAspectRatioStr = mAspectRatioStr.replace(RatioUtil.COLON_TAG, RatioUtil.V_TAG);
        }
        if (mInnerAssets != null) {
            mInnerAssets.setAspectRatio(mAspectRatioStr);
        }
        if (mFootageInfos != null) {
            mFootageInfos.setAspectRatio(mAspectRatioStr);
        }
        mTemplateClipList = new ArrayList<>();
        mLockTemplateClipList = new ArrayList<>();
        for (ExportTemplateSection templateSection : templateSectionList) {
            if (templateSection == null || templateSection.isHeader) {
                continue;
            }
            ExportTemplateClip exportTemplateClip = templateSection.t;
            if (exportTemplateClip != null) {
                mTemplateClipList.add(exportTemplateClip);
            }
        }
        return true;
    }

    /**
     * Gets template aspect ratio.
     * 获取模板近似比例
     *
     * @return the template aspect ratio
     */
    private int getTemplateAspectRatio() {
        NvsVideoResolution videoResolution = EditorEngine.getInstance().getVideoResolution();
        float ratio = videoResolution.imageWidth * 1.0F / videoResolution.imageHeight;
        return CommonData.AspectRatio.getTemplateAspect(ratio);
    }

    /**
     * 导出模板
     * Export template
     *
     * @param timeline         the timeline 时间线
     * @param streamingContext the streamingContext 流媒体上下午
     * @param templateName     the template name 模板名称
     * @param templateDesc     the template description 模板描述
     * @param templateDesc     the list of template section 导出模板部件列表
     */
    private boolean isSampleTemplate = false;
    private ExportTemplateDescInfo.InnerAssetWrapper mInnerAssets;
    private ExportTemplateDescInfo.FootageInfoWrapper mFootageInfos;

    public boolean exportSampleTemplate(MeicamTimeline timeline, NvsStreamingContext streamingContext,
                                        final String templateName, final String templateDesc, String templateUuid, List<ExportTemplateDescInfo.FootageInfo> internal) {
        isSampleTemplate = true;
        needPlaceValue = true;
        this.mInnerAssets = new ExportTemplateDescInfo.InnerAssetWrapper();
        this.mFootageInfos = new ExportTemplateDescInfo.FootageInfoWrapper();
        if (!CommonUtils.isEmpty(internal)) {
            mFootageInfos.getInfos().addAll(internal);
        }
        List<ExportTemplateSection> templateSectionList = getTemplateSectionList(timeline);
        if (!initBaseData(timeline, streamingContext, templateSectionList)) {
            return false;
        }
        mUuid = templateUuid;
        mGenerateTemplateFileFolder = PathUtils.getGenerateCloudDraftFileFolder(mUuid);
        mTemplateName = templateName;
        mTemplateDesc = templateDesc;
        mExportTask = new TaskBuilder()
                .addTask(new Initiation())
                .addTask(new ConvertMediaFile())
                .addTask(new ResourceInfoPrepareTask())
                .addTask(new ExportTemplate()).build();
        mExportTask.execute();
        return true;
    }

    public boolean exportTemplateInfo(MeicamTimeline timeline, NvsStreamingContext streamingContext, String templateUuid) {
        isSampleTemplate = true;
        needPlaceValue = false;
        this.mInnerAssets = new ExportTemplateDescInfo.InnerAssetWrapper();
        this.mFootageInfos = new ExportTemplateDescInfo.FootageInfoWrapper();
        List<ExportTemplateSection> templateSectionList = getTemplateSectionList(timeline);
        if (!initBaseData(timeline, streamingContext, templateSectionList)) {
            return false;
        }
        mUuid = templateUuid;
        mGenerateTemplateFileFolder = PathUtils.getGenerateCloudDraftFileFolder(mUuid);
        mExportTask = new TaskBuilder()
                .addTask(new Initiation())
                .addTask(new ConvertMediaFile())
                .addTask(new ResourceInfoPrepareTask())
                .addTask(new FinishTask())
                .build();
        mExportTask.execute();
        return true;
    }

    /**
     * 导出模板
     * Export template
     *
     * @param timeline         the timeline 时间线
     * @param streamingContext the streamingContext 流媒体上下午
     * @param templateName     the template name 模板名称
     * @param templateDesc     the template description 模板描述
     * @param templateDesc     the list of template section 导出模板部件列表
     */
    public boolean exportTemplate(MeicamTimeline timeline, NvsStreamingContext streamingContext, final String templateName, final String templateDesc, List<ExportTemplateSection> templateSectionList) {
        if (TextUtils.isEmpty(templateName) || TextUtils.isEmpty(templateDesc)) {
            return false;
        }
        if (!initBaseData(timeline, streamingContext, templateSectionList)) {
            return false;
        }
        mUuid = UUID.randomUUID().toString().toUpperCase();
        mGenerateTemplateFileFolder = PathUtils.getGenerateTemplateFileFolder(mUuid);
        mTemplateName = templateName;
        mTemplateDesc = templateDesc;

        mExportTask = new TaskBuilder()
                .addTask(new Initiation())
                .addTask(new SaveCover())
                .addTask(new DownLoadRemoteTask())
                .addTask(new CompileTask())
                .addTask(new ConvertMediaFile())
                .addTask(new ResourceInfoPrepareTask())
                .addTask(new ExportTemplate()).build();
        mExportTask.execute();
        return true;
    }

    private List<ExportTemplateSection> getTemplateSectionList(MeicamTimeline timeline) {
        int footageId = 0;
        List<ExportTemplateSection> templateSectionList = new ArrayList<>();
        int trackCount = timeline.videoTrackCount();
        for (int i = 0; i < trackCount; i++) {
            MeicamVideoTrack meicamVideoTrack = timeline.getVideoTrack(i);
            for (int j = 0; j < meicamVideoTrack.getClipCount(); j++) {
                MeicamVideoClip videoClip = meicamVideoTrack.getVideoClip(j);
                if (videoClip == null || videoClip.getVideoType().equals(CommonData.CLIP_HOLDER)) {
                    continue;
                }
                ExportTemplateClip exportTemplateClip = new ExportTemplateClip();
                exportTemplateClip.setClipName(String.format(StringUtils.getString(R.string.export_template_clip_param), j + 1));
                long clipDuration = videoClip.getOutPoint() - videoClip.getInPoint();
                exportTemplateClip.setClipDuration(FormatUtils.microsecond2Time(clipDuration));
                exportTemplateClip.setImagePath(videoClip.getFilePath());
                exportTemplateClip.setFileType(videoClip.getVideoType());
                exportTemplateClip.setFootageType(ExportTemplateClip.TYPE_FOOTAGE_IMAGE_AND_VIDEO);
                exportTemplateClip.setFootageGroupsId(0);
                exportTemplateClip.setFootageId(footageId);
                exportTemplateClip.setVideoReverse(videoClip.getVideoReverse());
                exportTemplateClip.setReversePath(videoClip.getReverseFilePath());
                exportTemplateClip.setLock(false);
                exportTemplateClip.setInPoint(videoClip.getInPoint());
                exportTemplateClip.setOutPoint(videoClip.getOutPoint());
                exportTemplateClip.setTrimIn(videoClip.getTrimIn());
                exportTemplateClip.setTrimOut(videoClip.getTrimOut());
                exportTemplateClip.setTrackIndex(i);
                ExportTemplateSection exportTemplateSectionContent = new ExportTemplateSection(exportTemplateClip);
                templateSectionList.add(exportTemplateSectionContent);
                footageId++;
            }
        }
        return templateSectionList;
    }

    /**
     * 设置模板附件
     * Set template attachment
     */
    private void setTemplateAttachment( ) {
        mLockTemplateClipList.clear();
        mFootageIdNumber = 0;
        for (int trackIndex = 0; trackIndex < mTimeline.videoTrackCount(); trackIndex++) {
            MeicamVideoTrack videoTrack = mTimeline.getVideoTrack(trackIndex);
            if (trackIndex >= MAX_TRACK_COUNT) {
                break;
            }
            if (videoTrack == null) {
                continue;
            }
            if (isSampleTemplate) {
                int lastIndex = videoTrack.getClipCount() - 1;
                MeicamVideoClip lastClip = videoTrack.getVideoClip(lastIndex);
                if (CommonData.CLIP_HOLDER.equals(lastClip.getVideoType())){
                    videoTrack.removeVideoClip(lastIndex, false);
                }
            }
            for (int j = 0; j < videoTrack.getClipCount(); j++) {
                MeicamVideoClip videoClip = videoTrack.getVideoClip(j);
                ExportTemplateClip templateClip = findExportTemplateClip(trackIndex, videoClip.getInPoint());
                if (templateClip != null) {
                    if (templateClip.isLock()) {
                        mLockTemplateClipList.add(templateClip);
                        String imagePath = templateClip.getImagePath();
                        File file = new File(imagePath);
                        if (file.exists() || FileUtils.isAndroidQUriPath(imagePath)) {
                            videoClip.setTemplateAttachment(NvsVideoClip.TEMPLATE_KEY_FOOTAGE_NAME, FileUtils.getFileName(imagePath));
                        }
                    }
                    String footageId = "footage" + (templateClip.getFootageGroupsId() == 0 ?
                            templateClip.getFootageId() : templateClip.getFootageGroupsId() * FOOTAGE_ID_BASE_NUMBER);
                    videoClip.setTemplateAttachment(NvsVideoClip.TEMPLATE_KEY_FOOTAGE_ID, footageId);
                    videoClip.setTemplateAttachment(NvsVideoClip.TEMPLATE_KEY_FOOTAGE_TYPE, templateClip.getFootageType());
                    videoClip.setTemplateAttachment(NvsVideoClip.TEMPLATE_KEY_FOOTAGE_CAN_REPLACE, templateClip.isLock() || isSampleTemplate ? "false" : "true");
                    videoClip.setTemplateAttachment(NvsAudioClip.TEMPLATE_KEY_FOOTAGE_NEED_REVERSE, videoClip.getVideoReverse() + "");
                    if (mFootageInfos != null) {
                        String remotePath = videoClip.getRemotePath();
                        String path = videoClip.isReverse() ? videoClip.getReverseFilePath() : TextUtils.isEmpty(remotePath) ? videoClip.getFilePath() : remotePath;
                        String footageName = null;
                        FileInfoBridge.FileInfo fileInfo = FileInfoBridge.getFileInfo(path);
                        if (fileInfo != null) {
                            footageName = fileInfo.remotePath;
                            if (CommonData.CLIP_IMAGE.equals(videoClip.getVideoType())) {
                                String[] split = fileInfo.remotePath.split("/");
                                footageName = split[split.length - 1];
                            }
                        }
                        if (needPlaceValue && isSampleTemplate) {
                            if (fileInfo != null) {
                                videoClip.changeFilePath(fileInfo.filePath);
                                videoClip.setTemplateAttachment(NvsObject.TEMPLATE_KEY_FOOTAGE_M3U8_NAME, fileInfo.filePath);
                                videoClip.setTemplateAttachment(NvsObject.TEMPLATE_KEY_FOOTAGE_NAME, footageName);
                            }
                        }
                        MeicamVideoFx meicamVideoFx = videoClip.getVideoFx(SUB_TYPE_ALPHA, SET_ALPHA);
                        if (meicamVideoFx != null) {
                            //替换Set Alpha特技为segmentation特技
                            //Replace the Set Alpha effect to the Segmentation effect
                            videoClip.removeVideoFx(meicamVideoFx);
                            final MeicamVideoFx newVideoFx = videoClip.appendVideoFx(TYPE_RAW_BUILTIN, SUB_TYPE_SEGMENT, SEGMENTATION, false);
                            if (newVideoFx != null) {
                                newVideoFx.setBooleanVal(INVERSE_SEGMENT, true);
                            }
                        }
                        ExportTemplateDescInfo.FootageInfo footageInfo = new ExportTemplateDescInfo.FootageInfo(footageId, path, getFileType(path));
                        footageInfo.extraData = footageName;
                        mFootageInfos.getInfos().add(footageInfo);
                    }
                    mFootageIdNumber++;
                }
            }
        }

        MeicamVideoTrack mainTrack = mTimeline.getVideoTrack(CommonData.MAIN_TRACK_INDEX);
        int lastIndex = mainTrack.getClipCount() - 1;
        MeicamVideoClip videoClip = mainTrack.getVideoClip(lastIndex);
        //如果是简易模板，补黑不能锁定
        //If it is a simple template, the black patch cannot be locked
        if (CommonData.CLIP_HOLDER.equals(videoClip.getVideoType()) && (!isSampleTemplate)) {
            ExportTemplateClip exportTemplateClip = new ExportTemplateClip();
            long clipDuration = videoClip.getOutPoint() - videoClip.getInPoint();
            exportTemplateClip.setClipDuration(FormatUtils.microsecond2Time(clipDuration));
            exportTemplateClip.setImagePath(videoClip.getFilePath());
            exportTemplateClip.setFileType(videoClip.getVideoType());
            exportTemplateClip.setFootageType(ExportTemplateClip.TYPE_FOOTAGE_IMAGE);
            exportTemplateClip.setFootageGroupsId(0);
            exportTemplateClip.setFootageId(mFootageIdNumber);
            exportTemplateClip.setVideoReverse(videoClip.getVideoReverse());
            exportTemplateClip.setReversePath(videoClip.getReverseFilePath());
            exportTemplateClip.setLock(true);
            exportTemplateClip.setInPoint(videoClip.getInPoint());
            exportTemplateClip.setOutPoint(videoClip.getOutPoint());
            exportTemplateClip.setTrimIn(videoClip.getTrimIn());
            exportTemplateClip.setTrimOut(videoClip.getTrimOut());
            exportTemplateClip.setTrackIndex(videoClip.getTrackIndex());

            mLockTemplateClipList.add(exportTemplateClip);
            String imagePath = videoClip.getFilePath();
            File file = new File(imagePath);
            if (imagePath.startsWith("assets") || file.exists() || FileUtils.isAndroidQUriPath(imagePath)) {
                videoClip.setTemplateAttachment(NvsVideoClip.TEMPLATE_KEY_FOOTAGE_NAME, FileUtils.getFileName(imagePath));
            }

            videoClip.setTemplateAttachment(NvsVideoClip.TEMPLATE_KEY_FOOTAGE_ID, "footage" + (exportTemplateClip.getFootageGroupsId() == 0 ?
                    exportTemplateClip.getFootageId() : exportTemplateClip.getFootageGroupsId() * FOOTAGE_ID_BASE_NUMBER));
            videoClip.setTemplateAttachment(NvsVideoClip.TEMPLATE_KEY_FOOTAGE_TYPE, exportTemplateClip.getFootageType());
            videoClip.setTemplateAttachment(NvsVideoClip.TEMPLATE_KEY_FOOTAGE_CAN_REPLACE, "false");
            videoClip.setTemplateAttachment(NvsAudioClip.TEMPLATE_KEY_FOOTAGE_NEED_REVERSE, videoClip.getVideoReverse() + "");
            mFootageIdNumber++;
        }
        LogUtils.d("footageIdNumber = " + mFootageIdNumber);

        for (int trackIndex = 0; trackIndex < mTimeline.getAudioTrackCount(); trackIndex++) {
            MeicamAudioTrack audioTrack = mTimeline.getAudioTrack(trackIndex);
            if (trackIndex >= MAX_TRACK_COUNT) {
                break;
            }
            if (audioTrack == null) {
                continue;
            }
            for (int j = 0; j < audioTrack.getClipCount(); j++) {
                MeicamAudioClip audioClip = audioTrack.getAudioClip(j);
                String audioPath = audioClip.getFilePath();
                File file = new File(audioPath);
                if (file.exists() || FileUtils.isAndroidQUriPath(audioPath)) {
                    audioClip.setTemplateAttachment(NvsAudioClip.TEMPLATE_KEY_FOOTAGE_NAME, FileUtils.getFileName(audioPath));
                }
                audioClip.setTemplateAttachment(NvsAudioClip.TEMPLATE_KEY_FOOTAGE_TYPE, "audio");
                audioClip.setTemplateAttachment(NvsAudioClip.TEMPLATE_KEY_FOOTAGE_NEED_REVERSE, "false");
                String footageId = "footage" + (mAudioBaseFootageId++);
                audioClip.setTemplateAttachment(NvsAudioClip.TEMPLATE_KEY_FOOTAGE_ID, footageId);
                audioClip.setTemplateAttachment(NvsAudioClip.TEMPLATE_KEY_FOOTAGE_TAGS, StringUtils.getString(R.string.export_template_audio));

                if (mFootageInfos != null) {
                    String remotePath = audioClip.getRemotePath();
                    String path = TextUtils.isEmpty(remotePath) ? audioClip.getFilePath() : remotePath;
                    String footageName = null;
                    FileInfoBridge.FileInfo fileInfo = FileInfoBridge.getFileInfo(path);
                    if (fileInfo != null) {
                        footageName = fileInfo.remotePath;
                    }
                    if (needPlaceValue && isSampleTemplate) {
                        if (fileInfo != null) {
                            audioClip.setFilePath(fileInfo.filePath);
                            audioClip.setTemplateAttachment(NvsObject.TEMPLATE_KEY_FOOTAGE_M3U8_NAME, fileInfo.filePath);
                            audioClip.setTemplateAttachment(NvsObject.TEMPLATE_KEY_FOOTAGE_NAME, footageName);
                        }
                    }
                    mFootageInfos.getInfos().add(new ExportTemplateDescInfo.FootageInfo(footageId, path, getFileType(path)));
                }
            }
        }
        int tempIndex = 0;
        for (int i = 0; i < mTimeline.getStickerCaptionTrackCount(); i++) {
            MeicamStickerCaptionTrack meicamStickerCaptionTrack = mTimeline.findStickCaptionTrack(i);
            if (meicamStickerCaptionTrack == null) {
                continue;
            }
            for (int j = 0; j < meicamStickerCaptionTrack.getClipCount(); j++) {
                ClipInfo<?> clipInfo = meicamStickerCaptionTrack.getCaptionStickerClip(j);
                if (clipInfo == null) {
                    continue;
                }
                if (clipInfo instanceof MeicamCaptionClip) {
                    MeicamCaptionClip captionClip = (MeicamCaptionClip) clipInfo;
                    captionClip.setTemplateAttachment(NvsVideoClip.TEMPLATE_KEY_REPLACE_ID, String.valueOf(tempIndex));
                    tempIndex++;
                } else if (clipInfo instanceof MeicamCompoundCaptionClip) {
                    MeicamCompoundCaptionClip captionClip = (MeicamCompoundCaptionClip) clipInfo;
                    captionClip.setTemplateAttachment(NvsVideoClip.TEMPLATE_KEY_REPLACE_ID, String.valueOf(tempIndex));
                    tempIndex++;
                }
            }
        }
        if (isSampleTemplate && !needPlaceValue) {
            findOtherInnerFilePath();
        }
    }


    /**
     * Change clip trim.
     * 修改clip的trim
     */
    public void changeClipTrim() {
        int tempIndex = 0;
        for (int i = 0; i < mTimeline.videoTrackCount(); i++) {
            MeicamVideoTrack videoTrack = mTimeline.getVideoTrack(i);
            if (tempIndex >= MAX_TRACK_COUNT) {
                break;
            }
            if (videoTrack == null) {
                continue;
            }
            for (int j = 0; j < videoTrack.getClipCount(); j++) {
                MeicamVideoClip videoClip = videoTrack.getVideoClip(j);
                ExportTemplateClip templateClip = findExportTemplateClip(tempIndex, videoClip.getInPoint());
                if (templateClip != null) {
                    videoClip.setTrimOut(templateClip.getTrimOutByUser(), true);
                    videoClip.setTrimIn(templateClip.getTrimInByUser(), true);
                }
            }
            tempIndex++;
        }
    }

    /**
     * restore clip trim.
     * 恢复clip的trim
     */
    public void restoreClipTrim( ) {
        int tempIndex = 0;
        for (int i = 0; i < mTimeline.videoTrackCount(); i++) {
            MeicamVideoTrack videoTrack = mTimeline.getVideoTrack(i);
            if (tempIndex >= MAX_TRACK_COUNT) {
                break;
            }
            if (videoTrack == null) {
                continue;
            }
            for (int j = 0; j < videoTrack.getClipCount(); j++) {
                MeicamVideoClip videoClip = videoTrack.getVideoClip(j);
                ExportTemplateClip templateClip = findExportTemplateClip(tempIndex, videoClip.getInPoint());
                if (templateClip != null) {
                    videoClip.setTrimOut(templateClip.getTrimOut(), i == 0);
                    videoClip.setTrimIn(templateClip.getTrimIn(), i == 0);
                }
            }
            tempIndex++;
        }
    }

    private String getFileType(String path) {
        if (!TextUtils.isEmpty(path) && path.toLowerCase().endsWith(TYPE_FOOTAGE_M3U8)) {

            return TYPE_FOOTAGE_M3U8;
        }
        return TYPE_FOOTAGE_ORIGINAL;
    }

    /**
     * 根据索引和入点查找模板片段
     * Find template clip by index and entry point
     *
     * @param trackIndex the track index 索引
     * @param inPoint    the in point 入点
     * @return the template clip
     */
    private ExportTemplateClip findExportTemplateClip(int trackIndex, long inPoint) {
        if (mTemplateClipList != null) {
            for (ExportTemplateClip templateClip : mTemplateClipList) {
                if (templateClip != null && templateClip.getInPoint() == inPoint && templateClip.getTrackIndex() == trackIndex) {
                    return templateClip;
                }
            }
        }
        return null;
    }

    /**
     * 导出模板
     * Export template
     *
     * @param templateName the template name 模板名称
     * @param templateDesc the template description 模板描述
     */
    private void prepareResourceInfo(final String templateName, final String templateDesc, final String coverPath,  List<AssetInfo> assetInfos) {
        File file = new File(mRootResourceFilePath + File.separator + FILE_TEMPLATE_INFO_DESC);
        if (file.exists()) {
            boolean delete = file.delete();
            LogUtils.d("delete=" + delete);
        }
        int innerAssetTotalCount = 0;
        if (!isSampleTemplate) {
            try {
                innerAssetTotalCount = copyResource(assetInfos);
            } catch (Exception e) {
                LogUtils.e(e);
            }
            saveAudioResource();
            saveWaterMakerResource();
            saveStickerResource();
            saveBackgroundResource();
            saveBuildInResource();
            saveLockedImageFle();
        }

        /*生成模板的时候的info.json
         * The info.json when generating templates.
         * */
        FileIOUtils.writeFileFromString(file
                , getInfoDesc(StringUtils.getString(R.string.template_default_creator), mUuid
                        , mAspectRatioStr, mTimeline.getDuration()
                        , mFootageIdNumber, innerAssetTotalCount, templateName, templateDesc, coverPath));

        //将描述文件输出到指定的目录 Output the description file to the specified directory.
        String templateDescFilePath = mGenerateTemplateFileFolder + File.separator + FILE_TEMPLATE_INFO_DESC;
        FileUtils.createOrExistsFile(templateDescFilePath);
        file = new File(templateDescFilePath);
        if (!file.exists()) {
            return;
        }
        String templatePath;
        if (isSampleTemplate) {
            templatePath = mGenerateTemplateFileFolder + File.separator + mUuid + ".project";
        } else {
            templatePath = mGenerateTemplateFileFolder + File.separator + mUuid + ".template";
        }
        FileIOUtils.writeFileFromString(file, getSimpleInfoDesc(mUuid,
                mAspectRatioStr,
                mTimeline.getDuration(),
                mFootageIdNumber, mInnerAssetTotalCount, templateName, templateDesc,
                coverPath, templatePath, mVideoSavePath, getMinSdkVersion(assetInfos), mInnerAssets, mFootageInfos));
        mUploadParam = new TemplateUploadParam();
        try {
            if (!TextUtils.isEmpty(templatePath)) {
                mUploadParam.materialFile = new File(templatePath);
            }
            if (!TextUtils.isEmpty(coverPath)) {
                mUploadParam.coverFile = new File(coverPath);
            }
            if (!TextUtils.isEmpty(mVideoSavePath)) {
                mUploadParam.previewVideoFile = new File(mVideoSavePath);
            }
            mUploadParam.templateDescFilePath = templateDescFilePath;
        } catch (Exception e) {
            LogUtils.e(e);
        }
        if (Utils.isZh()) {
            mUploadParam.descriptinZhCn = templateDesc;
        } else {
            mUploadParam.description = templateDesc;
        }
        mUploadParam.customDisplayName = templateName;
        assetsTypeMap.clear();
    }

    private String getMinSdkVersion(List<AssetInfo> assetInfos) {
        if (CommonUtils.isEmpty(assetInfos)) {
            return "1.0.0";
        }
        String minSdkVersion = "1.0.0";
        for (AssetInfo assetInfo : assetInfos) {
            String sdkVersion = assetInfo.getMinAppVersion();
            if (isBig(sdkVersion, minSdkVersion)) {
                minSdkVersion = sdkVersion;
            }
        }
        return minSdkVersion;
    }

    private boolean isBig(String beforesdkVersion, String lastSdkVersion) {
        if (TextUtils.isEmpty(beforesdkVersion)) {
            return false;
        }
        String[] beforeSplit = beforesdkVersion.split("\\.");
        String[] lastSplit = lastSdkVersion.split("\\.");
        if (beforeSplit.length < 3 || lastSplit.length < 3) {
            return false;
        }
        return Integer.parseInt(beforeSplit[0]) > Integer.parseInt(lastSplit[0])
                || Integer.parseInt(beforeSplit[1]) > Integer.parseInt(lastSplit[1])
                || Integer.parseInt(beforeSplit[2]) > Integer.parseInt(lastSplit[2]);
    }

    private void getInnerAssetsInfo(List<AssetInfo> assetInfos) {
        if (mInnerAssets == null || CommonUtils.isEmpty(assetInfos)) {
            return;
        }
        for (AssetInfo assetInfo : assetInfos) {
            mInnerAssets.getAssets().add(new ExportTemplateDescInfo.InnerAsset(assetInfo.getPackageId(), String.valueOf(assetInfo.getVersion()),
                    FileUtils.getFileSuffix(assetInfo.getAssetPath())));
        }
    }

    private List<AssetInfo> getAssetsInfo(Map<Integer, Set<String>> packageInfo) {
        if (packageInfo == null || packageInfo.isEmpty()) {
            return null;
        }
        List<AssetInfo> result = new ArrayList<>();
        Set<Map.Entry<Integer, Set<String>>> entrySet = packageInfo.entrySet();
        for (Map.Entry<Integer, Set<String>> setEntry : entrySet) {
            int[] types = getAssetType(setEntry.getKey());
            if (types != null && types.length > 0) {
                for (Integer type : types) {
                    List<AssetInfo> fromAsset = findFromLocal(type, setEntry.getValue());
                    if (!CommonUtils.isEmpty(fromAsset)) {
                        result.addAll(fromAsset);
                    }
                }
            }
        }
        return result;
    }

    /**
     * 执行sdk中的模板导出相关逻辑
     * Do the real template export
     */
    private void doNvsTemplateExport() {
        if (mStreamingContext == null) {
            dealWithExportComplete(false);
            return;
        }
        //导出前修改clip的trim为用户手动设置的值
        // Modify the trim of the clip to the manually set value by the user before exporting.
        changeClipTrim();
        boolean exportTemplateInfo;
        if (isSampleTemplate) {
            exportTemplateInfo = EditorEngine.getInstance().exportProjectInfo(mTimeline, mUuid, mRatio);
        } else {
            exportTemplateInfo = EditorEngine.getInstance().exportTemplateInfo(mTimeline, mUuid, mRatio);
        }
        if (!exportTemplateInfo) {
            if (mOnExportListener != null) {
                mOnExportListener.onCompleted(mUploadParam, true);
            }
            dealWithExportComplete(false);
            return;
        }
        boolean generateTemplatePackage;
        if (isSampleTemplate) {
            generateTemplatePackage = mStreamingContext.generateProjectPackage(mUuid, mRootResourceFilePath, mGenerateTemplateFileFolder);
        } else {
            generateTemplatePackage = mStreamingContext.generateTemplatePackage(mUuid, mRootResourceFilePath, mGenerateTemplateFileFolder);
        }
        if (mOnExportListener != null) {
            if (generateTemplatePackage) {
                mOnExportListener.onCompleted(mUploadParam, generateTemplatePackage);
            } else {
                mOnExportListener.onFailed(null);
            }
        }
        restoreClipTrim();
        dealWithExportComplete(generateTemplatePackage);
    }

    /**
     * 获取字幕资源
     * Save caption resource
     *
     * @param captionClip the caption clip 字幕片段
     * @param setMap      the PackageInfo set map PackageInfo的Map集合
     */
    private void getCaptionResource(MeicamCaptionClip captionClip, @NonNull Set<PackageInfo> setMap) {
        if (captionClip == null) {
            return;
        }
        addToSet(setMap, captionClip.getCombinationAnimationUuid(), TYPE_CAPTION_ANIMATION_COMBINATION);
        addToSet(setMap, captionClip.getMarchInAnimationUuid(), TYPE_CAPTION_ANIMATION_IN);
        addToSet(setMap, captionClip.getMarchOutAnimationUuid(), TYPE_CAPTION_ANIMATION_OUT);
        addToSet(setMap, captionClip.getBubbleUuid(), TYPE_CAPTION_BUBBLE);
        addToSet(setMap, captionClip.getRichWordUuid(), TYPE_CAPTION_FLOWER);
    }

    /**
     * 保存音频资源
     * save audio resource
     */
    private void saveAudioResource() {
        for (int i = 0; i < mTimeline.getAudioTrackCount(); i++) {
            MeicamAudioTrack meicamAudioTrack = mTimeline.getAudioTrack(i);
            if (meicamAudioTrack == null) {
                continue;
            }
            for (int j = 0; j < meicamAudioTrack.getClipCount(); j++) {
                MeicamAudioClip audioClip = meicamAudioTrack.getAudioClip(j);
                String filePath = audioClip.getFilePath();
                if (!TextUtils.isEmpty(filePath)) {
                    FileUtils.copy(filePath, mRootResourceFilePath + File.separator + FileUtils.getFileName(filePath));
                }
            }
        }
    }

    /**
     * 保存水印资源
     * save water maker resource
     */
    private void saveWaterMakerResource() {
        MeicamWaterMark meicamWaterMark = mTimeline.getMeicamWaterMark();
        if (meicamWaterMark == null) {
            return;
        }
        String filePath = meicamWaterMark.getWatermarkFilePath();
        //assets:/water_mark/water_mark_meiying.png
        if (!TextUtils.isEmpty(filePath)) {
            if (!TextUtils.isEmpty(filePath)) {
                String dstPath = mRootResourceFilePath + File.separator + FileUtils.getFileName(filePath);
                if (filePath.startsWith("assets:/")) {
                    filePath = filePath.replaceFirst("assets:/", "");
                    ResourceUtils.copyFileFromAssets(filePath, dstPath);
                } else {
                    FileUtils.copy(filePath, dstPath);
                }
            }
        }
    }

    /**
     * 保存贴纸资源
     * save sticker resource
     */
    private void saveStickerResource() {
        Set<String> pathSet = new HashSet<>();
        int trackCount = mTimeline.getStickerCaptionTrackCount();
        if (trackCount > 0) {
            for (int index = 0; index < trackCount; index++) {
                MeicamStickerCaptionTrack track = mTimeline.findStickCaptionTrack(index);
                if (track != null) {
                    int clipCount = track.getClipCount();
                    if (clipCount > 0) {
                        for (int clipIndex = 0; clipIndex < clipCount; clipIndex++) {
                            ClipInfo captionStickerClip = track.getCaptionStickerClip(index);
                            if (captionStickerClip instanceof MeicamStickerClip) {
                                MeicamStickerClip clip = (MeicamStickerClip) captionStickerClip;
                                if (clip.getIsCustomSticker()) {
                                    String path = clip.getCustomAnimatedStickerImagePath();
                                    if (!TextUtils.isEmpty(path)) {
                                        pathSet.add(path);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        if (pathSet.isEmpty()) {
            return;
        }
        for (String filePath : pathSet) {
            if (!TextUtils.isEmpty(filePath)) {
                String dstPath = mRootResourceFilePath + File.separator + FileUtils.getFileName(filePath);
                if (filePath.startsWith("assets:/")) {
                    filePath = filePath.replaceFirst("assets:/", "");
                    ResourceUtils.copyFileFromAssets(filePath, dstPath);
                } else {
                    FileUtils.copy(filePath, dstPath);
                }
            }
        }
    }

    private String[] changeWaterMarkFilePath() {
        MeicamWaterMark meicamWaterMark = mTimeline.getMeicamWaterMark();
        if (meicamWaterMark == null) {
            return null;
        }
        String filePath = meicamWaterMark.getWatermarkFilePath();
        if (!TextUtils.isEmpty(filePath)) {
            String stringMd5 = FileUtils.getStringMd5(filePath);
            mTimeline.addWatermark(getModifiedPath(stringMd5, filePath),
                    meicamWaterMark.getDisplayWidth(),
                    meicamWaterMark.getDisplayHeight(),
                    meicamWaterMark.getMarginX(),
                    meicamWaterMark.getMarginY());
            return new String[]{filePath, stringMd5};
        }
        return null;
    }

    /**
     * Get all package info
     * 获取所有的资源包信息
     *
     * @return package info list 资源包信息集合
     */
    private Map<Integer, Set<String>> getPackageInfo() {
        Set<PackageInfo> filterSet = new HashSet<>();
        /*Map
         * 转场
         * Transition
         */
        MeicamVideoTrack meicamVideoTrack = mTimeline.getVideoTrack(0);
        if (meicamVideoTrack != null) {
            for (int i = 0; i < meicamVideoTrack.getTransitionCount(); i++) {
                MeicamTransition meicamTransition = meicamVideoTrack.getTransitionByCollectionIndex(i);
                if (meicamTransition != null) {
                    addToSet(filterSet, meicamTransition.getDesc(), TYPE_TRANSITION);
                }
            }
        }
        /*
         * 动画和滤镜
         * Animation and filter
         */
        for (int i = 0; i < mTimeline.videoTrackCount(); i++) {
            meicamVideoTrack = mTimeline.getVideoTrack(i);
            for (int j = 0; j < meicamVideoTrack.getClipCount(); j++) {
                MeicamVideoClip meicamVideoClip = meicamVideoTrack.getVideoClip(j);

                /*
                 * 动画
                 * Animation
                 * */
                AnimationData animationData = EditorEngine.getInstance().getVideoClipAnimation(meicamVideoClip);
                if (animationData != null) {
                    addToSet(filterSet, animationData.getPackageID(), TYPE_ANIMATION);
                    addToSet(filterSet, animationData.getPackageID2(), TYPE_ANIMATION);
                }
                /*
                 * 滤镜
                 * Filter
                 * */
                for (int k = 0; k < meicamVideoClip.getVideoFxCount(); k++) {
                    MeicamVideoFx videoFx = meicamVideoClip.getVideoFx(k);
                    if (videoFx == null) {
                        continue;
                    }
                    if (MeicamVideoFx.SubType.SUB_TYPE_CLIP_FILTER.equals(videoFx.getSubType()) ||
                            MeicamVideoFx.SubType.SUB_TYPE_TIMELINE_FILTER.equals(videoFx.getSubType())) {
                        addToSet(filterSet, videoFx.getDesc(), TYPE_FILTER);
                    }
                }
                NvsVideoFx sceneFx = meicamVideoClip.findSceneFx();
                if (sceneFx != null) {
                    for (String[] param : NvsConstants.getShapeParam()) {
                        if (MeicamFxParam.TYPE_STRING.equals(param[0])) {
                            String stringVal = sceneFx.getStringVal(param[1]);
                            if (!TextUtils.isEmpty(stringVal)) {
                                addToSet(filterSet, stringVal, TYPE_SHAPE);
                            }
                        }
                    }
                }
            }
        }


        /*
         * 时间线滤镜
         * Timeline  filter
         */

        for (int i = 0; i < mTimeline.getFilterAndAdjustTimelineTracksCount(); i++) {
            MeicamTimelineVideoFxTrack meicamTimelineVideoFxTrack = mTimeline.getFilterAndAdjustTimelineTrack(i);
            if (meicamTimelineVideoFxTrack != null) {
                for (int index = 0; index < meicamTimelineVideoFxTrack.getFilterAndAdjustCount(); index++) {
                    MeicamTimelineVideoFilterAndAdjustClip meicamTimelineVideoFilterAndAdjustClip = meicamTimelineVideoFxTrack.getFilterAndAdjustClip(index);
                    if (meicamTimelineVideoFilterAndAdjustClip != null) {
                        MeicamTimelineVideoFxClip clipInfo = meicamTimelineVideoFilterAndAdjustClip.getAdjustTimelineFx(MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FILTER);
                        if (clipInfo != null) {
                            addToSet(filterSet, clipInfo.getDesc(), TYPE_FILTER);
                        }
                    }
                }
            }
        }

        /*
         * 时间线特效
         * Timeline  effect
         */

        for (int i = 0; i < mTimeline.getTimelineFxTrackCount(); i++) {
            MeicamTimelineVideoFxTrack meicamTimelineVideoFxTrack = mTimeline.getTimelineFxTrack(i);
            if (meicamTimelineVideoFxTrack != null) {
                for (int index = 0; index < meicamTimelineVideoFxTrack.getClipCount(); index++) {
                    MeicamTimelineVideoFxClip clipInfo = meicamTimelineVideoFxTrack.getClip(index);
                    if (clipInfo != null) {
                        addToSet(filterSet, clipInfo.getDesc(), TYPE_EFFECT);
                    }
                }
            }
        }


        /*
         * 字幕和贴纸
         * Caption and sticker
         */
        for (int i = 0; i < mTimeline.getStickerCaptionTrackCount(); i++) {
            MeicamStickerCaptionTrack stickerCaptionTrack = mTimeline.findStickCaptionTrack(i);
            if (stickerCaptionTrack == null) {
                continue;
            }
            for (int j = 0; j < stickerCaptionTrack.getClipCount(); j++) {
                ClipInfo<?> clipInfo = stickerCaptionTrack.getCaptionStickerClip(j);
                if (clipInfo instanceof MeicamStickerClip) {
                    MeicamStickerClip stickerClip = (MeicamStickerClip) clipInfo;
                    addToSet(filterSet, stickerClip.getPackageId(), TYPE_ANIMATED_STICKER);
                    StickerAnimation animation = stickerClip.getAnimation(StickerAnimation.TYPE_ANIMATION_IN);
                    if (animation != null) {
                        addToSet(filterSet, animation.getPackageId(), TYPE_ANIMATED_STICKER_ANIMATION_IN);
                    }
                    animation = stickerClip.getAnimation(StickerAnimation.TYPE_ANIMATION_OUT);
                    if (animation != null) {
                        addToSet(filterSet, animation.getPackageId(), TYPE_ANIMATED_STICKER_ANIMATION_OUT);
                    }
                    animation = stickerClip.getAnimation(StickerAnimation.TYPE_ANIMATION_COMP);
                    if (animation != null) {
                        addToSet(filterSet, animation.getPackageId(), TYPE_ANIMATED_STICKER_ANIMATION_COMP);
                    }
                } else if (clipInfo instanceof MeicamCaptionClip) {
                    /*
                     * 普通字幕
                     * Common caption
                     */
                    getCaptionResource((MeicamCaptionClip) clipInfo, filterSet);
                } else if (clipInfo instanceof MeicamCompoundCaptionClip) {
                    /*
                     * 组合字幕
                     * compound caption
                     */
                    addToSet(filterSet, ((MeicamCompoundCaptionClip) clipInfo).getStyleDesc(), TYPE_COMPOUND_CAPTION);
                }
            }
        }
        if (!filterSet.isEmpty()) {
            Map<Integer, Set<String>> date = new HashMap<>();
            for (PackageInfo packageInfo : filterSet) {
                addToSetMap(date, packageInfo.packageId, packageInfo.type);
            }
            return date;
        }
        return null;
    }

    /**
     * Add data to set of map
     *
     * @param setMap     the set of map
     * @param innerValue the inner value
     * @param key        the key
     */
    private void addToSetMap(@NonNull Map<Integer, Set<String>> setMap, String innerValue, int key) {
        if (TextUtils.isEmpty(innerValue)) {
            return;
        }
        Set<String> setValue = setMap.get(key);
        if (setValue == null) {
            setValue = new HashSet<>();
            setMap.put(key, setValue);
        }
        setValue.add(innerValue);
    }

    /**
     * Add data to set
     *
     * @param set        the set
     * @param innerValue the inner value
     * @param key        the key
     */
    private void addToSet(@NonNull Set<PackageInfo> set, String innerValue, int key) {
        if (TextUtils.isEmpty(innerValue)) {
            return;
        }
        set.add(new PackageInfo(innerValue, key));
    }


    /**
     * 保存图片背景资源
     * save background resource
     */
    private void saveBackgroundResource() {
        MeicamVideoTrack meicamVideoTrack = mTimeline.getVideoTrack(0);
        if (meicamVideoTrack == null) {
            return;
        }
        String filePath = getBackgroundFilePath();
        if (!TextUtils.isEmpty(filePath)) {
            String dstPath = mRootResourceFilePath + File.separator + FileUtils.getFileName(filePath);
            if (filePath.startsWith("assets:/")) {
                filePath = filePath.replaceFirst("assets:/", "");
                ResourceUtils.copyFileFromAssets(filePath, dstPath);
            } else {
                FileUtils.copy(filePath, dstPath);
            }
        }
    }

    /**
     * 保存内置特效资源
     * save build_in effect resource
     */
    private void saveBuildInResource() {
        Set<String> pathSet = new HashSet<>();
        int timelineFxTrackCount = mTimeline.getTimelineFxTrackCount();
        if (timelineFxTrackCount > 0) {
            for (int index = 0; index < timelineFxTrackCount; index++) {
                MeicamTimelineVideoFxTrack timelineFxTrack = mTimeline.getTimelineFxTrack(index);
                if (timelineFxTrack != null) {
                    int clipCount = timelineFxTrack.getClipCount();
                    if (clipCount > 0) {
                        for (int clipIndex = 0; clipIndex < clipCount; clipIndex++) {
                            MeicamTimelineVideoFxClip clip = timelineFxTrack.getClip(clipIndex);
                            if (clip != null) {
                                String desc = clip.getDesc();
                                if ("Water Ripple".equals(desc)) {
                                    String filePath = clip.getStringVal("Pattern Path");
                                    if (!TextUtils.isEmpty(filePath)) {
                                        pathSet.add(filePath);
                                    }
                                } else if ("Noise".equals(desc)) {
                                    String filePath = clip.getStringVal("Tex File Path");
                                    if (!TextUtils.isEmpty(filePath)) {
                                        pathSet.add(filePath);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        int count = mTimeline.videoTrackCount();
        if (count > 0) {
            for (int index = 0; index < count; index++) {
                MeicamVideoTrack meicamVideoTrack = mTimeline.getVideoTrack(index);
                if (meicamVideoTrack == null) {
                    continue;
                }
                int clipCount = meicamVideoTrack.getClipCount();
                if (clipCount <= 0) {
                    continue;
                }
                for (int clipIndex = 0; clipIndex < clipCount; clipIndex++) {
                    MeicamVideoClip videoClip = meicamVideoTrack.getVideoClip(clipIndex);
                    if (videoClip == null) {
                        continue;
                    }
                    int videoFxCount = videoClip.getVideoFxCount();
                    if (videoFxCount > 0) {
                        for (int fxIndex = 0; fxIndex < videoFxCount; fxIndex++) {
                            MeicamVideoFx videoFx = videoClip.getVideoFx(fxIndex);
                            if (videoFx != null) {
                                String desc = videoFx.getDesc();
                                if ("Water Ripple".equals(desc)) {
                                    String filePath = videoFx.getStringVal("Pattern Path");
                                    if (!TextUtils.isEmpty(filePath)) {
                                        pathSet.add(filePath);
                                    }
                                } else if ("Noise".equals(desc)) {
                                    String filePath = videoFx.getStringVal("Tex File Path");
                                    if (!TextUtils.isEmpty(filePath)) {
                                        pathSet.add(filePath);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        if (pathSet.isEmpty()) {
            return;
        }
        for (String filePath : pathSet) {
            if (!TextUtils.isEmpty(filePath)) {
                String dstPath = mRootResourceFilePath + File.separator + FileUtils.getFileName(filePath);
                if (filePath.startsWith("assets:/")) {
                    filePath = filePath.replaceFirst("assets:/", "");
                    ResourceUtils.copyFileFromAssets(filePath, dstPath);
                } else {
                    FileUtils.copy(filePath, dstPath);
                }
            }
        }
    }

    private String getBackgroundFilePath() {
        MeicamVideoTrack meicamVideoTrack = mTimeline.getVideoTrack(0);
        if (meicamVideoTrack == null) {
            return null;
        }
        for (int i = 0; i < meicamVideoTrack.getClipCount(); i++) {
            MeicamVideoClip meicamVideoClip = meicamVideoTrack.getVideoClip(i);
            MeicamVideoFx meicamVideoFx = meicamVideoClip.findPropertyVideoFx();
            if (meicamVideoFx != null && VALUE_IMAGE_BACKGROUND_MODE.equals(meicamVideoFx.getStringVal(KEY_BACKGROUND_MODE))) {
                return meicamVideoFx.getStringVal(NvsConstants.KEY_BACKGROUND_IMAGE_PATH);
            }
        }
        return null;
    }

    private String[] changeBackgroundFilePath() {
        MeicamVideoTrack meicamVideoTrack = mTimeline.getVideoTrack(0);
        if (meicamVideoTrack == null) {
            return null;
        }
        for (int i = 0; i < meicamVideoTrack.getClipCount(); i++) {
            MeicamVideoClip meicamVideoClip = meicamVideoTrack.getVideoClip(i);
            MeicamVideoFx meicamVideoFx = meicamVideoClip.findPropertyVideoFx();
            if (meicamVideoFx != null && VALUE_IMAGE_BACKGROUND_MODE.equals(meicamVideoFx.getStringVal(KEY_BACKGROUND_MODE))) {
                String stringVal = meicamVideoFx.getStringVal(NvsConstants.KEY_BACKGROUND_IMAGE_PATH);
                if (!TextUtils.isEmpty(stringVal)) {
                    String fileName = FileUtils.getFileName(stringVal);
                    String[] split = fileName.split("\\.");
                    String uuid;
                    String backgroundPath = PathUtils.getBackgroundDir() + File.separator + split[0] + File.separator + fileName;
                    String cloudBackgroundPath = PathUtils.getCloudDraftFootageFileFolder() + File.separator + fileName;
                    if (new File(backgroundPath).exists() || new File(cloudBackgroundPath).exists()) {
                        meicamVideoFx.setStringVal(NvsConstants.KEY_BACKGROUND_IMAGE_PATH, fileName);
                        uuid = split[0];
                        ExportTemplateDescInfo.FootageInfo internal =
                                new ExportTemplateDescInfo.FootageInfo(uuid, stringVal, TYPE_FOOTAGE_INTERNAL);
                        internal.extraData = uuid + "." + FileUtils.getFileSuffix(fileName);
                        mFootageInfos.getInfos().add(internal);
                    } else {
                        uuid = UUID.randomUUID().toString();
                        meicamVideoFx.setStringVal(NvsConstants.KEY_BACKGROUND_IMAGE_PATH, uuid + "." + FileUtils.getFileSuffix(stringVal));
                        ExportTemplateDescInfo.FootageInfo internal =
                                new ExportTemplateDescInfo.FootageInfo(uuid, stringVal, TYPE_FOOTAGE_INTERNAL);
                        internal.extraData = uuid + "_isBackground";
                        mFootageInfos.getInfos().add(internal);
                    }
                    return new String[]{stringVal, uuid};
                }
            }
        }
        return null;
    }

    @NonNull
    private String getModifiedPath(String stringMd5, String filePath) {
        return stringMd5 + "_" + FileUtils.getFileName(filePath);
    }

    private List<String[]> changeAlphaPath() {
        int count = mTimeline.videoTrackCount();
        List<String[]> result = new ArrayList<>();
        if (count > 0) {
            for (int index = 0; index < count; index++) {
                MeicamVideoTrack meicamVideoTrack = mTimeline.getVideoTrack(index);
                if (meicamVideoTrack == null) {
                    continue;
                }
                for (int i = 0; i < meicamVideoTrack.getClipCount(); i++) {
                    MeicamVideoClip meicamVideoClip = meicamVideoTrack.getVideoClip(i);
                    MeicamVideoFx meicamVideoFx = meicamVideoClip.getVideoFx(SUB_TYPE_ALPHA, SET_ALPHA);
                    if (meicamVideoFx != null) {
                        meicamVideoClip.removeVideoFx(meicamVideoFx);
                    }
                    /*if (meicamVideoFx != null) {
                        String alphaPath = meicamVideoFx.getStringVal(ALPHA_FILE);
                        if (!TextUtils.isEmpty(alphaPath)) {
                            String stringMd5 = FileUtils.getStringMd5(alphaPath);
                            meicamVideoFx.setStringVal(ALPHA_FILE, getModifiedPath(stringMd5, alphaPath));
                            result.add(new String[]{alphaPath, stringMd5});
                        }
                    }*/
                }
            }
        }
        return result;
    }

    private void findOtherInnerFilePath() {
        List<String[]> stickerPath = changeStickerPath();
        if (!CommonUtils.isEmpty(stickerPath)) {
            for (String[] path : stickerPath) {
                addInternalFile(path[1], path[0]);
            }
        }
        String[] pathPair = changeBackgroundFilePath();
        /*if (pathPair !=  null) {
            //addInternalFile(pathPair[1], pathPair[0]);
            if (!TextUtils.isEmpty(pathPair[0])) {
                ExportTemplateDescInfo.FootageInfo internal =
                        new ExportTemplateDescInfo.FootageInfo(pathPair[1], pathPair[0], TYPE_FOOTAGE_INTERNAL);
                internal.extraData = pathPair[1]+"_isBackground";
                mFootageInfos.getInfos().add(internal);
        }
        }*/
        pathPair = changeWaterMarkFilePath();
        if (pathPair != null) {
            addInternalFile(pathPair[1], pathPair[0]);
        }
       /* List<String[]> alphaPath = changeAlphaPath();
        if (!CommonUtils.isEmpty(alphaPath)) {
            for (String[] path : alphaPath) {
                addInternalFile(path[1], path[0]);
            }
        }*/

        List<String[]> alphaPath = changBuildInPath();
        if (!CommonUtils.isEmpty(alphaPath)) {
            for (String[] path : alphaPath) {
                addInternalFile(path[1], path[0]);
            }
        }
    }


    /**
     * 修改内置特效的路径
     * change build_in effect resource path
     */
    private List<String[]> changBuildInPath() {
        List<String[]> result = new ArrayList<>();
        int timelineFxTrackCount = mTimeline.getTimelineFxTrackCount();
        if (timelineFxTrackCount > 0) {
            for (int index = 0; index < timelineFxTrackCount; index++) {
                MeicamTimelineVideoFxTrack timelineFxTrack = mTimeline.getTimelineFxTrack(index);
                if (timelineFxTrack != null) {
                    int clipCount = timelineFxTrack.getClipCount();
                    if (clipCount > 0) {
                        for (int clipIndex = 0; clipIndex < clipCount; clipIndex++) {
                            MeicamTimelineVideoFxClip clip = timelineFxTrack.getClip(clipIndex);
                            if (clip != null) {
                                String desc = clip.getDesc();
                                if ("Water Ripple".equals(desc)) {
                                    replaceParam(result, clip, "Pattern Path");
                                } else if ("Noise".equals(desc)) {
                                    replaceParam(result, clip, "Tex File Path");
                                }
                            }
                        }
                    }
                }
            }
        }
        int count = mTimeline.videoTrackCount();
        if (count > 0) {
            for (int index = 0; index < count; index++) {
                MeicamVideoTrack meicamVideoTrack = mTimeline.getVideoTrack(index);
                if (meicamVideoTrack == null) {
                    continue;
                }
                int clipCount = meicamVideoTrack.getClipCount();
                if (clipCount <= 0) {
                    continue;
                }
                for (int clipIndex = 0; clipIndex < clipCount; clipIndex++) {
                    MeicamVideoClip videoClip = meicamVideoTrack.getVideoClip(clipIndex);
                    if (videoClip == null) {
                        continue;
                    }
                    int videoFxCount = videoClip.getVideoFxCount();
                    if (videoFxCount > 0) {
                        for (int fxIndex = 0; fxIndex < videoFxCount; fxIndex++) {
                            MeicamVideoFx videoFx = videoClip.getVideoFx(fxIndex);
                            if (videoFx != null) {
                                String desc = videoFx.getDesc();
                                if ("Water Ripple".equals(desc)) {
                                    String filePath = videoFx.getStringVal("Pattern Path");
                                    if (!TextUtils.isEmpty(filePath)) {
                                        replaceParam(result, videoFx, "Pattern Path");
                                    }
                                } else if ("Noise".equals(desc)) {
                                    String filePath = videoFx.getStringVal("Tex File Path");
                                    if (!TextUtils.isEmpty(filePath)) {
                                        replaceParam(result, videoFx, "Tex File Path");
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return result;
    }

    private void replaceParam(List<String[]> result, MeicamTimelineVideoFxClip clip, String paramKey) {
        String filePath = clip.getStringVal(paramKey);
        if (!TextUtils.isEmpty(filePath)) {
            String stringMd5 = FileUtils.getStringMd5(filePath);
            String newPath = getModifiedPath(stringMd5, filePath);
            clip.setStringVal(paramKey, newPath);
            result.add(new String[]{filePath, stringMd5});
        }
    }

    private void replaceParam(List<String[]> result, MeicamVideoFx videoFx, String paramKey) {
        String filePath = videoFx.getStringVal(paramKey);
        if (!TextUtils.isEmpty(filePath)) {
            String stringMd5 = FileUtils.getStringMd5(filePath);
            String newPath = getModifiedPath(stringMd5, filePath);
            videoFx.setStringVal(paramKey, newPath);
            result.add(new String[]{filePath, stringMd5});
        }
    }

    private List<String[]> changeStickerPath() {
        int trackCount = mTimeline.getStickerCaptionTrackCount();
        List<String[]> result = new ArrayList<>();
        if (trackCount > 0) {
            for (int index = 0; index < trackCount; index++) {
                MeicamStickerCaptionTrack track = mTimeline.findStickCaptionTrack(index);
                if (track != null) {
                    int clipCount = track.getClipCount();
                    if (clipCount > 0) {
                        for (int clipIndex = 0; clipIndex < clipCount; clipIndex++) {
                            ClipInfo captionStickerClip = track.getCaptionStickerClip(index);
                            if (captionStickerClip instanceof MeicamStickerClip) {
                                MeicamStickerClip clip = (MeicamStickerClip) captionStickerClip;
                                if (clip.getIsCustomSticker()) {
                                    String path = clip.getCustomAnimatedStickerImagePath();
                                    String stringMd5 = FileUtils.getStringMd5(path);
                                    clip.setCustomAnimatedStickerImagePath(getModifiedPath(stringMd5, path));
                                    result.add(new String[]{path, stringMd5});
                                }
                            }
                        }
                    }
                }
            }
        }
        return result;
    }

    private void addInternalFile(String id, String filePath) {
        if (!TextUtils.isEmpty(filePath)) {
            ExportTemplateDescInfo.FootageInfo internal =
                    new ExportTemplateDescInfo.FootageInfo(id, filePath, TYPE_FOOTAGE_INTERNAL);
            internal.extraData = getModifiedPath(id, filePath);
            mFootageInfos.getInfos().add(internal);
        }
    }

    /**
     * 保存图片背景资源
     * save background resource
     */
    private void saveLockedImageFle() {
        if (CommonUtils.isEmpty(mLockTemplateClipList)) {
            return;
        }
        for (ExportTemplateClip templateClip : mLockTemplateClipList) {
            String imagePath = templateClip.getImagePath();
            if (TextUtils.isEmpty(imagePath)) {
                continue;
            }
            String fileName = FileUtils.getFileName(imagePath);
            if (!TextUtils.isEmpty(fileName) && isImage(fileName)) {
                String destFilePath = mRootResourceFilePath + File.separator + fileName;
                FileUtils.createOrExistsFile(destFilePath);
                if (imagePath.startsWith("assets")) {
                    imagePath = imagePath.replaceFirst("assets:/", "");
                    ResourceUtils.copyFileFromAssets(imagePath, destFilePath);
                } else {
                    FileUtils.copy(imagePath, destFilePath);
                }
            }
        }
    }


    /**
     * 试图复制资源
     * Try to copy resource
     *
     * @param assetInfoList the asset list 资源包集合
     * @return the copied successful number 复制成功的数量
     */
    private int copyResource(List<AssetInfo> assetInfoList) {
        if (CommonUtils.isEmpty(assetInfoList)) {
            return 0;
        }
        AtomicInteger count = new AtomicInteger();
        for (AssetInfo assetInfo : assetInfoList) {
            String assetPath = assetInfo.getAssetPath();
            if (TextUtils.isEmpty(assetPath)) {
                continue;
            }
            String dstPath = getResourceFilePath(assetInfo.getAssetPath());
            String fileName = FileUtils.getFileName(assetInfo.getAssetPath());
            if (TextUtils.isEmpty(dstPath) || TextUtils.isEmpty(fileName)) {
                continue;
            }
            if (!FileUtils.createOrExistsDir(dstPath)) {
                continue;
            }
            String destPath = dstPath + File.separator + fileName;
            if (assetPath.startsWith("asset")) {
                if (!new File(destPath).exists()) {
                    if (copyFromAsset(assetInfo, destPath)) {
                        count.incrementAndGet();
                    }
                }
            } else {
                if (!new File(destPath).exists()) {
                    if (FileUtils.copy(assetPath, destPath)) {
                        count.incrementAndGet();
                    }
                }
            }
        }
        return count.intValue();
    }

    /**
     * 根据类型获取资源文件路径
     * Get resource file path
     *
     * @param path the file path 文件路径
     * @return 保存的文件路径
     */
    private String getResourceFilePath(String path) {
        String fileName = FileUtils.getFileSuffix(path);
        if (!TextUtils.isEmpty(fileName)) {
            return mRootResourceFilePath + File.separator + fileName;
        }
        return null;
    }

    /**
     * 根据类型和资源包id在asset目录中查找资源信息
     * Find resource information in the asset directory by type and package id
     *
     * @param type          the type 类型
     * @param packageIdList the packageId list 资源包id list
     */
    private List<AssetInfo> findFromLocal(int type, Set<String> packageIdList) {
        if (CommonUtils.isEmpty(packageIdList)) {
            return null;
        }
        List<AssetInfo> tempList = AssetsManager.get().getPackageAssetList(type);
        List<AssetInfo> assetInfos = new ArrayList<>();
        for (String packageId : packageIdList) {
            AssetInfo findAsset = null;
            if (!CommonUtils.isEmpty(tempList)) {
                for (AssetInfo assetInfo : tempList) {
                    if (packageId.equals(assetInfo.getPackageId())) {
                        findAsset = assetInfo;
                    }
                }
            }
            if (findAsset == null) {
                findAsset = AssetsManager.get().getAssetInfo(packageId, type);
            }
            if (findAsset != null) {
                assetInfos.add(findAsset);
            }
        }
        return assetInfos;
    }


    /**
     * 根据资源信息找到资源包文件路径并把它复制到目的路径。
     * Find the resource  file path based on the resource information and copy it to the destination path.
     *
     * @param assetInfo the asset info 资源信息
     * @param dstPath   the destination path  要复制到的目录
     */
    private boolean copyFromAsset(AssetInfo assetInfo, String dstPath) {
        String assetPath = assetInfo.getAssetPath();
        if (!TextUtils.isEmpty(assetPath)) {
            assetPath = assetPath.replaceFirst("assets:/", "");
            return ResourceUtils.copyFileFromAssets(assetPath, dstPath);
        }
        return false;
    }

    /**
     * 对info.json 文件进行设置
     * set file info.json
     *
     * @param creator              the creator 创建者
     * @param uuid                 the uuid 唯一标识
     * @param ratio                the ratio 比例
     * @param duration             the duration 时长
     * @param footageCount         the footage count
     * @param innerAssetTotalCount the inner asset total count 内部资源包的总数量
     * @param templateName         the template name 模板名称
     * @param templateDesc         the template description 模板描述
     * @param coverPath            the cover path 封面路径
     * @return the json info
     */
    private String getInfoDesc(String creator, String uuid, String ratio, long duration,
                               int footageCount,
                               int innerAssetTotalCount, String templateName, String templateDesc, String coverPath) {
        if (!TextUtils.isEmpty(ratio)) {
            ratio = ratio.replace(RatioUtil.COLON_TAG, RatioUtil.V_TAG);
        }
        TemplateInfo templateInfo = new TemplateInfo();
        templateInfo.setCover(coverPath);
        templateInfo.setMinSdkVersion("2.19.0");
        templateInfo.setName(templateName);
        templateInfo.setSupportedAspectRatio(ratio);
        templateInfo.setDefaultAspectRatio(ratio);

        templateInfo.setUuid(uuid);
        templateInfo.setVersion(1);
        templateInfo.setInnerAssetTotalCount(innerAssetTotalCount);
        templateInfo.setFootageCount(footageCount);
        templateInfo.setDuration(duration / 1000);
        templateInfo.setCreator(creator);
        templateInfo.setDescription(templateDesc);
        return GsonUtils.toJson(templateInfo);
    }

    /**
     * 获取描述文件信息
     * Get desc file info
     *
     * @param uuid              the uuid 唯一标识
     * @param ratio             the ratio 比例
     * @param duration          the duration 时长
     * @param footageCount      the footage count
     * @param templateName      the template name 模板名称
     * @param templateDesc      the template description 模板描述
     * @param coverPath         the cover path 封面路径
     * @param templatePath      the template path 模板文件路径
     * @param templateVideoPath the template video path 模板视频路径
     * @return the json info
     */
    private static String getSimpleInfoDesc(String uuid, String ratio, long duration,
                                            int footageCount, int innerTotalCount, String templateName, String templateDesc,
                                            String coverPath, String templatePath, String templateVideoPath, String minSdkVersion, ExportTemplateDescInfo.InnerAssetWrapper innerAssets, ExportTemplateDescInfo.FootageInfoWrapper footageInfos) {
        if (!TextUtils.isEmpty(ratio)) {
            ratio = ratio.replace(RatioUtil.COLON_TAG, RatioUtil.V_TAG);
        }
        ExportTemplateDescInfo descInfo = new ExportTemplateDescInfo();
        if (innerAssets != null) {
            List<ExportTemplateDescInfo.InnerAssetWrapper> data = new ArrayList<>();
            data.add(innerAssets);
            descInfo.setInnerAssets(data);
        }
        if (footageInfos != null) {
            List<ExportTemplateDescInfo.FootageInfoWrapper> data = new ArrayList<>();
            data.add(footageInfos);
            descInfo.setFootageInfos(data);
        }
        ExportTemplateDescInfo.TranslationBean translationBean = new ExportTemplateDescInfo.TranslationBean();
        List<ExportTemplateDescInfo.TranslationBean> beanList = new ArrayList<>();
        beanList.add(translationBean);
        descInfo.setTranslation(beanList);

        descInfo.setCreateTime(System.currentTimeMillis());
        descInfo.setSupportedAspectRatio(ratio);
        descInfo.setDefaultAspectRatio(ratio);
        descInfo.setName(templateName);
        descInfo.setDescription(templateDesc);
        descInfo.setDuration(duration / 1000);
        descInfo.setCover(coverPath);
        descInfo.setUuid(uuid);
        descInfo.setFootageCount(footageCount);
        descInfo.setInnerAssetTotalCount(innerTotalCount);
        descInfo.setTemplatePath(templatePath);
        NvsStreamingContext.SdkVersion sdkVersion = NvsStreamingContext.getInstance().getSdkVersion();
        String version = sdkVersion.majorVersion + "." + sdkVersion.minorVersion +
                "." + sdkVersion.revisionNumber;
        descInfo.setSdkVersion(version);
        descInfo.setMinSdkVersion(minSdkVersion);
        descInfo.setTemplateVideoPath(templateVideoPath);
        return GsonUtils.toJson(descInfo);
    }

    Map<Integer, int[]> assetsTypeMap = new HashMap<>();

    private int[] getAssetType(int type) {
        if (!assetsTypeMap.isEmpty()) {
            return assetsTypeMap.get(type);
        }
        assetsTypeMap.put(TYPE_TRANSITION, new int[]{
                AssetInfo.ASSET_VIDEO_TRANSITION,
                AssetInfo.ASSET_VIDEO_TRANSITION_3D,
                AssetInfo.ASSET_VIDEO_TRANSITION_EFFECT});

        assetsTypeMap.put(TYPE_ANIMATION, new int[]{
                AssetInfo.ASSET_ANIMATION_IN,
                AssetInfo.ASSET_ANIMATION_GROUP,
                AssetInfo.ASSET_ANIMATION_OUT});

        assetsTypeMap.put(TYPE_FILTER, new int[]{
                AssetInfo.ASSET_FILTER});
        assetsTypeMap.put(TYPE_CAPTION_STYLE, new int[]{
                AssetInfo.ASSET_CAPTION_STYLE});
        assetsTypeMap.put(TYPE_EFFECT, new int[]{
                AssetInfo.ASSET_EFFECT_DREAM,
                AssetInfo.ASSET_EFFECT_FRAME,
                AssetInfo.ASSET_EFFECT_LIVELY,
                AssetInfo.ASSET_EFFECT_SHAKING,
                AssetInfo.ASSET_EFFECT_OTHER});
        assetsTypeMap.put(TYPE_ANIMATED_STICKER, new int[]{
                AssetInfo.ASSET_ANIMATED_STICKER,
                AssetInfo.ASSET_ANIMATED_STICKER_CUSTOM});

        assetsTypeMap.put(TYPE_CAPTION_ANIMATION_IN, new int[]{
                AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_IN});
        assetsTypeMap.put(TYPE_CAPTION_ANIMATION_OUT, new int[]{
                AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_OUT});

        assetsTypeMap.put(TYPE_CAPTION_ANIMATION_COMBINATION, new int[]{
                AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_COMBINATION});

        assetsTypeMap.put(TYPE_CAPTION_BUBBLE, new int[]{
                AssetInfo.ASSET_CUSTOM_CAPTION_BUBBLE});

        assetsTypeMap.put(TYPE_CAPTION_FLOWER, new int[]{
                AssetInfo.ASSET_CUSTOM_CAPTION_FLOWER});

        assetsTypeMap.put(TYPE_COMPOUND_CAPTION, new int[]{
                AssetInfo.ASSET_COMPOUND_CAPTION});
        assetsTypeMap.put(TYPE_ANIMATED_STICKER_ANIMATION_IN, new int[]{
                AssetInfo.ASSET_STICKER_ANIMATION_IN});
        assetsTypeMap.put(TYPE_ANIMATED_STICKER_ANIMATION_OUT, new int[]{
                AssetInfo.ASSET_STICKER_ANIMATION_OUT});
        assetsTypeMap.put(TYPE_ANIMATED_STICKER_ANIMATION_COMP, new int[]{
                AssetInfo.ASSET_STICKER_ANIMATION_COMP});
        assetsTypeMap.put(TYPE_SHAPE, new int[]{
                AssetInfo.ASSET_PACKAGE_TYPE_FACE_MESH,
                AssetInfo.ASSET_PACKAGE_TYPE_FACE_WARP});
        return assetsTypeMap.get(type);
    }

    /**
     * 处理模板导出完成
     * A method to handle template export completion
     *
     * @param success true success 成功，false failed 失败
     */
    public void dealWithExportComplete(boolean success) {
        if (!success) {
            mUploadParam = null;
            FileUtils.delete(mRootResourceFilePath);
            FileUtils.delete(mGenerateTemplateFileFolder);
        }
        release();
    }

    /**
     * Cancel task
     * <p>
     * 取消任务
     */
    public void cancelTask() {
        if (mExportTask != null) {
            mExportTask.cancel();
        }
        dealWithExportComplete(false);
    }

    /**
     * Release
     * <p>
     * 释放资源
     */
    public void release() {
        if (mExportTask != null) {
            mExportTask.release();
        }
    }

    /**
     * Class of package info
     * 包信息，用于导出
     */
    static class PackageInfo {
        String packageId;
        int type;

        public PackageInfo(String packageId, int type) {
            this.packageId = packageId;
            this.type = type;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            PackageInfo that = (PackageInfo) o;
            return packageId.equals(that.packageId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(packageId);
        }
    }


    /**
     * Abstract class of task for exporting
     * <p>
     * 导出任务抽象类
     */
    abstract static class ExportTask {
        ExportTask nextChain;
        boolean mCanceled = false;

        public abstract void execute();

        public void setNextTask(ExportTask next) {
            this.nextChain = next;
        }

        /**
         * Do next task，accessed in UI thread.
         * 执行下个任务，主线程调用
         */
        public void doNext() {
            if (nextChain != null) {
                ThreadUtils.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (!mCanceled) {
                            nextChain.execute();
                        }
                    }
                });
            }
        }

        /**
         * Cancel，accessed in UI thread.
         * 依次取消任务，主线程调用
         */
        public void cancel() {
            ThreadUtils.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    mCanceled = true;
                    if (nextChain != null) {
                        nextChain.cancel();
                    }
                }
            });
        }

        /**
         * Release，accessed in UI thread.
         * 依次释放资源，主线程调用
         */
        public void release() {
            ThreadUtils.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (nextChain != null) {
                        nextChain.release();
                    }
                }
            });
        }
    }

    /**
     * Task for save cover
     * <p>
     * 保存封面的task
     */
    public class Initiation extends ExportTask {

        @Override
        public void execute() {
            ThreadUtils.getIoPool().execute(new Runnable() {
                @Override
                public void run() {
                    String templateFileFolder = PathUtils.getTemplateFileFolder();
                    mRootResourceFilePath = templateFileFolder + File.separator + mUuid;
                    mAudioBaseFootageId = FOOTAGE_ID_AUDIO_BASE_NUMBER;
                    FileUtils.delete(mRootResourceFilePath);
                    FileUtils.createOrExistsDir(mRootResourceFilePath);
                    setTemplateAttachment();
                    doNext();
                }
            });
        }

        @Override
        public void cancel() {
            dealWithExportComplete(false);
            super.cancel();
        }
    }

    /**
     * Task for save cover
     * <p>
     * 保存封面的task
     */
    public class SaveCover extends ExportTask {
        private final EngineCallbackObserver mCallback;

        public SaveCover() {
            EngineCallbackManager.get().registerCallbackObserver(mCallback = new EngineCallbackObserver() {
                @Override
                public boolean isActive() {
                    return mOnExportListener != null && mOnExportListener.isActive();
                }

                @Override
                public void onImageGrabbedArrived(Bitmap bitmap, long time) {
                    LogUtils.d("onImageGrabbedArrived = " + bitmap);
                    saveBitmap(bitmap);
                }
            });
        }

        @Override
        public void execute() {
            EditorEngine.getInstance().grabImageFromTimelineAsync(mTimeline, 0, null);
            EditorEngine.getInstance().stop();
        }

        @Override
        public void cancel() {
            EditorEngine.getInstance().stop();
            super.cancel();
        }

        @Override
        public void release() {
            EngineCallbackManager.get().unregisterCallbackObserver(mCallback);
            super.release();
        }

        /**
         * 保存图片
         * Save bitmap
         *
         * @param bitmap the bitmap
         */
        public void saveBitmap(final Bitmap bitmap) {
            ThreadUtils.getIoPool().execute(new Runnable() {
                @Override
                public void run() {
                    final String coverPath = mGenerateTemplateFileFolder + File.separator + mUuid + ".png";
                    FileUtils.createOrExistsFile(coverPath);
                    //保存封面图
                    File imageFile = new File(coverPath);
                    if (!imageFile.exists()) {
                        return;
                    }
                    Bitmap.CompressFormat format = Bitmap.CompressFormat.PNG;
                    ImageUtils.save(bitmap, imageFile, format);
                    ThreadUtils.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mCoverPath = coverPath;
                            doNext();
                        }
                    });
                }
            });
        }
    }

    /**
     * Task for compile
     * <p>
     * 编译的task
     */
    public class CompileTask extends ExportTask {
        private final EngineCallbackObserver mCallbackObserver;

        public CompileTask() {
            EngineCallbackManager.get().registerCallbackObserver(mCallbackObserver = new EngineCallbackObserver() {
                @Override
                public boolean isActive() {
                    if (mOnExportListener != null) {
                        return mOnExportListener.isActive();
                    }
                    return false;
                }

                @Override
                public void onCompileCompleted(NvsTimeline nvsTimeline, boolean isCanceled) {
                    LogUtils.d("onCompileCompleted = " + isCanceled);
                    if (mStreamingContext != null) {
                        mStreamingContext.setCompileConfigurations(null);
                        mStreamingContext.stop();
                    }
                    if (isCanceled) {
                        if (mStreamingContext != null) {
                            mStreamingContext.stop();
                        }
                        if (mOnExportListener != null) {
                            mOnExportListener.onCanceled(true);
                        }
                        dealWithExportComplete(false);
                    } else {
                        doNext();
                    }
                }

                @Override
                public void onCompileProgress(NvsTimeline nvsTimeline, int i) {
                    if (mOnExportListener != null) {
                        float progress = i * PROGRESS_COMPILE_TOTAL / 100F;
                        LogUtils.d("progress = " + progress);
                        mOnExportListener.onProgress(progress);
                    }
                }

                @Override
                public void onCompileFailed(NvsTimeline nvsTimeline) {
                    LogUtils.d("onCompileFailed");
                    if (mOnExportListener != null) {
                        mOnExportListener.onFailed(nvsTimeline);
                    }
                }

            });
        }

        @Override
        public void execute() {
            //保存视频
            //导出自定义高度应该合比例无关
            //Save Video
            // Exporting custom heights should be proportional and independent.

//            int ratio = TimelineData.getInstance().getMakeRatio();
//            if (ratio == BaseInfo.AspectRatio_NoFitRatio) {
//                mStreamingContext.setCustomCompileVideoHeight();
//            }
            for (ExportTemplateClip templateClip : mLockTemplateClipList) {
                String imagePath = templateClip.getImagePath();
                if (TextUtils.isEmpty(imagePath)) {
                    continue;
                }
                String fileName = FileUtils.getFileName(imagePath);
                if (!TextUtils.isEmpty(fileName) && !isImage(fileName)) {
                    long trimIn = templateClip.getTrimIn();
                    long trimOut = templateClip.getTrimOut();
                    if (trimOut < trimIn) {
                        continue;
                    }
                    PROGRESS_COMPILE_TOTAL = 80;
                    break;
                }
            }

            mVideoSavePath = mGenerateTemplateFileFolder + File.separator + PathUtils.getTemplateVideoSaveName(mUuid);
            if (!EditorEngine.getInstance().compileTimeline(mTimeline, 0, mTimeline.getDuration(), mVideoSavePath, NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_CUSTOM,
                    EditorEngine.getInstance().getCustomHeight(720, mRatio), NvsStreamingContext.COMPILE_BITRATE_GRADE_HIGH,
                    NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_BUDDY_HOST_VIDEO_FRAME
                            | NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_DISABLE_ALIGN_VIDEO_SIZE
                            | NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_IGNORE_TIMELINE_VIDEO_SIZE, null)) {
                if (mOnExportListener != null) {
                    mOnExportListener.onCompleted(null, false);
                }
            }
        }

        @Override
        public void cancel() {
            if (mStreamingContext != null) {
                mStreamingContext.stop();
            }
            super.cancel();
        }

        @Override
        public void release() {
            EngineCallbackManager.get().unregisterCallbackObserver(mCallbackObserver);
            super.release();
        }
    }


    public class ResourceInfoPrepareTask extends ExportTask {

        @Override
        public void execute() {
            ThreadUtils.getIoPool().execute(new Runnable() {
                @Override
                public void run() {
                    if (mAssetInfos == null) {
                        mAssetInfos = getAssetsInfo(getPackageInfo());
                    }
                    if (isSampleTemplate) {
                        getInnerAssetsInfo(mAssetInfos);
                    }
                    prepareResourceInfo(mTemplateName, mTemplateDesc, mCoverPath, mAssetInfos);
                    doNext();
                }
            });
        }
    }

    public class DownLoadRemoteTask extends ExportTask{
        DownloadManager downloadManager = new DownloadManager();

        @Override
        public void execute() {
            final DownloadManager.DownloadParam<String> downloadParam = new DownloadManager.DownloadParam<>(mUuid);
            ThreadUtils.getIoPool().execute(new Runnable() {
                @Override
                public void run() {
                    String downloadFileFolder = PathUtils.getCloudVideoDownloadFileFolder();
                    if (!mLockTemplateClipList.isEmpty()) {
                        for (ExportTemplateClip templateClip : mLockTemplateClipList) {
                            String imagePath = templateClip.getImagePath();
                            if (imagePath != null && imagePath.endsWith("m3u8")) {
                                FileInfoBridge.FileInfo fileInfo = FileInfoBridge.getFileInfo(imagePath);
                                if (fileInfo != null && !TextUtils.isEmpty(fileInfo.remotePath)){
                                    downloadParam.appendParam(fileInfo.remotePath, downloadFileFolder);
                                }
                            }
                        }
                    }
                    if (downloadParam.getParams().isEmpty()) {
                        doNext();
                        return;
                    }
                    downloadManager.downloadFile(downloadParam, 100, new DownLoadObserver<String>(){
                        @Override
                        public void onSuccess(String tag, DownloadManager.DownloadParam<String> param) {
                            Map<String, DownloadManager.Param> params = param.getParams();
                            if (!CommonUtils.isEmpty(params)) {
                                for (ExportTemplateClip templateClip : mLockTemplateClipList) {
                                    String imagePath = templateClip.getImagePath();
                                    if (imagePath != null && imagePath.endsWith("m3u8")) {
                                        FileInfoBridge.FileInfo fileInfo = FileInfoBridge.getFileInfo(imagePath);
                                        if (fileInfo != null && !TextUtils.isEmpty(fileInfo.remotePath)){
                                            DownloadManager.Param param1 = params.get(fileInfo.remotePath);
                                            if (param1 != null && !TextUtils.isEmpty(param1.dstFile)) {
                                                templateClip.setImagePath(param1.dstFile);
                                                MeicamVideoTrack videoTrack = mTimeline.getVideoTrack(templateClip.getTrackIndex());
                                                if (videoTrack != null) {
                                                    MeicamVideoClip videoClip = videoTrack.getVideoClip(templateClip.getInPoint());
                                                    if (videoClip != null) {
                                                        videoClip.changeFilePath(param1.dstFile);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            doNext();
                        }

                        @Override
                        public void onFailed(String tag) {
                            if (mOnExportListener != null) {
                                mOnExportListener.onFailed(null);
                            }
                        }
                    });

                }
            });
        }

        @Override
        public void cancel() {
            List<String> data = new ArrayList<>();
            data.add(mUuid);
            downloadManager.cancelDownload(data);
        }
    }

    public class FinishTask extends ExportTask {

        @Override
        public void execute() {
            if (mOnExportListener != null) {
                mUploadParam = new TemplateUploadParam();
                try {
                    mUploadParam.templateDescFilePath =
                            mGenerateTemplateFileFolder + File.separator + FILE_TEMPLATE_INFO_DESC;
                } catch (Exception e) {
                    LogUtils.e(e);
                }
                mOnExportListener.onCompleted(mUploadParam, true);
            }
        }
    }

    /**
     * Task for export template file
     * <p>
     * 导出模板文件的task
     */
    public class ExportTemplate extends ExportTask {
        @Override
        public void execute() {
            doNvsTemplateExport();
        }

        @Override
        public void cancel() {
            if (mStreamingContext != null) {
                mStreamingContext.stop();
            }
            super.cancel();
        }
    }

    /**
     * Task for convert media
     * <p>
     * 转码的Task
     */
    public class ConvertMediaFile extends ExportTask {
        private NvsMediaFileConvertor mMediaFileConverter;
        private int currentIndex = 0;
        private float total = 0;
        private long currentTask = -1;

        @Override
        public void execute() {
            if (mMediaFileConverter == null) {
                mMediaFileConverter = new NvsMediaFileConvertor();
            }
            final List<ExportTemplateClip> convertMediaList = new ArrayList<>();
            for (ExportTemplateClip templateClip : mLockTemplateClipList) {
                String imagePath = templateClip.getImagePath();
                if (TextUtils.isEmpty(imagePath)) {
                    continue;
                }
                String fileName = FileUtils.getFileName(imagePath);
                if (!TextUtils.isEmpty(fileName) && !isImage(fileName)) {
                    long trimIn = templateClip.getTrimIn();
                    long trimOut = templateClip.getTrimOut();
                    if (trimOut < trimIn) {
                        continue;
                    }
                    convertMediaList.add(templateClip);
                }
            }
            total = convertMediaList.size();
            if (convertMediaList.isEmpty()) {
                doNext();
                return;
            }
            mMediaFileConverter.setMeidaFileConvertorCallback(new NvsMediaFileConvertor.MeidaFileConvertorCallback() {
                @Override
                public void onProgress(long l, float v) {
                    int converterTotal = 100 - PROGRESS_COMPILE_TOTAL;
                    int progress = (int) (currentIndex / total * converterTotal + converterTotal * v / total) + PROGRESS_COMPILE_TOTAL;
                    LogUtils.d("progress = " + progress + ", v = " + v);
                    if (mOnExportListener != null) {
                        mOnExportListener.onProgress(progress);
                    }
                }

                @Override
                public void onFinish(long l, String s, String s1, int errorCode) {
                    if (errorCode == NvsMediaFileConvertor.CONVERTOR_ERROR_CODE_NO_ERROR) {
                        if (currentIndex == convertMediaList.size() - 1) {
                            doNext();
                        } else {
                            currentTask = doConverter(convertMediaList, ++currentIndex);
                            if (currentTask < 0) {
                                if (mOnExportListener != null) {
                                    mOnExportListener.onCompleted(null, false);
                                }
                            }
                        }
                    } else {
                        if (mOnExportListener != null) {
                            mOnExportListener.onCompleted(null, false);
                        }
                    }
                }

                @Override
                public void notifyAudioMuteRage(long l, long l1, long l2) {

                }
            }, true);
            currentTask = doConverter(convertMediaList, currentIndex);
            if (currentTask < 0) {
                doNext();
            }
        }

        @Override
        public void cancel() {
            if (mMediaFileConverter != null && currentTask >= 0) {
                mMediaFileConverter.cancelTask(currentTask);
                mMediaFileConverter.release();
            }
            super.cancel();
        }

        @Override
        public void release() {
            if (mMediaFileConverter != null) {
                mMediaFileConverter.setMeidaFileConvertorCallback(null, false);
                mMediaFileConverter.release();
            }
            super.release();
        }

        private long doConverter(List<ExportTemplateClip> convertMediaList, int currentIndex) {
            if (!CommonUtils.isIndexAvailable(currentIndex, convertMediaList)) {
                return -1;
            }
            ExportTemplateClip templateClip = convertMediaList.get(currentIndex);
            String imagePath = templateClip.getImagePath();
            String fileName = FileUtils.getFileName(imagePath);
            String destFilePath = mRootResourceFilePath + File.separator + fileName;
            long trimIn = templateClip.getTrimIn();
            long trimOut = templateClip.getTrimOut();
            if (WhiteList.isCovert4KFileWhiteList(imagePath)) {
                Hashtable<String, Object> hashtable = new Hashtable<>();
                int height = 720;
                SettingParameter parameter = GsonUtils.fromJson(PreferencesManager.get().getSettingParams(), SettingParameter.class);
                if (parameter != null && parameter.getCompileResolution() == NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_1080) {
                    height = 1080;
                }
                hashtable.put(NvsMediaFileConvertor.CONVERTOR_CUSTOM_VIDEO_HEIGHT, height);
                return mMediaFileConverter.convertMeidaFile(imagePath, destFilePath, templateClip.isVideoReverse(), trimIn, trimOut, hashtable);
            } else {
                return mMediaFileConverter.convertMeidaFile(imagePath, destFilePath, templateClip.isVideoReverse(), trimIn, trimOut, null);
            }
        }
    }

    /**
     * The builder of task chain.
     * <p>
     * 任务链构建者
     */
    public static class TaskBuilder {
        private ExportTask mCurrentNode;
        private ExportTask mFirstNode;

        public TaskBuilder addTask(ExportTask nextNode) {
            if (mFirstNode == null) {
                mFirstNode = nextNode;
            }
            if (mCurrentNode != null) {
                mCurrentNode.setNextTask(nextNode);
            }
            mCurrentNode = nextNode;
            return this;
        }

        public ExportTask build() {
            return mFirstNode;
        }
    }

    /**
     * Whether is the file image.
     * 文件是否是图片
     *
     * @param fileName the file name
     * @return Is image or not.true：yes；false：no
     */
    private boolean isImage(@NonNull String fileName) {
        fileName = fileName.toLowerCase();
        return fileName.endsWith(".png")
                || fileName.endsWith(".jpg")
                || fileName.endsWith(".jpeg")
                || fileName.endsWith(".gif")
                || fileName.endsWith(".webp");
    }


    public interface OnExportListener {

        void onCompleted(TemplateUploadParam destPath, boolean success);

        void onCanceled(boolean isCanceled);

        void onFailed(NvsTimeline timeline);

        void onProgress(float progress);

        boolean isActive();
    }
}
