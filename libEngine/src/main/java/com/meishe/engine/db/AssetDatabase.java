package com.meishe.engine.db;

import androidx.room.Database;
import androidx.room.RoomDatabase;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZ<PERSON>
 * @CreateDate :2021/2/8 15:39
 * @Description :数据库 Database
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
@Database(entities = {AssetEntity.class, UserAssetsEntity.class,TimelineEntity.class}, version = 4, exportSchema = false)
public abstract class AssetDatabase extends RoomDatabase {
    public abstract AssetDao getAssetDao();

    public abstract UserAssetsDao getUserAssetsData();

    public abstract TimelineDataDao getTimelineDao();

}
