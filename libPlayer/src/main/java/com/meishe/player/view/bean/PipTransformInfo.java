package com.meishe.player.view.bean;

import android.graphics.PointF;

import java.util.List;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2021/6/17 15:59
 * @Description:
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class PipTransformInfo {


    /**
     * 四个角的坐标集合
     * The set of coordinates of four angles
     */
    private List<PointF> cornerPointList;

    public List<PointF> getCornerPointList() {
        return cornerPointList;
    }

    public PipTransformInfo setCornerPointList(List<PointF> cornerPointList) {
        this.cornerPointList = cornerPointList;
        return this;
    }

    public PointF getCenterPointF() {
        PointF pointF = new PointF();
        if (cornerPointList != null && cornerPointList.size() >= 4) {
            pointF.x = (cornerPointList.get(0).x + cornerPointList.get(2).x) / 2f;
            pointF.y = (cornerPointList.get(0).y + cornerPointList.get(2).y) / 2f;
        }
        return pointF;
    }
}
