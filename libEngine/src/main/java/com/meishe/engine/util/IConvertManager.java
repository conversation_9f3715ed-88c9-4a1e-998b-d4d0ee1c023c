package com.meishe.engine.util;

import android.text.TextUtils;

import com.meishe.engine.observer.ConvertFileObserver;

import java.util.HashMap;
import java.util.Hashtable;
import java.util.Map;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/8/18 17:08
 * @Description :转码接口 The interface for converting file manager
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface IConvertManager {
    /**
     * Convert file.
     *  转码文件
     * @param convertParam the convert param 转码参数
     */
    void convertFile(ConvertParam convertParam);

    /**
     * Cancel convert.
     * 取消转码
     */
    void cancelConvert();

    /**
     * 注册app进入后台的监听
     * register background observer
     *
     * @param observer the observer
     */
    void registerConvertFileObserver(ConvertFileObserver observer);

    /**
     * 注销app进入后台的监听
     * unregister background observer
     *
     * @param observer the observer
     */
    void unregisterConvertFileObserver(ConvertFileObserver observer);

    /**
     * 转化参数
     * Convert param
     */
    class ConvertParam {
        /**
         * The Param map.
         */
        public Map<String, Param> paramMap;

        /**
         * Instantiates a new Convert param.
         */
        public ConvertParam() {
            paramMap = new HashMap<>();
        }

        /**
         * Gets param map.
         *
         * @return the param map
         */
        public Map<String, Param> getParamMap() {
            return paramMap;
        }

        /**
         * Append param.
         *
         * @param param the param
         */
        public void appendParam(Param param) {
            if (param != null && !TextUtils.isEmpty(param.getSrcFile())) {
                paramMap.put(param.getSrcFile(), param);
            }
        }

        /**
         * Append param.
         *
         * @param srcPath          the src path
         * @param dstPath          the dst path
         * @param isReverseConvert the is reverse convert
         */
        public void appendParam(String srcPath, String dstPath, boolean isReverseConvert) {
            appendParam(srcPath, dstPath, isReverseConvert, -1, -1, null);
        }

        /**
         * Append param.
         *
         * @param srcPath          the src path
         * @param dstPath          the dst path
         * @param isReverseConvert the is reverse convert
         * @param configurations the configurations
         */
        public void appendParam(String srcPath, String dstPath, boolean isReverseConvert, Hashtable<String, Object> configurations) {
            appendParam(srcPath, dstPath, isReverseConvert, -1, -1, configurations);
        }

        /**
         * Append param.
         *
         * @param srcPath          the src path
         * @param dstPath          the dst path
         * @param isReverseConvert the is reverse convert
         * @param fromPosition     the from position
         */
        public void appendParam(String srcPath, String dstPath, boolean isReverseConvert,
                                long fromPosition) {
            appendParam(srcPath, dstPath, isReverseConvert, fromPosition, -1, null);
        }

        /**
         * Append param.
         *
         * @param srcPath          the src path
         * @param dstPath          the dst path
         * @param isReverseConvert the is reverse convert
         * @param fromPosition     the from position
         * @param configurations   the configurations
         */
        public void appendParam(String srcPath, String dstPath, boolean isReverseConvert,
                                long fromPosition, Hashtable<String, Object> configurations) {
            appendParam(srcPath, dstPath, isReverseConvert, fromPosition, -1, configurations);
        }

        /**
         * Append param.
         *
         * @param srcPath          the src path
         * @param dstPath          the dst path
         * @param isReverseConvert the is reverse convert
         * @param fromPosition     the from position
         * @param toPosition       the to position
         */
        public void appendParam(String srcPath, String dstPath, boolean isReverseConvert,
                                long fromPosition, long toPosition) {
            appendParam(srcPath, dstPath, isReverseConvert, fromPosition, toPosition, null);
        }

        private void appendParam(String srcPath, String dstPath, boolean isReverseConvert,
                                 long fromPosition, long toPosition, Hashtable<String, Object> configurations) {
            Param param = new Param();
            param.setSrcFile(srcPath);
            param.setDstFile(dstPath);
            param.setReverseConvert(isReverseConvert);
            param.setFromPosition(fromPosition);
            param.setToPosition(toPosition);
            param.setConfigurations(configurations);
            paramMap.put(srcPath, param);
        }

        /**
         * The type Param.
         */
        public static class Param {
            private long taskId;
            private int progress;
            private boolean finish;
            private boolean success;
            /**
             * 是否再次转码已执行执行
             * Is reconvert execute.
             */
            private boolean isReconvertExecute;
            /**
             * 源文件路径
             * Source file path
             */
            private String srcFile;
            /**
             * 生成文件路径
             * The path to the generated file
             */
            public String dstFile;
            /**
             * 是否倒放转码
             * Whether to invert the code
             */
            private boolean isReverseConvert;

            /**
             * 转码的起点时间
             * The starting time of transcoding
             */
            private long fromPosition;
            /**
             * 转码的结束时间
             * End time of transcoding
             */
            private long toPosition = -1;

            /**
             * 转码的参数集合
             * The set of parameters for transcoding
             */
            private Hashtable<String, Object> configurations;

            /**
             * Gets task id.
             *
             * @return the task id
             */
            public long getTaskId() {
                return taskId;
            }

            /**
             * Sets task id.
             *
             * @param taskId the task id
             */
            public void setTaskId(long taskId) {
                this.taskId = taskId;
            }

            /**
             * Gets progress.
             *
             * @return the progress
             */
            public int getProgress() {
                return progress;
            }

            /**
             * Sets progress.
             *
             * @param progress the progress
             */
            public void setProgress(int progress) {
                this.progress = progress;
            }

            /**
             * Is finish boolean.
             *
             * @return the boolean
             */
            public boolean isFinish() {
                return finish;
            }

            /**
             * Sets finish.
             *
             * @param finish the finish
             */
            public void setFinish(boolean finish) {
                this.finish = finish;
            }

            /**
             * Gets src file.
             *
             * @return the src file
             */
            public String getSrcFile() {
                return srcFile;
            }

            /**
             * Is success boolean.
             *
             * @return the boolean
             */
            public boolean isSuccess() {
                return success;
            }

            /**
             * Sets success.
             *
             * @param success the success
             */
            public void setSuccess(boolean success) {
                this.success = success;
            }

            /**
             * Sets src file.
             *
             * @param srcFile the src file
             */
            public void setSrcFile(String srcFile) {
                this.srcFile = srcFile;
            }

            /**
             * Gets dst file.
             *
             * @return the dst file
             */
            public String getDstFile() {
                return dstFile;
            }

            /**
             * Sets dst file.
             *
             * @param dstFile the dst file
             */
            public void setDstFile(String dstFile) {
                this.dstFile = dstFile;
            }

            /**
             * Is reverse convert boolean.
             *
             * @return the boolean
             */
            public boolean isReverseConvert() {
                return isReverseConvert;
            }

            /**
             * Sets reverse convert.
             *
             * @param reverseConvert the reverse convert
             */
            public void setReverseConvert(boolean reverseConvert) {
                isReverseConvert = reverseConvert;
            }

            /**
             * Is reconvert execute boolean.
             *
             * @return the boolean
             */
            public boolean isReconvertExecute() {
                return isReconvertExecute;
            }

            /**
             * Sets reconvert execute.
             *
             * @param reconvertExecute the reconvert execute
             */
            public void setReconvertExecute(boolean reconvertExecute) {
                this.isReconvertExecute = reconvertExecute;
            }

            /**
             * Gets from position.
             *
             * @return the from position
             */
            public long getFromPosition() {
                return fromPosition;
            }

            /**
             * Sets from position.
             *
             * @param fromPosition the from position
             */
            public void setFromPosition(long fromPosition) {
                this.fromPosition = fromPosition;
            }

            /**
             * Gets to position.
             *
             * @return the to position
             */
            public long getToPosition() {
                return toPosition;
            }

            /**
             * Sets to position.
             *
             * @param toPosition the to position
             */
            public void setToPosition(long toPosition) {
                this.toPosition = toPosition;
            }

            /**
             * Gets configurations.
             *
             * @return the configurations
             */
            public Hashtable<String, Object> getConfigurations() {
                return configurations == null ? configurations = new Hashtable<String, Object>() : configurations;
            }

            /**
             * Sets configurations.
             *
             * @param configurations the configurations
             */
            public void setConfigurations(Hashtable<String, Object> configurations) {
                this.configurations = configurations;
            }
        }
    }
}
