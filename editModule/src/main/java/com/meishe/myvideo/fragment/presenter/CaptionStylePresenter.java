package com.meishe.myvideo.fragment.presenter;

import android.graphics.PointF;
import android.text.TextUtils;

import com.meicam.sdk.NvsColor;
import com.meicam.sdk.NvsVideoResolution;
import com.meishe.base.model.Presenter;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.command.CaptionCommand;
import com.meishe.engine.util.ColorUtil;
import com.meishe.engine.util.CoordinateUtil;
import com.meishe.logic.utils.UMengUtils;
import com.meishe.myvideo.fragment.iview.CaptionStyleView;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/1/13 13:29
 * @Description :字幕样式逻辑处理类 the caption style presenter
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CaptionStylePresenter extends Presenter<CaptionStyleView> {
    public static final float CAPTION_STANDARD_SPACING = 0;
    public static final float CAPTION_LINE_STANDARD_SPACING = 0;
    private MeicamCaptionClip mCaptionClip;
    private MeicamCaptionClip mOldCaptionClip;
    private final EditorEngine mEngine;

    public CaptionStylePresenter(MeicamCaptionClip captionClip) {
        mCaptionClip = captionClip;
        if (captionClip != null) {
            mOldCaptionClip = (MeicamCaptionClip) captionClip.clone();
        }
        mEngine = EditorEngine.getInstance();
    }

    public void updateCaptionClip(MeicamCaptionClip captionClip) {
        if (captionClip == null) {
            mOldCaptionClip = null;
        } else {
            if (!captionClip.equals(mCaptionClip)) {
                mOldCaptionClip = (MeicamCaptionClip) captionClip.clone();
            }
        }
        mCaptionClip = captionClip;
    }

    public MeicamCaptionClip getCaptionClip() {
        return mCaptionClip;
    }

    /**
     * 设置字幕加粗
     * Set caption bold
     *
     * @param apply true bold ,false not
     */
    public void setCaptionBold(boolean apply) {
        if (mCaptionClip == null) {
            return;
        }
        Boolean oldCaptionBold = mOldCaptionClip == null ? null : mOldCaptionClip.isBold();
        mEngine.changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_IS_BOLD, oldCaptionBold, apply);
    }

    /**
     * 设置字幕为斜体
     * Set caption italics
     *
     * @param apply true bold ,false not
     */
    public void setCaptionItalics(boolean apply) {
        if (mCaptionClip == null) {
            return;
        }
        Boolean oldCaptionItalics = mOldCaptionClip == null ? null : mOldCaptionClip.isItalic();
        mEngine.changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_IS_ITALIC, oldCaptionItalics, apply);
    }

    /**
     * 设置字幕阴影
     * Set caption shadow
     *
     * @param apply true bold ,false not
     */
    public void setCaptionShadow(boolean apply) {
        if (mCaptionClip == null) {
            return;
        }
        Boolean oldCaptionShadow = mOldCaptionClip == null ? null : mOldCaptionClip.isShadow();
        mEngine.changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_IS_SHADOW, oldCaptionShadow, apply);
    }

    /**
     * 设置字幕字体
     * Set caption font
     *
     * @param fontPath the fontPath
     */
    public void setCaptionFont(String fontPath) {
        if (mCaptionClip == null) {
            return;
        }
        String oldCaptionFont = mOldCaptionClip == null ? null : mOldCaptionClip.getFontPath();
        mEngine.changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_FONT_PATH, oldCaptionFont, fontPath);
    }

    /**
     * 设置字幕字体颜色
     * Set caption color
     *
     * @param colorValue the font color
     */
    public void setCaptionColor(String colorValue) {
        if (mCaptionClip == null || TextUtils.isEmpty(colorValue)) {
            return;
        }
        float[] colorArray = ColorUtil.stringColorToColor(colorValue);
        float[] oldColor = mOldCaptionClip == null ? null : mOldCaptionClip.getTextColor();
        mEngine.changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_TEXT_COLOR, oldColor, colorArray);
    }

    /**
     * 设置字幕不透明度
     * Set caption opacity
     *
     * @param newDegree the opacity degree
     */
    public void setCaptionOpacity(float newDegree) {
        if (mCaptionClip != null) {
            float[] textColor = mCaptionClip.getTextColor();
            if (textColor == null || textColor.length != 4) {
                return;
            }
            textColor[3] = newDegree;
            float[] oldColor = mOldCaptionClip == null ? null : mOldCaptionClip.getTextColor();
            mEngine.changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_TEXT_COLOR, oldColor, textColor);
        }
    }

    /**
     * 获取字幕不透明度
     * Get the caption opacity
     *
     * @return the opacity degree
     */
    public float getCaptionOpacity() {
        if (mCaptionClip == null) {
            return 1;
        }
        float[] textColor = mCaptionClip.getTextColor();
        if (textColor == null || textColor.length != 4) {
            return 1;
        }
        return textColor[3];
    }

    /**
     * 设置字幕字体边界颜色
     * Set caption out line color
     *
     * @param colorValue the font color
     */
    public void setCaptionOutLineColor(String colorValue) {
        if (mCaptionClip == null || TextUtils.isEmpty(colorValue)) {
            return;
        }
        float[] colorArray = ColorUtil.stringColorToColor(colorValue);
        float[] oldColor = mOldCaptionClip == null ? null : mOldCaptionClip.getOutlineColor();
        Boolean oldIsOutline = mOldCaptionClip == null ? null : mOldCaptionClip.isOutline();
        mEngine.changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_IS_OUTLINE, oldIsOutline, true);
        mEngine.changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_OUTLINE_COLOR, oldColor, colorArray);
    }

    /**
     * 设置字幕字体边界宽度颜色
     * Set caption out line width
     *
     * @param width the width
     */
    public void setCaptionOutLineWidth(int width) {
        if (mCaptionClip == null) {
            return;
        }
        Float oldOutlineWidth = mOldCaptionClip == null ? null : mOldCaptionClip.getOutlineWidth();
        mEngine.changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_OUTLINE_WIDTH, oldOutlineWidth, width / 10f);
    }

    /**
     * 设置字幕字体边界不透明度
     * Set caption out line opacity
     *
     * @param degree the opacity degree
     */
    public void setCaptionOutLineOpacity(int degree) {
        if (mCaptionClip == null) {
            return;
        }
        float[] outlineColor = mCaptionClip.getOutlineColor();
        if (outlineColor == null || outlineColor.length != 4) {
            return;
        }
        outlineColor[3] = degree / 100.0f;
        float[] oldColor = mOldCaptionClip == null ? null : mOldCaptionClip.getOutlineColor();
        Boolean oldIsOutline = mOldCaptionClip == null ? null : mOldCaptionClip.isOutline();
        mEngine.changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_IS_OUTLINE, oldIsOutline, true);
        mEngine.changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_OUTLINE_COLOR, oldColor, outlineColor);
    }

    /**
     * 清除字幕字体边界设置
     * Clear the caption out line
     */
    public void clearCaptionOutLine() {
        if (mCaptionClip == null) {
            return;
        }
        Boolean oldIsOutline = mOldCaptionClip == null ? null : mOldCaptionClip.isOutline();
        mEngine.changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_IS_OUTLINE, oldIsOutline, false);
        float[] outlineColor = mCaptionClip.getOutlineColor();
        if (outlineColor == null || outlineColor.length != 4) {
            return;
        }
        outlineColor[3] = 0;
        float[] oldColor = mOldCaptionClip == null ? null : mOldCaptionClip.getOutlineColor();
        mEngine.changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_OUTLINE_COLOR, oldColor, outlineColor);
    }

    /**
     * 获取字幕边界不透明度
     * Get the caption outline opacity
     *
     * @return the opacity degree
     */
    public float getCaptionOutLineOpacity() {
        if (mCaptionClip == null) {
            return 1;
        }
        float[] outlineColor = mCaptionClip.getOutlineColor();
        if (outlineColor == null || outlineColor.length != 4) {
            return 1;
        }
        return outlineColor[3];
    }

    /**
     * 获取字幕边界宽度
     * Get the caption outline width
     *
     * @return the caption outline width
     */
    public float getCaptionOutLineWidth() {
        if (mCaptionClip == null) {
            return 5;
        }
        return mCaptionClip.getOutlineWidth();
    }

    /**
     * 设置字幕背景颜色
     * Set caption background color
     *
     * @param colorValue the font color
     */
    public void setCaptionBackgroundColor(String colorValue) {
        if (mCaptionClip == null || TextUtils.isEmpty(colorValue)) {
            return;
        }
        float[] color = ColorUtil.stringColorToColor(colorValue);
        float[] oldColor = mOldCaptionClip == null ? null : mOldCaptionClip.getBackgroundColor();
        mEngine.changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_BACKGROUND_COLOR, oldColor, color);
    }

    /**
     * 设置字幕背景不透明度
     * Set caption background opacity
     *
     * @param degree the opacity degree
     */
    public void setCaptionBackgroundOpacity(int degree) {
        if (mCaptionClip == null) {
            return;
        }
        float[] textColor = mCaptionClip.getBackgroundColor();
        if (textColor == null || textColor.length != 4) {
            UMengUtils.generateCustomLog("handleCaptionBackgroundColor  curCaption is invisible!");
            return;
        }
        textColor[3] = degree / 100.0f;
        float[] oldColor = mOldCaptionClip == null ? null : mOldCaptionClip.getBackgroundColor();
        mEngine.changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_BACKGROUND_COLOR, oldColor, textColor);
    }

    /**
     * 设置字幕背景圆角
     * Set caption background round
     *
     * @param round the background round corner
     */
    public void setCaptionBackgroundCorner(int round) {
        if (mCaptionClip == null) {
            return;
        }
        Float oldRadius = mOldCaptionClip == null ? null : mOldCaptionClip.getBackgroundRadius();
        List<PointF> rectangleVertices = mCaptionClip.getCaptionBoundingVertices(2);
        float corner = round * 1F;
        if (rectangleVertices != null && rectangleVertices.size() == 4) {
            MeicamTimeline currentTimeline = mEngine.getCurrentTimeline();
            NvsVideoResolution videoResolution = currentTimeline.getVideoResolution();
            PointF leftTopPoint = CoordinateUtil.mapCanonicalToView(rectangleVertices.get(0), videoResolution.imageWidth, videoResolution.imageHeight);
            PointF rightBottomPoint = CoordinateUtil.mapCanonicalToView(rectangleVertices.get(2), videoResolution.imageWidth, videoResolution.imageHeight);
            float rotation = mCaptionClip.getRotation();
            if (rotation != 0) {
                PointF centerFx = new PointF((leftTopPoint.x + rightBottomPoint.x) / 2, (leftTopPoint.y + rightBottomPoint.y) / 2);
                CoordinateUtil.transformData(leftTopPoint, centerFx, 1, rotation);
                CoordinateUtil.transformData(rightBottomPoint, centerFx, 1, rotation);
            }
            corner = Math.min(Math.abs(rightBottomPoint.y - leftTopPoint.y), Math.abs(rightBottomPoint.x - leftTopPoint.x)) / 2F * round / 100F;
        }
        mEngine.changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_BACKGROUND_RADIUS, oldRadius, corner);
    }

    /**
     * 清除字幕背景设置
     * Clear the caption background
     */
    public void clearCaptionBackground() {
        if (mCaptionClip == null) {
            return;
        }
        float[] backgroundColor = mCaptionClip.getBackgroundColor();
        if (backgroundColor == null || backgroundColor.length != 4) {
            return;
        }
        backgroundColor[3] = 0;
        float[] oldColor = mOldCaptionClip == null ? null : mOldCaptionClip.getBackgroundColor();
        mEngine.changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_BACKGROUND_COLOR, oldColor, backgroundColor);
    }

    /**
     * 获取字幕背景不透明度
     * Get the caption background opacity
     *
     * @return the opacity degree
     */
    public float getCaptionBackgroundOpacity() {
        if (mCaptionClip == null) {
            return 1;
        }
        float[] backgroundColor = mCaptionClip.getBackgroundColor();
        if (backgroundColor == null || backgroundColor.length != 4) {
            return 1;
        }
        return backgroundColor[3];
    }

    /**
     * 获取字幕背景圆角
     * Get the caption outline opacity
     *
     * @return the opacity degree
     */
    public float getCaptionBackgroundCorner() {
        if (mCaptionClip == null) {
            return 1;
        }
        float round = mCaptionClip.getBackgroundRadius();
        List<PointF> rectangleVertices = mCaptionClip.getCaptionBoundingVertices(2);
        if (rectangleVertices != null && rectangleVertices.size() == 4) {
            MeicamTimeline currentTimeline = mEngine.getCurrentTimeline();
            NvsVideoResolution videoResolution = currentTimeline.getVideoResolution();
            PointF leftTopPoint = CoordinateUtil.mapCanonicalToView(rectangleVertices.get(0), videoResolution.imageWidth, videoResolution.imageHeight);
            PointF rightBottomPoint = CoordinateUtil.mapCanonicalToView(rectangleVertices.get(2), videoResolution.imageWidth, videoResolution.imageHeight);
            float rotation = mCaptionClip.getRotation();
            if (rotation != 0) {
                PointF centerFx = new PointF((leftTopPoint.x + rightBottomPoint.x) / 2, (leftTopPoint.y + rightBottomPoint.y) / 2);
                CoordinateUtil.transformData(leftTopPoint, centerFx, 1, rotation);
                CoordinateUtil.transformData(rightBottomPoint, centerFx, 1, rotation);
            }
            return round * 100F / (Math.min(Math.abs(rightBottomPoint.y - leftTopPoint.y), Math.abs(rightBottomPoint.x - leftTopPoint.x)) / 2F);
        }

        return 1;
    }

    /**
     * 设置字幕字体水平方向间隔
     * Set the caption word space
     *
     * @param space the caption word space
     */
    public void setCaptionWordSpace(float space) {
        if (mCaptionClip == null) {
            return;
        }
        if (mCaptionClip.getLetterSpacingType() == MeicamCaptionClip.LETTER_SPACING_TYPE_PERCENTAGE) {
            space = space + 100;
        }
        Float oldSpace = mOldCaptionClip == null ? null : mOldCaptionClip.getLetterSpacing();
        mEngine.changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_LETTER_SPACING, oldSpace, space);
    }

    /**
     * 获取字幕字体水平方向间隔
     * Get the caption word space
     *
     * @return the caption word space
     */
    public float getCaptionWordSpace() {
        if (mCaptionClip == null) {
            return CAPTION_STANDARD_SPACING;
        }
        return mCaptionClip.getLetterSpacing();
    }

    /**
     * 设置字幕行间隔
     * Set the caption line space
     *
     * @param space the caption word space
     */
    public void setCaptionLineSpace(float space) {
        if (mCaptionClip == null) {
            return;
        }
        Float oldSpace = mOldCaptionClip == null ? null : mOldCaptionClip.getLineSpacing();
        mEngine.changeCaptionParam(mCaptionClip, CaptionCommand.PARAM_LINE_SPACING, oldSpace, space);
    }

    /**
     * 获取字幕行间隔
     * Get the caption line space
     *
     * @return the caption line space
     */
    public float getCaptionLineSpace() {
        if (mCaptionClip == null) {
            return CAPTION_LINE_STANDARD_SPACING;
        }
        return mCaptionClip.getLineSpacing();
    }

    /**
     * 设置字幕位置
     * Set the caption position
     *
     * @param local          the caption local
     * @param keyFrameAtTime the key frame at time 关键帧所在时间点
     */
    public void setCaptionLocal(int local, long keyFrameAtTime) {
        mEngine.handleCaptionPosition(mCaptionClip, mOldCaptionClip, local, keyFrameAtTime);
    }

    /**
     * 获取字幕文字颜色值
     * get the Caption Text color
     */
    public String getCaptionTextColor(boolean ignoreAlpha) {
        if (mCaptionClip == null) {
            LogUtils.d("mCaptionClip is null");
            return "";
        }
        NvsColor nvsColor = ColorUtil.colorFloatToNvsColor(mCaptionClip.getTextColor());
        ;
        if (ignoreAlpha) {
            nvsColor.a = 1;
        }
        return ColorUtil.nvsColorToHexString(nvsColor);
    }

    /**
     * 获取字幕背景颜色值
     * get the Caption Background color
     */
    public String getCaptionBackgroundColor() {
        if (mCaptionClip == null) {
            return "";
        }
        return ColorUtil.nvsColorToHexString(ColorUtil.colorFloatToNvsColor(mCaptionClip.getBackgroundColor()));
    }

    /**
     * 获取字幕描边颜色值
     * get the Caption Outline color
     */
    public String getCaptionOutlineColor() {
        if (mCaptionClip == null) {
            return "";
        }
        return ColorUtil.nvsColorToHexString(ColorUtil.colorFloatToNvsColor(mCaptionClip.getOutlineColor()));
    }


    /**
     * 是否存在字幕描边
     * Has caption outline color boolean.
     *
     * @return the boolean
     */
    public boolean hasCaptionOutlineColor() {
        if (mCaptionClip == null) {
            return false;
        }
        return mCaptionClip.isOutline();
    }

    /**
     * 是否存在字幕背景
     * Has caption Background color boolean.
     *
     * @return the boolean
     */
    public boolean hasCaptionBackgroundColor() {
        if (mCaptionClip == null) {
            return false;
        }
        float[] backgroundColor = mCaptionClip.getBackgroundColor();
        if (backgroundColor == null || backgroundColor.length != 4) {
            return false;
        }
        return backgroundColor[3] != 0;
    }
}
