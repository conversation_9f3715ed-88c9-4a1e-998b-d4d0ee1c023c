package com.meishe.engine.bean;

import com.meishe.base.utils.LogUtils;
import com.meishe.engine.constant.NvsConstants;

import static com.meishe.base.constants.Constants.TRACK_INDEX_MAIN;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/6/16 14:02
 */
public class CommonData {
    /**
     * 主轨道index
     * The constant MAIN_TRACK_INDEX.
     */

    public static final int MAIN_TRACK_INDEX = TRACK_INDEX_MAIN;
    /**
     * 系统长按时长
     * The constant CLICK_LONG.
     */
    public static final int CLICK_LONG = 500;
    /**
     * 点击时长
     * The constant CLICK_TIME.
     */
    public static final int CLICK_TIME = 200;
    /**
     * 点击位置限制
     * The constant CLICK_MOVE.
     */
    public static final int CLICK_MOVE = 10;
    /**
     * 1秒的毫秒展开
     * The constant TIMEBASE.
     */
    public static final long TIMEBASE = 1000000L;
    /**
     * 最大轨道数
     * The constant TRACK_MAX.
     */
    public static final int TRACK_MAX = 5;
    /**
     * 一帧的时长
     * The constant ONE_FRAME.
     */
    public static final long ONE_FRAME = 4000L;
    /**
     * 默认的clip时长
     * The constant DEFAULT_LENGTH.
     */
    public static final long DEFAULT_LENGTH = 4 * TIMEBASE;
    /**
     * 默认的特效时长
     * The constant DEFAULT_LENGTH_FX.
     */
    public static final long DEFAULT_LENGTH_FX = 3 * TIMEBASE;

    /**
     * 默认的trimIn
     * The constant DEFAULT_TRIM_IN.
     */
    public static final long DEFAULT_TRIM_IN = TIMEBASE * 60 * 60L;

    /**
     * 最小的展示duration
     * The constant MIN_SHOW_LENGTH_DURATION.
     */
    public static final long MIN_SHOW_LENGTH_DURATION = (long) (CommonData.TIMEBASE * 0.8);
    /**
     * 定格最小的展示duration
     * The constant FREEZE_MIN_SHOW_LENGTH_DURATION.
     */
    public static final long FREEZE_MIN_SHOW_LENGTH_DURATION = (long) (CommonData.TIMEBASE * 0.1);


    /**
     * CLip组合动画最小时长为0.1s
     * The minimum length of clip combination animation is 0.1s
     */
    public static final long VIDEO_ANIMATION_COMP_MIN_DURATION = (long) (CommonData.TIMEBASE * 0.1);
    /**
     * CLip 入动画最小时长为0.5s
     * The minimum length of clip combination animation is 0.1s
     */
    public static final long VIDEO_ANIMATION_IN_DEFAULT_DURATION = (long) (CommonData.TIMEBASE * 0.5);
    /**
     * CLip 出动画最小时长为0.5s
     * The minimum length of clip combination animation is 0.1s
     */
    public static final long VIDEO_ANIMATION_OUT_DEFAULT_DURATION = (long) (CommonData.TIMEBASE * 0.5);

    /**
     * 贴纸组合动画最小时长为0.1s
     * The minimum length of sticker combination animation is 0.1s
     */
    public static final long STICKER_ANIMATION_COMP_MIN_DURATION = (long) (CommonData.TIMEBASE * 0.1);
    /**
     * 贴纸入动画默认时长为0.5s
     * The default length of sticker in animation is 0.5s
     */
    public static final long STICKER_ANIMATION_IN_DEFAULT_DURATION = (long) (CommonData.TIMEBASE * 0.5);
    /**
     * 贴纸出动画默认时长为0.5s
     * The default length of sticker out animation is 0.5s
     */
    public static final long STICKER_ANIMATION_OUT_DEFAULT_DURATION = (long) (CommonData.TIMEBASE * 0.5);
    /**
     * 贴纸组合动画最小时长为0.6s
     * The default length of sticker combination animation is 0.6s
     */
    public static final long STICKER_ANIMATION_COMP_DEFAULT_DURATION = (long) (CommonData.TIMEBASE * 0.6);

    /**
     * 时间线默认帧率
     * Default frame rate of timeline
     */
    public static final int DEFAULT_TIMELINE_FPS = 30;


    /**
     * AR sense
     * The constant VIDEO_FX_AR_SCENE.
     */
    public final static String VIDEO_FX_AR_SCENE = NvsConstants.VIDEO_FX_AR_SCENE;

    /**
     * 美颜特技
     * The constant VIDEO_FX_BEAUTY_EFFECT.
     */
    public final static String VIDEO_FX_BEAUTY_EFFECT = NvsConstants.VIDEO_FX_BEAUTY_EFFECT;

    /**
     * 快速美颜模式
     * The constant VIDEO_FX_BEAUTY_FAST_MODE.
     */
    public final static String VIDEO_FX_BEAUTY_FAST_MODE = NvsConstants.VIDEO_FX_BEAUTY_FAST_MODE;

    /**
     * 快速人脸检测模式
     * The constant VIDEO_FX_BEAUTY_FAST_FACE_DETECTION.
     */
    public final static String VIDEO_FX_BEAUTY_FAST_FACE_DETECTION = NvsConstants.VIDEO_FX_BEAUTY_FAST_FACE_DETECTION;

    /**
     * 美型
     * The constant VIDEO_FX_BEAUTY_SHAPE.
     */
    public final static String VIDEO_FX_BEAUTY_SHAPE = NvsConstants.VIDEO_FX_BEAUTY_SHAPE;
    /**
     * 高级美型
     * Advanced beauty
     */
    public final static String VIDEO_FX_BEAUTY_SHAPE_MESH = NvsConstants.FACE_MESH_INTERNAL_ENABLED;

    /**
     * 是否是新美型数据标记
     * Is a new  beauty shape data tag
     */
    public final static String VIDEO_FLAG_IS_NEW_BEAUTY_SHAPE_DATA = "Is New Beauty Shape Data";

    /**
     * 贴纸封面路径的attachment key
     * The constant ATTACHMENT_KEY_STICKER_COVER_PATH.
     * The attachment key that identifies sticker cover path.
     */
    public final static String ATTACHMENT_KEY_STICKER_COVER_PATH = "attachment_key_sticker_cover_path";
    /**
     * 标识特效是否是AR scene 的attachment key
     * The constant ATTACHMENT_KEY_VIDEO_CLIP_AR_SCENE.
     * The attachment key that identifies whether it is the AR scene.
     */
    public final static String ATTACHMENT_KEY_VIDEO_CLIP_AR_SCENE = "attachment_key_video_clip_ar_scene";
    /**
     * 标识包特效路径的attachment key
     * The constant ATTACHMENT_KEY_PACKAGE_PATH.
     * The attachment key that identifies the assets package path.
     */
    public final static String ATTACHMENT_KEY_PACKAGE_PATH = "attachment_key_video_package_path";

    /**
     * 标识clip裁剪比例的attachment key
     * The constant ATTACHMENT_KEY_VIDEO_RATIO.
     * The attachment key that identifies the ratio of clip roi.
     */
    public final static String ATTACHMENT_KEY_VIDEO_RATIO = "attachment_key_video_ratio";

    /**
     * 是否是封面字幕的attachment key
     *The attachment key that identifies whether it is the cover caption.
     */
    public final static String ATTACHMENT_KEY_IS_COVER_CAPTION = "attachment_key_is_cover_caption";

    /**
     * 轨道类型
     * The constant TRACK_VIDEO.
     */
    public static final String TRACK_VIDEO = "videoTrack";
    /**
     * 音频轨道
     * The constant TRACK_AUDIO.
     */
    public static final String TRACK_AUDIO = "audioTrack";
    /**
     * timeline 特效轨道
     * The constant TRACK_TIMELINE_FX.
     */
    public static final String TRACK_TIMELINE_FX = "timelineVideoFxTrack";
    /**
     * 贴纸字幕轨道
     * The constant TRACK_STICKER_CAPTION.
     */
    public static final String TRACK_STICKER_CAPTION = "stickerCaptionTrack";
    /**
     * timeline滤镜轨道
     * The constant TRACK_TIMELINE_FILTER_ADJUST.
     */
    public static final String TRACK_TIMELINE_FILTER_ADJUST = "timelineFilterAdjust";
    /**
     * The constant CLIP_TIMELINE_FX.
     * clip类型video audio timelineVideoFx caption compoundCaption  sticker
     */
    public static final String CLIP_TIMELINE_FX = "timelineVideoFx";
    /**
     * 字幕
     * The constant CLIP_CAPTION.
     */
    public static final String CLIP_CAPTION = "caption";
    /**
     * 组合字幕
     * The constant CLIP_COMPOUND_CAPTION.
     */
    public static final String CLIP_COMPOUND_CAPTION = "compound_caption";
    /**
     * 贴纸
     * The constant CLIP_STICKER.
     */
    public static final String CLIP_STICKER = "sticker";
    /**
     * 视频
     * The constant CLIP_VIDEO.
     */
    public static final String CLIP_VIDEO = "video";
    /**
     * 音频
     * The constant CLIP_AUDIO.
     */
    public static final String CLIP_AUDIO = "audio";
    /**
     * 图片
     * The constant CLIP_IMAGE.
     */
    public static final String CLIP_IMAGE = "image";
    /**
     * 占位
     * The constant CLIP_HOLDER.
     */
    public static final String CLIP_HOLDER = "holder";
    /**
     * 通用
     * The constant CLIP_COMMON.
     */
    public static final String CLIP_COMMON = "common";
    /**
     * 滤镜
     * The constant CLIP_FILTER.
     */
    public static final String CLIP_FILTER = "filter";
    /**
     * 调节
     * The constant CLIP_ADJUST.
     */
    public static final String CLIP_ADJUST = "adjust";
    /**
     * 默认补黑素材
     * Blacks the material by default
     */
    public static final String EMPTY_THUMBNAIL_IMAGE = "assets:/black.png";

    /**
     * 默认补黑素材,文件名是唯一的
     * The constant IMAGE_BLACK_FILE_NAME.
     *  Blacks the material by default.The file name is unique.
     */
    public static final String IMAGE_BLACK_FILE_NAME = "9d388abb-ab09-4b9f-953e-daba77e9037a.png";

    /**
     * 默认补黑素材,文件名是唯一的
     * Blacks the material by default.The file name is unique.
     */
    public static final String IMAGE_BLACK_HOLDER = "assets:/" + IMAGE_BLACK_FILE_NAME;


    /**
     * 字幕通用类型
     * The constant TYPE_COMMON_CAPTION.
     */
    public static final int TYPE_COMMON_CAPTION = 0;
    /**
     * AI字幕
     * The constant TYPE_AI_CAPTION.
     */
    public static final int TYPE_AI_CAPTION = 1;


    /**
     * 转场
     * The constant TRANSITION.
     */
    public static final String TRANSITION = "transition";

    /**
     * 纯色背景类型
     * The color background type
     */
    public final static int STORYBOARD_BACKGROUND_TYPE_COLOR = 0;
    /**
     * 图片背景类型
     * The constant STORYBOARD_BACKGROUND_TYPE_IMAGE.
     * The image background type
     */
    public final static int STORYBOARD_BACKGROUND_TYPE_IMAGE = 1;
    /**
     * 模糊背景类型
     * The constant STORYBOARD_BACKGROUND_TYPE_BLUR.
     * The blur background type
     */
    public final static int STORYBOARD_BACKGROUND_TYPE_BLUR = 2;

    /**
     * 内置特效
     * The constant TYPE_BUILD_IN.
     */
    public static final String TYPE_BUILD_IN = "builtin";
    /**
     * 包特效
     * The constant TYPE_PACKAGE.
     */
    public static final String TYPE_PACKAGE = "package";
    /**
     * 原始内置特效
     * The constant TYPE_RAW_BUILTIN.
     */
    public static final String TYPE_RAW_BUILTIN = "rawBuiltin";

    /**
     * 內建特效
     * The constant EFFECT_BUILTIN.
     */
    public final static int EFFECT_BUILTIN = 0;
    /**
     * 包特效
     * The constant EFFECT_PACKAGE.
     */
    public final static int EFFECT_PACKAGE = 1;

    /**
     * 最大音频数
     * The constant MAX_AUDIO_COUNT.
     */
    public static final int MAX_AUDIO_COUNT = 16;
    /**
     * 这块默认设置为4K,
     * 因为添加了一个compile页面可以动态修改分辨率，
     * 如果创建比分辨率timeline 选择更高的分辨率，则无法生成对应分辨率的视频
     * The constant TIMELINE_RESOLUTION_VALUE.
     * The default setting is 4K,
     * Because a compile page is added to dynamically modify the resolution,
     * If you select a higher resolution than the resolution timeline,
     * you cannot generate the corresponding resolution video.
     */
    public static final int TIMELINE_RESOLUTION_VALUE = 720;


    /**
     * 字幕类型
     * The constant TYPE_CAPTION.
     */
    public static final int TYPE_CAPTION = 1;
    /**
     * 组合字幕类型
     * The constant TYPE_COMPOUND_CAPTION.
     */
    public static final int TYPE_COMPOUND_CAPTION = 2;
    /**
     * 贴纸类型
     * The constant TYPE_STICKER.
     */
    public static final int TYPE_STICKER = 3;
    /**
     * 图片类型
     * The constant TYPE_PIC.
     */
    public static final int TYPE_PIC = 4;
    /**
     * 特效类型
     * The constant TYPE_EFFECT.
     */
    public static final int TYPE_EFFECT = 5;

    /**
     * 是否支持hdr
     * Support HDR or not
     */
    public static boolean SUPPORT_HDR = false;

    /**
     * 是否支持往属性特技中添加region
     * Whether adding region to attribute stunt is supported
     */
    public static boolean SUPPORT_PROPERTY_REGION = false;

    /**
     * 是否支持蒙版关键帧曲线开关
     * Whether mask keyframe curve switch is supported
     */
    public static boolean SUPPORT_MASK_KEY_FRAME_CURVE = false;

    /**
     * 用户资源类型
     * The type User asset type.
     */
    public static class UserAssetType {
        /**
         * 所有资源
         * The constant All.
         */
        public static final int ALL = 0;
        /**
         * 已购资源
         * The constant PURCHASED.
         */
        public static final int PURCHASED = 1;
        /**
         * 自定义资源
         * The constant CUSTOM.
         */
        public static final int CUSTOM = 2;
    }


    /**
     * The enum Aspect ratio.
     */
    public enum AspectRatio {
        /**
         * Aspect 16 v 9 aspect ratio.
         */
        ASPECT_16V9(NvsConstants.AspectRatio.AspectRatio_16v9, 16.0f / 9, "16v9"),
        /**
         * Aspect 1 v 1 aspect ratio.
         */
        ASPECT_1V1(NvsConstants.AspectRatio.AspectRatio_1v1, 1, "1v1"),
        /**
         * Aspect 9 v 16 aspect ratio.
         */
        ASPECT_9V16(NvsConstants.AspectRatio.AspectRatio_9v16, 9.0f / 16, "9v16"),
        /**
         * Aspect 4 v 3 aspect ratio.
         */
        ASPECT_4V3(NvsConstants.AspectRatio.AspectRatio_4v3, 4.0f / 3, "4v3"),
        /**
         * Aspect 3 v 4 aspect ratio.
         */
        ASPECT_3V4(NvsConstants.AspectRatio.AspectRatio_3v4, 3.0f / 4, "3v4"),

        /**
         * Aspect 18 v 9 aspect ratio.
         */
        ASPECT_18V9(NvsConstants.AspectRatio.AspectRatio_18v9, 18f / 9, "18v9"),
        /**
         * Aspect 9 v 18 aspect ratio.
         */
        ASPECT_9V18(NvsConstants.AspectRatio.AspectRatio_9v18, 9.0f / 18, "9v18"),
        /**
         * Aspect 21 v 9 aspect ratio.
         */
        ASPECT_21V9(NvsConstants.AspectRatio.AspectRatio_21v9, 21.0f / 9, "21v9"),
        /**
         * Aspect 9 v 21 aspect ratio.
         */
        ASPECT_9V21(NvsConstants.AspectRatio.AspectRatio_9v21, 9.0f / 21, "9v21");

        private int aspect;
        private float ratio;
        private String stringValue;

        AspectRatio(int aspect, float ratio, String stringValue) {
            this.aspect = aspect;
            this.ratio = ratio;
            this.stringValue = stringValue;
        }

        /**
         * Gets ratio.
         *
         * @return the ratio
         */
        public float getRatio() {
            return ratio;
        }

        /**
         * Gets string value.
         *
         * @return the string value
         */
        public String getStringValue() {
            return stringValue;
        }

        /**
         * Gets aspect.
         *
         * @return the aspect
         */
        public int getAspect() {
            return aspect;
        }

        /**
         * Gets template aspect.
         * 获取模板近似比例
         *
         * @param ratio the ratio
         * @return the aspect
         */
        public static int getTemplateAspect(float ratio) {
            AspectRatio[] values = AspectRatio.values();
            AspectRatio finalValue = values[0];

            for (int i = 1; i < values.length; i++) {
                float currRatio = values[i].ratio - ratio;
                float beforeRatio = finalValue.ratio - ratio;
                if (Math.abs(currRatio) < Math.abs(beforeRatio)) {
                    finalValue = values[i];
                }
            }

            LogUtils.e("finalValue.aspect===" + finalValue.aspect + "===finalValue.ratio===" + finalValue.aspect);
            return finalValue.aspect;
        }

        /**
         * 获取近似比例
         * Gets aspect.Get approximation
         *
         * @param ratio the ratio 比例
         * @return the aspect
         */
        public static int getAspect(float ratio) {
            AspectRatio[] values = AspectRatio.values();
            for (AspectRatio value : values) {
                if (Math.abs(value.ratio - ratio) < 0.1f) {
                    return value.aspect;
                }
            }
            return NvsConstants.AspectRatio.AspectRatio_NoFitRatio;
        }

        /**
         * 获取近似比例对应的串
         * Gets aspect.
         * Get the string corresponding to the approximate value.
         *
         * @param ratio the ratio
         * @return the aspect string
         */
        public static AspectRatio getAspectRatio(int ratio) {
            AspectRatio[] values = AspectRatio.values();
            for (AspectRatio value : values) {
                if (value.aspect == ratio) {
                    return value;
                }
            }
            return null;
        }
    }
}
