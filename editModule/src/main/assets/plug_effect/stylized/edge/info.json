{"paramList": [{"name": "类型", "enName": "type", "paramName": "Type", "coverPath": "plug_type", "valueType": "S", "valueDefault": "<PERSON><PERSON>", "modeList": [{"paramValue": "sobel", "modeName": "<PERSON><PERSON>", "modeEnName": "<PERSON><PERSON>"}, {"paramValue": "roberts", "modeName": "<PERSON>", "modeEnName": "<PERSON>"}]}, {"name": "强度", "enName": "strength", "paramName": "Strength", "coverPath": "plug_intensity", "valueType": "F", "valueDefault": 0.5, "valueMin": 0, "valueMax": 1}, {"name": "宽度", "enName": "width", "paramName": "<PERSON><PERSON><PERSON>", "coverPath": "plug_width", "valueType": "F", "valueDefault": 1, "valueMin": 1, "valueMax": 10}, {"name": "阈值", "enName": "threshold", "paramName": "<PERSON><PERSON><PERSON><PERSON>", "coverPath": "plug_amplitude_thresh", "valueType": "F", "valueDefault": 0, "valueMin": 0, "valueMax": 1}, {"name": "保留RGB", "enName": "keepRGB", "paramName": "Keep RGB", "coverPath": "plug_check", "valueType": "B", "valueDefault": false}, {"name": "颜色", "enName": "color", "paramName": "Color", "coverPath": "plug_color", "valueType": "CO", "valueDefault": "{1,1,1,1}"}, {"name": "锐度", "enName": "sharpIntensity", "paramName": "Sharp Intensity", "coverPath": "plug_sharp", "valueType": "F", "valueDefault": 0.5, "valueMin": 0, "valueMax": 1}, {"name": "平滑边缘", "enName": "smoothEdge", "paramName": "Smooth Edge", "coverPath": "plug_check", "valueType": "B", "valueDefault": false}, {"name": "边缘发光", "enName": "edge shining", "paramName": "<PERSON> Shining", "coverPath": "plug_check", "valueType": "B", "valueDefault": false}]}