package com.meishe.myvideo.fragment;

import static com.meishe.logic.constant.PagerConstants.BUNDLE_DATA;
import static com.meishe.logic.constant.PagerConstants.FROM_DRAFT_EDIT;
import static com.meishe.logic.constant.PagerConstants.FROM_MATERIAL_SELECTED;
import static com.meishe.logic.constant.PagerConstants.FROM_PAGE;
import static com.meishe.logic.constant.PagerConstants.MEDIA_DATA;
import static com.meishe.logic.constant.PagerConstants.MEDIA_MAX_NUM;
import static com.meishe.logic.constant.PagerConstants.MEDIA_REQUEST_CODE_PREVIEW;
import static com.meishe.logic.constant.PagerConstants.MEDIA_TAG;
import static com.meishe.logic.constant.PagerConstants.SELECTED_TYPE;
import static com.meishe.logic.constant.PagerConstants.TYPE_ADD_SOME;
import static com.meishe.logic.constant.PagerConstants.TYPE_DEFAULT;
import static com.meishe.logic.constant.PagerConstants.TYPE_ONE_FINISH;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.meishe.base.adapter.CommonFragmentAdapter;
import com.meishe.base.bean.MediaData;
import com.meishe.base.bean.MediaTag;
import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseFragment;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;
import com.meishe.engine.util.ConvertFileManager;
import com.meishe.engine.util.WhiteList;
import com.meishe.engine.view.ConvertProgressPop;
import com.meishe.myvideo.R;
import com.meishe.myvideo.activity.DraftEditActivity;
import com.meishe.myvideo.activity.MaterialPreviewActivity;
import com.meishe.myvideo.util.ConfigUtil;
import com.meishe.third.tablayout.SlidingTabLayout;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 素材选择容器Fragment，可嵌入其他页面
 * Material selection container Fragment that can be embedded in other pages
 */
public class MediaSelectContainerFragment extends BaseFragment {
    private static final String TAG = "MediaSelectContainerFragment";
    
    private final List<Fragment> mFragmentList = new ArrayList<>(4);
    private List<String> mTabTitleList;
    private ArrayList<MediaData> mSelectedMaterialList = new ArrayList<>();
    private int mSelectedType = TYPE_DEFAULT;
    private int mFromPage;
    private OnMediaSelectedListener mOnMediaSelectedListener;
    private TextView mTvNext;
    private boolean mShowNextButton = true;

    /**
     * 创建MediaSelectContainerFragment实例
     */
    public static MediaSelectContainerFragment newInstance(int selectedType) {
        return newInstance(selectedType, 0, true);
    }

    /**
     * 创建MediaSelectContainerFragment实例
     */
    public static MediaSelectContainerFragment newInstance(int selectedType, int fromPage, boolean showNextButton) {
        MediaSelectContainerFragment fragment = new MediaSelectContainerFragment();
        Bundle args = new Bundle();
        args.putInt(SELECTED_TYPE, selectedType);
        args.putInt(FROM_PAGE, fromPage);
        args.putBoolean("SHOW_NEXT_BUTTON", showNextButton);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_media_container;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initView(View rootView) {
        if (getArguments() != null) {
            mSelectedType = getArguments().getInt(SELECTED_TYPE, TYPE_DEFAULT);
            mFromPage = getArguments().getInt(FROM_PAGE, 0);
            mShowNextButton = getArguments().getBoolean("SHOW_NEXT_BUTTON", true);
        }

        mTabTitleList = Arrays.asList(getResources().getStringArray(R.array.select_media));
        mFragmentList.clear();
        mFragmentList.add(MaterialSelectFragment.create(MediaData.TYPE_ALL, mSelectedType, true, mMediaListener));
        mFragmentList.add(MaterialSelectFragment.create(MediaData.TYPE_VIDEO, mSelectedType, true, mMediaListener));
        mFragmentList.add(MaterialSelectFragment.create(MediaData.TYPE_PHOTO, mSelectedType, true, mMediaListener));
        SlidingTabLayout tabLayout = rootView.findViewById(R.id.tl_select_media);
        ViewPager viewPager = rootView.findViewById(R.id.vp_select_media);
        mTvNext = rootView.findViewById(R.id.tv_next);

        viewPager.setOffscreenPageLimit(3);
        viewPager.setAdapter(new CommonFragmentAdapter(getChildFragmentManager(), mFragmentList));
        tabLayout.setViewPager(viewPager, mTabTitleList);
        
        // 初始化下一步按钮
        if (mShowNextButton) {
            mTvNext.setVisibility(View.GONE); // 初始状态隐藏，等有选中项时再显示
            mTvNext.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (Utils.isFastClick()) {
                        return;
                    }
                    if (!ConfigUtil.needConvert()) {
                        gotoNext();
                        return;
                    }
                    boolean needConvert = false;
                    ConvertFileManager.ConvertParam convertParam = new ConvertFileManager.ConvertParam();
                    for (MediaData mediaData : mSelectedMaterialList) {
                        String path = mediaData.getPath();
                        if (WhiteList.isCovert4KFileWhiteList(path)) {
                            needConvert = true;
                            convertParam.appendParam(path, "", false);
                        }
                    }
                    if (needConvert) {
                        convert(getActivity(), convertParam, new ConvertFileManager.EventListener() {
                            @Override
                            public void onConvertFinish(ConvertFileManager.ConvertParam convertParam, boolean convertSuccess) {
                                if (convertSuccess) {
                                    Map<String, ConvertFileManager.ConvertParam.Param> paramMap =
                                            convertParam.getParamMap();
                                    if (paramMap != null && !paramMap.isEmpty()) {
                                        for (MediaData mediaData : mSelectedMaterialList) {
                                            ConvertFileManager.ConvertParam.Param param = paramMap.get(mediaData.getPath());
                                            if (param != null) {
                                                String dstFile = param.getDstFile();
                                                if (!TextUtils.isEmpty(dstFile)) {
                                                    mediaData.setPath(dstFile);
                                                }
                                            }
                                        }
                                    }
                                    gotoNext();
                                } else {
                                    ToastUtils.make()
                                            .setGravity(Gravity.CENTER, 0, 0)
                                            .setDurationIsLong(false)
                                            .show(R.string.convert_failed);
                                }
                            }
                        });
                    } else {
                        gotoNext();
                    }
                }
            });
        } else {
            mTvNext.setVisibility(View.GONE);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        for (Fragment fragment : mFragmentList) {
            fragment.onActivityResult(requestCode, resultCode, data);
        }
    }

    /**
     * 获取当前选中的素材列表
     */
    public ArrayList<MediaData> getSelectedMaterialList() {
        return mSelectedMaterialList;
    }

    /**
     * 设置素材选择监听器
     */
    public void setOnMediaSelectedListener(OnMediaSelectedListener listener) {
        this.mOnMediaSelectedListener = listener;
    }

    /**
     * 处理素材选中
     * deal material selected
     *
     * @param mediaData The MediaData
     */
    private void dealMaterialSelected(MediaData mediaData) {
        MediaData selectedMedia;
        if (mSelectedType == TYPE_ONE_FINISH || mSelectedMaterialList.size() == 0) {
            mSelectedMaterialList.clear();
            mSelectedMaterialList.add(selectedMedia = getMaterialWidthTag(mediaData));
            updateSelectedNumber(0, selectedMedia, false);
        } else {
            int currentType = ((MediaTag) mediaData.getTag()).getType();
            for (int i = 0; i < mSelectedMaterialList.size(); i++) {
                selectedMedia = mSelectedMaterialList.get(i);
                if (selectedMedia.getId() == mediaData.getId()) {
                    selectedMedia.setState(mediaData.isState());
                    if (!selectedMedia.isState()) {
                        MediaTag[] mediaTags = (MediaTag[]) selectedMedia.getTag();
                        MediaTag tag;
                        for (int j = 0; j < mFragmentList.size(); j++) {
                            tag = mediaTags[j];
                            if (tag.getType() == currentType) {
                                /*
                                 * 当前页面已经取消过了
                                 * The current page has been cancelled
                                 * */
                                continue;
                            }
                            /*
                             * 通知其他子页面，取消选中
                             * Notify other subpages to deselect them
                             * */
                            ((MaterialSelectFragment) mFragmentList.get(j)).dealSelectedState(tag.getIndex(), false);
                        }
                        /*
                         * 未选中，则在选中集合中删除
                         * Unchecked, the selected collection is deleted
                         * */
                        mSelectedMaterialList.remove(i);
                        updateSelectedNumber(i, selectedMedia, true);
                        
                        // 更新下一步按钮状态
                        updateNextButtonState();
                        return;
                    }
                }
            }
            /*
             * 没有选中，则添加选中集合。
             * If not selected, the selected collection is added.
             * */
            mSelectedMaterialList.add(selectedMedia = getMaterialWidthTag(mediaData));
            updateSelectedNumber(mSelectedMaterialList.size() - 1, selectedMedia, false);
        }
        
        // 更新下一步按钮状态
        updateNextButtonState();
        
        // 通知选择变化
        if (mOnMediaSelectedListener != null) {
            mOnMediaSelectedListener.onMediaSelectionChanged(mSelectedMaterialList);
        }
    }

    /**
     * 更新下一步按钮状态
     */
    private void updateNextButtonState() {
        if (mShowNextButton) {
            if (mSelectedMaterialList.size() > 0) {
                if (mTvNext.getVisibility() != View.VISIBLE) {
                    mTvNext.setVisibility(View.VISIBLE);
                }
            } else if (mTvNext.getVisibility() == View.VISIBLE) {
                mTvNext.setVisibility(View.GONE);
            }
        }
    }

    /**
     * 更新选中的数字
     * Update the selected number
     */
    private void updateSelectedNumber(int startIndex, MediaData data, boolean useSelectedListTag) {
        MediaTag[] mediaTags = (MediaTag[]) data.getTag();
        MediaTag tag;
        for (int i = startIndex; i < mSelectedMaterialList.size(); i++) {
            if (useSelectedListTag) {
                mediaTags = (MediaTag[]) mSelectedMaterialList.get(i).getTag();
            }
            for (int j = 0; j < mFragmentList.size(); j++) {
                tag = mediaTags[j];
                /*
                 * 更新选中索引
                 * Update selected index
                 * */
                if (tag != null) {
                    ((MaterialSelectFragment) mFragmentList.get(j)).updateSelectedNumber(tag.getIndex(), i + 1);
                }
            }
        }
    }

    /**
     * 获取带有其他子页面Tag的素材
     * Gets the material with the other sub-page tags
     */
    private MediaData getMaterialWidthTag(MediaData mediaData) {
        MediaTag[] mediaTags = new MediaTag[mFragmentList.size()];
        MediaTag tag = (MediaTag) mediaData.getTag();
        
        if (tag.getType() == MediaData.TYPE_ALL) {
            mediaTags[0] = tag;
            mediaTags[1] = ((MaterialSelectFragment) mFragmentList.get(1)).dealSelected(mediaData.getThumbPath());
            mediaTags[2] = ((MaterialSelectFragment) mFragmentList.get(2)).dealSelected(mediaData.getThumbPath());
        } else if (tag.getType() == MediaData.TYPE_VIDEO) {
            mediaTags[1] = tag;
            mediaTags[0] = ((MaterialSelectFragment) mFragmentList.get(0)).dealSelected(mediaData.getThumbPath());
            mediaTags[2] = ((MaterialSelectFragment) mFragmentList.get(2)).dealSelected(mediaData.getThumbPath());
        } else if (tag.getType() == MediaData.TYPE_PHOTO) {
            mediaTags[2] = tag;
            mediaTags[0] = ((MaterialSelectFragment) mFragmentList.get(0)).dealSelected(mediaData.getThumbPath());
            mediaTags[1] = ((MaterialSelectFragment) mFragmentList.get(1)).dealSelected(mediaData.getThumbPath());
        }
        MediaData newData = mediaData.copy();
        newData.setTag(mediaTags);
        return newData;
    }

    /**
     * 转码
     * Convert
     */
    private void convert(Context context, ConvertFileManager.ConvertParam convertParam, ConvertFileManager.EventListener listener) {
        ConvertProgressPop.create(context, convertParam, listener).show();
    }

    /**
     * 跳转到下一步
     */
    private void gotoNext() {
        if (mOnMediaSelectedListener != null) {
            // 通知宿主Activity/Fragment处理下一步逻辑
            mOnMediaSelectedListener.onNextClicked(mSelectedMaterialList);
            return;
        }
        
        // 如果没有设置监听器，则使用默认处理逻辑
        if (mSelectedType == TYPE_DEFAULT) {
            Intent it = new Intent(getActivity(), DraftEditActivity.class);
            it.putExtra(FROM_PAGE, FROM_MATERIAL_SELECTED);
            it.putParcelableArrayListExtra(BUNDLE_DATA, mSelectedMaterialList);
            startActivity(it);
        } else if (mSelectedType == TYPE_ONE_FINISH) {
            /*
             * 单选一个素材没有处理。
             * A single story is not processed
             * */
            if (getActivity() != null) {
                Intent it = new Intent();
                it.putExtra(BUNDLE_DATA, mSelectedMaterialList.get(0));
                getActivity().setResult(getActivity().RESULT_OK, it);
                getActivity().finish();
            }
        } else if (mSelectedType == TYPE_ADD_SOME) {
            if (mFromPage == FROM_DRAFT_EDIT && getActivity() != null) {
                Intent it = new Intent();
                it.putParcelableArrayListExtra(BUNDLE_DATA, mSelectedMaterialList);
                getActivity().setResult(getActivity().RESULT_OK, it);
                getActivity().finish();
            }
        }
    }

    private final MaterialSelectFragment.MediaChangeListener mMediaListener = new MaterialSelectFragment.MediaChangeListener() {
        @Override
        public void onMediaChange(MediaData mediaData) {
            dealMaterialSelected(mediaData);
        }

        @Override
        public void onMediaPreView(MediaData mediaData) {
            if (Utils.isFastClick()) {
                return;
            }
            if (mediaData == null) {
                LogUtils.e("mediaData is null !");
                return;
            }
            Bundle bundle = new Bundle();
            if (mSelectedType == TYPE_ONE_FINISH) {
                mediaData.setPosition(1);
                if (mediaData.isState()) {
                    bundle.putInt(MEDIA_MAX_NUM, 1);
                } else {
                    bundle.putInt(MEDIA_MAX_NUM, 0);
                }
            } else {
                bundle.putInt(MEDIA_MAX_NUM, mSelectedMaterialList == null ? 0 : mSelectedMaterialList.size());
            }
            bundle.putParcelable(MEDIA_DATA, mediaData);
            bundle.putParcelable(MEDIA_TAG, ((MediaTag) mediaData.getTag()));

            // 跳转预览页面
            // Jump to preview page
            AppManager.getInstance().jumpActivityForResult(getActivity(), MaterialPreviewActivity.class, bundle, MEDIA_REQUEST_CODE_PREVIEW);
        }
    };

    /**
     * 素材选择监听接口
     */
    public interface OnMediaSelectedListener {
        /**
         * 当素材选择变化时回调
         * @param selectedList 当前选中的素材列表
         */
        void onMediaSelectionChanged(ArrayList<MediaData> selectedList);
        
        /**
         * 当点击下一步按钮时回调
         * @param selectedList 当前选中的素材列表
         */
        default void onNextClicked(ArrayList<MediaData> selectedList) {
            // 默认实现为空，子类可以选择性实现
        }
    }
}