package com.meishe.myvideo.ui.trackview.bean;

import android.text.TextUtils;

import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.AnimationData;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.command.VideoClipCommand;
import com.meishe.myvideo.ui.bean.BaseTrackClip;
import com.meishe.myvideo.ui.bean.BaseUIClip;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/5/25 11:04
 * @Description :视频clip代理 Video clip proxy
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class VideoClipProxy extends BaseUIClip {

    private MeicamVideoClip mVideoClip;

    public VideoClipProxy(MeicamVideoClip captionClip, int trackIndex) {
        super(captionClip.getVideoType(), trackIndex);
        this.mVideoClip = captionClip;
        super.setSpeed(captionClip.getSpeed());
        super.setInPoint(captionClip.getInPoint());
        super.setOutPoint(captionClip.getOutPoint());
        super.setTrimOut(captionClip.getTrimOut());
        super.setTrimIn(captionClip.getTrimIn());
    }

    @Override
    public String getType() {
        if (mVideoClip != null) {
            return mVideoClip.getVideoType();
        }
        return super.getType();
    }

    @Override
    public int getSubType() {
        return INVALID;
    }

    @Override
    public int getClipIndexInTrack() {
        if (mVideoClip != null) {
            return mVideoClip.getIndex();
        }
        return INVALID;
    }


    @Override
    public void setInPoint(long inPoint) {
        if (mVideoClip != null) {
            mVideoClip.setInPoint(inPoint);
        }
        super.setInPoint(inPoint);
    }

    @Override
    public void setOutPoint(long outPoint) {
        if (mVideoClip != null) {
            mVideoClip.setOutPoint(outPoint);
        }
        super.setOutPoint(outPoint);
    }

    @Override
    public double getSpeed() {
        if (mVideoClip != null) {
            return mVideoClip.getSpeed();
        }
        return super.getSpeed();
    }

    @Override
    public long getInPoint() {
        if (mVideoClip != null) {
            return mVideoClip.getInPoint();
        }
        return super.getInPoint();
    }

    @Override
    public long getOutPoint() {
        if (mVideoClip != null) {
            return mVideoClip.getOutPoint();
        }
        return super.getOutPoint();
    }

    @Override
    public long getAnimationTrimIn() {
        if (mVideoClip != null) {
            AnimationData animationData = EditorEngine.getInstance().getVideoClipAnimation(mVideoClip);
            if (animationData == null) {
                return 0;
            }
            if (TextUtils.isEmpty(animationData.getPackageID())) {
                return 0;
            }
            return animationData.getInPoint();
        }
        return 0;
    }

    @Override
    public long getAnimationTrimOut() {
        if (mVideoClip != null) {
            AnimationData animationData = EditorEngine.getInstance().getVideoClipAnimation(mVideoClip);
            if (animationData == null) {
                return 0;
            }
            if (TextUtils.isEmpty(animationData.getPackageID())) {
                return 0;
            }
            return animationData.getOutPoint();
        }
        return 0;
    }

    @Override
    public long getAnimationTrimIn2() {
        if (mVideoClip != null) {
            AnimationData animationData = EditorEngine.getInstance().getVideoClipAnimation(mVideoClip);
            if (animationData == null) {
                return 0;
            }
            if (TextUtils.isEmpty(animationData.getPackageID2())) {
                return 0;
            }
            return animationData.getInPoint2();
        }
        return 0;
    }

    @Override
    public long getAnimationTrimOut2() {
        if (mVideoClip != null) {
            AnimationData animationData = EditorEngine.getInstance().getVideoClipAnimation(mVideoClip);
            if (animationData == null) {
                return 0;
            }
            if (TextUtils.isEmpty(animationData.getPackageID2())) {
                return 0;
            }
            return animationData.getOutPoint2();
        }
        return 0;
    }

    @Override
    public String getIconFilePath() {
        return "";
    }

    @Override
    public String getFilePath() {
        if (mVideoClip != null) {
            return mVideoClip.getVideoReverse() ? mVideoClip.getReverseFilePath() : mVideoClip.getFilePath();
        }
        return "";
    }

    @Override
    public long getTrimIn() {
        if (mVideoClip != null) {
            return mVideoClip.getTrimIn();
        }
        return super.getTrimIn();
    }

    @Override
    public void setTrimIn(long trimIn) {
        if (mVideoClip != null) {
            VideoClipCommand.setTrimIn(mVideoClip, trimIn, false);
        }
        super.setTrimIn(trimIn);
    }

    @Override
    public long getTrimOut() {
        if (mVideoClip != null) {
            return mVideoClip.getTrimOut();
        }
        return super.getTrimOut();
    }

    @Override
    public void setTrimOut(long trimOut) {
        if (mVideoClip != null) {
            VideoClipCommand.setTrimOut(mVideoClip, trimOut, false);
        }
        super.setTrimOut(trimOut);
    }

    @Override
    public float[] getRecordArray() {
        return new float[0];
    }

    @Override
    public void setDuration(long duration) {
        super.setDuration(duration);
    }

    @Override
    public long getDuration() {
        if (mVideoClip != null) {
            return (long) ((mVideoClip.getTrimOut() - mVideoClip.getTrimIn()) / mVideoClip.getSpeed());
        } else {
            return super.getDuration();
        }
    }

    @Override
    public long getFadeIn() {
        return 0;
    }

    @Override
    public long getFadeOut() {
        return 0;
    }

    @Override
    public String getDisplayName() {
        if (mVideoClip != null) {
            return mVideoClip.getCurveSpeedName();
        }
        return "";
    }

    @Override
    public boolean canExceedLength() {
        return true;
    }

    @Override
    public boolean canDrag() {
        return true;
    }

    @Override
    public int getBackGroundColor() {
        return 0;
    }

    @Override
    public String getCurveSpeedName() {
        if (mVideoClip != null) {
            return mVideoClip.getCurveSpeedName();
        }
        return super.getCurveSpeedName();
    }

    @Override
    public boolean isHasProp() {
        if (mVideoClip != null) {
            return !TextUtils.isEmpty(mVideoClip.getPropId());
        }
        return super.isHasProp();
    }

    @Override
    public float getVolume() {
        if (mVideoClip != null) {
            return mVideoClip.getVolume();
        }
        return super.getVolume();
    }

    @Override
    public ThumbNailInfo getThumbNailInfo() {
        if (mVideoClip != null) {
            MeicamVideoClip.ThumbNailInfo thumbNailInfo = mVideoClip.getThumbNailInfo();
            if (thumbNailInfo != null) {
                return new ThumbNailInfo(thumbNailInfo.urlPrefix, thumbNailInfo.interval, thumbNailInfo.extension, BaseTrackClip.CLIP_IMAGE.equals(mVideoClip.getVideoType()));
            }
        }
        return super.getThumbNailInfo();
    }
}
