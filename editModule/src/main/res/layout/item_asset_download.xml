<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_px_210">


    <ImageView
        android:id="@+id/iv_cover"
        android:layout_width="@dimen/dp_px_135"
        android:layout_height="@dimen/dp_px_135"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/dp_px_40"
        android:layout_marginLeft="@dimen/dp_px_40"
        android:contentDescription="@null" />


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/dp_px_220"
        android:layout_marginLeft="@dimen/dp_px_220"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_36" />

        <TextView
            android:id="@+id/tv_ratio"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_33" />

        <TextView
            android:id="@+id/tv_size"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_33" />
    </LinearLayout>

    <Button
        android:id="@+id/bt_download"
        android:layout_width="@dimen/dp_px_210"
        android:layout_height="@dimen/dp_px_90"
        android:layout_gravity="center_vertical|end"
        android:layout_marginEnd="@dimen/dp_px_40"
        android:layout_marginRight="@dimen/dp_px_40"
        android:background="@drawable/download_button_shape_corner_download"
        android:textAllCaps="false"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_36" />

    <com.meishe.myvideo.downLoad.DownloadProgressBar
        android:id="@+id/pb_download"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="@dimen/dp_px_210"
        android:layout_height="@dimen/dp_px_90"
        android:layout_gravity="center_vertical|end"
        android:layout_marginEnd="@dimen/dp_px_40"
        android:layout_marginRight="@dimen/dp_px_40"
        android:progressDrawable="@drawable/download_progressbar"
        android:visibility="gone" />
</FrameLayout>
