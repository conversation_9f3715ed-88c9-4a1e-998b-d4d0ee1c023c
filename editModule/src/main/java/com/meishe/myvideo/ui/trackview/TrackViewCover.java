package com.meishe.myvideo.ui.trackview;

import android.content.Context;
import android.util.AttributeSet;
import android.view.Gravity;
import android.widget.FrameLayout;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.meishe.myvideo.R;
import com.meishe.myvideo.ui.bean.KeyFrameInfo;
import com.meishe.myvideo.util.PixelPerMicrosecondUtil;

import java.util.Map;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/6/22 10:18
 * @Description :子轨道视图封面 the child track view cover
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class TrackViewCover extends FrameLayout {
    private FrameLayout mFlKeyFrameContainer;
    private KeyFrameInfo mKeyFrameInfo;
    private int mKeyFrameViewWidth;
    private int mKeyFrameViewHeight;
    private ImageView mIvSelectedKeyFrame;

    public TrackViewCover(@NonNull Context context) {
        this(context, null);
    }

    public TrackViewCover(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public TrackViewCover(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    private void initView() {
        mKeyFrameViewWidth = (int) getResources().getDimension(R.dimen.dp_px_36);
        mKeyFrameViewHeight = (int) getResources().getDimension(R.dimen.dp_px_44);
        addView(mFlKeyFrameContainer = new FrameLayout(getContext()));
        mFlKeyFrameContainer.setVisibility(INVISIBLE);
    }

    /**
     * 设置关键帧信息是否可用，可用则显示，不可用不显示
     * Enable Key frame info
     *
     * @param enable true enable ,false not
     */
    public void enableKeyFrame(boolean enable) {
        mFlKeyFrameContainer.setVisibility(enable ? VISIBLE : INVISIBLE);
    }

    /**
     * 设置关键帧信息
     * Set key frame info
     *
     * @param keyFrameInfo the key frame info
     */
    public void setKeyFrameInfo(KeyFrameInfo keyFrameInfo, long selectedPoint) {
        mKeyFrameInfo = keyFrameInfo;
        Map<Long, KeyFrameInfo.Info> keyFrameMap = keyFrameInfo.getKeyFrameMap();
        mFlKeyFrameContainer.removeAllViews();
        for (Map.Entry<Long, KeyFrameInfo.Info> entry : keyFrameMap.entrySet()) {
            KeyFrameInfo.Info info = entry.getValue();
            if (info.isValid()) {
                addKeyFrameView(info.getAtTime(), selectedPoint == info.getAtTime());
            }
        }
    }

    /**
     * 检测是否需要选中关键帧视图
     * Checks if the keyframe view needs to be selected
     *
     * @param clipInPoint the clip int point 片段的入点
     * @param timestamp   the timestamp 时间戳
     */
    public void checkSelectedKeyFrame(long clipInPoint, long timestamp) {
        if (mKeyFrameInfo != null) {
            int childCount = mFlKeyFrameContainer.getChildCount();
            long selectedPoint = mKeyFrameInfo.getSelectedPoint();
            long minTime = Long.MAX_VALUE;
            if (timestamp >= 0) {
                for (int i = 0; i < childCount; i++) {
                    ImageView keyFrameView = (ImageView) mFlKeyFrameContainer.getChildAt(i);
                    Long point = (Long) keyFrameView.getTag();
                    int selectedX = PixelPerMicrosecondUtil.durationToLength(timestamp);
                    int viewX = PixelPerMicrosecondUtil.durationToLength(point + clipInPoint);
                    if (selectedX >= viewX - mKeyFrameViewWidth / 2 && selectedX <= viewX + mKeyFrameViewWidth / 2) {
                        minTime = Math.min(selectedPoint - timestamp, point - timestamp);
                        if (selectedPoint == -1 || selectedPoint != minTime) {
                            if (mIvSelectedKeyFrame != null) {
                                mIvSelectedKeyFrame.setImageResource(R.mipmap.key_frame_icon);
                            }
                            mIvSelectedKeyFrame = keyFrameView;
                            keyFrameView.setImageResource(R.mipmap.key_frame_icon_selected);
                            selectedPoint = point;
                        }
                    } else {
                        keyFrameView.setImageResource(R.mipmap.key_frame_icon);
                    }
                }
            } else {
                if (mIvSelectedKeyFrame != null) {
                    mIvSelectedKeyFrame.setImageResource(R.mipmap.key_frame_icon);
                }
            }
            if (minTime == Long.MAX_VALUE) {
                selectedPoint = -1;
            }
            mKeyFrameInfo.setSelectedPoint(selectedPoint);
        }
    }

    /**
     * 添加关键帧
     * Add key frame
     *
     * @param inPoint the in point 入点
     */
    public void addKeyFrame(long inPoint, boolean selected) {
        if (mKeyFrameInfo != null && !mKeyFrameInfo.hasKeyFrame(inPoint)) {
            addKeyFrameView(inPoint, selected);
            mKeyFrameInfo.addKeyFrame(inPoint);
        }
    }

    /**
     * 添加关键帧视图
     * Add key frame view
     *
     * @param inPoint the in point 入点
     */
    private void addKeyFrameView(long inPoint, boolean selected) {
        if (mKeyFrameInfo != null) {
            ImageView keyFrameView = new ImageView(getContext());
            keyFrameView.setTag(inPoint);
            if (selected) {
                mKeyFrameInfo.setSelectedPoint(inPoint);
                keyFrameView.setImageResource(R.mipmap.key_frame_icon_selected);
            } else {
                keyFrameView.setImageResource(R.mipmap.key_frame_icon);
            }
            LayoutParams layoutParams = new LayoutParams(mKeyFrameViewWidth, mKeyFrameViewHeight);
            layoutParams.leftMargin = PixelPerMicrosecondUtil.durationToLength(inPoint) - mKeyFrameViewWidth / 2;
            layoutParams.gravity = Gravity.CENTER_VERTICAL;
            mFlKeyFrameContainer.addView(keyFrameView, layoutParams);
        }
    }

    /**
     * 更新关键帧视图的位置
     * Update key frame view position
     */
    public void updateKeyFramePosition() {
        int childCount = mFlKeyFrameContainer.getChildCount();
        for (int i = 0; i < childCount; i++) {
            ImageView keyFrameView = (ImageView) mFlKeyFrameContainer.getChildAt(i);
            Object tag = keyFrameView.getTag();
            if (tag instanceof Long) {
                Long point = (Long) tag;
                LayoutParams layoutParams = (LayoutParams) keyFrameView.getLayoutParams();
                layoutParams.leftMargin = PixelPerMicrosecondUtil.durationToLength(point) - mKeyFrameViewWidth / 2;
                keyFrameView.setLayoutParams(layoutParams);
            }
        }
    }

    /**
     * 删除关键帧视图
     * Delete key frame view
     *
     * @param inPoint     the in point 入点
     * @param clipInPoint the clip int point 片段的入点
     */
    public void deleteKeyFrame(long clipInPoint, long inPoint) {
        if (mKeyFrameInfo != null) {
            int childCount = mFlKeyFrameContainer.getChildCount();
            for (int i = 0; i < childCount; i++) {
                ImageView keyFrameView = (ImageView) mFlKeyFrameContainer.getChildAt(i);
                Long point = (Long) keyFrameView.getTag();
                if (point == inPoint) {
                    mFlKeyFrameContainer.removeViewAt(i);
                    mKeyFrameInfo.deleteKeyFrame(inPoint);
                    if (mKeyFrameInfo.getSelectedPoint() == inPoint) {
                        mKeyFrameInfo.setSelectedPoint(-1);
                    }
                    checkSelectedKeyFrame(clipInPoint, clipInPoint + inPoint);
                    break;
                }
            }
        }
    }
}
