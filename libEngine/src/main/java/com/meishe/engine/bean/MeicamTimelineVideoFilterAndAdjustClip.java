package com.meishe.engine.bean;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsObject;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsTimelineVideoFx;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.local.LMeicamTimelineVideoFilterAndAdjustClip;
import com.meishe.engine.local.LMeicamTimelineVideoFxClip;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: ChuChenGuang
 * @CreateDate: 2021/9/16 17:34
 * @Description: 时间线滤镜和调节 The timeline filter and adjust
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class MeicamTimelineVideoFilterAndAdjustClip extends ClipInfo<NvsTimeline> implements Serializable,
        TimelineDataParserAdapter<LMeicamTimelineVideoFilterAndAdjustClip> {

    /**
     * Clip ID, used to mark clip
     * clip标识，用于标记clip
     */
    private final String indicate;
    /**
     * Since adjustments are multiple, they are encapsulated in a map
     * Because the filter and the adjustment are going to be in the same orbit, so the filter is going to be in here, a fixed key
     * 由于调节是多个并列得关系,所以要用个map 封装一下,因为滤镜和调节是要放在同轨道中的，所以滤镜也要放在这里面，一个固定的key
     */
    protected Map<String, MeicamTimelineVideoFxClip> filterAndAdjustClipInfos = new HashMap<>();

    private String text;

    public MeicamTimelineVideoFilterAndAdjustClip(NvsTimeline nvsTimeline, String type, long inPoint, long duration) {
        super(nvsTimeline, type);
        this.outPoint = inPoint + duration;
        this.inPoint = inPoint;
        this.indicate = "regulate-" + UUID.randomUUID();
    }

    public Map<String, MeicamTimelineVideoFxClip> getFilterAndAdjustClipInfos() {
        return filterAndAdjustClipInfos;
    }

    public String getText() {
        return text;
    }

    public void setDisplayText(String text) {
        this.text = text;
    }

    public String getIndicate() {
        return indicate;
    }

    /**
     * 添加底层timeline 调节，滤镜
     * Add adjust timeline fx meicam timeline video fx clip.
     *
     * @param clip   the clip
     * @param fxType the fx type timelineAdjust or 滤镜类型
     * @return the meicam timeline video fx clip
     */
    public MeicamTimelineVideoFxClip addAdjustTimelineFx(MeicamTimelineVideoFxClip clip, String fxType) {
        if (clip == null) {
            return null;
        }
        NvsTimelineVideoFx nvsTimelineVideoFx = addNvsTimelineVideoFx(clip.getClipType(), clip.getInPoint(), clip.getOutPoint() - clip.getInPoint(), clip.getDesc());
        if (nvsTimelineVideoFx == null) {
            return null;
        }
        MeicamTimelineVideoFxClip meicamTimelineVideoFxClip = new MeicamTimelineVideoFxClip(nvsTimelineVideoFx, clip.getClipType(),
                clip.getInPoint(), clip.getOutPoint() - clip.getInPoint(), clip.getDesc());
        meicamTimelineVideoFxClip.copyData(clip);
        boolean isAdjustFx = MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_ADJUST.equals(fxType);
        filterAndAdjustClipInfos.put(isAdjustFx ? clip.getDesc() :
                MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FILTER, meicamTimelineVideoFxClip);
        return meicamTimelineVideoFxClip;
    }

    /**
     * 添加底层timeline特效,也可做为查找方法，如果已经存在则返回
     * Add adjust timeline fx meicam timeline video fx clip.
     *
     * @param type        the type build or package
     * @param fxType      the fx type 特效类型 timelineAdjust timelineFilter
     * @param videoFxName the video fx name
     * @return the meicam timeline video fx clip
     */
    public MeicamTimelineVideoFxClip addAdjustTimelineFx(String type, String fxType, String videoFxName) {
        if (TextUtils.isEmpty(videoFxName)) {
            LogUtils.e("param error: fxId or resolution is null");
            return null;
        }
        boolean isAdjustFx = MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_ADJUST.equals(fxType);
        MeicamTimelineVideoFxClip fxClip = isAdjustFx ? filterAndAdjustClipInfos.get(videoFxName) : filterAndAdjustClipInfos.get(fxType);
        if (fxClip != null) {
            return fxClip;
        }
        if (!TextUtils.isEmpty(type)) {
            NvsTimelineVideoFx nvsTimelineVideoFx = addNvsTimelineVideoFx(type, inPoint, outPoint - inPoint, videoFxName);
            if (nvsTimelineVideoFx == null) {
                return null;
            }
            nvsTimelineVideoFx.setBooleanVal(NvsConstants.KEY_VIDEO_MODE, true);
            MeicamTimelineVideoFxClip meicamTimelineVideoFxClip = buildMeicamTimelineFx(nvsTimelineVideoFx, type, videoFxName);
//            meicamTimelineVideoFxClip.setType(isAdjustFx ? videoFxName : fxType);
            meicamTimelineVideoFxClip.setSubType(isAdjustFx ? MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_ADJUST : MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FILTER);
            if (isAdjustFx) {
                meicamTimelineVideoFxClip.setIndicate(indicate);
            }
            filterAndAdjustClipInfos.put(isAdjustFx ? videoFxName : fxType, meicamTimelineVideoFxClip);
            return meicamTimelineVideoFxClip;
        }
        return null;
    }

    /**
     * 添加底层timeline特效,也可做为查找方法，如果已经存在则返回
     * Add adjust timeline fx meicam timeline video fx clip.
     *
     * @param nvsTimelineVideoFx        the nvsTimelineVideoFx native 实例
     * @param type                      the type build or package 类型：内建或者包特效
     * @param fxType                    the fx type 特效类型 timelineAdjust timelineFilter
     * @param videoFxName               the video fx name 特效名称
     */
    public void addAdjustTimelineFx(NvsTimelineVideoFx nvsTimelineVideoFx, String type, String fxType, String videoFxName) {
        if (TextUtils.isEmpty(videoFxName)) {
            LogUtils.e("param error: fxId or resolution is null");
            return;
        }
        boolean isAdjustFx = MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_ADJUST.equals(fxType);
        MeicamTimelineVideoFxClip fxClip = isAdjustFx ? filterAndAdjustClipInfos.get(videoFxName) : filterAndAdjustClipInfos.get(fxType);
        if (fxClip != null) {
            return;
        }
        if (!TextUtils.isEmpty(type)) {
            nvsTimelineVideoFx.setBooleanVal(NvsConstants.KEY_VIDEO_MODE, true);
            MeicamTimelineVideoFxClip meicamTimelineVideoFxClip = buildMeicamTimelineFx(nvsTimelineVideoFx, type, videoFxName);
            if (isAdjustFx) {
                meicamTimelineVideoFxClip.setIndicate(indicate);
            }
            meicamTimelineVideoFxClip.setSubType(isAdjustFx ? MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_ADJUST : MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FILTER);
            filterAndAdjustClipInfos.put(isAdjustFx ? videoFxName : fxType, meicamTimelineVideoFxClip);
            meicamTimelineVideoFxClip.recoverFromTimelineData(nvsTimelineVideoFx);
        }
    }

    @NonNull
    private MeicamTimelineVideoFxClip buildMeicamTimelineFx(NvsTimelineVideoFx nvsTimelineVideoFx, String type, String videoFxName) {
        MeicamTimelineVideoFxClip meicamTimelineVideoFxClip = new MeicamTimelineVideoFxClip(nvsTimelineVideoFx, type, inPoint, outPoint - inPoint, videoFxName);
        meicamTimelineVideoFxClip.setIntensity(1.0F);
        meicamTimelineVideoFxClip.setTrackIndex(getTrackIndex());
        meicamTimelineVideoFxClip.setIndex(getIndex());
        return meicamTimelineVideoFxClip;
    }

    @Override
    public void setInPoint(long inPoint) {
        for (Map.Entry<String, MeicamTimelineVideoFxClip> entry : filterAndAdjustClipInfos.entrySet()) {
            MeicamTimelineVideoFxClip meicamTimelineVideoFxClip = entry.getValue();
            meicamTimelineVideoFxClip.setInPoint(inPoint);
        }
        super.setInPoint(inPoint);
    }

    @Override
    public void setOutPoint(long outPoint) {
        for (Map.Entry<String, MeicamTimelineVideoFxClip> entry : filterAndAdjustClipInfos.entrySet()) {
            MeicamTimelineVideoFxClip meicamTimelineVideoFxClip = entry.getValue();
            meicamTimelineVideoFxClip.setOutPoint(outPoint);
        }
        super.setOutPoint(outPoint);
    }

    @Override
    void loadData() {
        super.loadData();
        for (Map.Entry<String, MeicamTimelineVideoFxClip> entry : filterAndAdjustClipInfos.entrySet()) {
            MeicamTimelineVideoFxClip meicamTimelineVideoFxClip = entry.getValue();
            meicamTimelineVideoFxClip.loadData();
        }
    }

    /**
     * 添加底层timeline特效,也可做为查找方法，如果已经存在则返回
     * Add nvs timeline video fx nvs timeline video fx.
     *
     * @param videoFxName the video fx name
     * @return the nvs timeline video fx
     */
    public MeicamTimelineVideoFxClip getAdjustTimelineFx(String videoFxName) {
        if (TextUtils.isEmpty(videoFxName)) {
            LogUtils.e("param error: fxId or resolution is null");
            return null;
        }
        return filterAndAdjustClipInfos.get(videoFxName);
    }


    /**
     * 添加底层timeline特效
     * Add nvs timeline video fx nvs timeline video fx.
     *
     * @param type        the type
     * @param inPoint     the in point
     * @param duration    the duration
     * @param videoFxName the video fx name
     * @return the nvs timeline video fx
     */
    private NvsTimelineVideoFx addNvsTimelineVideoFx(String type, long inPoint, long duration, String videoFxName) {
        NvsTimelineVideoFx nvsTimelineVideoFx;
        if (CommonData.TYPE_BUILD_IN.equals(type)) {
            nvsTimelineVideoFx = getObject().addBuiltinTimelineVideoFx(inPoint, duration, videoFxName);
        } else {
            nvsTimelineVideoFx = getObject().addPackagedTimelineVideoFx(inPoint, duration, videoFxName);
        }
        if (nvsTimelineVideoFx != null) {
            nvsTimelineVideoFx.setZValue(getTrackIndex());
        }
        return nvsTimelineVideoFx;
    }

    public boolean removeFilterAndAdjustClipFx() {
        for (Map.Entry<String, MeicamTimelineVideoFxClip> entry : filterAndAdjustClipInfos.entrySet()) {
            NvsTimelineVideoFx object = entry.getValue().getObject();
            if (object != null) {
                getObject().removeTimelineVideoFx(object);
            } else {
                LogUtils.e("removeFilterAndAdjustClipFx failed,getObject() is null!");
            }
        }
        return true;
    }

    /**
     * Remove filter and adjust clip fx boolean.
     * 删除滤镜或调节clip
     *
     * @param fxDesc the fx desc 特效的描述
     * @return the boolean
     */
    public boolean removeFilterAndAdjustClipFx(String fxDesc) {
        MeicamTimelineVideoFxClip videoFxClip = filterAndAdjustClipInfos.get(fxDesc);
        if (videoFxClip != null) {
            NvsTimelineVideoFx object = videoFxClip.getObject();
            if (object != null) {
                getObject().removeTimelineVideoFx(object);
                filterAndAdjustClipInfos.remove(fxDesc);
                return true;
            }
        }
        return false;
    }

    @Override
    public void setZValue(float zValue) {
        super.setZValue(zValue);
        if (!filterAndAdjustClipInfos.isEmpty()) {
            Set<Map.Entry<String, MeicamTimelineVideoFxClip>> entries = filterAndAdjustClipInfos.entrySet();
            for (Map.Entry<String, MeicamTimelineVideoFxClip> entry : entries) {
                MeicamTimelineVideoFxClip videoFxClip = entry.getValue();
                if (videoFxClip != null) {
                    videoFxClip.setZValue(zValue);
                }
            }
        }
    }

    @Override
    public void recoverFromLocalData(LMeicamTimelineVideoFilterAndAdjustClip lMeicamTimelineVideoFilterAndAdjustClip) {
        setDisplayText(lMeicamTimelineVideoFilterAndAdjustClip.getText());
        setInPoint(lMeicamTimelineVideoFilterAndAdjustClip.getInPoint());
        setOutPoint(lMeicamTimelineVideoFilterAndAdjustClip.getOutPoint());
        setIndex(lMeicamTimelineVideoFilterAndAdjustClip.getIndex());
        setZValue(lMeicamTimelineVideoFilterAndAdjustClip.getzValue());
    }

    @Override
    public void recoverFromTimelineData(NvsObject nvsObject) {

    }

    @Override
    public LMeicamTimelineVideoFilterAndAdjustClip parseToLocalData() {
        LMeicamTimelineVideoFilterAndAdjustClip adjustClip = new LMeicamTimelineVideoFilterAndAdjustClip();
        HashMap<String, LMeicamTimelineVideoFxClip> hashMap = new HashMap<>();
        for (Map.Entry<String, MeicamTimelineVideoFxClip> entry : filterAndAdjustClipInfos.entrySet()) {
            hashMap.put(entry.getKey(), entry.getValue().parseToLocalData());
        }
        adjustClip.setText(getText());
        adjustClip.setFilterAndAdjustClipInfos(hashMap);
        adjustClip.setType(getType());
        setCommonData(adjustClip);
        return adjustClip;
    }


}
