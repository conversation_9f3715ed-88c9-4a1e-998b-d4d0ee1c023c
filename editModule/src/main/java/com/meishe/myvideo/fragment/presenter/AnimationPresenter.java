package com.meishe.myvideo.fragment.presenter;

import com.meishe.base.utils.StringUtils;
import com.meishe.business.assets.iview.AssetsView;
import com.meishe.business.assets.presenter.AssetsPresenter;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.bean.CommonData;
import com.meishe.myvideo.R;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/8/17 11:40
 * @Description :动画presenter The animation presenter.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class AnimationPresenter extends AssetsPresenter<AssetsView> {
    @Override
    protected List<AssetInfo> handleDataInFirstPage(List<AssetInfo> list) {
        final AssetInfo nullInfo = new AssetInfo();
        nullInfo.setEffectMode(CommonData.EFFECT_BUILTIN);
        nullInfo.setName(StringUtils.getString(R.string.no));
        nullInfo.setCoverId(R.mipmap.ic_no);
        nullInfo.setHadDownloaded(true);
        list.add(0, nullInfo);
        return list;
    }
}
