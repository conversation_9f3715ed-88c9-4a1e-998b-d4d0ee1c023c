package com.meishe.user.manager.ali;

import android.net.Uri;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meishe.base.utils.AndroidVersionUtils;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FileIOUtils;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.GsonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.MediaTypeUtils;
import com.meishe.base.utils.ThreadUtils;
import com.meishe.base.utils.Utils;
import com.meishe.draft.data.DraftData;
import com.meishe.draft.db.DraftDbManager;
import com.meishe.draft.db.FileInfoDao;
import com.meishe.draft.db.FileInfoEntity;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.ExportTemplateManager;
import com.meishe.engine.asset.bean.TemplateUploadParam;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.TransformResponse;
import com.meishe.engine.bean.bridges.FileInfoBridge;
import com.meishe.engine.bean.template.ExportTemplateDescInfo;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.logic.manager.PreferencesManager;
import com.meishe.logic.utils.AppNetAPi;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.meishe.engine.bean.template.ExportTemplateDescInfo.TYPE_FOOTAGE_ORIGINAL;


/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2020/12/4 19:25
 * @Description :草稿上传阿里云的帮助类 Draft upload ali cloud help class
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class ALiHelper {
    String token = null;
    /**
     * 上传资源占用的进度条
     * Upload the progress bar occupied by resources
     */
    private static final float RESOURCE_MAX_PROGRESS = 90;
    /**
     * 上传project文件占用的资源
     * Resources occupied by uploading project files
     */
    private static final float RESOURCE_MAX_PROJECT = 5;
    private UploadListener mUploadListener;
    private Map<String, Integer> mProgressMap = new HashMap<>();
    private String mUuid;
    private String mProjectId;
    private String mBackgroundUuid;
    private DraftData mDraftData;
    private Map<String, String> mLocalToNetFileMap = new HashMap<>();
    private TemplateUploadParam mTemplateUploadParam;
    private ExportTemplateDescInfo mTemplateDescInfo;
    private ExportTemplateDescInfo mTemplateDescInfoBackUp;
    private String mProjectUrl;
    private String mInfoJsonUrl;
    private FileInfoDao mFileInfoDao;
    private Uploader mUploader;
    private ExportTemplateManager mExportManager;
    private MeicamTimeline meicamTimeline;

    private ALiHelper() {
        mFileInfoDao = DraftDbManager.get().getFileInfoDao();
        mUploader = new Uploader();
    }

    public static ALiHelper create() {
        return new ALiHelper();
    }

    /**
     * 设置上传监听
     * Sets upload listener
     *
     * @param uploadListener the listener
     */
    public void setOnUploadListener(UploadListener uploadListener) {
        mUploadListener = uploadListener;
    }

    /**
     * 试图上传草稿。
     * 注意：只有一次上传的资源全部完成后才能继续下次上传。
     * Try to upload draft.
     * Note: The next upload will not continue until all the resources uploaded in this upload are completed
     */
    List<UploadBean> draftResourceList;

    public void tryToUploadDraft(DraftData draftData, String token) {
        this.token = token;
        if (token == null || draftData == null) {
            return;
        }
        if (mUploadListener != null) {
            mUploadListener.onStart(draftData);
        }
        mDraftData = draftData;
        getFootageInfo(draftData, new ExportTemplateManager.OnExportListener() {
            @Override
            public void onCompleted(TemplateUploadParam destPath, boolean success) {
                tryToUploadResource(destPath, draftData, null, false);
            }

            @Override
            public void onCanceled(boolean isCanceled) {
                if (mUploadListener != null) {
                    mUploadListener.onFailed("Template is invalid!", -1);
                }
            }

            @Override
            public void onFailed(NvsTimeline timeline) {
                if (mUploadListener != null) {
                    mUploadListener.onFailed("Template is invalid!", -1);
                }
            }

            @Override
            public void onProgress(float progress) {

            }

            @Override
            public boolean isActive() {
                return true;
            }
        });
    }

    public void tryToUploadTemplate(TemplateUploadParam destPath, DraftData draftData, String token) {
        if (!TextUtils.isEmpty(token)) {
            if (mUploadListener != null) {
                mUploadListener.onStart(draftData);
            }
            this.token = token;
        }
        mDraftData = draftData;
        mTemplateUploadParam = destPath;
        String jsonPath = mTemplateUploadParam.templateDescFilePath;
        String jsonData = FileIOUtils.readFile2String(jsonPath, "utf-8");
       /* mTemplateDescInfo = GsonUtils.fromJson(jsonData, ExportTemplateDescInfo.class);
        if (mTemplateDescInfo == null) {
            if (mUploadListener != null) {
                mUploadListener.onFailed("Export template is error", -1);
            }
            return;
        }*/
        //mTemplateDescInfo.setName(draftData.getFileName());
        ThreadUtils.getSinglePool().execute(() -> {
            List<UploadBean> draftResourceList = getUploadList();
            if (!draftResourceList.isEmpty()) {
                ThreadUtils.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        mUploader.setUploadListener(new TemplateUploadCallBack());
                        mUploader.startUpload(ALiHelper.this.token, draftResourceList);
                    }
                });
            } else {
                ThreadUtils.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (mUploadListener != null) {
                            mUploadListener.onFailed("Export template is error", -1);
                        }
                    }
                });
            }
        });
    }

    public void tryToUploadResource(TemplateUploadParam destPath, DraftData draftData, String token, boolean needUploadProject) {
        if (!TextUtils.isEmpty(token)) {
            if (mUploadListener != null) {
                mUploadListener.onStart(draftData);
            }
            this.token = token;
        }
        mDraftData = draftData;
        mTemplateUploadParam = destPath;
        String jsonPath = mTemplateUploadParam.templateDescFilePath;
        String jsonData = FileIOUtils.readFile2String(jsonPath, "utf-8");
        mTemplateDescInfo = GsonUtils.fromJson(jsonData, ExportTemplateDescInfo.class);
        if (mTemplateDescInfo == null) {
            if (mUploadListener != null) {
                mUploadListener.onFailed("Export template is error", -1);
            }
            return;
        }
        mTemplateDescInfo.setName(draftData.getFileName());
        mTemplateDescInfoBackUp = mTemplateDescInfo;
        ThreadUtils.getSinglePool().execute(() -> {
            draftResourceList = getUploadList(draftData);
            if (!draftResourceList.isEmpty()) {
                for (UploadBean uploadBean : draftResourceList) {
                    if (FileUtils.isAndroidQUriPath(uploadBean.pathValue)) {
                        uploadBean.originalPathValue = uploadBean.pathValue;
                        uploadBean.pathValue = AndroidVersionUtils.getRealPathFromUri(Utils.getApp().getApplicationContext(), Uri.parse(uploadBean.pathValue));
                    }
                    String filePath = uploadBean.pathValue;
                    String extensionName = FileUtils.getFileExtension(filePath);
                    if (isAssetFile(filePath)) {
                        //如果是asset目录文件，写入到上传缓存目录中,文件名称是复用的，防止上传目录一直增大
                        //If it is an asset directory file, write it to the upload cache directory,
                        // and the file name is reused to prevent the upload directory from continuously increasing.
                        uploadBean.originalPathValue = uploadBean.pathValue;
                        uploadBean.pathValue = writeAssetsFileToUploadCache(uploadBean.pathKey + "." + extensionName,
                                filePath);
                    }
                    if (uploadBean.pathKey == UploadBean.KEY_BACKGROUND) {
                        uploadBean.setUploadModule("material_bgimage");
                        uploadBean.uuid = mBackgroundUuid;
                    } else {
                        uploadBean.setUploadModule(getUploadModule(uploadBean.pathValue));
                        uploadBean.uuid = mUuid;
                    }
                }
                ThreadUtils.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (needUploadProject) {
                            mUploader.setUploadListener(new UploadDirectCallback());
                        } else {
                            mUploader.setUploadListener(new ResourceUploadCallback());
                        }
                        mUploader.startUpload(ALiHelper.this.token, draftResourceList);
                    }
                });
            } else {
                ThreadUtils.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        //上传模板文件信息 Upload template file information
                        if (!uploadTemplateFile()) {
                            if (mUploadListener != null) {
                                mUploadListener.onFailed("Uploading info file error", -1);
                            }
                        }
                    }
                });
            }
        });
    }

    private List<UploadBean> getUploadList(DraftData draftData) {
        List<UploadBean> list = new ArrayList<>();
        String coverImagePath = draftData.getCoverPath();
        if (!TextUtils.isEmpty(coverImagePath)) {
            if (!filterFile(coverImagePath)) {
                list.add(getUploadBean(list.size(), UploadBean.KEY_COVER, coverImagePath));
            }
        }
        //project file
        if (mTemplateUploadParam != null && mTemplateUploadParam.materialFile != null) {
            list.add(new UploadBean(list.size(), UploadBean.KEY_PROJECT_TEMPLATE,
                    mTemplateUploadParam.materialFile.getAbsolutePath()));
        }
        if (mTemplateDescInfo != null) {
            List<ExportTemplateDescInfo.FootageInfoWrapper> footageInfos = mTemplateDescInfo.getFootageInfos();
            if (!CommonUtils.isEmpty(footageInfos)) {
                List<ExportTemplateDescInfo.FootageInfo> infos = footageInfos.get(0).getInfos();
                if (!CommonUtils.isEmpty(infos)) {
                    for (ExportTemplateDescInfo.FootageInfo info : infos) {
                        if (!info.url.startsWith("http")) {
                            if (filterFile(info.url)) {
                                continue;
                            }
                            if (!TextUtils.isEmpty(info.extraData) && info.extraData.endsWith("_isBackground")) {
                                mBackgroundUuid = info.extraData.replaceAll("_isBackground", "");
                                info.extraData = mBackgroundUuid + "." + FileUtils.getFileSuffix(info.url);
                                UploadBean uploadBean = getUploadBean(list.size(), UploadBean.KEY_BACKGROUND, info.url);
                                list.add(uploadBean);
                            } else {
                                UploadBean uploadBean = getUploadBean(list.size(), UploadBean.KEY_VIDEO, info.url);
                                list.add(uploadBean);
                            }
                        } else {
                            String userId = "";
                            IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
                            if (userPlugin != null) {
                                userId = userPlugin.getUserId();
                            }
                            FileInfoEntity fileByUrl = mFileInfoDao.getFileByUrl(info.url, userId);
                            if (fileByUrl != null) {
                                FileInfoBridge.FileInfo bridge = new FileInfoBridge.FileInfo(info.url);
                                bridge.setRemotePath(info.url).setFilePath(fileByUrl.getM3u8CommonUrl())
                                        .setResourceId(fileByUrl.getResourceId());
                                FileInfoBridge.putFileInFo(mUuid, bridge);
                            }
                        }
                    }
                }
            }
        }
        return list;
    }

    private List<UploadBean> getUploadList() {
        List<UploadBean> list = new ArrayList<>();
        //project file
        if (mTemplateUploadParam != null && mTemplateUploadParam.materialFile != null) {
            UploadBean uploadBean = new UploadBean(list.size(), UploadBean.KEY_PROJECT_TEMPLATE,
                    mTemplateUploadParam.materialFile.getAbsolutePath());
            uploadBean.setUploadModule("multi_project");
            uploadBean.uuid = mUuid;
            list.add(uploadBean);
        }
        return list;
    }

    private boolean filterFile(String filePath) {
        //是本地文件，但是本地文件已经上传，不需要上传
        // It is a local file, but the local file has already been uploaded and does not need to be uploaded
        String fileMD5 = FileUtils.getFileMD5ToString(filePath);
        String userId = "";
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin != null) {
            userId = userPlugin.getUserId();
        }
        FileInfoEntity fileInfo = mFileInfoDao.getFile(fileMD5, userId);
        if (fileInfo != null) {
            String url = fileInfo.getUrl();
            String m3u8CommonUrl = fileInfo.getM3u8CommonUrl();
            if (!TextUtils.isEmpty(url) && !TextUtils.isEmpty(m3u8CommonUrl)) {
                mLocalToNetFileMap.put(filePath, url);
                FileInfoBridge.FileInfo info = new FileInfoBridge.FileInfo(filePath);
                info.setFilePath(m3u8CommonUrl);
                info.setRemotePath(url);
                info.setResourceId(fileInfo.getResourceId());
                FileInfoBridge.putFileInFo(mUuid, info);
                return true;
            }
        }
        return false;
    }

    private UploadBean getUploadBean(int index, int key, String filePath) {
        return new UploadBean(index, key, filePath);
    }

    public void cancelAllNetTask() {
        mUploader.cancelAllNetTask();
    }

    private boolean uploadInfoData() {
        if (mTemplateDescInfo != null) {
            mTemplateDescInfo.setCover(mLocalToNetFileMap.get(mDraftData.getCoverPath()));
            mTemplateDescInfo.setTemplatePath(mLocalToNetFileMap.get(mTemplateUploadParam.materialFile.getAbsolutePath()));
            List<ExportTemplateDescInfo.InnerAssetWrapper> innerAssets = mTemplateDescInfo.getInnerAssets();
            if (!CommonUtils.isEmpty(innerAssets)) {
                List<ExportTemplateDescInfo.InnerAsset> assets = innerAssets.get(0).getAssets();
                if (!CommonUtils.isEmpty(assets)) {
                    for (ExportTemplateDescInfo.InnerAsset asset : assets) {
                        String httpPath = mLocalToNetFileMap.get(asset.url);
                        if (!TextUtils.isEmpty(httpPath)) {
                            asset.url = httpPath;
                        }
                    }
                } else {
                    mTemplateDescInfo.setInnerAssets(null);
                }
            }
            List<ExportTemplateDescInfo.FootageInfoWrapper> footageInfos = mTemplateDescInfo.getFootageInfos();
            if (!CommonUtils.isEmpty(footageInfos)) {
                List<ExportTemplateDescInfo.FootageInfo> infos = footageInfos.get(0).getInfos();
                if (!CommonUtils.isEmpty(infos)) {
                    for (ExportTemplateDescInfo.FootageInfo info : infos) {
                        FileInfoBridge.FileInfo fileInfo = FileInfoBridge.getFileInfo(info.url);
                        if (fileInfo != null) {
                            info.resourceId = fileInfo.resourceId;
                            info.m3u8FileName = fileInfo.filePath;
                            info.url = fileInfo.getRemotePath();
                            if (TextUtils.isEmpty(info.extraData) && TYPE_FOOTAGE_ORIGINAL.equals(info.type)) {
                                if (!TextUtils.isEmpty(info.url)) {
                                    String[] split = info.url.split("/");
                                    info.extraData = split[split.length - 1];
                                }
                            }
                        }
                    }
                }
            }
            String json = GsonUtils.toJson(mTemplateDescInfo);
            FileIOUtils.writeFileFromString(mTemplateUploadParam.templateDescFilePath, json);
            UploadBean uploadBean = new UploadBean(draftResourceList.size(), UploadBean.KEY_PROJECT_JSON, mTemplateUploadParam.templateDescFilePath);
            uploadBean.setUploadModule("multi_project");
            uploadBean.uuid = mUuid;
            List<UploadBean> uploadBeanList = new ArrayList<>(1);
            uploadBeanList.add(uploadBean);
            mUploader.setUploadListener(new TemplateInfoUploadCallback());
            mUploader.startUpload(token, uploadBeanList);
        } else {
            return false;
        }
        return true;
    }

    @NonNull
    private List<ExportTemplateDescInfo.FootageInfo> getInternalData() {
        List<ExportTemplateDescInfo.FootageInfo> backupInfo = new ArrayList<>();
        if (mTemplateDescInfoBackUp != null) {
            List<ExportTemplateDescInfo.FootageInfoWrapper> footageInfos = mTemplateDescInfoBackUp.getFootageInfos();
            if (!CommonUtils.isEmpty(footageInfos)) {
                List<ExportTemplateDescInfo.FootageInfo> infos = footageInfos.get(0).getInfos();
                if (!CommonUtils.isEmpty(infos)) {
                    for (ExportTemplateDescInfo.FootageInfo info : infos) {
                        if (ExportTemplateDescInfo.TYPE_FOOTAGE_INTERNAL.equals(info.type)) {
                            String httpPath = mLocalToNetFileMap.get(info.url);
                            FileInfoBridge.FileInfo fileInfo = FileInfoBridge.getFileInfo(info.url);
                            if (fileInfo != null) {
                                info.resourceId = fileInfo.resourceId;
                                //info.extraData = fileInfo.filePath;
                                info.m3u8FileName = fileInfo.filePath;
                            }
                            if (!TextUtils.isEmpty(httpPath)) {
                                info.url = httpPath;
                            }
                            backupInfo.add(info);
                        }
                    }
                }
            }
        }
        return backupInfo;
    }

    private boolean uploadTemplateFile() {
        if (meicamTimeline == null) {
            meicamTimeline = EditorEngine.getInstance().recoverTimeline(mDraftData.getJsonData());
        }
        if (meicamTimeline == null) {
            return false;
        }
        EditorEngine.getInstance().setCurrentTimeline(meicamTimeline);

        uploadExportTemplate(meicamTimeline, mDraftData.getFileName(),  new ExportTemplateManager.OnExportListener() {
            @Override
            public void onCompleted(TemplateUploadParam destPath, boolean success) {
                tryToUploadTemplate(destPath, mDraftData, null);
            }

            @Override
            public void onCanceled(boolean isCanceled) {
                if (mUploadListener != null) {
                    mUploadListener.onFailed("Export template is error", -1);
                }
            }

            @Override
            public void onFailed(NvsTimeline timeline) {
                if (mUploadListener != null) {
                    mUploadListener.onFailed("Export template is error", -1);
                }
            }

            @Override
            public void onProgress(float progress) {

            }

            @Override
            public boolean isActive() {
                return true;
            }
        });
        return true;
    }

    private void uploadExportTemplate(DraftData draftData, ExportTemplateManager.OnExportListener listener) {
        MeicamTimeline meicamTimeline = EditorEngine.getInstance().recoverTimeline(draftData.getJsonData());
        uploadExportTemplate(meicamTimeline, draftData.getFileName(), listener);
    }

    private void getFootageInfo(DraftData draftData, ExportTemplateManager.OnExportListener listener) {
        meicamTimeline = EditorEngine.getInstance().recoverTimeline(draftData.getJsonData());
        if (meicamTimeline == null) {
            return;
        }
        EditorEngine.getInstance().setCurrentTimeline(meicamTimeline);
        mExportManager = new ExportTemplateManager();
        mExportManager.setOnExportListener(listener);
        try {
            if (TextUtils.isEmpty(mUuid)) {
                mUuid = meicamTimeline.getProjectId().toUpperCase();
            }
            FileInfoBridge.sCurrentProject = mUuid;
            mExportManager.exportTemplateInfo(meicamTimeline, NvsStreamingContext.getInstance(),
                    mUuid);
        } catch (Exception e) {
            if (listener != null) {
                listener.onFailed(null);
            }
        }
    }

    private void uploadExportTemplate(MeicamTimeline meicamTimeline, String name, ExportTemplateManager.OnExportListener listener) {
        EditorEngine engine = EditorEngine.getInstance();
        if (mExportManager == null) {
            mExportManager = new ExportTemplateManager();
        }
        if (meicamTimeline == null) {
            if (listener != null) {
                listener.onFailed(null);
            }
            return;
        }
        engine.setCurrentTimeline(meicamTimeline);
        mExportManager.setOnExportListener(listener);
        try {
            if (TextUtils.isEmpty(mUuid)) {
                mUuid = meicamTimeline.getProjectId().toUpperCase();
            }
            mExportManager.exportSampleTemplate(meicamTimeline, engine.getStreamingContext(),
                    name, "", mUuid, getInternalData());
        } catch (Exception e) {
            if (listener != null) {
                listener.onFailed(null);
            }
        }
    }

    /**
     * 最后一步，通知云剪辑
     * The last step ,notify cloud edit
     */
    private void createOrUpdateCloudProject(String infoUrl, String templateUrl, String projectId) {
        //上传资源成功，同步project
        // Successfully uploaded resources, synchronized project.
        AppNetAPi.createOrUpdateProject(infoUrl, templateUrl, projectId, token, new RequestCallback<TransformResponse>() {

            @Override
            public void onSuccess(BaseResponse<TransformResponse> response) {
                ThreadUtils.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (mUploadListener != null) {
                            if (response.getCode() == 0) {
                                TransformResponse data = response.getData();
                                if (data != null && mDraftData != null) {
                                    long modifiedAt = data.getModifyAt() * 1000L;
                                    mDraftData.setLastModifyTimeLong(modifiedAt);
                                    mDraftData.setProjectId(data.getUuid().toUpperCase());
                                    DraftData.CloudInfo cloudInfo = mDraftData.getCloudInfo();
                                    cloudInfo.projectId = String.valueOf(data.getProjectId());
                                    cloudInfo.uuid = data.getUuid();
                                    cloudInfo.templateUrl = mProjectUrl;
                                    cloudInfo.infoUrl = mInfoJsonUrl;
                                    cloudInfo.templatePath = mTemplateUploadParam.materialFile.getAbsolutePath();
                                    cloudInfo.infoPath = mTemplateUploadParam.templateDescFilePath;
                                }
                                mUploadListener.onSuccess(mDraftData, PreferencesManager.get().getString("account"));
                            } else {
                                mUploadListener.onFailed(response.getMessage(), response.getCode());
                            }
                        }
                    }
                });
            }

            @Override
            public void onError(BaseResponse<TransformResponse> response) {
                LogUtils.e("transformProject onFailed = " + response.getMessage());
                if (mUploadListener != null) {
                    mUploadListener.onFailed(response.getMessage(), response.getCode());
                }
            }
        });
    }

    private void addBackgroundMaterial(String fileUrl) {
        //上传背景成功，通知后台 Successfully uploaded background, notify the service.
        AppNetAPi.addBackgroundMaterial(mBackgroundUuid, fileUrl, token, new RequestCallback<TransformResponse>() {

            @Override
            public void onSuccess(BaseResponse<TransformResponse> response) {
                ThreadUtils.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (mUploadListener != null) {
                            if (response.getCode() != 0) {
                                mUploadListener.onFailed(response.getMessage(), response.getCode());
                                cancelAllNetTask();
                            }
                        }
                    }
                });
            }

            @Override
            public void onError(BaseResponse<TransformResponse> response) {
                LogUtils.e("transformProject onFailed = " + response.getMessage());
                if (mUploadListener != null) {
                    mUploadListener.onFailed(response.getMessage(), response.getCode());
                }
            }
        });
    }

    /**
     * 写入上传缓存目录
     * Write to the upload cache directory
     *
     * @param newFileName the new file name
     */
    private String writeAssetsFileToUploadCache(String newFileName, String filePath) {
        if (TextUtils.isEmpty(newFileName)) {
            LogUtils.d("Error, file name or file content is null , check it!!!");
            return null;
        }
        File cacheDir = Utils.getApp().getCacheDir();
        FileUtils.createOrExistsDir(cacheDir);
        String uploadDir = cacheDir.getAbsolutePath() + File.separator + "UploadCloud";
        FileUtils.createOrExistsDir(uploadDir);
        File outFile = new File(uploadDir, newFileName);
        FileUtils.createOrExistsFile(outFile);
        InputStream in = null;
        try {
            in = Utils.getApp().getAssets().open(replaceAssetFilePath(filePath));
        } catch (IOException e) {
            e.printStackTrace();
        }
        FileIOUtils.writeFileFromIS(outFile, in);
        return outFile.getAbsolutePath();
    }


    /**
     * 是否是Asset资源文件
     * Whether it is an Asset resource file
     *
     * @param filePath the file path
     * @return true is ,false not
     */
    private boolean isAssetFile(String filePath) {
        return filePath.startsWith("assets:/") || (filePath.startsWith("file:///android_asset/"));
    }

    /**
     * 是否是Asset资源文件
     * replace Asset File Path
     *
     * @param filePath the file path
     * @return filePath
     */
    private static String replaceAssetFilePath(String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            return "";
        }
        if (filePath.startsWith("assets:/")) {
            filePath = filePath.replace("assets:/", "");
        }
        if (filePath.startsWith("file:///android_asset/")) {
            filePath = filePath.replace("file:///android_asset/", "");
        }
        return filePath;
    }

    /**
     * 获取上传的模块
     * Get the upload module
     *
     * @param filePath the file path
     */
    private String getUploadModule(String filePath) {
        if (MediaTypeUtils.isAudioFileType(filePath)) {
            return "resource_audio";
        } else if (MediaTypeUtils.isVideoFileType(filePath)) {
            return "resource_video";
        } else if (MediaTypeUtils.isImageFileType(filePath)) {
            return "resource_image";
        }
       /* else if (MediaTypeUtils.isTXTFileType(filePath)) {
            return "app_project";
        }*/
        else if (filePath.endsWith(".template")
                || filePath.endsWith("info.json")
                || filePath.endsWith(".project")) {
            return "multi_project";
        } else {
            NvsAVFileInfo avFileInfo = NvsStreamingContext.getInstance().getAVFileInfo(filePath);
            if (avFileInfo != null) {
                int avFileType = avFileInfo.getAVFileType();
                if (avFileType == NvsAVFileInfo.AV_FILE_TYPE_AUDIOVIDEO) {
                    return "resource_video";
                } else if (avFileType == NvsAVFileInfo.AV_FILE_TYPE_AUDIO) {
                    return "resource_audio";
                } else if (avFileType == NvsAVFileInfo.AV_FILE_TYPE_IMAGE) {
                    return "resource_image";
                } else {
                    return "temp";
                }
            }
            return "temp";
        }
    }

    public void setUuid(String uuid) {
        mUuid = uuid;
    }

    public String getTaskId() {
        return mUuid;
    }

    /**
     * The type Resource upload callback.
     * 资源文件的回调
     */
    public class ResourceUploadCallback implements Uploader.UploadListener {

        @Override
        public void onSuccess(List<UploadBean> uploadBeanList, String projectId) {
            ThreadUtils.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (!CommonUtils.isEmpty(uploadBeanList)) {
                        for (UploadBean uploadBean : uploadBeanList) {
                            String url = uploadBean.remoteInfo.url;
                            String key = TextUtils.isEmpty(uploadBean.originalPathValue) ? uploadBean.pathValue: uploadBean.originalPathValue;
                            FileInfoBridge.FileInfo info = new FileInfoBridge.FileInfo(key);
                            info.setRemotePath(url).setFilePath(uploadBean.remoteInfo.m3u8CommonUrl).setResourceId(uploadBean.remoteInfo.resourceId);
                            FileInfoBridge.putFileInFo(mUuid, info);
                            mLocalToNetFileMap.put(key, url);
                            if (UploadBean.KEY_PROJECT_TEMPLATE == uploadBean.pathKey) {
                                mProjectUrl = url;
                            }
                            if (UploadBean.KEY_BACKGROUND == uploadBean.pathKey) {
                                addBackgroundMaterial(url);
                            }
                        }
                    }
                    //上传模板文件信息 Upload template file information
                    if (!uploadTemplateFile()) {
                        if (mUploadListener != null) {
                            mUploadListener.onFailed("Uploading info file error", -1);
                        }
                    }
                }
            });

            if (!CommonUtils.isEmpty(uploadBeanList)) {
                ThreadUtils.getSinglePool().execute(() -> {
                    for (UploadBean uploadBean : uploadBeanList) {
                        String url = uploadBean.remoteInfo.url;
                        if (UploadBean.KEY_PROJECT_TEMPLATE != uploadBean.pathKey) {
                            final String localPath = uploadBean.pathValue;
                            String fileMD5 = FileUtils.getFileMD5ToString(localPath);
                            String userId = "";
                            IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
                            if (userPlugin != null) {
                                userId = userPlugin.getUserId();
                            }
                            FileInfoEntity fileInfo = mFileInfoDao.getFile(fileMD5, userId);
                            if (fileInfo != null) {
                                fileInfo.setLocalPath(localPath);
                                fileInfo.setUrl(url);
                                fileInfo.setM3u8CommonUrl(uploadBean.remoteInfo.m3u8CommonUrl);
                                fileInfo.setM3u8AlphaUrl(uploadBean.remoteInfo.m3u8AlphaUrl);
                                fileInfo.setM3u8ReverseUrl(uploadBean.remoteInfo.m3u8ReverseUrl);
                                fileInfo.setM3u8ReverseAlphaUrl(uploadBean.remoteInfo.m3u8ReverseAlphaUrl);
                                fileInfo.setResourceId(uploadBean.remoteInfo.resourceId);
                                mFileInfoDao.updateDraft(fileInfo);
                            } else {
                                fileInfo = new FileInfoEntity();
                                fileInfo.setMd5(fileMD5);
                                fileInfo.setId(fileMD5 + userId);
                                fileInfo.setLocalPath(localPath);
                                fileInfo.setUrl(url);
                                fileInfo.setM3u8CommonUrl(uploadBean.remoteInfo.m3u8CommonUrl);
                                fileInfo.setM3u8AlphaUrl(uploadBean.remoteInfo.m3u8AlphaUrl);
                                fileInfo.setM3u8ReverseUrl(uploadBean.remoteInfo.m3u8ReverseUrl);
                                fileInfo.setM3u8ReverseAlphaUrl(uploadBean.remoteInfo.m3u8ReverseAlphaUrl);
                                fileInfo.setResourceId(uploadBean.remoteInfo.resourceId);
                                fileInfo.setUserId(userId);
                                mFileInfoDao.insertDraft(fileInfo);
                            }
                        }
                    }
                });
            }
        }

        @Override
        public void onFailed(String message, int code) {
            if (mUploadListener != null) {
                mUploadListener.onFailed(message, code);
            }
        }

        @Override
        public void onProgress(long progress, long total) {
            if (mUploadListener != null) {
                long realProgress = (long) (progress / 100F * RESOURCE_MAX_PROGRESS);
                mUploadListener.onProgress(realProgress, total);
            }

        }
    }

    /**
     * The type Template info upload call back.
     * info.json的回调
     */
    private class TemplateInfoUploadCallback implements Uploader.UploadListener {

        @Override
        public void onSuccess(List<UploadBean> uploadBean, String projectId) {
            //最后通知云剪辑 Finally notify cloud editing.
            if (CommonUtils.isEmpty(uploadBean)) {
                if (mUploadListener != null) {
                    mUploadListener.onFailed("Param is error!", -1);
                }
                return;
            }
            mInfoJsonUrl = uploadBean.get(0).remoteInfo.url;
            createOrUpdateCloudProject(mInfoJsonUrl, mProjectUrl, projectId);
        }


        @Override
        public void onFailed(String message, int code) {
            if (mUploadListener != null) {
                mUploadListener.onFailed(message, code);
            }
        }

        @Override
        public void onProgress(long progress, long total) {
            if (mUploadListener != null) {
                long realProgress = (long) (progress / 100F * RESOURCE_MAX_PROJECT + RESOURCE_MAX_PROGRESS + RESOURCE_MAX_PROJECT);
                mUploadListener.onProgress(realProgress, total);
            }
        }
    }

    /**
     * The Template info upload listener.
     * 模板上传的回调
     */
    private class TemplateUploadCallBack implements Uploader.UploadListener {

        @Override
        public void onSuccess(List<UploadBean> uploadBean, String projectId) {
            ThreadUtils.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (CommonUtils.isEmpty(uploadBean)) {
                        if (mUploadListener != null) {
                            mUploadListener.onFailed("Template file is uploaded error", -1);
                        }
                        return;
                    }
                    UploadBean uploadBeanItem = uploadBean.get(0);
                    mProjectUrl = uploadBeanItem.remoteInfo.url;
                    mLocalToNetFileMap.put(uploadBeanItem.pathValue, mProjectUrl);
                    if (!uploadInfoData()) {
                        if (mUploadListener != null) {
                            mUploadListener.onFailed("Uploading info file error", -1);
                        }
                    }
                }
            });
        }


        @Override
        public void onFailed(String message, int code) {
            if (mUploadListener != null) {
                mUploadListener.onFailed(message, code);
            }
        }

        @Override
        public void onProgress(long progress, long total) {
            if (mUploadListener != null) {
                long realProgress = (long) (progress / 100F * RESOURCE_MAX_PROJECT + RESOURCE_MAX_PROGRESS);
                mUploadListener.onProgress(realProgress, total);
            }
        }
    }

    /**
     * The type Template info upload call back.
     * info.json的回调
     */
    private class UploadDirectCallback implements Uploader.UploadListener {

        @Override
        public void onSuccess(List<UploadBean> uploadBean, String projectId) {
            //最后通知云剪辑 Finally notify cloud editing
            if (CommonUtils.isEmpty(uploadBean)) {
                if (mUploadListener != null) {
                    mUploadListener.onFailed("Param is error!", -1);
                }
                return;
            }
            for (UploadBean bean : uploadBean) {
                if (UploadBean.KEY_PROJECT_TEMPLATE == bean.pathKey) {
                    mProjectUrl = bean.remoteInfo.url;
                } else if (UploadBean.KEY_PROJECT_JSON == bean.pathKey) {
                    mInfoJsonUrl = bean.remoteInfo.url;
                }
                mLocalToNetFileMap.put(bean.pathValue, bean.remoteInfo.url);
            }
           if (!uploadInfoData()) {
               if (mUploadListener != null) {
                   mUploadListener.onFailed("Uploading info file error", -1);
               }
           }
        }


        @Override
        public void onFailed(String message, int code) {
            if (mUploadListener != null) {
                mUploadListener.onFailed(message, code);
            }
        }

        @Override
        public void onProgress(long progress, long total) {
            if (mUploadListener != null) {
                mUploadListener.onProgress(progress, total);
            }
        }
    }

    public interface UploadListener {

        void onSuccess(DraftData data, String userName);

        void onFailed(String message, int code);

        void onProgress(long progress, long total);


        void onStart(DraftData data);
    }
}