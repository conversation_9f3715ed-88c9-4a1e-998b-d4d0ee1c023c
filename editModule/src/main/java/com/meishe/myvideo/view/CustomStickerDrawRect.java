package com.meishe.myvideo.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import com.meishe.base.utils.SizeUtils;
import com.meishe.myvideo.R;

import androidx.annotation.Nullable;

/**
 * 对选择的图片进行裁剪
 * Crop the selected image
 */
public class CustomStickerDrawRect extends View {
    public static final int FREEDOM_MODE = 2003;
    public static final int CIRCLE_MODE = 2004;
    public static final int SQUARE_MODE = 2005;
    private RectF mViewRectF = new RectF();
    private RectF scaleRectF = new RectF();
    private Paint mRectPaint = new Paint();
    private int mViewMode = 0;
    private Bitmap mScaleBitmap;
    private float mScaleViewWidthHalf;
    private float mScaleViewHeightHalf;
    private float prevRawX = 0;
    private float prevRawY = 0;
    private OnDrawRectListener mDrawRectListener;
    /**
     * 最小宽度值
     * Minimum width value
     * */
    private int mMinWidth = 100;
    /**
     * 最小高度值
     * Minimum height value
     * */
    private int mMinHeight = 100;
    private boolean canScaleRect = false;
    private boolean canMoveRect = false;

    private int mLeft;
    private int mRight;
    private int mTop;
    private int mBottom;

    /**
     * Sets rect area.
     * 设置矩形区域
     * @param left   the left 左
     * @param top    the top 顶部
     * @param right  the right 右
     * @param bottom the bottom底部
     */
    public void setRectArea(int left, int top, int right, int bottom) {
        this.mLeft = left;
        this.mTop = top;
        this.mRight = right;
        this.mBottom = bottom;
    }

    public CustomStickerDrawRect(Context context) {
        this(context, null);
    }

    public CustomStickerDrawRect(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        //setBackgroundColor(getResources().getColor(R.color.gray_4b4));
        mScaleBitmap = BitmapFactory.decodeResource(getResources(), R.mipmap.custom_sticker_scale);
        mScaleViewWidthHalf = getScaleViewWidth() / 2f;
        mScaleViewHeightHalf = getScaleViewHeight() / 2f;
        initRectPaint();
    }

    /**
     * Gets min width.
     * 获取宽度最小值
     * @return the min width 宽度最小值
     */
    public int getMinWidth() {
        return mMinWidth;
    }

    /**
     * Sets min width.
     * 设置宽度最小值
     * @param minWidth the min width 宽度最小值
     */
    public void setMinWidth(int minWidth) {
        this.mMinWidth = minWidth;
    }

    public int getMinHeight() {
        return mMinHeight;
    }

    public void setMinHeight(int minHeight) {
        this.mMinHeight = minHeight;
    }

    public int getScaleViewWidth() {
        return mScaleBitmap.getWidth();
    }

    public int getScaleViewHeight() {
        return mScaleBitmap.getHeight();
    }

    public void setDrawRect(RectF rectF, int mode) {
        mViewRectF = rectF;
        mViewMode = mode;
        invalidate();
    }

    public int getViewMode() {
        return mViewMode;
    }

    public void setOnDrawRectListener(OnDrawRectListener drawRectListener) {
        mDrawRectListener = drawRectListener;
    }

    private void initRectPaint() {
        /*
         * 设置颜色
         * Set color
         * */
        mRectPaint.setColor(Color.parseColor("#FC3E3E"));
        /*
         * 设置抗锯齿
         * Setting up anti-aliasing
         * */
        mRectPaint.setAntiAlias(true);
        /*
         * 设置线宽
         * Set line width
         * */
        mRectPaint.setStrokeWidth(SizeUtils.dp2px(3));
        /*
         * 设置非填充
         * Set non-fill
         * */
        mRectPaint.setStyle(Paint.Style.STROKE);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        if (mViewMode == FREEDOM_MODE
                || mViewMode == SQUARE_MODE) {//
            /*
             * 自由模式或者正方模式，绘制矩形框
             * Free mode or square mode，draw rectangle
             * */
            canvas.drawRect(mViewRectF, mRectPaint);
            /*
             * 绘制放缩按钮
             * Draw zoom button
             * */
            canvas.drawBitmap(mScaleBitmap, mViewRectF.right - mScaleViewWidthHalf,
                    mViewRectF.bottom - mScaleViewHeightHalf, mRectPaint);
            scaleRectF.set(mViewRectF.right - mScaleViewWidthHalf, mViewRectF.bottom - mScaleViewHeightHalf,
                    mViewRectF.right + mScaleViewWidthHalf, mViewRectF.bottom + mScaleViewHeightHalf);
        } else if (mViewMode == CIRCLE_MODE) {
            //圆形模式 Circular patterns
            //handle heres
            float cx = mViewRectF.width() / 2;
            float cy = mViewRectF.height() / 2;
            float radius = cx >= cy ? cy : cx;
            float newCenterX = mViewRectF.left + radius;
            float newCenterY = mViewRectF.top + radius;
            canvas.drawCircle(newCenterX, newCenterY, radius, mRectPaint);
            float scaleCx = newCenterX + (float) (radius * Math.cos(2 * Math.PI * 45 / 360.0D));
            float scaleCy = newCenterY + (float) (radius * Math.sin(2 * Math.PI * 45 / 360.0D));
            /*
             * 绘制放缩按钮
             * Draw zoom button
             * */
            canvas.drawBitmap(mScaleBitmap, scaleCx - mScaleViewWidthHalf,
                    scaleCy - mScaleViewHeightHalf, mRectPaint);
            scaleRectF.set(scaleCx - mScaleViewWidthHalf, scaleCy - mScaleViewHeightHalf,
                    scaleCx + mScaleViewWidthHalf, scaleCy + mScaleViewHeightHalf);
        }
    }


    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        int action = event.getAction();
        if (action == MotionEvent.ACTION_DOWN) {
            canScaleRect = scaleRectF.contains(event.getX(), event.getY());
            canMoveRect = mViewRectF.contains(event.getX(), event.getY());
            prevRawX = event.getRawX();
            prevRawY = event.getRawY();
        } else if (action == MotionEvent.ACTION_MOVE) {
            float tempRawX = event.getRawX();
            float tempRawY = event.getRawY();
            int dx = (int) Math.floor(tempRawX - prevRawX + 0.5D);
            int dy = (int) Math.floor(tempRawY - prevRawY + 0.5D);
            prevRawX = tempRawX;
            prevRawY = tempRawY;
            if (mViewMode == FREEDOM_MODE) {
                if (canScaleRect) {
                    scaleRect(dx, dy);
                } else {
                    if (canMoveRect) {
                        moveRect(dx, dy);
                    }
                }
            } else if (mViewMode == CIRCLE_MODE
                    || mViewMode == SQUARE_MODE) {
                //handle here
                if (canScaleRect) {
                    if (dx > dy) {
                        dx = dy;
                    } else {
                        dy = dx;
                    }
                    scaleRect(dx, dy);
                } else {
                    if (canMoveRect) {
                        moveRect(dx, dy);
                    }
                }
            }

        } else if (action == MotionEvent.ACTION_UP) {
            canScaleRect = false;
            canMoveRect = false;
            if (mDrawRectListener != null) {
                mDrawRectListener.onDrawRect(new RectF(mViewRectF));
            }
        }
        invalidate();
        return true;
    }

    private void moveRect(int dx, int dy) {
        float drawRectWidth = mViewRectF.width();
        float drawRectHeight = mViewRectF.height();
        mViewRectF.left += dx;
        mViewRectF.right += dx;
        mViewRectF.top += dy;
        mViewRectF.bottom += dy;

        if (mViewRectF.left < mLeft) {
            mViewRectF.left = mLeft;
            mViewRectF.right = mViewRectF.left + drawRectWidth;
        }
        if (mViewRectF.right > mRight) {
            mViewRectF.right = mRight;
            mViewRectF.left = mRight - drawRectWidth;
        }
        if (mViewRectF.top < mTop) {
            mViewRectF.top = mTop;
            mViewRectF.bottom = mViewRectF.top + drawRectHeight;
        }
        if (mViewRectF.bottom > mBottom) {
            mViewRectF.bottom = mBottom;
            mViewRectF.top = mBottom - drawRectHeight;
        }
    }

    private void scaleRect(int dx, int dy) {
        if (mViewMode == CIRCLE_MODE) {
            //要保证RectF是正方形，这样圆形才能绘制正确，且缩放后上下移动的范围是正确的 Make sure the RectF is square so that the circle is drawn correctly and that it moves up and down correctly after scaling
            mViewRectF.right += dx;
            mViewRectF.bottom = mViewRectF.width() + mViewRectF.top;
        } else {
            mViewRectF.right += dx;
            mViewRectF.bottom += dy;
        }
        if (mViewRectF.right >= mRight) {
            mViewRectF.right = mRight;
            if (mViewMode == SQUARE_MODE) {
                mViewRectF.bottom -= dy;
            }
        }

        if (mViewRectF.bottom >= mBottom) {
            mViewRectF.bottom = mBottom;
            if (mViewMode == SQUARE_MODE) {
                mViewRectF.right -= dx;
            }
        }
        if (mViewRectF.width() <= mMinWidth) {
            mViewRectF.right = mViewRectF.left + mMinWidth;
        }
        if (mViewRectF.height() <= mMinHeight) {
            mViewRectF.bottom = mViewRectF.top + mMinHeight;
        }
    }

    /**
     * The interface On draw rect listener.
     * 监听画矩形的接口
     */
    public interface OnDrawRectListener {
        /**
         * On draw rect.
         * 画矩形
         * @param rectF the rect f
         */
        void onDrawRect(RectF rectF);
    }
}
