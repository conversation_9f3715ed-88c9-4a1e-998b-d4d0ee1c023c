package com.meishe.myvideo.activity.presenter;

import com.meishe.myvideo.view.IdentifyCaptionView;
import com.meishe.myvideo.view.TopContainer;
import com.meishe.speaker.VoiceDictationHelperWrapper;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate :2021/03/02 16:47
 * @Description :顶部视图显示帮助类 The TopContainer Helper
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class TopViewHelper {
    private TopContainer mTopView;

    public TopViewHelper(TopContainer topContainer) {
        mTopView = topContainer;
    }

    /**
     * 显示识别字幕视图
     * Show identify caption view
     *
     * @param complete true is complete,false not
     * @param dismiss  true need dismiss ,false not
     */
    public void showIdentifyView(boolean complete, boolean dismiss) {
        if (dismiss) {
            if (mTopView.getShowView() instanceof IdentifyCaptionView) {
                ((IdentifyCaptionView) mTopView.getShowView()).showIdentifyComplete();
                if (complete) {
                    mTopView.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            mTopView.dismissView();
                        }
                    }, 500);
                } else {
                    mTopView.dismissView();
                }
            }
        } else {
            if (mTopView.getShowView() instanceof IdentifyCaptionView) {
                return;
            }
            IdentifyCaptionView identifyCaptionView = new IdentifyCaptionView(mTopView.getContext());
            identifyCaptionView.setEventListener(new IdentifyCaptionView.EventListener() {
                @Override
                public void onDismiss() {
                    VoiceDictationHelperWrapper.get().cancelDictation();
                    mTopView.dismissView();
                }
            });
            identifyCaptionView.showIdentify();
            mTopView.showView(identifyCaptionView);
        }
    }
}
