package com.meishe.engine.asset.bean;

import com.meishe.base.utils.Utils;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.db.AssetEntity;

import java.io.Serializable;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/14 15:36
 * @Description :路径字典 The cloud path map
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CloudPathMapBean implements Serializable {
   private List<PathMap> elements;

   public List<PathMap> getElements() {
      return elements;
   }

   public void setElements(List<PathMap> elements) {
      this.elements = elements;
   }

   public static class PathMap{
      private String packageUrl;
      private String id;
      private String customDisPlayName;
      private String displayName;
      private String displayNameZhCn;
      private String coverUrl;
      private String minAppVersion;
      private int category = -1;
      private int type = -1;
      private int kind = -1;
      private int version = -1;

      public String getPackageUrl() {
         return packageUrl;
      }

      public void setPackageUrl(String packageUrl) {
         this.packageUrl = packageUrl;
      }

      public String getId() {
         return id;
      }

      public void setId(String id) {
         this.id = id;
      }

      public String getCustomDisPlayName() {
         return customDisPlayName;
      }

      public void setCustomDisPlayName(String customDisPlayName) {
         this.customDisPlayName = customDisPlayName;
      }

      public String getDisplayName() {
         return displayName;
      }

      public void setDisplayName(String displayName) {
         this.displayName = displayName;
      }

      public String getDisplayNameZhCn() {
         return displayNameZhCn;
      }

      public void setDisplayNameZhCn(String displayNameZhCn) {
         this.displayNameZhCn = displayNameZhCn;
      }

      public String getCoverUrl() {
         return coverUrl;
      }

      public int getCategory() {
         return category;
      }

      public void setCategory(int category) {
         this.category = category;
      }

      public int getType() {
         return type;
      }

      public void setType(int type) {
         this.type = type;
      }

      public int getKind() {
         return kind;
      }

      public void setKind(int kind) {
         this.kind = kind;
      }

      public void setCoverUrl(String coverUrl) {
         this.coverUrl = coverUrl;
      }

      public String getMinAppVersion() {
         return minAppVersion;
      }

      public void setMinAppVersion(String minAppVersion) {
         this.minAppVersion = minAppVersion;
      }

      public int getVersion() {
         return version;
      }

      public void setVersion(int version) {
         this.version = version;
      }
   }

   public static AssetEntity create(PathMap map){
      AssetEntity entity = new AssetEntity();
      entity.setCoverPath(map.getCoverUrl());
      entity.setId(map.getId());
      entity.setPackageId(map.getId());
      entity.setName(Utils.isZh()? map.getDisplayNameZhCn():map.getDisplayName());
      entity.setType(AssetsManager.parseTypeFromNewData(map.type, map.category, map.kind));
      entity.setVersion(map.version);
      return entity;
   }
}
