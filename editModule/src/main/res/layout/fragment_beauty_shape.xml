<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <LinearLayout
        android:id="@+id/ll_seek_bar_parent"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_110"
        android:layout_marginStart="@dimen/dp_px_36"
        android:layout_marginLeft="@dimen/dp_px_36"
        android:layout_marginTop="@dimen/dp_px_66"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:visibility="invisible">

        <ImageView
            android:id="@+id/iv_apply_all"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@mipmap/ic_multi_trans"
            android:contentDescription="@null" />

        <TextView
            android:id="@+id/tv_apply_all"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dp_px_15"
            android:layout_marginLeft="@dimen/dp_px_15"
            android:shadowColor="@color/black"
            android:shadowDx="0"
            android:shadowDy="1.0"
            android:shadowRadius="3.0"
            android:text="@string/apply_all_clip"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_30" />


        <TextView
            android:id="@+id/tv_start_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_px_39"
            android:layout_marginLeft="@dimen/dp_px_39"
            android:gravity="center"
            android:text="@string/fragment_beauty_list_start"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_30"
            android:visibility="gone"/>

        <com.meishe.myvideo.view.MYSeekBarTextView
            android:id="@+id/seek_bar"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_px_21"
            android:layout_marginEnd="@dimen/dp_px_81" />

        <TextView
            android:id="@+id/tv_end_text"
            android:layout_width="@dimen/dp_px_81"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/fragment_beauty_tv_end"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_30"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/tv_reset"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_px_42"
            android:layout_marginRight="@dimen/dp_px_42"
            android:drawableTop="@drawable/icon_reset"
            android:gravity="center_horizontal"
            android:text="@string/adjust_reset"
            android:textColor="@drawable/tv_reset"
            android:textSize="@dimen/sp_px_30" />
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_180"
        android:layout_marginTop="@dimen/dp_px_210"
        android:paddingLeft="@dimen/dp_px_30"
        android:paddingRight="@dimen/dp_px_30" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_108"
        android:layout_gravity="bottom">

        <View
            android:id="@+id/v_bottom_line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_px_1"
            android:background="@color/menu_divide_color" />

        <CheckBox
            android:id="@+id/cb_beauty_switch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dp_px_39"
            android:layout_marginLeft="@dimen/dp_px_39"
            android:button="@null"
            android:checked="false"
            android:drawableStart="@drawable/bg_checkbox_beauty"
            android:drawableLeft="@drawable/bg_checkbox_beauty"
            android:drawablePadding="@dimen/dp_px_15"
            android:text="@string/fragment_beauty_shape_open"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp12" />

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/dp_px_4"
            android:gravity="center"
            android:text="@string/tab_name_shape"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_36" />

        <ImageView
            android:id="@+id/iv_confirm"
            android:layout_width="@dimen/dp_px_70"
            android:layout_height="@dimen/dp_px_70"
            android:layout_gravity="end|center_vertical"
            android:layout_marginEnd="@dimen/dp_px_45"
            android:layout_marginRight="@dimen/dp_px_45"
            android:background="@mipmap/ic_confirm"
            android:contentDescription="@null" />
    </FrameLayout>
</FrameLayout>