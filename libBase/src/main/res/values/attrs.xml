<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- indicator -->
    <!-- 设置显示器颜色 -->
    <attr name="tl_indicator_color" format="color"/>
    <!-- 设置显示器高度 -->
    <attr name="tl_indicator_height" format="dimension"/>
    <!-- 设置显示器固定宽度 -->
    <attr name="tl_indicator_width" format="dimension"/>
    <!-- 设置显示器margin,当indicator_width大于0,无效 -->
    <attr name="tl_indicator_margin_left" format="dimension"/>
    <attr name="tl_indicator_margin_top" format="dimension"/>
    <attr name="tl_indicator_margin_right" format="dimension"/>
    <attr name="tl_indicator_margin_bottom" format="dimension"/>
    <!-- 设置显示器圆角弧度-->
    <attr name="tl_indicator_corner_radius" format="dimension"/>
    <!-- 设置显示器上方还是下方,只对圆角矩形有用-->
    <attr name="tl_indicator_gravity" format="enum">
        <enum name="TOP" value="48"/>
        <enum name="BOTTOM" value="80"/>
    </attr>
    <!-- 设置显示器为常规|三角形|背景色块|-->
    <attr name="tl_indicator_style" format="enum">
        <enum name="NORMAL" value="0"/>
        <enum name="TRIANGLE" value="1"/>
        <enum name="BLOCK" value="2"/>
    </attr>
    <!-- 设置显示器长度与title一样长,只有在STYLE_NORMAL并且indicatorWidth小于零有效-->
    <attr name="tl_indicator_width_equal_title" format="boolean"/>
    <!-- 设置显示器支持动画-->
    <attr name="tl_indicator_anim_enable" format="boolean"/>
    <!-- 设置显示器动画时间-->
    <attr name="tl_indicator_anim_duration" format="integer"/>
    <!-- 设置显示器支持动画回弹效果-->
    <attr name="tl_indicator_bounce_enable" format="boolean"/>

    <!-- underline -->
    <!-- 设置下划线颜色 -->
    <attr name="tl_underline_color" format="color"/>
    <!-- 设置下划线高度 -->
    <attr name="tl_underline_height" format="dimension"/>
    <!-- 设置下划线上方还是下方-->
    <attr name="tl_underline_gravity" format="enum">
        <enum name="TOP" value="48"/>
        <enum name="BOTTOM" value="80"/>
    </attr>

    <!-- divider -->
    <!-- 设置分割线颜色 -->
    <attr name="tl_divider_color" format="color"/>
    <!-- 设置分割线宽度 -->
    <attr name="tl_divider_width" format="dimension"/>
    <!-- 设置分割线的paddingTop和paddingBottom -->
    <attr name="tl_divider_padding" format="dimension"/>

    <!-- tab -->
    <!-- 设置tab的paddingLeft和paddingRight -->
    <attr name="tl_tab_padding" format="dimension"/>
    <!-- 设置tab大小等分 -->
    <attr name="tl_tab_space_equal" format="boolean"/>
    <!-- 设置tab固定大小 -->
    <attr name="tl_tab_width" format="dimension"/>

    <!-- title -->
    <!-- 设置字体大小 -->
    <attr name="tl_textSize" format="dimension"/>
    <!-- 设置字体选中颜色 -->
    <attr name="tl_textSelectColor" format="color"/>
    <!-- 设置字体未选中颜色 -->
    <attr name="tl_textUnselectedColor" format="color"/>
    <!-- 设置字体加粗 -->
    <attr name="tl_textBold" format="enum">
        <enum name="NONE" value="0"/>
        <enum name="SELECT" value="1"/>
        <enum name="BOTH" value="2"/>
    </attr>
    <!-- 设置字体全大写 -->
    <attr name="tl_textAllCaps" format="boolean"/>

    <declare-styleable name="SlidingTabLayout">
        <!-- indicator -->
        <attr name="tl_indicator_color"/>
        <attr name="tl_indicator_height"/>
        <attr name="tl_indicator_width"/>
        <attr name="tl_indicator_margin_left"/>
        <attr name="tl_indicator_margin_top"/>
        <attr name="tl_indicator_margin_right"/>
        <attr name="tl_indicator_margin_bottom"/>
        <attr name="tl_indicator_corner_radius"/>
        <attr name="tl_indicator_gravity"/>
        <attr name="tl_indicator_style"/>
        <attr name="tl_indicator_width_equal_title"/>

        <!-- underline -->
        <attr name="tl_underline_color"/>
        <attr name="tl_underline_height"/>
        <attr name="tl_underline_gravity"/>

        <!-- divider -->
        <attr name="tl_divider_color"/>
        <attr name="tl_divider_width"/>
        <attr name="tl_divider_padding"/>

        <!-- tab -->
        <attr name="tl_tab_padding"/>
        <attr name="tl_tab_space_equal"/>
        <attr name="tl_tab_width"/>

        <!-- title -->
        <attr name="tl_textSize"/>
        <attr name="tl_textSelectColor"/>
        <attr name="tl_textUnselectedColor"/>
        <attr name="tl_textBold"/>
        <attr name="tl_textAllCaps"/>

    </declare-styleable>

    <declare-styleable name="CommonTabLayout">
        <!-- indicator -->
        <attr name="tl_indicator_color"/>
        <attr name="tl_indicator_height"/>
        <attr name="tl_indicator_width"/>
        <attr name="tl_indicator_margin_left"/>
        <attr name="tl_indicator_margin_top"/>
        <attr name="tl_indicator_margin_right"/>
        <attr name="tl_indicator_margin_bottom"/>
        <attr name="tl_indicator_corner_radius"/>
        <attr name="tl_indicator_gravity"/>
        <attr name="tl_indicator_style"/>
        <attr name="tl_indicator_anim_enable"/>
        <attr name="tl_indicator_anim_duration"/>
        <attr name="tl_indicator_bounce_enable"/>

        <!-- underline -->
        <attr name="tl_underline_color"/>
        <attr name="tl_underline_height"/>
        <attr name="tl_underline_gravity"/>

        <!-- divider -->
        <attr name="tl_divider_color"/>
        <attr name="tl_divider_width"/>
        <attr name="tl_divider_padding"/>

        <!-- tab -->
        <attr name="tl_tab_padding"/>
        <attr name="tl_tab_space_equal"/>
        <attr name="tl_tab_width"/>

        <!-- title -->
        <attr name="tl_textSize"/>
        <attr name="tl_textSelectColor"/>
        <attr name="tl_textUnselectedColor"/>
        <attr name="tl_textBold"/>
        <attr name="tl_textAllCaps"/>

        <!-- icon -->
        <!-- 设置icon宽度 -->
        <attr name="tl_iconWidth" format="dimension"/>
        <!-- 设置icon高度 -->
        <attr name="tl_iconHeight" format="dimension"/>
        <!-- 设置icon是否可见 -->
        <attr name="tl_iconVisible" format="boolean"/>
        <!-- 设置icon显示位置,对应Gravity中常量值 -->
        <attr name="tl_iconGravity" format="enum">
            <enum name="LEFT" value="3"/>
            <enum name="TOP" value="48"/>
            <enum name="RIGHT" value="5"/>
            <enum name="BOTTOM" value="80"/>
        </attr>
        <!-- 设置icon与文字间距 -->
        <attr name="tl_iconMargin" format="dimension"/>

    </declare-styleable>

    <declare-styleable name="SegmentTabLayout">
        <!-- indicator -->
        <attr name="tl_indicator_color"/>
        <attr name="tl_indicator_height"/>
        <attr name="tl_indicator_margin_left"/>
        <attr name="tl_indicator_margin_top"/>
        <attr name="tl_indicator_margin_right"/>
        <attr name="tl_indicator_margin_bottom"/>
        <attr name="tl_indicator_corner_radius"/>
        <attr name="tl_indicator_anim_enable"/>
        <attr name="tl_indicator_anim_duration"/>
        <attr name="tl_indicator_bounce_enable"/>

        <!-- divider -->
        <attr name="tl_divider_color"/>
        <attr name="tl_divider_width"/>
        <attr name="tl_divider_padding"/>

        <!-- tab -->
        <attr name="tl_tab_padding"/>
        <attr name="tl_tab_space_equal"/>
        <attr name="tl_tab_width"/>

        <!-- title -->
        <attr name="tl_textSize"/>
        <attr name="tl_textSelectColor"/>
        <attr name="tl_textUnselectedColor"/>
        <attr name="tl_textBold"/>
        <attr name="tl_textAllCaps"/>

        <attr name="tl_bar_color" format="color"/>
        <attr name="tl_bar_stroke_color" format="color"/>
        <attr name="tl_bar_stroke_width" format="dimension"/>

    </declare-styleable>

    <declare-styleable name="MsgView">
        <!-- 圆角矩形背景色 -->
        <attr name="mv_backgroundColor" format="color"/>
        <!-- 圆角弧度,单位dp-->
        <attr name="mv_cornerRadius" format="dimension"/>
        <!-- 圆角弧度,单位dp-->
        <attr name="mv_strokeWidth" format="dimension"/>
        <!-- 圆角边框颜色-->
        <attr name="mv_strokeColor" format="color"/>
        <!-- 圆角弧度是高度一半-->
        <attr name="mv_isRadiusHalfHeight" format="boolean"/>
        <!-- 圆角矩形宽高相等,取较宽高中大值-->
        <attr name="mv_isWidthHeightEqual" format="boolean"/>
    </declare-styleable>

    <declare-styleable name="HorizontalSeekBar">
        <!--线（进度条）宽度-->
        <attr name="lineHeight" format="dimension" />
        <!--左侧字的大小 100元-->
        <attr name="leftTextSize" format="dimension" />
        <!--左侧字的颜色 100元-->
        <attr name="leftTextColor" format="color" />
        <!--右侧字的大小 100元-->
        <attr name="rightTextSize" format="dimension" />
        <!--右侧侧字的颜色 100元-->
        <attr name="rightTextColor" format="color" />
        <attr name="middleTextColor" format="color" />
        <!--两个游标内部 线（进度条）的颜色-->
        <attr name="inColor" format="color" />
        <!--两个游标外部 线（左边进度条）的颜色-->
        <attr name="leftOutColor" format="color" />
        <!--两个游标外部 线（右边进度条）的颜色-->
        <attr name="rightOutColor" format="color" />
        <!--一个游标的颜色-->
        <attr name="middleOutColor" format="color" />
        <!--左边图标的图片-->
        <attr name="imageLeft" format="reference"/>
        <!--右边图标 的图片-->
        <attr name="imageRight" format="reference"/>
        <!--游标 图片宽度-->
        <attr name="imageWidth" format="dimension" />
        <!--游标 图片高度-->
        <attr name="imageHeight" format="dimension" />
        <!--中轴线，往下偏移的-->
        <attr name="imageLowPadding" format="dimension" />
        <!--是否有刻度线-->
        <attr name="hasRule" format="boolean" />
        <!--刻度的颜色-->
        <attr name="ruleColor" format="color" />
        <!--刻度上边的字 的颜色-->
        <attr name="ruleTextColor" format="color" />
        <!--单位 元-->
        <attr name="unit" format="string"/>
        <!--单位份数-->
        <attr name="equal" format="integer"/>
        <!--刻度单位 $-->
        <attr name="ruleUnit" format="string"/>
        <!--刻度上边文字的size-->
        <attr name="ruleTextSize" format="dimension" />
        <!--刻度线的高度-->
        <attr name="ruleLineHeight" format="dimension" />
        <!--选择器的最大值-->
        <attr name="bigValue" format="integer"/>
        <!--选择器的最小值-->
        <attr name="smallValue" format="integer"/>
        <attr name="lineLength" format="dimension"/>
    </declare-styleable>
    <declare-styleable name="CompileProgress">
        <attr name="edit_progress" format="integer" />
        <attr name="maxProgress" format="integer" />
        <attr name="minProgress" format="integer" />
        <attr name="progressColor" format="color" />
        <attr name="progressBackgroundColor" format="color" />
        <attr name="progressWidth" format="dimension" />
    </declare-styleable>


    <declare-styleable name="ScaleView">
        <attr name="min_index" format="integer"/>
        <attr name="max_index" format="integer"/>
        <attr name="now_index" format="integer"/>
        <attr name="scale_value" format="integer"/>
        <attr name="text_size" format="float"/>
        <attr name="pointer_width" format="float"/>
        <attr name="pointer_head" format="float"/>
        <attr name="pointer_top" format="boolean"/>
        <attr name="pointer_top_protruding" format="boolean"/>
        <attr name="pointer_bottom_protruding" format="boolean"/>
        <attr name="show_pointer_head" format="boolean"/>
        <attr name="baseLine_width" format="float"/>
        <attr name="low_scale_width" format="float"/>
        <attr name="middle_scale_width" format="float"/>
        <attr name="high_scale_width" format="float"/>
        <attr name="line_interval" format="integer"/>
        <attr name="baseLine_margin_bottom" format="integer"/>
        <attr name="pointer_margin_top" format="integer"/>
        <attr name="left_margin_left" format="integer"/>
        <attr name="right_margin_right" format="integer"/>
        <attr name="font_margin_bottom" format="integer"/>
        <attr name="font_margin_top" format="integer"/>
        <attr name="low_pointer_margin" format="integer"/>
        <attr name="middle_pointer_margin" format="integer"/>
        <attr name="high_pointer_margin" format="integer"/>
        <attr name="middle_frequency" format="integer"/>
        <attr name="high_frequency" format="integer"/>
        <attr name="baseLine_color" format="color"/>
        <attr name="high_scale_color" format="color"/>
        <attr name="middle_scale_color" format="color"/>
        <attr name="low_scale_color" format="color"/>
        <attr name="num_color" format="color"/>
        <attr name="pointer_color" format="color"/>
        <attr name="font_top" format="boolean"/>
        <attr name="font_visible" format="boolean"/>
        <attr name="scale_ratio" format="float"/>
        <attr name="sliding_ratio" format="float"/>
        <attr name="show_baseLine" format="boolean"/>
        <attr name="units" format="float"/>
        <attr name="max_velocity" format="float"/>
        <attr name="strokeCap" format="enum">
            <enum name="BUTT" value="0" />
            <enum name="ROUND" value="1" />
            <enum name="SQUARE" value="2" />
        </attr>
        <attr name="scale_position" format="enum">
            <enum name="top" value="0" />
            <enum name="center" value="1" />
            <enum name="bottom" value="2" />
        </attr>
    </declare-styleable>

    <declare-styleable name="ColorBarView">
        <!-- 设置滑块图标-->
        <attr name="thumbDrawable" format="reference" />
        <!-- 设置彩色长条高度-->
        <attr name="barHeight" format="dimension" />
        <!-- 设置滑块图标高度，宽度会根据高度等比适应-->
        <attr name="thumbHeight" format="dimension" />
    </declare-styleable>
</resources>