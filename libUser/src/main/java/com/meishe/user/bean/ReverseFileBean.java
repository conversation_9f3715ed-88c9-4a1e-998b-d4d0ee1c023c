package com.meishe.user.bean;

import java.io.Serializable;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/8/18 13:32
 * @Description :倒放接口返回bean The bean of reverse file.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class ReverseFileBean {
   private int jobId;
   private long duration;
   private String coverUrl;
   private String createdAt;
   private String leftChannelUrl;
   private String rightChannelUrl;
   private String alphaM3u8Url;
   private int width;
   private int height;
   private String alphaUrl;
   private String url;
   private String m3u8Url;
   private ThumbnailInfo thumbnailInfo;


   public int getJobId() {
      return jobId;
   }

   public void setJobId(int jobId) {
      this.jobId = jobId;
   }

   public long getDuration() {
      return duration;
   }

   public void setDuration(long duration) {
      this.duration = duration;
   }

   public String getCoverUrl() {
      return coverUrl;
   }

   public void setCoverUrl(String coverUrl) {
      this.coverUrl = coverUrl;
   }

   public String getCreatedAt() {
      return createdAt;
   }

   public void setCreatedAt(String createdAt) {
      this.createdAt = createdAt;
   }

   public String getLeftChannelUrl() {
      return leftChannelUrl;
   }

   public void setLeftChannelUrl(String leftChannelUrl) {
      this.leftChannelUrl = leftChannelUrl;
   }

   public String getRightChannelUrl() {
      return rightChannelUrl;
   }

   public void setRightChannelUrl(String rightChannelUrl) {
      this.rightChannelUrl = rightChannelUrl;
   }

   public String getAlphaM3u8Url() {
      return alphaM3u8Url;
   }

   public void setAlphaM3u8Url(String alphaM3u8Url) {
      this.alphaM3u8Url = alphaM3u8Url;
   }

   public int getWidth() {
      return width;
   }

   public void setWidth(int width) {
      this.width = width;
   }

   public int getHeight() {
      return height;
   }

   public void setHeight(int height) {
      this.height = height;
   }

   public String getAlphaUrl() {
      return alphaUrl;
   }

   public void setAlphaUrl(String alphaUrl) {
      this.alphaUrl = alphaUrl;
   }

   public String getUrl() {
      return url;
   }

   public void setUrl(String url) {
      this.url = url;
   }

   public String getM3u8Url() {
      return m3u8Url;
   }

   public void setM3u8Url(String m3u8Url) {
      this.m3u8Url = m3u8Url;
   }

   public ThumbnailInfo getThumbnailInfo() {
      return thumbnailInfo;
   }

   public void setThumbnailInfo(ThumbnailInfo thumbnailInfo) {
      this.thumbnailInfo = thumbnailInfo;
   }

   public static class ThumbnailInfo implements Serializable {
      public String extension;
      public String urlPrefix;
      public long interval;
   }
}
