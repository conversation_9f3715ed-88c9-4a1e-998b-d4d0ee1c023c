package com.meishe.myvideo.view.presenter;

import android.content.Context;

import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.event.MessageEvent;
import com.meishe.myvideo.view.MYSpeedCurveMenu;

import java.util.List;

import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_CHANGE_SPEED_CURVE;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_CHANGE_SPEED_CURVE_VIEV_UPDATE;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_UPDATE_SELECT_POSITION;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/30 16:16
 * @Description :曲线变速Presenter Presenter for speed curve
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class SpeedCurvePresenter extends BaseConfirmPresenter<MYSpeedCurveMenu> {

    @Override
    public void onItemClicked(IBaseInfo info, boolean isSelected) {
        if (isSelected) {
            MessageEvent.sendEvent(info, MESSAGE_TYPE_CHANGE_SPEED_CURVE_VIEV_UPDATE);
        } else {
            MessageEvent.sendEvent(info, MESSAGE_TYPE_CHANGE_SPEED_CURVE);
        }
    }

    @Override
    public void onMessageEvent(MessageEvent event) {
        super.onMessageEvent(event);
        int eventType = event.getEventType();
        if (eventType == MESSAGE_TYPE_UPDATE_SELECT_POSITION) {
            int intValue = event.getIntValue();
            getView().updateSelectPosition(intValue);
        }
    }

    @Override
    public void onProgressChanged(float progress, String tag, boolean isFromUser) {

    }

    @Override
    public void onStopTrackingTouch() {

    }

    @Override
    public void reset(List<IBaseInfo> data) {

    }

    @Override
    public void confirm() {

    }

    @Override
    public void applyToAll() {

    }

    @Override
    public void getData(Context context) {

    }

    @Override
    public boolean updateData(int subType, boolean needForceUpdate) {
        return true;
    }
}
