<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:gravity="center"
    android:orientation="horizontal">
    <ImageView
        android:contentDescription="@null"
        android:layout_width="wrap_content"
        android:layout_weight="1"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:src="@drawable/ic_user_assets_unavailable_dash_line"/>
    <TextView
        android:paddingStart="@dimen/dp_px_39"
        android:paddingEnd="@dimen/dp_px_39"
        android:background="@color/white"
        android:id="@+id/tv_text"
        android:gravity="center"
        android:layout_gravity="center_horizontal"
        android:textColor="@color/color_ffed1111"
        android:textSize="@dimen/dp_px_39"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>
    <ImageView
        android:contentDescription="@null"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:layout_gravity="center_vertical"
        android:src="@drawable/ic_user_assets_unavailable_dash_line"/>
</LinearLayout>
