<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/black">


    <ImageView
        android:id="@+id/iv_record"
        android:layout_width="@dimen/dp_px_132"
        android:layout_height="@dimen/dp_px_132"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_px_50"
        android:contentDescription="@null"
        android:src="@drawable/btn_record_selector" />

    <TextView
        android:id="@+id/tv_start_record"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_px_212"
        android:layout_marginBottom="@dimen/dp_px_60"
        android:text="@string/start_record"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_30" />

    <ImageView
        android:id="@+id/iv_record_confirm"
        android:layout_width="@dimen/dp_px_60"
        android:layout_height="@dimen/dp_px_60"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="@dimen/dp_px_45"
        android:layout_marginRight="@dimen/dp_px_45"
        android:layout_marginBottom="@dimen/dp_px_50"
        android:contentDescription="@null"
        android:src="@mipmap/ic_record_confirm" />


</merge>