<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <com.meishe.myvideo.view.MYSeekBarTopTextView
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:maxHeight="@dimen/dp_px_6"
        android:max="100"
        android:progressDrawable="@drawable/edit_seek_bar"
        android:thumb="@drawable/edit_seek_bar_ball"
        android:id="@+id/view_seek_bar"
        android:layout_marginTop="@dimen/dp_px_45"
        android:layout_width="@dimen/dp_px_750"
        android:layout_height="wrap_content" />

    <androidx.recyclerview.widget.RecyclerView
        app:layout_constraintBottom_toTopOf="@+id/line"
        app:layout_constraintTop_toBottomOf="@+id/view_seek_bar"
        android:id="@+id/rv_list"
        android:layout_width="match_parent"
        app:layout_goneMarginTop="@dimen/dp_px_37"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_px_30"
        android:layout_marginBottom="@dimen/dp_px_63"/>

    <View
        app:layout_constraintTop_toTopOf="@+id/rv_list"
        android:layout_marginTop="@dimen/dp_px_15"
        app:layout_constraintLeft_toLeftOf="@+id/rv_list"
        android:layout_marginStart="@dimen/dp_px_179"
        android:layout_width="@dimen/dp_px_3"
        android:layout_height="@dimen/dp_px_30"
        android:background="@color/color_33ffffff"/>

    <View
        android:id="@+id/line"
        app:layout_constraintBottom_toTopOf="@+id/tv_content"
        android:layout_width="match_parent"
        android:layout_marginTop="@dimen/dp_px_24"
        android:layout_height="@dimen/dp_px_1"
        android:background="@color/menu_divide_color" />

    <TextView
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:id="@+id/tv_content"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_225"
        android:gravity="center"
        android:text="@string/sub_menu_name_edit_color_picker"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_px_48"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_42" />

    <TextView
        android:id="@+id/tv_reset"
        app:layout_constraintTop_toTopOf="@+id/tv_content"
        app:layout_constraintBottom_toBottomOf="@+id/tv_content"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp_px_39"
        android:gravity="center"
        android:layout_marginStart="@dimen/dp_px_39"
        android:padding="@dimen/dp_px_18"
        android:textColor="@color/white"
        android:text="@string/tv_reset"/>

    <ImageView
        app:layout_constraintTop_toTopOf="@+id/tv_content"
        app:layout_constraintBottom_toBottomOf="@+id/tv_content"
        app:layout_constraintRight_toRightOf="parent"
        android:id="@+id/iv_confirm"
        android:layout_width="@dimen/dp_px_69"
        android:layout_height="@dimen/dp_px_69"
        android:layout_marginEnd="@dimen/dp_px_39"
        android:background="@mipmap/ic_confirm"
        android:contentDescription="@null"
        android:padding="@dimen/dp_px_18" />
</androidx.constraintlayout.widget.ConstraintLayout>