package com.meishe.engine.bean.template;

import com.meishe.third.adpater.entity.SectionEntity;


/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * <AUTHOR> LiuPanFeng
 * @CreateDate : 2020/12/24 15:59
 * @Description :导出模板部分 The export template section
 * @Copyright : www.meishesdk.com Inc. All rights reserved.
 */
public class ExportTemplateSection extends SectionEntity<ExportTemplateClip> {
    private int trackSectionIndex;
    /**
     * 是否是静音
     * Mute or not
     */
    private boolean mute;

    public ExportTemplateSection(ExportTemplateClip exportTemplateClip) {
        super(exportTemplateClip);
    }

    public int getTrackSectionIndex() {
        return trackSectionIndex;
    }

    public void setTrackSectionIndex(int trackSectionIndex) {
        this.trackSectionIndex = trackSectionIndex;
    }

    public boolean isMute() {
        return mute;
    }

    public void setMute(boolean mute) {
        this.mute = mute;
    }
}
