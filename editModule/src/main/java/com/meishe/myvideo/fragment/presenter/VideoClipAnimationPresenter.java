package com.meishe.myvideo.fragment.presenter;

import android.text.TextUtils;

import com.meishe.base.model.Presenter;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.AnimationData;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.myvideo.fragment.iview.StickerAnimationView;

import static com.meishe.engine.asset.bean.AssetInfo.ASSET_ANIMATION_GROUP;
import static com.meishe.engine.asset.bean.AssetInfo.ASSET_ANIMATION_IN;
import static com.meishe.engine.asset.bean.AssetInfo.ASSET_ANIMATION_OUT;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/10/08 11:40
 * @Description :贴纸动画presenter The video clip animation presenter
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class VideoClipAnimationPresenter extends Presenter<StickerAnimationView> {

    private MeicamVideoClip mVideoClip;


    /**
     * Sets  clip.
     *
     * @param videoClip the  clip
     */
    public void setVideoClip(MeicamVideoClip videoClip) {
        this.mVideoClip = videoClip;
    }

    public MeicamVideoClip getVideoClip() {
        return mVideoClip;
    }

    /**
     * Gets animation.
     *
     * @return the animation
     */
    public AnimationData getVideoClipAnimation() {
        return EditorEngine.getInstance().getVideoClipAnimation(mVideoClip);
    }

    /**
     * Gets max progress.
     *
     * @return the max progress
     */
    public int getMaxProgress() {
        return mVideoClip == null ? 0 : (int) (mVideoClip.getOutPoint() - mVideoClip.getInPoint());
    }

    /**
     * Play.
     *
     * @param type the type
     */
    public void play(int type) {
        AnimationData animationData = getVideoClipAnimation();
        if (type == ASSET_ANIMATION_GROUP) {
            boolean hasCompAnimation = (!TextUtils.isEmpty(animationData.getPackageID())) && (!animationData.getIsAnimationIn());
            if (hasCompAnimation) {
                EditorEngine.getInstance().playVideoLoop(mVideoClip.getInPoint(), mVideoClip.getInPoint() + animationData.getOutPoint() - animationData.getInPoint());
            }

        } else if (type == ASSET_ANIMATION_IN) {
            boolean hasInAnimation = (!TextUtils.isEmpty(animationData.getPackageID())) && animationData.getIsAnimationIn();
            if (hasInAnimation) {
                EditorEngine.getInstance().playVideoRollBack(mVideoClip.getInPoint(), mVideoClip.getInPoint()
                        + animationData.getOutPoint() - animationData.getInPoint());
            }
        } else if (type == ASSET_ANIMATION_OUT) {
            boolean hasOutAnimation = !TextUtils.isEmpty(animationData.getPackageID2());
            if (hasOutAnimation) {
                EditorEngine.getInstance().playVideoRollBack(mVideoClip.getOutPoint() - (animationData.getOutPoint2() - animationData.getInPoint2()), mVideoClip.getOutPoint());
            }
        }
    }

}
