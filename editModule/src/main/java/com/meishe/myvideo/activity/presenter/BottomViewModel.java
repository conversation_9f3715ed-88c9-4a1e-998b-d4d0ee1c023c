package com.meishe.myvideo.activity.presenter;

import android.app.Application;
import android.content.Context;
import android.content.res.AssetManager;
import android.content.res.TypedArray;
import android.graphics.PointF;
import android.text.TextUtils;
import android.util.Pair;

import com.google.gson.reflect.TypeToken;
import com.meishe.base.bean.FloatPoint;
import com.meishe.base.model.BaseModel;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.GsonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ResourceUtils;
import com.meishe.base.utils.Utils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.MaskInfoData;
import com.meishe.engine.bean.MeicamAdjustData;
import com.meishe.engine.bean.MeicamKeyFrame;
import com.meishe.engine.bean.MeicamKeyframeControlPoints;
import com.meishe.engine.bean.MeicamTheme;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamTimelineVideoFilterAndAdjustClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxClip;
import com.meishe.engine.bean.MeicamTrackVideoFx;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoFx;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.bean.CanvasStyleInfo;
import com.meishe.myvideo.bean.EditAdjustInfo;
import com.meishe.myvideo.edit.record.AudioFxInfo;
import com.meishe.myvideo.manager.MenuDataManager;
import com.meishe.myvideo.view.MYEffectTargetMenuView;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;


/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2020/12/29 10:15
 * @Description :底部视图Model the model of bottom view
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class BottomViewModel extends BaseModel {
    private final static String IMAGE_ASSETS_PATH = "background/image";

    /**
     * 根据视频片段获取当前的画布背景
     * Gets the current canvas background from the video clip
     *
     * @param videoClip the video clip
     */
    public String getCanvasColor(MeicamVideoClip videoClip) {
        String color = "";
        if (videoClip != null) {
            MeicamVideoFx videoFx = videoClip.findPropertyVideoFx();
            if (videoFx != null) {
                String stringVal = videoFx.getStringVal(NvsConstants.KEY_BACKGROUND_MODE);
                if (NvsConstants.VALUE_COLOR_BACKGROUND_MODE.equals(stringVal)) {
                    return videoFx.getStringVal(NvsConstants.KEY_BACKGROUND_COLOR);
                }
            }
        }

        return color;
    }


    /**
     * 根据视频片段获取当前的画布样式
     * Gets the current canvas style from the video clip
     *
     * @param videoClip the video clip
     */
    public String getCanvasStyle(MeicamVideoClip videoClip) {
        if (videoClip == null) {
            return null;
        }
        MeicamVideoFx videoFx = videoClip.findPropertyVideoFx();
        if (videoFx == null) {
            return null;
        }
        String stringVal = videoFx.getMenuVal(NvsConstants.KEY_BACKGROUND_MODE);
        if (NvsConstants.VALUE_IMAGE_BACKGROUND_MODE.equals(stringVal)) {
            String imagePath = videoFx.getStringVal(NvsConstants.KEY_BACKGROUND_IMAGE_PATH);
            if (!TextUtils.isEmpty(imagePath)) {
                String fileName = FileUtils.getFileName(imagePath);
                if (!TextUtils.isEmpty(fileName)) {
                    return fileName.split("\\.")[0];
                } else {
                    return "";
                }
            } else {
                return "";
            }
        }
        return null;
      /*  Map<String, MeicamFxParam<?>> map = videoFx.getMeicamFxParam();
        if (map == null) {
            return null;
        }
        MeicamFxParam fxParam = map.get(NvsConstants.KEY_BACKGROUND_MODE);
        if (fxParam == null) {
            return null;
        }
        if (NvsConstants.VALUE_IMAGE_BACKGROUND_MODE.equals(fxParam.getValue())) {
            fxParam = map.get(NvsConstants.KEY_BACKGROUND_IMAGE_PATH);
            if (fxParam != null) {
                path = (String) fxParam.getValue();
            }
        }
        return path;*/
    }

    /**
     * 获取画布样式列表
     * Gets the canvas style list
     */
    public List<IBaseInfo> getCanvasStyleList() {
        AssetManager assets = Utils.getApp().getAssets();
        try {
            String[] list = assets.list(IMAGE_ASSETS_PATH);
            if ((list == null) || (list.length <= 0)) {
                return null;
            }
            List<IBaseInfo> result = new ArrayList<>();
            IBaseInfo baseInfo = new CanvasStyleInfo();
            baseInfo.setCoverPath(ResourceUtils.getMipmapToUri(R.mipmap.ic_canvas_add_resource));
            result.add(baseInfo);
            baseInfo = new BaseInfo();
            baseInfo.setCoverPath(ResourceUtils.getMipmapToUri(R.mipmap.ic_canvas_style_no));
            result.add(baseInfo);
            for (String s : list) {
                baseInfo = new CanvasStyleInfo();
                baseInfo.setCoverPath("file:///android_asset/background/image/" + s);
                baseInfo.setAssetPath(s);
                result.add(baseInfo);
            }
            return result;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 根据视频片段获取当前的画布模糊值
     * Gets the current canvas blur value from the video clip
     *
     * @param videoClip the video clip
     */
    public float getCanvasBlur(MeicamVideoClip videoClip) {
        float strength = -1;
        if (videoClip != null) {
            MeicamVideoFx videoFx = videoClip.findPropertyVideoFx();
            if (videoFx != null) {
                String stringVal = videoFx.getMenuVal(NvsConstants.KEY_BACKGROUND_MODE);
                if (NvsConstants.VALUE_BLUR_BACKGROUND_MODE.equals(stringVal)) {
                    return videoFx.getFloatVal(NvsConstants.KEY_BACKGROUND_BLUR_RADIUS);
                }
                /*Map<String, MeicamFxParam<?>> map = videoFx.getMeicamFxParam();
                if (map != null) {
                    MeicamFxParam fxParam = map.get(NvsConstants.KEY_BACKGROUND_MODE);
                    if (fxParam != null) {
                        if (NvsConstants.VALUE_BLUR_BACKGROUND_MODE.equals(fxParam.getValue())) {
                            fxParam = map.get(NvsConstants.KEY_BACKGROUND_BLUR_RADIUS);
                            if (fxParam != null) {
                                Object valueRadius = fxParam.getValue();
                                if (valueRadius instanceof Float) {
                                    strength = (float) valueRadius;
                                } else {
                                    strength = Float.parseFloat(valueRadius.toString());
                                }
                            }
                        }
                    }
                }*/
            }
        }
        return strength;
    }

    /**
     * 获取蒙版数据列表
     * Get the mask data list
     */
    public List<MaskInfoData> getMaskData() {
        TypedArray editIconArray = Utils.getApp().getResources().obtainTypedArray(R.array.sub_edit_mask_menu_icon);
        String[] nameArray = Utils.getApp().getResources().getStringArray(R.array.sub_edit_mask_menu_name);
        List<MaskInfoData> maskInfoList = new ArrayList<>();

        for (int i = 0; i < nameArray.length; i++) {
            MaskInfoData maskItemInfo = new MaskInfoData();
            maskItemInfo.setCoverId(editIconArray.getResourceId(i, -1));
            maskItemInfo.setName(nameArray[i]);
            //这里设置类型与设置的maskType 是对应关系 所以直接设置i
            //Here, the set type corresponds to the set maskType, so set i directly.
            maskItemInfo.setMaskType(i);
            maskInfoList.add(maskItemInfo);
        }
        editIconArray.recycle();
        return maskInfoList;
    }


    /**
     * 获取音频变声列表
     * Gets a list of audio changes
     */
    public List<AudioFxInfo> getAudioChangeVoiceList() {
        String path;
        if (Utils.isZh()) {
            path = "record/record.json";
        } else {
            path = "record/record_en.json";
        }
        return GsonUtils.fromJson(ResourceUtils.readAssets2String(path, "UTF-8"),
                new TypeToken<List<AudioFxInfo>>() {
                }.getType());
    }


    /**
     * 获取本地资源目录下的字幕字体列表
     * Gets Assets caption font data
     *
     * @param context the context
     * @return the caption font data
     */
    public List<IBaseInfo> getCaptionFontList(Context context) {
        return MenuDataManager.getCaptionFontList(context);
    }

    /**
     * 获取调节数据
     * <p>
     * get adjust data.
     *
     * @param context 上下文 context
     * @return 数据 data
     */
    public List<IBaseInfo> getAdjustData(Context context) {
        return MenuDataManager.getAdjustMenuData(context.getApplicationContext());
    }

    /**
     * 获取混合模式数据
     * <p>
     * get mixed Mode data.
     *
     * @param context 上下文 context
     * @return 数据 data
     */
    public List<IBaseInfo> getMixedModeData(Context context) {
        return MenuDataManager.getMixedModeMenuData(context.getApplicationContext());
    }

    /**
     * 处理调节数据，设置数据的Strength值
     * <p>
     * handle adjust data to set the strength value to items.
     *
     * @param adjustData 调节数据 the adjust data
     * @param videoClip  视频数据 video clip data
     */
    public void handleAdjustData(List<IBaseInfo> adjustData, MeicamVideoClip videoClip, MeicamTimelineVideoFilterAndAdjustClip meicamTimelineVideoFilterAndAdjustClip) {

        if (CommonUtils.isEmpty(adjustData)) {
            return;
        }
        for (IBaseInfo baseInfo : adjustData) {
            String desc = baseInfo.getEffectId();
            Float value = null;
            String fxNameByEffectName = getFxNameByEffectName(desc);
            if (videoClip != null) {
                value = videoClip.getAdjustItemValue(fxNameByEffectName, desc);
                if (value == 0) {
                    value = getDefaultStrengthParam(desc);
                }
            } else {
                if (meicamTimelineVideoFilterAndAdjustClip != null) {
                    MeicamTimelineVideoFxClip clip = meicamTimelineVideoFilterAndAdjustClip.getAdjustTimelineFx(fxNameByEffectName);
                    if (clip != null) {
                        value = clip.getFloatVal(desc, getDefaultStrengthParam(desc));
                    }
                }
            }
            baseInfo.setEffectStrength(getAdjustStrength(value, desc));
        }
    }

    /**
     * 获取调节强度
     * <p>
     * get strength of adjust
     *
     * @param baseInfo  调节菜单数据 menu data of adjust
     * @param videoClip 视频数据 video clip data
     * @return 强度 strength
     */
    public float getAdjustStrength(IBaseInfo baseInfo, MeicamVideoClip videoClip, MeicamTimelineVideoFilterAndAdjustClip meicamTimelineVideoFilterAndAdjustClip) {
        if (baseInfo == null) {
            return 0;
        }
        String desc = baseInfo.getEffectId();
        Float value = null;
        String effectName = getFxNameByEffectName(desc);
        if (videoClip != null) {
            value = videoClip.getAdjustItemValue(effectName, desc);
            if (value == 0) {
                value = getDefaultStrengthParam(desc);
            }
        } else {
            MeicamTimelineVideoFxClip clip = meicamTimelineVideoFilterAndAdjustClip.getAdjustTimelineFx(effectName);
            if (clip != null) {
                value = clip.getFloatVal(desc, getDefaultStrengthParam(desc));
            }
        }
        return getAdjustStrength(value, desc);
    }

    /**
     * 获取调节强度
     * <p>
     * get strength of adjust
     *
     * @param meicamAdjustData 调节特效数据 adjust data
     * @param baseInfo         调节菜单数据 menu data of adjust
     * @return 强度 strength
     */
    private float getAdjustStrength(MeicamAdjustData meicamAdjustData, IBaseInfo baseInfo) {
        if (baseInfo == null) {
            return 0;
        }
        EditAdjustInfo data = (EditAdjustInfo) baseInfo;
        String desc = data.getEffectId();
        float value = 0;
        if (meicamAdjustData == null) {
            if (NvsConstants.ADJUST_BLACKPOINT.equals(desc) || NvsConstants.ADJUST_DEGREE.equals(desc)
                    || NvsConstants.ADJUST_AMOUNT.equals(desc)) {
                return 0;
            } else {
                return 50F;
            }
        }
        if (NvsConstants.ADJUST_AMOUNT.equals(desc)) {
            //锐度 amount
            value = meicamAdjustData.getAmount();
            return value * 100F;
        } else if (NvsConstants.ADJUST_DEGREE.equals(desc)) {
            //暗角 degree
            value = meicamAdjustData.getDegree();
            return value * 100F;
        } else if (NvsConstants.ADJUST_BLACKPOINT.equals(desc)) {
            //褪色 black point
            value = meicamAdjustData.getBlackPoint();
            return value * 100F;
        } else if (NvsConstants.ADJUST_BRIGHTNESS.equals(desc)) {
            //亮度 brightness
            value = meicamAdjustData.getBrightness();
        } else if (NvsConstants.ADJUST_CONTRAST.equals(desc)) {
            //对比度 contrast
            value = meicamAdjustData.getContrast();
        } else if (NvsConstants.ADJUST_SATURATION.equals(desc)) {
            //饱和度 saturation
            value = meicamAdjustData.getSaturation();
        } else if (NvsConstants.ADJUST_HIGHTLIGHT.equals(desc)) {
            //高光 hight light
            value = meicamAdjustData.getHighlight();
        } else if (NvsConstants.ADJUST_SHADOW.equals(desc)) {
            //阴影 shadow
            value = meicamAdjustData.getShadow();
        } else if (NvsConstants.ADJUST_TEMPERATURE.equals(desc)) {
            //色温 temperature
            value = meicamAdjustData.getTemperature();
        } else if (NvsConstants.ADJUST_TINT.equals(desc)) {
            //色调 tint
            value = meicamAdjustData.getTint();
        }
        return (value + 1) * 50F;
    }


    /**
     * 获取调节强度
     * <p>
     * get strength of adjust
     *
     * @param value the float value
     * @param desc  调节特效描述 menu description of adjust effect
     * @return 强度 strength
     */
    private float getAdjustStrength(Float value, String desc) {
        if (TextUtils.isEmpty(desc)) {
            return 0;
        }
        if (value == null) {
            if (NvsConstants.ADJUST_BLACKPOINT.equals(desc) || NvsConstants.ADJUST_DEGREE.equals(desc)
                    || NvsConstants.ADJUST_AMOUNT.equals(desc)) {
                return 0;
            } else {
                return 50F;
            }
        }
        if (NvsConstants.ADJUST_AMOUNT.equals(desc)
                || NvsConstants.ADJUST_DEGREE.equals(desc)) {
            //锐度 amount
            return value * 100F;
        } else if (NvsConstants.ADJUST_BLACKPOINT.equals(desc)) {
            //最小值是0，最大值是-10
            //The min is 0, the max is -10
            return -100F / 10 * (value);
        }
        return (value + 1) * 50F;
    }

    private float getDefaultStrengthParam(String name) {
        if (NvsConstants.FX_SHARPEN.equals(name)) {
            //锐度 0- 1；默认0
            return 0F;
        } else if (NvsConstants.FX_VIGNETTE.equals(name)) {
            //暗角0-1；默认0
            return 0F;
        } else if (NvsConstants.ADJUST_BLACKPOINT.equals(name)) {
            //褪色0- （-10）；默认0
            return 0F;
        } else if (NvsConstants.ADJUST_TINT.equals(name)) {
            //色调-1-1； 默认0
            return 0;
        } else if (NvsConstants.ADJUST_TEMPERATURE.equals(name)) {
            //色温-1-1； 默认0
            return 0F;
        } else if (NvsConstants.ADJUST_HIGHTLIGHT.equals(name)
                || NvsConstants.ADJUST_SATURATION.equals(name)
                || NvsConstants.ADJUST_CONTRAST.equals(name)
                || NvsConstants.ADJUST_SHADOW.equals(name)
                || NvsConstants.ADJUST_BRIGHTNESS.equals(name)) {
            //高光、饱和度、对比度、亮度、阴影 -1-1； 默认0
            return 0;
        }
        return 0;
    }


    private String getFxNameByEffectName(String name) {
        if (TextUtils.isEmpty(name)) {
            return null;
        }
        if (NvsConstants.ADJUST_AMOUNT.equals(name)) {
            return NvsConstants.FX_SHARPEN;
        } else if (NvsConstants.ADJUST_DEGREE.equals(name)) {
            return NvsConstants.FX_VIGNETTE;
        } else if (NvsConstants.ADJUST_BLACKPOINT.equals(name)) {
            return NvsConstants.ADJUST_TYPE_BASIC_IMAGE_ADJUST;
        } else if (NvsConstants.ADJUST_TINT.equals(name)) {
            return NvsConstants.ADJUST_TINT;
        } else if (NvsConstants.ADJUST_TEMPERATURE.equals(name)) {
            return NvsConstants.ADJUST_TINT;
        } else if (NvsConstants.ADJUST_SHADOW.equals(name)
                || NvsConstants.ADJUST_HIGHTLIGHT.equals(name)
                || NvsConstants.ADJUST_SATURATION.equals(name)
                || NvsConstants.ADJUST_CONTRAST.equals(name)
                || NvsConstants.ADJUST_BRIGHTNESS.equals(name)) {
            return NvsConstants.ADJUST_TYPE_BASIC_IMAGE_ADJUST;
        }
        return null;
    }


    /**
     * 获取选择的滤镜的index
     * <p>
     * get selection of filter
     *
     * @param dataList 滤镜数据列表 List of menu data
     * @return 选择滤镜的索引 index
     */
    public int getFilterSelection(List<IBaseInfo> dataList, MeicamVideoClip videoClip) {
        int position = 0;
        if (videoClip == null) {
            return position;
        }
        MeicamVideoFx filterFx = videoClip.getVideoFxByType(MeicamVideoFx.SubType.SUB_TYPE_CLIP_FILTER);
        if (filterFx == null) {
            return position;
        }
        String desc = filterFx.getDesc();
        String type = filterFx.getType();
        boolean isBuildIn = CommonData.TYPE_BUILD_IN.equals(type);
        IBaseInfo baseInfo;
        for (int index = 0; index < dataList.size(); index++) {
            baseInfo = dataList.get(index);
            String uiDesc;
            if (isBuildIn) {
                uiDesc = baseInfo.getEffectId();
            } else {
                uiDesc = baseInfo.getPackageId();
            }
            if (desc != null && desc.equals(uiDesc)) {
                position = index;
                break;
            }
        }
        return position;
    }


    /**
     * 获取滤镜强度
     * <p>
     * get intensity of filter
     *
     * @param videoClip 视频数据 video clip data
     * @return 强度 intensity
     */
    public float getFilterIntensity(MeicamVideoClip videoClip, MeicamTimelineVideoFilterAndAdjustClip meicamTimelineVideoFilterAndAdjustClip) {
        if (videoClip == null) {
            if (meicamTimelineVideoFilterAndAdjustClip != null) {
                MeicamTimelineVideoFxClip clip = meicamTimelineVideoFilterAndAdjustClip.getAdjustTimelineFx(MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FILTER);
                if (clip != null) {
                    return clip.getIntensity();
                }
            }
        } else {
            MeicamVideoFx videoFx = videoClip.getVideoFxByType(MeicamVideoFx.SubType.SUB_TYPE_CLIP_FILTER);
            if (videoFx != null) {
                return videoFx.getIntensity();
            }
        }
        return 0;
    }

    /**
     * 获取曲线变速数据
     * <p>
     * get speed curve data.
     *
     * @param context 上下文 context
     * @return 数据 data
     */
    public List<IBaseInfo> getSpeedCurveData(Context context) {
        return MenuDataManager.getChangeSpeedCurve(context.getApplicationContext());
    }

    /**
     * 获取曲线变速选中的index位置
     *
     * @return index
     */
    public int getCurveSpeedSelectPosition(List<IBaseInfo> uiItems, MeicamVideoClip videoClip) {
        if (videoClip == null || TextUtils.isEmpty(videoClip.getCurveSpeedName())) {
            return 0;
        }
        int position = 0;
        IBaseInfo baseInfo;
        for (int index = 0; index < uiItems.size(); index++) {
            baseInfo = uiItems.get(index);
            if (videoClip.getCurveSpeedName().equals(baseInfo.getName())) {
                position = index;
                break;
            }
        }
        return position;
    }


    /**
     * 获取选择的主题的index
     * <p>
     * get selection of theme
     *
     * @param data 主题数据列表 List of menu data
     * @return 选择主题的索引 index
     */
    public int getThemeSelection(List<IBaseInfo> data) {
        int position = 1;
        MeicamTheme theme = EditorEngine.getInstance().getCurrentTimeline().getMeicamTheme();
        if (theme == null) {
            return position;
        }
        String themePackageId = theme.getThemePackageId();
        if (TextUtils.isEmpty(themePackageId)) {
            return position;
        }
        IBaseInfo baseInfo;
        for (int index = 0; index < data.size(); index++) {
            baseInfo = data.get(index);
            if (themePackageId.equals(baseInfo.getPackageId())) {
                position = index;
                break;
            }
        }
        return position;
    }

    /**
     * 获取当前选中贝塞尔曲线调节类别的索引
     * Gets the index of the currently selected Bezier curve adjustment type
     *
     * @return the key frame curve index
     */
    public int getKeyFrameCurveIndex(Pair<MeicamKeyFrame, MeicamKeyFrame> paraFrame) {
        if (paraFrame == null || paraFrame.first == null || paraFrame.second == null) {
            return 1;
        }
        MeicamKeyFrame beforeKeyFrameInfo = paraFrame.first;
        MeicamKeyFrame backKeyFrameInfo = paraFrame.second;
        MeicamKeyframeControlPoints beforeControlPoints = beforeKeyFrameInfo.getControlPoints();
        MeicamKeyframeControlPoints backControlPoints = backKeyFrameInfo.getControlPoints();
        if (beforeControlPoints == null || backControlPoints == null
                || backControlPoints.getBackPointId() != beforeControlPoints.getFontPointId()
                || backControlPoints.getBackwardControlPoint() == null
                || beforeControlPoints.getForwardControlPoint() == null) {
            return 1;
        }
        return beforeControlPoints.getFontPointId();
    }


    /**
     * Gets key frame control points.
     * 获取关键帧控制点
     *
     * @param pairFrame 关键帧对 the pair frame
     * @return 关键帧控制点 the key frame control points
     */
    public Pair<PointF, PointF> getKeyFrameControlPoints(Pair<MeicamKeyFrame, MeicamKeyFrame> pairFrame) {
        if (pairFrame == null || pairFrame.first == null || pairFrame.second == null) {
            return null;
        }
        MeicamKeyFrame beforeKeyFrameInfo = pairFrame.first;
        MeicamKeyframeControlPoints beforeControlPoints = beforeKeyFrameInfo.getControlPoints();
        if (beforeControlPoints == null || beforeControlPoints.isInvalid()) {
            return null;
        }
        MeicamKeyFrame afterKeyFrameInfo = pairFrame.second;
        MeicamKeyframeControlPoints afterControlPoints = afterKeyFrameInfo.getControlPoints();
        if (afterControlPoints == null || afterControlPoints.isInvalid()) {
            return null;
        }
        FloatPoint forwardControlPoint = beforeControlPoints.getForwardControlPoint();
        FloatPoint backwardControlPoint = afterControlPoints.getBackwardControlPoint();
        return new Pair<>(new PointF(forwardControlPoint.x, forwardControlPoint.y), new PointF(backwardControlPoint.x, backwardControlPoint.y));
    }

    public List<MYEffectTargetMenuView.TargetInfo> getEffectTargetInfo(MeicamTimelineVideoFxClip timelineVideoFxClip, boolean isCurrentTimeInRange) {
        List<MYEffectTargetMenuView.TargetInfo> result = new ArrayList<>();
        Application context = Utils.getApp();
        MYEffectTargetMenuView.TargetInfo firstItem = new MYEffectTargetMenuView.TargetInfo(null, context.getString(R.string.menu_effect_target_all), -1, -1);
        firstItem.coverResId = R.mipmap.sub_menu_item_icon_edit_target;
        result.add(firstItem);

        if (timelineVideoFxClip == null) {
            LogUtils.d("param is null !");
            return result;
        }
        MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
        if (timeline == null) {
            LogUtils.d("timeline is null !");
            return result;
        }


        //fxClip的inPoint和outPoint范围 必须和videoClip的出入点重合
        //对于主轨，选择第一个重合的videoClip
        //对于画中画轨，
        // 1、如果seek时间点在fxClip的inPoint和outPoint范围内，选择覆盖seek时间点的videoClip，没有则无
        // 2、如果seek时间点在fxClip的inPoint和outPoint范围外，选择离seek时间点最近的videoClip
        //The inPoint and outPoint ranges of fxClip must coincide with the entry and exit points of videoClip
        // For the main track, select the first overlapping videoClip
        // For picture in picture tracks,
        // 1. If the seek time point is within the inPoint and outPoint range of fxClip, select a videoClip that covers the seek time point. If not, then none
        // 2. If the seek time point is outside the inPoint and outPoint range of fxClip, select the videoClip closest to the seek time point
        long inPoint = timelineVideoFxClip.getInPoint();
        long outPoint = timelineVideoFxClip.getOutPoint();
        long currentPosition = timeline.getCurrentPosition();
        int trackCount = timeline.videoTrackCount();
        String mainName = context.getString(R.string.menu_effect_target_main_clip);
        if (trackCount >= 1) {
            //主轨只取第一个clip
            //The main track only takes the first clip.
            MeicamVideoTrack mainTrack = timeline.getVideoTrack(0);
            int clipCount = mainTrack.getClipCount();
            for (int clipIndex = 0; clipIndex < clipCount; clipIndex++) {
                MeicamVideoClip videoClip = mainTrack.getVideoClip(clipIndex);
                if (videoClip == null) {
                    continue;
                }
                long clipInPoint = videoClip.getInPoint();
                long clipOutPoint = videoClip.getOutPoint();
                boolean isInTheRange = isCrossTheRange(clipInPoint, clipOutPoint, inPoint, outPoint);
                if (isInTheRange) {
                    result.add(new MYEffectTargetMenuView.TargetInfo(videoClip.getFilePath(), mainName,
                            0, videoClip.getIndex()));
                    break;
                }
            }

            String pipName = context.getString(R.string.menu_effect_target_pip_clip);
            List<MeicamVideoClip> tempList = new ArrayList<>();
            for (int trackIndex = 1; trackIndex < trackCount; trackIndex++) {
                tempList.clear();
                MeicamVideoTrack videoTrack = timeline.getVideoTrack(trackIndex);
                if (videoTrack == null) {
                    continue;
                }
                clipCount = videoTrack.getClipCount();
                if (clipCount > 0) {
                    for (int clipIndex = 0; clipIndex < clipCount; clipIndex++) {
                        MeicamVideoClip videoClip = videoTrack.getVideoClip(clipIndex);
                        if (videoClip == null) {
                            continue;
                        }
                        long clipInPoint = videoClip.getInPoint();
                        long clipOutPoint = videoClip.getOutPoint();
                        boolean isInTheRange = isCrossTheRange(clipInPoint, clipOutPoint, inPoint, outPoint);
                        if (isInTheRange) {
                            if (isCurrentTimeInRange) {
                                // 查找成功 Find it and break
                                if (currentPosition >= clipInPoint && currentPosition < clipOutPoint) {
                                    result.add(new MYEffectTargetMenuView.TargetInfo(videoClip.getFilePath(), pipName,
                                            trackIndex, videoClip.getIndex()));
                                    break;
                                }
                            } else {
                                tempList.add(videoClip);
                            }
                        }
                    }
                }
                if (!tempList.isEmpty()) {
                    MeicamVideoClip videoClip;
                    if (currentPosition < inPoint) {
                        videoClip = tempList.get(0);
                    } else {
                        videoClip = tempList.get(tempList.size() - 1);
                    }
                    result.add(new MYEffectTargetMenuView.TargetInfo(videoClip.getFilePath(), pipName,
                            trackIndex, videoClip.getIndex()));
                }
            }
        }
        return result;
    }

    private boolean isCrossTheRange(long inPoint, long outPoint, long diffInPoint, long diffOutPoint){
        return (diffInPoint >= inPoint && diffInPoint < outPoint) || (diffOutPoint >= inPoint && diffOutPoint < outPoint) || (diffInPoint <= inPoint && diffOutPoint >= outPoint);
    }

    public int getEffectTargetSelection(List<MYEffectTargetMenuView.TargetInfo> effectTargetInfo, MeicamTimelineVideoFxClip timelineVideoFxClip) {
        if (CommonUtils.isEmpty(effectTargetInfo) || timelineVideoFxClip == null) {
            LogUtils.e("Param are not invalid !");
            return -1;
        }
        String createTag = timelineVideoFxClip.getExtraTag();
        if (TextUtils.isEmpty(createTag)) {
            LogUtils.e("CreateTag is null!");
            return -1;
        }
        MeicamTimeline currentTimeline = EditorEngine.getInstance().getCurrentTimeline();
        if (currentTimeline == null) {
            LogUtils.e("currentTimeline is null!");
            return -1;
        }
        for (int index = 1; index < effectTargetInfo.size(); index++) {
            MYEffectTargetMenuView.TargetInfo targetInfo = effectTargetInfo.get(index);
            MeicamVideoTrack videoTrack = currentTimeline.getVideoTrack(targetInfo.trackIndex);
            if (videoTrack != null) {
                if (targetInfo.trackIndex == 0) {
                    int videoFxCount = videoTrack.getVideoFxCount();
                    for (int fxIndex = 0; fxIndex < videoFxCount; fxIndex++) {
                        MeicamTrackVideoFx videoFx = videoTrack.getVideoFx(fxIndex);
                        if (videoFx.hasTag(createTag)) {
                            return index;
                        }
                    }
                } else {
                    MeicamVideoClip videoClip = videoTrack.getVideoClip(targetInfo.clipIndex);
                    int videoFxCount = videoClip.getVideoFxCount();
                    for (int fxIndex = 0; fxIndex < videoFxCount; fxIndex++) {
                        MeicamVideoFx videoFx = videoClip.getVideoFx(fxIndex);
                        if (videoFx.hasTag(createTag)) {
                            return index;
                        }
                    }
                }
            }
        }
        return 0;
    }
}
