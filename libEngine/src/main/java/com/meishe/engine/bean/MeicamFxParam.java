package com.meishe.engine.bean;

import android.annotation.SuppressLint;

import com.meicam.sdk.NvsObject;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.local.LMeicamFxParam;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/7/3 21:46
 */
public class MeicamFxParam<T> implements Cloneable, Serializable, TimelineDataParserAdapter<LMeicamFxParam<?>> {
    private final static String TAG = "MeicamFxParam";
    public final static String TYPE_STRING = "string";
    public final static String TYPE_STRING_OLD = "String";
    public final static String TYPE_BOOLEAN = "boolean";
    public final static String TYPE_FLOAT = "float";
    public final static String TYPE_INT = "int";
    public final static String TYPE_OBJECT = "Object";
    public final static String TYPE_MENU = "menu";
    public final static String TYPE_COLOR = "color";
    public final static String TYPE_POSITION_2D = "Position2D";
    /**
     * The parameter type
     * 参数类型
     */
    String type;

    /**
     * The parameter key
     * 参数key值
     */
    String key;
    /**
     * The parameter value
     * 参数值
     */
    T value;

    @SuppressLint("NewApi")
    public MeicamFxParam(String type, String key, T value) {
        this.key = key;
        setValue(value);
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public T getValue() {
        return value;
    }

    public void setValue(T value) {
        if (value instanceof Float && Float.isNaN((Float) value)) {
            return;
        }
        if (value instanceof Double && Double.isNaN((Double) value)) {
            return;
        }
        this.value = value;
    }

    public float getFloatValue() {
        if (value instanceof Double) {
            return ((Double) value).floatValue();
        }
        if (value instanceof Float) {
            return (Float) value;
        }
        return Float.MAX_VALUE;
    }

    @Override
    public boolean equals(Object obj) {
        return super.equals(obj);
    }

    public MeicamFxParam<T> copy() {
        return new MeicamFxParam<>(getType(), getKey(), getValue());
    }

    @Override
    public LMeicamFxParam<?> parseToLocalData() {
        return new LMeicamFxParam<>(getType(), getKey(), getValue());
    }

    @Override
    public void recoverFromLocalData(LMeicamFxParam<?> lMeicamFxParam) {

    }

    @Override
    public void recoverFromTimelineData(NvsObject nvsObject) {

    }
}
