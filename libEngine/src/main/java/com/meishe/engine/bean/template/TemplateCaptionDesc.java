package com.meishe.engine.bean.template;

import android.graphics.Bitmap;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsAssetPackageManager;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/11/18 17:45
 * 模板标题Desc类
 * Template title Desc class
 */
public class TemplateCaptionDesc implements Comparable<TemplateCaptionDesc> {
    /**
     * 字幕类型
     * The type of common caption
     */
    public static final int TYPE_CAPTION = 1;
    /**
     * 复合字幕类型
     * The type of compound caption
     */
    public static final int TYPE_COMPOUND_CAPTION = 2;

    private TemplateCaptionDesc() {
    }

    private Bitmap mBitmap;
    private long inPoint;
    public String replaceId;
    public String text;
    public String originalText;
    public List<TemplateCaptionDesc> subCaptions = new ArrayList<>();
    public int type = TYPE_CAPTION;
    public int clipIndex = -1;
    public int trackIndex = -1;
    /**
     * 编组ID
     * The group id
     */
    private String groupID;
    private int groupIndex;
    public List<TemplateCompoundCaptionItemDesc> itemList;
    public List<TemplateCompoundCaptionItemDesc> originalItemList;
    private List<Integer> clipTrackIndexInTimelineList = new ArrayList<>();
    private List<Integer> clipIndexInTimelineList = new ArrayList<>();

    public int getGroupIndex() {
        return groupIndex;
    }

    public void setGroupIndex(int groupIndex) {
        this.groupIndex = groupIndex;
    }

    public String getGroupID() {
        return groupID;
    }

    public void setGroupID(String groupID) {
        this.groupID = groupID;
    }

    public void addClipTrackIndexInTimeline(int trackIndex) {
        clipTrackIndexInTimelineList.add(trackIndex);
    }

    public void addClipIndexInTimeline(int clipIndex) {
        clipIndexInTimelineList.add(clipIndex);
    }

    public List<Integer> getClipTrackIndexInTimelineList() {
        return clipTrackIndexInTimelineList;
    }

    public void setClipTrackIndexInTimelineList(List<Integer> clipTrackIndexInTimelineList) {
        this.clipTrackIndexInTimelineList.addAll(clipTrackIndexInTimelineList);
    }

    public List<Integer> getClipIndexInTimelineList() {
        return clipIndexInTimelineList;
    }

    public void setClipIndexInTimelineList(List<Integer> clipIndexInTimelineList) {
        this.clipIndexInTimelineList.addAll(clipIndexInTimelineList);
    }

    /**
     * 由复合字幕描述信息映射过来的创建方法
     * The creator from the NvsTemplateCompoundCaptionDesc
     *
     * @param nvsTemplateCaptionDesc 复合字幕描述数据
     * @return the TemplateCaptionDesc
     */
    public static TemplateCaptionDesc create(NvsAssetPackageManager.NvsTemplateCompoundCaptionDesc nvsTemplateCaptionDesc) {
        TemplateCaptionDesc templateCaptionDesc = new TemplateCaptionDesc();
        templateCaptionDesc.type = TemplateCaptionDesc.TYPE_COMPOUND_CAPTION;
        if (!CommonUtils.isEmpty(nvsTemplateCaptionDesc.itemList)) {
            List<TemplateCompoundCaptionItemDesc> itemDescs = new ArrayList<>();
            for (NvsAssetPackageManager.NvsTemplateCompoundCaptionItemDesc nvsTemplateCompoundCaptionItemDesc
                    : nvsTemplateCaptionDesc.itemList) {
                itemDescs.add(TemplateCompoundCaptionItemDesc.create(nvsTemplateCompoundCaptionItemDesc));
            }
            templateCaptionDesc.itemList = itemDescs;
            ArrayList<TemplateCompoundCaptionItemDesc> cloneData = new ArrayList<>();
            for (TemplateCompoundCaptionItemDesc itemDesc : itemDescs) {
                try {
                    cloneData.add(itemDesc.clone());
                    templateCaptionDesc.text = itemDesc.text;
                } catch (CloneNotSupportedException e) {
                    LogUtils.e(e);
                }
            }
            templateCaptionDesc.originalItemList = cloneData;
        }
        templateCaptionDesc.replaceId = nvsTemplateCaptionDesc.replaceId;
        templateCaptionDesc.trackIndex = nvsTemplateCaptionDesc.trackIndex;
        templateCaptionDesc.clipIndex = nvsTemplateCaptionDesc.clipIndex;
        for (NvsAssetPackageManager.NvsTemplateCompoundCaptionDesc nvsTemplateCompoundCaptionDesc
                : nvsTemplateCaptionDesc.subCaptions) {
            templateCaptionDesc.subCaptions.add(TemplateCaptionDesc.create(nvsTemplateCompoundCaptionDesc));
        }

        return templateCaptionDesc;
    }

    /**
     * 由普通字幕描述信息映射过来的创建方法
     * The creator from the NvsTemplateCaptionDesc
     *
     * @param nvsTemplateCaptionDesc 普通字幕描述数据
     * @return the TemplateCaptionDesc
     */
    public static TemplateCaptionDesc create(NvsAssetPackageManager.NvsTemplateCaptionDesc nvsTemplateCaptionDesc) {
        TemplateCaptionDesc templateCaptionDesc = new TemplateCaptionDesc();
        templateCaptionDesc.type = TYPE_CAPTION;
        templateCaptionDesc.text = nvsTemplateCaptionDesc.text;
        templateCaptionDesc.originalText = nvsTemplateCaptionDesc.text;
        templateCaptionDesc.replaceId = nvsTemplateCaptionDesc.replaceId;
        templateCaptionDesc.clipIndex = nvsTemplateCaptionDesc.clipIndex;
        templateCaptionDesc.trackIndex = nvsTemplateCaptionDesc.trackIndex;
        for (NvsAssetPackageManager.NvsTemplateCaptionDesc nvsTemplateCaption : nvsTemplateCaptionDesc.subCaptions) {
            templateCaptionDesc.subCaptions.add(TemplateCaptionDesc.create(nvsTemplateCaption));
        }
        return templateCaptionDesc;
    }

    /**
     * Is caption boolean.
     * 是否是普通字幕
     *
     * @return the boolean
     */
    public boolean isCaption() {
        return type == TYPE_CAPTION;
    }

    /**
     * Is compound caption boolean.
     * 是否是组合字幕
     *
     * @return the boolean
     */
    public boolean isCompoundCaption() {
        return type == TYPE_COMPOUND_CAPTION;
    }

    public long getInPoint() {
        return inPoint;
    }

    public void setInPoint(long inPoint) {
        this.inPoint = inPoint;
    }

    public Bitmap getBitmap() {
        return mBitmap;
    }

    public void setBitmap(Bitmap bitmap) {
        mBitmap = bitmap;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getText() {
        return text;
    }

    public String getOriginalText() {
        return originalText;
    }

    public void setOriginalText(String originalText) {
        this.originalText = originalText;
    }

    public void setText(int index, String text) {
        if (CommonUtils.isIndexAvailable(index, itemList)) {
            itemList.get(index).text = text;
        }
    }

    public String getText(int index) {
        if (CommonUtils.isIndexAvailable(index, itemList)) {
            return itemList.get(index).text;
        }
        return "";
    }

    public String getOriginalText(int index) {
        if (CommonUtils.isIndexAvailable(index, originalItemList)) {
            return originalItemList.get(index).text;
        }
        return "";
    }

    @Override
    public int compareTo(TemplateCaptionDesc o) {
        /*
         * 先按照创建时间排序 time sort first
         */
        if (o == null) {
            return 0;
        }
        return (int) (this.getInPoint() - o.getInPoint());
    }

    /**
     * 复合字幕子字幕类
     * The template compound caption item desc
     */
    public static class TemplateCompoundCaptionItemDesc implements Cloneable {
        public int index;
        public String text;


        public TemplateCompoundCaptionItemDesc(int index, String text) {
            this.index = index;
            this.text = text;
        }

        @NonNull
        @Override
        protected TemplateCompoundCaptionItemDesc clone() throws CloneNotSupportedException {
            return (TemplateCompoundCaptionItemDesc) super.clone();
        }

        public static TemplateCompoundCaptionItemDesc create(NvsAssetPackageManager.NvsTemplateCompoundCaptionItemDesc nvsTemplateCompoundCaptionItemDesc) {
            return new TemplateCompoundCaptionItemDesc(nvsTemplateCompoundCaptionItemDesc.index, nvsTemplateCompoundCaptionItemDesc.text);
        }
    }
}
