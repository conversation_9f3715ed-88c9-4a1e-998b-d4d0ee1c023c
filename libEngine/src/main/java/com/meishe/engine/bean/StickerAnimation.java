package com.meishe.engine.bean;

import androidx.annotation.NonNull;

import java.io.Serializable;
import java.util.Objects;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/10/8 15:42
 * @Description :贴纸动画 The sticker animation
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class StickerAnimation implements Serializable, Cloneable {
    public static final String TYPE_ANIMATION_IN = "in";
    public static final String TYPE_ANIMATION_OUT = "out";
    public static final String TYPE_ANIMATION_COMP = "comp";
    private String packageId;
    private String type;
    private long duration;

    public StickerAnimation(String packageId, String type, long duration) {
        this.packageId = packageId;
        this.type = type;
        this.duration = duration;
    }

    public String getPackageId() {
        return packageId;
    }

    public void setPackageId(String packageId) {
        this.packageId = packageId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public long getDuration() {
        return duration;
    }

    public void setDuration(long duration) {
        this.duration = duration;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o){
            return true;
        }
        if (!(o instanceof StickerAnimation)) {
            return false;
        }
        StickerAnimation that = (StickerAnimation) o;
        return packageId.equals(that.packageId) &&
                type.equals(that.type);
    }

    @Override
    public int hashCode() {
        return Objects.hash(packageId, type);
    }

    public void setData(String packageId, String type, long duration) {
        setDuration(duration);
        setPackageId(packageId);
        setType(type);
    }

    @NonNull
    @Override
    public StickerAnimation clone() throws CloneNotSupportedException {
        return (StickerAnimation) super.clone();
    }
}
