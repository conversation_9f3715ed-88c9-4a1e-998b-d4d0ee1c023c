package com.meishe.myvideo.downLoad;

import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

import androidx.annotation.NonNull;

import com.meishe.base.utils.ImageLoader;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.StringUtils;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.myvideo.R;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;

import static com.meishe.engine.asset.bean.AssetInfo.ASSET_ANIMATED_STICKER;
import static com.meishe.engine.bean.BaseInfo.AspectRatio_16v9;
import static com.meishe.engine.bean.BaseInfo.AspectRatio_1v1;
import static com.meishe.engine.bean.BaseInfo.AspectRatio_3v4;
import static com.meishe.engine.bean.BaseInfo.AspectRatio_4v3;
import static com.meishe.engine.bean.BaseInfo.AspectRatio_9v16;
import static com.meishe.engine.bean.BaseInfo.AspectRatio_All;


/**
 * 资源下载列表适配器
 * Resource download list adapter
 */
public class AssetDownloadListAdapter extends BaseQuickAdapter<AssetInfo, BaseViewHolder> {
    private final int[] RatioArray = {
            AspectRatio_16v9,
            AspectRatio_1v1,
            AspectRatio_9v16,
            AspectRatio_3v4,
            AspectRatio_4v3,
            AspectRatio_All
    };

    private final String[] RatioStringArray = {
            "16:9",
            "1:1",
            "9:16",
            "3:4",
            "4:3",
            StringUtils.getString(R.string.general)
    };
    private final int mSupportedRatio;
    private final ImageLoader.Options mOptions;


    AssetDownloadListAdapter(int supportedRatio) {
        super(R.layout.item_asset_download);
        mSupportedRatio = supportedRatio;
        mOptions = new ImageLoader.Options()
                .circleCrop()
                .placeholder(R.mipmap.bank_thumbnail_local);
    }

    @NonNull
    @Override
    public BaseViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        BaseViewHolder baseViewHolder = super.onCreateViewHolder(parent, viewType);
        baseViewHolder.addOnClickListener(R.id.bt_download);
        return baseViewHolder;
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, AssetInfo item) {
        helper.setText(R.id.tv_name, item.getName());
        if (item.getType() == ASSET_ANIMATED_STICKER) {
            helper.setText(R.id.tv_ratio, mContext.getString(R.string.download_asset_ratio) + mContext.getString(R.string.asset_ratio));
        } else {
            helper.setText(R.id.tv_ratio, getAssetRatio(item.getSupportedAspectRatio()));
        }
        helper.setText(R.id.tv_size, getAssetSize(item.getAssetSize()));
        ImageLoader.loadUrl(mContext, item.getCoverPath(), helper.getView(R.id.iv_cover), mOptions);
        DownloadProgressBar pbDownload = helper.getView(R.id.pb_download);
        Button btDownload = helper.getView(R.id.bt_download);
        LogUtils.d("version=" + item.getVersion() + ",oldVersion=" + item.getLocalVersion());
        if (item.isHadDownloaded()) {
            //已下载 Downloaded
            if (item.getVersion() > item.getLocalVersion()) {
                //更新 Update
                btDownload.setBackgroundResource(R.drawable.download_button_shape_corner_update);
                btDownload.setText(R.string.asset_update);
                btDownload.setTextColor(mContext.getResources().getColor(R.color.color_ffffffff));
            } else {
                //不需要更新 No update required
                btDownload.setBackgroundResource(R.drawable.download_button_shape_corner_finished);
                btDownload.setText(R.string.asset_downloadfinished);
                btDownload.setTextColor(mContext.getResources().getColor(R.color.color_ff909293));
            }
            if (btDownload.getVisibility() != View.VISIBLE) {
                btDownload.setVisibility(View.VISIBLE);
                pbDownload.setVisibility(View.GONE);
                pbDownload.setProgress(0);
            }
        } else {
            //未下载 Not downloaded
            if (mSupportedRatio >= AspectRatio_16v9
                    && item.getType() != ASSET_ANIMATED_STICKER
                    && (mSupportedRatio & item.getSupportedAspectRatio()) == 0) {
                //不支持该比例 The proportion is not supported
                btDownload.setBackgroundResource(R.drawable.download_button_shape_corner_finished);
                btDownload.setText(R.string.asset_mismatch);
                btDownload.setTextColor(mContext.getResources().getColor(R.color.color_ff928c8c));
            } else {
                //支持该比例 Support this ratio
                if (item.getDownloadProgress() == AssetInfo.DOWNLOAD_FAILED_PROGRESS) {
                    //下载失败 Download failed
                    btDownload.setBackgroundResource(R.drawable.download_button_shape_corner_retry);
                    btDownload.setText(R.string.retry);
                    btDownload.setTextColor(mContext.getResources().getColor(R.color.color_ffffffff));
                    pbDownload.setVisibility(View.GONE);
                    btDownload.setVisibility(View.VISIBLE);
                } else {
                    btDownload.setBackgroundResource(R.drawable.download_button_shape_corner_download);
                    btDownload.setText(R.string.asset_download);
                    btDownload.setTextColor(mContext.getResources().getColor(R.color.color_ffffffff));
                }
            }
            if (item.getDownloadProgress() >= 0 && item.getDownloadProgress() <= 100) {
                if (pbDownload.getVisibility() != View.VISIBLE) {
                    pbDownload.setVisibility(View.VISIBLE);
                    btDownload.setVisibility(View.GONE);
                }
                pbDownload.setProgress(item.getDownloadProgress());
            }
        }
    }

    private String getAssetRatio(int aspectRatio) {
        StringBuilder assetStrRatio = new StringBuilder();
        int length = RatioArray.length;
        for (int index = 0; index < length; ++index) {
            if ((aspectRatio & RatioArray[index]) != 0) {
                if (index == length - 1) {
                    /*
                     * 通用类型
                     * General type
                     * */
                    if (aspectRatio >= RatioArray[index]) {
                        assetStrRatio = new StringBuilder(mContext.getResources().getString(R.string.asset_ratio));
                    }
                } else {
                    /*
                     * 满足几种Ratio的素材
                     * Meets several Ratio materials
                     * */
                    assetStrRatio.append(RatioStringArray[index]);
                    assetStrRatio.append(" ");
                }
            }
        }
        assetStrRatio.insert(0, mContext.getString(R.string.download_asset_ratio));
        return assetStrRatio.toString();
    }

    private String getAssetSize(int assetSize) {
        int totalKbSize = assetSize / 1024;
        int mbSize = totalKbSize / 1024;
        int kbSize = totalKbSize % 1024;
        float tempSize = (float) (kbSize / 1024.0);
        String packageAssetSize;
        if (mbSize > 0) {
            tempSize = (mbSize + tempSize);
            packageAssetSize = String.format(mContext.getString(R.string.package_size_m), tempSize);
            packageAssetSize = packageAssetSize + "M";
        } else {
            packageAssetSize = String.format(mContext.getString(R.string.package_size_kb), kbSize);
            packageAssetSize = packageAssetSize + "K";
        }
        packageAssetSize = mContext.getString(R.string.download_asset_size) + packageAssetSize;
        return packageAssetSize;
    }
}
