<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_media_list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/tv_next"
        android:layout_marginBottom="@dimen/dp_px_20"/>

    <!-- 下一步按钮 -->
    <TextView
        android:id="@+id/tv_next"
        android:layout_width="@dimen/dp_px_168"
        android:layout_height="@dimen/dp_px_90"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="@dimen/dp_px_30"
        android:layout_marginRight="@dimen/dp_px_30"
        android:layout_marginBottom="@dimen/dp_px_30"
        android:background="@drawable/bg_rectangle_round_gray4b4b4b"
        android:gravity="center"
        android:text="@string/next"
        android:textColor="@color/color_ffa4a4a4"
        android:textSize="@dimen/sp_px_33"
        android:visibility="gone" />

</RelativeLayout>