package com.meishe.engine.command;

import java.io.Serializable;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/8/12 15:44
 * @Description :命令接口 The command interface
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface Command extends Serializable {
    /**
     * Do it.
     * 执行
     */
    void doIt();

    /**
     * Undo.
     * 取消执行
     */
    void undo();

    /**
     * Sets index.
     * 设置index,用来排序
     * @param index the index
     */
    void setIndex(int index);

    /**
     * Gets index.
     * 获取index
     * @return the index
     */
    int getIndex();
}

