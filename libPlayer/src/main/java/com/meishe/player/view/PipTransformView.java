package com.meishe.player.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PointF;
import android.graphics.RectF;
import android.graphics.Region;
import android.os.Vibrator;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.Nullable;

import com.meishe.player.view.bean.PipTransformInfo;

import java.util.ArrayList;
import java.util.List;

public class PipTransformView extends View {

    private final static String TAG = "PipTransformView";

    private final static int ONE_FINGER = 1;
    private final static int TWO_FINGER = 2;

    private boolean mIsTwoFingerEvent = false;
    private OnPipTouchEventListener mOnPipTouchEventListener;
    private double mTwoFingerStartLength;
    private PointF mLastPointF = new PointF(0, 0);
    private PointF mTwoFingerOldPoint = new PointF();
    private PipTransformInfo mBoxInfo;
    private Paint mRectPaint = new Paint();
    private boolean mIsVisible = true;

    private boolean mIsInsideBox = false;
    /**
     * 震动器
     */
    private Vibrator mVibrator;

    public PipTransformView(Context context) {
        this(context, null);
    }

    public PipTransformView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public PipTransformView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mVibrator = (Vibrator) context.getSystemService(Context.VIBRATOR_SERVICE);
        initRectPaint();
    }

    public void setOnPipTouchListener(OnPipTouchEventListener listener) {
        mOnPipTouchEventListener = listener;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (!mIsVisible) {
            return;
        }
        if (mBoxInfo == null || mBoxInfo.getCornerPointList() == null || mBoxInfo.getCornerPointList().size() != 4) {
            return;
        }
        canvas.drawPath(getRectPath(mBoxInfo.getCornerPointList()), mRectPaint);
    }

    private void initRectPaint() {
        // 设置颜色 Set color
        mRectPaint.setColor(Color.parseColor("#4A90E2"));
        // 设置抗锯齿  Set antialias
        mRectPaint.setAntiAlias(true);
        // 设置线宽 Set stroke width
        mRectPaint.setStrokeWidth(4);
        // 设置非填充 Set style
        mRectPaint.setStyle(Paint.Style.STROKE);
    }

    /**
     * 获取边框路径
     * Gets the path of border
     */
    private Path getRectPath(List<PointF> listPointF) {
        Path path = new Path();
        path.moveTo(listPointF.get(0).x, listPointF.get(0).y);
        path.lineTo(listPointF.get(1).x, listPointF.get(1).y);
        path.lineTo(listPointF.get(2).x, listPointF.get(2).y);
        path.lineTo(listPointF.get(3).x, listPointF.get(3).y);
        path.close();
        return path;
    }

    /**
     * 更改操作框的可见性,注意是操作框，不是整个视图
     * Change the visibility of the operation box
     */
    public void changeBoxVisibility(boolean visible) {
        if (mIsVisible == visible) {
            return;
        }
        if (!visible) {
            if (mBoxInfo != null) {
                //隐藏的时候就把数据清除，不然显示的时候可能还是上次绘制的位置，会有位置突变
                // Clear the data when hiding, otherwise the display may still be at the last drawn position and there may be sudden changes in position.
                mBoxInfo.setCornerPointList(new ArrayList<PointF>());
            }
        }
        mIsVisible = visible;
        invalidate();
    }

    public boolean isVisible() {
        return mIsVisible;
    }

    /**
     * 更新操作框
     * update the operation box
     *
     * @param info the operation box info
     */
    public void update(PipTransformInfo info) {
        mBoxInfo = info;
        notifyBoxChanged();
    }

    /**
     * 更新操作框
     * update the operation box
     */
    public void notifyBoxChanged() {
        changeBoxVisibility(true);
        setVisibility(VISIBLE);
        invalidate();
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (mOnPipTouchEventListener == null || mBoxInfo == null) {
            return false;
        }

        int pointerCount = event.getPointerCount();
        if (pointerCount > TWO_FINGER) {
            return false;
        }

        if (((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_DOWN) && (pointerCount == ONE_FINGER)) {
            mIsTwoFingerEvent = false;
        }
        targetX = event.getX();
        targetY = event.getY();
        if (pointerCount == TWO_FINGER) {
            mIsTwoFingerEvent = true;
            twoFingerTouch(event);
        } else {
            oneFingerTouch(event);
        }
        return true;
    }

    /**
     * 已经在x轴方向吸附范围内的标识
     * Identification within the adsorption range in the x-axis direction
     */

    private boolean mHadInXAdsorbRange;
    /**
     * 已经在y轴吸附范围内的标识
     * Identification that is already within the y-axis adsorption range
     */
    private boolean mHadInYAdsorbRange;

    private double mTotalRotationAngle;

    /**
     * 已经在旋转吸附范围内的标识
     * Identification that is already within the rotational adsorption range
     */
    private boolean mHadInRotationAdsorbRange;
    private final float TRANSLATE_ADSORB_DISTANCE = 30f;
    private final float ROTATION_ADSORB_DISTANCE = 10f;
    private final int RIGHT_ANGLE = 90;
    private float mNeedTranslateX;
    private float mNeedTranslateY;
    private boolean mMoveOutScreen = false;
    private float targetX, targetY;

    private void oneFingerTouch(MotionEvent event) {
        if (mIsTwoFingerEvent) {
            if (event.getAction() == MotionEvent.ACTION_UP) {
                mIsTwoFingerEvent = false;
                mOnPipTouchEventListener.onTouchUp(new PointF(targetX, targetY));
            }
            return;
        }
        int action = event.getAction();
        if (action == MotionEvent.ACTION_DOWN) {
            mLastPointF.set(targetX, targetY);
            mOnPipTouchEventListener.onTouchDown(new PointF(targetX, targetY));
            mIsInsideBox = insideOperationBox((int) targetX, (int) targetY);
            if (mBoxInfo != null) {
                PointF centerPointF = mBoxInfo.getCenterPointF();
                mHadInXAdsorbRange = Math.abs(centerPointF.x - (getWidth() / 2f)) <= TRANSLATE_ADSORB_DISTANCE;
                mHadInYAdsorbRange = Math.abs(centerPointF.y - (getHeight() / 2f)) <= TRANSLATE_ADSORB_DISTANCE;
            }
        } else if (action == MotionEvent.ACTION_UP) {
            PointF actionUpPoint = new PointF(targetX, targetY);
            mOnPipTouchEventListener.onTouchUp(actionUpPoint);
            mNeedTranslateX = 0;
            mNeedTranslateY = 0;
            mTotalRotationAngle = 0;
        } else if (action == MotionEvent.ACTION_MOVE) {
            if (!mIsVisible || mBoxInfo == null) {
                return;
            }
//            // 防止移出屏幕 Prevent screen removal.
//            if (targetX <= 100 || targetX >= getWidth() || targetY >= getHeight() || targetY <= 20) {
//                mMoveOutScreen = true;
//                return;
//            }
//            if (mMoveOutScreen) {
//                mMoveOutScreen = false;
//                return;
//            }
            //操作框中心点 Operation box center point.
            PointF centerPointF = mBoxInfo.getCenterPointF();
            PointF newPointF = new PointF(targetX + mNeedTranslateX, targetY + mNeedTranslateY);
            if (mIsInsideBox) {
                if (!dealWithTranslateAdsorb(centerPointF, newPointF)) {
                    mOnPipTouchEventListener.onDrag(mLastPointF, newPointF);
                } else {
                    return;
                }
            }
            mLastPointF = newPointF;
        }
    }

    private PointF mLastScalePointF = new PointF();

    private void twoFingerTouch(MotionEvent event) {
        if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_POINTER_DOWN) {
            mLastScalePointF.set(targetX, targetY);
            float xLen = event.getX(0) - event.getX(1);
            float yLen = event.getY(0) - event.getY(1);
            mTwoFingerOldPoint.set(xLen, yLen);
            mTwoFingerStartLength = Math.sqrt((xLen * xLen) + (yLen * yLen));
            mTotalRotationAngle = calculateBoxRotationAngle();
            mHadInRotationAdsorbRange = (Math.abs(mTotalRotationAngle % RIGHT_ANGLE) <= ROTATION_ADSORB_DISTANCE ||
                    Math.abs(Math.abs(mTotalRotationAngle % RIGHT_ANGLE) - RIGHT_ANGLE) <= ROTATION_ADSORB_DISTANCE);
        } else if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_MOVE) {
            // 防止移出屏幕 Prevent screen removal.
            if (targetX <= 100 || targetX >= getWidth() || targetY >= getHeight() || targetY <= 20) {
                mMoveOutScreen = true;
                return;
            }
            if (mMoveOutScreen) {
                mMoveOutScreen = false;
                return;
            }
            PointF centerPointF = mBoxInfo.getCenterPointF();
            PointF newPointF = new PointF(targetX + mNeedTranslateX, targetY + mNeedTranslateY);
            float xLen = event.getX(0) - event.getX(1);
            float yLen = event.getY(0) - event.getY(1);
            double mTwoFingerEndLength = Math.sqrt(xLen * xLen + yLen * yLen);
            float scale = (float) (mTwoFingerEndLength / mTwoFingerStartLength);

         /*   double theta = Math.atan2(targetY - centerPointF.y, targetX - centerPointF.x) -
                    Math.atan2(mLastPointF.y - centerPointF.y, mLastPointF.x - centerPointF.x);
            theta = theta * RIGHT_ANGLE * 2 / Math.PI;*/

            float oldDegree = (float) Math.toDegrees(Math.atan2(mTwoFingerOldPoint.x, mTwoFingerOldPoint.y));
            float newDegree = (float) Math.toDegrees(Math.atan2((event.getX(0) - event.getX(1)), (event.getY(0) - event.getY(1))));
            //得出移动的角度 Compute the angle of movement
            double theta = oldDegree - newDegree;
            /*
             * 排除异常情况
             * Eliminate abnormal conditions
             */
            if (Math.abs(theta) > 90) {
                theta = 0;
            }
            theta = dealWithRotationAdsorb(theta);
            if (theta != Double.MAX_VALUE) {
                //旋转与缩放 Rotate and Scale
                if (mOnPipTouchEventListener != null) {
                    mOnPipTouchEventListener.onScaleAndRotate(scale, (float) -theta);
                }
            } else {
                //在吸附范围内也要执行缩放,此时旋转角度是无效的。
                // Scaling should also be performed within the adsorption range, where the rotation angle is invalid.
                mOnPipTouchEventListener.onScaleAndRotate(scale, Float.MAX_VALUE);
                mLastScalePointF = newPointF;
                mTwoFingerStartLength = mTwoFingerEndLength;
                return;
            }

            mTwoFingerOldPoint.set(xLen, yLen);

            mTwoFingerStartLength = mTwoFingerEndLength;
            mLastScalePointF = newPointF;
            mLastPointF = newPointF;
        } else if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_UP) {
            mTotalRotationAngle = 0;
            mNeedTranslateX = 0;
            mNeedTranslateY = 0;
        }
    }

    /**
     * 处理旋转吸附
     * Deal with the rotation adsorb
     */
    private double dealWithRotationAdsorb(double theta) {
        double r = (mTotalRotationAngle + theta) % RIGHT_ANGLE;
        if (!mHadInRotationAdsorbRange) {
            if (Math.abs(r) <= ROTATION_ADSORB_DISTANCE) {
                //余数接近0，符合吸附条件的
                // Remainder close to 0, meeting adsorption conditions.
                // double b = theta < 0 ? -r + theta : -r + theta;
                theta = -r + theta;
                // LogUtils.d("余数接近0，需要移动的角度=" + b + ",LastRotationAngle=" + mTotalRotationAngle + ",r=" + r + ",total=" + (mTotalRotationAngle + b));
                mHadInRotationAdsorbRange = true;
                if (mVibrator != null) {
                    mVibrator.vibrate(50);
                }
            } else if (Math.abs(Math.abs(r) - RIGHT_ANGLE) <= ROTATION_ADSORB_DISTANCE) {
                //接近90，符合吸附条件的
                // Approaching 90, meeting the adsorption conditions.
                //double b = theta < 0 ? -(r + RIGHT_ANGLE) + theta : RIGHT_ANGLE - r + theta;
                theta = theta < 0 ? -(r + RIGHT_ANGLE) + theta : RIGHT_ANGLE - r + theta;
                //LogUtils.d("余数接近90，需要移动的角度=" + b + ",LastRotationAngle=" + mTotalRotationAngle + ",r=" + r + ",total=" + (mTotalRotationAngle + b));
                mHadInRotationAdsorbRange = true;
                if (mVibrator != null) {
                    mVibrator.vibrate(50);
                }
            }
        } else {
            mHadInRotationAdsorbRange = (Math.abs(r) <= ROTATION_ADSORB_DISTANCE || Math.abs(Math.abs(r) - RIGHT_ANGLE) <= ROTATION_ADSORB_DISTANCE);
            if (mHadInRotationAdsorbRange && Math.abs(theta) < ROTATION_ADSORB_DISTANCE) {
                //吸附保持。单次旋转角度较小，则保持吸附
                // Adsorption retention. If the single rotation angle is small, maintain adsorption.
                // LogUtils.d("移动角度小，保持，angle=" + theta);
                return Double.MAX_VALUE;
            }
        }
        mTotalRotationAngle = theta + mTotalRotationAngle;
        //LogUtils.d("theta=" + theta + ",mTotalRotationAngle=" + mTotalRotationAngle + ",r=" + r);
        return theta;
    }

    /**
     * 处理平移吸附
     * Deal with the translate adsorb
     */
    private boolean dealWithTranslateAdsorb(PointF centerPointF, PointF newPointF) {
        float translateDistance;
        if (!mHadInXAdsorbRange) {
            //没有在x轴方向吸附范围内
            // Not within the adsorption range in the x-axis direction.
            if (Math.abs(translateDistance = centerPointF.x - (getWidth() / 2f)) <= TRANSLATE_ADSORB_DISTANCE) {
                mHadInXAdsorbRange = true;
                //计算需要累计的x轴方向的平移值(位置变动了，但是在屏幕上触摸位置并没有等效变动，所以补上差值)
                //Calculate the cumulative translation value in the x-axis direction
                // (the position has changed, but there is no equivalent change when touching
                // the position on the screen, so add the difference)
                mNeedTranslateX = mNeedTranslateX - translateDistance;
                //更新新的点的位置
                // Update the position of new points.
                newPointF.set(newPointF.x - translateDistance, newPointF.y);
                if (mVibrator != null) {
                    mVibrator.vibrate(50);
                }
            }
        } else {
            //在x轴方向吸附范围内
            //Within the adsorption range in the x-axis direction.
            mHadInXAdsorbRange = Math.abs(centerPointF.x - (getWidth() / 2f)) <= TRANSLATE_ADSORB_DISTANCE;
        }
        if (!mHadInYAdsorbRange) {
            //没有在y轴方向吸附范围内
            // Not within the adsorption range in the y-axis direction.
            if (Math.abs(translateDistance = centerPointF.y - (getHeight() / 2f)) <= TRANSLATE_ADSORB_DISTANCE) {
                mHadInYAdsorbRange = true;
                //计算需要累计的y轴方向的平移值(位置变动了，但是在屏幕上触摸位置并没有等效变动，所以补上差值)
                //Calculate the cumulative translation value in the y-axis direction
                // (the position has changed, but there is no equivalent change when touching
                // the position on the screen, so add the difference)
                mNeedTranslateY = mNeedTranslateY - translateDistance;
                //更新新的点的位置
                // Update the position of new points.
                newPointF.set(newPointF.x, newPointF.y - translateDistance);
                if (mVibrator != null) {
                    mVibrator.vibrate(50);
                }
            }
        } else {
            //在y轴方向吸附范围内
            // Within the adsorption range in the y-axis direction.
            mHadInYAdsorbRange = Math.abs(centerPointF.y - (getHeight() / 2f)) <= TRANSLATE_ADSORB_DISTANCE;
        }
        boolean hold = false;
        float dx = Math.abs(newPointF.x - mLastPointF.x);
        float dy = Math.abs(newPointF.y - mLastPointF.y);
        //吸附保持范围
        // Adsorption retention range.
        double holdRange = TRANSLATE_ADSORB_DISTANCE;
        //如果x和y都在吸附保持范围内
        //如果x在吸附保持范围并且是沿x轴方向移动
        //如果y在吸附保持范围并且是沿y轴方向移动
        //这三种情况就保持吸附
        //If both x and y are within the adsorption retention range,
        // If x is within the adsorption holding range and moves along the x-axis direction,
        // If y is within the adsorption retention range and moves along the y-axis direction,
        // Maintain adsorption in these three situations
        if (mHadInXAdsorbRange && mHadInYAdsorbRange) {
            //x和y方向都进入吸附范围，
            // Both the x and y directions enter the adsorption range.
            hold = (dx <= holdRange && dy <= holdRange)
                    || dx <= holdRange && dx >= dy
                    || dy <= holdRange && dy >= dx;
        } else if (mHadInXAdsorbRange) {
            hold = dx <= holdRange && dx > dy;
        } else if (mHadInYAdsorbRange) {
            hold = dy <= holdRange && dy > dx;
        }
        return hold;
    }


    /**
     * 计算操作框的旋转角度
     * Calculates the rotation Angle of the operation box
     *
     * @return the angle
     */
    private double calculateBoxRotationAngle() {
        if (mBoxInfo == null) {
            return 0;
        }
        List<PointF> cornerPointList = mBoxInfo.getCornerPointList();
        if (cornerPointList == null || cornerPointList.size() < 2) {
            return 0;
        }
        PointF pointF0 = cornerPointList.get(0);
        PointF pointF1 = cornerPointList.get(1);
        float tan = (float) (Math.atan(Math.abs((pointF1.y - pointF0.y)
                / (pointF1.x - pointF0.x))) * 180 / Math.PI);
        if (pointF1.x > pointF0.x && pointF1.y > pointF0.y) {
            // 第一象限 first quadrant
            return Math.abs(-tan % RIGHT_ANGLE);
        } else if (pointF1.x > pointF0.x && pointF1.y < pointF0.y) {
            // 第二象限 second quadrant
            return Math.abs(tan % RIGHT_ANGLE);
        } else if (pointF1.x < pointF0.x && pointF1.y > pointF0.y) {
            // 第三象限 third quadrant
            return Math.abs((tan - RIGHT_ANGLE * 2) % RIGHT_ANGLE);
        } else {
            return Math.abs((RIGHT_ANGLE * 2 - tan) % RIGHT_ANGLE);
        }
    }

    /**
     * 所给点的是否在操作框内部
     * Whether the point given is inside the operation box
     *
     * @return true is inside ,false not
     */
    public boolean insideOperationBox(int x, int y) {
        if (mBoxInfo == null) {
            return false;
        }
        // 判断手指是否在框内
        // Determine if the fingers are inside the frame.
        return insideOperationBox(mBoxInfo.getCornerPointList(), x, y);
    }

    /**
     * 所给点的集合是否在操作框内部
     * Whether the set of points given is inside the operation box
     *
     * @return true is inside ,false not
     */
    public boolean insideOperationBox(List<PointF> pointList, int xPos, int yPos) {

        if (pointList == null || pointList.size() != 4) {
            return false;
        }
        // 判断手指是否在编辑框内
        // Determine if the finger is in the edit box.
        RectF r = new RectF();
        Path path = new Path();
        path.moveTo(pointList.get(0).x, pointList.get(0).y);
        path.lineTo(pointList.get(1).x, pointList.get(1).y);
        path.lineTo(pointList.get(2).x, pointList.get(2).y);
        path.lineTo(pointList.get(3).x, pointList.get(3).y);
        path.close();
        path.computeBounds(r, true);
        Region region = new Region();
        region.setPath(path, new Region((int) r.left, (int) r.top, (int) r.right, (int) r.bottom));
        return region.contains(xPos, yPos);
    }


    public interface OnPipTouchEventListener {

        void onTouchDown(PointF curPoint);

        void onScaleAndRotate(float scale, float degree);

        void onDrag(PointF prePointF, PointF nowPointF);

        void onTouchUp(PointF curPoint);

    }


}
