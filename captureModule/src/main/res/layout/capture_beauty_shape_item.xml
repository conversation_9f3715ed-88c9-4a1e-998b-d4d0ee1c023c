<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginLeft="4dp"
    android:layout_marginRight="4dp"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/shape_icon_layout"
        android:layout_width="@dimen/dp_px_90"
        android:layout_height="@dimen/dp_px_90"
        android:layout_centerHorizontal="true">

        <ImageView
            android:id="@+id/shape_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true" />

    </RelativeLayout>

    <TextView
        android:id="@+id/shape_txt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dp_px_120"
        android:gravity="center"
        android:text="@string/filter"
        android:textColor="@color/color_bcbcbc"
        android:textSize="@dimen/sp10" />

</RelativeLayout>