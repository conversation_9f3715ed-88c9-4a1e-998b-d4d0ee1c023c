package com.meishe.user.view.fragment;

import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.model.BaseMvpFragment;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.SizeUtils;
import com.meishe.base.view.PullToRefreshAndPushToLoadView;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.user.R;
import com.meishe.user.bean.VideoCompileBean;
import com.meishe.user.view.adapter.CloudCompileAdapter;
import com.meishe.user.view.iview.CloudDraftView;
import com.meishe.user.view.presenter.CloudDraftPresenter;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/9 13:28
 * @Description :云合成基础fragment The base cloud compile fragment
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class BaseCloudCompileFragment extends BaseMvpFragment<CloudDraftPresenter> implements CloudDraftView {
    protected RecyclerView mRecyclerView;
    protected CloudCompileAdapter mAdapter;
    protected PullToRefreshAndPushToLoadView mRefreshLayout;

    @Override
    protected CloudDraftPresenter createPresenter() {
        return new CloudDraftPresenter();
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_draft_cloud_compile_layout;
    }

    @Override
    protected void onLazyLoad() {
    }

    @Override
    protected void initView(View rootView) {
        mRecyclerView = rootView.findViewById(R.id.rv_list);
        mAdapter = new CloudCompileAdapter();
        mRecyclerView.setLayoutManager(new LinearLayoutManagerWrapper(getContext(), LinearLayoutManager.VERTICAL, false));
        int decoration = SizeUtils.dp2px(7.5F);
        mRecyclerView.addItemDecoration(new ItemDecoration(0, 0, 0, decoration));
        mRecyclerView.setAdapter(mAdapter);
        mRefreshLayout = rootView.findViewById(R.id.ptl_recyclerView);
        mRefreshLayout.setCanLoadMore(true);
        mRefreshLayout.setCanRefresh(true);
        mRefreshLayout.finishRefreshing();
        initListener();
    }

    @Override
    protected void initData() {

    }

    protected void initListener() {

    }

    @Override
    public void onNewDataBack(List<VideoCompileBean> data) {
        if (mAdapter != null) {
            mAdapter.setNewData(data);
            mRecyclerView.smoothScrollToPosition(0);
            finishRefresh();
            finishLoading();
        }
    }

    @Override
    public void onMoreDataBack(List<VideoCompileBean> data) {
        if (!CommonUtils.isEmpty(data)) {
            mAdapter.addData(data);
        }
        finishRefresh();
        finishLoading();
    }

    @Override
    public void onDataError() {
        finishRefresh();
        finishLoading();
    }

    @Override
    public void onDownloadProgress(String tag, int progress) {

    }

    protected void finishLoading() {
        if (mRefreshLayout.isLoading()) {
            mRefreshLayout.finishLoading();
        }
    }

    protected void finishRefresh() {
        if (mRefreshLayout.isRefreshing()) {
            mRefreshLayout.finishRefreshing();
        }
    }


    @Override
    public void refreshData(boolean isByUser) {

    }


    @Override
    public void onDelete(String tag){

    }


    public void onDownload(){

    }
}
