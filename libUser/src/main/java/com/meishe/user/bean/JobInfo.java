package com.meishe.user.bean;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/7/19 14:38
 * @Description :任务信息 the job info
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class JobInfo {
   private String createdAt;
   private String jobId;
   /**
    * 0:转码任务, 1:合成任务, 2:语音转写任务 10:三端互通项目
    * 0: Transcoding task, 1: Synthesis task, 2: Voice transcription task,
    * 10: Three terminal interoperability project
    */
   private String jobType;
   /**
    * 任务进度
    * The task progress
    */
   private int progress;
   private String projectId;
   /**
    * 任务完成后, 改字段内会有识别结果的url
    * After the task is completed, there will be a URL for the recognition result in the modification field.
    */
   private String recognitionResultUrl;
   private String resourceId;
   /**
    * 资源类型 1:视频 2:音频 3:图片 4: 识别
    * Resource type 1: Video 2: Audio 3: Image 4: Recognition.
    */
   private int resourceType;
   /**
    * 任务状态 0:初始状态, 1:完成, 2:失败, 3:超时, 5进行中
    * Task status 0: Initial state, 1: Completed, 2: Failed, 3: Timed out, 5 in progress.
    */
   private int status;
   private String title;
   private String workerId;
   /**
    * 如果是成片任务，会在任务完成时把成片路径返回
    * If it is a fragmented task, the fragmented path will be returned when the task is completed.
    */
   private String videoUrl;

   public String getCreatedAt() {
      return createdAt;
   }

   public void setCreatedAt(String createdAt) {
      this.createdAt = createdAt;
   }

   public String getJobId() {
      return jobId;
   }

   public void setJobId(String jobId) {
      this.jobId = jobId;
   }

   public String getJobType() {
      return jobType;
   }

   public void setJobType(String jobType) {
      this.jobType = jobType;
   }

   public int getProgress() {
      return progress;
   }

   public void setProgress(int progress) {
      this.progress = progress;
   }

   public String getProjectId() {
      return projectId;
   }

   public void setProjectId(String projectId) {
      this.projectId = projectId;
   }

   public String getRecognitionResultUrl() {
      return recognitionResultUrl;
   }

   public void setRecognitionResultUrl(String recognitionResultUrl) {
      this.recognitionResultUrl = recognitionResultUrl;
   }

   public String getResourceId() {
      return resourceId;
   }

   public void setResourceId(String resourceId) {
      this.resourceId = resourceId;
   }

   public int getResourceType() {
      return resourceType;
   }

   public void setResourceType(int resourceType) {
      this.resourceType = resourceType;
   }

   public int getStatus() {
      return status;
   }

   public void setStatus(int status) {
      this.status = status;
   }

   public String getTitle() {
      return title;
   }

   public void setTitle(String title) {
      this.title = title;
   }

   public String getWorkerId() {
      return workerId;
   }

   public void setWorkerId(String workerId) {
      this.workerId = workerId;
   }

   public String getVideoUrl() {
      return videoUrl;
   }

   public void setVideoUrl(String videoUrl) {
      this.videoUrl = videoUrl;
   }

   @Override
   public String toString() {
      return "JobInfo{" +
              "createdAt='" + createdAt + '\'' +
              ", jobId='" + jobId + '\'' +
              ", jobType='" + jobType + '\'' +
              ", progress=" + progress +
              ", projectId='" + projectId + '\'' +
              ", recognitionResultUrl='" + recognitionResultUrl + '\'' +
              ", resourceId='" + resourceId + '\'' +
              ", resourceType=" + resourceType +
              ", status=" + status +
              ", title='" + title + '\'' +
              ", workerId='" + workerId + '\'' +
              ", videoUrl='" + videoUrl + '\'' +
              '}';
   }
}
