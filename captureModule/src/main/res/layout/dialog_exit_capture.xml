<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_corners_white_8"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_exit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_px_30"
        android:layout_marginTop="@dimen/dp_px_36"
        android:layout_marginRight="@dimen/dp_px_102"
        android:drawableLeft="@mipmap/icon_capture_exit"
        android:drawablePadding="@dimen/dp_px_36"
        android:text="退出相机"
        android:textColor="@color/color_ffff365e"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_to_shoot"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_px_30"
        android:layout_marginTop="@dimen/dp_px_45"
        android:layout_marginRight="@dimen/dp_px_102"
        android:layout_marginBottom="@dimen/dp_px_36"
        android:drawableLeft="@mipmap/icon_capture_replay"
        android:drawablePadding="@dimen/dp_px_36"
        android:text="重新拍摄"
        android:textColor="@color/color_252525"
        android:textSize="14sp" />
</LinearLayout>