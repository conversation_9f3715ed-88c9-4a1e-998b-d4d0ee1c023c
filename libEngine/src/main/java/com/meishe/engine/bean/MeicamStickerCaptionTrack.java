package com.meishe.engine.bean;


import com.meicam.sdk.NvsColor;
import com.meicam.sdk.NvsObject;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsTimelineAnimatedSticker;
import com.meicam.sdk.NvsTimelineCaption;
import com.meicam.sdk.NvsTimelineCompoundCaption;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.local.LClipInfo;
import com.meishe.engine.local.LMeicamCaptionClip;
import com.meishe.engine.local.LMeicamCompoundCaptionClip;
import com.meishe.engine.local.LMeicamStickerCaptionTrack;
import com.meishe.engine.local.LMeicamStickerClip;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: ChuChenGuang
 * @CreateDate: 2021/5/20 10:30
 * @Description: 字幕，贴纸，组合字幕轨道 The track of caption、sticker、compound caption
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class MeicamStickerCaptionTrack extends TrackInfo<NvsTimeline> implements Cloneable, TimelineDataParserAdapter<LMeicamStickerCaptionTrack> {
    /**
     * clip数组
     * An ordered collection of clip info.
     */
    protected List<ClipInfo<?>> clipInfoList = new ArrayList<>();

    MeicamStickerCaptionTrack(NvsTimeline nvsTimeline, int index) {
        super(nvsTimeline, CommonData.TRACK_STICKER_CAPTION, index);
    }

    @Override
    void setIndex(int index) {
        super.setIndex(index);
        for (ClipInfo<?> clipInfo : clipInfoList) {
            clipInfo.setTrackIndex(index);
        }
    }

    List<ClipInfo<?>> getClipInfoList() {
        return clipInfoList;
    }

    void setClipInfoList(List<ClipInfo<?>> clipInfoList) {
        this.clipInfoList = clipInfoList;
    }

    /**
     * 根据入点查找clip
     * Find caption meicam caption clip.
     *
     * @param inPoint the in point
     * @return the meicam caption clip
     */
    public ClipInfo<?> getCaptionStickerClip(long inPoint) {
        for (ClipInfo<?> clipInfo : clipInfoList) {
            if (clipInfo.getInPoint() == inPoint) {
                return clipInfo;
            }
        }
        return null;
    }

    /**
     * 根据index查找clip
     * Find caption meicam caption clip.
     *
     * @param index the index
     * @return the meicam caption clip
     */
    public ClipInfo<?> getCaptionStickerClip(int index) {
        if (CommonUtils.isIndexAvailable(index, clipInfoList)) {
            return clipInfoList.get(index);
        }
        return null;
    }

    /**
     * 移除字幕，贴纸，组合字幕
     * Remove sticker caption clip boolean.
     *
     * @param inPoint         the in point
     * @param removeNvsObject the remove nvs object true移除 false不移除
     * @return the boolean
     */
    public ClipInfo<?> removeStickerCaptionClip(long inPoint, boolean removeNvsObject) {
        ClipInfo<?> captionStickerClip = getCaptionStickerClip(inPoint);
        if (captionStickerClip != null) {
            if (removeStickerCaptionClip(captionStickerClip, removeNvsObject)) {
                return captionStickerClip;
            }
        }
        return null;
    }


    /**
     * 移除字幕，贴纸，组合字幕
     * Remove sticker caption clip boolean.
     *
     * @param clipInfo        the clip info
     * @param removeNvsObject the remove nvs object  true移除 false不移除
     * @return the boolean
     */
    public boolean removeStickerCaptionClip(ClipInfo<?> clipInfo, boolean removeNvsObject) {
        if (clipInfo == null) {
            return false;
        }
        if (!removeNvsObject) {
            boolean isSuccess = clipInfoList.remove(clipInfo);
            if (!isSuccess) {
                for (int i = 0; i < clipInfoList.size(); i++) {
                    ClipInfo<?> itemInfo = clipInfoList.get(i);
                    if (itemInfo.getInPoint() == clipInfo.getInPoint() || itemInfo.sameCreateTag(clipInfo.getCreateTag())) {
                        clipInfoList.remove(i);
                        isSuccess = true;
                        break;
                    }
                }
            }
            if (isSuccess) {
                Collections.sort(clipInfoList);
            } else {
                LogUtils.e("not find the target clip " + clipInfo);
            }
            return isSuccess;
        }
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("removeStickerCaptionClip MeicamTimeline=null");
            return false;
        }
        boolean success = false;
        for (int i = getClipCount() - 1; i >= 0; i--) {
            ClipInfo<?> itemInfo = clipInfoList.get(i);
            if (itemInfo.getInPoint() == clipInfo.getInPoint() && itemInfo.sameCreateTag(clipInfo.getCreateTag())) {
                clipInfoList.remove(i);
                if (itemInfo instanceof MeicamCaptionClip) {
                    nvsTimeline.removeCaption((NvsTimelineCaption) itemInfo.getObject());
                } else if (itemInfo instanceof MeicamStickerClip) {
                    nvsTimeline.removeAnimatedSticker((NvsTimelineAnimatedSticker) itemInfo.getObject());
                } else if (itemInfo instanceof MeicamCompoundCaptionClip) {
                    nvsTimeline.removeCompoundCaption((NvsTimelineCompoundCaption) itemInfo.getObject());
                }
                success = true;
            }
        }
        Collections.sort(clipInfoList);
        return success;
    }

    /**
     * 移除字幕，贴纸，组合字幕
     * Remove sticker caption clip.
     *
     * @param clipInfo the clip info
     */
    public boolean removeStickerCaptionClip(ClipInfo<?> clipInfo) {
        return removeStickerCaptionClip(clipInfo, true);
    }


    /**
     * 添加字幕，贴纸，组合字幕
     * Add caption sticker clip info.
     *
     * @param clipInfo     the clip info
     * @param addNvsObject the add nvs object 是否创建新的对象添加 true 创建  false不创建
     * @return the clip info
     */
    public ClipInfo<?> addCaptionSticker(ClipInfo<?> clipInfo, boolean addNvsObject) {
        if (!addNvsObject) {
            clipInfo.setTrackIndex(getIndex());
            addCaptionStick(clipInfo);
            return clipInfo;
        }
        if (clipInfo instanceof MeicamCaptionClip) {
            return addModularCaption((MeicamCaptionClip) clipInfo);
        } else if (clipInfo instanceof MeicamStickerClip) {
            return addSticker((MeicamStickerClip) clipInfo);
        } else if (clipInfo instanceof MeicamCompoundCaptionClip) {
            return addCompoundCaption((MeicamCompoundCaptionClip) clipInfo);
        }
        return null;
    }

    /**
     * 添加模块字幕
     * Add modular caption
     *
     * @param captionClip 字幕对象
     * @return the captionClip
     */
    public MeicamCaptionClip addModularCaption(MeicamCaptionClip captionClip) {
        if (captionClip == null) {
            return null;
        }
        NvsTimelineCaption nvsTimelineCaption;
        if (MeicamCaptionClip.CAPTION_TYPE_MODULAR.equals(captionClip.getCaptionType())) {
            nvsTimelineCaption = addNvsModularCaption(captionClip.getText(), captionClip.getInPoint(), captionClip.getOutPoint());
        } else {
            nvsTimelineCaption = addNvsNormalCaption(captionClip.getText(), captionClip.getInPoint(), captionClip.getOutPoint());
        }
        if (nvsTimelineCaption == null) {
            return null;
        }
        captionClip.setObject(nvsTimelineCaption);
        captionClip.setTrackIndex(getIndex());
        //captionClip.setZValue(getIndex());
        captionClip.bindToTimeline();
        addCaptionStick(captionClip);
        return captionClip;
    }


    /**
     * 添加模块字幕
     * Add modular caption
     *
     * @param text     the text
     * @param inPoint  the in point
     * @param outPoint the out Point
     * @return the nvstimelinecaption
     */
    public MeicamCaptionClip addModularCaption(String text, long inPoint, long outPoint) {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("Timeline is null");
            return null;
        }
        NvsTimelineCaption nvsCaption = addNvsModularCaption(text, inPoint,
                outPoint);
        if (nvsCaption == null) {
            return null;
        }
        return addMeicamModularCaption(nvsCaption);
    }

    /**
     * 添加普通字幕
     * Add modular caption
     *
     * @param text     the text
     * @param inPoint  the in point
     * @param outPoint the out Point
     * @return the nvstimelinecaption
     */
    public MeicamCaptionClip addNormalCaption(String text, long inPoint, long outPoint) {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("Timeline is null");
            return null;
        }
        NvsTimelineCaption nvsCaption = addNvsNormalCaption(text, inPoint,
                outPoint);
        if (nvsCaption == null) {
            return null;
        }
        return addMeicamModularCaption(nvsCaption);
    }

    /**
     * 恢复上层数据
     * Add meicam modular caption meicam caption clip.
     *
     * @param nvsTimelineCaption the nvs timeline caption
     * @return the meicam caption clip
     */
    public MeicamCaptionClip addMeicamModularCaption(NvsTimelineCaption nvsTimelineCaption) {
        if (nvsTimelineCaption == null) {
            return null;
        }
        nvsTimelineCaption.setClipAffinityEnabled(false);
        MeicamCaptionClip meicamCaptionClip = new MeicamCaptionClip(nvsTimelineCaption,
                nvsTimelineCaption.getText(), nvsTimelineCaption.getInPoint(), nvsTimelineCaption.getOutPoint());
        meicamCaptionClip.loadData();
        meicamCaptionClip.setTrackIndex(getIndex());
        addCaptionStick(meicamCaptionClip);
        return meicamCaptionClip;
    }


    /**
     * Add nvs modular caption
     * 创建底层模块字幕
     *
     * @param text the text 字幕内容
     * @param inPoint the in point 入点
     * @param outPoint the out point 出点
     * @return The native NvsTimelineCaption object 底层字幕实体
     */
    private NvsTimelineCaption addNvsModularCaption(String text, long inPoint, long outPoint) {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("addNvsModularCaption MeicamTimeline=null");
            return null;
        }
        return nvsTimeline.addModularCaption(text, inPoint,
                outPoint - inPoint);
    }

    /**
     * Add nvs normal caption
     * 创建底层普通字幕
     *
     * @param text the text 字幕内容
     * @param inPoint the in point 入点
     * @param outPoint the out point 出点
     * @return The native NvsTimelineCaption object 底层字幕实体
     */
    private NvsTimelineCaption addNvsNormalCaption(String text, long inPoint, long outPoint) {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("addNvsModularCaption MeicamTimeline=null");
            return null;
        }
        return nvsTimeline.addCaption(text, inPoint,
                outPoint - inPoint, null);
    }

    /**
     * 添加组合字幕
     * Add compound caption meicam compound caption clip.
     *
     * @param inPoint  the in point
     * @param outPoint the out point
     * @param style    the style
     * @return the meicam compound caption clip
     */
    public MeicamCompoundCaptionClip addCompoundCaption(long inPoint, long outPoint, String style) {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("Timeline is null");
            return null;
        }
        NvsTimelineCompoundCaption nvsTimelineCompoundCaption = addNvsCompoundCaption(inPoint, outPoint, style);

        return addMeicamCompoundCaption(nvsTimelineCompoundCaption);
    }

    /**
     * 添加Meicam上层数据
     * Add meicam compound caption meicam compound caption clip.
     *
     * @param nvsTimelineCompoundCaption the nvs timeline compound caption
     * @return the meicam compound caption clip
     */
    public MeicamCompoundCaptionClip addMeicamCompoundCaption(NvsTimelineCompoundCaption nvsTimelineCompoundCaption) {
        if (nvsTimelineCompoundCaption == null) {
            return null;
        }
        nvsTimelineCompoundCaption.setClipAffinityEnabled(false);
        MeicamCompoundCaptionClip meicamCompoundCaptionClip = new MeicamCompoundCaptionClip(nvsTimelineCompoundCaption, nvsTimelineCompoundCaption.getInPoint(), nvsTimelineCompoundCaption.getOutPoint(),
                nvsTimelineCompoundCaption.getCaptionStylePackageId());
        //meicamCompoundCaptionClip.setZValue(getIndex());
        meicamCompoundCaptionClip.setTrackIndex(getIndex());

        int captionCount = nvsTimelineCompoundCaption.getCaptionCount();
        meicamCompoundCaptionClip.getCompoundCaptionItems().clear();
        for (int index = 0; index < captionCount; index++) {
            MeicamCompoundCaptionItem meicamCompoundCaptionItem = new MeicamCompoundCaptionItem(index, nvsTimelineCompoundCaption.getText(index));
            NvsColor color = nvsTimelineCompoundCaption.getTextColor(index);
            float[] textColor = new float[4];
            textColor[0] = color.r;
            textColor[1] = color.g;
            textColor[2] = color.b;
            textColor[3] = color.a;
            meicamCompoundCaptionItem.setTextColor(textColor);
            meicamCompoundCaptionItem.setFont(nvsTimelineCompoundCaption.getFontFamily(index));
            meicamCompoundCaptionClip.getCompoundCaptionItems().add(meicamCompoundCaptionItem);
        }
        addCaptionStick(meicamCompoundCaptionClip);
        return meicamCompoundCaptionClip;
    }

    /**
     * 添加组合字幕
     * Add Compound Caption Clip
     *
     * @param meicamCompoundCaptionClip 组合字幕对象
     * @return the meicamCompoundCaptionClip
     */
    public MeicamCompoundCaptionClip addCompoundCaption(MeicamCompoundCaptionClip meicamCompoundCaptionClip) {
        if (meicamCompoundCaptionClip == null) {
            return null;
        }
        NvsTimelineCompoundCaption nvsTimelineCompoundCaption = addNvsCompoundCaption(meicamCompoundCaptionClip.getInPoint(), meicamCompoundCaptionClip.getOutPoint(), meicamCompoundCaptionClip.getStyleDesc());
        if (nvsTimelineCompoundCaption == null) {
            return null;
        }
        meicamCompoundCaptionClip.setObject(nvsTimelineCompoundCaption);
        meicamCompoundCaptionClip.setTrackIndex(getIndex());
        //meicamCompoundCaptionClip.setZValue(getIndex());
        meicamCompoundCaptionClip.bindToTimeline();
        addCaptionStick(meicamCompoundCaptionClip);
        return meicamCompoundCaptionClip;
    }

    /**
     * add NvsCompoundCaption
     * 创建底层组合字幕
     *
     * @param inPoint the in point 入点
     * @param outPoint the out point 出点
     * @param style the style 字幕类型
     * @return The native object native实例
     */
    private NvsTimelineCompoundCaption addNvsCompoundCaption(long inPoint, long outPoint, String style) {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("Timeline is null");
            return null;
        }
        return nvsTimeline.addCompoundCaption(inPoint,
                outPoint - inPoint, style);
    }


    /**
     * 添加贴纸
     * Add sticker nvs timeline animated sticker.
     *
     * @param inPoint                        the in point
     * @param outPoint                       the outPoint
     * @param packageId                      the package id
     * @param isCustomSticker                the is custom sticker 是否是自定义贴纸
     * @param customAnimatedStickerImagePath the custom animated sticker image path 自定义贴纸路径
     * @return the MeicamStickerClip
     */
    public MeicamStickerClip addSticker(long inPoint, long outPoint, String packageId, boolean isCustomSticker, String customAnimatedStickerImagePath) {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("Timeline is null");
            return null;
        }
        NvsTimelineAnimatedSticker nvsTimelineAnimatedSticker = addNvsSticker(inPoint, outPoint, packageId, isCustomSticker, customAnimatedStickerImagePath);
        if (nvsTimelineAnimatedSticker == null) {
            return null;
        }
        nvsTimelineAnimatedSticker.setClipAffinityEnabled(false);
        MeicamStickerClip meicamStickerClip = new MeicamStickerClip(nvsTimelineAnimatedSticker, inPoint, outPoint, packageId, isCustomSticker, customAnimatedStickerImagePath);
        //meicamStickerClip.setZValue(getIndex());
        meicamStickerClip.setTrackIndex(getIndex());
        addCaptionStick(meicamStickerClip);
        return meicamStickerClip;
    }

    /**
     * 恢复上层数据
     * Add meicam sticker meicam sticker clip.
     *
     * @param nvsTimelineAnimatedSticker the nvs timeline animated sticker
     * @return the meicam sticker clip
     */
    public MeicamStickerClip addMeicamSticker(NvsTimelineAnimatedSticker nvsTimelineAnimatedSticker) {
        if (nvsTimelineAnimatedSticker == null) {
            return null;
        }
        nvsTimelineAnimatedSticker.setClipAffinityEnabled(false);
        MeicamStickerClip meicamStickerClip = new MeicamStickerClip(nvsTimelineAnimatedSticker, nvsTimelineAnimatedSticker.getInPoint(), nvsTimelineAnimatedSticker.getOutPoint(),
                nvsTimelineAnimatedSticker.getAnimatedStickerPackageId(), false, null);
        //meicamStickerClip.setZValue(getIndex());
        meicamStickerClip.setTrackIndex(getIndex());
        addCaptionStick(meicamStickerClip);
        return meicamStickerClip;
    }

    /**
     * 添加贴纸字幕
     * Add sticker
     *
     * @param meicamStickerClip 贴纸对象
     * @return the meicamStickerClip
     */
    public MeicamStickerClip addSticker(MeicamStickerClip meicamStickerClip) {
        if (meicamStickerClip == null) {
            return null;
        }
        NvsTimelineAnimatedSticker nvsTimelineAnimatedSticker = addNvsSticker(meicamStickerClip.getInPoint(), meicamStickerClip.getOutPoint(), meicamStickerClip.getPackageId()
                , meicamStickerClip.getIsCustomSticker(), meicamStickerClip.getCustomAnimatedStickerImagePath());
        if (nvsTimelineAnimatedSticker == null) {
            return null;
        }
        meicamStickerClip.setObject(nvsTimelineAnimatedSticker);
        meicamStickerClip.setTrackIndex(getIndex());
        //meicamStickerClip.setZValue(getIndex());
        meicamStickerClip.bindToTimeline();
        addCaptionStick(meicamStickerClip);
        return meicamStickerClip;
    }

    /**
     * Add sticker animated sticker.
     * 创建底层贴纸
     *
     * @param inPoint                        the in point
     * @param outPoint                       the out point
     * @param packageId                      the package id
     * @param isCustomSticker                the is custom sticker
     * @param customAnimatedStickerImagePath the custom animated sticker image path
     * @return the nvs timeline animated sticker
     */
    private NvsTimelineAnimatedSticker addNvsSticker(long inPoint, long outPoint, String packageId, boolean isCustomSticker, String customAnimatedStickerImagePath) {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("addNvsSticker MeicamTimeline=null");
            return null;
        }
        return isCustomSticker ?
                nvsTimeline.addCustomAnimatedSticker(inPoint, outPoint - inPoint, packageId, customAnimatedStickerImagePath)
                : nvsTimeline.addAnimatedSticker(inPoint, outPoint - inPoint, packageId);
    }

    /**
     * 添加字幕贴纸组合字幕上层数据
     * Add caption stick.
     *
     * @param clipInfo the clip info
     */
    private void addCaptionStick(ClipInfo<?> clipInfo) {
        clipInfoList.add(clipInfo);
        if (!clipInfoList.isEmpty()) {
            float zValue = getzValue();
            for (int index = 0; index < clipInfoList.size(); index++) {
                ClipInfo<?> clipInfoItem = clipInfoList.get(index);
                clipInfoItem.setZValue(zValue);
                clipInfoItem.setIndex(index);
            }
        }
        Collections.sort(clipInfoList);
    }

    @Override
    public int getClipCount() {
        return clipInfoList.size();
    }

    @Override
    public LMeicamStickerCaptionTrack parseToLocalData() {
        LMeicamStickerCaptionTrack local = new LMeicamStickerCaptionTrack(getIndex());
        for (ClipInfo<?> clipInfo : clipInfoList) {
            local.getClipInfoList().add(clipInfo.parseToLocalData());
        }
        setCommondData(local);
        return local;
    }

    @Override
    public void recoverFromLocalData(LMeicamStickerCaptionTrack trackInfo) {
        setShow(trackInfo.isShow());
        setVolume(trackInfo.getVolume());
        for (LClipInfo lClip : trackInfo.getClipInfoList()) {
            //删除封面字幕
            //remove cover caption
            if (lClip.getAttachment(CommonData.ATTACHMENT_KEY_IS_COVER_CAPTION) != null) {
                continue;
            }
            if (lClip instanceof LMeicamCaptionClip) {
                LMeicamCaptionClip lMeicamCaptionClip = (LMeicamCaptionClip) lClip;
                MeicamCaptionClip meicamCaptionClip;
                if (MeicamCaptionClip.CAPTION_TYPE_NORMAL.equals(lMeicamCaptionClip.getCaptionType())) {
                    meicamCaptionClip = addNormalCaption(lMeicamCaptionClip.getText(),
                            lMeicamCaptionClip.getInPoint(), lMeicamCaptionClip.getOutPoint());
                } else {
                    meicamCaptionClip = addModularCaption(lMeicamCaptionClip.getText(),
                            lMeicamCaptionClip.getInPoint(), lMeicamCaptionClip.getOutPoint());
                }
                if (meicamCaptionClip != null) {
                    meicamCaptionClip.recoverFromLocalData(lMeicamCaptionClip);
                }
            } else if (lClip instanceof LMeicamStickerClip) {
                LMeicamStickerClip lMeicamStickerClip = (LMeicamStickerClip) lClip;
                MeicamStickerClip meicamStickerClip = addSticker(lMeicamStickerClip.getInPoint(),
                        lMeicamStickerClip.getOutPoint(), lMeicamStickerClip.getPackageId(), lMeicamStickerClip.isCustomSticker()
                        , lMeicamStickerClip.getCustomanimatedStickerImagePath());
                if (meicamStickerClip != null) {
                    meicamStickerClip.recoverFromLocalData(lMeicamStickerClip);
                }
            } else if (lClip instanceof LMeicamCompoundCaptionClip) {
                LMeicamCompoundCaptionClip lMeicamCompoundCaptionClip = (LMeicamCompoundCaptionClip) lClip;
                MeicamCompoundCaptionClip meicamCompoundCaptionClip = addCompoundCaption(lMeicamCompoundCaptionClip.getInPoint(),
                        lMeicamCompoundCaptionClip.getOutPoint()
                        , lMeicamCompoundCaptionClip.getStyleDesc());
                if (meicamCompoundCaptionClip != null) {
                    meicamCompoundCaptionClip.recoverFromLocalData(lMeicamCompoundCaptionClip);
                }
            }
        }
    }

    @Override
    public void recoverFromTimelineData(NvsObject nvsObject) {
        if (nvsObject instanceof NvsTimelineCaption) {
            NvsTimelineCaption nvsTimelineCaption = (NvsTimelineCaption) nvsObject;
            MeicamCaptionClip meicamCaptionClip = addMeicamModularCaption(nvsTimelineCaption);
            if (meicamCaptionClip != null) {
                if (nvsTimelineCaption.isModular()) {
                    meicamCaptionClip.setCaptionType(MeicamCaptionClip.CAPTION_TYPE_MODULAR);
                } else {
                    meicamCaptionClip.setCaptionType(MeicamCaptionClip.CAPTION_TYPE_NORMAL);
                }
                meicamCaptionClip.recoverFromTimelineData(nvsTimelineCaption);
            }
        } else if (nvsObject instanceof NvsTimelineAnimatedSticker) {
            NvsTimelineAnimatedSticker nvsTimelineAnimatedSticker = (NvsTimelineAnimatedSticker) nvsObject;
            MeicamStickerClip meicamStickerClip = addMeicamSticker(nvsTimelineAnimatedSticker);
            if (meicamStickerClip != null) {
                meicamStickerClip.recoverFromTimelineData(nvsTimelineAnimatedSticker);
            }
        } else if (nvsObject instanceof NvsTimelineCompoundCaption) {
            NvsTimelineCompoundCaption nvsTimelineCompoundCaption = (NvsTimelineCompoundCaption) nvsObject;
            MeicamCompoundCaptionClip meicamCompoundCaptionClip = addMeicamCompoundCaption(nvsTimelineCompoundCaption);
            if (meicamCompoundCaptionClip != null) {
                meicamCompoundCaptionClip.recoverFromTimelineData(nvsTimelineCompoundCaption);
            }
        }
    }

    /**
     * 排序
     * Resort
     */
    public void reSort() {
        if (!CommonUtils.isEmpty(clipInfoList)) {
            Collections.sort(clipInfoList);
        }
    }
}
