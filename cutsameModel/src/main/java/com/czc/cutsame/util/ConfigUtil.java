package com.czc.cutsame.util;


import android.content.Context;

import com.czc.cutsame.R;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.Utils;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.asset.bean.AssetsConstants;
import com.meishe.engine.asset.bean.RequestParam;
import com.meishe.engine.asset.bean.TabParam;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/3/22 10:17
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class ConfigUtil {
    public static final String BUILD_TYPE_TOB = "2B";
    public static final String BUILD_TYPE_TOC = "2C";

    public static boolean isToC() {
        String className = Utils.getApp().getPackageName() + ".BuildConfig";
        try {
            Class<?> aClass = Class.forName(className);
            Field flavor = aClass.getField("FLAVOR");
            Object obj = flavor.get(null);
            return (obj instanceof String && ((String)obj).contains(BUILD_TYPE_TOC));
        } catch (Exception e) {
            LogUtils.e(e);
        }
        return true;
    }

    public static List<TabParam> getTabList(Context context, int type) {
        if (AssetsManager.IS_NEW_ASSETS && !isToC()) {
            if (type == AssetsConstants.AssetsTypeData.TEMPLATE.type) {
                List<TabParam> params = new ArrayList<>();
                String[] tabs = context.getResources().getStringArray(R.array.tab_template);
                if (tabs != null && tabs.length > 0) {
                    for (int index = 0; index < tabs.length; index++) {
                        params.add(new TabParam(tabs[index], new RequestParam(type, index, AssetsConstants.AssetsTypeData.TEMPLATE.category, index + 1)));
                    }
                }
                return params;
            }
        } else {
            if (type == AssetsConstants.AssetsTypeData.TEMPLATE.type) {
                List<TabParam> params = new ArrayList<>();
                String[] tabs = context.getResources().getStringArray(R.array.tab_template_old);
                if (tabs != null && tabs.length > 0) {
                    for (int index = 0; index < tabs.length; index++) {
                        params.add(new TabParam(tabs[index],
                                new RequestParam(type, index, AssetsConstants.AssetsTypeData.TEMPLATE.category, index + 1)));
                    }
                }
                return params;
            }
        }
        return new ArrayList<>();
    }

    /**
     * Need convert boolean.
     * 是否需要转码的开关
     * @return the boolean
     */
    public static boolean needConvert() {
        return true;
    }

    public static int[] colors = {R.color.color_point_group_1, R.color.color_point_group_2, R.color.color_point_group_3, R.color.color_point_group_4, R.color.color_point_group_5
            , R.color.color_point_group_6, R.color.color_point_group_7, R.color.color_point_group_8, R.color.color_point_group_9, R.color.color_point_group_10
            , R.color.color_point_group_11, R.color.color_point_group_12, R.color.color_point_group_13};
}
