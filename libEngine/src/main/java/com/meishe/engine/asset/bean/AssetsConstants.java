package com.meishe.engine.asset.bean;

import android.content.Context;
import android.content.res.Resources;

import com.meishe.engine.R;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/3/24 14:08
 * @Description :资源常量 Constants of assets
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
@SuppressWarnings("ALL")
public class AssetsConstants {

    public enum AssetsTypeData {
        CAPTION_FLOWER(3, 2, 1),
        CAPTION_BUBBLE(3, 2, 2),
        CAPTION_ANIMATION_IN(3, 2, 3),
        CAPTION_ANIMATION_OUT(3, 2, 4),
        CAPTION_ANIMATION_COMP(3, 2, 5),
        CAPTION_STYLE(3, 1, -1),

        FILTER(2, 1, -1),
        EFFECT(2, 2, 4),
        EFFECT_SHAKING(2, 2, 3),
        EFFECT_DREAM(2, 2, 6),
        EFFECT_FRAME(2, 2, 9),

        ANIMATION_IN(2, 3, 1),
        ANIMATION_OUT(2, 3, 2),
        ANIMATION_COMP(2, 3, 3),

        TEMPLATE(19, -1, -1),

        CAPTION_COMP(15, -1, -1),

        STICKER(4, 1, -1),
        STICKER_VOICE(4, 2, -1),
        STICKER_CUSTOM(4, 20000, -1),

        STICKER_ANIMATION(4, 3, -1),
        STICKER_ANIMATION_IN(4, 3, 1),
        STICKER_ANIMATION_OUT(4, 3, 2),
        STICKER_ANIMATION_COMP(4, 3, 3),

        TRANSITION(5, -1, -1),
        PROP(14, -1, 0),

        NONE(-1, -1, -1);


        /**
         * 一级类型 the type
         */
        public int type;
        /**
         * 二级分类 the category
         */
        public int category;
        /**
         * 三级分类 the kind
         */
        public int kind;

        AssetsTypeData(int type, int category, int kind) {
            this.type = type;
            this.category = category;
            this.kind = kind;
        }
    }

    public static String getAssetsTypeName(Context context, int type) {
        String name = "";
        Resources resources = context.getResources();
        switch (type) {
            case AssetInfo.ASSET_THEME:
                name = resources.getString(R.string.assets_type_name_theme);
                break;
            case AssetInfo.ASSET_FILTER:
                name = resources.getString(R.string.assets_type_name_filter);
                break;
            case AssetInfo.ASSET_CAPTION_STYLE:
                name = resources.getString(R.string.assets_type_name_caption);
                break;
            case AssetInfo.ASSET_ANIMATED_STICKER:
                name = resources.getString(R.string.assets_type_name_sticker);
                break;
            case AssetInfo.ASSET_VIDEO_TRANSITION:
            case AssetInfo.ASSET_VIDEO_TRANSITION_EFFECT:
            case AssetInfo.ASSET_VIDEO_TRANSITION_3D:
                name = resources.getString(R.string.assets_type_name_transition);
                break;
            case AssetInfo.ASSET_COMPOUND_CAPTION:
                name = resources.getString(R.string.assets_type_name_comp_caption);
                break;
            case AssetInfo.ASSET_EFFECT_LIVELY:
            case AssetInfo.ASSET_EFFECT_SHAKING:
            case AssetInfo.ASSET_EFFECT_DREAM:
            case AssetInfo.ASSET_EFFECT_FRAME:
            case AssetInfo.ASSET_EFFECT_OTHER:
                name = resources.getString(R.string.assets_type_name_effect);
                break;
            case AssetInfo.ASSET_ANIMATION_IN:
                name = resources.getString(R.string.assets_type_name_anim_in);
                break;
            case AssetInfo.ASSET_ANIMATION_OUT:
                name = resources.getString(R.string.assets_type_name_anim_out);
                break;
            case AssetInfo.ASSET_ANIMATION_GROUP:
                name = resources.getString(R.string.assets_type_name_anim_com);
                break;
            case AssetInfo.ASSET_CUSTOM_CAPTION_FLOWER:
                name = resources.getString(R.string.assets_type_name_flower);
                break;
            case AssetInfo.ASSET_CUSTOM_CAPTION_BUBBLE:
                name = resources.getString(R.string.assets_type_name_bubble);
                break;
            case AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_IN:
            case AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_OUT:
            case AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_COMBINATION:
                name = resources.getString(R.string.assets_type_name_caption_animation);
                break;
            case AssetInfo.ASSET_STICKER_ANIMATION_COMP:
            case AssetInfo.ASSET_STICKER_ANIMATION_IN:
            case AssetInfo.ASSET_STICKER_ANIMATION_OUT:
                name = resources.getString(R.string.assets_type_name_sticker_animation);
            default:
                break;
        }
        return name;
    }
}
