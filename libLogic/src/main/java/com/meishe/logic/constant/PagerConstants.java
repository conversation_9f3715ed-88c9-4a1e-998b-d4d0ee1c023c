package com.meishe.logic.constant;


/**
 * All rights reserved,Designed by www.meishesdk.com
 * 版权所有:www.meishesdk.com
 *
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate :2020/12/9 15:47
 * @Description :页面跳转、回调所需的常量 Constants required for page jumps
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class PagerConstants {
    /*
     * 公用常量---公用常量----公用常量
     * Common constant -- Common constant -- common constant
     * */
    public static final int REQUEST_CODE_1 = 1;
    public static final int REQUEST_CODE_2 = 2;
    public static final int REQUEST_CODE_3 = 3;
    public static final int REQUEST_CODE_4 = 4;
    public static final String FROM_PAGE = "from_page";//来自的页面 Page from
    public static final int FROM_MAIN_PAGE = 0;//来自首页 From the home page
    public static final int FROM_MATERIAL_SELECTED = 1;//来自素材选择页面 From the Material selection page
    public static final int FROM_DRAFT_EDIT = 2;//来自编辑页 From the editor page
    public static final int FROM_FEED_BACK_PAGE = 3;//来自意见反馈页面 From the feedback page
    public static final int FROM_TEMPLATE = 4;//来自模板页面
    public static final int FROM_UPLOAD = 5;//来自上传页面
    public static final int FROM_CAPTURE = 6;//来自拍摄页面
    public static final String NEXT_PAGE_ACTION = "next.action";//下一页面的的action The action on the next page
    public static final String SELECTED_TYPE = "selected.type";
    public static final String SELECTED_NEED_PREVIEW = "selected.needPreview";
    public static final String BUNDLE_DATA = "bundle.data";
    public static final String BUNDLE_SAVE_DRAFT = "save_draft";
    public static final String BUNDLE_CLIP_REPLACE_DURATION = "replace_duration";
    public static final String BUNDLE_CLIP_LIST = "bundle.clip.list";
    public static final String BUNDLE_CLIP = "bundle.clip";
    /*------------------------------------------------------结束线 End line---------------------------------------------------------------------------*/

    /*
     * 编辑页面
     * Editing pages
     * */
    public final static String DRAFT_PATH = "draft_path";//
    public final static String BUNDLE_KEY_TIMELINE_DATA = "timelineData";
    /*------------------------------------------------------结束线 End line--------------   -------------------------------------------------------------*/

    /*
     * 素材选择页面
     * Material selection page
     * */
    public final static int TYPE_DEFAULT = 0;//默认 default
    public final static int TYPE_ONE_FINISH = 1;//选中一个就跑 Pick one and run
    public final static int TYPE_ADD_SOME = 2;//增加 add
    public static final String MEDIA_TYPE = "media.type";
    public static final String MEDIA_FILTER = "media.filter";
    public static final String MEDIA_PATH = "media.path";
    public static final String MEDIA_DATA = "media.data";
    public static final String MEDIA_TAG = "media.tag";
    public static final String MEDIA_MAX_NUM = "media.maxNum";
    public static final int MEDIA_REQUEST_CODE_PREVIEW = 11;
    /*------------------------------------------------------结束线 End line---------------------------------------------------------------------------*/

    /*
     * 自定义贴纸页面
     * Custom sticker page
     * */
    public static final String FILE_PATH = "file.path";
    /*------------------------------------------------------结束线 End line---------------------------------------------------------------------------*/

    /*
     * 网页
     * webpage
     * */
    public static final String URL = "uri";
    /*------------------------------------------------------结束线 End line---------------------------------------------------------------------------*/

    /*
     * 资源下载页面
     * Resource Download page
     * */
    public static final String TITLE_ID = "title.id";
    public static final String ASSET_TYPE = "asset.type";
    public static final String RATIO = "ratio";
    /*------------------------------------------------------结束线 End line---------------------------------------------------------------------------*/

    /*
     * 编辑-全屏预览页面
     * Edit - Full screen preview page
     * */
    public static final String START_TIME = "start.time";
    /*------------------------------------------------------结束线 End line---------------------------------------------------------------------------*/

    /*
     * 通用模板相关页面
     * Cut pages related to the same style
     * */
    public static final String DATA_TEMPLATE = "template";
    public static final String TEMPLATE_ID = "template.id";
    public static final String TEMPLATE_NEED_EDIT = "template.needEdit";
    public static final String TEMPLATE_RATIO = "template.ratio";
    public static final String TEMPLATE_IS_FROM_MINE = "template.is.from.mine";
    public static final String TEMPLATE_IS_FROM_LOCAL = "template.is.from.local";
    public static final String TEMPLATE_CLIP_DATA = "clip.data";
    public static final String CATEGORY_ID = "category.id";
    public static final String TEMPLATE_CLIP_LIST = "template.clip.list";
    public static final String TEMPLATE_CLIP = "template.clip";
    public static final String TEMPLATE_PATH = "template.path";
    /*------------------------------------------------------结束线 End line---------------------------------------------------------------------------*/

    /*
     * 瀑布流Fragment相关
     * FlowFragment keys
     * */
    public static final String BUNDLE_KEY_ASSET_TYPE = "asset.type";
    public static final String BUNDLE_KEY_ASSET_TYPE_NEW = "asset.type.new";
    public static final String BUNDLE_KEY_ASSET_SBU_TYPE = "asset.type.sub";
    public static final String BUNDLE_KEY_ASSET_TYPE_CATEGORY = "asset.category";
    public static final String BUNDLE_KEY_ASSET_TYPE_KIND = "asset.kind";
    public static final String BUNDLE_KEY_INDEX= "assets.pager.index";
    public static final String BUNDLE_KEY_TRANSITION_TARGET_INDEX= "transition.targetIndex";
    public static final String BUNDLE_KEY_TRANSITION_TYPE= "transition.type";
    /*------------------------------------------------------结束线 End line---------------------------------------------------------------------------*/

    /*
     * 公共页
     * common
     * */
    public static final String COMMON_NEED_GO_MAIN = "common.needGoMain";
    /*------------------------------------------------------结束线 End line---------------------------------------------------------------------------*/
}
