package com.meishe.player.fragment;

import android.app.Activity;
import android.graphics.PointF;
import android.os.Bundle;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.IntDef;

import com.meicam.sdk.NvsLiveWindow;
import com.meicam.sdk.NvsLiveWindowExt;
import com.meicam.sdk.NvsTimeline;
import com.meishe.base.bean.FloatPoint;
import com.meishe.base.constants.Constants;
import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseMvpFragment;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.EngineCallbackManager;
import com.meishe.engine.bean.MeicamFxParam;
import com.meishe.engine.bean.MeicamKeyFrame;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.NvsObject;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.engine.observer.EngineCallbackObserver;
import com.meishe.player.R;
import com.meishe.player.fragment.iview.VideoFragmentView;
import com.meishe.player.fragment.presenter.VideoPresenter;
import com.meishe.player.view.ColorPicker;
import com.meishe.player.view.OperationBox;
import com.meishe.player.view.PipTransformView;
import com.meishe.player.view.bean.OperationBoxInfo;
import com.meishe.player.view.bean.PipTransformInfo;
import com.meishe.player.view.mask.MaskZoomView;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.List;

import static com.meishe.base.constants.Constants.EDIT_MODE_CAPTION;
import static com.meishe.base.constants.Constants.EDIT_MODE_COMPOUND_CAPTION;
import static com.meishe.base.constants.Constants.EDIT_MODE_MASK;
import static com.meishe.base.constants.Constants.EDIT_MODE_NONE;
import static com.meishe.base.constants.Constants.EDIT_MODE_STICKER;
import static com.meishe.base.constants.Constants.EDIT_MODE_VIDEO_CLIP;
import static com.meishe.base.constants.Constants.EDIT_MODE_WATERMARK;
import static com.meishe.base.constants.Constants.EDIT_MODE_WATERMARK_EFFECT;
import static com.meishe.player.fragment.presenter.VideoPresenter.EDIT_MODE_EDIT_TIMELINE;
import static com.meishe.player.view.OperationBox.FIRST_QUADRANT;
import static com.meishe.player.view.OperationBox.FOURTH_QUADRANT;
import static com.meishe.player.view.OperationBox.NONE_QUADRANT;
import static com.meishe.player.view.OperationBox.SECOND_QUADRANT;
import static com.meishe.player.view.OperationBox.THIRD_QUADRANT;

/**
 * 注意：编辑页面专用，其他页面不要使用
 * Note: Edit page only, do not use other pages
 */
public class VideoFragment extends BaseMvpFragment<VideoPresenter> implements VideoFragmentView {
    private static final String INTENT_KEY_MODE = "edit_mode";
    private static final String INTENT_HDR_DISPLAY_MODE = "hdr_display_mode";

    private NvsLiveWindowExt mLiveWindow;
    private FrameLayout mFlPlayerContainer;
    private FrameLayout mFlRootContainer;
    private OperationBox mOperationBox;
    private PipTransformView mPipTransformView;
    private MaskZoomView mMaskZoomView;
    private FxEditListener mAssetEditListener;
    private TouchEventListener mTouchEventListener;
    private ColorPickerChangedListener mColorPickerChangedListener;
    private boolean needSaveOperation = false;
    private ColorPicker mColorPickerView;
    private LifeCycleListener mLifeCycle;
    private int mEditMode = EDIT_MODE_EDIT_TIMELINE;
    private int mHdrDisplayMode = -1;

    public VideoFragment() {
    }

    public static VideoFragment create() {
        return new VideoFragment();
    }

    public static VideoFragment create(int editMode) {
        return create(editMode, -1);
    }

    public static VideoFragment create(int editMode, int hdrDisplayMode) {
        VideoFragment videoFragment = new VideoFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(INTENT_KEY_MODE, editMode);
        bundle.putInt(INTENT_HDR_DISPLAY_MODE, hdrDisplayMode);
        videoFragment.setArguments(bundle);
        return videoFragment;
    }

    /**
     *这里重写，不使用模板自动生成的Presenter了.
     *Rewrite here, do not use the Presenter generated automatically by the template
     * @return The presenter
     */
    @Override
    protected VideoPresenter createPresenter() {
        if (mPresenter == null) {
            mPresenter = new VideoPresenter(EditorEngine.getInstance().getCurrentTimeline(), EditorEngine.getInstance().getStreamingContext());
        }
        if (mLifeCycle != null) {
            mLifeCycle.onPresenterCreate();
        }
        return mPresenter;
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_video;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        mLiveWindow = rootView.findViewById(R.id.liveWindow);
        mLiveWindow.setFillMode(NvsLiveWindow.FILLMODE_PRESERVEASPECTFIT);
        mLiveWindow.setBackgroundColor(0.133f, 0.133f, 0.133f);
        mFlPlayerContainer = rootView.findViewById(R.id.fl_player_container);
        mOperationBox = rootView.findViewById(R.id.draw_rect);
        mPipTransformView = rootView.findViewById(R.id.pip_transform_view);
        mFlRootContainer = rootView.findViewById(R.id.fl_root);
        mMaskZoomView = rootView.findViewById(R.id.mask_zoom_view);
        mColorPickerView = rootView.findViewById(R.id.color_picker);
        setLiveWindowRatio();
        initListener();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EngineCallbackManager.get().unregisterCallbackObserver(mEngineCallBackObserver);
        if (mPresenter != null) {
            mPresenter.onDestroy();
        }
    }

    /**
     * 显示颜色选择器
     * Show color picker.
     */
    public void showColorPicker(){
        if (mColorPickerView.getVisibility() != View.VISIBLE) {
            mColorPickerView.setVisibility(View.VISIBLE);
            if (!mColorPickerView.hasBitmap()) {
                mColorPickerView.setBitmap(mPresenter.getBitmapWithoutMasterKeyer());
                mColorPickerView.setEffectRectF(mPresenter.getVideoRect(mLiveWindow));
                //mColorPickerView.setColorPosition(mPresenter.getColorPosition());
                mColorPickerView.setColor(mPresenter.getColor());
            }
        }
    }

    /**
     * 隐藏颜色选择器
     * Hide color picker.
     */
    public void hideColorPicker(){
        mColorPickerView.setVisibility(View.INVISIBLE);
    }

    /**
     * 释放颜色选择器
     * Release color picker.
     */
    public void releaseColorPicker(){
        hideColorPicker();
        mColorPickerView.release();
    }

    /**
     * 重新设置颜色选择器
     * Reset color picker.
     */
    public void resetColorPicker() {
        mColorPickerView.reset();
    }

    /**
     * 设置播放窗口的比例
     * Set the live window ratio
     */
    public void setLiveWindowRatio() {
        if (mFlRootContainer.getWidth() == 0 && mFlRootContainer.getHeight() == 0) {
            mFlRootContainer.post(new Runnable() {
                @Override
                public void run() {
                    setLiveWindowSize();
                }
            });
        } else {
            setLiveWindowSize();
        }
    }

    /**
     * 设置播放窗口的大小
     * Set the live window size
     */
    private void setLiveWindowSize() {
        EditorEngine.getInstance().setLiveWindrowSize(mLiveWindow);
    }

    private void initListener() {
        EngineCallbackManager.get().registerCallbackObserver(mEngineCallBackObserver);
        mLiveWindow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mTouchEventListener != null) {
                    mTouchEventListener.onLiveWindowClick(mPresenter.getEditMode());
                }
            }
        });
        mPipTransformView.setOnPipTouchListener(new PipTransformView.OnPipTouchEventListener() {
            private boolean mTransformFxChanged;

            @Override
            public void onTouchDown(PointF pointF) {
                if (mPresenter.isPlaying()) {
                    mPresenter.stopVideo();
                }
                mPresenter.tryToAddTempKeyFrame(true);
            }

            @Override
            public void onScaleAndRotate(float scale, float degree) {
                if ((scale == 1.0f) && (degree == -0.0f)) {
                    return;
                }
                mPresenter.changeTransformFxScaleAndRotate(mLiveWindow, scale, degree);
                mTransformFxChanged = true;
            }

            @Override
            public void onDrag(PointF prePointF, PointF nowPointF) {
                if (prePointF.equals(nowPointF.x, nowPointF.y)) {
                    return;
                }
                mPresenter.changeTransformFxPosition(mLiveWindow, prePointF, nowPointF);
                mTransformFxChanged = true;
            }

            @Override
            public void onTouchUp(PointF curPoint) {
                if (mAssetEditListener != null) {
                    if (mTransformFxChanged) {
                        mAssetEditListener.onFxEditEnd(mPresenter.getEditMode());
                    } else {
                        mAssetEditListener.onCheckSelected(curPoint, true);
                    }
                }
                mTransformFxChanged = false;
                mPresenter.removeTempKeyFrame(true);
            }
        });
        mOperationBox.setOperationListener(new OperationBox.OperationListener() {
            private boolean boxChanged;

            @Override
            public boolean onTouchDown(PointF pointF) {
                boxChanged = false;
                if (mTouchEventListener != null) {
                    mTouchEventListener.onTouchBoxDown(pointF, mPresenter.getEditMode());
                }
                if (mPresenter.isPlaying()) {
                    mPresenter.stopVideo();
                }
                mPresenter.tryToAddTempKeyFrame(false);
                return super.onTouchDown(pointF);
            }

            @Override
            public boolean onTouchUp(PointF pointF) {
                if (mPresenter.getEditMode() == EDIT_MODE_WATERMARK) {
                    mPresenter.addWatermark(mLiveWindow);
                }
                if (mAssetEditListener != null) {
                    if (!boxChanged) {
                        mAssetEditListener.onCheckSelected(pointF, false);
                    } else {
                        mAssetEditListener.onFxEditEnd(mPresenter.getEditMode());
                    }
                }
                mPresenter.removeTempKeyFrame(true);
                if (mTouchEventListener != null) {
                    mTouchEventListener.onTouchBoxUp(pointF, mPresenter.getEditMode(), needSaveOperation);
                }
                boxChanged = false;
                needSaveOperation = false;
                return super.onTouchUp(pointF);
            }

            @Override
            public boolean interceptClick(PointF pointF) {
                return super.interceptClick(pointF);
            }

            @Override
            public void onClickOther(boolean insideBox, PointF pointF) {
                if (mTouchEventListener == null || !mOperationBox.isVisible()) {
                    return;
                }
                if (insideBox) {
                    needSaveOperation = true;
                    mTouchEventListener.onClickBox(mOperationBox.getInsideIndex(pointF), mPresenter.getEditMode());
                    if (isFastDoubleClick()) {
                        mTouchEventListener.onDoubleClickBox(mOperationBox.getInsideIndex(pointF), mPresenter.getEditMode());
                    }

                } else {
                    mTouchEventListener.onClickBoxOutside(mPresenter.getEditMode());
                }
            }

            @Override
            public void onClickCorner(@OperationBox.Quadrant int quadrant, PointF pointF) {
                if (mAssetEditListener == null || !mOperationBox.isVisible()) {
                    return;
                }
                needSaveOperation = true;
                if (quadrant == FIRST_QUADRANT) {
                    if (mAssetEditListener != null) {
                        mAssetEditListener.onDelete(mPresenter.getEditMode());
                    }
                    mPresenter.dealFxDelete();
                } else if (quadrant == SECOND_QUADRANT) {
                    if (mPresenter.getEditMode() == EDIT_MODE_CAPTION) {
                        mPresenter.setTextAlignment(mLiveWindow);
                    } else if (mPresenter.getEditMode() == EDIT_MODE_STICKER) {
                        mPresenter.horizontalFlipSticker();
                    }
                    if (mAssetEditListener != null) {
                        mAssetEditListener.onChanged(quadrant, mPresenter.getEditMode());
                    }
                } else if (quadrant == THIRD_QUADRANT) {
                    if (mPresenter.getEditMode() == EDIT_MODE_STICKER) {
                        mPresenter.changeStickerVolume();
                    }
                    if (mAssetEditListener != null) {
                        mAssetEditListener.onChanged(quadrant, mPresenter.getEditMode());
                    }
                } else if (quadrant == FOURTH_QUADRANT) {
                } else if (quadrant == NONE_QUADRANT) {
                }
            }

            @Override
            public void onTranslate(PointF lastPointF, PointF newPointF, PointF centerPointF) {
                if (lastPointF.equals(newPointF.x, newPointF.y)) {
                    return;
                }
                needSaveOperation = true;
                boxChanged = true;
                mPresenter.dealFxDrag(lastPointF, newPointF, mOperationBox.getBoxInfo().getCornerPointList(), mLiveWindow, centerPointF);
                if (mAssetEditListener != null) {
                    mAssetEditListener.onTranslate(mPresenter.getEditMode());
                }
            }

            @Override
            public boolean onScale(float xFactor, float yFactor, PointF centerPointF) {
                needSaveOperation = true;
                if (mPresenter.getEditMode() == EDIT_MODE_WATERMARK_EFFECT) {
                    mPresenter.dealFxScale(xFactor, yFactor, centerPointF, mOperationBox.getBoxInfo().getCornerPointList(), mLiveWindow);
                    return true;
                }
                if (mAssetEditListener != null) {
                    mAssetEditListener.onScale(mPresenter.getEditMode());
                }
                return false;
            }

            @Override
            public void onRotationAndScale(float scaleFactor, float rotate, PointF centerPointF) {
                //注意rotate为Float.MAX_VALUE的时候是无效值
                // Note that rotate is Float.MAX_ Invalid value when valuing.
                if ((scaleFactor == 1.0f) && (rotate == -0.0f)) {
                    return;
                }
                needSaveOperation = true;
                boxChanged = true;
                mPresenter.dealFxScaleAndRotate(scaleFactor, centerPointF, rotate, mOperationBox.getBoxInfo().getCornerPointList(), mLiveWindow);
                if (mAssetEditListener != null) {
                    mAssetEditListener.onRotationAndScale(mPresenter.getEditMode());
                }
            }
        });

        mMaskZoomView.setMaskOperateListener(new MaskZoomView.MaskOperateListener() {
            @Override
            public void onOperate(MeicamVideoClip meicamVideoClip) {
                mPresenter.refreshMask(meicamVideoClip);
            }

            @Override
            public void onOperateStart(MeicamVideoClip meicamVideoClip) {
                mPresenter.tryToAddTempMaskKeyFrame(meicamVideoClip);
            }

            @Override
            public void onOperateEnd() {
                if (mAssetEditListener != null) {
                    mAssetEditListener.onFxEditEnd(mPresenter.getEditMode());
                }
            }
        });
        mColorPickerView.setOnColorChangedListener(new ColorPicker.OnColorChangedListener() {
            @Override
            public void onColorChanged(int a, int r, int g, int b, FloatPoint colorCenter) {
                if (mColorPickerChangedListener != null) {
                    mColorPickerChangedListener.onColorChanged(a, r, g, b, colorCenter);
                }
            }
        });
    }

    @Override
    protected void initData() {
        Bundle arguments = getArguments();
        if (arguments != null) {
            mEditMode = arguments.getInt(INTENT_KEY_MODE);
            mHdrDisplayMode = arguments.getInt(INTENT_HDR_DISPLAY_MODE);
        }
        mPresenter.setEditMode(mEditMode);
        if (mHdrDisplayMode >= 0) {
            mLiveWindow.setHDRDisplayMode(mHdrDisplayMode);
        }
        connectTimelineWithLiveWindow();
    }

    /**
     * 设置特效编辑事件监听
     * Set fx edit event listening
     *
     * @param listener the listener
     */
    public void setFxEditListener(FxEditListener listener) {
        this.mAssetEditListener = listener;
    }

    /**
     * 设置触摸事件监听
     * Set click event listening
     *
     * @param listener the listener
     */
    public void setTouchEventListener(TouchEventListener listener) {
        mTouchEventListener = listener;
    }

    /**
     * 设置颜色选择器监听
     * Sets color picker changed listener.
     *
     * @param listener the listener
     */
    public void setColorPickerChangedListener(ColorPickerChangedListener listener) {
        this.mColorPickerChangedListener = listener;
    }

    public void setLifeCycleListener(LifeCycleListener lifeCycle) {
        this.mLifeCycle = lifeCycle;
    }

    /**
     * 连接时间线和实时预览图像窗口
     * Connect timeline with live window
     */
    public void connectTimelineWithLiveWindow() {
        mPresenter.connectTimeline(mLiveWindow);
    }

    /**
     * 播放
     * Play
     *
     * @param startTime the start position开始时间
     * @param endTime   the end position结束时间
     */
    public void playVideo(long startTime, long endTime) {
        // 播放视频 play video
        mPresenter.playVideo(startTime, endTime);
    }

    /**
     * 播放
     * Play
     *
     * @param startTime the start position开始时间
     * @param endTime   the end position结束时间
     * @param flag      the flag播放标记
     */
    public void playVideo(long startTime, long endTime, int flag) {
        // 播放视频 play video
        mPresenter.playVideo(startTime, endTime, flag);
    }

    /**
     * 预览时间线的某个时间点
     * Seek to the position of timeline
     *
     * @param timestamp    the timestamp
     * @param seekShowMode the seek show mode
     */
    public void seekTimeline(long timestamp, int seekShowMode) {
        mPresenter.seekTimeline(timestamp, seekShowMode);
    }

    /**
     * 停止播放
     * Stop play
     */
    public void stopEngine() {
        mPresenter.stopVideo();
    }

    /**
     * 更新变形特效
     * Update Transform Fx
     *
     * @param videoClip the video clip
     */
    public void updateTransformFx(MeicamVideoClip videoClip) {
        mPresenter.openFxEditMode(Constants.EDIT_MODE_VIDEO_CLIP, videoClip);
    }


    /**
     * 更新变形特效
     * Update Transform Fx
     *
     * @param videoClip         the video clip
     * @param showTransformView boolean .true show transform view ,false not
     */
    public void updateTransformFx(MeicamVideoClip videoClip, boolean showTransformView) {
        if (showTransformView) {
            mFlPlayerContainer.setVisibility(View.VISIBLE);
            mOperationBox.setVisibility(View.GONE);
            mPresenter.openFxEditModeWithShow(Constants.EDIT_MODE_VIDEO_CLIP, videoClip, mLiveWindow);
        } else {
            mFlPlayerContainer.setVisibility(View.GONE);
        }
    }

    /**
     * 设置变形特效拖动视图的可见性
     */
    public void setTransformViewVisible(int visibility) {
        mFlPlayerContainer.setVisibility(visibility);
    }

    /**
     * 变形特效拖动视图是否可见
     * Deformation effect drag view visible
     *
     * @return true visible 可见，false not不可见
     */
    public boolean transforViewVisible() {
        return mFlPlayerContainer.getVisibility() == View.VISIBLE;
    }

    /**
     * 打开特效编辑模式
     * Open fx edit mode
     *
     * @param editMode the edit mode
     * @param nvsFx    the nvsFx
     */
    public void openFxEditMode(@FxEditMode int editMode, NvsObject<?> nvsFx) {
        openFxEditMode(editMode, nvsFx, false);
    }

    /**
     * 打开特效编辑模式
     * Open fx edit mode
     *
     * @param editMode the edit mode
     * @param nvsFx    the nvsFx
     * @param showFx   true show ,false not
     */
    public void openFxEditMode(@FxEditMode int editMode, NvsObject<?> nvsFx, boolean showFx) {
        if (showFx) {
            mOperationBox.setVisibility(View.VISIBLE);
            if (mPresenter != null) {
                mPresenter.openFxEditModeWithShow(editMode, nvsFx, mLiveWindow);
            }
            checkOperationBoxVisible();
        } else {
            if (mPresenter != null) {
                mPresenter.openFxEditMode(editMode, nvsFx);
            }
        }

    }

    /**
     * 打开特mask view 显示
     * Open mask view visible
     *
     * @param meicamVideoClip the meicamVideoClip
     * @param showMaskView    true show ,false not
     */
    public void openMaskZoomEditMode(MeicamVideoClip meicamVideoClip, boolean showMaskView) {
        openMaskZoomEditMode(meicamVideoClip, showMaskView, true);
        if (meicamVideoClip != null && showMaskView && meicamVideoClip.maskModel.maskType != MaskZoomView.MaskType.NONE) {
            mOperationBox.setVisibility(View.GONE);
            mFlPlayerContainer.setVisibility(View.GONE);
            mMaskZoomView.setVisibility(View.VISIBLE);
            openFxEditMode(EDIT_MODE_MASK, meicamVideoClip, true);
            mMaskZoomView.loadMaskModel(meicamVideoClip, mLiveWindow, EditorEngine.getInstance().getVideoResolution(), EditorEngine.getInstance().getNvsTransform(meicamVideoClip), false);
        } else {
            openFxEditMode(EDIT_MODE_VIDEO_CLIP, null, showMaskView);
            if (meicamVideoClip != null) {
                mFlPlayerContainer.setVisibility(View.VISIBLE);
            }
            if (mMaskZoomView != null) {
                mMaskZoomView.setVisibility(View.GONE);
            }
        }
    }

    /**
     * 打开特mask view 显示
     * Open mask view visible
     *
     * @param meicamVideoClip the meicamVideoClip
     * @param showMaskView    true show ,false not
     * @param needCallBack    true need call back ,false not
     */
    public void openMaskZoomEditMode(MeicamVideoClip meicamVideoClip, boolean showMaskView, boolean needCallBack) {
        if (meicamVideoClip != null && showMaskView && meicamVideoClip.maskModel.maskType != MaskZoomView.MaskType.NONE) {
            mOperationBox.setVisibility(View.GONE);
            mFlPlayerContainer.setVisibility(View.GONE);
            mMaskZoomView.setVisibility(View.VISIBLE);
            openFxEditMode(EDIT_MODE_MASK, meicamVideoClip, true);
            mMaskZoomView.loadMaskModel(meicamVideoClip, mLiveWindow, EditorEngine.getInstance().getVideoResolution(), EditorEngine.getInstance().getNvsTransform(meicamVideoClip), needCallBack);
        } else {
            openFxEditMode(EDIT_MODE_VIDEO_CLIP, null, showMaskView);
            if (meicamVideoClip != null) {
                mFlPlayerContainer.setVisibility(View.VISIBLE);
            }
            if (mMaskZoomView != null) {
                mMaskZoomView.setVisibility(View.GONE);
            }
        }
    }

    /**
     * 重置特效编辑模式(视图可见，但是肉眼不可见，可以接收触摸事件)
     * Reset fx edit mode.Views that are visible, but not visible to the naked eye, can receive touch events
     */
    public void resetFxEditMode() {
        mPresenter.openFxEditMode(EDIT_MODE_NONE, null);
        mOperationBox.setVisibility(View.VISIBLE);
        mOperationBox.changeBoxVisibility(false);
    }

    /**
     * 重置VideoClip编辑模式(视图可见，但是肉眼不可见，可以接收触摸事件)
     * Reset VideoClip edit mode.Views that are visible, but not visible to the naked eye, can receive touch events
     */
    public void resetVideoClipEditMode() {
        mFlPlayerContainer.setVisibility(View.VISIBLE);
        updateTransformFx(null);
        mPipTransformView.changeBoxVisibility(false);
    }

    /**
     * 关闭特效编辑模式，视图消失，不再接收触摸事件
     * Close fx edit mode.The view disappears and no longer receives touch events
     */
    public void closeFxEditMode() {
        mPresenter.closeFxEditMode();
        mOperationBox.setVisibility(View.GONE);
    }

    /**
     * 改变贴纸音量
     * change Sticker Volume
     */
    public void changeStickerVolume() {
        if (mPresenter != null) {
            mPresenter.changeStickerVolume();
        }
    }

    /**
     * 更新编辑的特效
     * Update the edit fx
     */
    public void updateEditFx(IBaseInfo baseInfo) {
        mPresenter.updateEditFx(baseInfo, mLiveWindow);
    }

    /**
     * 获取临时关键帧
     * Get temp key frame
     */
    public MeicamKeyFrame getTempKeyFrame() {
        return mPresenter.getTempKeyFrame();
    }

    /**
     * 不移除临时添加的关键帧
     * Not remove temp key frame
     */
    public void notRemoveTempKeyFrame() {
        mPresenter.removeTempKeyFrame(false);
    }

    /**
     * 当存在关键帧的时候去更新操作框
     * Update the operation box when a keyframe is present
     *
     * @param timestamp the timestamp 时间戳
     */
    public void updateOperationBoxWhenHadKeyFrame(long timestamp) {
        if (mOperationBox.isVisible()) {
            mPresenter.updateOperationBoxWhenHadKeyFrame(mLiveWindow, timestamp, false);
        }
    }

    /**
     * 当存在关键帧的时候去更新操作框
     * Update the operation box when a keyframe is present
     *
     * @param timestamp the timestamp 时间戳
     */
    public void updateOperationVideoBounding(long timestamp) {
        if (mPipTransformView.isVisible()) {
            mPresenter.refreshVideoBounding(mLiveWindow, timestamp);
        }
    }

    /**
     * 更新蒙版操作框
     * Update the mask bounding
     *
     * @param timestamp the timestamp 时间戳
     */
    public void updateMaskBounding(long timestamp) {
        if (mMaskZoomView.getVisibility() == View.VISIBLE) {
            NvsObject<?> editFx = mPresenter.getEditFx();
            if (editFx instanceof MeicamVideoClip) {
                mMaskZoomView.loadMaskModel((MeicamVideoClip) editFx, mLiveWindow, EditorEngine.getInstance().getVideoResolution(), EditorEngine.getInstance().getNvsTransform((MeicamVideoClip) editFx), false);
            }
            //mPresenter.refreshMaskBounding(mLiveWindow, timestamp);
        }
    }

    /**
     * 更新编辑的特效的属性
     * Update the property of the edit fx
     */
    public void updateEditFxProperty(MeicamFxParam<?> fxParam) {
        mPresenter.updateEditFxProperty(fxParam);
    }

    public NvsObject<?> getEditFx() {
        return mPresenter.getEditFx();
    }

    public int getEditMode() {
        return mPresenter.getEditMode();
    }

    /**
     * 是否在操作框内部
     * Whether it's inside the action box
     *
     * @return true inside ,false not
     */
    public boolean insideOperationBox(List<PointF> pointFList, int xPos, int yPos) {
        return mOperationBox.insideOperationBox(mPresenter.transformCoordinates(pointFList, mLiveWindow), xPos, yPos);
    }

    /**
     * 是否在VideoClip操作框内部
     * Whether it's inside the action box
     *
     * @return true inside ,false not
     */
    public boolean insideVideoClipOperationBox(int xPos, int yPos) {
        return mPipTransformView.insideOperationBox(xPos, yPos);
    }


    /**
     * 恢复水印点列表
     * Restore water list.
     */
    public void restoreWaterList() {
        mPresenter.restoreWaterList(mLiveWindow);
    }

    /**
     * change ratio add WaterMark
     * 改变比例的时候添加水印
     */
    public void changeRatioAddWaterToTimeline(final int beforeWidth, final int beforeHeight) {
        PointF liveWindrowSize = EditorEngine.getInstance().getLiveWindrowSize(mLiveWindow);
        mPresenter.changeRatioAddWaterToTimeline(mLiveWindow, beforeWidth, beforeHeight, (int) liveWindrowSize.x, (int) liveWindrowSize.y);
    }

    /**
     * 隐藏操作框
     * Hide the operation box
     */
    public void hideOperationBox() {
        mOperationBox.changeBoxVisibility(false);
    }

    /**
     * 隐藏VideoClip操作框
     * Hide the operation box
     */
    public void hideVideoClipOperationBox() {
        mPipTransformView.changeBoxVisibility(false);
    }

    /**
     * 检测画中画操作框的可见性
     * Check the visibility of the action box
     */
    public void checkVideoClipOperationBoxVisible() {
        mPipTransformView.changeBoxVisibility(mPresenter.checkVideoClipOperationBoxVisible());
    }

    /**
     * 检测操作框的可见性
     * Check the visibility of the action box
     */
    public void checkOperationBoxVisible() {
        mOperationBox.changeBoxVisibility(mPresenter.checkOperationBoxVisible());
    }

    /**
     * 操作框是否可见
     * Whether the action box is visible
     *
     * @return true is visible ,false not
     */
    public boolean operationBoxIsVisible() {
        return mOperationBox.isVisible();
    }

    public NvsLiveWindowExt getLiveWindow() {
        return mLiveWindow;
    }

    public int getHeight() {
        return mFlRootContainer.getHeight();
    }


    @Override
    public void onOperationBoxUpdate(OperationBoxInfo info) {
        mOperationBox.update(info);
    }

    @Override
    public void onOperationBoxUpdate(boolean dismiss) {
        if (dismiss) {
            mOperationBox.changeBoxVisibility(false);
        } else {
            mOperationBox.notifyBoxChanged();
        }
    }

    @Override
    public void onPipOperationBoxUpdate(PipTransformInfo info) {
        mPipTransformView.update(info);
    }

    private long lastClickTime;

    private boolean isFastDoubleClick() {
        long time = System.currentTimeMillis();
        long timeNew = time - lastClickTime;
        if (0 < timeNew && timeNew < 500) {
            return true;
        }
        lastClickTime = time;
        return false;
    }

    public void notifyTimelineChanged() {
        mPresenter.setTimeline(EditorEngine.getInstance().getCurrentTimeline());
        connectTimelineWithLiveWindow();
        seekTimeline(EditorEngine.getInstance().getCurrentTimelinePosition(), 0);
    }

    public interface LifeCycleListener {
        /**
         * Presenter 创建时的回调方法
         * On presenter create.
         */
        void onPresenterCreate();
    }


    /**
     * The type Fx edit listener.
     * 操作特效编辑监听
     */
    public static abstract class FxEditListener {
        /**
         * 检查是否有被选中的特效
         * Check if any special effects are selected
         *
         * @param pointF the touch pointF
         */
        public void onCheckSelected(PointF pointF, boolean isVideoClip) {

        }

        /**
         * 删除特效
         * Delete effect
         *
         * @param editMode edit mode
         */
        public void onDelete(@FxEditMode int editMode) {
        }

        /**
         * 特效变动了
         * The special effects have changed.
         *
         * @param quadrant the quadrant index
         * @param editMode edit mode
         */
        public void onChanged(@OperationBox.Quadrant int quadrant, @FxEditMode int editMode) {

        }

        /**
         * 特效平移
         * The special effects translate
         *
         * @param editMode edit mode
         */
        public void onTranslate(@FxEditMode int editMode) {
        }

        /**
         * 特效缩放
         * The special effects scale
         *
         * @param editMode edit mode
         */
        public void onScale(@FxEditMode int editMode) {
        }

        /**
         * 特效旋转和等比缩放
         * The special effects scale and rotation
         *
         * @param editMode edit mode
         */
        public void onRotationAndScale(@FxEditMode int editMode) {
        }

        /**
         * 一次特效编辑结束
         * The end of a special effects edit
         *
         * @param editMode edit mode
         */
        public void onFxEditEnd(@FxEditMode int editMode) {

        }
    }

    public interface ColorPickerChangedListener{
        /**
         * On color changed.
         *
         * @param a the a
         * @param r the r
         * @param g the g
         * @param b the b
         * @param colorPosition the color position
         */
        void onColorChanged(int a, int r, int g, int b, FloatPoint colorPosition);
    }


    /**
     * The type Touch event listener.
     * 操作特效触摸事件
     */
    public static abstract class TouchEventListener {
        /**
         * 播放窗口被点击
         * On live window clicked
         *
         * @param editMode edit mode
         */
        public void onLiveWindowClick(int editMode) {

        }

        /**
         * 点击在操作框外部
         * Click outside the operation box
         *
         * @param editMode edit mode
         */
        public void onClickBoxOutside(int editMode) {

        }

        /**
         * 点击在操作框上
         * Clicked inside the operation box
         *
         * @param index    the index
         * @param editMode edit mode
         */
        public void onClickBox(int index, int editMode) {

        }

        /**
         * 双击击在操作框上
         * Double Clicked inside the operation box
         *
         * @param index    the index
         * @param editMode edit mode
         */
        public void onDoubleClickBox(int index, int editMode) {

        }

        /**
         * 在操作框上按下
         * touch down , the operation box
         *
         * @param pointF   touch down point
         * @param editMode edit mode
         */
        public void onTouchBoxDown(PointF pointF, int editMode) {

        }

        /**
         * 在操作框上抬起
         * touch up  , the operation box
         *
         * @param pointF   touch down point
         * @param editMode edit mode
         */
        public void onTouchBoxUp(PointF pointF, int editMode, boolean needSaveOperation) {

        }
    }

    @IntDef({EDIT_MODE_NONE, EDIT_MODE_CAPTION, EDIT_MODE_STICKER, EDIT_MODE_WATERMARK, EDIT_MODE_WATERMARK_EFFECT,
            EDIT_MODE_COMPOUND_CAPTION, EDIT_MODE_VIDEO_CLIP, EDIT_MODE_MASK})
    @Retention(RetentionPolicy.SOURCE)
    public @interface FxEditMode {
    }

    private EngineCallbackObserver mEngineCallBackObserver = new EngineCallbackObserver() {
        private long lastTimestamp = -1;

        @Override
        public boolean isActive() {
            Activity activity = AppManager.getInstance().currentActivity();
            return activity != null && !activity.isFinishing() && activity.equals(getActivity());
        }

        @Override
        public void onPlaybackTimelinePosition(NvsTimeline nvsTimeline, long timestamp) {
            if (lastTimestamp != timestamp) {
                lastTimestamp = timestamp;
                updateOperationBoxWhenHadKeyFrame(timestamp);
                updateOperationVideoBounding(timestamp);
                updateMaskBounding(timestamp);
            }
        }

        @Override
        public void onSeekingTimelinePosition(NvsTimeline nvsTimeline, long timestamp) {
            if (lastTimestamp != timestamp) {
                lastTimestamp = timestamp;
                updateOperationBoxWhenHadKeyFrame(timestamp);
                updateOperationVideoBounding(timestamp);
                updateMaskBounding(timestamp);
            }
        }
    };
}
