package com.meishe.engine.db;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.meishe.engine.asset.bean.AssetInfo;


/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate :2021/2/8 15:10
 * @Description :网络资源数据库实体类 network db data
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
@Entity
public class AssetEntity {
    /**
     * 主键，资源Id
     * The primary key  , package id
     */
    @PrimaryKey
    @NonNull
    private String id = "123456789";
    /**
     * 名称
     * Name
     */
    private String name;
    /**
     * 类型
     * Type
     */
    private int type = -1;
    /**
     * 资源包Id
     * The package id
     */
    private String packageId;
    /**
     * 资源路径
     * The asset path
     */
    private String assetPath;

    /**
     * 授权路径
     * The lic path
     */
    private String licPath;
    /**
     * 封面路径
     * The cover path
     */
    private String coverPath;
    /**
     * 资源版本号
     * The asset version
     */
    private int version;
    /**
     * 资源支持的比例
     * The asset supported ratio
     */
    private int supportedAspectRatio;

    /**
     * 默认支持的比例
     * Default aspect ratio
     */
    public int defaultAspectRatio;

    /**
     * 资源支持的比例标记
     * The asset supported ratio flag
     */
    private int ratioFlag;
    /**
     * 是 post 动画
     * is Post Package Animation
     */
    private int isPostPackage;

    /**
     * 扩展参数
     * Extended parameter
     */
    private String extended;

    @NonNull
    public String getId() {
        return id;
    }

    public void setId(@NonNull String id) {
        this.id = id;
    }

    public void setPackageId(@NonNull String packageId) {
        this.packageId = packageId;
    }

    @NonNull
    public String getPackageId() {
        return packageId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setAssetPath(String assetPath) {
        this.assetPath = assetPath;
    }

    public String getAssetPath() {
        return assetPath;
    }

    public String getCoverPath() {
        return coverPath;
    }

    public void setCoverPath(String coverPath) {
        this.coverPath = coverPath;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public int getSupportedAspectRatio() {
        return supportedAspectRatio;
    }

    public void setSupportedAspectRatio(int supportedAspectRatio) {
        this.supportedAspectRatio = supportedAspectRatio;
    }

    public int getRatioFlag() {
        return ratioFlag;
    }

    public void setRatioFlag(int ratioFlag) {
        this.ratioFlag = ratioFlag;
    }

    public int getDefaultAspectRatio() {
        return defaultAspectRatio;
    }

    public void setDefaultAspectRatio(int defaultAspectRatio) {
        this.defaultAspectRatio = defaultAspectRatio;
    }

    public int getIsPostPackage() {
        return isPostPackage;
    }

    public void setIsPostPackage(int isPostPackage) {
        this.isPostPackage = isPostPackage;
    }

    public String getExtended() {
        return extended;
    }

    public void setExtended(String extended) {
        this.extended = extended;
    }

    public String getLicPath() {
        return licPath;
    }

    public void setLicPath(String licPath) {
        this.licPath = licPath;
    }

    @Override
    public String toString() {
        return "AssetEntity{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", type=" + type +
                ", packageId='" + packageId + '\'' +
                ", assetPath='" + assetPath + '\'' +
                ", licPath='" + licPath + '\'' +
                ", coverPath='" + coverPath + '\'' +
                ", version=" + version +
                ", supportedAspectRatio=" + supportedAspectRatio +
                ", defaultAspectRatio=" + defaultAspectRatio +
                ", ratioFlag=" + ratioFlag +
                ", isPostPackage=" + isPostPackage +
                ", extended='" + extended + '\'' +
                '}';
    }

    public static AssetEntity create(AssetInfo assetInfo) {
        return new AssetEntity().update(assetInfo);

    }

    public AssetEntity update(AssetInfo assetInfo) {
        setId(assetInfo.getId());
        setName(assetInfo.getName());
        setType(assetInfo.getType());
        setPackageId(assetInfo.getPackageId());
        setVersion(assetInfo.getVersion());
        setSupportedAspectRatio(assetInfo.getSupportedAspectRatio());
        setAssetPath(assetInfo.getAssetPath());
        setCoverPath(assetInfo.getCoverPath());
        setRatioFlag(assetInfo.getRatioFlag());
        setDefaultAspectRatio(assetInfo.getDefaultAspectRatio());
        setIsPostPackage(assetInfo.isPostPackage());
        setExtended(assetInfo.getExtents());
        setLicPath(assetInfo.getLicPath());
        return this;
    }
}
