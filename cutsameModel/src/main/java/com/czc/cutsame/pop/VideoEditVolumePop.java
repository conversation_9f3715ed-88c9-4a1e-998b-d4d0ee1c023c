package com.czc.cutsame.pop;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.SeekBar;

import androidx.annotation.NonNull;

import com.czc.cutsame.R;
import com.meishe.base.constants.Constants;
import com.meishe.base.view.SeekBarTextView;
import com.meishe.third.pop.XPopup;
import com.meishe.third.pop.core.BottomPopupView;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: Chu<PERSON>henGuang
 * @CreateDate: 2021/7/7 18:48
 * @Description:
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class VideoEditVolumePop extends BottomPopupView {

    private VideoVolumeListener mListener;
    private int currProgress = 0;
    private Context mContext;
    public VideoEditVolumePop(@NonNull Context context) {
        super(context);
        mContext = context;
    }

    public static VideoEditVolumePop create(Context context, int progress, VideoVolumeListener listener) {

        return (VideoEditVolumePop) new XPopup.Builder(context)
                .asCustom(new VideoEditVolumePop(context).setVideoVolumeListener(listener, progress));
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.cut_layout_edit_bottom_volume_view;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        SeekBarTextView seekBarTextView = findViewById(R.id.seek_bar_textview);
        ImageView imageConfirm = findViewById(R.id.iv_confirm);
        seekBarTextView.setInitData(Constants.maxVolumeProgress, currProgress);
        seekBarTextView.setProgressTextMargin((int) mContext.getResources().getDimension(R.dimen.dp_px_33));
        seekBarTextView.setProgressTextVisible(currProgress);
        seekBarTextView.setListener(new SeekBarTextView.OnSeekBarListener() {
            @Override
            public void onStopTrackingTouch(int progress) {
                if (mListener != null) {
                    mListener.changeVolume(progress);
                }
            }

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }
        });
        imageConfirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                dismiss();
            }
        });
    }

    /**
     * 设置事件监听
     * Set Video Volume listener
     *
     * @param listener VideoVolumeListener the listener
     */
    public VideoEditVolumePop setVideoVolumeListener(VideoVolumeListener listener, int progress) {
        mListener = listener;
        currProgress = progress;
        return this;
    }

    public interface VideoVolumeListener {
        /**
         * 改变音量
         * change Volume
         */
        void changeVolume(int progress);

    }
}
