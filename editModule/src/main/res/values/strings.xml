<resources>
    <array name="test">
        <item>@mipmap/bank_thumbnail_local</item>
    </array>
    <array name="music_fragment_title">
        <item>@string/music_local_music</item>
        <item>@string/music_my_music</item>
    </array>

    <plurals name="setSelectMedia">
        <item quantity="one">%1$d Asset</item>
        <item quantity="other">%1$d Assets</item>
    </plurals>
    <string name="app_name">MYVideo</string>
    <string name="start_marking">start making</string>
    <string name="cut_edit">cut edit</string>
    <string name="cut_same">cut same</string>
    <string name="single_select_picture">single select picture</string>
    <string name="lack_of_storage">The storage permission is missing. Please open it on the setting page</string>
    <string name="lack_of_record">The record permission is missing. Please open it on the setting page</string>

    <string name="select_media">Select Asset</string>
    <string name="yearMonthDate">yyyy-MM-dd</string>

    <string name="today">Today</string>
    <string name="checkAll">Select</string>

    <string name="cancelCheckAll">Deselect</string>


    <string name="select_production_proportion">Choose making ratio</string>

    <string name="sixteenTNine">16:9</string>
    <string name="nineTSixteen">9:16</string>
    <string name="fourTThree">4:3</string>
    <string name="threeTFour">3:4</string>
    <string name="oneTone">1:1</string>

    <string name="prompt">Tips</string>
    <string name="stayTuned">Stay tuned!</string>
    <string name="cancel_smart_keyer_hint">Is keying in progress, confirm the unkeying effect?</string>
    <string name="cancel_smart_keyer">cancel smart keyer</string>
    <string name="smart_keyer_success">smart keyer success</string>
    <string name="smart_keyer_progress">Intelligent keying %d%%</string>
    <string name="wait_when_smart_eyer">Smart keyer, please hold on</string>
    <string name="not_support_4k">4K and above materials are not allowed for intelligent keying.</string>
    <string name="no_preview_in_keying">No preview in keying.</string>
    <string name="i_know">Got it</string>
    <string name="confirm">Confirm</string>
    <string name="cancel">Cancel</string>
    <string name="contactBusiness">Move to the official website to contact business people</string>
    <string name="select_two_video">Choose 2 materials to make</string>
    <string name="select_the_second_video">Select the 2nd material</string>
    <string name="close_original_voice">Close voice</string>
    <string name="timeline_add_cover">Set \n cover</string>
    <string name="add_voice">Add voice</string>
    <string name="main_menu_name_theme">Theme</string>
    <string name="main_menu_name_add_theme">Add Theme</string>

    <!--导航栏-->
    <string name="nb_empty">empty</string>
    <string name="nb_main0">main0</string>
    <string name="nb_video_edit1">video_edit1</string>
    <string name="nb_picture_edit1">picture_edit1</string>
    <string name="nb_change_speed2">change_speed2</string>
    <string name="nb_animate2">animate2</string>
    <string name="nb_filter1">filter1</string>
    <string name="nb_filter2">filter2</string>
    <string name="nb_adjust2">adjust2</string>
    <string name="nb_effect1">effect1</string>
    <string name="nb_effect2">effect2</string>
    <string name="nb_sticker1">sticker1</string>
    <string name="nb_sticker2">sticker2</string>
    <string name="nb_caption2">caption2</string>
    <string name="nb_combination_caption2">combination_caption2</string>
    <string name="nb_watermark1">watermark1</string>
    <string name="nb_audio1">audio1</string>
    <string name="nb_audio2">audio2</string>
    <string name="nb_pip1">pip1</string>
    <string name="nb_background1">background1</string>
    <string name="nb_ratio1">ratio1</string>


    <string name="main_menu_name_edit">edit</string>
    <string name="main_menu_name_edit_video">edit video</string>
    <string name="main_menu_name_filter">filter</string>
    <string name="main_menu_name_sticker">sticker</string>
    <string name="main_menu_name_caption">caption</string>
    <string name="main_menu_name_com_caption">combined caption</string>
    <string name="main_menu_name_fx">effect</string>
    <string name="main_menu_name_water_mark">watermark</string>
    <string name="main_menu_name_music">audio</string>
    <string name="main_menu_name_dubbing">record</string>
    <string name="main_menu_name_picture_in_picture">PIP</string>
    <string name="main_menu_name_background">background</string>
    <string name="main_menu_name_adjust">adjust</string>
    <string name="main_menu_name_ratio">ratio</string>
    <string name="main_menu_name_transition">transition</string>

    <string name="menu_name_edit_cut">cut</string>
    <string name="menu_name_edit_copy">copy</string>
    <string name="menu_name_main">main menu</string>
    <string name="sub_menu_name_edit_divide">split</string>


    <string name="sub_menu_comb_name_effect">effect</string>

    <string name="sub_menu_name_edit_speed">speed</string>
    <string name="sub_menu_name_edit_volume">volume</string>
    <string name="sub_menu_name_edit_delete">delete</string>
    <string name="sub_menu_name_edit_mask">mask</string>
    <string name="sub_menu_name_edit_mask_convert">convert</string>
    <string name="sub_menu_name_edit_change_voice">tone</string>
    <string name="sub_menu_name_edit_denoise">denoise</string>
    <string name="sub_menu_name_edit_filter">filter</string>
    <string name="sub_menu_name_edit_adjust">adjust</string>
    <string name="sub_menu_name_edit_opacity">opacity</string>
    <string name="sub_menu_name_edit_rotation">rotation</string>
    <string name="sub_menu_name_edit_mirror">mirror</string>
    <string name="sub_menu_name_edit_copy">copy</string>
    <string name="sub_menu_name_edit_reverse">reverse</string>
    <string name="sub_menu_name_edit_freeze_frame">freeze</string>
    <string name="sub_menu_name_edit_animation">animation</string>
    <string name="sub_menu_name_edit_beauty">beauty</string>
    <string name="sub_menu_name_edit_replace">replace</string>
    <string name="sub_menu_name_edit_smart_keyer">smart keyer</string>
    <string name="sub_menu_name_edit_color_picker">color pick</string>
    <string name="sub_menu_name_edit_prop">prop</string>
    <string name="sub_menu_name_edit_cut">cut</string>
    <string name="sub_menu_name_edit_animation_edit">edit/animation</string>
    <string name="sub_menu_name_edit_animation_pip">pip/animation</string>
    <string name="sub_menu_audio_edit">audio edit</string>
    <string name="sub_menu_audio_edit_divide">split</string>
    <string name="sub_menu_audio_edit_speed">speed</string>
    <string name="sub_menu_audio_edit_volume">volume</string>
    <string name="sub_menu_audio_edit_copy">copy</string>
    <string name="sub_menu_audio_edit_delete">delete</string>
    <string name="sub_menu_audio_transition">transition</string>
    <string name="sub_menu_audio_transition_all">audio transition</string>
    <string name="sub_menu_audio_edit_point">point</string>
    <string name="sub_menu_audio_edit_change_voice">tone</string>
    <string name="sub_menu_name_edit_mixed_mode">mixed mode</string>
    <string name="sub_menu_name_edit_change_main_track">chang main track</string>
    <string name="sub_menu_name_edit_change_sub_track">chang sub track</string>
    <string name="sub_menu_video_edit_change_voice">tone</string>
    <string name="open_original_voice">open original voice</string>

    <string name="sub_menu_name_record">record</string>
    <string name="sub_menu_name_analysis">analysis</string>

    <string name="sub_menu_add_adjust">add adjust</string>
    <string name="sub_menu_add_filter">add filter</string>
    <string name="sub_menu_edit_bedspread">bedspread</string>

    <string name="open_denoise">open denoise</string>
    <string name="close_denoise">close denoise</string>


    <string name="apply_all_clip">apply all clip</string>
    <string name="apply_all">apply to all</string>
    <string name="top_menu_general">general</string>
    <string name="top_menu_3D">3D</string>
    <string name="top_menu_no">no</string>


    <string name="ic_add_voice_music">music</string>
    <string name="ic_add_voice_dubbing">record</string>


    <string name="ic_add_voice_extract_music">extract music</string>
    <string name="music_local_music">local music</string>
    <string name="music_my_music">mine music</string>
    <string name="music_default_size">00:00/00:10</string>
    <string name="music_use">use</string>
    <string name="music_select">select music</string>
    <string name="music_play_error">music play error</string>
    <string name="start_record">long press record</string>
    <string name="voice_recording">recording</string>

    <string name="effect_add">add</string>
    <string name="effect_plug_add">customize</string>
    <string name="tab_name_effects">effects</string>
    <string name="tab_name_common">common</string>
    <string name="tab_name_3D">3D</string>
    <string name="tab_name_beauty">beauty</string>
    <string name="tab_name_shape">shape</string>

    <string name="cheek_thinning">Face</string>
    <string name="eye_enlarging">Eyes</string>
    <string name="intensity_forehead">Forehead</string>
    <string name="intensity_chin">Chin</string>
    <string name="intensity_nose">Nose</string>
    <string name="intensity_mouth">Mouth</string>
    <string name="face_small">FacLen</string>
    <string name="face_thin">FacWid</string>
    <string name="nose_long">LonNor</string>
    <string name="eye_corner">EyeCor</string>
    <string name="mouse_corner">MouCor</string>

    <string name="strength">Strength</string>
    <string name="whitening">White</string>
    <string name="ruddy">Rosy</string>
    <string name="correctionColor">Correct</string>
    <string name="sharpness">Sharpness</string>

    <string name="adjust_brightness">brightness</string>
    <string name="adjust_contrast">contrast</string>
    <string name="adjust_saturation">saturation</string>
    <string name="adjust_highlight">highlight</string>
    <string name="adjust_shadow">shadow</string>
    <string name="adjust_temperature">temperature</string>
    <string name="adjust_tint">tint</string>
    <string name="adjust_black_point">fading</string>
    <string name="adjust_degree">degree</string>
    <string name="adjust_amount">amount</string>


    <string name="mixed_mode_normal">normal</string>
    <string name="mixed_mode_darken">darken</string>
    <string name="mixed_mode_screen">screen</string>
    <string name="mixed_mode_superposition">superposition</string>
    <string name="mixed_mode_multiply">multiply</string>
    <string name="mixed_mode_brighten">brighten</string>
    <string name="mixed_mode_bright_light">brightLight</string>
    <string name="mixed_mode_soft_light">softLight</string>
    <string name="mixed_mode_linear_burn">linearBurn</string>
    <string name="mixed_mode_color_burn">colorBurn</string>
    <string name="mixed_mode_color_dodge">colorDodge</string>
    <string name="mixed_mode_minus">minus</string>
    <string name="mixed_mode_exclude">exclude</string>
    <string name="mixed_mode_linear_dodge">linearDodge</string>
    <string name="mixed_mode_light">light</string>
    <string name="mixed_mode_point_light">pointLight</string>
    <string name="mixed_mode_linear_light">linearLight</string>
    <string name="mixed_mode_color_mixture">colorMixture</string>
    <string name="mixed_mode_difference">difference</string>

    <string name="sub_menu_canvas_color">canvas color</string>
    <string name="sub_menu_canvas_style">canvas style</string>
    <string name="sub_menu_canvas_blur">canvas blur</string>

    <string name="sub_menu_replace_filter">replace filter</string>
    <string name="sub_menu_replace_adjust">replace adjust</string>

    <string name="string_format_one_point">"%.1f"</string>

    <string name="draft_box">draft box</string>
    <string name="draft_manage">manager</string>
    <string name="draft_manage_cancel">cancel</string>
    <string name="draft_manage_rename">rename</string>
    <string name="draft_manage_cloud_compile">Compile by Server</string>
    <string name="draft_manager_copy">copy</string>
    <string name="draft_manager_delete">delete</string>
    <string name="draft_manager_upload">upload</string>
    <string name="draft_manager_upload_hint">Please contact the commerce department for uploading!</string>
    <string name="draft_manage_start">start</string>
    <string name="draft_input_draft_name">input draft name</string>
    <string name="draft_delete_confirm">confirm delete?</string>
    <string name="delete">delete</string>
    <string name="draft_confirm">confirm</string>
    <string name="edit_compile_info">Please do not lock the screen or switch to another application</string>
    <string name="saved">compiled</string>

    <string name="sub_pic_in_menu_add_pic">add</string>
    <string name="fragment_menu_table_all">all</string>

    <string name="fragment_menu_table_custom">custom</string>
    <string name="sub_menu_caption_sticker">sticker</string>

    <string name="sub_menu_caption">create caption</string>
    <string name="sub_menu_recognize_caption">recognize caption</string>
    <string name="sub_menu_caption_mould">caption mould</string>
    <string name="sub_menu_caption_compound_caption">compound caption</string>
    <string name="sub_menu_caption_caption">caption</string>

    <string name="menu_tab_caption_input">input</string>
    <string name="menu_tab_caption_style">style</string>
    <string name="menu_tab_caption_flower">flower</string>
    <string name="menu_tab_caption_bubble">bubble</string>
    <string name="menu_tab_caption_animation">animation</string>

    <string name="menu_tab_caption_text">colour</string>

    <string name="menu_tab_caption_contour">contour</string>
    <string name="menu_tab_caption_background">background</string>
    <string name="menu_tab_caption_space">space</string>
    <string name="menu_tab_caption_location">location</string>

    <string name="menu_tab_caption_animation_in">in</string>
    <string name="menu_tab_caption_animation_out">out</string>
    <string name="menu_tab_caption_animation_group">group</string>

    <string name="sub_menu_tab_watermark">watermark</string>
    <string name="sub_menu_tab_watermark_effect">effect</string>
    <string name="menu_tab_watermark_effect_mosaic">mosaic</string>

    <string name="menu_tab_watermark_effect_blur">blur</string>
    <string name="sub_add_voice_music">music</string>

    <string name="menu_sticker_all">all</string>
    <string name="menu_sticker_custom">custom</string>

    <string name="color_opacity">opacity</string>

    <string name="apply_all_caption">apply all caption</string>
    <string name="color_width">width</string>
    <string name="background_opacity">opacity</string>
    <string name="background_corner">corner</string>
    <string name="more">more</string>
    <string name="no">no</string>
    <string name="add">add</string>
    <string name="fragment_menu_water">water</string>
    <string name="fragment_menu_water_effect">effect</string>
    <string name="effect_mosaic">mosaic</string>


    <string name="effect_blur">blur</string>
    <string name="change_caption_content">Click the dotted box to modify the caption</string>


    <string name="sub_menu_pic_in_pic">PIP</string>
    <string name="captureResolution">Resolution</string>

    <string name="int1080">1080p</string>
    <string name="int720">720p</string>
    <string name="int360">360p</string>
    <string name="int480">480p</string>
    <string name="int4K">4K</string>

    <string name="frame_rate_15">15</string>
    <string name="frame_rate_24">24</string>
    <string name="frame_rate_25">25</string>
    <string name="frame_rate_30">30</string>
    <string name="frame_rate_50">50</string>
    <string name="frame_rate_60">60</string>
    <!--    设置-->
    <string name="setting_auto_append">auto append</string>
    <string name="setting_hdr_import">HDR import set</string>
    <string name="setting_hdr_preview_mode">HDR preview mode</string>
    <string name="setting_hdr_bit_depth">Bit depth</string>
    <string name="setting_hdr_sdr_to_hdr_color_gain">SDR To HDR color gain</string>
    <string name="setting_feed_back">feed back</string>
    <string name="setting_version_code">version code</string>
    <string name="setting_SDK_version_code">SDK version</string>
    <string name="setting_privacy_policy">privacy policy</string>
    <string name="setting_user_agreements">user agreements</string>
    <string name="setting_hdr_preview_mode_sdr">SDR</string>
    <string name="setting_hdr_preview_mode_device">device</string>
    <string name="setting_hdr_preview_mode_auto">AutoSDR</string>
    <string name="setting_hdr_bit_depth_8">8bit</string>
    <string name="setting_hdr_bit_depth_16">16bit</string>
    <string name="setting_hdr_bit_depth_auto">Auto</string>

    <string name="feedback_ques_desc">ques desc</string>
    <string name="ssl_error_prompt">The SSL certificate failed, whether to continue to visit?</string>
    <string name="free">free</string>
    <string name="circle">circle</string>
    <string name="square">square</string>
    <string name="cancel_theme_change_ratio">You need to cancel the theme to change the scale</string>
    <string name="delete_pip_add_theme">Delete the picture-in-picture track to add the theme effect</string>
    <string name="add_pip_cancel_theme">Adding a picture-in-picture track cancels the theme effect</string>
    <string name="got_it">Got it</string>
    <string name="compound_caption_title_style">style</string>


    //////////////////////Download////////////////////////////////
    <string name="moreTheme">More Themes</string>
    <string name="moreFilter">More Filters</string>
    <string name="moreAnimatedSticker">More Stickers</string>
    <string name="moreCaptionStyle">More Caption Styles</string>
    <string name="moreTransition">More Transitions</string>
    <string name="moreFaceU">More Props</string>
    <string name="moreCustomStickerEffect">More Custom Effects</string>
    <string name="moreCompoundCaptionStyle">More CompoundCaption Styles</string>
    <string name="moreFrameEffect">More Frame Effects</string>
    <string name="moreDreamEffect">More Dream Effects</string>
    <string name="moreLivelyEffect">More Lively Effects</string>
    <string name="moreShakingEffect">More Shaking Effects</string>
    <string name="moreTransitionCommon">More common transition</string>
    <string name="moreTransition3D">More 3d transition</string>
    <string name="moreTransitionEffect">More effect transition</string>

    <string name="asset_loading">Loading…</string>
    <string name="asset_loadfailed">Failed to load!</string>
    <string name="retry">Retry</string>
    <string name="asset_ratio">General</string>
    <string name="asset_mismatch">Mismatch</string>
    <string name="asset_download">Download</string>
    <string name="asset_downloadfinished">Finished</string>
    <string name="asset_update">Update</string>
    <string name="loading">Loading...</string>
    <string name="network_strayed_try">The network is running out, please try again</string>
    <string name="download_asset_ratio">asset ratio ：</string>
    <string name="download_asset_size">asset size ：</string>
    <string name="caption_font_bold">bold</string>
    <string name="font_italics">italics</string>
    <string name="font_shadow">shadow</string>

    <string name="letter_space_standard">standard</string>
    <string name="letter_space_more">more</string>
    <string name="letter_space_big">big</string>

    <string name="adjust_reset">reset</string>
    <string name="fade_in_time">fade in</string>
    <string name="fade_out_time">fade out</string>

    <string name="beauty_buffing">buffing</string>
    <string name="beauty_whitening">whitening</string>
    <string name="beauty_ruddy">ruddy</string>

    <string name="fragment_menu_beauty">beauty</string>
    <string name="fragment_menu_beauty_type">shape</string>

    <!--///////////////Transition//////////////-->
    <string name="trans_time">Transition time</string>
    <string name="trans_fade">Fade</string>
    <string name="trans_turning">Turning</string>
    <string name="trans_swap">Swap</string>

    <!--Cut To -->
    <string name="trans_stretch_in">Stretch In</string>
    <string name="trans_page_curl">Page Curl</string>
    <string name="trans_lens_flare">Lens Flare</string>
    <string name="trans_star">Star</string>
    <string name="trans_dip_to_black">Dip To Black</string>
    <string name="trans_dip_to_white">Dip To White</string>
    <string name="trans_push_to_right">Push To Right</string>
    <string name="trans_push_to_left">Push To Top</string>
    <string name="trans_upper_left_into">Upper Left Into</string>
    <string name="toast_can_not_add_effect">One effect is in this position</string>
    <string name="toast_least_one_material">The orbit retains at least one material</string>
    <string name="sub_menu_caption_edit">caption edit</string>

    <string name="sub_menu_caption_edit_copy">copy</string>
    <string name="sub_menu_caption_edit_delete">delete</string>

    <string name="sub_menu_caption_edit_style">style</string>
    <string name="sub_menu_sticker_edit">sticker edit</string>
    <string name="sub_menu_sticker_edit_copy">copy</string>
    <string name="sub_menu_sticker_edit_delete">delete</string>
    <string name="sub_menu_caption_edit_read">read caption</string>

    <string name="sub_menu_sticker_edit_mirror">mirror</string>
    <string name="sub_menu_sticker_edit_voice">voice</string>
    <string name="sub_menu_compound_caption_edit">compound caption edit</string>
    <string name="sub_menu_compound_caption_edit_copy">copy</string>
    <string name="sub_menu_compound_caption_edit_delete">delete</string>


    <string name="sub_menu_effect_edit">effect edit</string>
    <string name="sub_menu_effect_edit_copy">edit</string>
    <string name="sub_menu_effect_edit_target">target</string>
    <string name="sub_menu_effect_edit_delete">delete</string>
    <string name="sub_menu_effect_edit_replace">replace</string>

    <string name="sub_menu_pip_edit">PIP</string>
    <string name="title_edit_not_opacity">not opacity</string>
    <string name="audio_num">audio num</string>
    <string name="adjust_num">adjust num</string>

    <string name="compile_cancel">Canceled</string>


    <string name="current_position_not_allow_cut">current position not allow cut</string>
    <string name="unusable_space">Unusable space</string>
    <string name="audio_unable_cut">audio unable cut</string>

    <string name="audio_max_track">audio max track %d</string>
    <string name="sub_menu_pip_edit_video">picture in picture edit video</string>
    <string name="revert_finish">revert finish</string>
    <string name="revert_cancel">cancel revert</string>
    <string name="revert_fail">revert fail</string>


    <string name="reverting">reverting</string>

    <!--revert-->
    <string name="file_size">File Size is about</string>
    <string name="video_compiling">video compiling</string>
    <string name="current_position_not_allow_freeze">current position not allow freeze</string>

    <string name="noneMusic">none music</string>
    <string name="has_been_apply_to_all">Has been apply to all</string>

    <string name="draft_update_time">update time </string>
    <string name="feed_back_desc">describe question</string>
    <string name="feed_back_contact_way">contact way（QQ、wechat、phone）</string>
    <string name="feed_back_input_contact_way">input contact way</string>
    <string name="feed_back_submit">submit</string>
    <string name="privacy_statement">Agreement and policy</string>
    <string name="not_used">
Not agree
</string>
    <string name="agree">Agree</string>

    <string name="statement_content">Please carefully read and fully understand the terms and conditions of service agreement and privacy policy. Meitu only provides video editing and shooting services, and will not collect your personal information.\n You can read the \"Service Agreement\" and \"Privacy Policy\" for details. If you agree, please click agree to accept our service.</string>
    <string name="service_agreement">Service Agreement</string>
    <string name="privacy_policy">Privacy Policy</string>
    <string name="draft_upload">upload</string>
    <string name="draft_already_upload">already upload</string>
    <string name="dialog_account_number">phone:</string>
    <string name="dialog_account_pwd">password:</string>
    <string name="dialog_hint_input_phone">input phone</string>
    <string name="dialog_hint_input_pwd">Input password</string>

    <string name="upload_input_dialog_fail">no find %s</string>
    <string name="upload_toast_upload_fail">upload fail</string>
    <string name="customStickerselecteffect">Select actions</string>
    <string name="level">effect level</string>
    <string name="number">effect num</string>

    <string name="upload_fail">upload fail</string>
    <string name="sub_menu_animation_in">In animation</string>
    <string name="sub_menu_animation_out">Out animation</string>
    <string name="sub_menu_animation_group">Group animation</string>

    <!-- animation-->
    <string name="animation_duration">Duration</string>
    <string name="tv_sticker_make">confirm</string>
    <string name="fragment_menu_table_sticker_effect">sticker effect</string>
    <string name="menu_sub_tab_change_speed_practice">practice speed</string>
    <string name="menu_sub_tab_change_speed_curve">curve speed</string>
    <string name="original">original</string>
    <string name="customize">customize</string>
    <string name="tv_point_add">add point</string>
    <string name="tv_point_remove">remove point</string>
    <string name="tv_reset">reset</string>
    <string name="tv_custom">custom</string>
    <string name="tv_from_time_format">during%ss</string>
    <string name="video_change_voice">speed and tone</string>
    <string name="compile_out">confirm compile</string>
    <string name="video_frame_rate">frame rate</string>
    <string name="video_resolution">resolution</string>
    <string name="video_frame_rate_info">higher the frame rate, higher the smoothness</string>
    <string name="video_resolution_info">higher the resolution, clearer the video</string>
    <string name="compile_out_failed">compile_out_failed</string>
    <string name="compile_hdr_export_setting">hdr export setting</string>
    <string name="compile_hdr_export_switch_to_HEVC">switch to HEVC</string>
    <string name="compile_hdr_export_config">export config</string>
    <string name="compile_hdr_export_config_none">None</string>
    <string name="compile_hdr_export_config_st2084">St2084</string>
    <string name="compile_hdr_export_config_hlg">Hlg</string>
    <string name="compile_hdr_export_config_hdr10plus">Hdr10plus</string>

    <string name="sub_menu_edit_mask_none">None</string>
    <string name="sub_menu_edit_mask_line">Line</string>
    <string name="sub_menu_edit_mask_mirror">Mirror</string>

    <string name="sub_menu_edit_mask_circle">Circle</string>
    <string name="sub_menu_edit_mask_rect">Rect</string>
    <string name="sub_menu_edit_mask_heart">Heart</string>
    <string name="sub_menu_edit_mask_star">Star</string>
    <string name="tv_text_margin">text space</string>
    <string name="tv_raw_margin">raw space</string>
    <string name="format_1f">%1f</string>
    <string name="tv_confim_rest">confim reset</string>
    <string name="init_arsence_error">init arscene error,please wait</string>
    <string name="network_not_available">network not available</string>
    <string name="audio_compilation">audio compilation</string>

    <string name="export_content">export</string>
    <string name="export_template">export template</string>
    <string name="package_size_m">%.1f</string>
    <string name="package_size_kb">%d</string>
    <string name="animation_view_during">%.1fs</string>
    <string name="simple_data_format_yy_mm_dd_hh_ss">yyyy-MM-dd-HH-mm-ss</string>
    <string name="fragment_beauty_tv_start">0.0</string>
    <string name="fragment_beauty_tv_end">1</string>
    <string name="fragment_beauty_list_start">-1.0</string>
    <string name="fragment_beauty_list_end">1</string>
    <string name="fragment_beauty_shape_close">close shape</string>
    <string name="fragment_beauty_shape_open">open shape</string>
    <string name="caption_style_default">default</string>
    <string name="caption_style_true_type">Imitating Song</string>
    <string name="caption_style_book_song_typeface">Simplified Song</string>
    <string name="caption_style_zhu_shi_ti">Bamboo stone</string>
    <string name="caption_style_wen_yi_ti">Literature art</string>
    <string name="font">font</string>
    <string name="cut_music_time">00:00</string>
    <string name="track_view_add_flag">+</string>
    <string name="track_view_clip_tag_main">M</string>
    <string name="track_view_clip_tag_pip">P</string>
    <string name="track_view_clip_tag_all">A</string>
    <string name="text_zero">0</string>
    <string name="text_one">1</string>
    <string name="item_speed_curve_click_edit">click edit</string>
    <string name="view_menu_beauty">beauty</string>
    <string name="view_menu_beauty_makeup">makeup</string>
    <string name="ratio_original">original</string>
    <string name="ratio_9_16">9:16</string>
    <string name="ratio_3_4">3:4</string>
    <string name="ratio_1_1">1:1</string>
    <string name="ratio_4_3">4:3</string>
    <string name="ratio_16_9">16:9</string>
    <string name="fragment_caption_animation_fast">fast</string>
    <string name="fragment_caption_animation_slow">slow</string>
    <string name="max_track_pip">max num %d</string>
    <string name="identification_caption">identify caption</string>
    <string name="identifying_caption">Identifying Caption &#8230;</string>
    <string name="not_identify_voices">not identify voices</string>
    <string name="identify_success_turn_to_caption">identify success,turn to caption</string>
    <string name="identify_net_not_available">Identification failed, please check the network</string>
    <string name="clear_caption">Empty existing caption</string>
    <string name="start_identify">Start identify</string>
    <string name="menu_tab_user_all">Public</string>
    <string name="menu_tab_user_purchased">Purchased</string>
    <string name="menu_tab_user_custom">Custom</string>

    <string name="asset_purchased">Purchased</string>
    <string name="user_login">Login</string>
    <string name="error_draft_data_is_error">数据错误</string>
    <string name="error_clip_file_is_invalid">HDR material editing is not supported at present</string>
    <string name="add_transition_tip">The time field is too short to add transitions</string>

    <string name="key_frame_curve_custom">Custom</string>
    <string name="key_frame_curve_tab_name">Key Frame Curve</string>

    <string name="convert_failed">Import failed, please try again</string>
    <string name="convert_music_failed">Music extraction failed</string>

    <string name="tab_name_sticker_animation">sticker animation</string>
    <string name="tab_name_video_animation">video animation</string>
    <string name="item_hint_custom_water">custom</string>


    <string name="tab_effect_1">Basic</string>
    <string name="tab_effect_2">Atmosphere</string>
    <string name="tab_effect_3">Bling</string>
    <string name="tab_effect_4">Lively</string>
    <string name="tab_effect_5">Nature</string>
    <string name="tab_effect_6">Shadow</string>
    <string name="tab_effect_7">Retro</string>
    <string name="tab_effect_8">Split</string>
    <string name="tab_effect_9">Border</string>
    <string name="tab_effect_10">Comics</string>
    <string name="tab_effect_11">Texture</string>

    <string name="effect_lively">lively</string>
    <string name="effect_shaking">shaking</string>
    <string name="effect_dream">dream</string>

    <string name="effect_frame">frame</string>
    <string name="tab_sticker_1">Normal</string>
    <string name="tab_sticker_2">Audio</string>
    <string name="tab_sticker_3D">3D</string>

    <string name="tab_sticker_effects">Effect</string>
    <string name="tab_transition_2D">2D</string>

    <string name="tab_transition_3D">3D</string>
    <string name="tab_prop_1">2D</string>

    <string name="tab_prop_2">3D</string>
    <string name="tab_prop_3">spilt</string>

    <string name="tab_prop_4">foreground</string>
    <string name="tab_prop_5">particle</string>
    <string name="tab_draft_1">Local draft</string>

    <string name="identify_caption_pop_hint_top">Automatic caption recognition</string>
    <string name="identify_caption_pop_selector_video">Video</string>
    <string name="identify_caption_pop_selector_record">Record</string>
    <string name="identify_caption_pop_selector_all">All</string>
    <string name="hint_main_draft_no_draft">No draft</string>
    <string name="hint_main_draft_start_create">Start create my work</string>
    <string name="title_plug">plug effect</string>

    <string name="tv_add_plug_desc">点击右侧”+“，添加自定义特效</string>
    <string name="plug_menu_delete">plug_delete</string>
    <string name="plug_menu_hide">plug_hide</string>
    <string name="plug_menu_copy">plug_copy</string>
    <string name="plug_menu_bedspread">plug_bedspread</string>
    <string name="plug_visible">visible</string>
    <string name="plug_hide">invisible</string>
    <string name="error_video_duration_short">The video clip is too short</string>
    <string name="selected_material_num_hint">select %d clips</string>
    <string name="select_material_content">select content</string>
    <string name="select_material_next">next</string>
    <string name="nothing">Not yet</string>
    <string name="imported">imported</string>
    <string name="lack_of_authority">Lack of necessary authority,Please open it on the setting page</string>
    <string name="video_clip_replace_progress">Processing %d%%</string>
    <string name="video_clip_replace_failed">Replace failed, please try again</string>
    <string name="cover_edit_reset">reset</string>
    <string name="cover_edit_save">save</string>
    <string name="cover_edit_replace_icon_hint">Click replace</string>
    <string name="cover_edit_select_frame_hint">Slide left and right to select the cover</string>
    <string-array name="select_media">
        <item>All</item>
        <item>Video</item>
        <item>Image</item>
        <item>Business</item>
    </string-array>
    <string-array name="select_image">
        <item>Image</item>
    </string-array>
    <string-array name="permissions_tips">
        <item>@string/prompt</item>
        <item>Please open the relevant permissions (camera, microphone, storage)</item>
    </string-array>

    <string name="tab_cover_edit_frame">Frame</string>
    <string name="tab_cover_edit_import">Import</string>

    <string name="menu_color_pick_color_picker">Color picker</string>
    <string name="menu_color_pick_overflow_removal">Overflow removal</string>
    <string name="menu_color_pick_edge_shrinkage">Edge shrinkage</string>
    <string name="menu_color_strength">Strength</string>

    <string name="menu_effect_target_main_clip">main</string>
    <string name="menu_effect_target_pip_clip">pip</string>
    <string name="menu_effect_target_all">all</string>

    <!-- Media Selector Activity -->
    <string name="close">Close</string>
    <string name="camera">Camera</string>
    <string name="phone">Phone</string>
</resources>
