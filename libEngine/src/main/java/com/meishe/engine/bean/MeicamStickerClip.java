package com.meishe.engine.bean;

import android.graphics.PointF;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsObject;
import com.meicam.sdk.NvsTimelineAnimatedSticker;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.adapter.parser.IResourceParser;
import com.meishe.engine.bean.bridges.FileInfoBridge;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.interf.IKeyFrameProcessor;
import com.meishe.engine.local.LMeicamKeyFrame;
import com.meishe.engine.local.LMeicamStickerClip;
import com.meishe.engine.util.DeepCopyUtil;
import com.meishe.engine.util.gson.GsonContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: ChuChenGuang
 * @CreateDate: 2021/5/20 10:30
 * @Description: 贴纸 The Sticker
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class MeicamStickerClip extends ClipInfo<NvsTimelineAnimatedSticker> implements Cloneable, TimelineDataParserAdapter<LMeicamStickerClip>, IResourceParser, IKeyFrameProcessor<NvsTimelineAnimatedSticker> {
    /**
     * Sticker type
     * 贴纸类型
     */
    private String stickerType = "general";
    /**
     * ID
     * 包id
     */
    private String packageId;
    /**
     * scale
     * 缩放比例
     */
    private float scale = 1.0F;
    /**
     * Rotation Angle
     * 旋转角度
     */
    private float rotation;
    /**
     * Horizontal translation
     * 水平方向平移
     */
    private float translationX;
    /**
     * vertical translation
     * 竖直方向平移
     */
    private float translationY;
    /**
     * The horizontal flip of the animation sticker is true for the horizontal flip and false for the non-flip
     * 动画贴纸的水平翻转 true表示水平翻转，false则不翻转
     */
    private boolean horizontalFlip = false;
    /**
     * Flip it vertically.True means flipping vertically, false means not flipping
     * 竖直翻转。true表示竖直翻转，false则不翻转
     */
    private boolean verticalFlip = false;
    /**
     * name
     * 名称
     */
    private String displayName;
    /**
     * en name
     * 英文名称
     */
    private String displayNameEN;
    /**
     * LeftVolumeGain Left channel volume gain, value range [0,1]
     * leftVolumeGain	左声道音量增益,取值范围[0,1]
     */
    private float leftVolume = 1.0f;
    /**
     * Is it a sound sticker, true sound, false silent
     * 是否是有声贴纸，true有声，false无声
     */
    private boolean hasAudio;
    /**
     * Cover image path
     * 封面图片路径
     */
    private String coverImagePath;

    /**
     * Is it a custom sticker? True yes, false no
     * 是否是自定义贴纸，true 是，false 不是
     */

    private boolean isCustomSticker;
    /**
     * The custom animated sticker image path
     * 自定义贴纸图片路径
     */
    private String customAnimatedStickerImagePath;

    private String resourceId;

    /**
     * The data of sticker animations
     * 贴纸动画数据
     */
    private final Map<String, StickerAnimation> animationMap = new HashMap<>();

    /**
     * 缩放锚点，是临时数据
     */
    private transient PointF anchorForScale;


    MeicamStickerClip(NvsTimelineAnimatedSticker sticker, long inPoint, long outPoint, String packageId, boolean isCustomSticker, String customAnimatedStickerImagePath) {
        super(sticker, CommonData.CLIP_STICKER);
        setInPoint(inPoint);
        setOutPoint(outPoint);
        this.packageId = packageId;
        this.isCustomSticker = isCustomSticker;
        this.customAnimatedStickerImagePath = customAnimatedStickerImagePath;
    }

    public String getStickerType() {
        return stickerType;
    }

    public void setStickerType(String stickerType) {
        this.stickerType = stickerType;
    }

    public String getPackageId() {
        return packageId;
    }

    public void setPackageId(String packageId) {
        this.packageId = packageId;
    }

    public float getScale() {
        return scale;
    }

    public void setScale(float scale) {
        if (invalidFloat(scale)) {
            return;
        }
        NvsTimelineAnimatedSticker object = getObject();
        if (object != null) {
            object.setScale(scale);
            this.scale = scale;
        }
    }

    public float getRotation() {
        return rotation;
    }

    public void setRotation(float rotation) {
        if (invalidFloat(rotation)) {
            return;
        }
        NvsTimelineAnimatedSticker object = getObject();
        if (object != null) {
            object.setRotationZ(rotation);
            this.rotation = rotation;
        }
    }

    public float getTranslationX() {
        return translationX;
    }

    public void setTranslationX(float translationX) {
        if (invalidFloat(translationX)) {
            return;
        }
        NvsTimelineAnimatedSticker object = getObject();
        if (object != null) {
            object.setTranslation(new PointF(translationX, getTranslationY()));
            this.translationX = translationX;
        }
    }

    public float getTranslationY() {
        return translationY;
    }

    public void setTranslationY(float translationY) {
        if (invalidFloat(translationY)) {
            return;
        }
        NvsTimelineAnimatedSticker object = getObject();
        if (object != null) {
            object.setTranslation(new PointF(getTranslationX(), translationY));
            this.translationY = translationY;
        }
    }

    public void translateAnimatedSticker(PointF translation) {
        if (translation == null || invalidFloat(translation.x) || invalidFloat(translation.y)) {
            return;
        }
        NvsTimelineAnimatedSticker object = getObject();
        if (object != null) {
            object.translateAnimatedSticker(translation);
            this.translationX += translation.x;
            this.translationY += translation.y;
        }
    }

    public boolean isHorizontalFlip() {
        return horizontalFlip;
    }

    public void setHorizontalFlip(boolean horizontalFlip) {
        NvsTimelineAnimatedSticker object = getObject();
        if (object != null) {
            object.setHorizontalFlip(horizontalFlip);
            this.horizontalFlip = horizontalFlip;
        }
    }

    public boolean isVerticalFlip() {
        return verticalFlip;
    }

    public void setVerticalFlip(boolean verticalFlip) {
        NvsTimelineAnimatedSticker object = getObject();
        if (object != null) {
            object.setVerticalFlip(verticalFlip);
            this.verticalFlip = verticalFlip;
        }
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayNameEN() {
        return displayNameEN;
    }

    public void setDisplayNameEN(String displayNameEN) {
        this.displayNameEN = displayNameEN;
    }

    public float getLeftVolume() {
        return leftVolume;
    }

    public void setLeftVolume(float leftVolume) {
        if (invalidFloat(leftVolume)) {
            return;
        }
        NvsTimelineAnimatedSticker object = getObject();
        if (object != null) {
            object.setVolumeGain(leftVolume, leftVolume);
            this.leftVolume = leftVolume;
        }
    }

    public boolean isHasAudio() {
        NvsTimelineAnimatedSticker object = getObject();
        if (object != null) {
            hasAudio = object.hasAudio();
        }
        return hasAudio;
    }

    public void setHasAudio(boolean hasAudio) {
        this.hasAudio = hasAudio;
    }

    public void setIsCustomSticker(boolean mIsCustomSticker) {
        this.isCustomSticker = mIsCustomSticker;
    }

    public String getCoverImagePath() {
        return coverImagePath;
    }

    public void setCoverImagePath(String coverImagePath) {
        this.coverImagePath = coverImagePath;
    }

    @Override
    public float getZValue() {
        NvsTimelineAnimatedSticker object = getObject();
        if (object != null) {
            return object.getZValue();
        }
        return super.getZValue();
    }

    @Override
    public void setZValue(float zValue) {
        if (invalidFloat(zValue)) {
            return;
        }
        NvsTimelineAnimatedSticker object = getObject();
        if (object != null) {
            object.setZValue(zValue);
            super.setZValue(zValue);
        }
    }

    @Override
    public void setInPoint(long inPoint) {
        NvsTimelineAnimatedSticker object = getObject();
        if (object != null) {
            object.changeInPoint(inPoint);
            this.inPoint = inPoint;
        }
    }

    @Override
    public void setOutPoint(long outPoint) {
        NvsTimelineAnimatedSticker object = getObject();
        if (object != null) {
            object.changeOutPoint(outPoint);
            this.outPoint = outPoint;
        }
    }

    /**
     * Gets the vertex position of the original envelope of the animation sticker in the timeline coordinate system
     * Return List&lt;PointF&gt;Object containing four vertex positions corresponding to the top left, bottom left,
     * bottom right, and top right vertices of the original envelope
     * <p>
     * 获取动画贴纸在时间线坐标系下原始包络框的顶点位置
     * 返回List<PointF>对象，包含四个顶点位置，分别对应原始包络框的左上，左下，右下，右上顶点
     *
     * @return List<PointF>对象，包含四个顶点位置，分别对应原始包络框的左上，左下，右下，右上顶点
     * Return List&lt;PointF&gt;Object containing four vertex positions corresponding to the top left, bottom left,
     * bottom right, and top right vertices of the original envelope
     */
    public List<PointF> getBoundingRectangleVertices() {
        NvsTimelineAnimatedSticker object = getObject();
        if (object != null) {
            return object.getBoundingRectangleVertices();
        }
        return null;
    }

    /**
     * 贴纸缩放
     * Scale animated sticker.
     *
     * @param scaleFactor the scale factor
     * @param assetAnchor the asset anchor
     */
    public void scaleAnimatedSticker(float scaleFactor, PointF assetAnchor) {
        NvsTimelineAnimatedSticker object = getObject();
        if (object != null) {
            object.scaleAnimatedSticker(scaleFactor, assetAnchor);
            this.anchorForScale = assetAnchor;
            scale = object.getScale();
        }
    }

    public PointF getAnchorForScale() {
        return anchorForScale;
    }

    /**
     * 贴纸旋转
     * Rotate animated sticker.
     *
     * @param angle the angle
     */
    public void rotateAnimatedSticker(float angle) {
        NvsTimelineAnimatedSticker object = getObject();
        if (object != null) {
            object.rotateAnimatedSticker(angle);
            rotation = object.getRotationZ();
        }
    }

    /**
     * Set animation.
     * 设置动画
     *
     * @param packageId 动画包id the package id
     * @param type      动画类型, 见StickerAnimation.TYPE_ANIMATION_IN, StickerAnimation.TYPE_ANIMATION_OUT 和 StickerAnimation.TYPE_ANIMATION_COMP
     *                  the type {@code StickerAnimation.TYPE_ANIMATION_IN, StickerAnimation.TYPE_ANIMATION_OUT and StickerAnimation.TYPE_ANIMATION_COMP }
     * @param duration  动画时长 the duration
     */
    public void setAnimation(String packageId, String type, long duration) {
        NvsTimelineAnimatedSticker object = getObject();
        if (object == null) {
            return;
        }
        if (StickerAnimation.TYPE_ANIMATION_IN.equals(type)) {
            object.applyAnimatedStickerInAnimation(packageId);
            object.setAnimatedStickerInAnimationDuration((int) duration);
            object.applyAnimatedStickerPeriodAnimation("");
            animationMap.remove(StickerAnimation.TYPE_ANIMATION_COMP);
        } else if (StickerAnimation.TYPE_ANIMATION_OUT.equals(type)) {
            object.applyAnimatedStickerOutAnimation(packageId);
            object.applyAnimatedStickerPeriodAnimation("");
            animationMap.remove(StickerAnimation.TYPE_ANIMATION_COMP);
            object.setAnimatedStickerOutAnimationDuration((int) duration);
        } else if (StickerAnimation.TYPE_ANIMATION_COMP.equals(type)) {
            object.applyAnimatedStickerOutAnimation("");
            object.applyAnimatedStickerInAnimation("");
            animationMap.remove(StickerAnimation.TYPE_ANIMATION_IN);
            animationMap.remove(StickerAnimation.TYPE_ANIMATION_OUT);
            object.applyAnimatedStickerPeriodAnimation(packageId);
            object.setAnimatedStickerAnimationPeriod((int) duration);
        }
        if (TextUtils.isEmpty(packageId)) {
            animationMap.remove(type);
        } else {
            addAnimation(packageId, type, duration);
        }
    }

    private void addAnimation(String packageId, String type, long duration) {
        StickerAnimation stickerAnimation = animationMap.get(type);
        if (stickerAnimation == null) {
            stickerAnimation = new StickerAnimation(packageId, type, duration);
            animationMap.put(type, stickerAnimation);
        } else {
            stickerAnimation.setData(packageId, type, duration);
        }
    }


    /**
     * Change animation duration.
     * 改变动画时长
     *
     * @param type     动画类型, 见StickerAnimation.TYPE_ANIMATION_IN, StickerAnimation.TYPE_ANIMATION_OUT 和 StickerAnimation.TYPE_ANIMATION_COMP
     *                 the type {@code StickerAnimation.TYPE_ANIMATION_IN, StickerAnimation.TYPE_ANIMATION_OUT and StickerAnimation.TYPE_ANIMATION_COMP }
     * @param duration 动画时长 the duration
     */
    public void changeAnimationDuration(String type, long duration) {
        NvsTimelineAnimatedSticker object = getObject();
        if (object == null) {
            return;
        }
        if (StickerAnimation.TYPE_ANIMATION_IN.equals(type)) {
            object.setAnimatedStickerInAnimationDuration((int) duration);
        } else if (StickerAnimation.TYPE_ANIMATION_OUT.equals(type)) {
            object.setAnimatedStickerOutAnimationDuration((int) duration);
        } else if (StickerAnimation.TYPE_ANIMATION_COMP.equals(type)) {
            object.setAnimatedStickerAnimationPeriod((int) duration);
        }
        StickerAnimation stickerAnimation = animationMap.get(type);
        if (stickerAnimation != null) {
            stickerAnimation.setDuration(duration);
        }
    }

    /**
     * Get animation sticker animation.
     * 获取贴纸动画
     *
     * @param type 动画类型, 见StickerAnimation.TYPE_ANIMATION_IN, StickerAnimation.TYPE_ANIMATION_OUT 和 StickerAnimation.TYPE_ANIMATION_COMP
     *             the type {@code StickerAnimation.TYPE_ANIMATION_IN, StickerAnimation.TYPE_ANIMATION_OUT and StickerAnimation.TYPE_ANIMATION_COMP }
     * @return 动画数据类 the sticker animation
     */
    public StickerAnimation getAnimation(String type) {
        return animationMap.get(type);
    }

    /**
     * Get all animation list.
     * 获取所有的动画
     *
     * @return the list
     */
    public List<StickerAnimation> getAllAnimation() {
        List<StickerAnimation> animationList = new ArrayList<>();
        if (animationMap.isEmpty()) {
            return animationList;
        }
        Set<Map.Entry<String, StickerAnimation>> entries = animationMap.entrySet();
        for (Map.Entry<String, StickerAnimation> entry : entries) {
            StickerAnimation value = entry.getValue();
            if (value != null) {
                animationList.add(value);
            }
        }
        return animationList;
    }

    /**
     * 清除所有动画
     * Clear all animation
     **/
    public void clearAllAnimation() {
        setAnimation("", StickerAnimation.TYPE_ANIMATION_COMP, 0);
    }

    public boolean getIsCustomSticker() {
        return isCustomSticker;
    }

    public void setCustomAnimatedStickerImagePath(String customAnimatedStickerImagePath) {
        this.customAnimatedStickerImagePath = customAnimatedStickerImagePath;
        NvsTimelineAnimatedSticker object = getObject();
        if (object != null) {
            object.setStringVal(NvsConstants.STICKER_KEY_FOR_CUSTOM_FILE_PATH, customAnimatedStickerImagePath);
        }
    }

    public String getCustomAnimatedStickerImagePath() {
        return customAnimatedStickerImagePath;
    }


    boolean bindToTimeline() {
        NvsTimelineAnimatedSticker nvsTimelineAnimatedSticker = getObject();
        if (nvsTimelineAnimatedSticker == null) {
            return false;
        }
        nvsTimelineAnimatedSticker.setHorizontalFlip(isHorizontalFlip());
        nvsTimelineAnimatedSticker.setClipAffinityEnabled(false);
        PointF translation = new PointF(getTranslationX(), getTranslationY());
        float scaleFactor = getScale();
        float rotation = getRotation();
        if (scaleFactor > 0) {
            nvsTimelineAnimatedSticker.setScale(scaleFactor);
        }
        nvsTimelineAnimatedSticker.setZValue(getZValue());
        nvsTimelineAnimatedSticker.setRotationZ(rotation);
        nvsTimelineAnimatedSticker.setTranslation(translation);
        float volumeGain = getLeftVolume();
        nvsTimelineAnimatedSticker.setVolumeGain(volumeGain, volumeGain);
        nvsTimelineAnimatedSticker.setHorizontalFlip(isHorizontalFlip());
        nvsTimelineAnimatedSticker.setVerticalFlip(isVerticalFlip());
        /*还原关键帧
        * Restore key frame
        * */
        keyFrameProcessor().bindToTimeline();
        if (!CommonUtils.isEmpty(animationMap)) {
            Set<Map.Entry<String, StickerAnimation>> entries = animationMap.entrySet();
            for (Map.Entry<String, StickerAnimation> entry : entries) {
                StickerAnimation value = entry.getValue();
                setAnimation(value.getPackageId(), value.getType(), value.getDuration());
            }
        }
        return true;
    }

    @Override
    public void loadData() {
        NvsTimelineAnimatedSticker sticker = getObject();
        if (sticker == null) {
            return;
        }
        setObject(sticker);
        scale = sticker.getScale();
        rotation = sticker.getRotationZ();
        PointF translation = sticker.getTranslation();
        translationX = translation.x;
        translationY = translation.y;
        horizontalFlip = sticker.getHorizontalFlip();
        verticalFlip = sticker.getVerticalFlip();
        leftVolume = sticker.getVolumeGain().leftVolume;
        hasAudio = sticker.hasAudio();
        zValue = sticker.getZValue();
        setInPoint(sticker.getInPoint());
        setOutPoint(sticker.getOutPoint());
        packageId = sticker.getAnimatedStickerPackageId();
        FileInfoBridge.FileInfo fileInfo = FileInfoBridge.getFileInfo(packageId);
        if (fileInfo != null) {
            coverImagePath = fileInfo.filePath;
        }
        customAnimatedStickerImagePath = sticker.getStringVal(NvsConstants.STICKER_KEY_FOR_CUSTOM_FILE_PATH);
        if (!TextUtils.isEmpty(customAnimatedStickerImagePath)) {
            coverImagePath = customAnimatedStickerImagePath;
            isCustomSticker = true;
        }
        String periodPackageId = sticker.getAnimatedStickerPeriodAnimationPackageId();
        if (!TextUtils.isEmpty(periodPackageId)) {
            addAnimation(periodPackageId, StickerAnimation.TYPE_ANIMATION_COMP, sticker.getAnimatedStickerAnimationPeriod());
        }
        String inPackageId = sticker.getAnimatedStickerInAnimationPackageId();
        if (!TextUtils.isEmpty(inPackageId)) {
            addAnimation(inPackageId, StickerAnimation.TYPE_ANIMATION_IN, sticker.getAnimatedStickerInAnimationDuration());
        }
        String outPackageId = sticker.getAnimatedStickerOutAnimationPackageId();
        if (!TextUtils.isEmpty(outPackageId)) {
            addAnimation(outPackageId, StickerAnimation.TYPE_ANIMATION_OUT, sticker.getAnimatedStickerOutAnimationDuration());
        }
        keyFrameProcessor().recoverFromTimelineData(sticker);
    }


    @NonNull
    @Override
    public Object clone() {
        return clone(true);
    }

    /**
     * 复制，
     * Clone
     *
     * @param changeCreateTag true change the create tag,false not
     * @return the MeicamCaptionClip
     */
    @Override
    public Object clone(boolean changeCreateTag) {
        Object clone = DeepCopyUtil.deepClone(this);
        if (clone == null) {
            String cloneString = GsonContext.getInstance().toJson(this);
            if (!TextUtils.isEmpty(cloneString)) {
                clone = GsonContext.getInstance().fromJson(cloneString, this.getClass());
            }
        }
        if (changeCreateTag && clone instanceof ClipInfo) {
            ((ClipInfo<?>) clone).generateCreateTag();
        }
        return clone;
    }

    @Override
    public LMeicamStickerClip parseToLocalData() {
        LMeicamStickerClip local = new LMeicamStickerClip(getPackageId());
        setCommonData(local);
        local.setStickerType(getStickerType());
        local.setPackageId(getPackageId());
        local.setScale(getScale());
        local.setRotation(getRotation());
        local.setTranslationX(getTranslationX());
        local.setTranslationY(getTranslationY());
        local.setHorizontalFlip(isHorizontalFlip());
        local.setVerticalFlip(isVerticalFlip());
        local.setDisplayName(getDisplayName());
        local.setDisplayNamezhCN(getDisplayNameEN());
        local.setLeftVolume(getLeftVolume());
        local.setHasAudio(isHasAudio());
        local.setCoverImagePath(getCoverImagePath());
        local.setzValue(getZValue());
        local.setIsCustomSticker(getIsCustomSticker());
        HashMap<String, StickerAnimation> localAnimationMap = new HashMap<>();
        if (!animationMap.isEmpty()) {
            Set<Map.Entry<String, StickerAnimation>> entries = animationMap.entrySet();
            for (Map.Entry<String, StickerAnimation> entry : entries) {
                try {
                    localAnimationMap.put(entry.getKey(), entry.getValue().clone());
                } catch (CloneNotSupportedException e) {
                    LogUtils.e(e);
                }
            }
        }
        local.setAnimationMap(localAnimationMap);
        local.setCustomanimatedStickerImagePath(getCustomAnimatedStickerImagePath());
        if (keyFrameMap != null && keyFrameMap.size() > 0) {
            List<LMeicamKeyFrame> lKeyFrameList = new ArrayList<>();
            for (Map.Entry<Long, MeicamKeyFrame> entry : keyFrameMap.entrySet()) {
                MeicamKeyFrame value = entry.getValue();
                if (value != null) {
                    lKeyFrameList.add(value.parseToLocalData());
                }
            }
            local.setKeyFrameList(lKeyFrameList);
        }
        local.setKeyFrameProcessor(keyFrameProcessor().parseToLocalData());
        local.setResourceId(resourceId);
        return local;
    }

    @Override
    public void recoverFromLocalData(LMeicamStickerClip lMeicamStickerClip) {
        setStickerType(lMeicamStickerClip.getStickerType());
        setScale(lMeicamStickerClip.getScale());
        setRotation(lMeicamStickerClip.getRotation());
        setTranslationX(lMeicamStickerClip.getTranslationX());
        setTranslationY(lMeicamStickerClip.getTranslationY());
        setHorizontalFlip(lMeicamStickerClip.isHorizontalFlip());
        setVerticalFlip(lMeicamStickerClip.isVerticalFlip());
        setDisplayName(lMeicamStickerClip.getDisplayName());
        setDisplayNameEN(lMeicamStickerClip.getDisplayNamezhCN());
        setLeftVolume(lMeicamStickerClip.getLeftVolume());
        setHasAudio(lMeicamStickerClip.isHasAudio());
        setCoverImagePath(lMeicamStickerClip.getCoverImagePath());
        setZValue(lMeicamStickerClip.getzValue());
        setZValue(lMeicamStickerClip.getzValue());
        Map<String, StickerAnimation> animationMap = lMeicamStickerClip.getAnimationMap();
        if (!CommonUtils.isEmpty(animationMap)) {
            Set<Map.Entry<String, StickerAnimation>> entries = animationMap.entrySet();
            for (Map.Entry<String, StickerAnimation> entry : entries) {
                StickerAnimation value = entry.getValue();
                setAnimation(value.getPackageId(), value.getType(), value.getDuration());
            }
        }
        keyFrameProcessor().recoverKeyFrame(lMeicamStickerClip);
        keyFrameProcessor().recoverFromLocalData(lMeicamStickerClip.getKeyFrameProcessor());
    }

    @Override
    public void recoverFromTimelineData(NvsObject nvsObject) {
        if (!(nvsObject instanceof NvsTimelineAnimatedSticker)) {
            return;
        }
        NvsTimelineAnimatedSticker nvsTimelineAnimatedSticker = (NvsTimelineAnimatedSticker) nvsObject;
        setObject(nvsTimelineAnimatedSticker);
        loadData();
//        setDisplayName(nvsTimelineAnimatedSticker.getDisplayName());
//        setDisplayNameEN(lMeicamStickerClip.getDisplayNamezhCN());
//        setStickerType(lMeicamStickerClip.getStickerType());
//        setCoverImagePath(lMeicamStickerClip.getCoverImagePath());
    }

    @Override
    public void parseToResourceId(MeicamTimeline timeline) {
        if (timeline == null) {
            return;
        }
        if (!TextUtils.isEmpty(customAnimatedStickerImagePath)) {
            MeicamResource resource = new MeicamResource();
            resource.addPathInfo(new MeicamResource.PathInfo("path",
                    customAnimatedStickerImagePath, false));
            resourceId = timeline.getPlaceId(resource);
        }
    }

    private KeyFrameProcessor<NvsTimelineAnimatedSticker> mKeyFrameHolder;

    @Override
    public KeyFrameProcessor<NvsTimelineAnimatedSticker> keyFrameProcessor() {
        if (mKeyFrameHolder == null) {
            mKeyFrameHolder = new KeyFrameProcessor<>(this);
        }
        return mKeyFrameHolder;
    }
}
