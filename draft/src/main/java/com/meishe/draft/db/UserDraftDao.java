package com.meishe.draft.db;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.RoomWarnings;
import androidx.room.Update;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/28 14:15
 * @Description :用户草稿dao  the Dao of user to draft
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
@Dao
public interface UserDraftDao {
   @Insert
   void insert(UserDraftEntity... entities);

   @Update
   void update(UserDraftEntity... entities);

   @Delete
   void delete(UserDraftEntity... entities);

   @SuppressWarnings(RoomWarnings.CURSOR_MISMATCH)
   @Query("SELECT * from LocalDraftEntity INNER JOIN UserDraftEntity ON UserDraftEntity.id = LocalDraftEntity.id AND UserDraftEntity.userId = :userId")
   List<LocalDraftEntity> getDraftList(String userId);

   @Query("SELECT * from UserDraftEntity WHERE UserDraftEntity.id = :projectId")
   UserDraftEntity getUserDraft(String projectId);
}
