package com.meishe.myvideo.bean;


import android.text.TextUtils;

import com.meishe.base.utils.ResourceUtils;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.bean.BaseInfo;

/**
 * The type Change speed curve info.
 * 变速曲线信息类
 */
public class ChangeSpeedCurveInfo extends BaseInfo {

    private String coverName;
    /**
     * The Speed.
     * 速度
     */
    private String speed;

    /**
     * The Speed original.
     * 原来的速度
     */
    private String speedOriginal;

    private String enName;

    public String getCoverName() {
        return coverName;
    }

    public void setCoverName(String coverName) {
        this.coverName = coverName;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    @Override
    public int getType() {
        return AssetInfo.ASSET_CHANGE_SPEED_CURVE;
    }

    @Override
    public int getCoverId() {
        if (TextUtils.isEmpty(coverName)) {
            return super.getCoverId();
        }
        return ResourceUtils.getMipmapIdByName(coverName);
    }

    public String getSpeed() {
        return speed;
    }

    public void setSpeed(String speed) {
        this.speed = speed;
    }

    public String getSpeedOriginal() {
        return speedOriginal;
    }

    public void setSpeedOriginal(String speedOriginal) {
        this.speedOriginal = speedOriginal;
    }
}
