package com.meishe.engine.local;

import com.google.gson.annotations.SerializedName;
import com.meishe.engine.bean.CommonData;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 版权所有:www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/05/20 10:07
 * @Description : 草稿视频轨道 The video track of draft data
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class LMeicamVideoTrack extends LTrackInfo implements Cloneable, Serializable {
    @SerializedName("transitions")
    private List<LMeicamTransition> mTransitionInfoList = new ArrayList<>();
    @SerializedName("clipInfos")
    private List<LMeicamVideoClip> mVideoClipList = new ArrayList<>();
    @SerializedName("isMute")
    private boolean mIsMute = false;

    public LMeicamVideoTrack(int index) {
        super(CommonData.TRACK_VIDEO, index);
    }

    public List<LMeicamTransition> getTransitionInfoList() {
        return mTransitionInfoList;
    }

    private List<LMeicamTrackVideoFx> trackVideoFxList;

    public void setTransitionInfoList(List<LMeicamTransition> transitionInfoList) {
        mTransitionInfoList = transitionInfoList;
    }

    public boolean isMute() {
        return mIsMute;
    }

    public void setIsMute(boolean isMute) {
        this.mIsMute = isMute;
    }

    public List<LMeicamVideoClip> getVideoClipList() {
        return mVideoClipList;
    }

    public void setVideoClipList(List<LMeicamVideoClip> videoClipList) {
        this.mVideoClipList = videoClipList;
    }

    public List<LMeicamTrackVideoFx> getTrackVideoFxList() {
        if (trackVideoFxList == null) {
            trackVideoFxList = new ArrayList<>();
        }
        return trackVideoFxList;
    }

    public void setTrackVideoFxList(List<LMeicamTrackVideoFx> trackVideoFxList) {
        this.trackVideoFxList = trackVideoFxList;
    }
}
