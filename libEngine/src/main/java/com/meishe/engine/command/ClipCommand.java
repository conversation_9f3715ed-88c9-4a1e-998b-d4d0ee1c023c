package com.meishe.engine.command;

import com.meishe.annotation.Undo;
import com.meishe.engine.bean.ClipInfo;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.MeicamAudioClip;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.engine.bean.MeicamCompoundCaptionClip;
import com.meishe.engine.bean.MeicamStickerClip;
import com.meishe.engine.bean.MeicamTimelineVideoFilterAndAdjustClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxClip;
import com.meishe.engine.bean.MeicamVideoClip;

import java.security.InvalidParameterException;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/9/7 14:57
 * @Description : Clip的命令 The clip command.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class ClipCommand {

    /**
     * 注意：如果同时改变in point 和 out point请使用changeInAndOutPoint方法
     * Note: Use the changeInAndOutPoint method if you change both in point and out point
     */
    @Undo(className = "ClipSetInPointCommand", function = "setInPoint",
            param = {"long|inPoint", "boolean...|needSaveOperate"})
    public static void setInPoint(ClipInfo<?> stickerClip, long inPoint, boolean... needSavaOperate) {
        if (CommandUtil.needSaveOperate(needSavaOperate)) {
            String tag = getTag(stickerClip);
            Object[] unDoParam = new Object[]{stickerClip.getInPoint(), new boolean[]{false}};
            Object[] redoParam = new Object[]{inPoint};
            CommandUtil.saveOperate("ClipSetInPointCommand", unDoParam, redoParam, tag, tag);
        }
        stickerClip.setInPoint(inPoint);
    }

    /**
     * 注意：如果同时改变in point 和 out point请使用changeInAndOutPoint
     * Note: Use the changeInAndOutPoint method if you change both in point and out point
     */
    @Undo(className = "ClipSetOutPointCommand", function = "setOutPoint",
            param = {"long|outPoint", "boolean...|needSaveOperate"})
    public static void setOutPoint(ClipInfo<?> stickerClip, long outPoint, boolean... needSavaOperate) {
        if (CommandUtil.needSaveOperate(needSavaOperate)) {
            String tag = getTag(stickerClip);
            Object[] unDoParam = new Object[]{stickerClip.getOutPoint(), new boolean[]{false}};
            Object[] redoParam = new Object[]{outPoint};
            CommandUtil.saveOperate("ClipSetOutPointCommand", unDoParam, redoParam, tag, tag);
        }
        stickerClip.setOutPoint(outPoint);
    }

    @Undo(className = "ClipChangeInAndOutPointCommand", function = "changeInAndOutPoint",
            param = {"long|inPoint", "long|outPoint", "boolean...|needSaveOperate"})
    public static void changeInAndOutPoint(ClipInfo<?> stickerClip, long inPoint, long outPoint, boolean... needSavaOperate) {
        long oldInPoint = stickerClip.getInPoint();
        long oldOutPoint = stickerClip.getOutPoint();
        if (CommandUtil.needSaveOperate(needSavaOperate)) {
            String tag = getTag(stickerClip);
            Object[] unDoParam = new Object[]{oldInPoint, oldOutPoint, new boolean[]{false}};
            Object[] redoParam = new Object[]{inPoint, outPoint};
            CommandUtil.saveOperate("ClipChangeInAndOutPointCommand", unDoParam, redoParam, tag, tag);
        }
        if (oldInPoint < inPoint) {
            stickerClip.setOutPoint(outPoint);
            stickerClip.setInPoint(inPoint);
        } else {
            stickerClip.setInPoint(inPoint);
            stickerClip.setOutPoint(outPoint);
        }
    }


    @Undo(className = "ClipSetZCommand", function = "setZValue",
            param = {"float|zValue", "boolean...|needSaveOperate"})
    public static void setZValue(ClipInfo<?> stickerClip, float zValue, boolean... needSavaOperate) {
        if (CommandUtil.needSaveOperate(needSavaOperate)) {
            String tag = getTag(stickerClip);
            Object[] unDoParam = new Object[]{stickerClip.getZValue(), new boolean[]{false}};
            Object[] redoParam = new Object[]{zValue};
            CommandUtil.saveOperate("ClipSetZCommand", unDoParam, redoParam, tag, tag);
        }
        stickerClip.setZValue(zValue);
    }

    private static String getTag(ClipInfo<?> clipInfo) {
        String tag;
        if (clipInfo instanceof MeicamAudioClip) {
            tag = AudioCommand.getTag((MeicamAudioClip) clipInfo);
        } else if (clipInfo instanceof MeicamStickerClip) {
            tag = StickerCommand.getTag((MeicamStickerClip) clipInfo);
        } else if (clipInfo instanceof MeicamCaptionClip) {
            tag = CaptionCommand.getTag((MeicamCaptionClip) clipInfo);
        } else if (clipInfo instanceof MeicamCompoundCaptionClip) {
            tag = CompCaptionCommand.getTag((MeicamCompoundCaptionClip) clipInfo);
        } else if (clipInfo instanceof MeicamVideoClip) {
            tag = VideoClipCommand.getTag((MeicamVideoClip) clipInfo);
        } else if (clipInfo instanceof MeicamTimelineVideoFilterAndAdjustClip) {
            tag = FilterAndAdjustClipCommand.getTag((MeicamTimelineVideoFilterAndAdjustClip) clipInfo);
        } else if (clipInfo instanceof MeicamTimelineVideoFxClip) {
            tag = TimelineFxCommand.getTag((MeicamTimelineVideoFxClip) clipInfo);
        } else {
            throw new InvalidParameterException("Please support the class of ClipInfo");
        }
        tag += "[" + clipInfo.getType();
        return tag;
    }

    public static ClipInfo<?> getItByTag(String tag) {
        int index = tag.lastIndexOf("[");
        String extraTag = "";
        if (index > 0) {
            extraTag = tag.substring(0, index);
        }
        String clipType = tag.substring(index + 1);
        switch (clipType) {
            case CommonData.CLIP_IMAGE:
            case CommonData.CLIP_VIDEO:
                return VideoClipCommand.getItByTag(extraTag);
            case CommonData.CLIP_AUDIO:
                return AudioCommand.getItByTag(extraTag);
            case CommonData.CLIP_STICKER:
                return StickerCommand.getItByTag(extraTag);
            case CommonData.CLIP_CAPTION:
                return CaptionCommand.getItByTag(extraTag);
            case CommonData.CLIP_COMPOUND_CAPTION:
                return CompCaptionCommand.getItByTag(extraTag);
            case MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_ADJUST:
            case MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FILTER:
                return FilterAndAdjustClipCommand.getItByTag(extraTag);
            case CommonData.CLIP_TIMELINE_FX:
                return TimelineFxCommand.getItByTag(extraTag);
            default:
                return null;
        }
    }
}
