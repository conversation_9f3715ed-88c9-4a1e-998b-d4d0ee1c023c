package com.meishe.draft.observer;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2021/5/17 14:02
 * @Description: 草稿数据观察者 the observer of draft data
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public abstract class DraftObserver {

    /**
     * 草稿资源发生变化
     * On draft changed
     */
    public void onDraftChanged() {
    }
}
