package com.meishe.engine.observer;

import com.meishe.engine.util.ConvertFileManager;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2021/6/11 10:30
 * @Description: 倒放视频片段
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public abstract class ConvertFileObserver {

    /**
     * 转换进度
     * On convert progress.
     *
     * @param progress the progress
     */
    public void onConvertProgress(float progress) {
    }

    /**
     * 转换完成
     * On convert finish.
     *
     * @param convertParam   the convert param 转化参数
     * @param convertSuccess the convert success  是否成功
     */
    public void onConvertFinish(ConvertFileManager.ConvertParam convertParam, boolean convertSuccess) {

    }

    /**
     * 取消转换
     * On convert cancel.
     */
    public void onConvertCancel() {

    }
}
