package com.meishe.myvideo.fragment;

import android.graphics.Bitmap;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;

import androidx.fragment.app.Fragment;

import com.meishe.base.bean.MediaData;
import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseFragment;
import com.meishe.myvideo.R;
import com.meishe.myvideo.activity.MaterialSingleSelectActivity;

import java.util.ArrayList;

import static com.meishe.logic.constant.PagerConstants.MEDIA_FILTER;
import static com.meishe.logic.constant.PagerConstants.MEDIA_TYPE;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/12/14 17:53
 * @Description :编辑页面替换fragment The cover edit Fragment for importing
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CoverEditImportFragment extends BaseFragment {
    private static final String INTENT_CODE_REQUEST_CODE = "requestCode";
    private static final String INTENT_CODE_COVER = "cover";
    private int mRequestCode;
    private ImageView mCoverImage;

    public CoverEditImportFragment() {
    }

    public static Fragment create(int requestCode) {
        CoverEditImportFragment importFragment = new CoverEditImportFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(INTENT_CODE_REQUEST_CODE, requestCode);
        importFragment.setArguments(bundle);
        return importFragment;
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_cover_edit_import;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        mCoverImage = rootView.findViewById(R.id.iv_cover);
        mCoverImage.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Bundle bundle = new Bundle();
                bundle.putInt(MEDIA_TYPE, MediaData.TYPE_PHOTO);
                ArrayList<String> filter = new ArrayList<>();
                filter.add(MediaData.TYPE_FILTER_GIF);
                bundle.putStringArrayList(MEDIA_FILTER, filter);
                AppManager.getInstance().jumpActivityForResult(getActivity(),
                        MaterialSingleSelectActivity.class, bundle, mRequestCode);
            }
        });

    }

    @Override
    protected void initData() {
        Bundle arguments = getArguments();
        if (arguments != null) {
            mRequestCode = arguments.getInt(INTENT_CODE_REQUEST_CODE);
        }
    }

    /**
     * Sets cover.
     * 设置封面图标
     * @param bitmap the bitmap
     */
    public void setCover(Bitmap bitmap) {
        mCoverImage.setImageBitmap(bitmap);
    }
}
