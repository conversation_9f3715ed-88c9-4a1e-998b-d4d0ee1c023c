package com.meishe.myvideo.manager;

import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;

import com.meishe.base.utils.LogUtils;

import java.io.File;
import java.io.IOException;

/**
 * The type Record manager.
 * 此类为 记录管理器
 */
public class RecordManager {
    private static final RecordManager ourInstance = new RecordManager();

    /**
     * Gets instance.
     * 获取实例
     * @return the instance
     */
    public static RecordManager getInstance() {
        return ourInstance;
    }

    private RecordManager() {
    }

    private String mDirName;

    private File file;

    private MediaRecorder mRecorder;

    /**
     * 音频获取源
     * Audio acquisition source
     */
    public static int audioSource = MediaRecorder.AudioSource.MIC;

    /**
     * 设置采样率 44100是目前的标准，但是某些设备仍然支持22050，16000，11025
     * Set the audio sampling rate, 44100 is the current standard, but some devices still support 22050, 16000, 11025
     */
    public static int sampleRateInHz = 44100;
    /**
     * 声道设置，CHANNEL_IN_STEREO为双声道，CHANNEL_CONFIGURATION_MONO为单声道
     * <p>
     * Set the audio recording channel CHANNEL_IN_STEREO to two-channel and CHANNEL_CONFIGURATION_MONO to mono
     */
    public static int channelConfig = AudioFormat.CHANNEL_IN_STEREO;

    /**
     * 音频数据格式 PCM 16位每个样本 保证设备支持  8位的有的设备不支持
     * Audio data format: PCM 16 bits per sample. Guaranteed device support.
     * PCM 8 bits per sample. Not necessarily supported by the device.
     */
    public static int audioFormat = AudioFormat.ENCODING_PCM_16BIT;


    /**
     * 设置音频采样率，44100是目前的标准，但是某些设备仍然支持22050，16000，11025
     * Set the audio sampling rate. 44100 is the current standard, but some devices still support 220501600011025
     */
    private static final int FREQUENCY = 16000;
    /**
     * 设置单声道声道
     * Set mono
     */
    private static final int CHANNELCONGIFIGURATION = AudioFormat.CHANNEL_IN_MONO;
    /**
     * 音频数据格式：每个样本16位
     * Audio data format: 16 bits per sample
     */
    private static final int AUDIOENCODING = AudioFormat.ENCODING_PCM_16BIT;
    /**
     * The constant AUDIO_SOURCE.
     * 常数AUDIO_SOURCE
     * 音频获取源
     * Audio acquisition source
     */
    public final static int AUDIO_SOURCE = MediaRecorder.AudioSource.MIC;

    /**
     * 缓冲区字节大小
     * Buffer byte size
     * */
    public static int bufferSizeInBytes = 0;


    /**
     * The On record start listener.
     * 记录启动监听器
     */
    OnRecordListener onRecordStartListener;

    /**
     * The interface On record listener.
     * 记录监听器上的接口
     */
    public interface OnRecordListener {
        /**
         * On record start.
         * 记录起始
         *
         * @param id       the id
         * @param filePath the file path
         */
        void onRecordStart(Long id, String filePath);

        /**
         * On record end.
         * 记录结束
         */
        void onRecordEnd();
    }

    /**
     * Sets on record listener.
     *
     * @param onRecordStart the on record start
     */
    public void setOnRecordListener(OnRecordListener onRecordStart) {
        this.onRecordStartListener = onRecordStart;
    }

    /**
     * 资源释放
     * Release of resources
     */
    public void release() {
        try {
            if (mRecorder != null) {
                mRecorder.stop();
                if (onRecordStartListener != null) {
                    onRecordStartListener.onRecordEnd();
                }
                mRecorder.release();
                mRecorder = null;
            } else {
                if (onRecordStartListener != null) {
                    onRecordStartListener.onRecordEnd();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 判断是否有录音权限
     * Determine if you have recording rights
     *
     * @return boolean
     */
    public static boolean isHasPermission() {
        bufferSizeInBytes = 0;
        bufferSizeInBytes = AudioRecord.getMinBufferSize(sampleRateInHz,
                channelConfig, audioFormat);
        AudioRecord audioRecord = new AudioRecord(audioSource, sampleRateInHz,
                channelConfig, audioFormat, bufferSizeInBytes);
        try {
            audioRecord.startRecording();
        } catch (IllegalStateException e) {
            e.printStackTrace();
        }
        LogUtils.d(audioRecord.getRecordingState());
        if (audioRecord.getRecordingState() != AudioRecord.RECORDSTATE_RECORDING) {
            return false;
        }
        audioRecord.stop();
        audioRecord.release();
        return true;
    }

    /**
     * 准备开始录制
     * Ready to start recording
     *
     * @param dirName the dir name
     */
    public void prepareAudio(String dirName) {
        try {
            File dir = new File(dirName);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            long nowTime = System.currentTimeMillis();
            String fileNameString = nowTime + ".mp3";

            file = new File(dir, fileNameString);
            if (!file.exists()) {
                file.createNewFile();
            }
            mDirName = file.getAbsolutePath();

            mRecorder = new MediaRecorder();

            /*
             * 设置输出文件
             * Set the output file
             * */
            mRecorder.setOutputFile(file.getAbsolutePath());
            /*
             * 设置mediaRecorder的音频源是麦克风
             * Set mediaRecorder's audio source to be a microphone
             * */
            mRecorder.setAudioSource(MediaRecorder.AudioSource.MIC);

            /*
             * 设置文件音频的输出格式为MPEG_4
             * Set the file audio output format to MPEG_4
             * */
            mRecorder.setOutputFormat(MediaRecorder.OutputFormat.MPEG_4);

            /*
             * 设置音频的编码格式为AAC
             * Set the audio encoding format to AAC
             * */
            mRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.AAC);

            mRecorder.prepare();
            mRecorder.start();

            onRecordStartListener.onRecordStart(nowTime,mDirName);
        } catch (IllegalStateException e) {
            if (mRecorder != null) {
                mRecorder.release();
                mRecorder = null;
            }
            if (file.exists()){
                file.delete();
            }
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
