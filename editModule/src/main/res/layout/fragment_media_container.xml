<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tl="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:orientation="vertical">

    <com.meishe.third.tablayout.SlidingTabLayout
        android:id="@+id/tl_select_media"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_100"
        tl:tl_indicator_height="@dimen/dp_px_4"
        tl:tl_indicator_width="@dimen/dp_px_30"
        tl:tl_tab_space_equal="true"
        tl:tl_textSelectColor="@color/white"
        tl:tl_textSize="@dimen/sp_px_36"
        tl:tl_textUnselectedColor="@color/white_5" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/vp_select_media"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_px_30"
        android:layout_marginBottom="@dimen/dp_px_30"
        android:layout_weight="1" />

</LinearLayout>