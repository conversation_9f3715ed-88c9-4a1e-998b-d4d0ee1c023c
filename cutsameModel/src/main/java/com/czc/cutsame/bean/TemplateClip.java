package com.czc.cutsame.bean;

import android.os.Parcel;
import android.os.Parcelable;

import com.meishe.base.utils.GsonUtils;

import java.util.ArrayList;
import java.util.List;


/**
 * author : lhz
 * date   : 2020/11/4
 * desc   :模板中的片段实体类
 * A fragment entity class in a template
 */
public class TemplateClip implements Parcelable, Comparable<TemplateClip> {

    /**
     * 模板片段所需文件路径
     * The file path required for the template fragment
     */
    private String filePath;
    /**
     * 模板片段时长
     * Template clip length
     */
    private long duration;

    /**
     * 模板片段的trim 时长
     * the template clip trim duration
     */
    private long trimDuration;

    /**
     * 模板片段的trim in
     * the template clip trim in
     */
    private long trimIn;
    /**
     * 模板片段类型
     * Template fragment type
     */
    private int type;
    /**
     * 辅助类/属性
     * Helper classes/attributes int[]
     */
    private Object tag;

    private String mFootageId;

    private int groupIndex;

    private long inPoint;

    private int trackIndex;

    /**
     * 是否有编组 1代表有编组，0代表无编组
     * is or not group
     */
    private int isHasGroup;
    /**
     * 是否需要倒放 1需要，0代表不需要
     * is or not Reverse
     */
    private int needReverse;

    private String reversePath;
    /**
     * 填入素材类型
     * media type
     */
    private int mediaType;

    private int originalWidth;
    private int originalHeight;

    private List<Integer> clipTrackIndexInTimelineList = new ArrayList<>();
    private List<Integer> clipIndexInTimelineList = new ArrayList<>();
    private int clipIndex;

    public TemplateClip() {
    }

    public String getFootageId() {
        return mFootageId;
    }

    public TemplateClip setFootageId(String footageId) {
        this.mFootageId = footageId;
        return this;
    }

    public int getType() {
        return type;
    }

    public TemplateClip setType(int type) {
        this.type = type;
        return this;
    }

    public String getFilePath() {
        return filePath;
    }

    public TemplateClip setFilePath(String filePath) {
        this.filePath = filePath;
        return this;
    }

    public long getDuration() {
        return duration;
    }

    public TemplateClip setDuration(long duration) {
        this.duration = duration;
        return this;
    }

    public long getTrimDuration() {
        return trimDuration;
    }

    public TemplateClip setTrimDuration(long trimDuration) {
        this.trimDuration = trimDuration;
        return this;
    }

    public TemplateClip setTrimIn(long trimIn) {
        this.trimIn = trimIn;
        return this;
    }

    public long getTrimIn() {
        return trimIn;
    }

    public Object getTag() {
        return tag;
    }

    public TemplateClip setTag(Object tag) {
        this.tag = tag;
        return this;
    }

    public int getGroupIndex() {
        return groupIndex;
    }

    public TemplateClip setGroupIndex(int groupIndex) {
        this.groupIndex = groupIndex;
        return this;
    }

    public long getInPoint() {
        return inPoint;
    }

    public TemplateClip setInPoint(long inPoint) {
        this.inPoint = inPoint;
        return this;
    }

    public int getTrackIndex() {
        return trackIndex;
    }

    public TemplateClip setTrackIndex(int trackIndex) {
        this.trackIndex = trackIndex;
        return this;
    }

    public boolean isHasGroup() {
        return isHasGroup == 1;
    }

    public TemplateClip setHasGroup(boolean hasGroup) {
        this.isHasGroup = hasGroup ? 1 : 0;
        return this;
    }

    public boolean getNeedReverse() {
        return needReverse == 1;
    }

    public TemplateClip setNeedReverse(boolean needReverse) {
        this.needReverse = needReverse ? 1 : 0;
        return this;
    }

    public int getOriginalWidth() {
        return originalWidth;
    }

    public void setOriginalWidth(int originalWidth) {
        this.originalWidth = originalWidth;
    }

    public int getOriginalHeight() {
        return originalHeight;
    }

    public void setOriginalHeight(int originalHeight) {
        this.originalHeight = originalHeight;
    }

    public String getReversePath() {
        return reversePath;
    }

    public void setReversePath(String reversePath) {
        this.reversePath = reversePath;
    }

    public int getMediaType() {
        return mediaType;
    }

    public void setMediaType(int mediaType) {
        this.mediaType = mediaType;
    }

    public List<Integer> getClipTrackIndexInTimelineList() {
        return clipTrackIndexInTimelineList;
    }

    public TemplateClip setClipTrackIndexInTimelineList(List<Integer> clipTrackIndexInTimelineList) {
        this.clipTrackIndexInTimelineList = clipTrackIndexInTimelineList;
        return this;
    }

    public List<Integer> getClipIndexInTimelineList() {
        return clipIndexInTimelineList;
    }

    public TemplateClip setClipIndexInTimelineList(List<Integer> clipIndexInTimelineList) {
        this.clipIndexInTimelineList = clipIndexInTimelineList;
        return this;
    }

    public int getClipIndex() {
        return clipIndex;
    }

    public TemplateClip setClipIndex(int clipIndex) {
        this.clipIndex = clipIndex;
        return this;
    }

    protected TemplateClip(Parcel in) {
        filePath = in.readString();
        duration = in.readLong();
        trimDuration = in.readLong();
        trimIn = in.readLong();
        type = in.readInt();
        mFootageId = in.readString();
        groupIndex = in.readInt();
        inPoint = in.readLong();
        trackIndex = in.readInt();
        isHasGroup = in.readInt();
        needReverse = in.readInt();
        reversePath = in.readString();
        originalWidth = in.readInt();
        originalHeight = in.readInt();
        in.readList(clipTrackIndexInTimelineList, Integer.class.getClassLoader());
        in.readList(clipIndexInTimelineList, Integer.class.getClassLoader());
        clipIndex = in.readInt();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(filePath);
        dest.writeLong(duration);
        dest.writeLong(trimDuration);
        dest.writeLong(trimIn);
        dest.writeInt(type);
        dest.writeString(mFootageId);
        dest.writeInt(groupIndex);
        dest.writeLong(inPoint);
        dest.writeInt(trackIndex);
        dest.writeInt(isHasGroup);
        dest.writeInt(needReverse);
        dest.writeString(reversePath);
        dest.writeInt(originalWidth);
        dest.writeInt(originalHeight);
        dest.writeList(clipTrackIndexInTimelineList);
        dest.writeList(clipIndexInTimelineList);
        dest.writeInt(clipIndex);
    }

    public TemplateClip copy() {
        return GsonUtils.fromJson(GsonUtils.toJson(this), getClass());
    }

    public void update(TemplateClip clip) {
        setFilePath(clip.getFilePath());
        setDuration(clip.getDuration());
        setTrimDuration(clip.getTrimDuration());
        setType(clip.getType());
        setGroupIndex(clip.getGroupIndex());
        setFootageId(clip.getFootageId());
        setInPoint(clip.getInPoint());
        setTrackIndex(clip.getTrackIndex());
        setHasGroup(clip.isHasGroup());
        setNeedReverse(clip.getNeedReverse());
        setReversePath(clip.getReversePath());
        setClipIndex(clip.getClipIndex());
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<TemplateClip> CREATOR = new Creator<TemplateClip>() {
        @Override
        public TemplateClip createFromParcel(Parcel in) {
            return new TemplateClip(in);
        }

        @Override
        public TemplateClip[] newArray(int size) {
            return new TemplateClip[size];
        }
    };

    @Override
    public int compareTo(TemplateClip o) {
        int i = Integer.parseInt(this.getFootageId().replaceAll("footage", ""))
                - Integer.parseInt(o.getFootageId().replaceAll("footage", ""));
        if (true) {
            //先按照Inpoint排序 Sort by Input first
            i = (int) (this.getInPoint() - o.getInPoint());
            if (i == 0) {
                //如果Inpoint相同，按照trackIndex排序 If the Inputs are the same, sort by trackIndex.
                return this.getTrackIndex() - o.getTrackIndex();
            }
        }
        return i;
    }
}
