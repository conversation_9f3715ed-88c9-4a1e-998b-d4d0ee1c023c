apply plugin: 'com.android.library'
android {
    compileSdkVersion rootProject.android.extCompileSdkVersion
    buildToolsVersion rootProject.android.extBuildToolsVersion


    defaultConfig {
        minSdkVersion rootProject.android.extMinSdkVersion
        targetSdkVersion rootProject.android.extTargetSdkVersion
        versionCode rootProject.config.extVersionCode
        versionName rootProject.config.extVersionName

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
    }

    buildTypes {
        release {
            consumerProguardFiles 'proguard-rules.pro'
        }
    }
}

dependencies {
    api fileTree(dir: 'libs', include: ['*.jar'])
    testImplementation rootProject.ext.dependencies.extTestJunit
    androidTestImplementation rootProject.ext.dependencies.extAndroidTestRunner
    androidTestImplementation rootProject.ext.dependencies.extTestEspresso
    //android
    api rootProject.ext.dependencies.extAppcompat
    api rootProject.ext.dependencies.extAppcompatRecycler
    api rootProject.ext.dependencies.extMutilDex
    api rootProject.ext.dependencies.extGoogleGson
    //图片
    implementation rootProject.ext.dependencies.extBumptechGlide

    //统计，目前内置友盟
    api project(path: ':libStatistic')
    //bugly
    //api rootProject.ext.dependencies.buglyCrashSDK
    //api rootProject.ext.dependencies.buglyCrashNDK

//    api 'com.android.support:appcompat-v7:25.3.1'
//    api 'com.github.huburt-Hu:NewbieGuide:v2.4.0'
}