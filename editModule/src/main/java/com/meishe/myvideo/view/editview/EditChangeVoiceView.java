package com.meishe.myvideo.view.editview;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.base.utils.SizeUtils;
import com.meishe.myvideo.R;
import com.meishe.myvideo.edit.record.AudioFxInfo;
import com.meishe.myvideo.interfaces.BottomEventListener;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 音频变声视图
 * Audio change sound view
 */
public class EditChangeVoiceView extends LinearLayout {
    private RecyclerView mRvVoiceList;
    private VoiceAdapter mAdapter;
    private ImageView mIvConfirm;
    private BottomEventListener mEventListener;

    public EditChangeVoiceView(Context context) {
        this(context, null);
    }

    public EditChangeVoiceView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public EditChangeVoiceView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
        initData();
        initListener();
    }

    private void initView() {
        setOrientation(VERTICAL);
        View rootView = LayoutInflater.from(getContext()).inflate(R.layout.view_edit_chang_voice, this);
        mRvVoiceList = rootView.findViewById(R.id.recycleView);
        mIvConfirm = rootView.findViewById(R.id.iv_confirm);
        TextView tvContent = rootView.findViewById(R.id.tv_content);
        tvContent.setText(R.string.sub_menu_audio_edit_change_voice);
    }

    private void initData() {
        LinearLayoutManager layoutManager = new LinearLayoutManagerWrapper(getContext(), LinearLayoutManager.HORIZONTAL, false);
        mRvVoiceList.setLayoutManager(layoutManager);
        mRvVoiceList.addItemDecoration(new ItemDecoration(0, SizeUtils.dp2px(12.5f)));
        mAdapter = new VoiceAdapter();
        mRvVoiceList.setAdapter(mAdapter);
    }

    private void initListener() {
        mIvConfirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mEventListener != null) {
                    mEventListener.onDismiss(true);
                }
            }
        });
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                mAdapter.selected(position);
                if (mEventListener != null) {
                    mEventListener.onItemClick(mAdapter.getItem(position), false);
                }
            }
        });
    }

    /**
     * 设置事件监听
     * Sets the listener
     *
     * @param listener the listener
     */
    public void setListener(BottomEventListener listener) {
        mEventListener = listener;
    }

    /**
     * 设置变声数据列表
     * Sets the change voice data
     *
     * @param list the list
     */
    public void setData(List<AudioFxInfo> list) {
        mAdapter.setNewData(list);
    }

    /**
     * Sets selected position.
     *
     * @param desc the desc
     */
    public void setSelectedPosition(String desc) {
        if (mAdapter != null) {
            mAdapter.setSelectedPosition(desc);
        }
    }

    private static class VoiceAdapter extends BaseQuickAdapter<AudioFxInfo, BaseViewHolder> {
        private int mSelectedPosition = -1;

        private VoiceAdapter() {
            super(R.layout.view_change_voice_item);
        }

        /**
         * 选中某一项
         * Selected item .
         *
         * @param position The index of list
         */
        public void selected(int position) {
            if (mSelectedPosition >= 0) {
                notifyItemChanged(mSelectedPosition);
            }
            mSelectedPosition = position;
            if (position >= 0 && position < getData().size()) {
                notifyItemChanged(position);
            }
        }

        /**
         * Sets selected position.
         *
         * @param desc the desc
         */
        public void setSelectedPosition(String desc) {
            if (TextUtils.isEmpty(desc)) {
                selected(0);
                return;
            }
            for (int i = 1; i < getData().size(); i++) {
                AudioFxInfo fxInfo = getData().get(i);
                if (desc.equals(fxInfo.getEffectId())) {
                    selected(i);
                    break;
                }
            }
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, AudioFxInfo item) {
            ImageView view = helper.getView(R.id.iv_cover);
            ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
            if (helper.getAdapterPosition() == 0) {
                layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT;
                layoutParams.width = SizeUtils.dp2px(23);
            } else {
                layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT;
                layoutParams.width = ViewGroup.LayoutParams.WRAP_CONTENT;
            }
            view.setLayoutParams(layoutParams);

            helper.setImageResource(R.id.iv_cover, item.getCoverId());
            helper.setText(R.id.tv_name, item.getName());
            helper.setVisible(R.id.v_mask, mSelectedPosition == helper.getAdapterPosition());
        }
    }
}
