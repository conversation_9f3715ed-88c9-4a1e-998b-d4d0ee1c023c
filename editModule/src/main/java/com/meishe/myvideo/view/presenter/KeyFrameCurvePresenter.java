package com.meishe.myvideo.view.presenter;

import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.meishe.base.model.Presenter;
import com.meishe.base.utils.GsonUtils;
import com.meishe.base.utils.ResourceUtils;
import com.meishe.engine.bean.CurveAdjustData;
import com.meishe.myvideo.view.interf.CurveKeyFrameView;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/8/9 14:06
 * @Description :关键帧曲线表示类 The key frame curve presenter
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class KeyFrameCurvePresenter extends Presenter<CurveKeyFrameView> {

    public void loadData() {
        String jsonText = ResourceUtils.readAssets2String("curve_key_frame/curve_adjust.json");
        if (TextUtils.isEmpty(jsonText)) {
            return;
        }
        List<CurveAdjustData> dataList = GsonUtils.fromJson(jsonText, new TypeToken<List<CurveAdjustData>>() {
        }.getType());
        getView().onDataBack(dataList);
    }

    public void confirm() {

    }

    public void onItemClicked(CurveAdjustData item) {

    }
}
