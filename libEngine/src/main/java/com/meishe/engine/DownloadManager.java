package com.meishe.engine;

import android.text.TextUtils;

import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.observer.DownLoadObserver;
import com.meishe.engine.observer.DownloadObservable;
import com.meishe.logic.utils.NvsServerClient;
import com.meishe.net.custom.SimpleDownListener;
import com.meishe.net.model.Progress;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/11 14:45
 * @Description :批量下载管理类 The batch download manager.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class DownloadManager {

    private float mMaxProgress = 100;

    private DownloadObservable mObservable;
    private Map<String, DownloadTask> mDownloadTaskMap = new HashMap<>();

    /**
     * Instantiates a new Cloud download manager.
     */
    public DownloadManager() {
        mObservable = new DownloadObservable();
    }

    /**
     * Register download observer.
     * 注册下载监听
     *
     * @param observer the observer
     */
    public void registerDownloadObserver(DownLoadObserver observer) {
        mObservable.registerObserver(observer);
    }

    /**
     * Unregister download observer.
     * 解注册下载监听
     *
     * @param observer the observer
     */
    public void unRegisterDownloadObserver(DownLoadObserver observer) {
        try {
            mObservable.unregisterObserver(observer);
        } catch (Exception e) {}
    }

    /**
     * Sets max progress.
     * 设置最大下载进度
     *
     * @param maxProgress the max progress 最大下载进度
     */
    public void setMaxProgress(int maxProgress) {
        this.mMaxProgress = maxProgress;
    }

    /**
     * Download file.
     * 下载文件
     *
     * @param downloadParam the download param 下载参数
     */
    public <T> void downloadFile(DownloadParam<T> downloadParam, int maxProgress) {
        DownloadTask downloadTask = new DownloadTask(downloadParam).registerDownloadObserver(new InnerObserver<T>());
        downloadTask.mMaxProgress = maxProgress;
        String tag = downloadTask.start();
        mDownloadTaskMap.put(tag, downloadTask);
    }

    /**
     * Download file.
     * 下载文件
     *
     * @param downloadParam the download param 下载参数
     * @param observer      the observer 监听
     */
    public <T> void downloadFile(DownloadParam<T> downloadParam, int maxProgress, DownLoadObserver<T> observer) {
        DownloadTask downloadTask = new DownloadTask(downloadParam).registerDownloadObserver(observer);
        downloadTask.mMaxProgress = maxProgress;
        String tag = downloadTask.start();
        mDownloadTaskMap.put(tag, downloadTask);
    }

    /**
     * Cancel download.
     * 取消上传
     * @param uuidList the uuid list
     */
    public void cancelDownload(List<String> uuidList) {
        for (String uuid : uuidList) {
            DownloadTask downloadTask = mDownloadTaskMap.get(uuid);
            if (downloadTask != null) {
                downloadTask.unRegisterDownloadObserver();
                downloadTask.cancelAll();
            }
        }
    }

    /**
     * Cancel all download.
     * 取消所有上传
     */
    public void cancelAllDownload() {
        if (!CommonUtils.isEmpty(mDownloadTaskMap)) {
            Set<Map.Entry<String, DownloadTask>> entries = mDownloadTaskMap.entrySet();
            for (Map.Entry<String, DownloadTask> entry : entries) {
                DownloadTask downloadTask = entry.getValue();
                if (downloadTask != null) {
                    downloadTask.unRegisterDownloadObserver();
                    downloadTask.cancelAll();
                }
            }
        }
    }

    /**
     * The type Download task.
     * 下载任务，支持批量下载
     */
    public static class DownloadTask {
        private float mMaxProgress = 100;
        private final DownloadParam mDownloadParam;

        private final DownloadObservable mObservable;
        private DownLoadObserver mObserver;
        private final List<String> mAllTags = new ArrayList<>();

        /**
         * Instantiates a new Download task.
         *
         * @param downloadParam the download param
         */
        public DownloadTask(DownloadParam downloadParam) {
            mDownloadParam = downloadParam;
            mObservable = new DownloadObservable();
        }

        /**
         * Register download observer download task.
         * 注册下载任务监听
         *
         * @param observer the observer 监听
         * @return the download task 下载任务
         */
        public DownloadTask registerDownloadObserver(DownLoadObserver observer) {
            mObservable.registerObserver(observer);
            mObserver = observer;
            return this;
        }

        /**
         * Unregister download observer.
         * 解注册下载任务
         */
        public void unRegisterDownloadObserver() {
            if (mObserver != null) {
                mObservable.unregisterObserver(mObserver);
            }
        }

        private void downloadFile() {
            if (mDownloadParam == null) {
                return;
            }
            Map<String, Param> params = mDownloadParam.params;
            if (CommonUtils.isEmpty(params)) {
                return;
            }
            int needDownload = 0;
            Set<Map.Entry<String, Param>> entries1 = params.entrySet();
            for (Map.Entry<String, Param> entry : entries1) {
                if (!entry.getValue().finish) {
                    needDownload++;
                }
            }
            final String downloadTag = mDownloadParam.tag;
            if (needDownload == 0) {
                needDownload = 1;
            }
            final float itemProgress = mMaxProgress / needDownload;
            final Set<Map.Entry<String, Param>> entries = entries1;

            List<Param> needDownloadParam = new ArrayList<>();

            for (Map.Entry<String, Param> entry : entries) {
                Param param = entry.getValue();
                if (param.finish) {
                    continue;
                }
                if (TextUtils.isEmpty(param.url)) {
                    continue;
                }
                String[] split = param.url.split("/");
                String fileName = null;
                if (split.length > 0) {
                    fileName = split[split.length - 1];
                }
                if (!TextUtils.isEmpty(fileName)) {
                    String filePath = param.dstFile + File.separator + fileName;
                    if (new File(filePath).exists()) {
                        param.dstFile = filePath;
                        param.finish = true;
                        param.progress = (int) itemProgress;
                        LogUtils.d("The file already exists and does not need to be downloaded!");
                        continue;
                    }
                }
                needDownloadParam.add(param);
            }
            if (needDownloadParam.isEmpty()) {
                mObservable.notifyProgress(downloadTag, 100);
                mObservable.notifySuccess(downloadTag, mDownloadParam);
            } else {
                for (final Param param : needDownloadParam) {
                    //tag加盐，防止误停其它下载 Tag with salt to prevent accidental stopping of other downloads.
                    String itemTag = param.url + downloadTag;
                    mAllTags.add(itemTag);
                    NvsServerClient.get().download(itemTag, param.url, param.dstFile, param.fileName, new SimpleDownListener(param.url) {
                        @Override
                        public void onFinish(File file, Progress progress) {
                            param.finish = true;
                            param.dstFile = file.getAbsolutePath();
                            for (Map.Entry<String, Param> stringParamEntry : entries) {
                                if (!stringParamEntry.getValue().finish) {
                                    return;
                                }
                            }
                            mObservable.notifySuccess(downloadTag, mDownloadParam);
                        }

                        @Override
                        public void onProgress(Progress progress) {
                            if (progress.totalSize <= 0) {
                                return;
                            }
                            param.progress = (int) (itemProgress * progress.currentSize / progress.totalSize);
                            int allProgress = 0;
                            Map<String, Param> params = mDownloadParam.params;
                            for (Map.Entry<String, Param> stringParamEntry : params.entrySet()) {
                                Param value = stringParamEntry.getValue();
                                allProgress += value.progress;
                            }
                            LogUtils.d("onProgress: " + allProgress + ",progress.currentSize/ progress.totalSize =  " + progress.currentSize + "/" + progress.totalSize);
                            mObservable.notifyProgress(downloadTag, allProgress);
                        }

                        @Override
                        public void onError(Progress progress) {
                            mObservable.notifyFailed(downloadTag);
                            cancelAll();
                        }
                    });
                }
            }
        }

        /**
         * Cancel all.
         * 取消下载
         */
        public void cancelAll() {
            for (String tag : mAllTags) {
                NvsServerClient.get().cancelRequest(tag);
            }
        }

        /**
         * Start string.
         * 开始下载
         *
         * @return the string 下载的tag信息
         */
        public String start() {
            downloadFile();
            return mDownloadParam.tag;
        }
    }

    /**
     * The type Download param.
     * 下载信息
     *
     * @param <T> the type parameter 下载信息携带的参数化对象
     */
    public static class DownloadParam<T> {
        /**
         * The Params.
         * 参数
         */
        Map<String, Param> params = new HashMap<>();
        /**
         * The Tag.
         * 下tag
         */
        String tag;

        /**
         * The Attachment.
         * 附件信息
         */
        T attachment;

        /**
         * Sets attachment.
         * 设置附件信息
         *
         * @param attachment the attachment
         */
        public void setAttachment(T attachment) {
            this.attachment = attachment;
        }

        /**
         * Gets attachment.
         * 获取附件信息
         *
         * @return the attachment
         */
        public T getAttachment() {
            return attachment;
        }

        /**
         * Instantiates a new Download param.
         *
         * @param tag the tag
         */
        public DownloadParam(String tag) {
            this.tag = tag;
        }

        /**
         * Sets tag.
         * 设置tag
         *
         * @param tag the tag
         */
        public void setTag(String tag) {
            this.tag = tag;
        }

        /**
         * Append param.
         * 添加参数
         *
         * @param param the param 参数
         */
        public void appendParam(Param param) {
            params.put(param.url, param);
        }

        /**
         * Append param.
         * 添加参数
         *
         * @param url     the url 下载路径
         * @param dstPath the dst path 下载位置
         */
        public void appendParam(String url, String fileName, String dstPath) {
            params.put(url, new Param(url, dstPath, fileName));
        }

        /**
         * Append param.
         * 添加参数
         *
         * @param url     the url 下载路径
         * @param dstPath the dst path 下载位置
         */
        public void appendParam(String url, String dstPath) {
            params.put(url, new Param(url, dstPath, ""));
        }

        /**
         * Clear data.
         * 清除数据
         */
        public void clearData() {
            params.clear();
        }

        /**
         * Get param param.
         * 获取参数
         *
         * @param key the key 参数key
         * @return the param 参数
         */
        public Param getParam(String key) {
            return params.get(key);
        }

        /**
         * Gets params.
         * 获取所有参数
         *
         * @return the params
         */
        public Map<String, Param> getParams() {
            return params;
        }
    }

    /**
     * The type Param.
     */
    public static class Param {
        /**
         * The Tag.
         * 参数tag
         */
        public String tag;
        /**
         * The Progress.
         * 进度
         */
        public int progress;
        /**
         * The Finish.
         * 是否完成
         */
        public boolean finish;
        /**
         * The Success.
         * 是否成功
         */
        public boolean success;
        /**
         * url
         * the url
         */
        public String url;
        /**
         * 生成文件路径
         * The path to the generated file
         */
        public String dstFile;

        /**
         * 生成名称
         * The name of file
         */
        public String fileName;

        public Map<String, String> extendData;

        /**
         * Instantiates a new Param.
         *
         * @param url     the url
         * @param dstFile the dst file
         */
        public Param(String url, String dstFile) {
            this.tag = url;
            this.url = url;
            this.dstFile = dstFile;
        }

        /**
         * Instantiates a new Param.
         *
         * @param url      the url
         * @param dstFile  the dst file
         * @param name     the file name
         */
        public Param(String url, String dstFile, String name) {
            this(url, dstFile);
            this.fileName = name;
        }

        /**
         * Add extend data.
         * 添加扩展参数
         * @param key  the key
         * @param vale the vale
         */
        public void addExtendData(String key, String vale) {
            if (extendData == null) {
                extendData = new HashMap<>();
            }
            extendData.put(key, vale);
        }

        /**
         * Gets extend data.
         * 获取扩展参数
         * @param key the key
         * @return the extend data
         */
        public String getExtendData(String key) {
            return extendData != null ? extendData.get(key) : "";
        }
    }

    /**
     * The type Inner observer.
     */
    class InnerObserver<T> extends DownLoadObserver<T> {
        @Override
        public void onFailed(String tag) {
            mObservable.notifyFailed(tag);
            DownloadTask task = mDownloadTaskMap.remove(tag);
            if (task != null) {
                task.unRegisterDownloadObserver();
            }
            mObservable.notifyStateChanged(mDownloadTaskMap.size());
        }

        @Override
        public void onProgress(String tag, int progress) {
            progress = (int) (progress * (mMaxProgress / 100F));
            mObservable.notifyProgress(tag, progress);
        }

        @Override
        public void onSuccess(String tag, DownloadParam<T> param) {
            mObservable.notifySuccess(tag, param);
            DownloadTask task = mDownloadTaskMap.remove(tag);
            if (task != null) {
                task.unRegisterDownloadObserver();
            }
            mObservable.notifyStateChanged(mDownloadTaskMap.size());
        }
    }
}
