package com.meishe.engine.bean;

import android.text.TextUtils;

import com.meishe.base.utils.LogUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/7/3 17:10
 */
public class NvsObject<T> implements Cloneable, Serializable {
    /**
     * SDK的底层对象
     * The native object
     */
    private transient T mObject;

    /**
     * 附带参数
     * The attachment
     */
    protected Map<String, Object> attachment = new HashMap<>();

    private String extraTag;

    /**
     * 创建的对象标记
     * The tag of created object
     */
    private String createTag;

    NvsObject() {
    }

    NvsObject(T object) {
        mObject = object;
        generateCreateTag();
    }

    T getObject() {
        return mObject;
    }

    void setObject(T object) {
        mObject = object;
    }

    public long getInPoint() {
        return 0;
    }

    public long getOutPoint() {
        return 0;
    }

    public String getExtraTag() {
        return extraTag;
    }

    public void setExtraTag(String extraTag) {
        this.extraTag = extraTag;
    }

    public void setAttachment(String key, Object vale) {
        if (TextUtils.isEmpty(key) || vale == null) {
            LogUtils.e("Key or value is null!");
            return;
        }
        T object = getObject();
        if (object instanceof com.meicam.sdk.NvsObject) {
            ((com.meicam.sdk.NvsObject)object).setAttachment(key, vale);
        }
        attachment.put(key, vale);
    }

    public Object getAttachment(String key) {
        return attachment.get(key);
    }

    /**
     * 生成创建标记
     * Generate the create tag
     */
    protected void generateCreateTag() {
        createTag = System.nanoTime() + "";
    }

    public String getCreateTag() {
        return createTag;
    }

    protected void setCreateTag(String createTag) {
        this.createTag = createTag;
    }

    public boolean sameCreateTag(String createTag) {
        return !TextUtils.isEmpty(createTag) && TextUtils.equals(createTag, this.createTag);
    }

    void loadData() {
    }
}
