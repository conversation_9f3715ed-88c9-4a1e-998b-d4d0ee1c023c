package com.meishe.myvideo.ui.trackview;

import android.content.Context;
import android.graphics.Bitmap;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.meicam.sdk.NvsIconGenerator;
import com.meishe.base.utils.ImageLoader;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.myvideo.R;
import com.meishe.myvideo.ui.bean.BaseTrackClip;
import com.meishe.myvideo.ui.bean.ITrackClip;

import java.util.ArrayList;
import java.util.List;


/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/3/9 15:28
 * @Description :拖拽缩略图 Drag and drop view of thumbnails
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class DragThumbnailView extends FrameLayout implements NvsIconGenerator.IconCallback {
    private final String TAG = "DragThumbnailView";
    private FrameLayout mFlItemContainer;
    private OnMoveListener mListener;
    private List<ImageView> mThumbnailList;
    private List<Task> mTaskList;
    private int mThumbnailWidth;
    private int mScreenWidth;
    private NvsIconGenerator mIconGenerator;
    private float mTranslationX;
    private float mLastTouchX = -1;
    private float mDownTranslationX = -1;
    private float mMoveX;
    private boolean mMoveToLeft;
    private int mLastExchangePosition = -1;
    private int mRealExchangePosition = -1;
    private boolean mLastExchangeToSmall;
    private boolean mCanAutoMove = false;
    private int mViewMargin;

    public DragThumbnailView(@NonNull Context context) {
        this(context, null);
    }

    public DragThumbnailView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public DragThumbnailView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        mThumbnailList = new ArrayList<>();
        mScreenWidth = ScreenUtils.getScreenWidth();
        mThumbnailWidth = (int) getResources().getDimension(R.dimen.dp_px_150);
        mViewMargin = (int) getResources().getDimension(R.dimen.dp_px_10);
        mFlItemContainer = new FrameLayout(getContext());
        LayoutParams containerParams = new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.MATCH_PARENT);
        //mFlItemContainer.setBackgroundColor(getResources().getColor(R.color.white_5));
        addView(mFlItemContainer, containerParams);
    }

    /**
     * 设置宽度
     * Set width
     *
     * @param width the width 宽度
     */
    public void setWidth(int width) {
        Log.d(TAG, "width=" + width);
        ViewGroup.LayoutParams layoutParams = getLayoutParams();
        layoutParams.width = width;
        setLayoutParams(layoutParams);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if (!isInEditMode()) {
            checkIconGenerator();
        }
    }

    /**
     * 检查图片创建者
     * Check icon generator
     */
    private void checkIconGenerator() {
        if (mIconGenerator == null) {
            mIconGenerator = new NvsIconGenerator();
            mIconGenerator.setIconCallback(this);
        }
    }

    @Override
    public void onIconReady(Bitmap bitmap, long timestamp, final long taskId) {
        for (int i = 0; i < mTaskList.size(); i++) {
            Task task = mTaskList.get(i);
            if (task.taskId == taskId) {
                task.imageView.setImageBitmap(bitmap);
                mTaskList.remove(i);
                break;
            }
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        if (mIconGenerator != null) {
            mIconGenerator.setIconCallback(null);
            mIconGenerator.release();
            mIconGenerator = null;
        }
        if (mTaskList != null) {
            mTaskList.clear();
        }
        super.onDetachedFromWindow();
    }

    /**
     * 设置轨道视频片段集合
     * Sets the collection of thumbnail image segments
     *
     * @param trackClipList the track clip list 主轨道视频片段集合
     */
    public void setThumbnailList(List<ITrackClip> trackClipList) {
        if (trackClipList != null) {
            checkIconGenerator();
            int size = trackClipList.size();
            mFlItemContainer.setTranslationX(0);
            mFlItemContainer.removeAllViews();
            if (mTaskList == null) {
                mTaskList = new ArrayList<>();
            } else {
                mTaskList.clear();
            }
            mThumbnailList.clear();
            for (int i = 0; i < size; i++) {
                ITrackClip trackClip = trackClipList.get(i);
                if (BaseTrackClip.CLIP_HOLDER.equals(trackClip.getType())) {
                    continue;
                }
                ImageView imageView;
                if (i == size - 1) {
                    imageView = addView(trackClip, (mThumbnailWidth + mViewMargin) * i + mScreenWidth / 2, mScreenWidth / 2);
                } else {
                    imageView = addView(trackClip, (mThumbnailWidth + mViewMargin) * i + mScreenWidth / 2, 0);
                }
                //imageView.setTag(i);
                mThumbnailList.add(imageView);
            }
            mSelectedPosition = -1;
            mSelectedView = null;
        }
    }

    /**
     * 添加视图
     * Add view
     *
     * @param trackClip   the track clip 轨道片段
     * @param leftMargin  left margin 左间隔
     * @param rightMargin right margin 右间隔
     */
    private ImageView addView(ITrackClip trackClip, int leftMargin, int rightMargin) {
        ImageView imageView = new ImageView(getContext());
        imageView.setScaleType(ImageView.ScaleType.FIT_XY);
        LayoutParams layoutParams = new LayoutParams(mThumbnailWidth, LayoutParams.MATCH_PARENT);
        layoutParams.leftMargin = leftMargin;
        layoutParams.rightMargin = rightMargin;
        mFlItemContainer.addView(imageView, layoutParams);
        ITrackClip.ThumbNailInfo thumbNailInfo = trackClip.getThumbNailInfo();
        if (thumbNailInfo != null) {
            //假设要获取第is的缩略图, %07d 表示最少7位，不足补0 ，d表示正整数
            // Suppose you want to get the thumbnail of the is,% 07d represents at least 7 digits, and if it is not enough, fill in 0, and d represents a positive integer.
            //url = urlPrefix + "-" + String.format("%07d",interval * i) + "." + extension;
            int i = (int) (trackClip.getTrimIn() / thumbNailInfo.interval / 1000);
            String url = thumbNailInfo.urlPrefix + "-" + String.format("%07d",thumbNailInfo.interval * i) +"."+thumbNailInfo.extension;
            ImageLoader.loadUrl(getContext(), url, imageView);
        } else {
            Bitmap bitmap = mIconGenerator.getIconFromCache(trackClip.getAssetPath(), trackClip.getTrimIn(), 0);
            if (bitmap != null) {
                imageView.setImageBitmap(bitmap);
            } else {
                Task task = new Task();
                task.taskId = mIconGenerator.getIcon(trackClip.getAssetPath(), trackClip.getTrimIn(), 0);
                task.imageView = imageView;
                mTaskList.add(task);
            }
        }
        return imageView;
    }

    /**
     * 添加缩略图片段
     * Add thumbnail clip
     *
     * @param index     int Location index to which to add 添加到的位置索引
     * @param trackClip ITrackClip The track clip to add 要添加的片段
     */
    public void addThumbnail(int index, ITrackClip trackClip) {
        if (trackClip == null || BaseTrackClip.CLIP_HOLDER.equals(trackClip.getType())) {
            return;
        }
        List<ITrackClip> list = new ArrayList<>(1);
        list.add(trackClip);
        addThumbnail(index, list);
    }

    /**
     * 添加缩略图片段
     * Add thumbnail clip
     *
     * @param index         int Location index to which to add 添加到的位置索引
     * @param trackClipList List<ITrackClip> The track clip list to add 要添加的片段集合
     */
    public void addThumbnail(int index, List<ITrackClip> trackClipList) {
        if (index < 0 || index > mThumbnailList.size()) {
            Log.e(TAG, "addThumbnail,illegal index,check it");
            return;
        }
        int extraIndex = mThumbnailList.size();
        ImageView imageView;
        if (index == extraIndex) {
            /*更改最后一个view的右边距
            * Change the right margin of the last view
            * */
            if (mThumbnailList.size() > 0) {
                imageView = mThumbnailList.get(extraIndex - 1);
                LayoutParams layoutParams = (LayoutParams) imageView.getLayoutParams();
                layoutParams.rightMargin = 0;
                imageView.setLayoutParams(layoutParams);
            }
        } else {
            extraIndex = index;
        }
        /*添加新的view
        * Add a new view
        * */
        List<ImageView> tempList = new ArrayList<>();
        for (int i = 0; i < trackClipList.size(); i++) {
            ITrackClip trackClip = trackClipList.get(i);
            /*
             * 过滤补黑片段
             * Filter black patch
             * */
            if (!BaseTrackClip.CLIP_HOLDER.equals(trackClip.getType())) {
                imageView = addView(trackClip, (mThumbnailWidth + mViewMargin) * (i + extraIndex) + mScreenWidth / 2, 0);
                // imageView.setTag((i + extraIndex) + (mThumbnailWidth * (i + extraIndex) + mScreenWidth / 2)+"");
                tempList.add(imageView);
            }
        }
        int tempIndex = tempList.size();
        if (tempIndex <= 0) {
            if (index == mThumbnailList.size() && index > 0) {
                /*还原最后一个view的右边距
                * Restore the right margin of the last view
                * */
                imageView = mThumbnailList.get(extraIndex - 1);
                LayoutParams layoutParams = (LayoutParams) imageView.getLayoutParams();
                layoutParams.rightMargin = mScreenWidth / 2;
                imageView.setLayoutParams(layoutParams);
            }
            return;
        }
        /*更改插入索引后边的view的leftMargin
        * Change the leftMargin of the view inserted after the index
        * */
        for (int i = index; i < mThumbnailList.size(); i++) {
            imageView = mThumbnailList.get(i);
            LayoutParams layoutParams = (LayoutParams) imageView.getLayoutParams();
            layoutParams.leftMargin = mThumbnailWidth * (i + tempIndex) + mScreenWidth / 2;
            // imageView.setTag((i + extraIndex) + layoutParams.leftMargin+"");
            imageView.setLayoutParams(layoutParams);
        }
        //新添加的view加入到集合中
        // Add the newly added view to the collection
        mThumbnailList.addAll(extraIndex, tempList);
        if (mThumbnailList.size() > 0) {
            //更改最后一个view的右边距
            // Change the right margin of the last view
            imageView = mThumbnailList.get(mThumbnailList.size() - 1);
            LayoutParams layoutParams = (LayoutParams) imageView.getLayoutParams();
            layoutParams.rightMargin = mScreenWidth / 2;
            imageView.setLayoutParams(layoutParams);
        }
       /* for (int i = 0; i < mThumbnailList.size(); i++) {
            Log.d(TAG,"tag="+mThumbnailList.get(i).getTag()+",index="+i);
        }*/
    }

    /**
     * 删除缩略图片段
     * Delete thumbnail clip
     *
     * @param index int  Dropped index 删除的索引
     */
    public void deleteThumbnail(int index) {
        if (index >= 0 && index < mThumbnailList.size()) {
            ImageView imageView = mThumbnailList.remove(index);
            /*这里不能使用索引删除，因为imageView在mThumbnailList中的索引和在mFlItemContainer中的索引不同
            * Index deletion cannot be used here because the index of imageView in mThumbnailList is different from that in mFlItemContainer
            * */
            mFlItemContainer.removeView(imageView);
            int count = mThumbnailList.size();
            for (int i = index; i < count; i++) {
                imageView = mThumbnailList.get(i);
                LayoutParams layoutParams = (LayoutParams) imageView.getLayoutParams();
                layoutParams.leftMargin = mThumbnailWidth * i + mScreenWidth / 2;
                if (i == count - 1) {
                    layoutParams.rightMargin = mScreenWidth / 2;
                }
                //imageView.setTag(i + tempIndex);
                imageView.setLayoutParams(layoutParams);
            }
        }
    }

    /**
     * 设置移动监听
     * Set move listener
     *
     * @param listener the listener 监听者
     */
    public void setOnMoveListener(OnMoveListener listener) {
        mListener = listener;
    }

    private View mSelectedView;
    private int[] mOldLocal;
    private int mSelectedPosition = -1;

    /**
     * 设置选中
     *
     * @param index the selected index 选中的索引
     * @param downX the downX  按下的x坐标
     */
    public void selected(int index, float downX) {
        //Log.d(TAG, "index=" + index + ",mSelectedPosition=" + mSelectedPosition);
        if (index == -1) {
            mSelectedView = null;
            return;
        }
        if (index == mSelectedPosition || index >= mThumbnailList.size()) {
            return;
        }
        mSelectedView = mThumbnailList.get(index);
        if (mOldLocal == null) {
            mOldLocal = new int[2];
        }
        mLastTouchX = -1;
        mSelectedPosition = index;
        if (mSelectedView != null) {
            mSelectedView.getLocationInWindow(mOldLocal);
            exchangePrepare(downX);
        }
    }

    /**
     * 处理触摸事件
     * Handle touch event
     *
     * @param event the motion event 触摸事件
     */
    public boolean onHandleTouchEvent(MotionEvent event) {
        if (mSelectedView == null) {
            return false;
        }
        //Log.d(TAG, "onHandleTouchEvent,onTouch,event=" + event);
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            /*基本上不会接收到ACTION_DOWN事件
            * Basically, you won't receive ACTION_ DOWN event
            * */
            exchangePrepare(event.getRawX());
        } else if (event.getAction() == MotionEvent.ACTION_UP) {
            dealRealExchange();
            setTranslationX(0);
            mTranslationX = 0;
            mFlItemContainer.setTranslationX(0);
            mLastTouchX = -1;
            mSelectedPosition = -1;
            mLastExchangePosition = -1;
            mRealExchangePosition = -1;
            mMoveX = 0;
            mCanAutoMove = false;
            mSelectedView = null;
        } else if (event.getAction() == MotionEvent.ACTION_MOVE) {
            //一般情况下不会有ACTION_DOWN，所以处理一下
            // Generally, there will be no ACTION_ DOWN, so deal with it
            exchangePrepare(event.getRawX());
            //float tempX = event.getX();
            float x = event.getRawX();
            float tempDx = x - mLastTouchX;
            mMoveToLeft = tempDx < 0;
            //  Log.d(TAG, "mThumbnailWidth=" + mThumbnailWidth + ",mScreenWidth=" + mScreenWidth + ",x=" + x);
            if (x < mThumbnailWidth && mMoveToLeft || x + mThumbnailWidth > mScreenWidth && !mMoveToLeft) {
                if (!mCanAutoMove) {
                    mCanAutoMove = true;
                    post(mAutoMoveRunnable);
                }
                return true;
            }
            mCanAutoMove = false;
            removeCallbacks(mAutoMoveRunnable);
            mMoveX += tempDx;
            dealMove(getNextExchangePosition());
            mLastTouchX = x;
        }
        return true;
    }

    /**
     * 处理移动
     * Deal with move
     *
     * @param nextExchangePosition the next exchange position 下一个要交换的位置
     */
    private void dealMove(int nextExchangePosition) {
        //Log.d(TAG, "nextExchangePosition=" + nextExchangePosition + ",mThumbnailWidth=" + mThumbnailWidth + ",mTranslationX=" + mTranslationX);
        if (nextExchangePosition >= 0 && nextExchangePosition < mThumbnailList.size()) {
            float nextExchangeX;
            if (nextExchangePosition != mLastExchangePosition) {
                nextExchangeX = getNextExchangeX(nextExchangePosition);
                if (nextExchangePosition < mSelectedPosition) {
                    if (mMoveX <= nextExchangeX) {
                        //交换
                        //exchange
                        dealExchange(nextExchangePosition, true);
                        mLastExchangePosition = nextExchangePosition;
                        mRealExchangePosition = nextExchangePosition;
                        mLastExchangeToSmall = true;
                        // Log.d(TAG, "向x轴减小方向移动，交换，position=" + nextExchangePosition);
                    }
                } else if (nextExchangePosition > mSelectedPosition) {
                    if (mMoveX >= nextExchangeX) {
                        //交换
                        //exchange
                        dealExchange(nextExchangePosition, true);
                        mLastExchangePosition = nextExchangePosition;
                        mRealExchangePosition = nextExchangePosition;
                        mLastExchangeToSmall = false;
                        //Log.d(TAG, "向x轴增大方向移动，交换，position=" + nextExchangePosition);
                    }
                }
            } else {
                if (mMoveToLeft && !mLastExchangeToSmall) {
                    //向x轴增大方向交换后，换方向了
                    // After exchanging in the increasing direction of the x-axis,
                    // the direction is changed
                    nextExchangeX = getNextExchangeX(nextExchangePosition);
                    // Log.d(TAG, "tempDx < 0 ,nextExchangeX=" + nextExchangeX + ",mMoveX=" + mMoveX + ",nextPosition=" + nextExchangePosition);
                    if (mMoveX <= nextExchangeX) {
                        //交换
                        //exchange
                        mLastExchangeToSmall = true;
                        if (nextExchangePosition > 0) {
                            mRealExchangePosition = nextExchangePosition - 1;
                        }
                        dealExchange(nextExchangePosition, false);
                        // Log.d(TAG, "换方向了，上次方向是向x轴增大方向，交换，nextExchangePosition=" + nextExchangePosition + ",mRealExchangePosition=" + mRealExchangePosition);
                    } else {
                        mRealExchangePosition = nextExchangePosition;
                    }
                } else if (!mMoveToLeft && mLastExchangeToSmall) {
                    //向x轴减小方向交换后，换方向了
                    // After exchanging in the decreasing direction of the x-axis,
                    // the direction is changed
                    nextExchangeX = getNextExchangeX(nextExchangePosition);
                    //Log.d(TAG, "tempDx > 0 ,nextExchangeX=" + nextExchangeX + ",mMoveX=" + mMoveX + ",nextPosition=" + nextExchangePosition);
                    if (mMoveX >= nextExchangeX) {
                        //交换
                        //exchange
                        mLastExchangeToSmall = false;
                        if (nextExchangePosition < mThumbnailList.size() - 1) {
                            mRealExchangePosition = nextExchangePosition + 1;
                        }
                        dealExchange(nextExchangePosition, false);
                        //Log.d(TAG, "换方向了，上次方向是向x轴减小方向，交换，nextExchangePosition=" + nextExchangePosition + ",mRealExchangePosition=" + mRealExchangePosition);
                    } else {
                        mRealExchangePosition = nextExchangePosition;
                    }
                }
            }
        }
        mSelectedView.setTranslationX(mMoveX);
    }

    /**
     * 交换准备
     * Prepare to exchange
     *
     * @param downX float the down x 按下的x坐标
     */
    private void exchangePrepare(float downX) {
        if (mLastTouchX == -1) {
            mLastTouchX = downX;
            mSelectedView.bringToFront();
            if (mOldLocal != null) {
                float v = mLastTouchX - mOldLocal[0] - mSelectedView.getWidth() / 2f;
                mDownTranslationX = v;
                mFlItemContainer.setTranslationX(v);
                // LogUtils.d("transitionX="+v+",mLastTouchX="+mLastTouchX+",mOldLocal[0]="+mOldLocal[0]+",w="+mSelectedView.getWidth() / 2f);
            }
        }
    }

    /**
     * 获取下一个交换位置的X轴坐标
     * Gets the x-coordinate of the next swapped position 下一个要交换的位置
     */
    private float getNextExchangeX(int nextExchangePosition) {
        float nextExchangeX;
        if (nextExchangePosition < mSelectedPosition) {
            nextExchangeX = (nextExchangePosition - mSelectedPosition) * (mThumbnailWidth + mViewMargin) + (mThumbnailWidth + mViewMargin) / 2f;
        } else {
            nextExchangeX = (nextExchangePosition - mSelectedPosition) * (mThumbnailWidth + mViewMargin) - (mThumbnailWidth + mViewMargin) / 2f;
        }
        return nextExchangeX;
    }

    /**
     * 获取下一个交换位置
     * Gets the next swapped position
     */
    private int getNextExchangePosition() {
        return (int) (mSelectedPosition + (mMoveX > 0 ? Math.ceil(Math.abs(mMoveX / (mThumbnailWidth + mViewMargin))) : -Math.ceil(Math.abs(mMoveX / (mThumbnailWidth + mViewMargin)))));
    }

    /**
     * 处理交换
     * Deal with exchange
     *
     * @param nextExchangePosition the next swapped position 下一个要交换的位置
     * @param change               true will set translationX 真则会被设置x轴平移
     */
    private void dealExchange(int nextExchangePosition, boolean change) {
        View view = mThumbnailList.get(nextExchangePosition);
        if (view != null) {
            if (view.getTranslationX() == 0 || change) {
                view.setTranslationX(nextExchangePosition > mSelectedPosition ? -(mThumbnailWidth + mViewMargin) : (mThumbnailWidth + mViewMargin));
            } else {
                view.setTranslationX(0);
            }
        }
    }

    /**
     * 处理真正的交换
     * Deal with the real exchange
     */
    private void dealRealExchange() {
        if (mSelectedView == null) {
            return;
        }
        //Log.d(TAG, "mSelectedPosition=" + mSelectedPosition + ",mRealExchangePosition=" + mRealExchangePosition);
        int differenceValue = mRealExchangePosition - mSelectedPosition;
        if (differenceValue != 0 && mRealExchangePosition >= 0) {
            boolean canMove = true;
            if (mListener != null) {
                canMove = mListener.onMove(mSelectedPosition, mRealExchangePosition);
            }
            if (canMove) {
                int startIndex;
                int endIndex;
                if (differenceValue > 0) {
                    startIndex = mSelectedPosition + 1;
                    endIndex = mRealExchangePosition + 1;
                    /*+1是为了更改两个交换位置间（不包含选中的view）的布局参数
                    * +1 is to change the layout parameters between two exchange locations (excluding the selected view)
                    * */
                } else {
                    startIndex = mRealExchangePosition;
                    endIndex = mSelectedPosition;
                }
                /*更改两个交换位置间（不包含选中的view）的布局参数
                * Change the layout parameters between two swap locations (excluding the selected view)
                * */
                for (int i = startIndex; i < endIndex; i++) {
                    changeLayoutParams(mThumbnailList.get(i), true);
                }
                /*设置选中的view的布局参数
                * Set the layout parameters of the selected view
                * */
                LayoutParams layoutParams = (LayoutParams) mSelectedView.getLayoutParams();
                layoutParams.leftMargin = layoutParams.leftMargin + differenceValue * (mThumbnailWidth + mViewMargin);
                mSelectedView.setLayoutParams(layoutParams);
                /*更改两个交换位置间对应的索引
                * Change the corresponding index between two swap locations
                * */
                ImageView remove = mThumbnailList.remove(mSelectedPosition);
                mThumbnailList.add(mRealExchangePosition, remove);
                /*重置translationX
                * Reset translationX
                * */
                for (int i = 0; i < mThumbnailList.size(); i++) {
                    changeLayoutParams(mThumbnailList.get(i), false);
                }
                /*更改最后一个view的右边距
                * Change the right margin of the last view
                * */
                ImageView lastView = mThumbnailList.get(mThumbnailList.size() - 1);
                layoutParams = (LayoutParams) lastView.getLayoutParams();
                layoutParams.rightMargin = mScreenWidth / 2;
                lastView.setLayoutParams(layoutParams);

            }
        }
        mSelectedView.setTranslationX(0);
    }

    /**
     * 更改view布局参数
     * Change  layout params of the view
     *
     * @param view               the view 视图
     * @param changeLayoutParams true change layout params .真则要更高布局参数，假则不更新
     */
    private void changeLayoutParams(View view, boolean changeLayoutParams) {
        LayoutParams layoutParams;
        if (view != null) {
            float translationX = view.getTranslationX();
            //Log.d(TAG, "translationX=" + translationX);
            if (translationX != 0) {
                if (changeLayoutParams) {
                    layoutParams = (LayoutParams) view.getLayoutParams();
                    layoutParams.leftMargin = (int) (layoutParams.leftMargin + translationX);
                    view.setLayoutParams(layoutParams);
                }
                view.setTranslationX(0);
            }
        }
    }

    private Runnable mAutoMoveRunnable = new Runnable() {
        @Override
        public void run() {
            int nextExchangePosition = getNextExchangePosition();
            /*屏幕边缘的自动滚动
            * Automatic scrolling of screen edges
            * */
            if (nextExchangePosition >= 0 && nextExchangePosition < mThumbnailList.size()) {
                /*每次移动的距离
                * Distance per move
                * */
                int moveX = mThumbnailWidth / 3;
                if (mMoveToLeft) {
                    mTranslationX += moveX;
                    mMoveX -= moveX;
                } else {
                    mTranslationX -= moveX;
                    mMoveX += moveX;
                }
                /*平移父类布局，使其子view可见
                * Translate the parent class layout to make its child view visible
                * */
                mFlItemContainer.setTranslationX(mTranslationX + mDownTranslationX);
                dealMove(nextExchangePosition);
                postDelayed(this, 100);
            } else {
                mCanAutoMove = false;
            }
        }
    };

    public interface OnMoveListener {
        /**
         * 移动
         * Move
         *
         * @param from from position
         * @param to   to position
         * @return true move ,false not
         */
        boolean onMove(int from, int to);
    }

    private static class Task {
        long taskId;
        ImageView imageView;
    }
}
