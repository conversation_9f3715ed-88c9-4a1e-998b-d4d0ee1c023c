package com.meishe.player.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PointF;
import android.graphics.Rect;
import android.view.MotionEvent;
import android.view.View;

/**
 * author：yangtailin on 2020/7/1 17:15
 */
public class CutRectView extends View {
    private final static String TAG = "MYCutView";
    private final static int RECT_L_T = 1;
    private final static int RECT_L_B = 2;
    private final static int RECT_R_T = 3;
    private final static int RECT_R_B = 4;
    private Rect mDrawRect = new Rect();
    private int mTouchRect = -1;
    private Paint mPaint;
    private Paint mCornerPaint;
    private final static int ANGEL_LENGTH = 30;
    private final static int PADDING = 0;
    private int mPadding = PADDING;
    private int mAngelLength = ANGEL_LENGTH;
    private int mStrokeWidth = 4;
    private OnTransformListener mOnTransformListener;
    private float mOldTouchX = 0;
    private float mOldTouchY = 0;

    private final static int ONE_FINGER = 1;
    private final static int TWO_FINGER = 2;
    private boolean mIsTwoFingerEvent = false;
    private double mTwoFingerStartLength;
    private PointF mTwoFingerOldPoint = new PointF();
    private double mTwoFingerEndLength;

    /**
     * 宽高比，如果是-1，代表自由宽高比
     * Aspect ratio, if -1, represents the free aspect ratio
     */
    private float mWidthHeightRatio = -1;

    /**
     * 触摸区域的范围
     * The range of the touch area
     */
    private final static int TOUCH_RECT_SIZE = 100;

    public CutRectView(Context context) {
        super(context);
        mPaint = new Paint();
        mPaint.setColor(Color.WHITE);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(2);

        mCornerPaint = new Paint();
        mCornerPaint.setColor(Color.WHITE);
        mCornerPaint.setStyle(Paint.Style.STROKE);
        mCornerPaint.setStrokeWidth(6);
        initView();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        mDrawRect.left = mPadding;
        mDrawRect.top = mPadding;
        mDrawRect.bottom = MeasureSpec.getSize(heightMeasureSpec) - mPadding;
        mDrawRect.right = MeasureSpec.getSize(widthMeasureSpec) - mPadding;
    }

    private void initView() {

    }

    @Override
    protected void onDraw(Canvas canvas) {
        Path path = new Path();
        //绘制边框 Draw bound box
        path.moveTo(mDrawRect.left, mDrawRect.top);
        path.lineTo(mDrawRect.right, mDrawRect.top);
        path.lineTo(mDrawRect.right, mDrawRect.bottom);
        path.lineTo(mDrawRect.left, mDrawRect.bottom);
        path.lineTo(mDrawRect.left, mDrawRect.top);
        canvas.drawPath(path, mPaint);

        //绘制中线 Draw center line
        int width = mDrawRect.right - mDrawRect.left;
        int height = mDrawRect.bottom - mDrawRect.top;
        //竖线 Vertical line
        path.moveTo(mDrawRect.left + (width) * 1.0F / 3, mDrawRect.top);
        path.lineTo(mDrawRect.left + (width) * 1.0F / 3, mDrawRect.bottom);
        canvas.drawPath(path, mPaint);

        path.moveTo(mDrawRect.left + (width) * 1.0F / 3 * 2, mDrawRect.top);
        path.lineTo(mDrawRect.left + (width) * 1.0F / 3 * 2, mDrawRect.bottom);
        canvas.drawPath(path, mPaint);

        //横线 horizontal line
        path.moveTo(mDrawRect.left, mDrawRect.top + (height) * 1.0F / 3 * 2);
        path.lineTo(mDrawRect.right, mDrawRect.top + (height) * 1.0F / 3 * 2);
        canvas.drawPath(path, mPaint);

        path.moveTo(mDrawRect.left, mDrawRect.top + (height) * 1.0F / 3);
        path.lineTo(mDrawRect.right, mDrawRect.top + (height) * 1.0F / 3);
        canvas.drawPath(path, mPaint);

        mAngelLength = ANGEL_LENGTH;
        if (mAngelLength > width) {
            mAngelLength = width;
        }
        if (mAngelLength > height) {
            mAngelLength = height;
        }
        //绘制左上角 Draw upper left corner
        path.reset();
        path.moveTo(mDrawRect.left + mAngelLength + mStrokeWidth / 2, mDrawRect.top + mStrokeWidth / 2);
        path.lineTo(mDrawRect.left + mStrokeWidth / 2, mDrawRect.top + mStrokeWidth / 2);
        path.lineTo(mDrawRect.left + mStrokeWidth / 2, mDrawRect.top + mAngelLength + +mStrokeWidth / 2);
        canvas.drawPath(path, mCornerPaint);

        //绘制右上角 Draw upper right corner
        path.moveTo(mDrawRect.right - mAngelLength - mStrokeWidth / 2, mDrawRect.top + mStrokeWidth / 2);
        path.lineTo(mDrawRect.right - mStrokeWidth / 2, mDrawRect.top + mStrokeWidth / 2);
        path.lineTo(mDrawRect.right - mStrokeWidth / 2, mDrawRect.top + mAngelLength + mStrokeWidth / 2);
        canvas.drawPath(path, mCornerPaint);

        //绘制右下角 Draw bottom right corner
        path.moveTo(mDrawRect.right - mStrokeWidth / 2, mDrawRect.bottom - mStrokeWidth / 2 - mAngelLength);
        path.lineTo(mDrawRect.right - mStrokeWidth / 2, mDrawRect.bottom - mStrokeWidth / 2);
        path.lineTo(mDrawRect.right - mStrokeWidth / 2 - mAngelLength, mDrawRect.bottom - mStrokeWidth / 2);
        canvas.drawPath(path, mCornerPaint);

        //绘制左下角 Draw bottom left corner
        path.moveTo(mDrawRect.left + mStrokeWidth / 2, mDrawRect.bottom - mStrokeWidth / 2 - mAngelLength);
        path.lineTo(mDrawRect.left + mStrokeWidth / 2, mDrawRect.bottom - mStrokeWidth / 2);
        path.lineTo(mDrawRect.left + mStrokeWidth / 2 + mAngelLength, mDrawRect.bottom - mStrokeWidth / 2);
        canvas.drawPath(path, mCornerPaint);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        int pointerCount = event.getPointerCount();
        if (pointerCount > TWO_FINGER) {
            return false;
        }

        if (((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_DOWN) && (pointerCount == ONE_FINGER)) {
            mIsTwoFingerEvent = false;
        }

        if (pointerCount == TWO_FINGER) {
            mIsTwoFingerEvent = true;
            return twoFingerTouch(event);
        } else {
            return oneFingerTouch(event);
        }
    }

    public void setWidthHeightRatio(float ratio) {
        this.mWidthHeightRatio = ratio;
    }

    public int getPadding() {
        return mPadding;
    }

    private boolean oneFingerTouch(MotionEvent event) {
        if (mIsTwoFingerEvent) {
            if (event.getAction() == MotionEvent.ACTION_UP) {
                mIsTwoFingerEvent = false;
                mOldTouchX = 0;
                mOldTouchY = 0;
            }
            return false;
        }
        int action = event.getAction();
        float touchX = event.getRawX();
        float touchY = event.getRawY();
        if (action == MotionEvent.ACTION_DOWN) {
            mTouchRect = getTouchRect(event);
            return true;
        } else if (action == MotionEvent.ACTION_MOVE) {
            int eventX = (int) event.getX();
            int eventY = (int) event.getY();
            if (mTouchRect == RECT_L_T) {
                mDrawRect.right = getWidth() - mPadding;
                mDrawRect.bottom = getHeight() - mPadding;
                if (mWidthHeightRatio > 0) {
                    mDrawRect.left = mDrawRect.right - (int) ((mDrawRect.bottom - mDrawRect.top) * 1.0F * mWidthHeightRatio);
                    mDrawRect.top = eventY;
                } else {
                    mDrawRect.left = eventX;
                }
                mDrawRect.top = eventY;
            } else if (mTouchRect == RECT_L_B) {
                mDrawRect.top = mPadding;
                mDrawRect.right = getWidth() - mPadding;

                if (mWidthHeightRatio > 0) {
                    mDrawRect.left = mDrawRect.right - (int) ((mDrawRect.bottom - mDrawRect.top) * 1.0F * mWidthHeightRatio);
                } else {
                    mDrawRect.left = eventX;
                }
                mDrawRect.bottom = eventY;
            } else if (mTouchRect == RECT_R_T) {
                mDrawRect.left = mPadding;
                mDrawRect.bottom = getHeight() - mPadding;
                if (mWidthHeightRatio > 0) {
                    mDrawRect.right = mDrawRect.left + (int) ((mDrawRect.bottom - mDrawRect.top) * 1.0F * mWidthHeightRatio);
                } else {
                    mDrawRect.right = eventX;
                }
                mDrawRect.top = eventY;
            } else if (mTouchRect == RECT_R_B) {
                mDrawRect.left = mPadding;
                mDrawRect.top = mPadding;
                if (mWidthHeightRatio > 0) {
                    mDrawRect.right = mDrawRect.left + (int) ((mDrawRect.bottom - mDrawRect.top) * 1.0F * mWidthHeightRatio);
                } else {
                    mDrawRect.right = eventX;
                }
                mDrawRect.bottom = eventY;
            }
            invalidate();
            mOldTouchX = touchX;
            mOldTouchY = touchY;
        } else if (action == MotionEvent.ACTION_UP) {
            float scale = getWidth() * 1.0F / Math.abs(mDrawRect.right - mDrawRect.left);
            if (mOnTransformListener != null) {
                mOnTransformListener.onTransEnd(scale, new float[]{mDrawRect.right - mDrawRect.left, mDrawRect.bottom - mDrawRect.top});
            }
            mDrawRect.left = mPadding;
            mDrawRect.top = mPadding;
            mDrawRect.right = getWidth() - mPadding;
            mDrawRect.bottom = getHeight() - mPadding;
            mOldTouchX = 0;
            mOldTouchY = 0;
            invalidate();
        }
        return super.onTouchEvent(event);
    }

    private boolean twoFingerTouch(MotionEvent event) {
        if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_POINTER_DOWN) {
            float xLen = event.getX(0) - event.getX(1);
            float yLen = event.getY(0) - event.getY(1);
            mTwoFingerStartLength = Math.sqrt((xLen * xLen) + (yLen * yLen));
            mTwoFingerOldPoint.set(xLen, yLen);
        } else if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_MOVE) {
            float xLen = event.getX(0) - event.getX(1);
            float yLen = event.getY(0) - event.getY(1);
            float oldDegree = (float) Math.toDegrees(Math.atan2(mTwoFingerOldPoint.x, mTwoFingerOldPoint.y));
            float newDegree = (float) Math.toDegrees(Math.atan2((event.getX(0) - event.getX(1)), (event.getY(0) - event.getY(1))));
            mTwoFingerEndLength = Math.sqrt(xLen * xLen + yLen * yLen);

            float scalePercent = (float) (mTwoFingerEndLength / mTwoFingerStartLength);
            float degree = newDegree - oldDegree;

            if (mOnTransformListener != null) {
                mOnTransformListener.onScaleAndRotate(scalePercent, degree);
            }
            mTwoFingerStartLength = mTwoFingerEndLength;
            mTwoFingerOldPoint.set(xLen, yLen);
        } else if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_UP) {
            if (mOnTransformListener != null) {
                float scale = getWidth() * 1.0F / mDrawRect.right - mDrawRect.left;
                mOnTransformListener.onTransEnd(scale, new float[]{mDrawRect.right - mDrawRect.left, mDrawRect.bottom - mDrawRect.top});
            }
        }
        return super.onTouchEvent(event);
    }

    public int getTouchRect(MotionEvent event) {
        if (isInLeftTop(event)) {
            //Logger.d(TAG, "getTouchRect: RECT_L_T");
            return RECT_L_T;
        } else if (isInLeftBottom(event)) {
            //Logger.d(TAG, "getTouchRect: RECT_L_B");
            return RECT_L_B;
        } else if (isInRightBottom(event)) {
            //Logger.d(TAG, "getTouchRect: RECT_R_B");
            return RECT_R_B;
        } else if (isInRightTop(event)) {
            //Logger.d(TAG, "getTouchRect: RECT_R_T");
            return RECT_R_T;
        }
        return -1;
    }

    private boolean isInLeftTop(MotionEvent event) {
        float touchX = event.getRawX();
        float touchY = event.getRawY();
        return (touchX >= getLeft() && touchX <= getLeft() + TOUCH_RECT_SIZE
                && touchY >= getTop() && touchY <= getTop() + TOUCH_RECT_SIZE);
    }

    private boolean isInRightTop(MotionEvent event) {
        float touchX = event.getRawX();
        float touchY = event.getRawY();
        return (touchX >= (getLeft() + getWidth() - TOUCH_RECT_SIZE) && touchX <= getLeft() + getWidth()
                && touchY >= getTop() && touchY <= getTop() + TOUCH_RECT_SIZE);
    }

    private boolean isInLeftBottom(MotionEvent event) {
        float touchX = event.getRawX();
        float touchY = event.getRawY();
        return (touchX >= getLeft() && touchX <= getLeft() + TOUCH_RECT_SIZE
                && touchY >= (getTop() + getHeight() - TOUCH_RECT_SIZE) && touchY <= getTop() + getHeight());
    }

    private boolean isInRightBottom(MotionEvent event) {
        float touchX = event.getRawX();
        float touchY = event.getRawY();
        return (touchX >= (getLeft() + getWidth() - TOUCH_RECT_SIZE) && touchX <= getLeft() + getWidth()
                && touchY >= (getTop() + getHeight() - TOUCH_RECT_SIZE) && touchY <= getTop() + getHeight());
    }

    public void setOnTransformListener(OnTransformListener listener) {
        mOnTransformListener = listener;
    }

    public interface OnTransformListener {
        void onTrans(float deltaX, float deltaY);

        void onScaleAndRotate(float scale, float degree);

        void onTransEnd(float scale, float[] size);
    }
}
