package com.meishe.engine.db;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate :2021/2/8 15:43
 * @Description :数据库增删改查接口 Database add delete change check interface
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
@Dao
public interface AssetDao {
    @Insert
    void insertAsset(AssetEntity... asset);

    @Update
    void updateAsset(AssetEntity... asset);

    @Delete
    void deleteAsset(AssetEntity... asset);

    @Query("DELETE  FROM AssetEntity")
    void deleteAllAsset();

    @Query("SELECT * FROM AssetEntity WHERE id =  :id")
    AssetEntity getAsset(String id);

    @Query("SELECT * FROM AssetEntity WHERE id =  :id AND type = :type")
    AssetEntity getAsset(String id, int type);

    @Query("SELECT * FROM AssetEntity WHERE type =  :type")
    List<AssetEntity> getAssetList(int type);

    @Query("SELECT * FROM AssetEntity WHERE packageId =  :packageId")
    List<AssetEntity> getAssetList(String packageId);
}
