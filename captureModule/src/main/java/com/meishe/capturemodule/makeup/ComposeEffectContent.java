package com.meishe.capturemodule.makeup;

import java.util.List;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * <AUTHOR> Liu<PERSON>an<PERSON><PERSON>
 * @CreateDate : 2021/2/19 14:16
 * @Description : 组合美妆的特效集合，包括美妆数据
 *             Special effects collection of combined beauty, including beauty data
 * @Copyright : www.meishesdk.com Inc. All rights reserved.
 */
public class ComposeEffectContent extends MakeupEffectContent {
    private List<FilterArgs> filter;
    private List<BeautyFxArgs> shape;
    private List<BeautyFxArgs> beauty;

    public List<FilterArgs> getFilter() {
        return filter;
    }

    public void setFilter(List<FilterArgs> filter) {
        this.filter = filter;
    }

    public List<BeautyFxArgs> getShape() {
        return shape;
    }

    public void setShape(List<BeautyFxArgs> shape) {
        this.shape = shape;
    }

    public List<BeautyFxArgs> getBeauty() {
        return beauty;
    }

    public void setBeauty(List<BeautyFxArgs> beauty) {
        this.beauty = beauty;
    }
}
