package com.meishe.myvideo.activity;


import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseActivity;
import com.meishe.base.bean.MediaData;
import com.meishe.myvideo.R;
import com.meishe.myvideo.fragment.MaterialSelectFragment;

import androidx.fragment.app.FragmentTransaction;

import java.util.ArrayList;

import static com.meishe.logic.constant.PagerConstants.BUNDLE_DATA;
import static com.meishe.logic.constant.PagerConstants.MEDIA_FILTER;
import static com.meishe.logic.constant.PagerConstants.MEDIA_TYPE;
import static com.meishe.logic.constant.PagerConstants.NEXT_PAGE_ACTION;
import static com.meishe.logic.constant.PagerConstants.TYPE_ONE_FINISH;

/**
 * The type Material single select activity.
 * 素材选择 -单选类
 */
public class MaterialSingleSelectActivity extends BaseActivity {

    private TextView mTvNext;
    private MediaData mSelectedMedia;
    private int mMediaType;
    private String mNextPageAction;
    private ArrayList<String> mMediaFilter;

    @Override
    protected int bindLayout() {
        return R.layout.activity_material_single_select;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        Intent intent = getIntent();
        if (intent != null) {
            Bundle bundle = intent.getExtras();
            if (bundle != null) {
                mMediaType = bundle.getInt(MEDIA_TYPE, MediaData.TYPE_VIDEO);
                mNextPageAction = bundle.getString(NEXT_PAGE_ACTION, "");
                mMediaFilter = bundle.getStringArrayList(MEDIA_FILTER);
                //mSelectedType = bundle.getInt(SELECTED_TYPE, TYPE_ONE_FINISH);
            }
        }
    }

    @Override
    protected void initView() {
        ImageView ivBack = findViewById(R.id.iv_back);
        TextView tvTile = findViewById(R.id.tv_title);
        mTvNext = findViewById(R.id.tv_next);

        if (mMediaType == MediaData.TYPE_PHOTO) {
            tvTile.setText(R.string.single_select_picture);
        }

        ivBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        mTvNext.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                gotoNext();
            }
        });
        initFragment();
    }

    private void initFragment() {
        MaterialSelectFragment fragment = MaterialSelectFragment.create(mMediaType, mMediaFilter, TYPE_ONE_FINISH, false,  new MaterialSelectFragment.MediaChangeListener() {
            @Override
            public void onMediaChange(MediaData mediaData) {
                if (mediaData.isState()) {
                    mSelectedMedia = mediaData;
                    if (mTvNext.getVisibility() != View.VISIBLE) {
                        mTvNext.setVisibility(View.VISIBLE);
                    }
                } else {
                    mSelectedMedia = null;
                    if (mTvNext.getVisibility() == View.VISIBLE) {
                        mTvNext.setVisibility(View.GONE);
                    }
                }
            }

            @Override
            public void onMediaPreView(MediaData mediaData) {

            }
        });
        FragmentTransaction fragmentTransaction = getSupportFragmentManager().beginTransaction();
        fragmentTransaction.add(R.id.fl_fragment_container, fragment).commitAllowingStateLoss();
        fragmentTransaction.show(fragment);
    }

    private void gotoNext() {
        if (mSelectedMedia == null) {
            return;
        }
        Intent it = new Intent();
        it.putExtra(BUNDLE_DATA, mSelectedMedia);
        if (TextUtils.isEmpty(mNextPageAction)) {
            setResult(RESULT_OK, it);
        } else {
            it.setAction(mNextPageAction);
            startActivity(it);
        }
        AppManager.getInstance().finishActivity();
    }
}
