<resources>
    <style name="dialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <!--边框-->
        <item name="android:windowIsFloating">true</item>
        <!--是否浮现在activity之上-->
        <item name="android:windowIsTranslucent">true</item>
        <!--半透明-->
        <item name="android:windowNoTitle">true</item>
        <!--无标题-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--背景透明-->
        <item name="android:backgroundDimEnabled">true</item>
        <!--模糊-->
    </style>

    <declare-styleable name="RectSelectView">
        <attr name="innerPadding" format="dimension" />
    </declare-styleable>

</resources>
