package com.meishe.myvideoapp.fragment;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.czc.cutsame.TemplateListActivity;
import com.meishe.base.BuildConfig;
import com.meishe.base.manager.AppManager;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.PermissionConstants;
import com.meishe.base.utils.PermissionUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.util.PathUtils;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.logic.manager.PreferencesManager;
import com.meishe.myvideo.activity.MaterialSelectActivity;
import com.meishe.myvideo.activity.TestActivity;
import com.meishe.myvideo.fragment.EditingFragment;
import com.meishe.myvideoapp.R;
import com.meishe.myvideoapp.activity.NewMainActivity;
import com.meishe.myvideoapp.util.ConfigUtil;
import com.meishe.myvideoapp.util.CrashHandler;
import com.meishe.myvideoapp.view.pop.PrivacyPolicyPop;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.lang.reflect.Method;
import java.util.List;

import static com.meishe.base.utils.PermissionConstants.CAMERA;
import static com.meishe.base.utils.PermissionConstants.LOCATION;
import static com.meishe.base.utils.PermissionConstants.MICROPHONE;
import static com.meishe.base.utils.PermissionConstants.NET;
import static com.meishe.base.utils.PermissionConstants.SENSORS;
import static com.meishe.base.utils.PermissionConstants.STORAGE;
import static com.meishe.logic.constant.PagerConstants.BUNDLE_SAVE_DRAFT;
import static com.meishe.logic.constant.PagerConstants.FROM_MAIN_PAGE;
import static com.meishe.logic.constant.PagerConstants.FROM_PAGE;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_REQUEST_PERMISSION;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 版权所有www.meishesdk.com设计
 *
 * <AUTHOR> LiCong
 * @CreateDate : 2025/05/20
 * @Description : 新的首页Fragment New Home page Fragment
 * @Copyright : www.meishesdk.com Inc.All rights reserved.
 */
public class NewMainFragment extends Fragment implements View.OnClickListener, IUserPlugin.ILoginCallBack {
    private FrameLayout mFlBottomContainer;
    private View mRootView;
    private static boolean hasInitSDK = false;
    private Fragment mEditingFragment;
    private View mLoginLayout;
    private ImageView mLoginImageView;
    private TextView mLoginTextView;
    public static boolean hasStoragePermission = false;

    public static NewMainFragment newInstance() {
        return new NewMainFragment();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.activity_main1, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mRootView = view;
        initView();
        initData();
    }

    private void initData() {
        // Initialize data
    }

    private void initView() {
        EventBus.getDefault().register(this);
        mFlBottomContainer = mRootView.findViewById(R.id.fl_main_bottom_container);
        mLoginLayout = mRootView.findViewById(R.id.ll_login);
        mLoginImageView = mRootView.findViewById(R.id.iv_login);
        mLoginTextView = mRootView.findViewById(R.id.tv_login);
        initListener();
        if (ConfigUtil.isToC()) {
            mLoginLayout.setVisibility(View.INVISIBLE);
        } else {
            mLoginLayout.setVisibility(View.VISIBLE);
        }
        if (!ConfigUtil.isToC() && com.meishe.myvideo.util.ConfigUtil.IS_NEED_CLOUD) {
            IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
            if (userPlugin != null) {
                mEditingFragment = userPlugin.getEditingFragment(R.id.fl_main_bottom_container);
            }
        } else {
            mEditingFragment = EditingFragment.create(R.id.fl_main_bottom_container);
        }
        if (mEditingFragment == null) {
            LogUtils.e("Project config is error! Please fix it!");
            return;
        }
        showView(mEditingFragment, mRootView.findViewById(R.id.fl_fragment_container), true);

        hasStoragePermission = PermissionUtils.isGroupGranted(STORAGE, NET, LOCATION);

        if(!hasStoragePermission){
            requestPermission(new PermissionUtils.FullCallback() {
                @Override
                public void onGranted(@NonNull List<String> granted) {
                    initSDK();
                    doInitAfterAllPermissionsGranted();
                }

                @Override
                public void onDenied(@NonNull List<String> deniedForever, @NonNull List<String> denied) {
                    ToastUtils.showShort("请授予存储权限");
                }
            },STORAGE, NET, LOCATION);
        }
//        showPrivacyDialog();
        try {
            Class.forName("com.meishe.capturemodule.CaptureActivity");
        } catch (ClassNotFoundException e) {
            mRootView.findViewById(R.id.ll_capture).setVisibility(View.GONE);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        LogUtils.d("NewMainFragment", "onResume");
        if (hasStoragePermission) {
            initSDK();
            if (!ConfigUtil.isToC()) {
                boolean login = isLogin();
                onLoginBack(login);
                if (!login) {
                    login();
                }
            }
        }
        hasStoragePermission = PermissionUtils.isGroupGranted(STORAGE, NET, LOCATION);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

    public void onNewIntent(Intent intent) {
        if (intent != null) {
            Bundle bundle = intent.getExtras();
            if (bundle != null) {
                boolean saveDraft = bundle.getBoolean(BUNDLE_SAVE_DRAFT, false);
                if (saveDraft) {
                    CrashHandler.getInstance().saveDraft();
                }
            }
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 1000) {
            IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
            if (userPlugin != null) {
                userPlugin.goToCompilingPage(mEditingFragment);
            }
        }
    }

    private void initListener() {
        mRootView.findViewById(R.id.iv_create).setOnClickListener(this);
        mRootView.findViewById(R.id.bt_setting).setOnClickListener(this);
        mRootView.findViewById(R.id.ll_login).setOnClickListener(this);
        mRootView.findViewById(R.id.ll_capture).setOnClickListener(this);
        mRootView.findViewById(R.id.ll_template).setOnClickListener(this);
    }

    private void requestPermission(final PermissionUtils.FullCallback callback,
                                   @PermissionConstants.Permission final String... permissions) {
        if (PermissionUtils.isGroupGranted(permissions)) {
            doInitAfterAllPermissionsGranted();
        } else {
            PermissionUtils.permission(permissions).callback(callback).request();
        }
    }

    /**
     * 展示隐私条款
     * Display Privacy Policy
     */
    private void showPrivacyDialog() {
        mRootView.post(new Runnable() {
            @Override
            public void run() {
                if (!PreferencesManager.get().isAgreePrivacy()) {
                    PrivacyPolicyPop privacyPolicyPop = PrivacyPolicyPop.create(requireActivity(), () -> {
                        if (hasStoragePermission = PermissionUtils.isGroupGranted(STORAGE, NET, LOCATION)) {
                            doInitAfterAllPermissionsGranted();
                            refreshFragmentData();
                        }
                    });
                    privacyPolicyPop.show();
                } else {
                    if (hasStoragePermission = PermissionUtils.isGroupGranted(STORAGE, NET, LOCATION)) {
                        doInitAfterAllPermissionsGranted();
                        refreshFragmentData();
                    }
                }
            }
        });
    }

    private void refreshFragmentData() {
        if (mEditingFragment == null) {
            return;
        }
        Class<? extends Fragment> aClass = mEditingFragment.getClass();
        try {
            Method refreshData = aClass.getMethod("refreshData");
            refreshData.invoke(mEditingFragment);
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }

    private void doInitAfterAllPermissionsGranted() {
        PathUtils.getFontFilePath();
        initSDK();
    }

    private void initSDK() {
        if (!hasInitSDK) {
            CrashHandler.getInstance().init(requireActivity());
            PreferencesManager.get().adaptOldData(requireActivity());
            if (BuildConfig.BUILD_TYPE.contains("debug")) {
                LogUtils.getConfig().setLogSwitch(true);
            } else {
                LogUtils.getConfig().setLogSwitch(BuildConfig.DEBUG);
            }
            hasInitSDK = true;
            initSDKCore();
            AssetsManager.get().init(ConfigUtil.isToC());
            initARScene();
        }
    }

    /**
     * 初始化SDK核心功能
     * Initialize SDK core functionality
     */
    private void initSDKCore() {
        EditorEngine.init(ConfigUtil.getLicPath());
    }

    /**
     * 初始化人脸
     * init AR Scene
     */
    private void initARScene() {
        // 直接调用相关初始化方法
        if (PluginManager.get().getUserPlugin() != null) {
//            PluginManager.get().getUserPlugin().initARScene();
        }
    }

    /**
     * 显示底部view
     * Show the bottom view
     *
     * @param fragment Fragment The view to be displayed
     */
    public void showBottomView(Fragment fragment) {
        showBottomView(fragment, true);
    }

    private void showView(Fragment fragment, View container, boolean replace) {
        if (fragment == null || !isAdded()) {
            return;
        }
        if (container.getVisibility() != View.VISIBLE) {
            container.setVisibility(View.VISIBLE);
        }
        FragmentTransaction transaction = getChildFragmentManager().beginTransaction();
        if (replace) {
            transaction.replace(container.getId(), fragment);
        } else {
            if (!fragment.isAdded()) {
                transaction.add(container.getId(), fragment);
            }
            transaction.show(fragment);
        }
        transaction.commitAllowingStateLoss();
    }

    /**
     * 显示底部view
     * Show the bottom view
     *
     * @param fragment Fragment The view to be displayed
     * @param replace  boolean ,true ,Last view will be replace .false , add new view ,but not replace last view.
     */
    public void showBottomView(Fragment fragment, boolean replace) {
        showView(fragment, mFlBottomContainer, replace);
    }

    /**
     * 不显示底部view
     * Not displayed the bottom view
     *
     * @param fragment Fragment The view to be not displayed
     */
    public void dismissBottomView(Fragment fragment) {
        if (fragment == null || !isAdded()) {
            return;
        }
        if (mFlBottomContainer.getVisibility() == View.VISIBLE) {
            mFlBottomContainer.setVisibility(View.GONE);
        }
        FragmentTransaction transaction = getChildFragmentManager().beginTransaction();
        transaction.remove(fragment);
        transaction.commitAllowingStateLoss();
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (Utils.isFastClick()) {
            return;
        }
        if (id == R.id.bt_setting) {
            if (!PermissionUtils.isGroupGranted(STORAGE)) {
                requestPermission(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(@NonNull List<String> granted) {
                        if (PermissionUtils.isGroupGranted(STORAGE)) {
                            EditorEngine.init(ConfigUtil.getLicPath());
                            AppManager.getInstance().jumpActivity(requireActivity(), TestActivity.class);
                        } else {
                            ToastUtils.make()
                                    .setGravity(Gravity.CENTER, 0, 0)
                                    .setDurationIsLong(false)
                                    .show(R.string.lack_of_authority);
                        }
                    }

                    @Override
                    public void onDenied(@NonNull List<String> deniedForever, @NonNull List<String> denied) {
                        if (deniedForever.size() > 0) {
                            ToastUtils.make()
                                    .setGravity(Gravity.CENTER, 0, 0)
                                    .setDurationIsLong(false)
                                    .show(R.string.lack_of_authority);
                        }
                    }
                }, STORAGE);
            } else {
                EditorEngine.init(ConfigUtil.getLicPath());
                AppManager.getInstance().jumpActivity(requireActivity(), TestActivity.class);
            }

        } else if (id == R.id.iv_create) {
            if (!hasStoragePermission) {
                requestPermission(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(@NonNull List<String> granted) {
                        if (PermissionUtils.isGroupGranted(STORAGE, NET, LOCATION)) {
                            doInitAfterAllPermissionsGranted();
                        } else {
                            ToastUtils.make()
                                    .setGravity(Gravity.CENTER, 0, 0)
                                    .setDurationIsLong(false)
                                    .show(R.string.lack_of_authority);
                        }
                    }

                    @Override
                    public void onDenied(@NonNull List<String> deniedForever, @NonNull List<String> denied) {
                        if (deniedForever.size() > 0) {
                            ToastUtils.make()
                                    .setGravity(Gravity.CENTER, 0, 0)
                                    .setDurationIsLong(false)
                                    .show(R.string.lack_of_authority);
                        }
                    }
                }, STORAGE, NET, LOCATION);
            } else {
                Bundle bundle = new Bundle();
                bundle.putInt(FROM_PAGE, FROM_MAIN_PAGE);
                AppManager.getInstance().jumpActivity(requireActivity(), MaterialSelectActivity.class, bundle);
            }
        } else if (id == R.id.ll_login) {
            if (!hasStoragePermission) {
                requestPermission(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(@NonNull List<String> granted) {
                        if (PermissionUtils.isGroupGranted(STORAGE, NET, LOCATION)) {
                            doInitAfterAllPermissionsGranted();
                            startLogin();
                        } else {
                            ToastUtils.make()
                                    .setGravity(Gravity.CENTER, 0, 0)
                                    .setDurationIsLong(false)
                                    .show(R.string.lack_of_authority);
                        }
                    }

                    @Override
                    public void onDenied(@NonNull List<String> deniedForever, @NonNull List<String> denied) {
                        if (deniedForever.size() > 0) {
                            ToastUtils.make()
                                    .setGravity(Gravity.CENTER, 0, 0)
                                    .setDurationIsLong(false)
                                    .show(R.string.lack_of_authority);
                        }
                    }
                }, STORAGE, NET, LOCATION);
            } else {
                startLogin();
            }
        } else if (id == R.id.ll_capture) {
            if (!PermissionUtils.isGroupGranted(STORAGE, NET, CAMERA, MICROPHONE, SENSORS, LOCATION)) {
                requestPermission(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(@NonNull List<String> granted) {
                        if (PermissionUtils.isGroupGranted(STORAGE, NET, CAMERA, MICROPHONE, SENSORS, LOCATION)) {
                            doInitAfterAllPermissionsGranted();
                        } else {
                            ToastUtils.make()
                                    .setGravity(Gravity.CENTER, 0, 0)
                                    .setDurationIsLong(false)
                                    .show(R.string.lack_of_authority);
                        }
                    }

                    @Override
                    public void onDenied(@NonNull List<String> deniedForever, @NonNull List<String> denied) {
                        if (deniedForever.size() > 0) {
                            ToastUtils.make()
                                    .setGravity(Gravity.CENTER, 0, 0)
                                    .setDurationIsLong(false)
                                    .show(R.string.lack_of_authority);
                        }
                    }
                }, STORAGE, NET, CAMERA, MICROPHONE, SENSORS, LOCATION);
            } else {
                Class<?> aClass;
                try {
                    aClass = Class.forName("com.meishe.capturemodule.CaptureActivity");
                    AppManager.getInstance().jumpActivity(requireActivity(), (Class<? extends Activity>) aClass);
                } catch (ClassNotFoundException e) {
                    LogUtils.e(e);
                }
            }
        } else if (id == R.id.ll_template) {
            if (!hasStoragePermission) {
                requestPermission(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(@NonNull List<String> granted) {
                        if (PermissionUtils.isGroupGranted(STORAGE, NET, LOCATION)) {
                            doInitAfterAllPermissionsGranted();
                        } else {
                            ToastUtils.make()
                                    .setGravity(Gravity.CENTER, 0, 0)
                                    .setDurationIsLong(false)
                                    .show(R.string.lack_of_authority);
                        }
                    }

                    @Override
                    public void onDenied(@NonNull List<String> deniedForever, @NonNull List<String> denied) {
                        if (deniedForever.size() > 0) {
                            ToastUtils.make()
                                    .setGravity(Gravity.CENTER, 0, 0)
                                    .setDurationIsLong(false)
                                    .show(R.string.lack_of_authority);
                        }
                    }
                }, STORAGE, NET, LOCATION);
            } else {
                AppManager.getInstance().jumpActivity(requireActivity(), TemplateListActivity.class);
            }
        }
    }

    private void startLogin() {
        if (isLogin()) {
            IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
            if (userPlugin != null) {
                userPlugin.showLoginOut(requireActivity(), mLoginLayout, this);
            }
        } else {
            showLoginDialogEx();
        }
    }

    /**
     * 展示登录弹窗
     * Show login dialog
     */
    private void showLoginDialogEx() {
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin != null) {
            userPlugin.showLoginPop(requireActivity(), this);
        }
    }

    /**
     * Whether is user login
     * <p>
     * 用户是否登录
     *
     * @return true：yes false：no
     */
    public boolean isLogin() {
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin == null) {
            return false;
        }
        return userPlugin.isLogin();
    }

    /**
     * User login
     * <P></>
     * 用户登录
     */
    public void login() {
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin != null) {
//            userPlugin.login(requireActivity());
        }
    }

    public void onLoginBack(boolean isLogin) {
        mLoginTextView.setText(isLogin ? "" : getResources().getString(R.string.user_login));
        mLoginImageView.setSelected(isLogin);
        if (PermissionUtils.isGroupGranted(STORAGE, LOCATION)) {
            refreshFragmentData();
        }
    }

    @Override
    public void onLoginSuccess(String token) {
        onLoginBack(true);
    }

    @Override
    public void onLoginFailed(int code) {
        onLoginBack(false);
    }

    /**
     * On message event.
     * 消息事件
     *
     * @param event the event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(Integer event) {
        //登录
        if (event == IUserPlugin.MESSAGE_LOGIN) {
            showLoginDialogEx();
        } else if (event == MESSAGE_REQUEST_PERMISSION) {
            doInitAfterAllPermissionsGranted();
        }
    }
}
