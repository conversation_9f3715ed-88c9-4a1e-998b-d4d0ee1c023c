package com.meishe.myvideo.ui.trackview;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Vibrator;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.meishe.base.utils.FormatUtils;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.myvideo.R;
import com.meishe.myvideo.ui.bean.BaseTrackClip;
import com.meishe.myvideo.ui.bean.ITrackClip;
import com.meishe.myvideo.ui.bean.SpeedInfo;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZ<PERSON>
 * @CreateDate :2020/10/28 14:54
 * @Description :拖动把手 Drag span view
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class SpanView extends LinearLayout {
    private final String TAG = "SpanView";
    private final float TRANSLATE_ADSORB_DISTANCE = 30f;
    /**
     * 屏幕宽度
     * The screen width
     */
    private int mScreenWidth;
    /**
     * 两边把手宽度
     * The hand width
     */
    private int mHandleWidth;
    /**
     * 左把手往右移的最大值
     * Maximum value of the left handle moving to the right
     */
    private int mMaxLeftToRight = 0;
    /**
     * 左把手往左移的最大值
     * Maximum value of left handle moving to the left
     */
    private int mMaxLeftToLeft = 0;
    /**
     * 右把手往左移的最大值
     * Maximum value of right handle moving to the left
     */
    private int mMaxRightToLeft = 0;
    /**
     * 右把手往右移的最大值
     * Maximum value of right handle moving to the right
     */
    private int mMaxRightToRight = 0;
    private OnHandleChangeListener mOnHandleChangeListener;
    /**
     * 片段时长
     * The clip duration
     */
    private TextView mTvDuration;
    /**
     * 片段加速倍数
     * The speed value
     **/
    private TextView mTvSpeedNum;
    private LinearLayout mLlSpeedContainer;
    private ITrackClip mTrackClip;
    private double mScaleFactor;
    private boolean mScrollEnable = true;
    private boolean mCanAutoMove = false;
    private int mMargin;
    private boolean mHadInXAdsorbRange;
    private float mAdsorbAccumulateX;
    private long mMinDuration = 100000;
    private View mStartEmptyView;
    private View mEndEmptyView;
    /**
     * 震动器
     * Vibrator
     */
    private Vibrator mVibrator;
    private View mFaceIcon;
    private View mVolumeIcon;

    public SpanView(Context context) {
        this(context, null);
    }


    public SpanView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);

    }

    public SpanView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    @SuppressLint("ClickableViewAccessibility")
    private void init(Context context) {
        setOrientation(HORIZONTAL);
        LayoutInflater layoutInflater = LayoutInflater.from(context);
        View view = layoutInflater.inflate(R.layout.layout_span_view, this);
        //setBackgroundColor(getResources().getColor(R.color.color_blue_994a));
        int screenWidth = ScreenUtils.getScreenWidth();
        mScaleFactor = screenWidth / 10d / 1000000;
        mScreenWidth = screenWidth;
        ImageView ivLeftHandle = view.findViewById(R.id.iv_left_hand);
        ImageView ivRightHandleView = view.findViewById(R.id.iv_right_hand);
        mStartEmptyView = view.findViewById(R.id.v_start_empty);
        mEndEmptyView = view.findViewById(R.id.v_end_empty);
        mTvDuration = view.findViewById(R.id.tv_duration);
        mLlSpeedContainer = view.findViewById(R.id.ll_speed_container);
        mFaceIcon = view.findViewById(R.id.iv_face_icon);
        mVolumeIcon = view.findViewById(R.id.iv_volume_icon);
        //ImageView mIvSpeed = view.findViewById(R.id.iv_speed);
        mTvSpeedNum = view.findViewById(R.id.tv_speed);
        mHandleWidth = (int) getResources().getDimension(R.dimen.dp_px_50);

        ivLeftHandle.setOnTouchListener(mOnTouch);
        ivRightHandleView.setOnTouchListener(mOnTouch);
        mVibrator = (Vibrator) context.getSystemService(Context.VIBRATOR_SERVICE);
    }

    /**
     * 设置参数
     * Set params
     */
    public void setParams(int margin, double scaleFactor, long minDuration) {
        mMargin = margin;
        mScaleFactor = scaleFactor;
        mMinDuration = minDuration;
        setMargin(margin);
    }

    /**
     * 设置所需的UI展示数据
     * Set t drag span view required UI presentation data
     *
     * @param trackClip the track clip
     */
    public void setTrackClip(ITrackClip trackClip) {
        setTrackClip(trackClip, false);
    }

    /**
     * 设置左边距
     * Set left margin
     *
     * @param margin the left and right margin ,左右间隔
     */
    public void setMargin(int margin) {
        mMargin = margin;
        if (mMargin == 0) {
            mMargin = mHandleWidth;
        }
        //增加空白View是为了防止scroll的时候显示不全以及改变拖动把手的起始位置。
        // The purpose of adding a blank view is to prevent incomplete display and change the starting position of the drag handle when scrolling.
        ViewGroup.LayoutParams layoutParams = mStartEmptyView.getLayoutParams();
        layoutParams.width = margin - mHandleWidth;
        mStartEmptyView.setLayoutParams(layoutParams);
        layoutParams = mEndEmptyView.getLayoutParams();
        if (layoutParams.width != mScreenWidth / 2) {
            //后边的固定写死。
            // The fixed at the back is dead.
            layoutParams.width = mScreenWidth / 2;
            mEndEmptyView.setLayoutParams(layoutParams);
        }
    }

    /**
     * 设置所需的UI展示数据,并更新所有
     * Set t drag span view required UI presentation data ,and update all
     *
     * @param trackClip the track clip 轨道片段
     * @param update    true update all,false not.真则更新，假则不更新。
     */
    public void setTrackClip(ITrackClip trackClip, boolean update) {
        if (trackClip == null) {
            Log.e(TAG, "setUiClip, trackClip is null!!!");
            return;
        }
        mTrackClip = trackClip;
        if (update) {
            displayDuration();
            displaySpeed(true);
            displayFaceProp();
            displayVolumeIcon();
            updateWidth();
        }
        updateDragLimit();
    }

    /**
     * 更新
     * Update
     *
     * @param showDuration true show duration text ,false not 真则显示时长，假则不。
     */
    public void update(boolean showDuration) {
        displayDuration();
        displaySpeed(showDuration);
        updateWidth();
        updateDragLimit();
    }

    /**
     * 更新拖动的限制
     * Update drag limits
     */
    private void updateDragLimit() {
        SpeedInfo speedInfo = mTrackClip.getSpeedInfo();
        if (BaseTrackClip.CLIP_IMAGE.equals(mTrackClip.getType())) {
            mMaxRightToRight = durationToLength((getOrgDuration() - (mTrackClip.getTrimOut() - mTrackClip.getTrimIn())) / 2);
            mMaxLeftToLeft = mMaxRightToRight;
        } else if (BaseTrackClip.CLIP_VIDEO.equals(mTrackClip.getType())) {
            mMaxRightToRight = durationToLength((long) (getOrgDuration() / speedInfo.getSpeed() - mTrackClip.getTrimOut() / speedInfo.getSpeed()));
            mMaxLeftToLeft = durationToLength((long) (mTrackClip.getTrimIn() / speedInfo.getSpeed()));
        }
        mMaxRightToLeft = durationToLength(getDuration() - mMinDuration);
        mMaxLeftToRight = mMaxRightToLeft;
    }

    /**
     * 获取轨道片段
     * Get track clip
     */
    public ITrackClip getTrackClip() {
        return mTrackClip;
    }

    /**
     * 时长转化成长度
     * Duration convert to length
     *
     * @param duration the duration 时长
     */
    public int durationToLength(long duration) {
        return (int) Math.floor(duration * mScaleFactor + 0.5D);
    }

    /**
     * 缩放(左右)
     * Scale left or right
     *
     * @param factor the scale factor 索方因子
     */
    public void scale(double factor) {
        mScaleFactor = factor;
        if (mTrackClip != null) {
            updateWidth();
            updateDragLimit();
        }
    }

    /**
     * 设置滚动是否可用
     * Set scroll enable
     *
     * @param enable true can scroll ,false not 真则滚动有效，假无效
     */
    public void setScrollEnable(boolean enable) {
        mScrollEnable = enable;
    }

    @Override
    public void scrollBy(int x, int y) {
       /* if (mTrackClip == null || mTrackClip.getOutPoint() - mTrackClip.getInPoint() <= mMinDuration) {
            return;
        }*/
        //  LogUtils.d("x=" + x + ",by=" + getScrollX() + ",width=" + getWidth());
        if (mScrollEnable) {
            super.scrollBy(x, y);
        }
    }

    /**
     * 强制滚动
     * Force scroll
     */
    public void forceScrollTo(int x) {
       /* if (mTrackClip == null || mTrackClip.getOutPoint() - mTrackClip.getInPoint() <= mMinDuration) {
            return;
        }*/
        //  LogUtils.d("x=" + x + ",by=" + getScrollX() + ",width=" + getWidth());
        super.scrollTo(x, getScrollY());
    }

    @Override
    public void scrollTo(int x, int y) {
       /* if (mTrackClip == null || mTrackClip.getOutPoint() - mTrackClip.getInPoint() <= mMinDuration) {
            return;
        }*/
        //LogUtils.d("x=" + x + ",by=" + getScrollX() + ",width=" + getWidth());
        if (mScrollEnable) {
            super.scrollTo(x, y);
        }
    }


    /**
     * 更新宽度
     * Update view width
     */
    private void updateWidth() {
        if (mTrackClip != null) {
            ViewGroup.LayoutParams layoutParams = getLayoutParams();
            layoutParams.width = durationToLength(mTrackClip.getOutPoint() - mTrackClip.getInPoint()) + mHandleWidth + mMargin + mScreenWidth / 2;
            //   LogUtils.d("width="+layoutParams.width+",mTrackClip="+mTrackClip+",mMargin="+mMargin);
            setLayoutParams(layoutParams);
        }
    }

    /**
     * 展示时长
     * Display duration view
     */
    public void displayDuration() {
        if (mTrackClip != null) {
            mTvDuration.setText(FormatUtils.duration2Text2((mTrackClip.getOutPoint() - mTrackClip.getInPoint())));
        }
    }

    /**
     * 展示加速
     * Display speed view
     *
     * @param showDuration true show duration view ,false not.真则显示时长，假则不
     */
    public void displaySpeed(boolean showDuration) {
        if (mTrackClip != null) {
            if (BaseTrackClip.CLIP_IMAGE.equals(mTrackClip.getType())) {
                mLlSpeedContainer.setVisibility(GONE);
            } else {
                if (mTrackClip.getSpeedInfo().getSpeed() != 1) {
                    mLlSpeedContainer.setVisibility(VISIBLE);
                    if (TextUtils.isEmpty(mTrackClip.getSpeedInfo().getSpeedName())) {
                        mTvSpeedNum.setText(getSpeedText());
                    } else {
                        mTvSpeedNum.setText(mTrackClip.getSpeedInfo().getSpeedName());
                    }
                } else {
                    mLlSpeedContainer.setVisibility(GONE);
                    showDuration = true;
                }
            }
        } else {
            mLlSpeedContainer.setVisibility(GONE);
        }
        mTvDuration.setVisibility(showDuration ? VISIBLE : GONE);
    }

    /**
     * 展示人脸道具icon
     * Display face prop.
     *
     */
    public void displayFaceProp() {
        mFaceIcon.setVisibility((mTrackClip != null && mTrackClip.hasProp()) ? VISIBLE : GONE);
    }

    /**
     * 展示音量icon
     * Display face prop.
     *
     */
    public void displayVolumeIcon() {
        mVolumeIcon.setVisibility((mTrackClip != null && mTrackClip.getVolume() == 0) ? VISIBLE : GONE);
    }

    /**
     * 是否让父布局禁用事件拦截功能
     * Request disallow intercept touch event
     *
     * @param intercept true disallow ,false not .真则拦截，假则不
     */
    private void requestIntercept(boolean intercept) {
        ViewParent viewParent = getParent();
        if (viewParent == null) {
            return;
        }
        viewParent.requestDisallowInterceptTouchEvent(intercept);
    }

    /**
     * 是否是相同的片段
     * Whether it's the same clip
     *
     * @param clip the track clip 轨道片段
     */
    public boolean isSameClip(ITrackClip clip) {
        return mTrackClip != null && clip != null && mTrackClip.getIndexInTrack() == clip.getIndexInTrack();
    }

    /**
     * 获取当前片段的时长
     * Get the clip duration
     */
    public long getDuration() {
        return mTrackClip == null ? 0 : mTrackClip.getOutPoint() - mTrackClip.getInPoint();
    }

    /**
     * 获取原始时长(真实文件的时长)
     * Get the clip original duration
     */
    public long getOrgDuration() {
        return mTrackClip == null ? 0 : mTrackClip.getOriginalDuration();
    }

    /**
     * 设置把手的宽度
     * Set handle width
     *
     * @param handleWidth the width
     */
    public void setHandleWidth(int handleWidth) {
        this.mHandleWidth = handleWidth;
    }

    /**
     * 获取把手的宽度
     * Get handle width
     */
    public int getHandleWidth() {
        return mHandleWidth;
    }

    /**
     * 设置把手事件监听
     * Set handle change listener
     *
     * @param listener the listener 监听者
     */
    public void setOnHandleChangeListener(OnHandleChangeListener listener) {
        this.mOnHandleChangeListener = listener;
    }

    /**
     * 获取加速值
     * Get speed text
     */
    private String getSpeedText() {
        if (mTrackClip == null) {
            Log.e(TAG, "getSpeedText: mTrackClip is null!");
            return "";
        }
        return FormatUtils.objectFormat2String(mTrackClip.getSpeedInfo().getSpeed()) + "x";
    }


    /**
     * 是否可以移动
     * Whether it can be moved
     *
     * @param leftHandle true is left handle ,false right handle 真则是左把手，假则是右把手
     * @param moveToLeft true move left ,false move right 真则是向左移动，假则是右移动
     */
    private boolean canMove(boolean leftHandle, boolean moveToLeft) {
        if (leftHandle) {
            if (moveToLeft) {
                return mMaxLeftToLeft > 0;
            } else {
                return mMaxLeftToRight > 0;
            }
        } else {
            if (moveToLeft) {
                return mMaxRightToLeft > 0;
            } else {
                return mMaxRightToRight > 0;
            }
        }
    }

    /**
     * 处理吸附
     * Deal with  adsorb
     *
     * @param viewX      the drag span view x 把手的x坐标
     * @param dx         move dx 移动的x间隔
     * @param leftHandle true is left handle ,false right handle 真则是左把手，假则是右把手
     * @param toLeft     true move left ,false move right 真则是向左移动，假则是向右移动
     */
    private boolean dealWithAdsorb(float viewX, float dx, boolean leftHandle, boolean toLeft) {
        float translateDistance;
        if (!mHadInXAdsorbRange) {
            //没有在x轴方向吸附范围内
            // Not within the adsorption range of x-axis direction
            if (Math.abs(translateDistance = viewX - mScreenWidth / 2f + (leftHandle ? mHandleWidth : 0)) <= TRANSLATE_ADSORB_DISTANCE) {
                //到吸附范围了，吸附。
                mHadInXAdsorbRange = true;
                if (mVibrator != null) {
                    mVibrator.vibrate(50);
                }
                dealMove(-translateDistance, leftHandle, toLeft, false);
                mLastIsToLeft = toLeft;
                mAdsorbAccumulateX = 0;
                //LogUtils.d("震动，mNeedTranslateX=" + mAdsorbAccumulateX + ",toLeft=" + toLeft + ",translateDistance=" + translateDistance + ",viewX=" + viewX);
            }
        } else {
            //在x轴方向吸附范围内
            // Within the adsorption range in the x-axis direction
            mHadInXAdsorbRange = Math.abs(viewX - mScreenWidth / 2f + (leftHandle ? mHandleWidth : 0)) <= TRANSLATE_ADSORB_DISTANCE;
        }
        boolean hold = mHadInXAdsorbRange && Math.abs(dx + mAdsorbAccumulateX) <= TRANSLATE_ADSORB_DISTANCE;
        if (hold) {
            //在吸附范围内,保持位置不动，累积dx,直到超过吸附范围
            // Within the adsorption range, keep the position unchanged and accumulate dx until it exceeds the adsorption range
            mAdsorbAccumulateX += dx;
        }
        // LogUtils.d("lastX=" + lastX + ",hadInXAdsorbRange=" + hadInXAdsorbRange + ",hold=" + hold + ",dx=" + dx + ",moveX=" + moveX + ",mNeedTranslateX=" + mAdsorbAccumulateX);
        //保持范围
        // Hold Range
        return hold;
    }

    /**
     * 处理移动
     * Deal with move
     *
     * @param dx         move dx 移动的x间隔
     * @param leftHandle true is left handle ,false right handle 真则是左把手，假则是右把手
     * @param moveToLeft true move left ,false move right 真则是向左移动，假则是向右移动
     * @param isBorder   true to the border ,false not 真则是到边界了，假则不是
     */
    private boolean dealMove(float dx, boolean leftHandle, boolean moveToLeft, boolean isBorder) {
        if (canMove(leftHandle, moveToLeft)) {
            if (mOnHandleChangeListener != null) {
                //通知外部移动、更改入出点
                // Notify external movement and change entry and exit points
                mOnHandleChangeListener.onHandleMove(dx, leftHandle, moveToLeft, isBorder);
            }
           /* if (leftHandle) {
                dx = -dx;
                if (moveToLeft) {
                    //左把手向左移动 Left handle moves to the left
                   // layoutParams.leftMargin = (int) (layoutParams.leftMargin + dx);
                } else {
                    //左把手向右移动Left handle moves to the right
                    dx = -dx;
                    //layoutParams.leftMargin = (int) (layoutParams.leftMargin - dx);
                }
            }*/
            //这里根据入出点计算宽更为精确
            // It is more accurate to calculate the width according to the entry and exit points
            updateWidth();
            updateDragLimit();
            displayDuration();
            return true;
        }
        return false;
    }

    boolean mLastIsToLeft;
    private final OnTouchListener mOnTouch = new OnTouchListener() {
        boolean leftHandle;
        boolean toLeft;
        float lastRawX = 0;
        private final Runnable moveRunnable = new Runnable() {
            @Override
            public void run() {
                //屏幕边缘的自动滚动
                // Automatic scrolling of screen edges
                if (toLeft == mLastIsToLeft) {
                    float dx = toLeft ? -40 : 40;
                    if (dealMove(dx, leftHandle, toLeft, true)) {
                        if (mOnHandleChangeListener != null) {
                            mOnHandleChangeListener.onNeedScroll(dx, leftHandle, toLeft);
                        }
                        postDelayed(this, 200);
                    } else {
                        mCanAutoMove = false;
                    }
                } else {
                    mCanAutoMove = false;
                }

            }
        };

        @SuppressLint("ClickableViewAccessibility")
        @Override
        public boolean onTouch(View v, MotionEvent event) {
            if (event.getAction() == MotionEvent.ACTION_DOWN) {
                lastRawX = event.getRawX();
                if (mOnHandleChangeListener != null) {
                    mOnHandleChangeListener.onHandleDown(v.getId() == R.id.iv_left_hand);
                }
                mAdsorbAccumulateX = 0;
                requestIntercept(true);
            } else if (event.getAction() == MotionEvent.ACTION_MOVE) {
                float x = event.getRawX();
                float dx = x - lastRawX;
                leftHandle = v.getId() == R.id.iv_left_hand;
                toLeft = dx < 0;
                int[] local = new int[2];
                v.getLocationOnScreen(local);
                int viewX = local[0];
                //LogUtils.d("onTouch,dx=" + dx + ",x=" + x + ",lastX=" + lastX + ",local[0]=" + local[0] + ",mCanAutoMove=" + mCanAutoMove + ",mLastIsToLeft=" + mLastIsToLeft);
                if (viewX < 200 && toLeft || mScreenWidth - viewX < 200 && !toLeft) {
                    /*屏幕边缘的时候长按（即没有收到UP事件），周期移动并滚动固定的距离
                    * Press and hold (that is, no UP event is received) at the edge of the screen
                    * to move periodically and scroll for a fixed distance
                    * */
                    if (!mCanAutoMove) {
                        mCanAutoMove = true;
                        mLastIsToLeft = toLeft;
                        post(moveRunnable);
                    }
                    if (mLastIsToLeft == toLeft) {
                        return true;
                    }
                }
                mCanAutoMove = false;
                removeCallbacks(moveRunnable);
                if (dealWithAdsorb(viewX, dx, leftHandle, toLeft)) {
                    lastRawX = x;
                    return true;
                }
                if (dx != 0) {
                    dealMove(dx, leftHandle, toLeft, false);
                    mLastIsToLeft = toLeft;
                }
                lastRawX = x;
            } else if (event.getAction() == MotionEvent.ACTION_UP) {
                requestIntercept(false);
                if (mOnHandleChangeListener != null) {
                    mOnHandleChangeListener.onHandleUp(v.getId() == R.id.iv_left_hand);
                }
                mCanAutoMove = false;
                removeCallbacks(moveRunnable);
            }
            return true;
        }
    };


    public interface OnHandleChangeListener {
        /**
         * 把手被按下
         * Press the handle down
         *
         * @param leftHandle true is left handle ,false right handle  真则是左把手，假则是右把手
         */
        void onHandleDown(boolean leftHandle);

        /**
         * 移动把手
         * Move handle
         *
         * @param dx         move dx 移动的x间隔
         * @param leftHandle true is left handle ,false right handle 真则是左把手，假则是右把手
         * @param moveToLeft true move left ,false move right 真则是向左移动，假则是向右移动
         * @param isBorder   true to the border ,false not 真则是到边界了，假则不是
         */
        void onHandleMove(float dx, boolean leftHandle, boolean moveToLeft, boolean isBorder);

        /**
         * 把手被按下后抬起
         * Press the handle up
         *
         * @param leftHandle true is left handle ,false right handle 真则是左把手，假则是右把手
         */
        void onHandleUp(boolean leftHandle);

        /**
         * 到屏幕边缘了，需要滚动
         * We're at the edge of the screen, and we need to scroll
         *
         * @param dx         move dx 移动间隔
         * @param leftHandle true is left handle ,false right handle 真则是左把手，假则是右把手
         * @param leftBorder true is left border ,false is right border 真则是到边界了，假则不是
         */
        void onNeedScroll(float dx, boolean leftHandle, boolean leftBorder);
    }
}
