package com.meishe.engine.command;

import com.meishe.annotation.Undo;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.bean.MeicamFxParam;
import com.meishe.engine.bean.MeicamKeyFrame;
import com.meishe.engine.bean.MeicamMaskRegionInfo;
import com.meishe.engine.bean.MeicamPosition2D;
import com.meishe.engine.interf.IKeyFrameProcessor;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/8/30 16:15
 * @Description :关键帧命令 The key frame command
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class KeyFrameCommand {
    private final static String TAG = "KeyFrame";

    @Undo(className = "KeyFrameSetFloatCommand", function = "setFloatVal", param = {"String|key", "float|value", "boolean...|needSaveOperate"})
    public static void setFloatVal(MeicamKeyFrame keyFrame, String key, float value, boolean... needSaveOperate) {
        setFloatValWidthOld(keyFrame, key, (float) keyFrame.getFloatValue(key, keyFrame.getAtTime()), value, needSaveOperate);
    }

    public static void setFloatValWidthOld(MeicamKeyFrame keyFrame, String key, Float oldValue, float value, boolean... needSaveOperate) {
        String tag = getTag(keyFrame, key);
        String tagAndKey = tag + key;
        long atTime = keyFrame.getAtTime();
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            Object[] unDoParam;
            if (oldValue == null) {
                unDoParam = new Object[]{key, (float) keyFrame.getFloatValue(key, keyFrame.getAtTime()), new boolean[]{false}};
            } else {
                unDoParam = new Object[]{key, (float) oldValue, new boolean[]{false}};
            }
            Object[] redoParam = new Object[]{key, value, atTime};
            CommandUtil.saveOperate("KeyFrameSetFloatCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        keyFrame.setFloatVal(key, value);
    }

    @Undo(className = "KeyFrameSetFloatAtCommand", function = "setFloatValAt",
            param = {"String|key", "float|value", "long|atTime", "boolean...|needSaveOperate"})
    public static void setFloatValAt(MeicamKeyFrame keyFrame, String key, float value, long atTime, boolean... needSaveOperate) {
        String tag = getTag(keyFrame, key);
        String tagAndKey = tag + key + System.nanoTime();
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            Object[] unDoParam = new Object[]{key, (float) keyFrame.getFloatValue(key, atTime), atTime, new boolean[]{false}};
            Object[] redoParam = new Object[]{key, value, atTime};
            CommandUtil.saveOperate("KeyFrameSetFloatAtCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        keyFrame.setFloatValAtTime(key, value, atTime);
    }

    @Undo(className = "KeyFrameSetArbCommand", function = "setArbDataVal", param = {"String|key", "MeicamMaskRegionInfo|value", "boolean...|needSaveOperate"})
    public static void setArbDataVal(MeicamKeyFrame keyFrame, String key, MeicamMaskRegionInfo value, boolean... needSaveOperate) {
        String tag = getTag(keyFrame, key);
        String tagAndKey = tag + key;
        long atTime = keyFrame.getAtTime();
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            KeyFrameSetArbCommand command = CommandManager.getInstance().getCommand(tagAndKey, KeyFrameSetArbCommand.class);
            if (command == null) {
                command = new KeyFrameSetArbCommand(tag, new KeyFrameSetArbCommand.UndoParam(key, keyFrame.getArbValue(key, atTime), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            KeyFrameSetArbCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam != null) {
                redoParam.setKey(key);
                redoParam.setValue(value);
            } else {
                command.setRedoParam(new KeyFrameSetArbCommand.RedoParam(key, value));
            }
        }
        keyFrame.setArbDataVal(key, value);
    }

    @Undo(className = "KeyFrameSetBooleanCommand", function = "setBooleanVal", param = {"String|key", "boolean|value", "boolean...|needSaveOperate"})
    public static void setBooleanVal(MeicamKeyFrame keyFrame, String key, boolean value, boolean... needSaveOperate) {
        String tag = getTag(keyFrame, key);
        String tagAndKey = tag + key;
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            KeyFrameSetBooleanCommand command = CommandManager.getInstance().getCommand(tagAndKey, KeyFrameSetBooleanCommand.class);
            if (command == null) {
                command = new KeyFrameSetBooleanCommand(tag, new KeyFrameSetBooleanCommand.UndoParam(key, keyFrame.getBooleanValue(key, keyFrame.getAtTime()), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            KeyFrameSetBooleanCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam != null) {
                redoParam.setKey(key);
                redoParam.setValue(value);
            } else {
                command.setRedoParam(new KeyFrameSetBooleanCommand.RedoParam(key, value));
            }
        }
        keyFrame.setBooleanVal(key, value);
    }

    @Undo(className = "KeyFrameSetOffsetCommand", function = "setOffsetTime", param = {"long|offsetTime", "boolean...|needSaveOperate"})
    public static void setOffsetTime(MeicamKeyFrame keyFrame, long offsetTime, boolean... needSaveOperate) {
        List<MeicamFxParam<?>> params = keyFrame.getParams();
        if (CommonUtils.isEmpty(params)) {
            return;
        }
        String key = params.get(0).getKey();
        String tag = getTag(keyFrame, key);
        String tagAndKey = tag + key;
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            KeyFrameSetOffsetCommand command = CommandManager.getInstance().getCommand(tagAndKey, KeyFrameSetOffsetCommand.class);
            if (command == null) {
                command = new KeyFrameSetOffsetCommand(tag, new KeyFrameSetOffsetCommand.UndoParam(offsetTime, false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            KeyFrameSetOffsetCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam != null) {
                redoParam.setOffsetTime(offsetTime);
            } else {
                command.setRedoParam(new KeyFrameSetOffsetCommand.RedoParam(offsetTime));
            }
        }
        keyFrame.setOffsetTime(offsetTime);
    }

    @Undo(className = "KeyFramePosition2DCommand", function = "setPosition2DVal", param = {"String|key", "MeicamPosition2D|value", "boolean...|needSaveOperate"})
    public static void setPosition2DVal(MeicamKeyFrame keyFrame, String key, MeicamPosition2D value, boolean... needSaveOperate) {
        String tag = getTag(keyFrame, key);
        String tagAndKey = tag + key;
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            KeyFramePosition2DCommand command = CommandManager.getInstance().getCommand(tagAndKey, KeyFramePosition2DCommand.class);
            if (command == null) {
                command = new KeyFramePosition2DCommand(tag, new KeyFramePosition2DCommand.UndoParam(key, keyFrame.getPosition2DVal(key, keyFrame.getAtTime()), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            KeyFramePosition2DCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam != null) {
                redoParam.setKey(key);
                redoParam.setValue(value);
            } else {
                command.setRedoParam(new KeyFramePosition2DCommand.RedoParam(key, value));
            }
        }
        keyFrame.setPosition2DVal(key, value);
    }

    @Undo(className = "KeyFrameIntCommand", function = "setIntVal",
            param = {"String|key", "int|value", "boolean...|needSaveOperate"})
    public static void setIntVal(MeicamKeyFrame keyFrame, String key, int value, boolean... needSaveOperate) {
        String tag = getTag(keyFrame, key);
        String tagAndKey = tag + key;
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            KeyFrameIntCommand command = CommandManager.getInstance().getCommand(tagAndKey, KeyFrameIntCommand.class);
            if (command == null) {
                command = new KeyFrameIntCommand(tag, new KeyFrameIntCommand.UndoParam(key, keyFrame.getIntValue(key, keyFrame.getAtTime()), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            KeyFrameIntCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam != null) {
                redoParam.setKey(key);
                redoParam.setValue(value);
            } else {
                command.setRedoParam(new KeyFrameIntCommand.RedoParam(key, value));
            }
        }
        keyFrame.setIntVal(key, value);
    }

    @Undo(className = "KeyFrameColorCommand", function = "setColorVal", param = {"String|key", "String|value", "boolean...|needSaveOperate"})
    public static void setColorVal(MeicamKeyFrame keyFrame, String key, String value, boolean... needSaveOperate) {
        String tag = getTag(keyFrame, key);
        String tagAndKey = tag + key;
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            KeyFrameColorCommand command = CommandManager.getInstance().getCommand(tagAndKey, KeyFrameColorCommand.class);
            if (command == null) {
                command = new KeyFrameColorCommand(tag, new KeyFrameColorCommand.UndoParam(key, keyFrame.getColorValue(key, keyFrame.getAtTime()), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            KeyFrameColorCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam != null) {
                redoParam.setKey(key);
                redoParam.setValue(value);
            } else {
                command.setRedoParam(new KeyFrameColorCommand.RedoParam(key, value));
            }
        }
        keyFrame.setColor(key, value);
    }

    private static String getTag(MeicamKeyFrame keyFrame, String key) {
        return TAG + keyFrame.getExtraTag() + "[" + key + "|" + keyFrame.getAtTime();
    }

    public static MeicamKeyFrame getItByTag(String tag) {
        try {
            tag = tag.replaceAll(TAG, "");
            int index = tag.lastIndexOf("[");
            String extraTag = "";
            if (index > 0) {
                extraTag = tag.substring(0, index);
            }
            String lastTag = tag.substring(index + 1);
            String[] subSplit = lastTag.split("\\|");
            long atTime = Long.parseLong(subSplit[subSplit.length - 1]);
            String key = subSplit[subSplit.length - 2];
            IKeyFrameProcessor<?> frameProcessor = KeyFrameHolderCommand.getItByTag(extraTag);
            if (frameProcessor != null) {
                return frameProcessor.keyFrameProcessor().getKeyFrame(key, atTime);
            }
        } catch (Exception e) {
            LogUtils.e(e);
            LogUtils.e(tag);
        }
        return null;
    }
}
