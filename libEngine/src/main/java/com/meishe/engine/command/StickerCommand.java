package com.meishe.engine.command;

import android.graphics.PointF;

import com.meishe.annotation.Undo;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.ClipInfo;
import com.meishe.engine.bean.MeicamStickerCaptionTrack;
import com.meishe.engine.bean.MeicamStickerClip;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.StickerAnimation;

import java.util.List;
import java.util.UUID;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/8/24 16:08
 * @Description :贴纸命令 The sticker command.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class StickerCommand {
    private static final String TAG = "Sticker";

    public static final int PARAM_SCALE = 1;
    public static final int PARAM_ROTATION = 2;
    public static final int PARAM_TRANS_X = 3;
    public static final int PARAM_TRANS_Y = 4;
    public static final int PARAM_HORIZONTAL_FLIP = 5;
    public static final int PARAM_VERTICAL_FLIP = 6;
    public static final int PARAM_VOLUME = 7;
    public static final int PARAM_HAS_AUDIO = 8;
    public static final int PARAM_IS_CUSTOM_STICKER = 9;
    public static final int PARAM_COVER_IMAGE_PATH= 10;

    @Undo(className = "StickerAnimationCommand", function = "setAnimation",
            param = {"java.util.List<StickerAnimation>|allAnimation"})
    public static void setAnimation(MeicamStickerClip stickerClip, String packageId, String type, long duration, boolean... needSavaOperate){
        List<StickerAnimation> allAnimation = stickerClip.getAllAnimation();
        if (CommandUtil.needSaveOperate(needSavaOperate)) {
            String tag = getTag(stickerClip);
            StickerAnimationCommand command = CommandManager.getInstance().getCommand(tag, StickerAnimationCommand.class);
            if (command == null) {
                command = new StickerAnimationCommand(tag, new StickerAnimationCommand.UndoParam(allAnimation));
                CommandManager.getInstance().putCommand(tag, command);
            }
            command.setRedoParam(new StickerAnimationCommand.RedoParam(packageId, type, duration));
        }
        stickerClip.setAnimation(packageId, type, duration);
    }

    @Undo(className = "StickerAnimationDurationCommand", function = "changeAnimationDuration",
            param = {"String|type","long|duration", "boolean...|needSaveOperate"})
    public static void changeAnimationDuration(MeicamStickerClip stickerClip, String type, long duration, boolean... needSavaOperate) {
        if (CommandUtil.needSaveOperate(needSavaOperate)) {
            String tag = getTag(stickerClip);
            long oldDuration = 0;
            StickerAnimation animation = stickerClip.getAnimation(type);
            if (animation != null) {
                oldDuration = animation.getDuration();
            }
            StickerAnimationDurationCommand command = CommandManager.getInstance().getCommand(tag, StickerAnimationDurationCommand.class);
            if (command == null) {
                command = new StickerAnimationDurationCommand(tag, new StickerAnimationDurationCommand.UndoParam(type, oldDuration, false));
                CommandManager.getInstance().putCommand(tag, command);
            }
            StickerAnimationDurationCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam != null) {
                redoParam.setDuration(duration);
            } else {
                command.setRedoParam(new StickerAnimationDurationCommand.RedoParam(type, duration));
            }
        }
        stickerClip.changeAnimationDuration(type, duration);
    }

    @Undo(className = "StickerScaleParamCommand", function = "scaleAnimatedSticker",
            param = {"float|scaleFactor", "float|anchorX", "float|anchorY", "boolean...|needSaveOperate"})
    public static void scaleAnimatedSticker(MeicamStickerClip stickerClip, float scaleFactor, float anchorX, float anchorY, boolean... needSavaOperate) {
        if (CommandUtil.needSaveOperate(needSavaOperate)) {
            String tag = getTag(stickerClip);

            String tagAndKey = tag + UUID.randomUUID();
            float oldAnchorX = anchorX;
            float oldAnchorY = anchorY;
            PointF anchorForScale = stickerClip.getAnchorForScale();
            if (anchorForScale != null) {
                oldAnchorX = anchorForScale.x;
                oldAnchorY = anchorForScale.y;
            }
            Object[] unDoParam = new Object[]{1F / scaleFactor, oldAnchorX, oldAnchorY, new boolean[]{false}};
            Object[] redoParam = new Object[]{scaleFactor, anchorX, anchorY};
            CommandUtil.saveOperate("StickerScaleParamCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        stickerClip.scaleAnimatedSticker(scaleFactor, new PointF(anchorX, anchorY));
    }

    @Undo(className = "StickerRotateParamCommand", function = "rotateAnimatedSticker",
            param = {"float|scaleFactor", "boolean...|needSaveOperate"})
    public static void rotateAnimatedSticker(MeicamStickerClip stickerClip, float angle, boolean... needSavaOperate) {
        if (CommandUtil.needSaveOperate(needSavaOperate)) {
            String tag = getTag(stickerClip);

            String tagAndKey = tag + UUID.randomUUID();
            Object[] unDoParam = new Object[]{-angle, new boolean[]{false}};
            Object[] redoParam = new Object[]{angle};
            CommandUtil.saveOperate("StickerRotateParamCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        stickerClip.rotateAnimatedSticker(angle);
    }

    @Undo(className = "StickerTranslateCommand", function = "translateAnimatedSticker",
            param = {"float|transX", "float|transY", "boolean...|needSaveOperate"})
    public static void translateAnimatedSticker(MeicamStickerClip stickerClip, float transX, float transY,  boolean... needSavaOperate){
        stickerClip.translateAnimatedSticker(new PointF(transX, transY));
        if (CommandUtil.needSaveOperate(needSavaOperate)) {
            String tag = getTag(stickerClip);

            String tagAndKey = tag + UUID.randomUUID();
            Object[] unDoParam = new Object[]{-transX, -transY, new boolean[]{false}};
            Object[] redoParam = new Object[]{transX, transY};
            CommandUtil.saveOperate("StickerTranslateCommand", unDoParam, redoParam, tag, tagAndKey);
        }
    }

    @Undo(className = "StickerSetParamCommand", function = "setParam",
            param = {"int|paramType", "Object|param", "boolean...|needSaveOperate"})
    public static void setParam(MeicamStickerClip stickerClip, int paramType, Object param, boolean... needSavaOperate){
        Object oldParam = null;
        try {
            switch (paramType) {
                case PARAM_SCALE:
                    oldParam = stickerClip.getScale();
                    stickerClip.setScale((Float) param);
                    break;
                case PARAM_ROTATION:
                    oldParam = stickerClip.getRotation();
                    stickerClip.setRotation((Float) param);
                    break;
                case PARAM_TRANS_X:
                    oldParam = stickerClip.getTranslationX();
                    stickerClip.setTranslationX((Float) param);
                    break;
                case PARAM_TRANS_Y:
                    oldParam = stickerClip.getTranslationY();
                    stickerClip.setTranslationY((Float) param);
                    break;
                case PARAM_HORIZONTAL_FLIP:
                    oldParam = stickerClip.isHorizontalFlip();
                    stickerClip.setHorizontalFlip((Boolean) param);
                    break;
                case PARAM_VERTICAL_FLIP:
                    oldParam = stickerClip.isVerticalFlip();
                    stickerClip.setVerticalFlip((Boolean) param);
                    break;
                case PARAM_VOLUME:
                    oldParam = stickerClip.getLeftVolume();
                    stickerClip.setLeftVolume((Float) param);
                    break;
                case PARAM_HAS_AUDIO:
                    oldParam = stickerClip.isHasAudio();
                    stickerClip.setHasAudio((Boolean) param);
                    break;
                case PARAM_IS_CUSTOM_STICKER:
                    oldParam = stickerClip.getIsCustomSticker();
                    stickerClip.setIsCustomSticker((Boolean) param);
                    break;
                case PARAM_COVER_IMAGE_PATH:
                    oldParam = stickerClip.getCoverImagePath();
                    stickerClip.setCoverImagePath((String) param);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            LogUtils.e(e);
        }

        if (CommandUtil.needSaveOperate(needSavaOperate)) {
            String tag = getTag(stickerClip);
            String tagAndKey = tag + paramType;
            StickerSetParamCommand command = CommandManager.getInstance().getCommand(tagAndKey, StickerSetParamCommand.class);
            if (command == null) {
                command = new StickerSetParamCommand(tag, new StickerSetParamCommand.UndoParam(paramType, oldParam, false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            StickerSetParamCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam != null) {
                redoParam.setParamType(paramType);
                redoParam.setParam(param);
            } else {
                command.setRedoParam(new StickerSetParamCommand.RedoParam(paramType, param));
            }
        }
    }

    static void setAnimation(MeicamStickerClip stickerClip, List<StickerAnimation> allAnimation){
        if (CommonUtils.isEmpty(allAnimation)) {
            stickerClip.clearAllAnimation();
            return;
        }
        for (StickerAnimation animation : allAnimation) {
            stickerClip.setAnimation(animation.getPackageId(), animation.getType(), animation.getDuration());
        }
    }

    public static String getTag(MeicamStickerClip stickerClip){
        return TAG + stickerClip.getTrackIndex() + "|" + stickerClip.getIndex();
    }

    public static MeicamStickerClip getItByTag(String tag){
        tag = tag.replaceAll(TAG, "");
        String[] split = tag.split("\\|");
        int trackIndex = Integer.parseInt(split[0]);
        int clipIndex = Integer.parseInt(split[1]);
        MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
        MeicamStickerCaptionTrack track = timeline.findStickCaptionTrack(trackIndex);
        if (track != null) {
            ClipInfo<?> clip = track.getCaptionStickerClip(clipIndex);
            if (clip instanceof MeicamStickerClip) {
                return (MeicamStickerClip) clip;
            }
        }
        return null;
    }
}
