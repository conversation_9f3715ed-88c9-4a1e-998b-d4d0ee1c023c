package com.meishe.myvideo.audio.function;

import android.media.AudioFormat;
import android.media.AudioRecord;

import com.meishe.base.utils.LogUtils;
import com.meishe.myvideo.audio.AudioRecorder;

import android.util.Log;


/**
 * The type Recorder.
 * 此类为录音类
 */
public class Recorder {
    /**
     * An exception is thrown when starting or recording
     * 启动或录音时抛出异常
     */
    public static final int RECORDER_EXCEPTION_OCCUR = 0;

    /**
     * Recorder.read 过程中发生错误
     */
    public static final int RECORDER_READ_ERROR = 1;

    /**
     * The current recording has no permission or the recording function is occupied
     * 当前录音没有权限或者录音功能被占用
     */
    public static final int RECORDER_PERMISSION_ERROR = 3;

    public static final int TIMER_INTERVAL = 20;
    private static final String TAG = "Recorder";
    private AudioRecorder.RecordConfig mRecordConfig;
    private AudioRecord mAudioRecorder = null;
    private final RecorderCallback mCallback;
    private boolean isRecord = false;
    private Thread mThread = null;
    private short[] wave;
    private final Runnable RecordRun = new Runnable() {

        @Override
        public void run() {
            if (mCallback != null) {
                mCallback.onRecorderStart();
            }
            if ((mAudioRecorder != null) && (mAudioRecorder.getState() == 1)) {
                try {
                    mAudioRecorder.stop();
                    mAudioRecorder.startRecording();
                } catch (Exception e) {
                    LogUtils.e(e);
                    recordFailed(RECORDER_EXCEPTION_OCCUR);
                    mAudioRecorder = null;
                }
            }
            if ((mAudioRecorder != null) &&
                    (mAudioRecorder.getState() == 1) && (mAudioRecorder.getRecordingState() == 1)) {
                LogUtils.e("no recorder permission or recorder is not available right now");
                recordFailed(RECORDER_PERMISSION_ERROR);
                mAudioRecorder = null;
            }
            for (int i = 0; i < 2; i++) {
                if (mAudioRecorder == null) {
                    isRecord = false;
                    break;
                }
                mAudioRecorder.read(wave, 0, wave.length);
            }
            while (isRecord) {
                int nLen = 0;
                try {
                    nLen = mAudioRecorder.read(wave, 0, wave.length);
                } catch (Exception e) {
                    isRecord = false;
                    recordFailed(RECORDER_EXCEPTION_OCCUR);
                }
                if (nLen == wave.length) {
                    if (mCallback != null) {
                        mCallback.onRecorded(wave);
                    }
                } else {
                    recordFailed(RECORDER_READ_ERROR);
                    isRecord = false;
                }
            }
            releaseRecord();
            doRecordStop();
        }
    };


    public Recorder(AudioRecorder.RecordConfig config, RecorderCallback callback) {
        this.mCallback = callback;
        this.mRecordConfig = config;
    }

    /**
     * Sets record config.
     * 设置记录配置
     * @param config the config
     */
    public void setRecordConfig(AudioRecorder.RecordConfig config) {
        this.mRecordConfig = config;
    }


    /**
     * Start boolean.
     * 开始
     * @return the boolean
     */
    public boolean start() {
        isRecord = true;
        synchronized (this) {
            if (initializeRecord()) {
                mThread = new Thread(RecordRun);
                mThread.start();
                return true;
            }
        }
        isRecord = false;
        return false;
    }


    /**
     * Stop.
     * 停止
     */
    public void stop() {
        synchronized (this) {
            mThread = null;
            isRecord = false;
        }
    }

    /**
     * Immediate stop.
     * 立刻停止
     */
    public void immediateStop() {
        isRecord = false;
        if (mThread != null) {
            try {
                mThread.join();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        mThread = null;
    }

    /**
     * Is started boolean.
     * 开始
     * @return the boolean
     */
    public boolean isStarted() {
        return isRecord;
    }

    private boolean initializeRecord() {
        synchronized (this) {
            try {
                if (mCallback == null) {
                    Log.e(TAG, "Error VoiceRecorderCallback is  null");
                    return false;
                }
                if (mRecordConfig == null) {
                    Log.e(TAG, "Error recordConfig is null");
                    return false;
                }
                short nChannels;
                int sampleRate;
                short bSamples;
                int audioSource;
                int audioFormat;
                int channelConfig;
                if (mRecordConfig.getAudioFormat() == AudioFormat.ENCODING_PCM_16BIT) {
                    bSamples = 16;
                } else {
                    bSamples = 8;
                }

                if ((channelConfig = mRecordConfig.getChannelConfig()) == AudioFormat.CHANNEL_IN_MONO) {
                    nChannels = 1;
                } else {
                    nChannels = 2;
                }
                audioSource = mRecordConfig.getAudioSource();
                sampleRate = mRecordConfig.getSampleRate();
                audioFormat = mRecordConfig.getAudioFormat();
                int framePeriod = sampleRate * TIMER_INTERVAL / 1000;
                int bufferSize = framePeriod * 2 * bSamples * nChannels / 8;

                //一百毫秒的需要的数据大小， The required data size for 100 milliseconds
                wave = new short[framePeriod * bSamples / 8 * nChannels / 2];
                LogUtils.d( "buffersize = " + bufferSize);
                int nMinSize = AudioRecord.getMinBufferSize(sampleRate, channelConfig, audioFormat);
                if (bufferSize < nMinSize) {
                    bufferSize = nMinSize;
                     LogUtils.d("Increasing buffer size to " + bufferSize);
                }
                if (mAudioRecorder != null) {
                    releaseRecord();
                }
                mAudioRecorder = new AudioRecord(audioSource, sampleRate, channelConfig, audioFormat, bufferSize);
                if (mAudioRecorder.getState() != 1) {
                    mAudioRecorder = null;
                    recordFailed(RECORDER_PERMISSION_ERROR);
                    LogUtils.e("AudioRecord initialization failed,because of no RECORD permission or unavailable AudioRecord ");
                    throw new Exception("AudioRecord initialization failed");
                }
                return true;
            } catch (Throwable e) {
                Log.e(TAG, "Exception=" + e);
                return false;
            }
        }
    }

    private void releaseRecord() {
        synchronized (this) {
            if (mAudioRecorder != null) {
                try {
                    mAudioRecorder.stop();
                    mAudioRecorder.release();
                } catch (Exception e) {
                    LogUtils.e(e);
                }
                mAudioRecorder = null;
            }
        }
    }


    private void doRecordStop() {
        if (mCallback != null) {
            mCallback.onRecorderStop();
        }
    }

    private void recordFailed(int errorCode) {
        if (mCallback != null) {
            mCallback.onRecordedFail(errorCode);
        }
    }
}
