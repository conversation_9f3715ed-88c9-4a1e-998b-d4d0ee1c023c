package com.meishe.user.view.fragment;

import com.meishe.base.view.PullToRefreshAndPushToLoadView;
import com.meishe.engine.DownloadManager;
import com.meishe.user.R;
import com.meishe.user.bean.VideoCompileBean;
import com.meishe.user.manager.CloudCompileManager;
import com.meishe.user.view.iview.CloudDraftView;

import java.util.List;

import static com.meishe.user.bean.VideoCompileBean.STATUS_DOWNLOADING;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/9 13:28
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CloudCompileAllFragment extends BaseCloudCompileFragment implements CloudDraftView {
    private int mCurrentClickedPosition;
    private CloudCompileManager.CompileDownloadObserver mDownLoadObserver;
    private CloudCompileManager.CompileUpDownloadDataObserver mCompileUpDownloadDataObserver;

    public static CloudCompileAllFragment create() {
        return new CloudCompileAllFragment();
    }

    @Override
    public void initListener() {
        CloudCompileManager.getInstance().registerUpDownloadDataObserver(mCompileUpDownloadDataObserver = new CloudCompileManager.CompileUpDownloadDataObserver(){
            @Override
            public void onUploadDataChanged(List<VideoCompileBean> data) {
                refreshData(false);
            }
        });
        CloudCompileManager.getInstance().registerDownloadObserver(mDownLoadObserver = new CloudCompileManager.CompileDownloadObserver() {

            @Override
            public void onProgress(String tag, int progress) {
                List<VideoCompileBean> data = mAdapter.getData();
                for (int index = 0; index < data.size(); index++) {
                    VideoCompileBean bean = data.get(index);
                    if (tag.equals(bean.getJobId())) {
                        bean.setDownloadProgress(progress);
                        mAdapter.notifyItemChanged(index, 2);
                        break;
                    }
                }
            }

            @Override
            public void onSuccess(String tag, DownloadManager.DownloadParam<String> param) {
            }

            @Override
            public void onFailed(String tag) {
            }
        });

        mAdapter.setOnItemChildClickListener((adapter, view, position) -> {
            if (view.getId() == R.id.iv_delete) {
                mCurrentClickedPosition = position;
                VideoCompileBean item = mAdapter.getItem(position);
                if (item != null) {
                    mPresenter.deleteCompileSuccessJob(item.getId());
                }
            } else if (view.getId() == R.id.iv_download) {
                VideoCompileBean item = mAdapter.getItem(position);
                if (item != null) {
                    item.setStatus(STATUS_DOWNLOADING);
                    mAdapter.notifyItemChanged(position);
                    mPresenter.downloadFile(item);
                }
            }
        });

        mRefreshLayout.setOnRefreshAndLoadMoreListener(new PullToRefreshAndPushToLoadView.PullToRefreshAndPushToLoadMoreListener() {
            @Override
            public void onRefresh() {
                mPresenter.getCompileSuccessData();
            }

            @Override
            public void onLoadMore() {
                if (!mPresenter.getMoreCompileSuccessData()) {
                    finishLoading();
                }
            }
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        mPresenter.getCompileSuccessData();
    }

    @Override
    protected void onLazyLoad() {
        super.onLazyLoad();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        CloudCompileManager.getInstance().unregisterDownloadObserver(mDownLoadObserver);
        CloudCompileManager.getInstance().unregisterUpDownloadDataObserver(mCompileUpDownloadDataObserver);
    }

    @Override
    protected void initData() {
    }

    @Override
    public void refreshData(boolean isByUser) {
        if (isByUser) {
            mRefreshLayout.autoRefresh();
        } else {
            mPresenter.getCompileSuccessData();
        }
    }

    @Override
    public void onDelete(String tag) {
        mAdapter.remove(mCurrentClickedPosition);
    }
}
