package com.meishe.myvideo.audio;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.media.AudioFormat;
import android.media.MediaRecorder;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import androidx.core.content.ContextCompat;

import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.PermissionUtils;
import com.meishe.myvideo.audio.function.AudioFileHelper;
import com.meishe.myvideo.audio.function.AudioFileListener;
import com.meishe.myvideo.audio.function.Recorder;
import com.meishe.myvideo.audio.function.RecorderCallback;

import java.nio.ByteOrder;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static com.meishe.base.utils.PermissionConstants.STORAGE;
import static com.meishe.myvideo.audio.function.Recorder.RECORDER_EXCEPTION_OCCUR;
import static com.meishe.myvideo.audio.function.Recorder.RECORDER_PERMISSION_ERROR;
import static com.meishe.myvideo.audio.function.Recorder.RECORDER_READ_ERROR;
import static com.meishe.myvideo.audio.function.Recorder.TIMER_INTERVAL;


/**
 * The core class of the recording function
 * 录音功能的核心类
 */
public class AudioRecorder implements RecorderCallback, AudioFileListener {
    private final String TAG = "AudioRecorder";
    private final Handler mHandler;
    private RecordConfig config;

    private AudioFileHelper mAudioFileHelper;
    private final Recorder mRecorder;
    private AudioStatusListener mAudioStatusListener;
    private long mMaxRecordTime = 6000L;
    private long mVolumeInterval = 200L;
    private final AtomicInteger count = new AtomicInteger(0);
    private final AtomicBoolean isStarted = new AtomicBoolean(false);

    /**
     * The number of pixels per second
     * 每秒钟的像素量
     */
    private int mPerSecondPixel = 100;

    private AudioRecorder() {
        mHandler = new Handler(Looper.getMainLooper());
        mRecorder = new Recorder(config, this);
    }


    /**
     * Gets instance.
     * 获取实例
     *
     * @return the instance
     */
    public static AudioRecorder getInstance() {
        return RecorderHolder.instance;
    }

    /**
     * Sets record config.
     * 设置记录配置
     *
     * @param config the config
     * @return the record config
     */
    public AudioRecorder setRecordConfig(RecordConfig config) {
        this.config = config;
        mAudioFileHelper = new AudioFileHelper(this);
        mAudioFileHelper.setRecorderConfig(this.config);
        mRecorder.setRecordConfig(config);
        return this;
    }

    /**
     * 设置最长语音
     *
     * @param maxRecordTimeMillis 最长录音时间 单位 毫秒
     * @return max record time
     */
    public AudioRecorder setMaxRecordTime(long maxRecordTimeMillis) {
        this.mMaxRecordTime = maxRecordTimeMillis;
        return this;
    }


    /**
     * Setting the volume callback length in milliseconds must be an integer multiple of 100 milliseconds
     * 设置音量回调时长 单位毫秒 必须为100毫秒的整数倍
     *
     * @param intervalMillis 音量回调间隔时长
     * @return volume interval
     */
    public AudioRecorder setVolumeInterval(long intervalMillis) {
        if (intervalMillis < 100) {
            LogUtils.e("Volume interval should at least 100 Millisecond .Current set will not take effect, default interval is 200ms");
            return this;
        }
        if (intervalMillis % TIMER_INTERVAL != 0) {
            intervalMillis = intervalMillis / TIMER_INTERVAL * TIMER_INTERVAL;
            LogUtils.e("Current interval is changed to " + intervalMillis);
        }
        this.mVolumeInterval = intervalMillis;
        return this;
    }


    /**
     * Set the recording save path to save format waV
     * 设置录音保存路径 保存格式为wav
     *
     * @param path 文件保存绝对路径
     * @return the record file path
     */
    public AudioRecorder setRecordFilePath(String path) {
        if (!TextUtils.isEmpty(path)) {
            mAudioFileHelper.setSavePath(path);
        } else {
            mAudioFileHelper.setSavePath(null);
            mAudioFileHelper = null;
        }
        return this;
    }

    /**
     * Set the recording save path to save format waV
     * 设置录音保存路径 保存格式为wav
     *
     * @param name String 文件名称
     * @return the record file name
     */
    public AudioRecorder setRecordFileName(String name) {
        if (!TextUtils.isEmpty(name) && mAudioFileHelper != null) {
            mAudioFileHelper.setAudioName(name);
        }
        return this;
    }

    /**
     * Sets the number of pixels per second in the waveform and the number of data converted to a pixel
     * 设置波形每秒钟有多少像素，转化成一像素对应多少个数据
     *
     * @param perSecondPixel float 每秒钟中有多少像素
     * @return the per second pixel
     */
    public AudioRecorder setPerSecondPixel(int perSecondPixel) {
        this.mPerSecondPixel = perSecondPixel;
        return this;
    }

    /**
     * Set the monitoring of various states during recording
     * 设置录音时各种状态的监听
     *
     * @param audioStatusListener audioStatusListener
     * @return audio status listener
     */
    public AudioRecorder setAudioStatusListener(AudioStatusListener audioStatusListener) {
        this.mAudioStatusListener = audioStatusListener;
        return this;
    }

    /**
     * start recording
     * 开始录音
     *
     * @param context the context
     * @return boolean boolean
     */
    public boolean start(Context context) {
        if (context == null) {
            Log.e(TAG, "context is null");
            return false;
        }
        if (!isRecordAudioPermissionGranted(context) || !isWriteExternalStoragePermissionGranted(context)) {
            Log.e(TAG, "no permission");
            return false;
        }
        if (isStarted.compareAndSet(false, true)) {
            mRecorder.start();
            return true;
        } else {
            return false;
        }
    }

    /**
     * Determine if there is permission to record
     * 判断是否有录音权限
     *
     * @return the boolean
     */
    private boolean isRecordAudioPermissionGranted(Context context) {
        return ContextCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED;
    }

    /**
     * Determines whether read and write storage permissions are available
     * 判断是否有读写存储权限
     *
     * @return the boolean
     */
    private boolean isWriteExternalStoragePermissionGranted(Context context) {
        return PermissionUtils.isGroupGranted(STORAGE);
    }

    /**
     * Record Audio Stop
     * 停止录音
     */
    public void stop() {
        if (this.isStarted.get()) {
            this.isStarted.set(false);
            this.mRecorder.immediateStop();

        } else if (this.mRecorder != null) {
            this.mRecorder.immediateStop();
        }
    }

    /**
     * Recording destruction related
     * 销毁录音相关
     */
    public void destory() {

    }

    @Override
    public boolean onRecorderStart() {
        if (mAudioFileHelper != null) {
            mAudioFileHelper.start();
        }
        count.set(0);
        runOnUi(() -> {
            if (mAudioStatusListener != null) {
                String path = mAudioFileHelper == null ? "" : mAudioFileHelper.getAudioPath();
                mAudioStatusListener.onStartRecording(path);
            }
        });
        return true;
    }

    @Override
    public void onRecorded(final short[] wave) {
        if (wave.length <= 0) {
            Log.e(TAG, "wave.length is 0");
            return;
        }
        count.incrementAndGet();
        final byte[] bytes = Shorts2Bytes(wave);
        if (mAudioFileHelper != null) {
            mAudioFileHelper.save(bytes, 0, bytes.length);
        }
        runOnUi(() -> {
            if (mAudioStatusListener != null) {
                mAudioStatusListener.onRecordData(wave, wave.length);
            }
        });
        calculateData(wave);
    }

    private float maxWave;
    private float minWave;
    private int lastRemaining;
    private short[] tempWaveData;

    /**
     * Processing waveform data
     * 处理波形数据
     */
    private void calculateData(short[] wave) {
        long temp = 0;
        //先算出每秒钟采集的数据量，再算出一像素对应的数据量
        // First calculate the amount of data collected per second,
        // and then calculate the amount of data corresponding to one pixel
        int perPixelData = wave.length * (1000 / TIMER_INTERVAL) / mPerSecondPixel;
        if (wave.length < perPixelData) {
            //如果一次采集出来的数据不够一像素对应的数据量则累加直到够一像素对应的数据
            // If the data collected at one time is not enough for the data corresponding to one pixel,
            // it will be accumulated until it is enough for the data corresponding to one pixel
            if (tempWaveData == null) {
                tempWaveData = new short[wave.length];
                System.arraycopy(wave, 0, tempWaveData, 0, wave.length);
                return;
            } else if (tempWaveData.length < perPixelData) {
                short[] newWaveData = new short[tempWaveData.length + wave.length];
                short[] oldWaveData = tempWaveData;
                System.arraycopy(oldWaveData, 0, newWaveData, 0, oldWaveData.length);
                System.arraycopy(wave, 0, newWaveData, oldWaveData.length, wave.length);
                tempWaveData = newWaveData;
                if (tempWaveData.length < perPixelData) {
                    return;
                }
            }
        } else {
            tempWaveData = wave;
        }
        final int dataLength = tempWaveData.length;
        //N组波形数据
        //N groups of waveform data
        final float[] waveData = new float[((dataLength + lastRemaining) / perPixelData) * 2];
        LogUtils.d("perPixelData=" + perPixelData + "**waveData.length=" + waveData.length + "**lastRemaining=" + lastRemaining + "**length=" + dataLength + "**wave.length=" + wave.length);
        int tempIndex = 0;
        int value;
        for (int i = 0; i < dataLength; i++) {
            value = tempWaveData[i];
            maxWave = Math.max(value, maxWave);
            minWave = Math.min(value, minWave);
            if ((i + 1 + lastRemaining) % perPixelData == 0) {
                //从每像素对应数据中取出一组波形数据
                // Take a group of waveform data from the corresponding data of each pixel
                LogUtils.d("maxWave=" + maxWave + "**minWave=" + minWave + "**tempIndex=" + tempIndex);
                waveData[tempIndex] = maxWave / 32768f;
                waveData[++tempIndex] = minWave / 32768f;
                tempIndex++;
                maxWave = 0;
                minWave = 0;
            }
            // 将 buffer 内容取出，进行平方和运算
            // Take out the buffer content and perform the square sum operation
            temp += value * value;
        }
        //The amount of data remaining (whose maximum and minimum values are not included in the previous set of waveform data) is obtained
        //得出剩余的（其最大最小值未包含在上组波形数据中）数据量
        lastRemaining = (dataLength + lastRemaining) % perPixelData;
        if (mAudioStatusListener != null && waveData.length > 0) {
            runOnUi(() -> {
                String path = mAudioFileHelper == null ? "" : mAudioFileHelper.getAudioPath();
                mAudioStatusListener.onWaveDate(waveData, count.get() * TIMER_INTERVAL, path);
            });
        }
        long recordedTime = (long) count.get() * TIMER_INTERVAL;
        if (recordedTime >= mVolumeInterval && recordedTime % mVolumeInterval == 0) {
            // 平方和除以数据总长度，得到音量大小。
            // Divide the sum of squares by the total length of the data to get the volume.
            double mean = temp / (double) tempWaveData.length;
            double volume = 10 * Math.log10(mean);
            //回调音量
            // Callback volume
            onRecorderVolume((int) volume);
        }
        if (recordedTime >= mMaxRecordTime) {
            mRecorder.stop();
            isStarted.set(false);
        }
        tempWaveData = null;
        LogUtils.d("maxWave=" + maxWave + "**minWave=" + minWave);
    }

    private byte[] Shorts2Bytes(short[] s) {
        byte bLength = 2;
        byte[] buf = new byte[s.length * bLength];
        for (int i = 0; i < s.length; i++) {
            byte[] temp = getBytes(s[i]);
            for (int j = 0; j < bLength; j++) {
                buf[(i * bLength + j)] = temp[j];
            }
        }
        return buf;
    }

    private byte[] getBytes(short s) {
        byte[] buf = new byte[2];
        if (ByteOrder.nativeOrder() == ByteOrder.BIG_ENDIAN) {
            for (int i = buf.length - 1; i >= 0; i--) {
                buf[i] = ((byte) (s & 0xFF));
                s = (short) (s >> 8);
            }
        } else {
            for (int i = 0; i < buf.length; i++) {
                buf[i] = ((byte) (s & 0xFF));
                s = (short) (s >> 8);
            }
        }
        return buf;
    }

    private void onRecorderVolume(final int volume) {

        runOnUi(() -> {
            if (mAudioStatusListener != null) {
                mAudioStatusListener.onVoiceVolume(volume);
            }
        });


    }

    @Override
    public void onRecordedFail(final int paramInt) {
        if (mAudioFileHelper != null) {
            //mAudioFileHelper.cancel();
        }
        runOnUi(() -> {
            String errorMsg;
            if (paramInt == RECORDER_EXCEPTION_OCCUR) {
                errorMsg = "Throw an exception when starting or recording";
            } else if (paramInt == RECORDER_READ_ERROR) {
                errorMsg = "An error occurred during Recorder.read()";
            } else if (paramInt == RECORDER_PERMISSION_ERROR) {
                errorMsg = "The current application does not have recording permission or the recording function is occupied";
            } else {
                errorMsg = "unknown error";
            }
            if (mAudioStatusListener != null) {
                mAudioStatusListener.onRecordError(paramInt, errorMsg);
            }
        });
    }

    @Override
    public void onRecorderStop() {
        if (mAudioFileHelper != null) {
            mAudioFileHelper.finish((long) count.get() * TIMER_INTERVAL);
        }
        lastRemaining = 0;
        count.set(0);
        runOnUi(() -> {
            if (mAudioStatusListener != null) {
                mAudioStatusListener.onStopRecording();
            }
        });
    }

    private int calculateVolume(short[] wave) {
        long v = 0;
        // 将 buffer 内容取出，进行平方和运算
        // Take out the buffer content and perform the square sum operation
        for (short value : wave) {
            v += value * value;
        }
        // 平方和除以数据总长度，得到音量大小。
        // Divide the sum of squares by the total length of data to get the volume
        double mean = v / (double) wave.length;
        double volume = 10 * Math.log10(mean);
        return (int) volume;
    }

    /**
     * Execution in the UI thread
     * 在UI线程执行
     *
     * @param runnable 需要执行的runnable
     */
    private void runOnUi(Runnable runnable) {
        mHandler.post(runnable);
    }

    /**
     * Failed to save file
     * 保存文件失败
     */
    @Override
    public void onFailure(final String reason) {
        runOnUi(() -> {
            if (mAudioStatusListener != null) {
                mAudioStatusListener.onFileSaveFailed(reason);
            }
        });
    }

    /**
     * Saved file successfully
     * 保存文件成功
     */
    @Override
    public void onSuccess(final String savePath, final long duration) {
        runOnUi(() -> {
            if (mAudioStatusListener != null) {
                mAudioStatusListener.onFileSaveSuccess(savePath, duration);
            }
        });

    }

    /**
     * The recording's configuration information is configured by default to a 16K sampling rate of 16 bits per single channel
     * 录音的配置信息  默认配置为16K采样率 单通道 16位
     * <pre>
     *      audioSource = MediaRecorder.AudioSource.MIC;
     *      sampleRate = SAMPLE_RATE_16K_HZ;
     *      channelConfig = AudioFormat.CHANNEL_IN_MONO;
     *      audioFormat = AudioFormat.ENCODING_PCM_16BIT;
     * </pre>
     */
    public static class RecordConfig {
        /**
         * The constant SAMPLE_RATE_44K_HZ.
         * 常数SAMPLE_RATE_44K_HZ
         */
        public static final int SAMPLE_RATE_44K_HZ = 44100;
        /**
         * The constant SAMPLE_RATE_22K_HZ.
         * 常数SAMPLE_RATE_22K_HZ
         */
        public static final int SAMPLE_RATE_22K_HZ = 22050;
        /**
         * The constant SAMPLE_RATE_16K_HZ.
         * 常数SAMPLE_RATE_16K_HZ
         */
        public static final int SAMPLE_RATE_16K_HZ = 16000;
        /**
         * The constant SAMPLE_RATE_11K_HZ.
         * 常数SAMPLE_RATE_11K_HZ
         */
        public static final int SAMPLE_RATE_11K_HZ = 11025;
        /**
         * The constant SAMPLE_RATE_8K_HZ.
         * 常数SAMPLE_RATE_8K_HZ
         */
        public static final int SAMPLE_RATE_8K_HZ = 8000;
        private int audioSource = MediaRecorder.AudioSource.MIC;
        private int sampleRate = SAMPLE_RATE_16K_HZ;
        private int channelConfig = AudioFormat.CHANNEL_IN_MONO;
        private int audioFormat = AudioFormat.ENCODING_PCM_16BIT;

        /**
         * A constructor for a recording configuration
         * 录音配置的构造方法
         *
         * @param audioSource   the recording source.      录音源                See {@link MediaRecorder.AudioSource} for the recording source definitions.                      recommend {@link MediaRecorder.AudioSource#MIC}
         * @param sampleRate    the sample rate expressed in Hertz. {@link RecordConfig#SAMPLE_RATE_44K_HZ} is Recommended ,采样率
         * @param channelConfig describes the configuration of the audio channels.         音频通道的配置             See {@link AudioFormat#CHANNEL_IN_MONO} and                      {@link AudioFormat#CHANNEL_IN_STEREO}.  {@link AudioFormat#CHANNEL_IN_MONO} is guaranteed                      to work on all devices.
         * @param audioFormat   the format in which the audio data is to be returned.    音频格式                  See {@link AudioFormat#ENCODING_PCM_8BIT}, {@link AudioFormat#ENCODING_PCM_16BIT},                      and {@link AudioFormat#ENCODING_PCM_FLOAT}. @link RecordConfig#SAMPLE_RATE_22K_HZ},@link RecordConfig#SAMPLE_RATE_16K_HZ},@link RecordConfig#SAMPLE_RATE_11K_HZ},@link RecordConfig#SAMPLE_RATE_8K_HZ}                      {@link AudioFormat#SAMPLE_RATE_UNSPECIFIED} means to use a route-dependent value                      which is usually the sample rate of the source.
         */
        public RecordConfig(int audioSource, int sampleRate, int channelConfig, int audioFormat) {
            this.audioSource = audioSource;
            this.sampleRate = sampleRate;
            this.channelConfig = channelConfig;
            this.audioFormat = audioFormat;
        }

        /**
         * A constructor for a recording configuration
         * 录音配置的构造方法
         */
        public RecordConfig() {

        }

        /**
         * Gets audio source.
         * 得到音频源
         *
         * @return the audio source
         */
        public int getAudioSource() {
            return audioSource;
        }

        /**
         * Sets audio source.
         * 设置音频源
         *
         * @param audioSource the recording source.                    See {@link MediaRecorder.AudioSource} for the recording source definitions.                    recommend {@link MediaRecorder.AudioSource#MIC}
         * @return the audio source
         */
        public RecordConfig setAudioSource(int audioSource) {
            this.audioSource = audioSource;
            return this;
        }

        /**
         * Gets sample rate.
         * 获取采样率
         *
         * @return the sample rate
         */
        public int getSampleRate() {
            return sampleRate;
        }

        /**
         * Sets sample rate.
         * 设置采样率
         *
         * @param sampleRate the sample rate expressed in Hertz. {@link RecordConfig#SAMPLE_RATE_44K_HZ} is Recommended ,
         * @return the sample rate
         * @link RecordConfig#SAMPLE_RATE_22K_HZ },@link RecordConfig#SAMPLE_RATE_16K_HZ},@link RecordConfig#SAMPLE_RATE_11K_HZ},@link RecordConfig#SAMPLE_RATE_8K_HZ} {@link AudioFormat#SAMPLE_RATE_UNSPECIFIED} means to use a route-dependent value which is usually the sample rate of the source.
         */
        public RecordConfig setSampleRate(int sampleRate) {
            this.sampleRate = sampleRate;
            return this;
        }

        /**
         * Gets channel config.
         * 获取道设置
         *
         * @return the channel config
         */
        public int getChannelConfig() {
            return channelConfig;
        }

        /**
         * Sets channel config.
         * 设置道设置
         *
         * @param channelConfig describes the configuration of the audio channels.                      See {@link AudioFormat#CHANNEL_IN_MONO} and                      {@link AudioFormat#CHANNEL_IN_STEREO}.  {@link AudioFormat#CHANNEL_IN_MONO} is guaranteed                      to work on all devices.
         * @return the channel config
         */
        public RecordConfig setChannelConfig(int channelConfig) {
            this.channelConfig = channelConfig;
            return this;
        }

        /**
         * Gets audio format.
         * 获取音频格式
         *
         * @return the audio format
         */
        public int getAudioFormat() {
            return audioFormat;
        }

        /**
         * Sets audio format.
         * 设置音频格式
         *
         * @param audioFormat the format in which the audio data is to be returned.                    See {@link AudioFormat#ENCODING_PCM_8BIT}, {@link AudioFormat#ENCODING_PCM_16BIT},                    and {@link AudioFormat#ENCODING_PCM_FLOAT}.
         * @return the audio format
         */
        public RecordConfig setAudioFormat(int audioFormat) {
            this.audioFormat = audioFormat;
            return this;
        }


    }

    /**
     * AudioRecorder的holder 用来初始化
     */
    private static class RecorderHolder {
        private final static AudioRecorder instance = new AudioRecorder();
    }
}
