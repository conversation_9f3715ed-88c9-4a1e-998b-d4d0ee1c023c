package com.meishe.myvideo.fragment;

import android.os.Bundle;
import android.view.View;

import com.meishe.base.model.BaseFragment;
import com.meishe.base.view.HorizontalSeekBar;
import com.meishe.engine.bean.CommonData;
import com.meishe.myvideo.R;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2022/11/1 10:59
 * @Description: 音频淡入淡出Fragment The audio fade fragment
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class AudioFadeFragment extends BaseFragment {

    private long mFadeInProgress, mFadeOutProgress;
    private VolumeFragment.EventListener mEventListener;
    private static final String BUNDLE_FADE_IN = "fadeIn";
    private static final String BUNDLE_FADE_Out = "fadeOut";
    private static final String BUNDLE_MAX_DURATION = "maxDuration";
    private HorizontalSeekBar mSeekBar;

    public AudioFadeFragment() {
    }

    public static AudioFadeFragment create(long maxDuration, long fadeIn, long fadeOut, VolumeFragment.EventListener eventListener) {
        AudioFadeFragment volumeFragment = new AudioFadeFragment();
        volumeFragment.setEventListener(eventListener);
        Bundle bundle = new Bundle();
        bundle.putLong(BUNDLE_FADE_IN, fadeIn);
        bundle.putLong(BUNDLE_FADE_Out, fadeOut);
        bundle.putLong(BUNDLE_MAX_DURATION, maxDuration);
        volumeFragment.setArguments(bundle);
        return volumeFragment;
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_audio_fade;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        mSeekBar = rootView.findViewById(R.id.seek_bar);
        mSeekBar.setTransformText(1000, 1);

        Bundle bundle = getArguments();
        if (bundle != null) {
            setSeekBarMax((int) (bundle.getLong(BUNDLE_MAX_DURATION) / 1000F));
            setProgress(bundle.getLong(BUNDLE_FADE_IN), bundle.getLong(BUNDLE_FADE_Out));
        }
        initListener();
    }

    private void initListener() {
        mSeekBar.setOnRangeListener(new HorizontalSeekBar.onRangeListener() {
            @Override
            public void onRange(float low, float big) {

            }

            @Override
            public void onLeftScrollEnd(float value) {
                mFadeInProgress = (long) (value * CommonData.TIMEBASE);
                if (mEventListener != null) {
                    mEventListener.onAudioFadeChange(mFadeInProgress, mFadeOutProgress);
                }
            }

            @Override
            public void onRightScrollEnd(float value) {
                mFadeOutProgress = (long) (value * CommonData.TIMEBASE);
                if (mEventListener != null) {
                    mEventListener.onAudioFadeChange(mFadeInProgress, mFadeOutProgress);
            }
            }
        });
    }

    @Override
    protected void initData() {

    }

    public void setEventListener(VolumeFragment.EventListener mEventListener) {
        this.mEventListener = mEventListener;
    }

    /**
     * 设置进度最大值
     * Set seek bar max value
     *
     * @param max the max 最大值
     */
    public void setSeekBarMax(int max) {
        mSeekBar.setMaxProgress(max);
    }

    /**
     * 设置进度
     * Set progress
     *
     * @param inProgress  the fade in time 淡入时间
     * @param outProgress the fade out time 淡出时间
     */
    public void setProgress(long inProgress, long outProgress) {
        mFadeInProgress = inProgress;
        mFadeOutProgress = outProgress;
        int progressFadeIn = (int) (inProgress  / 1000F);
        int progressFadeOut = (int) (outProgress / 1000F);
        mSeekBar.setLeftProgress(progressFadeIn, false);
        mSeekBar.setRightProgress(progressFadeOut);
    }
}
