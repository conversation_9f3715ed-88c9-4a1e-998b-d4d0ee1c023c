package com.meishe.engine.bean;

import com.meicam.sdk.NvsObject;
import com.meicam.sdk.NvsVideoFx;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.local.LMeicamMaskInfo;

import java.io.Serializable;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 版权所有:www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/05/24 10:07
 * @Description : 蒙版信息 The mask info
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MeicamMaskInfo implements Cloneable, Serializable, TimelineDataParserAdapter<LMeicamMaskInfo> {
    private MeicamMaskRegionInfo maskRegionInfo;
    private boolean revert;
    private float featherWidth;

    public boolean isRevert() {
        return revert;
    }

    public void setRevert(boolean revert) {
        this.revert = revert;
    }

    public void setMaskRegionInfo(MeicamMaskRegionInfo maskRegionInfo) {
        this.maskRegionInfo = maskRegionInfo;
    }

    public MeicamMaskRegionInfo getMaskRegionInfo() {
        return maskRegionInfo;
    }

    public float getFeatherWidth() {
        return featherWidth;
    }

    public void setFeatherWidth(float featherWidth) {
        if (Float.isNaN(featherWidth)) {
            return;
        }
        this.featherWidth = featherWidth;
    }

    @Override
    public LMeicamMaskInfo parseToLocalData() {
        LMeicamMaskInfo lMeicamMaskInfo = new LMeicamMaskInfo();
        lMeicamMaskInfo.setFeatherWidth(getFeatherWidth());
        lMeicamMaskInfo.setRevert(isRevert());
        if (maskRegionInfo != null) {
            lMeicamMaskInfo.setLocalMaskRegionInfo(maskRegionInfo.parseToLocalData());
        }
        return lMeicamMaskInfo;
    }

    @Override
    public void recoverFromLocalData(LMeicamMaskInfo lMaskInfo) {
        setFeatherWidth(lMaskInfo.getFeatherWidth());
        setRevert(lMaskInfo.ismRevert());
        if (lMaskInfo.getLocalMaskRegionInfo() != null) {
            MeicamMaskRegionInfo meicamMaskRegionInfo = new MeicamMaskRegionInfo();
            meicamMaskRegionInfo.recoverFromLocalData(lMaskInfo.getLocalMaskRegionInfo());
            setMaskRegionInfo(meicamMaskRegionInfo);
        }
    }

    @Override
    public void recoverFromTimelineData(NvsObject nvsObject) {
        if (nvsObject instanceof NvsVideoFx) {
            NvsVideoFx nvsVideoFx = (NvsVideoFx) nvsObject;
            setFeatherWidth((float) nvsVideoFx.getFloatVal(NvsConstants.KEY_MASK_FEATHER_WIDTH));
            setRevert(nvsVideoFx.getBooleanVal(NvsConstants.KEY_MASK_INVERSE_REGION));
            MeicamMaskRegionInfo meicamMaskRegionInfo = new MeicamMaskRegionInfo();
            meicamMaskRegionInfo.recoverFromTimelineData(nvsVideoFx);
            setMaskRegionInfo(meicamMaskRegionInfo);
        }
    }

}
