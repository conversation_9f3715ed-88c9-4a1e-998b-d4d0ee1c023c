package com.meishe.player.fragment.iview;

import com.meishe.base.model.IBaseView;
import com.meishe.player.view.bean.OperationBoxInfo;
import com.meishe.player.view.bean.PipTransformInfo;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate :2021/1/22 10:37
 * @Description :the video fragment view
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface VideoFragmentView extends IBaseView {

    /**
     * 更新特效操作框
     * Update the effects operation box
     *
     * @param info the operation box info
     */
    void onOperationBoxUpdate(OperationBoxInfo info);

    /**
     * 更新特效操作框
     * Update the effects operation box
     *
     * @param dismiss true the box will dismiss,false not
     */
    void onOperationBoxUpdate(boolean dismiss);

    /**
     * 更新画中画操作框
     * Update the effects operation box
     *
     * @param info the operation box info
     */
    void onPipOperationBoxUpdate(PipTransformInfo info);
}
