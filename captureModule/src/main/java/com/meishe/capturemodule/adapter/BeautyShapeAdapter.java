package com.meishe.capturemodule.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.capturemodule.R;
import com.meishe.capturemodule.bean.BeautyShapeDataItem;

import java.util.List;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * <AUTHOR> LiuPanFeng
 * @CreateDate : 2021/2/7 9:58
 * @Description : 美型数据适配器 The beauty shape adapter
 * @Copyright : www.meishesdk.com Inc. All rights reserved.
 */
public class BeautyShapeAdapter extends RecyclerView.Adapter<BeautyShapeAdapter.ViewHolder> {

    private List<BeautyShapeDataItem> mDataList;
    private int mSelectedPos = Integer.MAX_VALUE;
    private Context mContext;
    private OnItemClickListener mClickListener;
    private boolean mIsEnable = true;

    public BeautyShapeAdapter(Context context, List<BeautyShapeDataItem> dataList) {
        mContext = context;
        mDataList = dataList;
    }

    public void setDataList(List<BeautyShapeDataItem> data) {
        this.mDataList = data;
    }

    @SuppressLint("NotifyDataSetChanged")
    public void setEnable(boolean enable) {
        mIsEnable = enable;
        notifyDataSetChanged();
    }

    @SuppressLint("NotifyDataSetChanged")
    public void setSelectPos(int pos) {
        mSelectedPos = pos;
        notifyDataSetChanged();
    }

    public int getSelectPos() {
        return mSelectedPos;
    }

    public BeautyShapeDataItem getSelectItem() {
        if (mDataList != null && mSelectedPos >= 0 && mSelectedPos < mDataList.size()) {
            return mDataList.get(mSelectedPos);
        }
        return null;
    }

    public BeautyShapeDataItem getItem(int pos) {
        if (mDataList != null && pos >= 0 && pos < mDataList.size()) {
            return mDataList.get(pos);
        }
        return null;
    }

    public void setWittenName(int pos, String newName) {
        if (mDataList != null && pos >= 0 && pos < mDataList.size()) {
            BeautyShapeDataItem item = mDataList.get(pos);
            if (item == null) {
                return;
            }
            item.name = newName;
            notifyItemChanged(pos);
        }
    }


    public void setOnItemClickListener(OnItemClickListener listener) {
        mClickListener = listener;
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.capture_beauty_shape_item, parent, false);
        ViewHolder holder = new ViewHolder(view);
        return holder;
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, final int position) {
        BeautyShapeDataItem item = mDataList.get(position);
        if (item.resId != 0) {
            holder.shape_icon.setImageResource(item.resId);
        }
        holder.shape_name.setText(item.name);
        if (mIsEnable && mSelectedPos == position) {
            holder.shape_icon.setSelected(true);
            holder.shape_name.setTextColor(mContext.getResources().getColor(R.color.white));
            holder.shape_icon_layout.setAlpha(1.0f);
            holder.shape_name.setAlpha(1.0f);
        } else {
            holder.shape_icon.setSelected(false);
            holder.shape_name.setTextColor(mContext.getResources().getColor(R.color.color_bcbcbc));
            if (mIsEnable && mSelectedPos != position) {
                holder.shape_icon_layout.setAlpha(1.0f);
                holder.shape_name.setAlpha(0.8f);

            } else if (!mIsEnable) {
                holder.shape_icon_layout.setAlpha(0.5f);
                holder.shape_name.setAlpha(0.5f);
            }
        }

        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!mIsEnable) {
                    return;
                }
                if (mClickListener != null) {
                    notifyItemChanged(mSelectedPos);
                    mSelectedPos = position;
                    notifyItemChanged(mSelectedPos);
                    mClickListener.onItemClick(v, position);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return mDataList.size();
    }

    public interface OnItemClickListener {
        void onItemClick(View view, int position);
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        private RelativeLayout shape_icon_layout;
        private ImageView shape_icon;
        private TextView shape_name;


        public ViewHolder(View view) {
            super(view);
            shape_icon_layout = (RelativeLayout) view.findViewById(R.id.shape_icon_layout);
            shape_icon = (ImageView) view.findViewById(R.id.shape_icon);
            shape_name = (TextView) view.findViewById(R.id.shape_txt);
        }
    }

}
