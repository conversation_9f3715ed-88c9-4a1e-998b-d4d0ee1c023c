<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:background="@color/color_ff242424"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/video_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true" />

    <ImageView
        android:id="@+id/image_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true" />

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/dp30"
        android:layout_height="@dimen/dp30"
        android:layout_marginStart="@dimen/dp14"
        android:layout_marginLeft="@dimen/dp14"
        android:layout_marginTop="@dimen/dp56"
        android:contentDescription="@null"
        android:padding="@dimen/dp5"
        android:src="@mipmap/ic_cancel" />

    <TextView
        android:id="@+id/tv_selected"
        android:layout_width="@dimen/dp20"
        android:layout_height="@dimen/dp20"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_marginTop="@dimen/dp56"
        android:layout_marginEnd="@dimen/dp14"
        android:layout_marginRight="@dimen/dp14"
        android:background="@drawable/bg_annulus_white"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/sp12" />

    <ImageView
        android:id="@+id/iv_start_stop"
        android:layout_width="@dimen/dp20"
        android:layout_height="@dimen/dp20"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="@dimen/dp25"
        android:layout_marginLeft="@dimen/dp25"
        android:layout_marginBottom="@dimen/dp60"
        android:contentDescription="@null"
        app:srcCompat="@mipmap/control_bar_ic_play" />

    <SeekBar
        android:id="@+id/sb_seek_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="@dimen/dp61"
        android:layout_toStartOf="@+id/tv_current_time"
        android:layout_toLeftOf="@+id/tv_current_time"
        android:layout_toEndOf="@+id/iv_start_stop"
        android:layout_toRightOf="@+id/iv_start_stop"
        android:maxHeight="@dimen/dp2"
        android:progressDrawable="@drawable/bar_progress_white"
        android:thumb="@drawable/edit_seek_bar_thumb" />

    <TextView
        android:id="@+id/tv_current_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="@dimen/dp61"
        android:layout_toStartOf="@+id/tv_duration"
        android:layout_toLeftOf="@+id/tv_duration"
        android:text="@string/zeroZZZ"
        android:textColor="@color/white" />

    <TextView
        android:id="@+id/tv_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="@dimen/dp25"
        android:layout_marginRight="@dimen/dp25"
        android:layout_marginBottom="@dimen/dp61"
        android:text="@string/zeroZZZ"
        android:textColor="#ccffffff" />


</RelativeLayout>