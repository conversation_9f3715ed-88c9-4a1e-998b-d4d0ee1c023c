package com.meishe.engine.command;

import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.meicam.sdk.NvsMaskRegionInfo;
import com.meicam.sdk.NvsVideoFx;
import com.meishe.annotation.Undo;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.KeyFrameProcessor;
import com.meishe.engine.bean.MeicamFxParam;
import com.meishe.engine.bean.MeicamKeyFrame;
import com.meishe.engine.bean.MeicamMaskRegionInfo;
import com.meishe.engine.bean.MeicamPosition2D;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoFx;
import com.meishe.engine.constant.ColorsConstants;
import com.meishe.engine.constant.NvsConstants;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/8/24 16:07
 * @Description :视频特技命名 The video fx command
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class VideoFxCommand {
    private static final String TAG = "videoFx";

    @Undo(className = "VideoFxSetStringValCommand", function = "setStringVal",
            param = {"String|key", "String|value", "boolean...|needSaveOperate"})
    public static void setStringVal(MeicamVideoFx videoFx, String key, String value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key+"string";
            VideoFxSetStringValCommand command = CommandManager.getInstance().getCommand(tagAndKey, VideoFxSetStringValCommand.class);
            if (command == null) {
                command = new VideoFxSetStringValCommand(tag, new VideoFxSetStringValCommand.UndoParam(key, videoFx.getStringVal(key), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            VideoFxSetStringValCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam == null) {
                redoParam = new VideoFxSetStringValCommand.RedoParam(key, value);
                command.setRedoParam(redoParam);
            } else {
                redoParam.setKey(key);
                redoParam.setValue(value);
            }
        }
        videoFx.setStringVal(key, value);
    }

    @Undo(className = "VideoFxSetBooleanValCommand", function = "setBooleanVal",
            param = {"String|key", "boolean|value", " boolean...|needSaveOperate"})
    public static void setBooleanVal(MeicamVideoFx videoFx, String key, boolean value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key;
            VideoFxSetBooleanValCommand command = CommandManager.getInstance().getCommand(tagAndKey, VideoFxSetBooleanValCommand.class);
            if (command == null) {
                command = new VideoFxSetBooleanValCommand(tag, new VideoFxSetBooleanValCommand.UndoParam(key, videoFx.getBooleanVal(key), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            VideoFxSetBooleanValCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam == null) {
                redoParam = new VideoFxSetBooleanValCommand.RedoParam(key, value);
                command.setRedoParam(redoParam);
            } else {
                redoParam.setKey(key);
                redoParam.setValue(value);
            }
        }
        videoFx.setBooleanVal(key, value);
    }

    @Undo(className = "VideoFxSetObjectValCommand", function = "setObjectVal",
            param = {"String|key", "Object|value", " boolean...|needSaveOperate"})
    public static void setObjectVal(MeicamVideoFx videoFx, String key, Object value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key;
            VideoFxSetObjectValCommand command = CommandManager.getInstance().getCommand(tagAndKey, VideoFxSetObjectValCommand.class);
            if (command == null) {
                command = new VideoFxSetObjectValCommand(tag, new VideoFxSetObjectValCommand.UndoParam(key, videoFx.getObjectVal(key), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            VideoFxSetObjectValCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam == null) {
                redoParam = new VideoFxSetObjectValCommand.RedoParam(key, value);
                command.setRedoParam(redoParam);
            } else {
                redoParam.setKey(key);
                redoParam.setValue(value);
            }
        }
        videoFx.setObjectVal(key, value);
    }

    @Undo(className = "VideoFxSetFloatValCommand", function = "setFloatVal",
            param = {"String|key", "float|value", " boolean...|needSaveOperate"})
    public static void setFloatVal(MeicamVideoFx videoFx, String key, float value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key;
            VideoFxSetFloatValCommand command = CommandManager.getInstance().getCommand(tagAndKey, VideoFxSetFloatValCommand.class);
            if (command == null) {
                command = new VideoFxSetFloatValCommand(tag, new VideoFxSetFloatValCommand.UndoParam(key, videoFx.getFloatVal(key, 0), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            VideoFxSetFloatValCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam == null) {
                redoParam = new VideoFxSetFloatValCommand.RedoParam(key, value);
                command.setRedoParam(redoParam);
            } else {
                redoParam.setKey(key);
                redoParam.setValue(value);
            }
        }
        videoFx.setFloatVal(key, value);
    }

    @Undo(className = "VideoFxSetIntValCommand", function = "setFloatVal",
            param = {"String|key", "int|value", " boolean...|needSaveOperate"})
    public static void setIntVal(MeicamVideoFx videoFx, String key, int value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key;
            VideoFxSetIntValCommand command = CommandManager.getInstance().getCommand(tagAndKey, VideoFxSetIntValCommand.class);
            if (command == null) {
                command = new VideoFxSetIntValCommand(tag, new VideoFxSetIntValCommand.UndoParam(key, videoFx.getIntVal(key), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            VideoFxSetIntValCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam == null) {
                redoParam = new VideoFxSetIntValCommand.RedoParam(key, value);
                command.setRedoParam(redoParam);
            } else {
                redoParam.setKey(key);
                redoParam.setValue(value);
            }
        }
        videoFx.setIntVal(key, value);
    }

    @Undo(className = "VideoFxSetPosition2DCommand", function = "setPosition2DVal",
            param = {"String|key", "MeicamPosition2D|value", " boolean...|needSaveOperate"})
    public static void setPosition2DVal(MeicamVideoFx videoFx, String key, MeicamPosition2D value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key;
            VideoFxSetPosition2DCommand command = CommandManager.getInstance().getCommand(tagAndKey, VideoFxSetPosition2DCommand.class);
            if (command == null) {
                command = new VideoFxSetPosition2DCommand(tag, new VideoFxSetPosition2DCommand.UndoParam(key, videoFx.getPosition2DVal(key), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            VideoFxSetPosition2DCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam == null) {
                redoParam = new VideoFxSetPosition2DCommand.RedoParam(key, value);
                command.setRedoParam(redoParam);
            } else {
                redoParam.setKey(key);
                redoParam.setValue(value);
            }
        }
        videoFx.setPosition2DVal(key, value);
    }

    @Undo(className = "VideoFxSetColorCommand", function = "setColorVal",
            param = {"String|key", "String|value", " boolean...|needSaveOperate"})
    public static void setColorVal(MeicamVideoFx videoFx, String key, String value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key+"color";
            VideoFxSetColorCommand command = CommandManager.getInstance().getCommand(tagAndKey, VideoFxSetColorCommand.class);
            if (command == null) {
                command = new VideoFxSetColorCommand(tag, new VideoFxSetColorCommand.UndoParam(key, videoFx.getColor(key), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            VideoFxSetColorCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam == null) {
                redoParam = new VideoFxSetColorCommand.RedoParam(key, value);
                command.setRedoParam(redoParam);
            } else {
                redoParam.setKey(key);
                redoParam.setValue(value);
            }
        }
        videoFx.setColor(key, value);
    }

    @Undo(className = "VideoFxSetMenuValCommand", function = "setMenuVal",
            param = {"String|key", "String|value", " boolean...|needSaveOperate"})
    public static void setMenuVal(MeicamVideoFx videoFx, String key, String value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key+"menu";
            VideoFxSetMenuValCommand command = CommandManager.getInstance().getCommand(tagAndKey, VideoFxSetMenuValCommand.class);
            if (command == null) {
                command = new VideoFxSetMenuValCommand(tag, new VideoFxSetMenuValCommand.UndoParam(key, videoFx.getMenuVal(key), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            VideoFxSetMenuValCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam == null) {
                redoParam = new VideoFxSetMenuValCommand.RedoParam(key, value);
                command.setRedoParam(redoParam);
            } else {
                redoParam.setKey(key);
                redoParam.setValue(value);
            }
        }
        videoFx.setMenuVal(key, value);
    }

    @Undo(className = "VideoFxSetExprValCommand", function = "setExprVar",
            param = {"String|key", "double|value", " boolean...|needSaveOperate"})
    public static void setExprVar(MeicamVideoFx videoFx, String key, double value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key;
            VideoFxSetExprValCommand command = CommandManager.getInstance().getCommand(tagAndKey, VideoFxSetExprValCommand.class);
            if (command == null) {
                double dValue = 0;
                String stringVal = videoFx.getStringVal(key);
                if (!TextUtils.isEmpty(stringVal)) {
                    dValue = Double.parseDouble(stringVal);
                }
                command = new VideoFxSetExprValCommand(tag, new VideoFxSetExprValCommand.UndoParam(key, dValue, false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            VideoFxSetExprValCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam == null) {
                redoParam = new VideoFxSetExprValCommand.RedoParam(key, value);
                command.setRedoParam(redoParam);
            } else {
                redoParam.setKey(key);
                redoParam.setValue(value);
            }
        }
        videoFx.setExprVar(key, value);
    }

    @Undo(className = "VideoFxSetIntensityCommand", function = "setIntensity",
            param = {"float|intensity", " boolean...|needSaveOperate"})
    public static void setIntensity(MeicamVideoFx videoFx, float intensity, boolean... needSaveOperate) {
        if (videoFx != null) {
            if (CommandUtil.needSaveOperate(needSaveOperate)) {
                String tag = getTag(videoFx);
                Object[] unDoParam = new Object[]{videoFx.getIntensity(), new boolean[]{false}};
                Object[] redoParam = new Object[]{intensity};
                CommandUtil.saveOperate("VideoFxSetIntensityCommand", unDoParam, redoParam, tag, tag);
            }
            videoFx.setIntensity(intensity);
        }
    }

    @Undo(className = "VideoFxChangeInPointCommand", function = "changeInPoint",
            param = {"long|inPoint", " boolean...|needSaveOperate"})
    public static void changeInPoint(MeicamVideoFx videoFx, long inPoint, boolean... needSaveOperate) {
        if (videoFx != null) {
            if (CommandUtil.needSaveOperate(needSaveOperate)) {
                String tag = getTag(videoFx);
                Object[] unDoParam = new Object[]{videoFx.getInPointInClip(), new boolean[]{false}};
                Object[] redoParam = new Object[]{inPoint};
                CommandUtil.saveOperate("VideoFxChangeInPointCommand", unDoParam, redoParam, tag, tag);
            }
            videoFx.changInPointInClip(inPoint);
        }
    }

    @Undo(className = "VideoFxChangeOutPointCommand", function = "changeOutPoint",
            param = {"long|inPoint", " boolean...|needSaveOperate"})
    public static void changeOutPoint(MeicamVideoFx videoFx, long outPoint, boolean... needSaveOperate) {
        if (videoFx != null) {
            if (CommandUtil.needSaveOperate(needSaveOperate)) {
                String tag = getTag(videoFx);
                Object[] unDoParam = new Object[]{videoFx.getOutPointInClip(), new boolean[]{false}};
                Object[] redoParam = new Object[]{outPoint};
                CommandUtil.saveOperate("VideoFxChangeOutPointCommand", unDoParam, redoParam, tag, tag);
            }
            videoFx.changOutPointInClip(outPoint);
        }
    }

    @Undo(className = "DeleteBackgroundCommand", function = "restoreFx", param = {"MeicamVideoFx|videoFx"})
    public static void deleteBackground(MeicamVideoFx videoFx, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag +"deleteBackground";
            Object[] unDoParam = new Object[]{videoFx.clone()};
            Object[] redoParam = new Object[]{};
            CommandUtil.saveOperate("DeleteBackgroundCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        videoFx.setMenuVal(NvsConstants.KEY_BACKGROUND_MODE, NvsConstants.VALUE_COLOR_BACKGROUND_MODE);
        videoFx.setColor(NvsConstants.KEY_BACKGROUND_COLOR, ColorsConstants.BACKGROUND_TRANSPARENT_COLOR);
    }

    @Undo(className = "AdjustMaskGeneratorCommand",
            function = "adjustMaskGeneratorData", param = {"String|key", "float|rateX", "float|rateY"," boolean...|needSaveOperate"})
    public static void adjustMaskGeneratorData(MeicamVideoFx videoFx, String key, float rateX, float rateY, boolean... needSaveOperate) {

        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag +"deleteBackground";
            Object[] unDoParam = new Object[]{key, 1F/ rateX, 1F / rateY, new boolean[]{false}};
            Object[] redoParam = new Object[]{key, rateX, rateY};
            CommandUtil.saveOperate("AdjustMaskGeneratorCommand", unDoParam, redoParam, tag, tagAndKey);
        }

        KeyFrameProcessor<NvsVideoFx> frameProcessor = videoFx.keyFrameProcessor();
        Map<Long, MeicamKeyFrame> keyFrameMap = frameProcessor.getKeyFrameMap(NvsConstants.KEY_PROPERTY_MASK_REGION_INFO);
        if (!keyFrameMap.isEmpty()) {
            Set<Map.Entry<Long, MeicamKeyFrame>> entries = keyFrameMap.entrySet();
            if (!entries.isEmpty()) {
                for (Map.Entry<Long, MeicamKeyFrame> entry : entries) {
                    MeicamKeyFrame keyFrame = entry.getValue();
                    if (keyFrame != null) {
                        List<MeicamFxParam<?>> keyFrameParams = keyFrame.getParams();
                        for (MeicamFxParam meicamFxParam : keyFrameParams) {
                            if (meicamFxParam == null) {
                                continue;
                            }
                            key = meicamFxParam.getKey();
                            if (NvsConstants.KEY_PROPERTY_MASK_REGION_INFO.equals(key)) {
                                Object value = meicamFxParam.getValue();
                                if (value instanceof MeicamMaskRegionInfo) {
                                    adjustMaskRegionInfo(rateX, rateY, (MeicamMaskRegionInfo) value);
                                }
                            }
                        }
                    }
                }
            }
            frameProcessor.bindToTimeline();
        } else {
            Object object = videoFx.getObjectVal(key);
            if (!(object instanceof MeicamMaskRegionInfo)) {
                return;
            }
            MeicamMaskRegionInfo regionInfo = adjustMaskRegionInfo(rateX, rateY, (MeicamMaskRegionInfo) object);
            if (regionInfo != null) {
                videoFx.setObjectVal(key, regionInfo);
            }
        }
    }

    @Undo(className = "VideoFxBindTimelineFxCommand",
            function = "removeTimelineFxTag", param = {"String|timelineCreateTag", " boolean...|needSaveOperate"})
    public static void bindTimelineFxTag(MeicamVideoFx videoFx, String timelineCreateTag, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag +"VideoFxBindTimelineFx";
            Object[] unDoParam = new Object[]{timelineCreateTag, new boolean[]{false}};
            Object[] redoParam = new Object[]{timelineCreateTag};
            CommandUtil.saveOperate("VideoFxBindTimelineFxCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        videoFx.addTimelineFxTag(timelineCreateTag);
    }

    @Undo(className = "VideoFxRemoveTimelineFxCommand",
            function = "bindTimelineFxTag", param = {"String|timelineCreateTag", " boolean...|needSaveOperate"})
    public static void removeTimelineFxTag(MeicamVideoFx videoFx, String timelineCreateTag, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag +"VideoFxBindTimelineFx";
            Object[] unDoParam = new Object[]{timelineCreateTag, new boolean[]{false}};
            Object[] redoParam = new Object[]{timelineCreateTag};
            CommandUtil.saveOperate("VideoFxRemoveTimelineFxCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        videoFx.addTimelineFxTag(timelineCreateTag);
    }

    @Nullable
    private static MeicamMaskRegionInfo adjustMaskRegionInfo(float rateX, float rateY, MeicamMaskRegionInfo object) {
        MeicamMaskRegionInfo regionInfo = object;
        List<MeicamMaskRegionInfo.RegionInfo> regionInfos = regionInfo.getLocalRegionInfoArray();
        if (regionInfos == null || (!CommonUtils.isIndexAvailable(regionInfos.size() - 1, regionInfos))) {
            return null;
        }
        MeicamMaskRegionInfo.RegionInfo info = regionInfos.get(regionInfos.size() - 1);
        if (NvsMaskRegionInfo.MASK_REGION_TYPE_ELLIPSE2D == info.getType()) {
            //圆形
            MeicamMaskRegionInfo.Ellipse2D ellipse2D = info.getEllipse2D();
            MeicamMaskRegionInfo.Transform2D transform2D = info.getTransform2D();
            ellipse2D.setA(ellipse2D.getA() * rateX);
            ellipse2D.setB(ellipse2D.getB() * rateY);
            MeicamPosition2D nvsPosition2D = ellipse2D.getCenter();
            nvsPosition2D.x *= rateX;
            nvsPosition2D.y *= rateY;
            MeicamPosition2D anchor = transform2D.getAnchor();
            anchor.x *= rateX;
            anchor.y *= rateY;
            info.setEllipse2D(ellipse2D);
        } else {
            List<MeicamPosition2D> adjustArray = new ArrayList<>();
            List<MeicamPosition2D> position2DS = info.getPoints();
            for (MeicamPosition2D nvsPosition2D : position2DS) {
                nvsPosition2D.x *= rateX;
                nvsPosition2D.y *= rateY;
                adjustArray.add(nvsPosition2D);
            }
            info.setPoints(adjustArray);
        }
        return regionInfo;
    }

    static void restoreFx(MeicamVideoFx old, MeicamVideoFx newFx) {
        old.setMenuVal(NvsConstants.KEY_BACKGROUND_MODE, newFx.getMenuVal(NvsConstants.KEY_BACKGROUND_MODE));
        old.setColor(NvsConstants.KEY_BACKGROUND_COLOR, newFx.getColor(NvsConstants.KEY_BACKGROUND_COLOR));
    }

    private static String getTag(MeicamVideoFx videoFx) {
        StringBuilder stringBuilder = new StringBuilder();
        try {
            String extraTag = videoFx.getExtraTag();
            if (!TextUtils.isEmpty(extraTag)) {
                stringBuilder.append(extraTag).append("|");
            }
            stringBuilder.append(videoFx.getType()).append("|")
                    .append(videoFx.getSubType()).append("|")
                    .append(videoFx.getDesc()).append("|")
                    .append(videoFx.getIndex());
        } catch (Exception e) {
            LogUtils.e(e.getMessage());
        }
        return stringBuilder.toString();
    }

    public static MeicamVideoFx getItByTag(String tag) {
        String[] split = tag.replaceAll(TAG, "").split("\\|");
        int videoTrackIndex = Integer.parseInt(split[0]);
        int videoClipIndex = Integer.parseInt(split[1]);
        String type = split[2];
        String subType = split[3];
        String desc = split[4];
        int index = Integer.parseInt(split[5]);
        MeicamVideoClip videoClip = EditorEngine.getInstance().getVideoClip(videoTrackIndex, videoClipIndex);
        if (videoClip != null) {
            MeicamVideoFx videoFx = videoClip.getVideoFx(type, subType, desc, index);
            if (videoFx == null) {
                LogUtils.e("find video error!,tag=" + tag);
                videoFx = videoClip.getVideoFxById(desc);
            }
            return videoFx;
        }
        return null;
    }
}
