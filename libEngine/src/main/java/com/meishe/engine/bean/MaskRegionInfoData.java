package com.meishe.engine.bean;

import com.meicam.sdk.NvsObject;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.local.LMaskRegionInfoData;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date :2020/9/21 16:07
 * @des : 构建NvRegionInfo 使用的数据源 Build the data source used by NvRegionInfo
 */
public class MaskRegionInfoData implements Cloneable, Serializable, TimelineDataParserAdapter<LMaskRegionInfoData> {
    /**
     * 锚点的X值
     * The anchor X
     */
    private float centerX;
    /**
     * 锚点的Y值
     * The anchor X
     */
    private float centerY;
    /**
     * 蒙版宽度
     * The mask width
     */
    private int maskWidth;
    /**
     * 蒙版高度
     * The mask height
     */
    private int maskHeight;
    /**
     * 旋转角度
     * The rotation
     */
    private float rotation;
    /**
     * 蒙版类型 0--6
     * The type 0--6
     */
    private int type;
    /**
     * item 名字
     * The item name
     */
    private String itemName;
    /**
     * 图标
     * The drawable icon
     */
    private int drawableIcon;
    /**
     * 是否区域反转
     * Whether the region is reversed
     */
    private boolean reverse;
    /**
     * 羽化值
     * The feather width
     */
    private float featherWidth;

    private float roundCornerRate;
    private float translationX;

    private float translationY;

    /**
     * 水平压缩值
     * The horizontal scale
     */
    private float horizontalScale = 1F;

    /**
     * 竖直方向压缩值
     * The vertical scale
     */
    private float verticalScale = 1F;


    public float getHorizontalScale() {
        return horizontalScale;
    }

    public void setHorizontalScale(float horizontalScale) {
        if(Float.isNaN(horizontalScale)){
            return;
        }
        this.horizontalScale = horizontalScale;
    }

    public float getVerticalScale() {
        return verticalScale;
    }

    public void setVerticalScale(float verticalScale) {
        if(Float.isNaN(verticalScale)){
            return;
        }
        this.verticalScale = verticalScale;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getItemName() {
        return itemName;
    }

    public void setDrawableIcon(int drawableIcon) {
        this.drawableIcon = drawableIcon;
    }

    public int getDrawableIcon() {
        return drawableIcon;
    }

    public float getCenterX() {
        return centerX;
    }

    public void setCenterX(float centerX) {
        if(Float.isNaN(centerX)){
            return;
        }
        this.centerX = centerX;
    }

    public float getCenterY() {
        return centerY;
    }

    public void setCenterY(float centerY) {
        if(Float.isNaN(centerY)){
            return;
        }
        this.centerY = centerY;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getMaskWidth() {
        return maskWidth;
    }

    public void setMaskWidth(int mMaskWidth) {
        this.maskWidth = mMaskWidth;
    }

    public int getMaskHeight() {
        return maskHeight;
    }

    public void setMaskHeight(int mMashHeight) {
        this.maskHeight = mMashHeight;
    }

    public float getRotation() {
        return rotation;
    }

    public void setRotation(float rotation) {
        this.rotation = rotation;
    }

    public void setFeatherWidth(float featherWidth) {
        if(Float.isNaN(featherWidth)){
            return;
        }
        this.featherWidth = featherWidth;
    }

    public void setReverse(boolean reverse) {
        this.reverse = reverse;
    }

    public float getFeatherWidth() {
        return featherWidth;
    }

    public boolean isReverse() {
        return reverse;
    }

    public void setRoundCornerRate(float roundCornerRate) {
        if(Float.isNaN(roundCornerRate)){
            return;
        }
        this.roundCornerRate = roundCornerRate;
    }

    public float getRoundCornerRate() {
        return roundCornerRate;
    }

    public float getTranslationX() {
        return translationX;
    }

    public void setTranslationX(float translationX) {
        if(Float.isNaN(translationX)){
            return;
        }
        this.translationX = translationX;
    }

    public float getTranslationY() {
        return translationY;
    }

    public void setTranslationY(float translationY) {
        if(Float.isNaN(translationY)){
            return;
        }
        this.translationY = translationY;
    }

    @Override
    public LMaskRegionInfoData parseToLocalData() {
        LMaskRegionInfoData lMaskRegionInfoData = new LMaskRegionInfoData();
        lMaskRegionInfoData.setDrawableIcon(getDrawableIcon());
        lMaskRegionInfoData.setFeatherWidth(getFeatherWidth());
        lMaskRegionInfoData.setItemName(getItemName());
        lMaskRegionInfoData.setmCenterX(getCenterX());
        lMaskRegionInfoData.setmCenterY(getCenterY());
        lMaskRegionInfoData.setMaskHeight(getMaskHeight());
        lMaskRegionInfoData.setMaskWidth(getMaskWidth());
        lMaskRegionInfoData.setRotation(getRotation());
        lMaskRegionInfoData.setmType(getType());
        lMaskRegionInfoData.setReverse(isReverse());
        lMaskRegionInfoData.setRoundCornerRate(getRoundCornerRate());
        lMaskRegionInfoData.setTranslationX(getTranslationX());
        lMaskRegionInfoData.setTranslationY(getTranslationY());
        lMaskRegionInfoData.setHorizontalScale(getHorizontalScale());
        lMaskRegionInfoData.setVerticalScale(getVerticalScale());
        return lMaskRegionInfoData;
    }

    @Override
    public void recoverFromLocalData(LMaskRegionInfoData local) {
        setDrawableIcon(local.getDrawableIcon());
        setFeatherWidth(local.getFeatherWidth());
        setItemName(local.getItemName());
        setCenterX(local.getmCenterX());
        setCenterY(local.getmCenterY());
        setMaskHeight(local.getMaskHeight());
        setMaskWidth(local.getMaskWidth());
        setRotation(local.getRotation());
        setType(local.getmType());
        setReverse(local.isReverse());
        setRoundCornerRate(local.getRoundCornerRate());
        setTranslationX(local.getTranslationX());
        setTranslationY(local.getTranslationY());
        setHorizontalScale(local.getHorizontalScale());
        setVerticalScale(local.getVerticalScale());
    }

    @Override
    public void recoverFromTimelineData(NvsObject nvsObject) {

    }

    public class MaskType {
        public static final int NONE = 0;
        public static final int LINE = 1;
        public static final int MIRROR = 2;
        public static final int CIRCLE = 3;
        public static final int RECT = 4;
        public static final int HEART = 5;
        public static final int STAR = 6;

    }
}
