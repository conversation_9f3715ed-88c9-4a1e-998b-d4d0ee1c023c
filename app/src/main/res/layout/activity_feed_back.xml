<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/menu_bg"
    android:paddingStart="@dimen/dp_px_40"
    android:paddingLeft="@dimen/dp_px_40"
    android:paddingEnd="@dimen/dp_px_40"
    android:paddingRight="@dimen/dp_px_40">


    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/dp_px_80"
        android:layout_height="@dimen/dp_px_80"
        android:layout_marginTop="@dimen/dp_px_100"
        android:background="@mipmap/ic_draft_back"
        android:contentDescription="@null"
        android:padding="@dimen/dp_px_7" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_80"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dp_px_100"
        android:gravity="center_vertical"
        android:text="@string/setting_feed_back"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_48" />


    <TextView
        android:id="@+id/tv_feed_back_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/iv_back"
        android:layout_marginTop="@dimen/dp_px_132"
        android:text="@string/feedback_ques_desc"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_42" />

    <View
        android:id="@+id/v_feed_back_content_bg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_680"
        android:layout_below="@+id/tv_feed_back_desc"
        android:layout_marginTop="@dimen/dp_px_36"
        android:background="@drawable/feed_back_bg" />

    <EditText
        android:id="@+id/et_feed_back_content"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_516"
        android:layout_below="@+id/tv_feed_back_desc"
        android:layout_marginLeft="@dimen/dp_px_45"
        android:layout_marginTop="@dimen/dp_px_40"
        android:layout_marginRight="@dimen/dp_px_45"
        android:background="@null"
        android:gravity="top"
        android:hint="@string/feed_back_desc"
        android:importantForAutofill="no"
        android:singleLine="false"
        android:scrollHorizontally="false"
        android:inputType="textMultiLine"
        android:textColor="@color/white_8"
        android:textColorHint="@color/color_white_33"
        android:textSize="@dimen/sp_px_42" />

    <ImageView
        android:id="@+id/iv_selected_picture"
        android:layout_width="@dimen/dp_px_120"
        android:layout_height="@dimen/dp_px_120"
        android:layout_marginTop="@dimen/dp_px_2"
        android:layout_below="@+id/et_feed_back_content"
        android:layout_marginStart="@dimen/dp_px_45"
        android:layout_marginLeft="@dimen/dp_px_45"
        android:contentDescription="@null"
        android:src="@mipmap/icon_feed_back_pic" />

    <TextView
        android:id="@+id/tv_contact_way"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/v_feed_back_content_bg"
        android:layout_marginTop="@dimen/dp_px_90"
        android:text="@string/feed_back_contact_way"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_42" />

    <EditText
        android:id="@+id/et_contact_way"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_160"
        android:layout_below="@+id/tv_contact_way"
        android:layout_marginTop="@dimen/dp_px_45"
        android:background="@drawable/feed_back_bg"
        android:gravity="center_vertical"
        android:hint="@string/feed_back_input_contact_way"
        android:importantForAutofill="no"
        android:inputType="text"
        android:paddingStart="@dimen/dp_px_45"
        android:paddingLeft="@dimen/dp_px_45"
        android:paddingEnd="@dimen/dp_px_45"
        android:paddingRight="@dimen/dp_px_45"
        android:textColor="@color/white_8"
        android:textColorHint="@color/color_white_33"
        android:textSize="@dimen/sp_px_42" />

    <TextView
        android:id="@+id/tv_submit"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_135"
        android:layout_below="@+id/et_contact_way"
        android:layout_marginTop="@dimen/dp_px_180"
        android:background="@drawable/feed_back_commit_bg"
        android:clickable="false"
        android:gravity="center"
        android:text="@string/feed_back_submit"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_48" />
</RelativeLayout>