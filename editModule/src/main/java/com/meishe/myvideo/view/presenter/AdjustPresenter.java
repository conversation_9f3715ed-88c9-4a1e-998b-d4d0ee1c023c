package com.meishe.myvideo.view.presenter;

import android.content.Context;

import com.meishe.base.utils.CommonUtils;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.bean.EditAdjustInfo;
import com.meishe.myvideo.event.MessageEvent;
import com.meishe.myvideo.view.MYAdjustMenuView;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_ADJUST;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_ADJUST_CLICK;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_APPLY_ADJUST_TO_ALL;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_CHANGE_ADJUST_FINISH;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_RESET_ADJUST_CLIP;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_RESET_ADJUST_TIMELINE;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_UPDATE_FILTER_PROGRESS;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_UPDATE_SELECT_POSITION;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 调整Presenter类
 * Adjust the Presenter class
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/28 15:22
 * @Description :Presenter of adjust menu view
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class AdjustPresenter extends BaseConfirmPresenter<MYAdjustMenuView> {

    @Override
    public void getData(Context context) {
        //  return MenuDataManager.getAdjustMenuData(context);
    }

    @Override
    public boolean updateData(int subType, boolean needForceUpdate) {
        return true;
    }

    @Override
    public void onMessageEvent(MessageEvent event) {
        super.onMessageEvent(event);
        int eventType = event.getEventType();
        if (eventType == MESSAGE_TYPE_UPDATE_FILTER_PROGRESS) {
            float floatValue = event.getFloatValue();
            getView().setProgress(floatValue / 100F);
        } else if (eventType == MESSAGE_TYPE_UPDATE_SELECT_POSITION) {
            getView().updateSelectPosition(-1);
        }
    }

    @Override
    public void onItemClicked(IBaseInfo info, boolean isSelected) {
        MessageEvent messageEvent = new MessageEvent();
        messageEvent.setStrValue(info.getEffectId());
        messageEvent.setEventType(MESSAGE_TYPE_ADJUST_CLICK);
        EventBus.getDefault().post(messageEvent);
    }

    @Override
    public void onProgressChanged(float progress, String tag, boolean isFromUser) {
        if (!isFromUser) {
            return;
        }
        MessageEvent messageEvent = new MessageEvent();
        messageEvent.setProgress((int) progress);
        messageEvent.setStrValue(tag);
        messageEvent.setEventType(MESSAGE_TYPE_ADJUST);
        EventBus.getDefault().post(messageEvent);
    }

    @Override
    public void onStopTrackingTouch() {
        MessageEvent messageEvent = new MessageEvent();
        messageEvent.setEventType(MESSAGE_TYPE_CHANGE_ADJUST_FINISH);
        EventBus.getDefault().post(messageEvent);
    }

    @Override
    public void reset(List<IBaseInfo> data) {
        if (!CommonUtils.isEmpty(data)) {
            for (IBaseInfo datum : data) {
                EditAdjustInfo adjustInfo = (EditAdjustInfo) datum;
                String desc = adjustInfo.getEffectId();
                if (NvsConstants.ADJUST_BLACKPOINT.equals(desc) || NvsConstants.ADJUST_DEGREE.equals(desc)
                        || NvsConstants.ADJUST_AMOUNT.equals(desc)) {
                    adjustInfo.setEffectStrength(0);
                } else {
                    adjustInfo.setEffectStrength(50F);
                }
            }
        }
        MessageEvent messageEvent = new MessageEvent();
        if (getView().needShowApply()) {
            messageEvent.setEventType(MESSAGE_TYPE_RESET_ADJUST_CLIP);
        } else {
            messageEvent.setEventType(MESSAGE_TYPE_RESET_ADJUST_TIMELINE);
        }
        EventBus.getDefault().post(messageEvent);
    }

    @Override
    public void confirm() {
        //MessageEvent.sendEvent(MESSAGE_TYPE_ADJUST_CONFIRM);
    }

    @Override
    public void applyToAll() {
        MessageEvent messageEvent = new MessageEvent();
        messageEvent.setEventType(MESSAGE_TYPE_APPLY_ADJUST_TO_ALL);
        EventBus.getDefault().post(messageEvent);
    }
}
