package com.meishe.draft.db;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.RoomWarnings;
import androidx.room.Update;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/5/18 15:56
 * @Description :工程信息dao.The dao of project information.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
@Dao
public interface ProjectDao {
   @Insert
   void insert(ProjectEntity... info);

   @Update
   void update(ProjectEntity... info);

   @Delete
   void delete(ProjectEntity... info);

   @Query("DELETE FROM ProjectEntity")
   void deleteAll();

   @Query("DELETE FROM ProjectEntity WHERE projectId = :project")
   void delete(String project);

   @SuppressWarnings(RoomWarnings.CURSOR_MISMATCH)
   @Query("SELECT * from ResourceEntity INNER JOIN ProjectEntity ON ProjectEntity.resourceId = ResourceEntity.id AND ProjectEntity.projectId = :id")
   List<ResourceEntity> getResource(String id);

   @Query("SELECT * FROM ProjectEntity WHERE projectId = :project AND resourceId = :resouceId")
   ProjectEntity getProject(String project, String resouceId);

   @Query("SELECT * FROM ProjectEntity")
   List<ProjectEntity> getResource();
}
