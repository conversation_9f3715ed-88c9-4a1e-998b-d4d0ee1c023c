package com.meishe.myvideo.activity.presenter;

import static com.meishe.business.assets.view.MYMultiBottomView.TYPE_MENU_EFFECT;
import static com.meishe.business.assets.view.MYMultiBottomView.TYPE_MENU_PROP;
import static com.meishe.business.assets.view.MYMultiBottomView.TYPE_MENU_TRANSITION;
import static com.meishe.engine.asset.bean.AssetInfo.ASSET_CUSTOM_CAPTION_BUBBLE;
import static com.meishe.engine.asset.bean.AssetInfo.ASSET_CUSTOM_CAPTION_FLOWER;
import static com.meishe.myvideo.fragment.BeautyShapeFragment.VIDEO_CLIP;

import android.os.Bundle;

import androidx.fragment.app.Fragment;

import com.meishe.base.constants.Constants;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.StringUtils;
import com.meishe.base.utils.Utils;
import com.meishe.business.assets.AssetUtils;
import com.meishe.business.assets.view.MYMultiBottomView;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.bean.AssetsConstants;
import com.meishe.engine.asset.bean.RequestParam;
import com.meishe.engine.asset.bean.TabParam;
import com.meishe.engine.bean.ClipInfo;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxClip;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.fragment.AudioFadeFragment;
import com.meishe.myvideo.fragment.BeautyFragment;
import com.meishe.myvideo.fragment.BeautyShapeFragment;
import com.meishe.myvideo.fragment.CaptionAnimationFragment;
import com.meishe.myvideo.fragment.CaptionBubbleFlowerFragment;
import com.meishe.myvideo.fragment.CaptionStyleFragment;
import com.meishe.myvideo.fragment.EffectFragment;
import com.meishe.myvideo.fragment.PropFragment;
import com.meishe.myvideo.fragment.StickerAllFragment;
import com.meishe.myvideo.fragment.StickerCustomFragment;
import com.meishe.myvideo.fragment.TransitionFragment;
import com.meishe.myvideo.fragment.VolumeFragment;
import com.meishe.myvideo.interfaces.BottomEventListener;
import com.meishe.myvideo.interfaces.OnAssetsClickedListener;
import com.meishe.myvideo.util.ConfigUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/1/7 16:47
 * @Description :多类型底部容器显示、刷新帮助类 The MYMultiBottomView Helper
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MultiBottomHelper {
    private final MYMultiBottomView mBottomView;

    public MultiBottomHelper(MYMultiBottomView bottomView) {
        mBottomView = bottomView;
    }

    /**
     * 展示特效列表视图
     * Show effect list view
     */
    public void showEffectView(MeicamTimelineVideoFxClip meicamTimelineVideoFxClip) {
        List<TabParam> tabList = AssetUtils.getTabList(mBottomView.getContext(), AssetsConstants.AssetsTypeData.EFFECT_DREAM.type);
        if (!CommonUtils.isEmpty(tabList)) {
            List<Fragment> fragmentList = new ArrayList<>();
            String[] tabs = new String[tabList.size()];
            for (int index = 0; index < tabList.size(); index++) {
                TabParam tabParam = tabList.get(index);
                tabs[index] = tabParam.tabName;
                fragmentList.add(EffectFragment.create(tabParam.param, meicamTimelineVideoFxClip));
            }

            mBottomView.show(tabs, fragmentList, 0, TYPE_MENU_EFFECT, true);
        }
    }

    /**
     * 展示贴纸列表视图
     * Show sticker list view
     */
    public void showStickerView(final int liveWindowWidth, final int liveWindowHeight) {
        List<Fragment> stickerList = new ArrayList<>();
        if (!ConfigUtil.isNewAssets()) {
            stickerList.add(StickerAllFragment.create(new RequestParam(AssetInfo.ASSET_ANIMATED_STICKER, -1, 0, -1), new OnAssetsClickedListener() {
                @Override
                public void onItemClicked(IBaseInfo baseInfo) {
                    if (baseInfo != null) {
                        EditorEngine.getInstance().addAnimatorSticker(baseInfo.getPackageId(), baseInfo.getCoverPath(),
                                liveWindowWidth, liveWindowHeight);
                    }
                }
            }));
            stickerList.add(StickerCustomFragment.create(new OnAssetsClickedListener() {
                @Override
                public void onItemClicked(IBaseInfo baseInfo) {
                    EditorEngine.getInstance().addCustomAnimatorSticker(baseInfo.getAssetPath(), baseInfo.getPackageId(),
                            null, liveWindowWidth, liveWindowHeight);
                }
            }));
            String[] tabs = {StringUtils.getString(R.string.fragment_menu_table_all),
                    StringUtils.getString(R.string.fragment_menu_table_custom)};
            mBottomView.showDefault(tabs, stickerList, 0, MYMultiBottomView.TYPE_MENU_STICKER);
        } else {
            List<TabParam> tabList = AssetUtils.getTabList(mBottomView.getContext(), AssetsConstants.AssetsTypeData.STICKER.type);
            if (!CommonUtils.isEmpty(tabList)) {
                String[] newTabList = new String[tabList.size() + 1];
                for (int index = 0; index < tabList.size(); index++) {
                    TabParam tabParam = tabList.get(index);
                    newTabList[index] = tabParam.tabName;
                    stickerList.add(StickerAllFragment.create(tabParam.param, new OnAssetsClickedListener() {
                        @Override
                        public void onItemClicked(IBaseInfo baseInfo) {
                            if (baseInfo != null) {
                                EditorEngine.getInstance().addAnimatorSticker(baseInfo.getPackageId(), baseInfo.getCoverPath(),
                                        liveWindowWidth, liveWindowHeight);
                            }
                        }
                    }));
                }
                newTabList[newTabList.length - 1] = StringUtils.getString(R.string.fragment_menu_table_custom);

                stickerList.add(StickerCustomFragment.create(new OnAssetsClickedListener() {
                    @Override
                    public void onItemClicked(IBaseInfo baseInfo) {
                        EditorEngine.getInstance().addCustomAnimatorSticker(baseInfo.getAssetPath(), baseInfo.getPackageId(),
                                null, liveWindowWidth, liveWindowHeight);
                    }
                }));
                mBottomView.showDefault(newTabList, stickerList, 0, MYMultiBottomView.TYPE_MENU_STICKER);
            }
        }
    }


    /**
     * 展示音量和音频淡入淡出
     * Show volume and fade.
     *
     * @param videoClip the video clip
     * @param listener  the listener
     */
    public void showVolumeAndFade(MeicamVideoClip videoClip, VolumeFragment.EventListener listener) {
        if (videoClip == null) {
            return;
        }
        List<Fragment> volumeAndFadeFragmentList = new ArrayList<>();
        int volumeProgress = (int) (videoClip.getVolume() * Constants.maxVolumeProgress / Constants.maxNvVolume);
        VolumeFragment volumeFragment = VolumeFragment.create(volumeProgress, listener);
        volumeAndFadeFragmentList.add(volumeFragment);
        AudioFadeFragment audioFadeFragment = AudioFadeFragment.create((videoClip.getTrimOut() - videoClip.getTrimIn()), videoClip.getFadeInDuration(), videoClip.getFadeOutDuration(), listener);
        volumeAndFadeFragmentList.add(audioFadeFragment);
        String[] tabs = {StringUtils.getString(R.string.sub_menu_name_edit_volume),
                StringUtils.getString(R.string.sub_menu_audio_transition_all)};
        mBottomView.showDefault(tabs, volumeAndFadeFragmentList, 0, MYMultiBottomView.TYPE_MENU_VOLUME_FADE);
    }

    /**
     * 展示美颜视图
     * Show beauty view.
     *
     * @param meicamVideoClip the video clip
     */
    public void showBeautyView(MeicamVideoClip meicamVideoClip, final BottomEventListener listener) {
        List<Fragment> beautyFragmentList = new ArrayList<>();
        BeautyFragment beautyFragment = new BeautyFragment(new BeautyFragment.BeautyEventListener() {
            @Override
            public void onConfirm() {
                mBottomView.hide();
                if (listener != null) {
                    listener.onDismiss(true);
                }
            }
        });
        Bundle bundle = new Bundle();
        bundle.putSerializable(VIDEO_CLIP, meicamVideoClip);
        beautyFragment.setArguments(bundle);
        beautyFragmentList.add(beautyFragment);
        BeautyShapeFragment beautyShapeFragment = new BeautyShapeFragment(new BottomEventListener() {
            @Override
            public void onDismiss(boolean confirm) {
                mBottomView.hide();
                if (listener != null) {
                    listener.onDismiss(confirm);
                }
                //美型编辑完成的时候，需要再次检查flag
                //When the edit of beauty and shape are finished, you need to check the flag again.
                EditorEngine.getInstance().checkBeautyShape();
            }
        });
        beautyShapeFragment.setArguments(bundle);
        beautyFragmentList.add(beautyShapeFragment);
        String[] tabs = {StringUtils.getString(R.string.fragment_menu_beauty),
                StringUtils.getString(R.string.fragment_menu_beauty_type)};
        mBottomView.showNoConfirm(tabs, beautyFragmentList, 0, MYMultiBottomView.TYPE_MENU_BEAUTY);
    }


    /**
     * 展示转场列表视图
     * Show transition list view .
     *
     * @param selectPosition  the selected tab position
     * @param transitionIndex the transition index
     * @param listener        the listener
     */
    public void showTransitionView(int selectPosition, int transitionIndex,
                                   TransitionFragment.TransitionEventListener listener) {
        List<Fragment> transitionFragmentList = new ArrayList<>();
        if (!ConfigUtil.isNewAssets()) {
            transitionFragmentList.add(TransitionFragment.getInstance(transitionIndex, TransitionFragment.TRANS_TYPE_GENERAL, listener));
            transitionFragmentList.add(TransitionFragment.getInstance(transitionIndex, TransitionFragment.TRANS_TYPE_3D, listener));
            transitionFragmentList.add(TransitionFragment.getInstance(transitionIndex, TransitionFragment.TRANS_TYPE_EFFECT, listener));
            String[] tabs = {StringUtils.getString(R.string.tab_name_common), StringUtils.getString(R.string.tab_name_3D),
                    StringUtils.getString(R.string.tab_name_effects)};
            mBottomView.show(tabs, transitionFragmentList, selectPosition, TYPE_MENU_TRANSITION,
                    false, true, false);
        } else {
            List<TabParam> tabList = AssetUtils.getTabList(mBottomView.getContext(), 5);
            if (!CommonUtils.isEmpty(tabList)) {
                String[] tabs = new String[tabList.size()];
                for (int index = 0; index < tabList.size(); index++) {
                    TabParam tabParam = tabList.get(index);
                    tabs[index] = tabParam.tabName;
                    transitionFragmentList.add(TransitionFragment.getInstance(transitionIndex, tabParam.param.categoryId, listener));
                }
                mBottomView.show(tabs, transitionFragmentList, selectPosition, TYPE_MENU_TRANSITION,
                        false, true, false);
            }
        }
    }

    /**
     * 展示字幕列表视图
     * Show caption list view
     *
     * @param clipInfo       the caption clip
     * @param keyFrameAtTime the key frame at time关键帧的时间点
     * @param keyboardHeight the keyboard height
     * @param listener       the listener
     */
    public void showCaptionView(ClipInfo<?> clipInfo, int keyboardHeight, String captionText, long keyFrameAtTime,
                                CaptionStyleFragment.CaptionStyleEventListener listener) {
        MeicamCaptionClip captionClip = null;
        if (clipInfo instanceof MeicamCaptionClip) {
            captionClip = (MeicamCaptionClip) clipInfo;
        }
        List<Fragment> fragmentList = new ArrayList<>();
        fragmentList.add(new CaptionStyleFragment(captionClip, keyFrameAtTime, listener));
        String[] tabs;
        if (captionClip == null || MeicamCaptionClip.CAPTION_TYPE_MODULAR.equals(captionClip.getCaptionType())) {
            fragmentList.add(CaptionBubbleFlowerFragment.create(ASSET_CUSTOM_CAPTION_FLOWER, captionClip));
            fragmentList.add(CaptionBubbleFlowerFragment.create(ASSET_CUSTOM_CAPTION_BUBBLE, captionClip));
            fragmentList.add(CaptionAnimationFragment.create(captionClip));
            tabs = Utils.getApp().getResources().getStringArray(R.array.menu_tab_caption_modular);
        } else {
            tabs = Utils.getApp().getResources().getStringArray(R.array.menu_tab_caption_normal);
        }
        try {
            mBottomView.show(tabs, fragmentList, 0,
                    MYMultiBottomView.TYPE_MENU_CAPTION, captionText, keyboardHeight);
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }

    /**
     * 更新字幕列表视图
     * Update the caption list view
     *
     * @param clipInfo the caption clip
     */
    public void updateCaptionView(ClipInfo<?> clipInfo) {
        if (!(clipInfo instanceof MeicamCaptionClip)) {
            return;
        }
        MeicamCaptionClip captionClip = (MeicamCaptionClip) clipInfo;
        Fragment fragment = mBottomView.getSelectedFragment();
        if (fragment instanceof CaptionStyleFragment) {
            ((CaptionStyleFragment) fragment).updateCaptionClip(captionClip);
        } else if (fragment instanceof CaptionBubbleFlowerFragment) {
            ((CaptionBubbleFlowerFragment) fragment).updateCaptionClip(captionClip);
        } else if (fragment instanceof CaptionAnimationFragment) {
            ((CaptionAnimationFragment) fragment).updateCaptionClip(captionClip);
        }
    }


    /**
     * Show prop menu.
     * 展示道具菜单
     */
    public void showPropMenu(MeicamVideoClip videoClip, OnAssetsClickedListener listener) {
        List<TabParam> tabList = AssetUtils.getTabList(mBottomView.getContext(), AssetsConstants.AssetsTypeData.PROP.type);
        if (!CommonUtils.isEmpty(tabList)) {
            List<Fragment> fragmentList = new ArrayList<>();
            String[] tabs = new String[tabList.size()];
            for (int index = 0; index < tabList.size(); index++) {
                TabParam tabParam = tabList.get(index);
                tabs[index] = tabParam.tabName;
                fragmentList.add(PropFragment.create(videoClip, tabParam.param, listener));
            }
            mBottomView.setOnViewStateListener(new MYMultiBottomView.OnViewStateListener() {
                @Override
                public void onShow() {

                }

                @Override
                public void onHide() {
                    EditorEngine.getInstance().checkBeautyShape();
                }
            });
            mBottomView.show(tabs, fragmentList, 0, TYPE_MENU_PROP, true);
        }
    }
}
