package com.meishe.logic.bean;

import java.io.Serializable;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> Li<PERSON><PERSON>Z<PERSON>
 * @CreateDate :2020/12/1 20:16
 * @Description : 设置页面的一些参数 some parameters of the setting page
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class SettingParameter implements Serializable {
    /**
     * 导出视频的分辨率
     * Exported video resolution
     */
    private int compileResolution = 3;
    /**
     * 导出视频的比特率
     * Exported video bitrate
     */
    private int compileBitrate;
    /**
     * 自动添加片尾
     * Auto append trailer
     */
    private boolean autoAppendTrailer;

    /**
     * Hdr 预览模式
     * The hdr preview mode
     */
    private int hdrPreViewMode = -1;

    /**
     * Hdr 颜色增益
     * The hdr color gain
     */
    private float hdrColorGain;

    /**
     * Hdr 位深度
     * The hdr bit depth
     */
    private int hdrBitDepth;

    public int getCompileResolution() {
        return compileResolution;
    }

    public SettingParameter setCompileResolution(int compileResolution) {
        this.compileResolution = compileResolution;
        return this;
    }

    public int getCompileBitrate() {
        return compileBitrate;
    }

    public SettingParameter setCompileBitrate(int compileBitrate) {
        this.compileBitrate = compileBitrate;
        return this;
    }

    public boolean isAutoAppendTrailer() {
        return autoAppendTrailer;
    }

    public SettingParameter setAutoAppendTrailer(boolean autoAppendTrailer) {
        this.autoAppendTrailer = autoAppendTrailer;
        return this;
    }

    public int getHdrPreViewMode() {
        return hdrPreViewMode;
    }

    public void setHdrPreViewMode(int hdrPreViewMode) {
        this.hdrPreViewMode = hdrPreViewMode;
    }

    public float getHdrColorGain() {
        return hdrColorGain;
    }

    public void setHdrColorGain(float hdrColorGain) {
        this.hdrColorGain = hdrColorGain;
    }

    public int getHdrBitDepth() {
        return hdrBitDepth;
    }

    public void setHdrBitDepth(int hdrBitDepth) {
        this.hdrBitDepth = hdrBitDepth;
    }
}
