package com.meishe.engine.local;

import java.io.Serializable;

/**
 * author：yangtailin on 2020/8/24 14:29
 */
public class LPathInfo implements Serializable {
    /**
     * 文件类型
     * the type
     */
    private String type;
    /**
     * 文件路径
     * the path
     */
    private String path;
    /**
     * 文件名称
     * the file name
     */
    private String title;
    /**
     * 是否需要转码
     * Indicates  whether transcoding is required.
     */
    private boolean needTranscode;

    public LPathInfo(String type, String path, boolean needTranscode) {
        this.type = type;
        this.path = path;
        this.needTranscode = needTranscode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public boolean isNeedTranscode() {
        return needTranscode;
    }

    public void setNeedTranscode(boolean needTranscode) {
        this.needTranscode = needTranscode;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
