package com.meishe.draft.observer;


import android.database.Observable;

import com.meishe.base.utils.ThreadUtils;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2021/5/17 14:03
 * @Description: 草稿数据被观察者 the observable of draft data
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class DraftObservable extends Observable<DraftObserver> {
    /**
     * 设置草稿发生变化
     * Set draft change
     */
    public void setDraftChange() {
        setDraftChange(null);
    }

    /**
     * Sets draft change.
     * 设置草稿发生变化
     * 防止循环调用
     * @param observer the observer 调用者
     */
    public void setDraftChange(final DraftObserver observer) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (int i = mObservers.size() - 1; i >= 0; i--) {
                    DraftObserver draftObserver = mObservers.get(i);
                    if (!draftObserver.equals(observer)) {
                        draftObserver.onDraftChanged();
                    }
                }
            }
        });
    }
}
