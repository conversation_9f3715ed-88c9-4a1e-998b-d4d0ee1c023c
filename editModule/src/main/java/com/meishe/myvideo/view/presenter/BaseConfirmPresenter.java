package com.meishe.myvideo.view.presenter;

import com.meishe.myvideo.event.MessageEvent;
import com.meishe.myvideo.view.base.BaseConfirmMenuView;
import com.meishe.myvideo.view.interf.AdjustViewContract;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 基本确认Presenter类
 * Basically validate the Presenter class
 * @param <V> the type parameter
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/28 15:22
 * @Description :Base presenter of menu view
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public abstract class BaseConfirmPresenter<V extends BaseConfirmMenuView> implements AdjustViewContract.Presenter<V> {
    V mView;

    @Override
    public void attachView(V view) {
        mView = view;
        EventBus.getDefault().register(this);
    }

    @Override
    public void detach() {
        EventBus.getDefault().unregister(this);
    }

    @Override
    public V getView() {
        return mView;
    }

    /**
     * On message event.
     * 消息事件
     * @param event the event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(MessageEvent event) {
    }

}
