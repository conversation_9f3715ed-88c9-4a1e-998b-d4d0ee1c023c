package com.meishe.engine.bean;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsArbitraryData;
import com.meicam.sdk.NvsColor;
import com.meicam.sdk.NvsMaskRegionInfo;
import com.meicam.sdk.NvsPosition2D;
import com.meicam.sdk.NvsTrackVideoFx;
import com.meicam.sdk.NvsVideoTrack;
import com.meishe.base.bean.FloatPoint;
import com.meishe.base.constants.Constants;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.GsonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.bean.bridges.AtomicFxBridge;
import com.meishe.engine.constant.ColorsConstants;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.local.LMeicamFxParam;
import com.meishe.engine.local.LMeicamMaskRegionInfo;
import com.meishe.engine.local.LMeicamTrackVideoFx;
import com.meishe.engine.local.LMeicamVideoFx;
import com.meishe.engine.util.ColorUtil;
import com.meishe.engine.util.DeepCopyUtil;
import com.meishe.engine.util.gson.GsonContext;

import java.io.Serializable;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import static com.meishe.engine.bean.MeicamFxParam.TYPE_BOOLEAN;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_COLOR;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_FLOAT;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_INT;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_MENU;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_OBJECT;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_POSITION_2D;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_STRING;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_STRING_OLD;
import static com.meishe.engine.constant.NvsConstants.AMPLITUDE;
import static com.meishe.engine.constant.NvsConstants.KEY_BACKGROUND_IMAGE_PATH;
import static com.meishe.engine.constant.NvsConstants.KEY_BACKGROUND_MODE;
import static com.meishe.engine.constant.NvsConstants.KEY_CROPPER_REGION_INFO;
import static com.meishe.engine.constant.NvsConstants.KEY_PROPERTY_MASK_REGION_INFO;
import static com.meishe.engine.constant.NvsConstants.TYPE_BUILD_IN;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 版权所有:www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/05/20 10:07
 * @Description : 特效 The track video clip fx
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MeicamTrackVideoFx extends NvsObject<NvsTrackVideoFx> implements Cloneable, Serializable,
        TimelineDataParserAdapter<LMeicamTrackVideoFx>{

    public static final float INVALID_VALUE = -10000;
    public static final int INVALID_VALUE_INT = -10000;

    /**
     * Constants for subType of effect key.
     * This value is used to mark subType value
     * by accessing {NvsTrackVideoFx.setAttachment(String key, Object value)} as first param.
     * <p>
     * 特效子类型标记key值，这个值用来调用NvsTrackVideoFx.setAttachment(String key, Object value)时的key值，
     * 用来标记NvsTrackVideoFx的子类型
     */
    public static final String ATTACHMENT_KEY_SUB_TYPE = "subType";

    /**
     * Class type tag, used to distinguish classification types when gosn deserializes
     * <p>
     * 类的类型标记，Gosn反序列化的时候用来区分类类型
     */
    protected String classType = "trackVideoFx";
    
    /**
     * type. builtin: built in; package
     * 特效类型（取值：builtin： 内建；package：包）
     */
    protected String type;
    /**
     * subTpe {@see SubType}
     * 特效次类型，{@see SubType}
     */
    protected String subType;

    /**
     * Clip description
     * If it is a built-in  effect, it is the name of the effect.
     * If it is a package  effect, it is the package ID of the  effect
     * <p>
     * 特效描述，如果是内建特效，是特效的名称，如果是包特效，则是特效的包id
     */
    protected String desc;
    /**
     * intensity
     * <p>
     * 强度
     */
    protected float intensity = 1;
    /**
     * Fx params
     * 特效参数
     */
    protected Map<String, MeicamFxParam<?>> fxParams = new TreeMap<>();

    /**
     * Indicates whether it is a local effect. true:yes false:no
     * <p>
     * 表明是否是局部的特效 true:yes false:no
     */
    private boolean isRegional = false;

    /**
     * Region data for region effect
     * <p>
     * 区域特效数据
     */
    private float[] regionData;
    
    private long inPoint;

    private long outPoint;

    /**
     * 时间线特效的标记
     * the set of timeline fx tag
     */
    private Set<String> timelineFxTagSet;

    private int flag;

    /**
     * The info for region effect of fx
     * 特效的区域特效
     */
    private MeicamMaskRegionInfo regionInfo;

    MeicamTrackVideoFx(NvsTrackVideoFx videoFx, String type, String desc, long inPoint, long duration, int flag,  String tag) {
        super(videoFx);
        this.type = type;
        this.desc = desc;
        this.inPoint = inPoint;
        this.outPoint = inPoint + duration;
        this.flag = flag;
        setCreateTag(tag);
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public float getIntensity() {
        return intensity;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public String getSubType() {
        return subType;
    }

    public boolean isRegional() {
        return isRegional;
    }

    /**
     * 设置特效区域
     * Set fx regional
     *
     * @param regional the regional 区域
     */
    public void setRegional(boolean regional) {
        NvsTrackVideoFx object = getObject();
        if (object != null) {
            object.setRegional(regional);
            isRegional = regional;
        }
    }

    public float[] getRegionData() {
        return regionData;
    }

    public void setRegionData(float[] regionData) {
        if (regionData == null) {
            LogUtils.e("regionData is null");
            return;
        }

        NvsTrackVideoFx object = getObject();
        if (object == null) {
            LogUtils.e("object is null");
            return;
        }
        object.setRegion(regionData);
        this.regionData = regionData;
    }

    public void setIntensity(float intensity) {
        if (Float.isNaN(intensity) || Float.isInfinite(intensity)) {
            return;
        }
        NvsTrackVideoFx object = getObject();
        if (object != null) {
            this.intensity = intensity;
            object.setFilterIntensity(intensity);
        }
    }

    @Deprecated
    public Map<String, MeicamFxParam<?>> getMeicamFxParam() {
        return fxParams;
    }

    @Override
    public long getInPoint() {
        return inPoint;
    }

    @Override
    public long getOutPoint() {
        return outPoint;
    }

    @Override
    void loadData() {
        NvsTrackVideoFx NvsTrackVideoFx = getObject();
        if (NvsTrackVideoFx != null) {
            intensity = NvsTrackVideoFx.getFilterIntensity();
            isRegional = NvsTrackVideoFx.getRegional();
            String builtinVideoFxName = NvsTrackVideoFx.getBuiltinTrackVideoFxName();
            if (TextUtils.isEmpty(builtinVideoFxName)) {
                desc = NvsTrackVideoFx.getTrackVideoFxPackageId();
            } else {
                //desc = builtinVideoFxName;
                List<String[]> fxParams = NvsConstants.getFxParams(builtinVideoFxName);
                if (fxParams.size() != 0) {
                    for (String[] fxParam : fxParams) {
                        String type = fxParam[0];
                        String key = fxParam[1];
                        if (TYPE_FLOAT.equals(type)) {
                            setFloatVal(key, (float) NvsTrackVideoFx.getFloatVal(key));
                        } else if (TYPE_BOOLEAN.equals(type)) {
                            setBooleanVal(key, NvsTrackVideoFx.getBooleanVal(key));
                        } else if (TYPE_STRING.equals(type)) {
                            if (AMPLITUDE.equals(key)) {
                                setExprVar(key, NvsTrackVideoFx.getExprVar(key));
                            } else if (KEY_BACKGROUND_MODE.equals(key)) {
                                setMenuVal(key, NvsTrackVideoFx.getMenuVal(key));
                            } else {
                                setStringVal(key, NvsTrackVideoFx.getStringVal(key));
                            }
                        } else if (TYPE_OBJECT.equals(type)) {
                            if (NvsConstants.KEY_MASK_REGION_INFO.equals(key)) {
                                MeicamMaskRegionInfo maskRegionInfo = new MeicamMaskRegionInfo();
                                maskRegionInfo.setRegionInfo((NvsMaskRegionInfo) getArbDataValFromNvs(NvsConstants.KEY_MASK_REGION_INFO));
                                setObjectVal(NvsConstants.KEY_MASK_REGION_INFO, maskRegionInfo);
                            }  else if (KEY_PROPERTY_MASK_REGION_INFO.equals(key)) {
                                MeicamMaskRegionInfo maskRegionInfo = new MeicamMaskRegionInfo();
                                maskRegionInfo.setRegionInfo((NvsMaskRegionInfo) getArbDataValFromNvs(NvsConstants.KEY_PROPERTY_MASK_REGION_INFO));
                                setObjectVal(NvsConstants.KEY_PROPERTY_MASK_REGION_INFO, maskRegionInfo);
                            } else if (NvsConstants.MasterKeyer.KEYER_MODE.equals(key)) {
                                setMenuVal(key, NvsTrackVideoFx.getMenuVal(key));
                            } else if (NvsConstants.MasterKeyer.KEY_COLOR.equals(key)) {
                                NvsColor colorVal = NvsTrackVideoFx.getColorVal(key);
                                if (colorVal != null) {
                                    setColor(key, ColorUtil.nvsColorToHexString(colorVal));
                                }
                            }
                        }
                    }
                } else {
                    PlugDetail plugDetail = AtomicFxBridge.getParamListByEffectID(builtinVideoFxName);
                    if (plugDetail != null) {
                        for (PlugDetail.Param param : plugDetail.paramList) {
                            String valueType = param.valueType;
                            String paramName = param.paramName;
                            if (Constants.PlugType.BOOL.equals(valueType)) {
                                //bool形式的
                                boolean nvsValue = NvsTrackVideoFx.getBooleanVal(paramName);
                                setBooleanVal(paramName, nvsValue);
                            } else if (Constants.PlugType.FLOAT.equals(valueType)) {
                                //float形式的
                                float nvsValue = (float) NvsTrackVideoFx.getFloatVal(paramName);
                                setFloatVal(paramName, nvsValue);
                            } else if (Constants.PlugType.PATH.equals(valueType)
                                    || Constants.PlugType.STRING.equals(valueType)
                                    || Constants.PlugType.CURVE.equals(valueType)) {
                                String nvsValue = NvsTrackVideoFx.getStringVal(paramName);
                                setStringVal(paramName, nvsValue);
                            } else if (Constants.PlugType.COLOR.equals(valueType)) {
                                NvsColor nvsColor = NvsTrackVideoFx.getColorVal(paramName);
                                if (nvsColor != null) {
                                    String color = ColorUtil.nvsColorToHexString(nvsColor);
                                    setColor(paramName, color);
                                }
                            } else if (Constants.PlugType.MENU.equals(valueType)) {
                                String nvsValue = NvsTrackVideoFx.getMenuVal(paramName);
                                setMenuVal(paramName, nvsValue);
                            } else if (Constants.PlugType.INT.equals(valueType) ||
                                    Constants.PlugType.INT_CHOOSE.equals(valueType)) {
                                int nvsValue = NvsTrackVideoFx.getIntVal(paramName);
                                setIntVal(paramName, nvsValue);
                            }
                        }
                    }
                }


            }
            NvsMaskRegionInfo regionInfo = NvsTrackVideoFx.getRegionInfo();
            if (regionInfo != null) {
                MeicamMaskRegionInfo maskRegionInfo = new MeicamMaskRegionInfo();
                maskRegionInfo.setRegionInfo(regionInfo);
                setRegionInfo(maskRegionInfo);
            }
        }
    }

    /**
     * Set menu value
     * 设置menu值
     *
     * @param key   key键
     * @param value value值
     */
    public void setMenuVal(String key, String value) {
        NvsTrackVideoFx object = getObject();
        if (object != null) {
            object.setMenuVal(key, value);
        }
        MeicamFxParam<String> param = new MeicamFxParam<>(TYPE_MENU, key, value);
        fxParams.put(param.getKey(), param);
    }


    /**
     * Get menu value
     * 获取menu值
     *
     * @param key key键
     */
    public String getMenuVal(String key) {
        MeicamFxParam<?> meicamFxParam = fxParams.get(key);
        if (meicamFxParam == null) {
            return null;
        }
        if (TYPE_MENU.equals(meicamFxParam.getType())) {
            return (String) meicamFxParam.getValue();
        }
        return null;
    }

    /**
     * Set Expr Var
     * 设置ExprVar值
     *
     * @param key   key键
     * @param value value值
     */
    public void setExprVar(String key, double value) {
        NvsTrackVideoFx object = getObject();
        if (object != null) {
            object.setExprVar(key, value);
        }
        MeicamFxParam<String> param = new MeicamFxParam<>(TYPE_STRING, key, String.valueOf(value));
        fxParams.put(param.getKey(), param);
    }

    /**
     * Set String value into fxParam map
     * * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value.
     * 设置String类型的特效参数，如果有这个key值，新的会替换旧的
     *
     * @param key   key
     * @param value value
     */
    public void setStringVal(String key, String value) {
        NvsTrackVideoFx object = getObject();
        if (object != null) {
            object.setStringVal(key, value);
        }
        MeicamFxParam<String> param = new MeicamFxParam<>(TYPE_STRING, key, value);
        fxParams.put(param.getKey(), param);
    }

    /**
     * Get String val from fxParam map
     * <p>
     * 从fxParam中获取String类型的值
     *
     * @param key key
     * @return value
     */
    public String getStringVal(String key) {
        MeicamFxParam<?> meicamFxParam = fxParams.get(key);
        if (meicamFxParam == null) {
            return null;
        }
        if (TYPE_STRING.equals(meicamFxParam.getType())) {
            return (String) meicamFxParam.getValue();
        }
        return null;
    }

    /**
     * Get float val from fxParam map
     * <p>
     * 从fxParam中获取float类型的值
     *
     * @param key key
     * @return value
     */
    private float getFloatVal(String key) {
        MeicamFxParam<?> meicamFxParam = fxParams.get(key);
        if (meicamFxParam == null) {
            return INVALID_VALUE;
        }
        if (TYPE_FLOAT.equals(meicamFxParam.getType())) {
            Object value = meicamFxParam.getValue();
            if (value instanceof Float) {
                return (float) value;
            } else if (value instanceof Double) {
                double resultD = (double) value;
                return (float) resultD;
            }
        }
        return INVALID_VALUE;
    }


    /**
     * Get float val from fxParam map
     * <p>
     * 从fxParam中获取float类型的值
     *
     * @param key          key
     * @param defaultValue the default value
     * @return value
     */
    public float getFloatVal(String key, float defaultValue) {
        float floatVal = getFloatVal(key);
        boolean valueEquals = Math.abs(floatVal - INVALID_VALUE) < 0.000000002;
        return valueEquals ? defaultValue : floatVal;
    }

    /**
     * Get float val from NvsObject
     * <p>
     * 从NvsObject中获取float类型的值
     *
     * @param key key
     * @return value
     */
    public float getNvsFloatVal(String key) {
        NvsTrackVideoFx object = getObject();
        if (object != null) {
            return (float) object.getFloatVal(key);
        }
        return INVALID_VALUE;
    }

    /**
     * Get boolean val from fxParam map
     * <p>
     * 从fxParam中获取boolean类型的值
     *
     * @param key key
     * @return value
     */
    public boolean getBooleanVal(String key) {
        MeicamFxParam<?> meicamFxParam = fxParams.get(key);
        if (meicamFxParam == null) {
            return false;
        }
        if (TYPE_BOOLEAN.equals(meicamFxParam.getType())) {
            Object value = meicamFxParam.getValue();
            if (value instanceof Boolean) {
                return (boolean) value;
            }
        }
        return false;
    }

    /**
     * Get  val from fxParam map
     * <p>
     * 从fxParam中是否存在该值
     *
     * @param key key
     * @return value
     */
    public boolean getParamVal(String key) {
        MeicamFxParam<?> meicamFxParam = fxParams.get(key);
        return meicamFxParam != null;
    }

    /**
     * Set Boolean value into fxParam map
     * * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value.
     * 设置Boolean类型的特效参数，如果有这个key值，新的会替换旧的
     *
     * @param key   key
     * @param value value
     */
    public void setBooleanVal(String key, boolean value) {
        NvsTrackVideoFx object = getObject();
        if (object != null) {
            object.setBooleanVal(key, value);
        }
        MeicamFxParam<Boolean> param = new MeicamFxParam<>(TYPE_BOOLEAN, key, value);
        fxParams.put(param.getKey(), param);
    }

    /**
     * Set float value into fxParam map
     * * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value.
     * 设置float类型的特效参数，如果有这个key值，新的会替换旧的
     *
     * @param key   key
     * @param value value
     */
    public void setFloatVal(String key, float value) {
        if (Float.isNaN(value) || Float.isInfinite(value)) {
            return;
        }
        NvsTrackVideoFx object = getObject();
        if (object != null) {
            object.setFloatVal(key, value);
        }
        MeicamFxParam<Float> param = new MeicamFxParam<>(TYPE_FLOAT, key, value);
        fxParams.put(param.getKey(), param);
    }

    public void setPosition2DVal(String key, MeicamPosition2D value) {

        NvsTrackVideoFx object = getObject();
        if (object != null) {
            object.setPosition2DVal(key, new NvsPosition2D(value.x, value.y));
        }
        MeicamFxParam<MeicamPosition2D> param = new MeicamFxParam<>(TYPE_POSITION_2D, key, value);
        fxParams.put(param.getKey(), param);
    }

    public MeicamPosition2D getPosition2DVal(String key) {
        MeicamFxParam<?> meicamFxParam = fxParams.get(key);
        if (meicamFxParam == null) {
            return null;
        }
        if (TYPE_POSITION_2D.equals(meicamFxParam.getType())) {
            return (MeicamPosition2D) meicamFxParam.getValue();
        }
        return null;
    }

    /**
     * Set Int value into fxParam map
     * * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value.
     * 设置int类型的特效参数，如果有这个key值，新的会替换旧的
     *
     * @param key   key
     * @param value value
     */
    public void setIntVal(String key, int value) {
        if (Float.isNaN(value)) {
            //防止gson解析报错,IllegalArgumentException: JSON forbids NaN and infinities: NaN
            //Prevent error in gson parsing:IllegalArgumentException: JSON forbids NaN and infinities: NaN.
            value = 0;
        }
        NvsTrackVideoFx object = getObject();
        if (object != null) {
            object.setIntVal(key, value);
        }
        MeicamFxParam<Integer> param = new MeicamFxParam<>(TYPE_INT, key, value);
        fxParams.put(param.getKey(), param);
    }

    /**
     * Get int val from fxParam map
     * <p>
     * 从fxParam中获取int类型的值
     *
     * @param key key
     * @return value
     */
    public int getIntVal(String key) {
        MeicamFxParam<?> meicamFxParam = fxParams.get(key);
        if (meicamFxParam == null) {
            return INVALID_VALUE_INT;
        }
        if (TYPE_INT.equals(meicamFxParam.getType())) {
            Object value = meicamFxParam.getValue();
            if (value instanceof Integer) {
                return (int) value;
            }
        }
        return INVALID_VALUE_INT;
    }

    /**
     * Set Object value into fxParam map.
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value.
     * 给特效参数设置对象，如果有这个key值，新的会替换旧的
     *
     * @param key   key
     * @param value value
     * @param <T>   参数化对象 Parameterized object
     */
    public <T> void setObjectVal(String key, T value) {
        NvsTrackVideoFx object = getObject();
        if (object != null) {
            if (value instanceof MeicamMaskRegionInfo) {
                MeicamMaskRegionInfo meicamMaskRegionInfo = (MeicamMaskRegionInfo) value;
                object.setArbDataVal(key, meicamMaskRegionInfo.getMaskRegionInfo());
            }
        }
        MeicamFxParam<T> param = new MeicamFxParam<>(TYPE_OBJECT, key, value);
        fxParams.put(param.getKey(), param);
    }

    /**
     * 从底层特效取得argb 数据
     * Get arb data from NvsFx
     *
     * @param key the key键值
     * @return NvsArbitraryData
     */
    NvsArbitraryData getArbDataValFromNvs(String key) {
        NvsTrackVideoFx NvsTrackVideoFx = getObject();
        if (NvsTrackVideoFx != null) {
            return NvsTrackVideoFx.getArbDataVal(key);
        }
        return null;
    }

    /**
     * Gets object val.
     * 获取存入的Object对象
     *
     * @param key the key
     * @return the object val
     */
    public Object getObjectVal(String key) {
        NvsTrackVideoFx object = getObject();
        if (object != null) {
            MeicamFxParam<?> meicamFxParam = fxParams.get(key);
            if (meicamFxParam != null) {
                return meicamFxParam.getValue();
            }
        }
        return null;
    }

    /**
     * 设置颜色
     * Set color
     *
     * @param key   key 键值
     * @param value color 颜色
     */
    public void setColor(String key, String value) {
        if (TextUtils.isEmpty(key) || TextUtils.isEmpty(value)) {
            return;
        }
        setNvsColor(key, ColorUtil.colorToNvsColor(value));
        MeicamFxParam<String> param = new MeicamFxParam<>(TYPE_COLOR, key, value);
        fxParams.put(param.getKey(), param);
    }

    /**
     * 设置颜色
     * Set color
     *
     * @param key   key 键值
     * @param value color 颜色
     */
    public void setColor(String key,  NvsColor value) {
        if (TextUtils.isEmpty(key) || value == null) {
            return;
        }
        setNvsColor(key, value);
        MeicamFxParam<String> param = new MeicamFxParam<>(TYPE_COLOR, key, ColorUtil.nvsColorToHexString(value));
        fxParams.put(param.getKey(), param);
    }

    /**
     * Gets color.
     * 获取颜色值
     *
     * @param key the key
     * @return the color
     */
    public String getColor(String key) {
        MeicamFxParam<?> meicamFxParam = fxParams.get(key);
        if (meicamFxParam == null) {
            return null;
        }
        if (TYPE_COLOR.equals(meicamFxParam.getType())) {
            Object value = meicamFxParam.getValue();
            if (value instanceof String) {
                return (String) value;
            }
        }
        return null;
    }


    /**
     * 设置颜色
     * Set color
     *
     * @param key   key 键值
     * @param color color 颜色
     */
    private boolean setNvsColor(String key, NvsColor color) {
        NvsTrackVideoFx object = getObject();
        if (object != null) {
            object.setColorVal(key, color);
            return true;
        }
        return false;
    }

    /**
     * Find key frame time long.
     * 在当前时间点查找某参数的关键帧时间
     *
     * @param fxParam the fx param 特效参数
     * @param time    the time 当前时间点
     * @param flags   the flags flag，用于决定向前查找还是向后查找
     * @return the long 查找的关键帧时间
     */
    public long findKeyFrameTime(String fxParam, long time, int flags) {
        return getObject().findKeyframeTime(fxParam, time, flags);
    }

    /**
     * Sets float val at time.
     * 设置某个时间的float值
     *
     * @param key   the key key值
     * @param value the value value值
     * @param time  the time 时间点
     */
    public void setFloatValAtTime(String key, float value, long time) {
        if (Float.isNaN(value) || Float.isInfinite(value)) {
            return;
        }
        NvsTrackVideoFx object = getObject();
        if (object != null) {
            object.setFloatValAtTime(key, value, time);
        }
    }

    /**
     * Get float val at time float.
     * 查找某个时间的float 值
     *
     * @param key  the key key值
     * @param time the time 时间点
     * @return the float value值
     */
    public float getFloatValAtTime(String key, long time) {
        NvsTrackVideoFx object = getObject();
        if (object != null) {
            return (float) object.getFloatValAtTime(key, time);
        }
        return INVALID_VALUE;
    }

    /**
     * 设置默认背景
     * Set default background
     */
    public void setDefaultBackground() {
        setMenuVal(KEY_BACKGROUND_MODE, NvsConstants.VALUE_COLOR_BACKGROUND_MODE);
        setColor(NvsConstants.KEY_BACKGROUND_COLOR, ColorsConstants.BACKGROUND_DEFAULT_COLOR);
        setFloatVal(NvsConstants.FX_TRANSFORM_2D_SCALE_X, 1.0F);
        setFloatVal(NvsConstants.FX_TRANSFORM_2D_SCALE_Y, 1.0F);
        setFloatVal(NvsConstants.FX_TRANSFORM_2D_TRANS_X, 0F);
        setFloatVal(NvsConstants.FX_TRANSFORM_2D_TRANS_Y, 0F);
        setFloatVal(NvsConstants.FX_TRANSFORM_2D_ROTATION, 0F);
        setFloatVal(NvsConstants.KEY_BACKGROUND_BLUR_RADIUS, -1);
        setStringVal(KEY_BACKGROUND_IMAGE_PATH, "");
        setStringVal(NvsConstants.POST_PACKAGE_ID, "");
        setStringVal(NvsConstants.PACKAGE_ID, "");
        setBooleanVal(NvsConstants.IS_POST_STORY_BOARD_3D, false);
        setFloatVal(NvsConstants.PACKAGE_EFFECT_IN, 0);
        setFloatVal(NvsConstants.PACKAGE_EFFECT_OUT, 0);
    }

    /**
     * 设置背景
     * Set  background
     */
    public void setBackground() {
        NvsTrackVideoFx NvsTrackVideoFx = getObject();
        if (NvsTrackVideoFx != null) {
            MeicamFxParam<?> meicamFxParam = fxParams.get(KEY_BACKGROUND_MODE);
            if (meicamFxParam != null && meicamFxParam.getValue().equals(NvsConstants.VALUE_COLOR_BACKGROUND_MODE)) {
                NvsTrackVideoFx.setMenuVal(KEY_BACKGROUND_MODE, NvsConstants.VALUE_COLOR_BACKGROUND_MODE);
                MeicamFxParam<?> fxParam = fxParams.get(NvsConstants.KEY_BACKGROUND_COLOR);
                if (fxParam != null && fxParam.getValue() != null) {
                    NvsTrackVideoFx.setColorVal(NvsConstants.KEY_BACKGROUND_COLOR, ColorUtil.colorToNvsColor((String) fxParam.getValue()));
                }
            } else if (meicamFxParam != null && meicamFxParam.getValue().equals(NvsConstants.VALUE_IMAGE_BACKGROUND_MODE)) {
                NvsTrackVideoFx.setMenuVal(KEY_BACKGROUND_MODE, NvsConstants.VALUE_IMAGE_BACKGROUND_MODE);
                setStringValue(KEY_BACKGROUND_IMAGE_PATH);
            } else {
                NvsTrackVideoFx.setMenuVal(KEY_BACKGROUND_MODE, NvsConstants.VALUE_BLUR_BACKGROUND_MODE);
                setFloatValue(NvsConstants.KEY_BACKGROUND_BLUR_RADIUS);
            }
            setFloatValue(NvsConstants.FX_TRANSFORM_2D_SCALE_X);
            setFloatValue(NvsConstants.FX_TRANSFORM_2D_SCALE_Y);
            setFloatValue(NvsConstants.FX_TRANSFORM_2D_TRANS_X);
            setFloatValue(NvsConstants.FX_TRANSFORM_2D_TRANS_Y);
            setFloatValue(NvsConstants.FX_TRANSFORM_2D_ROTATION);

            if (fxParams.get(NvsConstants.POST_PACKAGE_ID) == null && fxParams.get(NvsConstants.PACKAGE_ID) == null) {
                MeicamFxParam<String> paramNine = new MeicamFxParam<>("String", NvsConstants.POST_PACKAGE_ID, "");
                MeicamFxParam<Boolean> paramTen = new MeicamFxParam<>("boolean", NvsConstants.IS_POST_STORY_BOARD_3D, false);
                MeicamFxParam<Float> paramEle = new MeicamFxParam<>("float", NvsConstants.PACKAGE_EFFECT_IN, 0f);
                MeicamFxParam<Float> paramTwe = new MeicamFxParam<>("float", NvsConstants.PACKAGE_EFFECT_OUT, 0f);
                MeicamFxParam<String> paramThird = new MeicamFxParam<>("String", NvsConstants.PACKAGE_ID, "");
                fxParams.put(NvsConstants.POST_PACKAGE_ID, paramNine);
                fxParams.put(NvsConstants.IS_POST_STORY_BOARD_3D, paramTen);
                fxParams.put(NvsConstants.PACKAGE_EFFECT_IN, paramEle);
                fxParams.put(NvsConstants.PACKAGE_EFFECT_OUT, paramTwe);
                fxParams.put(NvsConstants.PACKAGE_ID, paramThird);
            }
            setStringValue(NvsConstants.POST_PACKAGE_ID);
            setStringValue(NvsConstants.PACKAGE_ID);
            setBooleanValue(NvsConstants.IS_POST_STORY_BOARD_3D);
            setFloatValue(NvsConstants.PACKAGE_EFFECT_IN);
            setFloatValue(NvsConstants.PACKAGE_EFFECT_OUT);
        }
    }

    public Set<String> getTimelineFxTagSet() {
        return timelineFxTagSet;
    }

    void setTimelineFxTagSet(Set<String> timelineFxTagSet) {
        this.timelineFxTagSet = timelineFxTagSet;
    }

    /**
     * Add timeline fx tag set.
     * 设置tag
     *
     * @param timelineFxTag the timeline fx tag
     */
    public void addTimelineFxTagSet(String timelineFxTag) {
        if (timelineFxTagSet == null) {
            timelineFxTagSet = new HashSet<>();
        }
        timelineFxTagSet.add(timelineFxTag);
    }

    /**
     * Remove timeline fx tag set.
     * 删除tag
     *
     * @param timelineFxTag the timeline fx tag
     */
    public void removeTimelineFxTagSet(String timelineFxTag) {
        if (timelineFxTagSet == null) {
            return;
        }
        timelineFxTagSet.remove(timelineFxTag);
    }

    /**
     * Has tag boolean.
     * 是否存在tag
     * @param tag the tag tag
     * @return the boolean
     */
    public boolean hasTag(String tag){
        if (timelineFxTagSet == null) {
            return false;
        }
        return timelineFxTagSet.contains(tag);
    }

    /**
     * 设置String类型属性
     * Set String value
     *
     * @param key the key 键值
     */
    private void setStringValue(String key) {
        MeicamFxParam<?> fxParam = fxParams.get(key);
        Object value;
        if (fxParam != null && fxParam.getValue() != null) {
            value = fxParam.getValue();
            if (value instanceof String) {
                getObject().setStringVal(key, (String) value);
            }
        }
    }

    /**
     * 设置boolean类型属性
     * Set boolean value
     *
     * @param key the key 键值
     */
    private void setBooleanValue(String key) {
        MeicamFxParam<?> fxParam = fxParams.get(key);
        Object value;
        boolean b = false;
        if (fxParam != null && fxParam.getValue() != null) {
            value = fxParam.getValue();
            if (value instanceof Boolean) {
                b = (boolean) value;
            }
        }
        getObject().setBooleanVal(key, b);
    }

    /**
     * 设置float类型属性
     * Set float value
     *
     * @param key the key 键值
     */
    private void setFloatValue(String key) {
        MeicamFxParam<?> fxParam = fxParams.get(key);
        Object value;
        float number;
        if (fxParam != null && fxParam.getValue() != null) {
            value = fxParam.getValue();
            if (value instanceof Float) {
                number = (float) value;
            } else {
                number = Float.parseFloat(value.toString());
            }
            getObject().setFloatVal(key, number);
        }
    }

    /**
     * Copy param.
     * 复制参数
     * @param videoFx the video fx
     */
    public void copyParam(MeicamTrackVideoFx videoFx){
        if (videoFx == null) {
            return;
        }
        setValue(getObject(), videoFx.fxParams);
    }

    private void setValue(NvsTrackVideoFx videoFx, Map<String, MeicamFxParam<?>> fxParam) {
        Set<String> keySet = fxParam.keySet();
        for (String key : keySet) {
            MeicamFxParam<?> meicamFxParam = fxParam.get(key);
            if (meicamFxParam == null) {
                continue;
            }
            if (TYPE_STRING.equals(meicamFxParam.getType())) {
                if (AMPLITUDE.equals(meicamFxParam.getKey())) {
                    Object value = meicamFxParam.getValue();
                    if (value != null) {
                        try {
                            videoFx.setExprVar(key, Double.parseDouble((String) value));
                        } catch (Exception ignore) {
                        }
                    }
                } else {
                    videoFx.setStringVal(key, (String) meicamFxParam.getValue());
                }
            } else if (TYPE_MENU.equals(meicamFxParam.getType())) {
                videoFx.setMenuVal(key, (String) meicamFxParam.getValue());
            } else if (TYPE_INT.equals(meicamFxParam.getType())) {
                Object value = meicamFxParam.getValue();
                if (value instanceof Integer) {
                    int intValue = (int) value;
                    setIntVal(meicamFxParam.getKey(), intValue);
                } else if (value instanceof Double) {
                    double dValue = (double) value;
                    setIntVal(meicamFxParam.getKey(), (int) dValue);
                }
            } else if (TYPE_BOOLEAN.equals(meicamFxParam.getType())) {
                videoFx.setBooleanVal(key, (Boolean) meicamFxParam.getValue());
            } else if (TYPE_FLOAT.equals(meicamFxParam.getType())) {
                Object value = meicamFxParam.getValue();
                if (value instanceof Float) {
                    float floatValue = (float) value;
                    videoFx.setFloatVal(key, floatValue);
                } else if (value instanceof Double) {
                    videoFx.setFloatVal(key, (Double) value);
                }
            } else if (TYPE_POSITION_2D.equals(meicamFxParam.getType())) {
                Object value = meicamFxParam.getValue();
                if (value instanceof MeicamPosition2D) {
                    MeicamPosition2D position2D = (MeicamPosition2D) value;
                    videoFx.setPosition2DVal(key, new NvsPosition2D(position2D.x, position2D.y));
                }
            } else if (TYPE_OBJECT.equals(meicamFxParam.getType())) {
                Object value = meicamFxParam.getValue();
                if (value instanceof NvsArbitraryData) {
                    videoFx.setArbDataVal(key, (NvsArbitraryData) value);
                } else if (KEY_CROPPER_REGION_INFO.equals(key) || KEY_PROPERTY_MASK_REGION_INFO.equals(key)) {
                    try {
                        String json = GsonUtils.toJson(value);
                        MeicamMaskRegionInfo maskRegionInfo = GsonUtils.fromJson(json, MeicamMaskRegionInfo.class);
                        videoFx.setArbDataVal(key, maskRegionInfo.getMaskRegionInfo());
                        return;
                    } catch (Exception e) {
                        LogUtils.e("1,error:" + e.getMessage());
                    }
                    try {
                        NvsMaskRegionInfo result = GsonUtils.fromJson(GsonUtils.toJson(value), NvsMaskRegionInfo.class);
                        if (result != null) {
                            videoFx.setArbDataVal(key, result);
                        }
                    } catch (Exception e) {
                        LogUtils.e("2,error:" + e.getMessage());
                    }
                }
            } else if (TYPE_COLOR.equals(meicamFxParam.getType())) {
                setColor(meicamFxParam.getKey(), (String) meicamFxParam.getValue());
            }
        }
    }


    void bindToTimeline() {
        NvsTrackVideoFx object = getObject();
        if (object != null) {
            setValue(object, fxParams);
            object.setFilterIntensity(getIntensity());
        }
    }

    public void setAttachment() {
        setAttachment(ATTACHMENT_KEY_SUB_TYPE, getSubType());
    }

    public MeicamMaskRegionInfo getRegionInfo() {
        return regionInfo;
    }

    public void setRegionInfo(MeicamMaskRegionInfo regionInfo) {
        if (regionInfo == null) {
            return;
        }
        NvsTrackVideoFx object = getObject();
        if (object != null) {
            object.setRegionInfo(regionInfo.getMaskRegionInfo());
            this.regionInfo = regionInfo;
        }
    }

    public void setInPoint(long inPoint) {
        NvsTrackVideoFx object = getObject();
        if (object != null) {
            object.changeInPoint(inPoint);
        }
        this.inPoint = inPoint;
    }


    public void setOutPoint(long outPoint) {
        NvsTrackVideoFx object = getObject();
        if (object != null) {
            object.changeOutPoint(outPoint);
        }
        this.outPoint = outPoint;
    }

    @NonNull
    @Override
    public MeicamTrackVideoFx clone() {
        MeicamTrackVideoFx clone = (MeicamTrackVideoFx) DeepCopyUtil.deepClone(this);
        if (clone == null) {
            String jsonData = GsonContext.getInstance().toJson(this);
            if (!TextUtils.isEmpty(jsonData)) {
                return GsonContext.getInstance().fromJson(jsonData, MeicamTrackVideoFx.class);
            }
        }
        if (clone == null) {
            clone = new MeicamTrackVideoFx(null, getType(), getDesc(), getInPoint(), getOutPoint() - getInPoint(), NvsVideoTrack.TRACK_ADD_VIDEO_FX_FLAGS_RENDER_AT_CLIP_RAW_FILTER, System.nanoTime() + "");
            LogUtils.e("Just create a virtual object!");
        }
        return clone;
    }


    @Override
    public LMeicamTrackVideoFx parseToLocalData() {
        LMeicamTrackVideoFx local = new LMeicamTrackVideoFx();
        setCommonData(local);
        local.setFlag(flag);
        return local;
    }

    @Override
    public void recoverFromLocalData(LMeicamTrackVideoFx lVideoFx) {
        setExtraTag(lVideoFx.getExtraTag());
        setIntensity(lVideoFx.getIntensity());
        setInPoint(lVideoFx.getInPoint());
        setOutPoint(lVideoFx.getOutPoint());
        setTimelineFxTagSet(lVideoFx.getTimelineFxTagSet());

        List<LMeicamFxParam<?>> meicamFxParam = lVideoFx.getMeicamFxParam();

        //遍历查询需要获取的参数，避免多次遍历 Traversal of the query parameters need to be obtained to avoid multiple traversal
        Map<String, LMeicamFxParam<?>> tempData = new HashMap<>();
        if (!CommonUtils.isEmpty(meicamFxParam)) {
            for (LMeicamFxParam<?> fxParam : meicamFxParam) {
                if (NvsConstants.KEY_BACKGROUND_COLOR.equals(fxParam.getKey())
                        || KEY_BACKGROUND_IMAGE_PATH.equals(fxParam.getKey())
                        || NvsConstants.AMPLITUDE.equals(fxParam.getKey())
                        || NvsConstants.KEY_BACKGROUND_BLUR_RADIUS.equals(fxParam.getKey())) {
                    tempData.put(fxParam.getKey(), fxParam);
                }
            }
        }

        if (!CommonUtils.isEmpty(meicamFxParam)) {
            for (LMeicamFxParam<?> fxParam : meicamFxParam) {
                if (TYPE_STRING.equals(fxParam.getType()) || TYPE_STRING_OLD.equals(fxParam.getType())
                        || TYPE_MENU.equals(fxParam.getType())) {
                    //背景参数 Param of background
                    if (NvsConstants.VALUE_COLOR_BACKGROUND_MODE.equals(fxParam.getValue())) {
                        setMenuVal(KEY_BACKGROUND_MODE, NvsConstants.VALUE_COLOR_BACKGROUND_MODE);
                        LMeicamFxParam<?> lMeicamFxParam = tempData.get(NvsConstants.KEY_BACKGROUND_COLOR);
                        if (lMeicamFxParam != null && lMeicamFxParam.getValue() instanceof String) {
                            setColor(NvsConstants.KEY_BACKGROUND_COLOR, (String) lMeicamFxParam.getValue());
                        }
                    } else if (NvsConstants.VALUE_IMAGE_BACKGROUND_MODE.equals(fxParam.getValue())) {
                        setMenuVal(KEY_BACKGROUND_MODE, NvsConstants.VALUE_IMAGE_BACKGROUND_MODE);
                        LMeicamFxParam<?> lMeicamFxParam = tempData.get(KEY_BACKGROUND_IMAGE_PATH);
                        if (lMeicamFxParam != null && lMeicamFxParam.getValue() instanceof String) {
                            setStringVal(KEY_BACKGROUND_IMAGE_PATH, (String) lMeicamFxParam.getValue());
                        }
                    } else if (NvsConstants.VALUE_BLUR_BACKGROUND_MODE.equals(fxParam.getValue())) {
                        setMenuVal(KEY_BACKGROUND_MODE, NvsConstants.VALUE_BLUR_BACKGROUND_MODE);
                        float radius = 0;
                        LMeicamFxParam<?> lMeicamFxParam = tempData.get(NvsConstants.KEY_BACKGROUND_BLUR_RADIUS);
                        if (lMeicamFxParam != null) {
                            Object valueRadius = lMeicamFxParam.getValue();
                            if (valueRadius != null) {
                                if (valueRadius instanceof Float) {
                                    radius = (float) valueRadius;
                                } else {
                                    radius = Float.parseFloat(valueRadius.toString());
                                }
                            }
                            setFloatVal(NvsConstants.KEY_BACKGROUND_BLUR_RADIUS, radius);
                        }
                    } else if (NvsConstants.MasterKeyer.KEY_COLOR.equals(fxParam.getKey())) {
                        setColor(fxParam.getKey(), (String) fxParam.getValue());
                    } else if (TYPE_MENU.equals(fxParam.getType())) {
                        setMenuVal(fxParam.getKey(), (String) fxParam.getValue());
                    } else {
                        //其他 Other
                        setStringVal(fxParam.getKey(), (String) fxParam.getValue());
                    }
                } else if (TYPE_BOOLEAN.equals(fxParam.getType())) {
                    setBooleanVal(fxParam.getKey(), (Boolean) fxParam.getValue());
                } else if (TYPE_INT.equals(fxParam.getType())) {
                    Object value = fxParam.getValue();
                    if (value instanceof Integer) {
                        int intValue = (int) value;
                        setIntVal(fxParam.getKey(), intValue);
                    } else if (value instanceof Double) {
                        double dValue = (double) value;
                        setIntVal(fxParam.getKey(), (int) dValue);
                    }

                } else if (TYPE_FLOAT.equals(fxParam.getType())) {
                    Object value = fxParam.getValue();
                    if (value instanceof Float) {
                        float floatValue = (float) value;
                        setFloatVal(fxParam.getKey(), floatValue);
                    } else if (value instanceof Double) {
                        double dValue = (double) value;
                        setFloatVal(fxParam.getKey(), (float) dValue);
                    }
                } else if (TYPE_POSITION_2D.equals(fxParam.getType())) {
                    Object value = fxParam.getValue();
                    if (value instanceof MeicamPosition2D) {
                        setPosition2DVal(fxParam.getKey(), (MeicamPosition2D) fxParam.getValue());
                    }
                } else if (TYPE_OBJECT.equals(fxParam.getType())) {
                    String key = fxParam.getKey();
                    if (NvsConstants.KEY_MASK_REGION_INFO.equals(key) || KEY_PROPERTY_MASK_REGION_INFO.equals(key)) {
                        convertObject(key, fxParam.getValue());
                    } else if (NvsConstants.MasterKeyer.KEYER_MODE.equals(key)) {
                        setMenuVal(key, String.valueOf(fxParam.getValue()));
                    } else if (NvsConstants.MasterKeyer.KEY_COLOR_POSITION.equals(key)) {
                        Object value = fxParam.getValue();
                        if (value != null) {
                            String json = GsonUtils.toJson(fxParam.getValue());
                            value = GsonUtils.fromJson(json, FloatPoint.class);
                            setObjectVal(key, value);
                        }
                    }
                } else if (TYPE_COLOR.equals(fxParam.getType())) {
                    setColor(fxParam.getKey(), (String) fxParam.getValue());
                }

                //正则表达式 Regular expression value
                if (NvsConstants.AMPLITUDE.equals(fxParam.getKey())) {
                    LMeicamFxParam<?> lMeicamFxParam = tempData.get(NvsConstants.AMPLITUDE);
                    if (lMeicamFxParam != null && lMeicamFxParam.getValue() instanceof String) {
                        try {
                            setExprVar(NvsConstants.AMPLITUDE, Double.parseDouble((String) lMeicamFxParam.getValue()));
                        } catch (Exception ignore) {
                        }
                    }
                }
            }
        }
        LMeicamMaskRegionInfo localRegionInfo = lVideoFx.getRegionInfo();
        if (localRegionInfo != null) {
            MeicamMaskRegionInfo regionInfo = new MeicamMaskRegionInfo();
            regionInfo.recoverFromLocalData(localRegionInfo);
            setRegionInfo(regionInfo);
        }
    }

    @Override
    public void recoverFromTimelineData(com.meicam.sdk.NvsObject nvsObject) {
        if (nvsObject instanceof NvsTrackVideoFx) {
            setObject((NvsTrackVideoFx) nvsObject);
            loadData();
        }
    }

    /**
     * 转化成需要的对象类
     * Convert object to set object value
     *
     * @param key   the key 键值
     * @param value the value 值
     */
    private void convertObject(String key, Object value) {
        if (KEY_CROPPER_REGION_INFO.equals(key) || KEY_PROPERTY_MASK_REGION_INFO.equals(key)) {
            try {
                String json = GsonUtils.toJson(value);
                value = GsonUtils.fromJson(json, MeicamMaskRegionInfo.class);
            } catch (Exception e) {
                LogUtils.e("error:" + e.getMessage());
            }
        }
        setObjectVal(key, value);
    }

    protected void setCommonData(LMeicamTrackVideoFx local) {
        local.setExtraTag(getExtraTag());
        local.setType(getType());
        local.setSubType(getSubType());
        local.setDesc(getDesc());
        local.setIntensity(getIntensity());
        local.setInPoint(getInPoint());
        local.setOutPoint(getOutPoint());
        local.setTimelineFxTagSet(getTimelineFxTagSet());

        Set<String> keySet = fxParams.keySet();
        for (String key : keySet) {
            MeicamFxParam<?> meicamFxParam = fxParams.get(key);
            if (meicamFxParam != null) {
                local.getMeicamFxParam().add(meicamFxParam.parseToLocalData());
            }
        }
        local.setAttachment(attachment);
        MeicamMaskRegionInfo regionInfo = getRegionInfo();
        if (regionInfo != null) {
            local.setRegionInfo(regionInfo.parseToLocalData());
        }
    }

    protected void setCommonRecoverData(LMeicamVideoFx local) {
        String type = local.getType();
        if ("0".equals(type)) {
            type = TYPE_BUILD_IN;
        } else if ("1".equals(type)) {
            type = CommonData.TYPE_PACKAGE;
        }
        setType(type);
        setSubType(local.getSubType());
        setDesc(local.getDesc());
        setIntensity(local.getIntensity());
        setValue(local);
    }

    private void setValue(LMeicamVideoFx local) {
        List<LMeicamFxParam<?>> meicamFxParam = local.getMeicamFxParam();
        if (!CommonUtils.isEmpty(meicamFxParam)) {
            for (LMeicamFxParam<?> fxParam : meicamFxParam) {
                if (TYPE_STRING.equals(fxParam.getType())) {
                    setStringVal(fxParam.getKey(), (String) fxParam.getValue());
                } else if (TYPE_BOOLEAN.equals(fxParam.getType())) {
                    setBooleanVal(fxParam.getKey(), (Boolean) fxParam.getValue());
                } else if (TYPE_FLOAT.equals(fxParam.getType())) {
                    Object value = fxParam.getValue();
                    if (value instanceof Float) {
                        float floatValue = (float) value;
                        setFloatVal(fxParam.getKey(), floatValue);
                    } else if (value instanceof Double) {
                        setFloatVal(fxParam.getKey(), (float) value);
                    }
                } else if (TYPE_POSITION_2D.equals(fxParam.getType())) {
                    Object value = fxParam.getValue();
                    if (value instanceof MeicamPosition2D) {
                        setPosition2DVal(fxParam.getKey(), (MeicamPosition2D) value);
                    }
                } else if (TYPE_OBJECT.equals(fxParam.getType())) {
                    setObjectVal(fxParam.getKey(), fxParam.getValue());
                } else if (TYPE_MENU.equals(fxParam.getType())) {
                    setMenuVal(fxParam.getKey(), (String) fxParam.getValue());
                } else if (TYPE_INT.equals(fxParam.getType())) {
                    Object value = fxParam.getValue();
                    if (value instanceof Integer) {
                        int intValue = (int) value;
                        setIntVal(fxParam.getKey(), intValue);
                    } else if (value instanceof Double) {
                        double dValue = (double) value;
                        setIntVal(fxParam.getKey(), (int) dValue);
                    }
                } else if (TYPE_COLOR.equals(fxParam.getType())) {
                    setColor(fxParam.getKey(), (String) fxParam.getValue());
                }
            }
        }
    }

    public int getFlag() {
        return flag;
    }
}
