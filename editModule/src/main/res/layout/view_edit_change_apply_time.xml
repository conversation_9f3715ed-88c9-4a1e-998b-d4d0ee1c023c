<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:parentTag="android.widget.RelativeLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/black"
    tools:ignore="RtlCompat">

    <TextView
        android:id="@+id/tv_start_text"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:gravity="center"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_30" />

    <SeekBar
        android:id="@+id/seek_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="@dimen/dp_px_50"
        android:layout_marginRight="@dimen/dp_px_50"
        android:maxHeight="@dimen/dp_px_6"
        android:paddingTop="@dimen/dp_px_9"
        android:paddingBottom="@dimen/dp_px_9"
        android:progressDrawable="@drawable/edit_seek_bar"
        android:thumb="@drawable/edit_seek_bar_ball" />

    <TextView
        android:id="@+id/tv_current_text"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:gravity="center"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_30" />

</merge>