package com.meishe.myvideo.activity;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.meicam.sdk.NvsTimeline;
import com.meishe.base.adapter.CommonFragmentAdapter;
import com.meishe.base.model.BaseMvpActivity;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.ImageLoader;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.activity.iview.CustomStickerView;
import com.meishe.myvideo.activity.presenter.CustomStickerPresenter;
import com.meishe.myvideo.fragment.SingleClipFragment;
import com.meishe.myvideo.fragment.StickerCustomEffectFragment;
import com.meishe.base.view.CustomTitleBar;
import com.meishe.third.tablayout.SlidingTabLayout;

import java.util.ArrayList;
import java.util.List;

import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import static com.meishe.logic.constant.PagerConstants.FILE_PATH;

/**
 * 自定义动画贴纸特效制作页面
 * Custom animation stickers special effects production page
 */
public class CustomStickerEffectActivity extends BaseMvpActivity<CustomStickerPresenter> implements CustomStickerView {
    private static final long seekTimelineTamp = 1000000;
    private CustomTitleBar mTitleBar;
    private FrameLayout mFlMiddleContainer;
    private ImageView mIvConfirm;
    private ImageView mIvStickerPicture;
    private SingleClipFragment mClipFragment;
    private String mPicturePath;

    private SlidingTabLayout mTabLayout;
    private ViewPager mViewPager;
    private boolean mUseCustomSticker;

    @Override
    protected int bindLayout() {
        return R.layout.activity_custom_animate_sticker_effect;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        Intent intent = getIntent();
        if (intent != null) {
            mPicturePath = intent.getStringExtra(FILE_PATH);
        }
        mPresenter.createTimeline();
    }

    @Override
    protected void initView() {
        mTitleBar = findViewById(R.id.title_bar);
        mFlMiddleContainer = findViewById(R.id.fl_fragment_container);
        mIvStickerPicture = findViewById(R.id.iv_sticker);
        mIvConfirm = findViewById(R.id.iv_confirm);
        mTabLayout = findViewById(R.id.tab_layout);
        mViewPager = findViewById(R.id.view_pager);
        mTitleBar.setTextCenter(R.string.customStickerselecteffect);
        if (!TextUtils.isEmpty(mPicturePath)) {
            mPresenter.setStickerImageSize(getResources().getDimensionPixelOffset(R.dimen.dp_px_192),
                    getResources().getDimensionPixelOffset(R.dimen.dp_px_782), mPicturePath, mIvStickerPicture);
            ImageLoader.loadUrl(this, mPicturePath, mIvStickerPicture);
        }
        initFragment();
        initListener();
    }

    private void initFragment() {
        List<Fragment> fragmentList = new ArrayList<>(2);
        fragmentList.add(StickerCustomEffectFragment.create().setOnEventListener(stickerId -> {
            if (mIvStickerPicture.getVisibility() != View.GONE) {
                mIvStickerPicture.setVisibility(View.GONE);
            }
            if (mFlMiddleContainer.getVisibility() != View.VISIBLE) {
                mFlMiddleContainer.setVisibility(View.VISIBLE);
            }
            mPresenter.previewSticker(stickerId, mPicturePath);
            mClipFragment.playVideo(0, mPresenter.getTimeline().getDuration());
        }));
        mViewPager.setAdapter(new CommonFragmentAdapter(getSupportFragmentManager(), fragmentList));
        List<String> titleList = new ArrayList<>(2);
        titleList.add(getResources().getString(R.string.fragment_menu_table_sticker_effect));
        mTabLayout.setViewPager(mViewPager, titleList);

        mClipFragment = new SingleClipFragment();
        mClipFragment.setTimeline(mPresenter.getTimeline());
        getSupportFragmentManager().beginTransaction()
                .add(R.id.fl_fragment_container, mClipFragment)
                .show(mClipFragment)
                .commitAllowingStateLoss();
        delayToDealOnUiThread(() -> {
            mClipFragment.setLiveWindowRatio(mTitleBar.getLayoutParams().height, getResources().getDimensionPixelOffset(R.dimen.dp_px_782));
            mClipFragment.seekTimeline(seekTimelineTamp, 0);
        });
    }

    private void initListener() {
        mIvConfirm.setOnClickListener(v -> {
            mIvConfirm.setClickable(false);
            mUseCustomSticker = mPresenter.applySticker(mPicturePath);
            finish();
        });
        mClipFragment.setPlayEventListener(new SingleClipFragment.PlayEventListener() {
            @Override
            protected void playBackEOF(NvsTimeline timeline) {
                mClipFragment.seekTimeline(seekTimelineTamp, 0);
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        mIvConfirm.setClickable(true);
    }


    @Override
    public void onStickerBack(List<IBaseInfo> stickerList) {

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (!mUseCustomSticker) {
            FileUtils.delete(mPicturePath);
        }
    }
}
