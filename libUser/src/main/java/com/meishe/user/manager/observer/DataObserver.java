package com.meishe.user.manager.observer;

import com.meishe.draft.data.DraftData;
import com.meishe.user.bean.CloudDraftData;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/11 18:40
 * @Description :上传监听对象， The observer for uploading.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class DataObserver {

    public void onUploadDataChanged(List<DraftData> data){

    }

    public void onDownloadDataChanged(List<CloudDraftData> data){

    }

    public void onUploadProgressChanged(String tag, int progress){

    }
}
