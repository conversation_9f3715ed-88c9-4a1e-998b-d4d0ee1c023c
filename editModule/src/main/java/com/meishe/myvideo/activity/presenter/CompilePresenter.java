package com.meishe.myvideo.activity.presenter;

import android.graphics.Bitmap;
import android.net.Uri;
import android.text.TextUtils;

import com.meicam.sdk.NvsRational;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsVideoResolution;
import com.meishe.base.model.Presenter;
import com.meishe.base.utils.AndroidVersionUtils;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.MediaUtils;
import com.meishe.base.utils.StringUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;
import com.meishe.base.view.bean.CompileParamData;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.EngineCallbackManager;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.observer.EngineCallbackObserver;
import com.meishe.engine.util.PathUtils;
import com.meishe.logic.utils.UMengUtils;
import com.meishe.myvideo.R;
import com.meishe.myvideo.activity.iview.CompileView;

import java.util.ArrayList;
import java.util.Hashtable;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2020/12/17 15:21
 * @Description :导出视频逻辑处理类 Compile Presenter
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CompilePresenter extends Presenter<CompileView> {
    private static final String TAG = "CompilePresenter";
    private NvsStreamingContext mStreamingContext;
    private MeicamTimeline mTimeline;
    private String mVideoSavePath;
    private Hashtable<String, Object> mParamsTable;
    /**
     * The resolution
     * 分辨率
     */
    private int mCompileResolutionNum = 720;
    /**
     * The resolution coefficient of 720p is the benchmark of 1.0
     * 分辨率系数 720p为基准1.0
     */
    private float mBaseResolution = 1.0f;
    /**
     * The frame rate coefficient corresponds to 30 frames as a 1.0 reference
     * 帧率系数对应 30帧作为1.0基准
     * <p>
     * 实际帧率并不会影响到视频文件的大小
     */
    private float mBaseFrameRate = 1.0f;

    private boolean isCompile = false;
    private EngineCallbackObserver mCallbackObserver;

    @Override
    public void onResume() {
        super.onResume();
        if (isCompile) {
            mStreamingContext.resumeCompiling();
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        //重写onStop 避免 compile流程异常
        //Rewrite onStop to avoid compile process exceptions
        if (isCompile) {
            mStreamingContext.pauseCompiling();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EngineCallbackManager.get().unregisterCallbackObserver(mCallbackObserver);
        if (isCompile) {
            stopCompileVideo();
        }
    }

    /**
     * 初始化时间线
     * init Timeline
     */
    public void initTimeline() {
        mStreamingContext = EditorEngine.getInstance().getStreamingContext();
        mTimeline = EditorEngine.getInstance().getCurrentTimeline();
        mParamsTable = new Hashtable<>(2);
        EngineCallbackManager.get().registerCallbackObserver(mCallbackObserver = new EngineCallbackObserver() {
            @Override
            public boolean isActive() {
                return getView().isActive();
            }

            @Override
            public void onCompileProgress(NvsTimeline nvsTimeline, int i) {
                if (getView() != null) {
                    getView().onCompileProgress(i);
                }
            }

            @Override
            public void onCompileFinished(NvsTimeline nvsTimeline) {
            }

            @Override
            public void onCompileFailed(NvsTimeline nvsTimeline) {
                ToastUtils.showShort(R.string.compile_out_failed);
                deleteCompileFailedData();
            }

            @Override
            public void onCompileCompleted(NvsTimeline nvsTimeline, boolean b) {
                if (!b) {
                    //jumpToCompile();
                    mStreamingContext.setCompileConfigurations(null);
                    PathUtils.scanInnerFile(mVideoSavePath, nvsTimeline.getDuration(), new PathUtils.OnScanCallBack() {
                        @Override
                        public void onSuccess(String filePath) {
                            if (getView() != null) {
                                getView().onCompileEnd(true);
                            }
                        }

                        @Override
                        public void onFail() {
                            if (getView() != null) {
                                getView().onCompileEnd(false);
                            }
                        }
                    });
                    //路径置空，防止，在点击返回键等操作的时候删除文件
                    //The path is empty to prevent deleting files when clicking the return button and other operations.
                    mVideoSavePath = null;
                } else {
                    deleteCompileFailedData();
                    ToastUtils.showShort(R.string.compile_cancel);
                }
                if (getView() != null) {
                    getView().onCompileEnd(!b);
                }
                isCompile = false;
            }

        });
    }


    /**
     * 从时间线中获取图片
     * grab image from timeline
     */
    public Bitmap grabImageFromTimeline() {
        return EditorEngine.getInstance().grabImageFromTimeline(mTimeline, 0, new NvsRational(1, 3));
    }

    /**
     * 计算文件大小
     * calculate file size
     * <p>
     * 计算规则
     * 视频码率 / 1024 * 时长（秒） / 8
     *
     * @param compileParam 导出参数 The compile params
     * @param isResolution true is resolution ,false is frame rate
     */
    public String calculateFileSize(CompileParamData compileParam, boolean isResolution) {
        if (isResolution) {
            mCompileResolutionNum = getResolution(compileParam);
        } else {
            int frameRate = 30;
            if (compileParam != null) {
                frameRate = Integer.parseInt(compileParam.getShowData());
            }
            mParamsTable.put(NvsStreamingContext.COMPILE_FPS, new NvsRational(frameRate, 1));
            //计算选择的帧率对应基准的值
            // Calculate the value of the selected frame rate corresponding to the benchmark
            mBaseFrameRate = getBaseVideoFrameRate(frameRate);
        }
        //mBaseResolution = 1.0f;
        float second = mTimeline.getDuration() * 1.0f / 1000000;
        /*
         * Video size KB per second based on standard resolution frame rate
         * 基于标准的分辨率 帧率下每秒视频的size kb
         * 4.0*1024k bps是当前编译等级COMPILE_BITRATE_GRADE_HIGH 对应的码率 其他等级码率参考官网
         */
        float perSecondSize = 4.0f * 1024 / 8;
        int size = (int) (second * perSecondSize * mBaseFrameRate * mBaseResolution);
        int mb = (size / 1024);
        String fileSize = StringUtils.getString(R.string.file_size);
        if (mb > 0) {
            fileSize = fileSize + " " + mb + " M";
        } else {
            fileSize = fileSize + " " + size + " Kb";
        }
        return fileSize;
    }

    /**
     * 通过输入选中的帧率 计算相对默认帧率的比例
     * calculate baseFrameRate by selected frameRate
     *
     * @param frameRate selectedFrameRate
     * @return the video frame rate
     */
    private float getBaseVideoFrameRate(int frameRate) {
        if (frameRate <= 30) {
            return frameRate / 30.0f;
        } else {
            if (frameRate == 50) {
                return 1.1f;
            } else if (frameRate == 60) {
                return 1.2f;
            }
        }
        return 1.0f;
    }

    /**
     * 获取导出文件参数列表
     * Gets the compile params
     *
     * @param originalArray String[] The original data
     * @param recommend     true is recommend ,false not
     */
    public List<CompileParamData> getCompileParams(String[] originalArray, String recommend) {
        if (originalArray == null) {
            return null;
        }
        List<CompileParamData> resolutionList = new ArrayList<>(originalArray.length);
        if (originalArray.length > 0) {
            for (String s : originalArray) {
                CompileParamData compileParamData = new CompileParamData();
                compileParamData.setShowData(s);
                compileParamData.setRecommend(s.equals(recommend));
                resolutionList.add(compileParamData);
            }
        }
        return resolutionList;
    }

    /**
     * 导出视频
     * compile Video
     */
    public void compileVideo() {
        //友盟统计 The UMeng Util
        UMengUtils.onEvent(Utils.getApp().getApplicationContext());
        if (mTimeline == null) {
            return;
        }
        mVideoSavePath = PathUtils.getVideoSavePathNew_Q(PathUtils.getVideoSaveName(), mTimeline.getDuration());
        if (TextUtils.isEmpty(mVideoSavePath)) {
            LogUtils.e("Video save path is null!");
            return;
        }
        NvsVideoResolution videoRes = mTimeline.getVideoResolution();
        int imageWidth = videoRes.imageWidth;
        int imageHeight = videoRes.imageHeight;
        int customHeight = EditorEngine.getInstance().getCustomHeight(mCompileResolutionNum, mTimeline.getMakeRatio());
        LogUtils.d("timeline Width=" + imageWidth + ", height = " + imageHeight + ", customHeight = " + customHeight);
        boolean isCompiling = EditorEngine.getInstance().compileTimeline(mTimeline, 0, mTimeline.getDuration(),
                mVideoSavePath, NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_CUSTOM, customHeight,
                NvsStreamingContext.COMPILE_BITRATE_GRADE_HIGH,
                NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_BUDDY_HOST_VIDEO_FRAME
                        | NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_DISABLE_ALIGN_VIDEO_SIZE
                        | NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_IGNORE_TIMELINE_VIDEO_SIZE, mParamsTable);
        if (isCompiling) {
            if (getView() != null) {
                getView().onCompileStart();
            }
            isCompile = true;
        }
    }

    public void putParam(String key, Object value){
        if (mParamsTable == null) {
            mParamsTable = new Hashtable<>(2);
        }
        mParamsTable.put(key, value);
    }

    private int getResolution(CompileParamData compileParam) {
        if (null == compileParam) {
            return 720;
        }
        if (compileParam.getShowData().equals(StringUtils.getString(R.string.int360))) {
            return 360;
        } else if (compileParam.getShowData().equals(StringUtils.getString(R.string.int480))) {
            return 480;
        } else if (compileParam.getShowData().equals(StringUtils.getString(R.string.int720))) {
            return 720;
        } else if (compileParam.getShowData().equals(StringUtils.getString(R.string.int1080))) {
            return 1080;
        } else if (compileParam.getShowData().equals(StringUtils.getString(R.string.int4K))) {
            return 2160;
        } else if (compileParam.getShowData().equals(StringUtils.getString(R.string.int576))) {
            return 576;
        } else {
            return 720;
        }
    }


    /**
     * 停止视频导出，并关闭
     * Stop the video export and close
     */
    public void stopCompileVideo() {
        int state = mStreamingContext.getStreamingEngineState();
        if (state == NvsStreamingContext.STREAMING_ENGINE_STATE_COMPILE) {
            FileUtils.delete(mVideoSavePath);
            mStreamingContext.stop();
            mStreamingContext.setCompileConfigurations(null);
        }
    }

    /**
     * 获取导出视频的分辨率
     *
     * @param compileParam 导出参数 The compile params
     */
    private int getVideoResolution(CompileParamData compileParam) {
        if (null == compileParam) {
            mBaseResolution = 1.0f;
            return NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_720;
        }
        if (compileParam.getShowData().equals(StringUtils.getString(R.string.int360))) {
            mBaseResolution = 0.65f;
            return NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_360;
        } else if (compileParam.getShowData().equals(StringUtils.getString(R.string.int480))) {
            mBaseResolution = 0.8f;
            return NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_480;
        } else if (compileParam.getShowData().equals(StringUtils.getString(R.string.int720))) {
            mBaseResolution = 1.0f;
            return NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_720;
        } else if (compileParam.getShowData().equals(StringUtils.getString(R.string.int1080))) {
            mBaseResolution = 1.2f;
            return NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_1080;
        } else if (compileParam.getShowData().equals(StringUtils.getString(R.string.int4K))) {
            mBaseResolution = 1.3f;
            return NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_2160;
        } else if (compileParam.getShowData().equals(StringUtils.getString(R.string.int576))) {
            mBaseResolution = 1.3f;
            return NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_CUSTOM;
        } else {
            mBaseResolution = 1.0f;
            return NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_720;
        }
    }

    /**
     * 删除合成失败的数据
     * Delete the compile failed data
     */
    private void deleteCompileFailedData() {
        if (AndroidVersionUtils.isAboveAndroid_Q()) {
            if (!TextUtils.isEmpty(mVideoSavePath)) {
                MediaUtils.deleteVideoRecord_Q(Utils.getApp().getApplicationContext(), Uri.parse(mVideoSavePath));
            }
        } else {
            FileUtils.delete(mVideoSavePath);
        }
    }
}
