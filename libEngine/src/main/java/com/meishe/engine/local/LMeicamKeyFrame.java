package com.meishe.engine.local;

import java.io.Serializable;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/6/25 16:02
 * @Description :关键帧本地信息 The key frame local info
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class LMeicamKeyFrame implements Cloneable, Serializable {
    /**
     * 关键帧所在的时间点
     * The point in time at which the keyframe is located
     */
    private long atTime;

    /**
     * 关键帧所在的时间点的偏移量
     * The offset point in time at which the keyframe is located
     */
    private long offsetTime;

    /**
     * 关键帧所对应的参数列表
     * The list of parameters corresponding to the keyframe
     */
    private List<LMeicamFxParam<?>> paramList;

    /**
     * 控制点
     * Control points
     */
    private LMeicamKeyframeControlPoints controlPoints;
    private LMeicamKeyFrame next;
    private LMeicamKeyFrame font;
    private String extraTag;


    public long getAtTime() {
        return atTime;
    }

    public void setAtTime(long atTime) {
        this.atTime = atTime;
    }

    public long getOffsetTime() {
        return offsetTime;
    }

    public void setOffsetTime(long offsetTime) {
        this.offsetTime = offsetTime;
    }

    public List<LMeicamFxParam<?>> getParamList() {
        return paramList;
    }

    public void setParamList(List<LMeicamFxParam<?>> paramList) {
        this.paramList = paramList;
    }

    public LMeicamKeyframeControlPoints getControlPoints() {
        return controlPoints;
    }

    public void setControlPoints(LMeicamKeyframeControlPoints controlPoints) {
        this.controlPoints = controlPoints;
    }

    public LMeicamKeyFrame getNext() {
        return next;
    }

    public void setNext(LMeicamKeyFrame next) {
        this.next = next;
    }

    public LMeicamKeyFrame getFont() {
        return font;
    }

    public void setFont(LMeicamKeyFrame font) {
        this.font = font;
    }

    public String getExtraTag() {
        return extraTag;
    }

    public void setExtraTag(String extraTag) {
        this.extraTag = extraTag;
    }
}
