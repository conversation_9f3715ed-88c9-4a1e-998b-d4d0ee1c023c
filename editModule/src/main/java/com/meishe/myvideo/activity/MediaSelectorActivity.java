package com.meishe.myvideo.activity;

import static com.meishe.logic.constant.PagerConstants.TYPE_DEFAULT;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.meishe.base.bean.MediaData;
import com.meishe.base.model.BaseActivity;
import com.meishe.myvideo.R;
import com.meishe.myvideo.fragment.MaterialSelectFragment;
import com.meishe.myvideo.fragment.MediaSelectContainerFragment;

import java.util.ArrayList;

/**
 * 媒体选择Activity
 * 顶部有相机和手机两个切换按钮，分别对应不同的Fragment
 */
public class MediaSelectorActivity extends BaseActivity {

    private ImageView mIvClose;
    private ImageView mIvCamera;
    private ImageView mIvPhone;

    private MediaSelectContainerFragment mCameraFragment;
    private MaterialSelectFragment mPhoneFragment;

    private boolean mIsCameraSelected = true; // 默认选中相机

    private static final int TYPE_CAMERA = 1;
    private static final int TYPE_PHONE = 2;

    @Override
    protected int bindLayout() {
        return R.layout.activity_media_selector;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        // 初始化Fragment
        mCameraFragment = MediaSelectContainerFragment.newInstance(TYPE_DEFAULT);
        mPhoneFragment = MaterialSelectFragment.create(
            MediaData.TYPE_BUSINESS,
            TYPE_DEFAULT,
            true,
            mMediaChangeListener
        );
    }

    @Override
    protected void initView() {
        mIvClose = findViewById(R.id.iv_close);
        mIvCamera = findViewById(R.id.iv_camera);
        mIvPhone = findViewById(R.id.iv_phone);

        initListener();

        // 默认显示相机Fragment
        showFragment(TYPE_CAMERA);
        updateButtonState();
    }

    private void initListener() {
        mIvClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        mIvCamera.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!mIsCameraSelected) {
                    mIsCameraSelected = true;
                    showFragment(TYPE_CAMERA);
                    updateButtonState();
                }
            }
        });

        mIvPhone.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mIsCameraSelected) {
                    mIsCameraSelected = false;
                    showFragment(TYPE_PHONE);
                    updateButtonState();
                }
            }
        });
    }

    /**
     * 显示对应的Fragment
     */
    private void showFragment(int type) {
        FragmentManager fragmentManager = getSupportFragmentManager();
        FragmentTransaction transaction = fragmentManager.beginTransaction();

        // 隐藏所有Fragment
        if (mCameraFragment != null && mCameraFragment.isAdded()) {
            transaction.hide(mCameraFragment);
        }
        if (mPhoneFragment != null && mPhoneFragment.isAdded()) {
            transaction.hide(mPhoneFragment);
        }

        // 显示对应的Fragment
        if (type == TYPE_CAMERA) {
            if (mCameraFragment.isAdded()) {
                transaction.show(mCameraFragment);
            } else {
                transaction.add(R.id.fl_fragment_container, mCameraFragment);
            }
        } else if (type == TYPE_PHONE) {
            if (mPhoneFragment.isAdded()) {
                transaction.show(mPhoneFragment);
            } else {
                transaction.add(R.id.fl_fragment_container, mPhoneFragment);
            }
        }

        transaction.commitAllowingStateLoss();
    }

    /**
     * 更新按钮选中状态
     */
    private void updateButtonState() {
        mIvCamera.setSelected(mIsCameraSelected);
        mIvPhone.setSelected(!mIsCameraSelected);
    }

    /**
     * 媒体变化监听器
     */
    private final MaterialSelectFragment.MediaChangeListener mMediaChangeListener =
        new MaterialSelectFragment.MediaChangeListener() {
            @Override
            public void onMediaChange(MediaData mediaData) {
                // 处理媒体选择变化
            }

            @Override
            public void onMediaPreView(MediaData mediaData) {
                // 处理媒体预览
            }
        };
}
