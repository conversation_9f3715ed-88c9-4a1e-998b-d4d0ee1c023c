package com.meishe.myvideo.view;

import android.content.Context;
import android.util.AttributeSet;

import com.meishe.base.utils.LogUtils;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.myvideo.R;
import com.meishe.myvideo.ui.bean.BaseUIClip;
import com.meishe.myvideo.ui.bean.BaseUIVideoClip;
import com.meishe.myvideo.ui.trackview.BaseItemView;
import com.meishe.myvideo.ui.trackview.TrackViewLayout;
import com.meishe.myvideo.util.PixelPerMicrosecondUtil;
import com.meishe.myvideo.util.TrackViewDataHelper;

import java.util.HashMap;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/6/18 11:24
 * 编辑器时间轴跟踪视图类
 * The editor timeline tracks the view class
 */
public class MYEditorTimelineTrackView extends TrackViewLayout {
    private MeicamTimeline mTimeline;

    public MYEditorTimelineTrackView(Context context) {
        super(context);
    }

    public MYEditorTimelineTrackView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public MYEditorTimelineTrackView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    /**
     * Sets timeline.
     * 设置时间线
     *
     * @param mTimeline the m timeline
     */
    public void setTimeline(MeicamTimeline mTimeline) {
        this.mTimeline = mTimeline;
        setAudioView();
        initWidth(mTimeline.getDuration());
    }


    /**
     * 绘制音频波形图
     * Draw audio waveforms
     *
     * @param integerListHashMap the integer list hash map 整数列表
     * @param timelineDuration   the timeline duration 持续时间
     */
    public void setTrackViewLayoutData(HashMap<Integer, List<BaseUIClip>> integerListHashMap, long timelineDuration, String trackType) {
        setData(integerListHashMap, timelineDuration, trackType);
    }

    /**
     * Show pip track view.
     * 显示轨道视图
     *
     * @param timeline the timeline 时间线
     */
    public void showPipTrackView(MeicamTimeline timeline) {
        if (timeline == null) {
            return;
        }
        HashMap<Integer, List<BaseUIClip>> integerListHashMap = TrackViewDataHelper.getInstance().getTrackData(CommonData.CLIP_VIDEO);
        setData(integerListHashMap, timeline.getDuration(), CommonData.CLIP_VIDEO);
    }

    /**
     * 更改所给片段的音量icon
     * Update track clip volume
     */
    public void changeVolumeState() {
        BaseItemView dragView = getDragView();
        if (dragView != null) {
            dragView.updateVolumeState();
        }
    }

    /**
     * Sets select.
     * 设置选择
     *
     * @param trackIndex the track index 轨道索引
     * @param inPoint    the in point 点
     */
    public void setSelect(int trackIndex, long inPoint,boolean isCallBack) {
        BaseUIVideoClip baseUIVideoClip = new BaseUIVideoClip(trackIndex);
        baseUIVideoClip.setInPoint(inPoint);
        setSelect(baseUIVideoClip, isCallBack);
    }

    /**
     * Sets select.
     * 设置选择
     *
     * @param trackIndex the track index 轨道索引
     * @param inPoint    the in point 点
     */
    public void setSelect(int trackIndex, long inPoint) {
        BaseUIVideoClip baseUIVideoClip = new BaseUIVideoClip(trackIndex);
        baseUIVideoClip.setInPoint(inPoint);
        setSelect(baseUIVideoClip, true);
    }

    /**
     * Sets select.
     * 设置选择
     *
     * @param baseUIClip the base ui clip 基础ui裁剪
     */
    public void setSelect(BaseUIClip baseUIClip) {
        setTimelineDuration(mTimeline.getDuration());
        setSelectDragView(baseUIClip, true);
        audioScroll(baseUIClip);
    }

    /**
     * Sets select.
     * 设置选择
     *
     * @param baseUIClip the base ui clip 基础ui裁剪
     */
    public void setSelect(BaseUIClip baseUIClip, boolean isCallBack) {
        setTimelineDuration(mTimeline.getDuration());
        setSelectDragView(baseUIClip, isCallBack);
        audioScroll(baseUIClip);
    }

    /**
     * Audio scroll.
     * 音频滚动
     *
     * @param baseUIClip the base ui clip
     */
    public void audioScroll(BaseUIClip baseUIClip) {
        if (baseUIClip == null) {
            LogUtils.e("audioScroll baseUIClip==null");
            return;
        }
        if (CommonData.CLIP_AUDIO.equals(baseUIClip.getType())) {
            int trackIndex = baseUIClip.getTrackIndex();
            if (trackIndex == 0) {
                return;
            }
            int trackHeight = (baseUIClip.getTrackIndex() - 1) * (getResources().getDimensionPixelOffset(R.dimen.track_view_real_height)
                    + getResources().getDimensionPixelOffset(R.dimen.track_view_real_margin_top));
            scrollAnimation(trackHeight);
        }
    }

    /**
     * Audio scroll x.
     * 音频滚动x
     *
     * @param baseUIClip the base ui clip
     */
    public void audioScrollX(BaseUIClip baseUIClip) {
        long currPosition = 0;
        if (mTimeline != null) {
            currPosition = mTimeline.getCurrentPosition();
        }
        long outPoint = baseUIClip.getInPoint() + baseUIClip.getTrimOut() - baseUIClip.getTrimIn();
        if (currPosition < baseUIClip.getInPoint()) {
            //滑动到选中的inPoint处 Slide to the selected InPoint
            smoothScrollView(PixelPerMicrosecondUtil.durationToLength(baseUIClip.getInPoint()));
        } else if (currPosition > outPoint) {
            //滑动到选中的outPoint处 Slide to the selected OutPoint
            smoothScrollView(PixelPerMicrosecondUtil.durationToLength(outPoint));
        }
    }
}
