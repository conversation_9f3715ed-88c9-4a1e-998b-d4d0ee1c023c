package com.czc.cutsame;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.EditText;
import android.widget.ImageView;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.czc.cutsame.fragment.TemplateListFragment;
import com.meishe.base.model.BaseActivity;
import com.meishe.base.utils.KeyboardUtils;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/4/13 18:25
 * @Description :模板列表页面 The template list Activity
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class TemplateListActivity extends BaseActivity {
    private ViewTreeObserver.OnGlobalLayoutListener mOnGlobalLayoutListener = null;
    private EditText mEditText;
    private TemplateListFragment mTemplateListFragment;
    /**
     * 键盘是否曾经显示过
     * Has the keyboard ever displayed
     */
    private boolean hasKeyboardDisplayed;

    @Override
    protected int bindLayout() {
        return R.layout.activity_template_list;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {

    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        hasKeyboardDisplayed = false;
        KeyboardUtils.registerSoftInputChangedListener(this, new KeyboardUtils.OnSoftInputChangedListener() {
            @Override
            public void onSoftInputChanged(int height) {
                if (height == 0) {
                    if (hasKeyboardDisplayed) {
                        if ( mEditText != null || mTemplateListFragment != null) {
                            mTemplateListFragment.setKeyword(mEditText.getText().toString());
                        }
                    }
                } else {
                    hasKeyboardDisplayed = true;
                }
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        KeyboardUtils.unregisterSoftInputChangedListener(this.getWindow());
    }

    @Override
    protected void initView() {
        FragmentManager fragmentManager = getSupportFragmentManager();
        FragmentTransaction transaction = fragmentManager.beginTransaction();
        mTemplateListFragment = TemplateListFragment.create();
        transaction.add(R.id.fl_content, mTemplateListFragment).commitAllowingStateLoss();
        ImageView back = findViewById(R.id.iv_back);
        back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });
        mEditText = findViewById(R.id.tv_search_hint);
        mEditText.setOnKeyListener((view, keyCode, keyEvent) -> {
            if (keyCode == KeyEvent.KEYCODE_ENTER && keyEvent.getAction() != KeyEvent.ACTION_UP) {
                KeyboardUtils.hideSoftInput(TemplateListActivity.this);
                return true;
            }
            return false;
        });
        mEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });
    }
}
