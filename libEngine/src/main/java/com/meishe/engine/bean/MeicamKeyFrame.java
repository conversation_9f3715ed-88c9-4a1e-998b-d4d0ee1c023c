package com.meishe.engine.bean;


import android.graphics.PointF;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsCaption;
import com.meicam.sdk.NvsColor;
import com.meicam.sdk.NvsCompoundCaption;
import com.meicam.sdk.NvsControlPointModePair;
import com.meicam.sdk.NvsControlPointPair;
import com.meicam.sdk.NvsFx;
import com.meicam.sdk.NvsMaskRegionInfo;
import com.meicam.sdk.NvsPointD;
import com.meicam.sdk.NvsPosition2D;
import com.meicam.sdk.NvsTimelineAnimatedSticker;
import com.meicam.sdk.NvsTimelineCaption;
import com.meicam.sdk.NvsTimelineVideoFx;
import com.meicam.sdk.NvsVideoFx;
import com.meishe.base.bean.FloatPoint;
import com.meishe.base.utils.GsonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.local.LMeicamFxParam;
import com.meishe.engine.local.LMeicamKeyFrame;
import com.meishe.engine.local.LMeicamKeyframeControlPoints;
import com.meishe.engine.util.ColorUtil;
import com.meishe.engine.util.gson.GsonContext;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.meishe.engine.bean.MeicamFxParam.TYPE_BOOLEAN;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_COLOR;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_FLOAT;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_INT;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_OBJECT;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_POSITION_2D;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_STRING;


/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/6/17 15:36
 * @Description :关键帧信息 The key frame info
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MeicamKeyFrame extends NvsObject<NvsFx> implements Cloneable, Serializable, TimelineDataParserAdapter<LMeicamKeyFrame> {
    public static final String CAPTION_TRANS_X = "Caption TransX";
    public static final String CAPTION_TRANS_Y = "Caption TransY";
    public static final String CAPTION_SCALE_X = "Caption ScaleX";
    public static final String CAPTION_SCALE_Y = "Caption ScaleY";
    public static final String CAPTION_ROTATION_Z = "Caption RotZ";
    public static final String STICKER_TRANS_X = "Sticker TransX";
    public static final String STICKER_TRANS_Y = "Sticker TransY";
    public static final String STICKER_SCALE = "Sticker Scale";
    public static final String STICKER_ROTATION_Z = "Sticker RotZ";
    public static final String ROTATION = "Rotation";
    public static final String SCALE_X = "Scale X";
    public static final String SCALE_Y = "Scale Y";
    public static final String TRANS_X = "Trans X";
    public static final String TRANS_Y = "Trans Y";
    /**
     * 关键帧所在的时间点
     * The point in time at which the keyframe is located
     */
    private long atTime;

    /**
     * 关键帧所在的时间点的偏移量
     * The offset point in time at which the keyframe is located
     */
    private long offsetTime;
    /**
     * 关键帧所对应的参数列表
     * The list of parameters corresponding to the keyframe
     */
    private List<MeicamFxParam<?>> paramList;

    /**
     * 控制点
     * Control points
     */
    private MeicamKeyframeControlPoints mControlPoints;
    private MeicamKeyFrame mNext;
    private MeicamKeyFrame mFont;

    public long getAtTime() {
        return atTime;
    }

    void setAtTime(long atTime) {
        this.atTime = atTime;
    }

    public long getOffsetTime() {
        return offsetTime;
    }

    public void setOffsetTime(long offsetTime) {
        this.offsetTime = offsetTime;
    }

    List<MeicamFxParam<?>> getParamList() {
        checkParamList();
        return paramList;
    }

    public MeicamKeyFrame getNext() {
        return mNext;
    }

    public void setNext(MeicamKeyFrame mNext) {
        this.mNext = mNext;
    }

    public MeicamKeyFrame getFont() {
        return mFont;
    }

    public void setFont(MeicamKeyFrame mFont) {
        this.mFont = mFont;
    }

    public List<MeicamFxParam<?>> getParams() {
        if (paramList == null) {
            return null;
        }
        return new ArrayList<>(paramList);
    }

    public void setParamList(List<MeicamFxParam<?>> paramList) {
        this.paramList = paramList;
        bindToTimeline(true);
    }


    public void removeParam(@NonNull Set<String> keySet) {
        for (int index = paramList.size() - 1; index >= 0; index--) {
            if (keySet.contains(paramList.get(index).getKey())) {
                paramList.remove(index);
            }
        }
    }


    private void checkParamList() {
        if (paramList == null) {
            paramList = new ArrayList<>();
        }
    }

    /**
     * 更新关键帧
     * Update key frame
     */
    public void update(boolean setKeyFrameAtTime) {
        setKeyFrameValue(setKeyFrameAtTime);
    }

    /**
     * 设置关键帧的值
     * Set key frame value
     */
    public void setDefaultKeyFrameValue() {
        setKeyFrameValue(true);
    }

    /**
     * Get default key frame value list.
     * 获取默认参数
     * @param nvsObject the nvs object
     * @param atTime    the at time 关键帧时间点
     * @return the list
     */
    public static List<MeicamFxParam<?>> getDefaultKeyFrameValue(NvsObject<?> nvsObject, long atTime){
        Object object = nvsObject.getObject();
        List<MeicamFxParam<?>> data = new ArrayList<>();
        if (object instanceof NvsTimelineCaption) {
            NvsTimelineCaption caption = (NvsTimelineCaption) object;
            float scaleX = caption.getScaleX();
            float scaleY = caption.getScaleY();
            PointF transition = caption.getCaptionTranslation();
            if (transition == null) {
                transition = new PointF();
            }
            float rotation = caption.getRotationZ();
            data.add(new MeicamFxParam<>(TYPE_FLOAT, CAPTION_SCALE_X, scaleX));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, CAPTION_SCALE_Y, scaleY));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, CAPTION_TRANS_X, transition.x));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, CAPTION_TRANS_Y, transition.y));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, CAPTION_ROTATION_Z, rotation));
        }  else if (object instanceof NvsTimelineAnimatedSticker) {
            //贴纸关键帧
            //Sticker key frame
            NvsTimelineAnimatedSticker sticker = (NvsTimelineAnimatedSticker) object;

            PointF translation = sticker.getTranslation();
            if (translation == null) {
                translation = new PointF();
            }
            data.add(new MeicamFxParam<>(TYPE_FLOAT, STICKER_SCALE, sticker.getScale()));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, STICKER_TRANS_X, translation.x));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, STICKER_TRANS_Y, translation.y));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, STICKER_ROTATION_Z, sticker.getRotationZ()));
        } else if (object instanceof NvsVideoFx) {
            //视频关键帧
            //Video clip key frame
            NvsVideoFx nvsVideoFx = (NvsVideoFx) object;
            data.add(new MeicamFxParam<>(TYPE_FLOAT, SCALE_X, (float) nvsVideoFx.getFloatValAtTime(SCALE_X, atTime)));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, SCALE_Y, (float) nvsVideoFx.getFloatValAtTime(SCALE_Y, atTime)));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, TRANS_X, (float) nvsVideoFx.getFloatValAtTime(TRANS_X, atTime)));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, TRANS_Y, (float) nvsVideoFx.getFloatValAtTime(TRANS_Y, atTime)));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, ROTATION, (float) nvsVideoFx.getFloatValAtTime(ROTATION, atTime)));
        }
        return data;
    }


    /**
     * Sets key frame value.
     *设置关键帧的值
     *
     * @param setKeyFrameTime the set key frame time 是否更新关键帧时间
     */
    public void setKeyFrameValue(boolean setKeyFrameTime) {
        NvsFx object = getObject();
        if (object instanceof NvsTimelineCaption) {
            NvsTimelineCaption caption = (NvsTimelineCaption) object;
            float scaleX = caption.getScaleX();
            float scaleY = caption.getScaleY();
            PointF transition = caption.getCaptionTranslation();
            if (transition == null) {
                transition = new PointF();
            }
            float rotation = caption.getRotationZ();
            LogUtils.d("scaleX=" + scaleX + ",scaleY=" + scaleY + ",transition=" + transition + ",rotation=" + rotation + ",atTime=" + atTime + ",caption=" + caption);
            if (setKeyFrameTime) {
                caption.setCurrentKeyFrameTime(atTime);
            }
            setFloatParam(CAPTION_SCALE_X, scaleX);
            caption.setScaleX(scaleX);
            setFloatParam(CAPTION_SCALE_Y, scaleY);
            caption.setScaleY(scaleY);
            setFloatParam(CAPTION_TRANS_X, transition.x);
            setFloatParam(CAPTION_TRANS_Y, transition.y);
            caption.setCaptionTranslation(transition);
            setFloatParam(CAPTION_ROTATION_Z, rotation);
            caption.setRotationZ(rotation);

            List<MeicamFxParam<?>> data = new ArrayList<>();
            data.add(new MeicamFxParam<>(TYPE_FLOAT, CAPTION_SCALE_X, scaleX));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, CAPTION_SCALE_Y, scaleY));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, CAPTION_TRANS_X, transition.x));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, CAPTION_TRANS_Y, transition.y));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, CAPTION_ROTATION_Z, rotation));
            this.paramList = data;
            LogUtils.d("scaleX=" + scaleX + ",scaleY=" + scaleY + ",transition="
                    + transition + ",rotation=" + rotation + ",atTime=" + atTime);
        } else if (object instanceof NvsCompoundCaption) {
            //组合字幕暂无
            //No compound caption now
        } else if (object instanceof NvsTimelineAnimatedSticker) {
            //贴纸关键帧
            //Sticker key frame
            NvsTimelineAnimatedSticker sticker = (NvsTimelineAnimatedSticker) object;
            if (setKeyFrameTime) {
                sticker.setCurrentKeyFrameTime(atTime);
            }
            PointF translation = sticker.getTranslation();
            if (translation == null) {
                translation = new PointF();
            }
            setFloatParam(STICKER_SCALE, sticker.getScale());
            sticker.setScale(sticker.getScale());
            setFloatParam(STICKER_TRANS_X, translation.x);
            setFloatParam(STICKER_TRANS_Y, translation.y);
            sticker.setTranslation(translation);
            setFloatParam(STICKER_ROTATION_Z, sticker.getRotationZ());
            sticker.setRotationZ(sticker.getRotationZ());
            List<MeicamFxParam<?>> data = new ArrayList<>();
            data.add(new MeicamFxParam<>(TYPE_FLOAT, STICKER_SCALE, sticker.getScale()));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, STICKER_TRANS_X, translation.x));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, STICKER_TRANS_Y, translation.y));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, STICKER_ROTATION_Z, sticker.getRotationZ()));
            this.paramList = data;
        } else if (object instanceof NvsVideoFx) {
            //视频关键帧
            //Video clip key frame
            setFloatVal(SCALE_X, (float) object.getFloatValAtTime(SCALE_X, atTime + offsetTime));
            setFloatVal(SCALE_Y, (float) object.getFloatValAtTime(SCALE_Y, atTime + offsetTime));
            setFloatVal(TRANS_X, (float) object.getFloatValAtTime(TRANS_X, atTime + offsetTime));
            setFloatVal(TRANS_Y, (float) object.getFloatValAtTime(TRANS_Y, atTime + offsetTime));
            setFloatVal(ROTATION, (float) object.getFloatValAtTime(ROTATION, atTime + offsetTime));
        }
    }


    public void setPosition2DVal(String key, MeicamPosition2D value) {
        if (getObject() != null) {
            getObject().setPosition2DValAtTime(key, new NvsPosition2D(value.x, value.y), atTime + offsetTime);
        }
        setPosition2DParam(key, value);
    }

    private void setPosition2DParam(String key, MeicamPosition2D value) {
        MeicamFxParam fxParam = getFxParam(key);
        if (fxParam == null) {
            checkParamList();
            paramList.add(new MeicamFxParam<>(TYPE_POSITION_2D, key, value));
        } else {
            fxParam.setValue(value);
        }
    }

    /**
     * 设置关键帧浮点类型参数
     * Set the float value of key frame
     *
     * @param key   the key键
     * @param value the value值
     */
    public void setFloatVal(String key, float value) {
        if (getObject() != null) {
            getObject().setFloatValAtTime(key, value, atTime + offsetTime);
        }
        setFloatParam(key, value);
    }

    /**
     * 设置关键帧整类型参数
     * Set the int value of key frame
     *
     * @param key   the key键
     * @param value the value值
     */
    public void setIntVal(String key, int value) {
        if (getObject() != null) {
            getObject().setIntValAtTime(key, value, atTime + offsetTime);
        }
        setIntParam(key, value);
    }

    /**
     * 设置颜色
     * Set color
     *
     * @param key   key 键值
     * @param value color 颜色
     */
    public void setColor(String key, String value) {
        if (TextUtils.isEmpty(key) || TextUtils.isEmpty(value)) {
            return;
        }
        if (setColor(key, ColorUtil.colorToNvsColor(value))) {
            setColorParam(key, value);
        }
    }

    /**
     * 设置颜色
     * Set color
     *
     * @param key   key 键值
     * @param color color 颜色
     */
    private boolean setColor(String key, NvsColor color) {
        if (getObject() != null) {
            getObject().setColorValAtTime(key, color, atTime + offsetTime);
            return true;
        }
        return false;
    }

    /**
     * Sets arb data val.
     * 设置属性参数
     *
     * @param key        the key
     * @param regionInfo the region info
     */
    public void setArbDataVal(String key, MeicamMaskRegionInfo regionInfo) {
        if (getObject() != null) {
            getObject().setArbDataValAtTime(key, regionInfo.getMaskRegionInfo(), atTime + offsetTime);
        }
        setObjectParam(key, regionInfo);
    }

    /**
     * 设置Boolean类型参数
     * Set the Boolean value of key frame
     *
     * @param key   the key键
     * @param value the value值
     */
    public void setBooleanVal(String key, boolean value) {
        if (getObject() != null) {
            getObject().setBooleanValAtTime(key, value, atTime + offsetTime);
        }
        setBooleanParam(key, value);
    }

    /**
     * 设置关键帧浮点类型参数
     * Set the float value of key frame
     *
     * @param key   the key键
     * @param value the value值
     */
    public void setFloatValAtTime(String key, float value, long atTime) {
        if (getObject() != null) {
            getObject().setFloatValAtTime(key, value, atTime);
        }
        setFloatParam(key, value);
    }

    /**
     * 设置关键帧浮点类型参数
     * Set the float value of key frame
     *
     * @param key    the key键
     * @param atTime the value值
     */
    public void removeKeyframeAtTime(String key, long atTime) {
        if (getObject() != null) {
            removeKeyFrame(key);
            getObject().removeKeyframeAtTime(key, atTime);
        }
    }

    /**
     * 设置关键帧浮点类型参数
     * Set the float value of key frame
     *
     * @param key   the key键
     * @param value the value值
     */
    private void setFloatParam(String key, float value) {
        MeicamFxParam fxParam = getFxParam(key);
        if (fxParam == null) {
            checkParamList();
            paramList.add(new MeicamFxParam<>(TYPE_FLOAT, key, value));
        } else {
            fxParam.setValue(value);
        }
    }


    /**
     * 设置关键帧整类型参数
     * Set the int value of key frame
     *
     * @param key   the key键
     * @param value the value值
     */
    private void setIntParam(String key, int value) {
        MeicamFxParam fxParam = getFxParam(key);
        if (fxParam == null) {
            checkParamList();
            paramList.add(new MeicamFxParam<>(TYPE_INT, key, value));
        } else {
            fxParam.setValue(value);
        }
    }

    /**
     * 设置关键帧颜色类型参数
     * Set the color value of key frame
     *
     * @param key   the key键
     * @param value the value值
     */
    private void setColorParam(String key, String value) {
        MeicamFxParam fxParam = getFxParam(key);
        if (fxParam == null) {
            checkParamList();
            paramList.add(new MeicamFxParam<>(TYPE_COLOR, key, value));
        } else {
            fxParam.setValue(value);
        }
    }

    /**
     * 设置Object类型参数
     * Set the object value of key frame
     *
     * @param key   the key键
     * @param value the value值
     */
    private void setObjectParam(String key, Object value) {
        MeicamFxParam fxParam = getFxParam(key);
        if (fxParam == null) {
            checkParamList();
            paramList.add(new MeicamFxParam<>(TYPE_OBJECT, key, value));
        } else {
            fxParam.setValue(value);
        }
    }

    /**
     * 设置Boolean类型参数
     * Set the Boolean value of key frame
     *
     * @param key   the key键
     * @param value the value值
     */
    private void setBooleanParam(String key, boolean value) {
        MeicamFxParam fxParam = getFxParam(key);
        if (fxParam == null) {
            checkParamList();
            paramList.add(new MeicamFxParam<>(TYPE_BOOLEAN, key, value));
        } else {
            fxParam.setValue(value);
        }
    }

    /**
     * 获取关键帧浮点类型参数
     * Get the float value of key frame
     *
     * @param key    the key键
     * @param atTime the time
     * @return the value
     */
    public double getFloatValue(String key, long atTime) {
        if (getObject() != null) {
            return getObject().getFloatValAtTime(key, atTime);
        }
        return Double.MIN_VALUE;
    }

    /**
     * Get position2d val
     * 获取position2d值
     *
     * @param key    the key
     * @param atTime the at time
     * @return the meicam position 2 d
     */
    public MeicamPosition2D getPosition2DVal(String key, long atTime) {
        if (getObject() != null) {
            NvsPosition2D position2D = getObject().getPosition2DValAtTime(key, atTime);
            if (position2D != null) {
                return new MeicamPosition2D(position2D.x, position2D.y);
            }
        }
        return null;
    }

    /**
     * 获取关键帧Mask类型参数
     * Get the Mask value of key frame
     *
     * @param key    the key键
     * @param atTime the time
     * @return the value
     */
    public MeicamMaskRegionInfo getArbValue(String key, long atTime) {
        if (getObject() != null) {
            NvsMaskRegionInfo dataValAtTime = new NvsMaskRegionInfo();
            dataValAtTime = (NvsMaskRegionInfo) getObject().getArbDataValAtTime(key, dataValAtTime, atTime);
            MeicamMaskRegionInfo regionInfo = new MeicamMaskRegionInfo();
            regionInfo.setRegionInfo(dataValAtTime);
            return regionInfo;
        }
        return null;
    }

    /**
     * 获取关键帧Boolean类型参数
     * Get the Boolean value of key frame
     *
     * @param key    the key键
     * @param atTime the time
     * @return the value
     */
    public boolean getBooleanValue(String key, long atTime) {
        if (getObject() != null) {
            return getObject().getBooleanValAtTime(key, atTime);
        }
        return false;
    }

    /**
     * 获取关键帧int类型参数
     * Get the int value of key frame
     *
     * @param key    the key键
     * @param atTime the time
     * @return the value
     */
    public int getIntValue(String key, long atTime) {
        if (getObject() != null) {
            return getObject().getIntValAtTime(key, atTime);
        }
        return 0;
    }


    /**
     * 获取关键帧MeicamPosition2D类型参数
     * Get the MeicamPosition2D value of key frame
     *
     * @param key    the key键
     * @param atTime the time
     * @return the value
     */
    public MeicamPosition2D getMeicamPosition2DValue(String key, long atTime) {
        if (getObject() != null) {
            NvsPosition2D position2D = getObject().getPosition2DValAtTime(key, atTime);

            return new MeicamPosition2D(position2D.x, position2D.y);
        }
        return null;
    }

    /**
     * 获取关键帧Color类型参数
     * Get the Color value of key frame
     *
     * @param key    the key键
     * @param atTime the time
     * @return the value
     */
    public String getColorValue(String key, long atTime) {
        if (getObject() != null) {
            NvsColor nvsColor = getObject().getColorValAtTime(key, atTime);
            if (nvsColor == null) {
                return null;
            }
            return ColorUtil.nvsColorToHexString(nvsColor);
        }
        return null;
    }


    /**
     * 获取关键帧参数
     * Get the key frame param
     *
     * @param key the key键
     * @return MeicamFxParam 参数
     */
    public MeicamFxParam<?> getFxParam(String key) {
        if (paramList != null && !TextUtils.isEmpty(key)) {
            for (MeicamFxParam<?> item : paramList) {
                if (key.equals(item.getKey())) {
                    return item;
                }
            }
        }
        return null;
    }

    /**
     * 获取参数数量
     * Get param count
     *
     * @return count数量
     */
    public int getFxParamCount() {
        if (paramList == null) {
            return -1;
        }
        return paramList.size();
    }

    /**
     * 获取关键帧参数
     * Get the key frame param
     *
     * @param index the index 索引
     * @return MeicamFxParam 参数
     */
    public MeicamFxParam<?> getFxParam(int index) {
        if (paramList != null && index >= 0 && index < paramList.size()) {
            return paramList.get(index);
        }
        return null;
    }

    /**
     * 获取关键帧在某个时间点的参数列表
     * Get params from at time
     *
     * @param atTime the time 所在时间点
     */
    public List<MeicamFxParam<?>> getParamsFromAtTime(long atTime) {
        if (paramList == null) {
            return null;
        }
        List<MeicamFxParam<?>> newParams = new ArrayList<>();
        try {
            float fxValue;
            for (MeicamFxParam<?> param : paramList) {
                MeicamFxParam copy = param.copy();
                Object value = param.getValue();
                String key = param.getKey();
                String type = param.getType();
                if (TYPE_FLOAT.equals(type)) {
                    if (value instanceof Double || value instanceof Float) {
                        fxValue = (float) getFloatValue(key, atTime);
                        copy.setValue(fxValue);
                    }
                } else if (TYPE_BOOLEAN.equals(type)) {
                    copy.setValue(getBooleanValue(key, atTime));
                } else if (TYPE_STRING.equals(type)) {
                } else if (TYPE_INT.equals(type)) {
                    copy.setValue(getIntValue(key, atTime));
                } else if (TYPE_COLOR.equals(type)) {
                    copy.setValue(getColorValue(key, atTime));
                } else if (TYPE_OBJECT.equals(type)) {
                    if (value instanceof MeicamPosition2D) {
                        copy.setValue(getMeicamPosition2DValue(key, atTime));
                    } else {
                        copy.setValue(getArbValue(key, atTime));
                    }
                } else {
                    continue;
                }
                newParams.add(copy);
            }
        } catch (Exception e) {
            LogUtils.e(e);
        }
        return newParams;
    }

    /**
     * 移除关键帧参数
     * Delete the key frame param
     *
     * @param key the key键
     */
    private void deleteFxParam(String key) {
        if (paramList != null && !TextUtils.isEmpty(key)) {
            for (int i = 0; i < paramList.size(); i++) {
                MeicamFxParam<?> fxParam = paramList.get(i);
                if (key.equals(fxParam.getKey())) {
                    paramList.remove(i);
                    break;
                }
            }
        }
    }

    /**
     * 移除关键帧
     * remove key frame
     *
     * @param key the key键
     */
    public void removeKeyFrame(String key) {
        if (getObject() != null) {
            deleteFxParam(key);
            getObject().removeKeyframeAtTime(key, this.atTime);
        }
    }

    /**
     * 移除所有的关键帧
     * remove all key frame
     *
     * @param keySet the set of key， if it is null, remove all keys. 要删除的key的集合，如果为空，则删除左右的key
     * @param clear  true clear list 清除集合，false not不清除
     */
    public void removeKeyFrame(Set<String> keySet, boolean clear) {
        NvsFx object = getObject();
        if (paramList != null && object != null) {
            for (MeicamFxParam<?> item : paramList) {
                String key = item.getKey();
                if (keySet == null) {
                    object.removeKeyframeAtTime(key, atTime + offsetTime);
                } else {
                    if (keySet.contains(key)) {
                        object.removeKeyframeAtTime(key, atTime + offsetTime);
                    }
                }
            }
            if (clear) {
                paramList.clear();
            }
        }
    }

    public void removeKeyFrame(boolean clear) {
        removeKeyFrame(null, clear);
    }


    /**
     * Add control points
     * 添加控制点
     *
     * @param linkedNode   关联的节点 the linked node
     * @param controlPoint 控制点 the control point
     * @param isFont       是否是前置控制点. Is font control point or not.
     * @return 关键帧控制点 the key frame control point
     */
    public MeicamKeyframeControlPoints addControlPoints(MeicamKeyFrame linkedNode, FloatPoint controlPoint, boolean isFont) {
        if (linkedNode == null || controlPoint == null) {
            LogUtils.e("params are error!");
            return null;
        }
        /*
         * 添加控制点，前面关键帧的前置点，后面关键帧的后置点
         * Add control points, front points of previous keyframes, and rear points of subsequent keyframes.
         */
        MeicamKeyframeControlPoints controlPoints = getControlPoints();
        if (isFont) {
            mNext = linkedNode.clone();
            controlPoints.setForwardControlPoint(controlPoint);
        } else {
            mFont = linkedNode.clone();
            controlPoints.setBackwardControlPoint(controlPoint);
        }
        bindCurveToTimeline();
        return controlPoints;
    }

    /**
     * Fill control point data
     * 填充控制点数据
     *
     * @param startFrame   开始节点 the start frame node
     * @param endFrame     结束节点 the end frame node
     * @param controlPoint 控制点 the control point
     * @param isFont       是否是前置控制点. Is font control point or not.
     * @return 关键帧控制点 the key frame control point
     */
    private MeicamKeyframeControlPoints fillControlPointData(MeicamKeyFrame startFrame, MeicamKeyFrame endFrame, FloatPoint controlPoint, boolean isFont) {
        if (startFrame == null || endFrame == null || controlPoint == null) {
            return null;
        }
        MeicamKeyframeControlPoints controlPoints = getControlPoints();
        long nextTimeStamp = endFrame.getAtTime();
        long beforeTimeStamp = startFrame.getAtTime();
        float controlPointX = (float) linearScale(nextTimeStamp, beforeTimeStamp, controlPoint.x);
        Map<String, Float> lastParamMap = new HashMap<>();
        for (MeicamFxParam<?> param : endFrame.getParamList()) {
            Object value = param.getValue();
            float valueF;
            if (value instanceof Double) {
                valueF = ((Double) value).floatValue();
            } else if (value instanceof Float) {
                valueF = (float) value;
            } else {
                continue;
            }
            lastParamMap.put(param.getKey(), valueF);
        }
        for (MeicamFxParam<?> before : startFrame.getParamList()) {
            Object value = before.getValue();
            float beforeValue;
            if (value instanceof Double) {
                beforeValue = ((Double) value).floatValue();
            } else if (value instanceof Float) {
                beforeValue = (float) value;
            } else {
                continue;
            }
            Float lastValue = lastParamMap.get(before.getKey());
            if (lastValue != null) {
                float forwardControlPointYForTransX = (float) linearScale(lastValue, beforeValue, controlPoint.y);
                if (isFont) {
                    controlPoints.addForwardControlPoint(before.getKey(), new FloatPoint(controlPointX, forwardControlPointYForTransX));
                } else {
                    controlPoints.addBackwardControlPoint(before.getKey(), new FloatPoint(controlPointX, forwardControlPointYForTransX));
                }
            }
        }

        return controlPoints;
    }

    private double linearScale(long next, long before, float factor) {
        return (next - before) * factor + before;
    }

    private double linearScale(float next, float before, float factor) {
        return (next - before) * factor + before;
    }

    /**
     * Inverse formula
     * 反推公式
     *
     * @param next   the next
     * @param before the before
     * @param value  the Y valye
     * @return the X value
     */
    private double linearScale_y_x(long next, long before, float value) {
        return (value - before) / (next - before);
    }

    /**
     * Inverse formula
     * 反推公式
     *
     * @param next   the next
     * @param before the before
     * @param value  the Y valye
     * @return the X value
     */
    private double linearScale_y_x(float next, float before, float value) {
        return (value - before) / (next - before);
    }


    /**
     * Remove backward control points
     * 删除后置控制点
     */
    public void removeBackwardControlPoints() {
        getControlPoints().setBackwardControlPoint(null);
        mFont = null;
    }

    /**
     * Remove forward control points
     * 删除前置控制点
     */
    public void removeForwardControlPoints() {
        getControlPoints().setForwardControlPoint(null);
        mNext = null;
    }

    /**
     * Get linked curve.
     * 获取连接的曲线
     *
     * @return 连接的曲线 the key frame curve.
     */
    public MeicamKeyframeControlPoints getControlPoints() {
        if (mControlPoints == null) {
            mControlPoints = new MeicamKeyframeControlPoints();
        }
        return mControlPoints;
    }


    void bindToTimeline(boolean setKeyFrame) {
        if (getObject() != null && paramList != null && paramList.size() > 0) {
            NvsFx object = getObject();
            float value;
            if (object instanceof NvsTimelineCaption) {
                //字幕关键帧
                //Caption key frame
                NvsTimelineCaption caption = (NvsTimelineCaption) object;
                PointF transition = new PointF();
                if (setKeyFrame) {
                    caption.setCurrentKeyFrameTime(atTime);
                }
                for (MeicamFxParam<?> item : paramList) {
                    Object itemValue = item.getValue();
                    if (itemValue instanceof Double) {
                        value = ((Double) itemValue).floatValue();
                    } else if (itemValue instanceof Float) {
                        value = (Float) itemValue;
                    } else {
                        continue;
                    }
                    if (CAPTION_SCALE_X.equals(item.getKey())) {
                        caption.setScaleX(value);
                    } else if (CAPTION_SCALE_Y.equals(item.getKey())) {
                        caption.setScaleY(value);
                    } else if (CAPTION_TRANS_X.equals(item.getKey())) {
                        transition.x = value;
                    } else if (CAPTION_TRANS_Y.equals(item.getKey())) {
                        transition.y = value;
                    } else if (CAPTION_ROTATION_Z.equals(item.getKey())) {
                        caption.setRotationZ(value);
                    }
                }
                caption.setCaptionTranslation(transition);
                //LogUtils.d("scaleX=" + caption.getScaleX() + ",scaleY=" + caption.getScaleY() + ",transition=" + caption.getCaptionTranslation() + atTime + ",caption=" + caption);
            } else if (object instanceof NvsCompoundCaption) {
                //组合字幕暂无
                //No compound caption now
            } else if (object instanceof NvsTimelineAnimatedSticker) {
                /*贴纸关键帧
                 * Sticker key frame
                 * */
                NvsTimelineAnimatedSticker sticker = (NvsTimelineAnimatedSticker) object;
                if (setKeyFrame) {
                    sticker.setCurrentKeyFrameTime(atTime);
                }
                PointF transition = new PointF();
                for (MeicamFxParam item : paramList) {
                    if (item.getValue() instanceof Double) {
                        value = ((Double) item.getValue()).floatValue();
                    } else if (item.getValue() instanceof Float) {
                        value = (float) item.getValue();
                    } else {
                        continue;
                    }
                    if (STICKER_SCALE.equals(item.getKey())) {
                        sticker.setScale(value);
                    } else if (STICKER_TRANS_X.equals(item.getKey())) {
                        transition.x = value;
                    } else if (STICKER_TRANS_Y.equals(item.getKey())) {
                        transition.y = value;
                    } else if (STICKER_ROTATION_Z.equals(item.getKey())) {
                        sticker.setRotationZ(value);
                    }
                }
                sticker.setTranslation(transition);
            } else if (object instanceof NvsVideoFx || object instanceof NvsTimelineVideoFx) {
                /*视频片段关键帧*/
                for (MeicamFxParam<?> item : paramList) {
                    String type = item.getType();
                    String itemKey = item.getKey();
                    Object itemValue = item.getValue();
                    if (MeicamFxParam.TYPE_OBJECT.equals(type)) {
                        String json = GsonUtils.toJson(itemValue);
                        itemValue = GsonUtils.fromJson(json, MeicamMaskRegionInfo.class);
                        if (itemValue != null) {
                            object.setArbDataValAtTime(itemKey, ((MeicamMaskRegionInfo) itemValue).getMaskRegionInfo(), atTime + offsetTime);
                        }
                    } else if (TYPE_BOOLEAN.equals(type)) {
                        if (itemValue instanceof Boolean) {
                            object.setBooleanValAtTime(itemKey, (Boolean) itemValue, atTime + offsetTime);
                        }
                    } else if (TYPE_FLOAT.equals(type)) {
                        if (item.getValue() instanceof Double) {
                            object.setFloatValAtTime(itemKey, ((Double) itemValue).floatValue(), atTime + offsetTime);
                        } else if (item.getValue() instanceof Float) {
                            object.setFloatValAtTime(itemKey, (float) itemValue, atTime + offsetTime);
                        }
                    } else if (TYPE_POSITION_2D.equals(type)) {
                        MeicamPosition2D position2D = (MeicamPosition2D) item.getValue();
                        object.setPosition2DValAtTime(itemKey, new NvsPosition2D(position2D.x, position2D.y), atTime + offsetTime);
                    } else if (TYPE_INT.equals(type)) {
                        if (item.getValue() instanceof Double) {
                            object.setIntValAtTime(itemKey, ((Double) itemValue).intValue(), atTime + offsetTime);
                        } else if (item.getValue() instanceof Integer) {
                            object.setIntValAtTime(itemKey, (int) itemValue, atTime + offsetTime);
                        }
                    } else if (TYPE_COLOR.equals(type)) {
                        object.setColorValAtTime(itemKey, ColorUtil.colorToNvsColor((String) itemValue), atTime + offsetTime);
                    }
                }
            }
        }
    }


    public void bindOnlyKeyFrame() {
        if (getObject() != null) {
            NvsFx object = getObject();
            if (object instanceof NvsTimelineCaption) {
                //字幕关键帧
                //Caption key frame
                NvsTimelineCaption caption = (NvsTimelineCaption) object;
                caption.setCurrentKeyFrameTime(atTime);
            } else if (object instanceof NvsCompoundCaption) {
                //组合字幕暂无
                //No compound caption now
            } else if (object instanceof NvsTimelineAnimatedSticker) {
                /*贴纸关键帧
                 * Sticker key frame
                 * */
                NvsTimelineAnimatedSticker sticker = (NvsTimelineAnimatedSticker) object;
                sticker.setCurrentKeyFrameTime(atTime);
            }
        }
    }

    public void bindCurveToTimeline() {
        if (mNext != null) {
            fillControlPointData(this, mNext, getControlPoints().getForwardControlPoint(), true);
            fillControlPointData(this, mNext, getControlPoints().getBackwardControlPoint(), false);
        } else {
            MeicamKeyframeControlPoints controlPoints = getControlPoints();
            controlPoints.getForwardControlPointMap().clear();
        }
        if (mFont != null) {
            fillControlPointData(mFont, this, getControlPoints().getForwardControlPoint(), true);
            fillControlPointData(mFont, this, getControlPoints().getBackwardControlPoint(), false);
        } else {
            MeicamKeyframeControlPoints controlPoints = getControlPoints();
            controlPoints.getBackwardControlPointMap().clear();
        }

        MeicamKeyframeControlPoints controlPoints = getControlPoints();
        if (controlPoints == null) {
            return;
        }
        if (getObject() != null && paramList != null && paramList.size() > 0) {
            NvsFx object = getObject();
            if (object instanceof NvsTimelineCaption) {
                /*字幕关键帧
                * Caption key frame
                * */
                addControlPoint(controlPoints, (NvsTimelineCaption) object);
            } else if (object instanceof NvsCompoundCaption) {
                /*组合字幕暂无
                * No compound caption key frame now
                * */
            } else if (object instanceof NvsTimelineAnimatedSticker) {
                /*贴纸关键帧
                * Sticker key frame
                * */
                addControlPoint(controlPoints, (NvsTimelineAnimatedSticker) object);
            } else if (object instanceof NvsVideoFx) {
                /*视频片段关键帧
                * Video clip key frame
                * */
                addControlPoint(controlPoints, (NvsVideoFx) object);
            } else if (object instanceof NvsTimelineVideoFx) {
                /*时间线特效关键帧
                * Timeline fx key frame
                * */
                addControlPoint(controlPoints, (NvsTimelineVideoFx) object);
            }
        }
    }

    private void addControlPoint(@NonNull MeicamKeyframeControlPoints controlPoints, @NonNull NvsTimelineVideoFx nvsTimelineVideoFx) {
        long atTime = getAtTime() + offsetTime;
        for (MeicamFxParam<?> item : paramList) {
            String key = item.getKey();
            NvsControlPointPair pairX = nvsTimelineVideoFx.getKeyFrameControlPoint(key, atTime);
            NvsControlPointModePair keyFrameControlPointMode = nvsTimelineVideoFx.getKeyFrameControlPointMode(key, atTime);
            nvsTimelineVideoFx.setKeyFrameControlPoint(key, atTime, handleNvsControlPoints(controlPoints, key, pairX, keyFrameControlPointMode));
            nvsTimelineVideoFx.setKeyFrameControlPointMode(key, atTime, keyFrameControlPointMode);
        }
    }

    private void addControlPoint(@NonNull MeicamKeyframeControlPoints controlPoints, @NonNull NvsVideoFx videoFx) {
        long atTime = getAtTime() + offsetTime;
        for (MeicamFxParam<?> item : paramList) {
            String key = item.getKey();
            NvsControlPointPair pairX = videoFx.getKeyFrameControlPoint(key, atTime);
            NvsControlPointModePair keyFrameControlPointMode = videoFx.getKeyFrameControlPointMode(key, atTime);
            videoFx.setKeyFrameControlPoint(key, atTime, handleNvsControlPoints(controlPoints, key, pairX, keyFrameControlPointMode));
            videoFx.setKeyFrameControlPointMode(key, atTime, keyFrameControlPointMode);
        }
    }

    private NvsControlPointPair handleNvsControlPoints(@NonNull MeicamKeyframeControlPoints controlPoints, String key, NvsControlPointPair pairX, NvsControlPointModePair modePairX) {
        if (pairX == null) {
            pairX = new NvsControlPointPair(new NvsPointD(0, 0), new NvsPointD(0, 0));
        }
        if (modePairX == null) {
            modePairX = new NvsControlPointModePair(NvsControlPointModePair.ControlPointMode_Linear, NvsControlPointModePair.ControlPointMode_Linear);
        }
        Map<String, FloatPoint> forwardControlPointMap = controlPoints.getForwardControlPointMap();
        FloatPoint pointF = forwardControlPointMap.get(key);
        if (pointF != null) {
            pairX.forwardControlPoint = new NvsPointD(pointF.x, pointF.y);
            modePairX.forwardControlPointMode = NvsControlPointModePair.ControlPointMode_Bezier;
        } else {
            modePairX.forwardControlPointMode = NvsControlPointModePair.ControlPointMode_Linear;
        }
        Map<String, FloatPoint> backwardControlPointMap = controlPoints.getBackwardControlPointMap();
        pointF = backwardControlPointMap.get(key);
        if (pointF != null) {
            pairX.backwardControlPoint = new NvsPointD(pointF.x, pointF.y);
            modePairX.backwardControlPointMode = NvsControlPointModePair.ControlPointMode_Bezier;
        } else {
            modePairX.backwardControlPointMode = NvsControlPointModePair.ControlPointMode_Linear;
        }
        if (pairX.forwardControlPoint != null) {
            LogUtils.d("addControlPoint:NvsVideoFx >>>> atTime = " + atTime + ", key = " + key + ",forwardControlPoint X = " + pairX.forwardControlPoint.x + ", Y = " + pairX.forwardControlPoint.y);
        }
        if (pairX.backwardControlPoint != null) {
            LogUtils.d("addControlPoint:NvsVideoFx >>>> atTime = " + atTime + ", key = " + key + ", backwardControlPoint X = " + pairX.backwardControlPoint.x + ", Y = " + pairX.backwardControlPoint.y);
        }
        return pairX;
    }

    private void addControlPoint(@NonNull MeicamKeyframeControlPoints controlPoints,
                                 @NonNull NvsCaption caption) {
        long atTime = getAtTime() + offsetTime;
        for (MeicamFxParam<?> item : paramList) {
            String key = item.getKey();
            caption.setCurrentKeyFrameTime(atTime);
            NvsControlPointPair pairX = caption.getKeyFrameControlPoint(key, atTime);
            NvsControlPointModePair keyFrameControlPointMode = caption.getKeyFrameControlPointMode(key, atTime);
            caption.setControlPoint(key, handleNvsControlPoints(controlPoints, key, pairX, keyFrameControlPointMode));
            caption.setKeyFrameControlPointMode(key, atTime, keyFrameControlPointMode);
        }
    }

    private void addControlPoint(@NonNull MeicamKeyframeControlPoints controlPoints,
                                 @NonNull NvsTimelineAnimatedSticker sticker) {
        long atTime = getAtTime() + offsetTime;
        for (MeicamFxParam<?> item : paramList) {
            String key = item.getKey();
            sticker.setCurrentKeyFrameTime(atTime);
            NvsControlPointPair pairX = sticker.getControlPoint(key);
            NvsControlPointModePair keyFrameControlPointMode = sticker.getKeyFrameControlPointMode(key, atTime);
            sticker.setControlPoint(key, handleNvsControlPoints(controlPoints, key, pairX, keyFrameControlPointMode));
            sticker.setKeyFrameControlPointMode(key, atTime, keyFrameControlPointMode);
        }
    }

    /**
     * KEY_FRAME_FIND_MODE_INPUT_TIME_BEFORE,KEY_FRAME_FIND_MODE_INPUT_TIME_AFTER
     * 获取指定时间点附近的关键帧
     * Gets a key frame near the specified point in time
     *
     * @param key    the key value of the key frame 关键帧的键参数
     * @param atTime the time of the key frame 关键帧的时间点
     * @param flag   the flag  KEY_FRAME_FIND_MODE_INPUT_TIME_BEFORE,KEY_FRAME_FIND_MODE_INPUT_TIME_AFTER
     * @return the key frame time point
     */
    long findKeyframeTime(String key, long atTime, int flag) {
        NvsFx object = getObject();
        if (object != null) {
            return object.findKeyframeTime(key, atTime, flag);
        }
        return -1;
    }

    @NonNull
    @Override
    public MeicamKeyFrame clone() {
//        Object o = DeepCopyUtil.deepClone(this);
//        if (o != null) {
//            return (MeicamKeyFrame) o;
//        }
        String jsonData = GsonContext.getInstance().toJson(this);
        if (!TextUtils.isEmpty(jsonData)) {
            return GsonContext.getInstance().fromJson(jsonData, MeicamKeyFrame.class);
        }
        MeicamKeyFrame keyFrame = new MeicamKeyFrame();
        keyFrame.setAtTime(getAtTime());
        keyFrame.paramList = new ArrayList<>();
        for (MeicamFxParam meicamFxParam : paramList) {
            keyFrame.paramList.add(meicamFxParam.copy());
        }
        return keyFrame;
    }

    @Override
    public LMeicamKeyFrame parseToLocalData() {
        LMeicamKeyFrame lMeicamKeyFrame = new LMeicamKeyFrame();
        lMeicamKeyFrame.setAtTime(getAtTime());
        lMeicamKeyFrame.setExtraTag(getExtraTag());
        lMeicamKeyFrame.setOffsetTime(getOffsetTime());
        MeicamKeyFrame next = getNext();
        if (next != null) {
            lMeicamKeyFrame.setNext(next.parseToLocalData());
        }
        MeicamKeyFrame font = getFont();
        if (font != null) {
            lMeicamKeyFrame.setFont(font.parseToLocalData());
        }
        MeicamKeyframeControlPoints controlPoints = getControlPoints();
        if (controlPoints != null) {
            lMeicamKeyFrame.setControlPoints(controlPoints.parseToLocalData());
        }
        if (paramList != null && paramList.size() > 0) {
            List<LMeicamFxParam<?>> params = new ArrayList<>();
            for (MeicamFxParam<?> param : paramList) {
                params.add(param.parseToLocalData());
            }
            lMeicamKeyFrame.setParamList(params);
        }
        return lMeicamKeyFrame;
    }

    @Override
    public void recoverFromLocalData(LMeicamKeyFrame lMeicamKeyFrame) {
        if (lMeicamKeyFrame != null) {
            setExtraTag(lMeicamKeyFrame.getExtraTag());
            setOffsetTime(lMeicamKeyFrame.getOffsetTime());
            setAtTime(lMeicamKeyFrame.getAtTime());
            LMeicamKeyFrame next = lMeicamKeyFrame.getNext();
            if (next != null) {
                MeicamKeyFrame meicamKeyFrame = new MeicamKeyFrame();
                meicamKeyFrame.recoverFromLocalData(next);
                setNext(meicamKeyFrame);
            }

            LMeicamKeyFrame font = lMeicamKeyFrame.getFont();
            if (font != null) {
                MeicamKeyFrame meicamKeyFrame = new MeicamKeyFrame();
                meicamKeyFrame.recoverFromLocalData(font);
                setFont(meicamKeyFrame);
            }
            LMeicamKeyframeControlPoints local = lMeicamKeyFrame.getControlPoints();
            if (local != null) {
                MeicamKeyframeControlPoints controlPoints = new MeicamKeyframeControlPoints();
                controlPoints.recoverFromLocalData(local);
                mControlPoints = controlPoints;
            }
            List<LMeicamFxParam<?>> paramList = lMeicamKeyFrame.getParamList();
            if (paramList != null) {
                List<MeicamFxParam<?>> list = new ArrayList<>(paramList.size());
                for (LMeicamFxParam<?> param : paramList) {
                    list.add(new MeicamFxParam<>(param.getType(), param.getKey(), param.getValue()));
                }
                setParamList(list);
            }
        }
    }

    @Override
    public void recoverFromTimelineData(com.meicam.sdk.NvsObject nvsObject) {
        if (nvsObject instanceof NvsTimelineCaption) {
            setObject((NvsTimelineCaption) nvsObject);
            NvsTimelineCaption caption = (NvsTimelineCaption) nvsObject;
            caption.setCurrentKeyFrameTime(atTime);
            float scaleX = caption.getScaleX();
            float scaleY = caption.getScaleY();
            PointF transition = caption.getCaptionTranslation();
            if (transition == null) {
                transition = new PointF();
            }
            float rotation = caption.getRotationZ();
            setFloatParam(CAPTION_SCALE_X, scaleX);
            setFloatParam(CAPTION_SCALE_Y, scaleY);
            setFloatParam(CAPTION_TRANS_X, transition.x);
            setFloatParam(CAPTION_TRANS_Y, transition.y);
            setFloatParam(CAPTION_ROTATION_Z, rotation);
        } else if (nvsObject instanceof NvsTimelineAnimatedSticker) {
            /*贴纸关键帧
             * Sticker key frame
             * */
            setObject((NvsTimelineAnimatedSticker) nvsObject);
            NvsTimelineAnimatedSticker sticker = (NvsTimelineAnimatedSticker) nvsObject;
            sticker.setCurrentKeyFrameTime(atTime);
            PointF translation = sticker.getTranslation();
            if (translation == null) {
                translation = new PointF();
            }
            setFloatParam(STICKER_SCALE, sticker.getScale());
            setFloatParam(STICKER_TRANS_X, translation.x);
            setFloatParam(STICKER_TRANS_Y, translation.y);
            setFloatParam(STICKER_ROTATION_Z, sticker.getRotationZ());
        } else if (nvsObject instanceof NvsVideoFx) {
            /*视频关键帧
            * Video key frame
            * */
            setObject((NvsVideoFx) nvsObject);
            NvsVideoFx object = (NvsVideoFx) nvsObject;
            setFloatParam(SCALE_X, (float) object.getFloatValAtTime(SCALE_X, atTime + offsetTime));
            setFloatParam(SCALE_Y, (float) object.getFloatValAtTime(SCALE_Y, atTime + offsetTime));
            setFloatParam(TRANS_X, (float) object.getFloatValAtTime(TRANS_X, atTime + offsetTime));
            setFloatParam(TRANS_Y, (float) object.getFloatValAtTime(TRANS_Y, atTime + offsetTime));
            setFloatParam(ROTATION, (float) object.getFloatValAtTime(ROTATION, atTime + offsetTime));
            String videoFxName = object.getBuiltinVideoFxName();
            if (NvsConstants.KEY_MASK_GENERATOR.equals(videoFxName)) {
                NvsMaskRegionInfo arbDataValAtTime = (NvsMaskRegionInfo) object.getArbDataValAtTime(NvsConstants.KEY_MASK_REGION_INFO, new NvsMaskRegionInfo(), atTime + offsetTime);
                if (arbDataValAtTime != null) {
                    MeicamMaskRegionInfo meicamMaskRegionInfo = new MeicamMaskRegionInfo();
                    meicamMaskRegionInfo.setRegionInfo(arbDataValAtTime);
                    setArbDataVal(NvsConstants.KEY_MASK_REGION_INFO, meicamMaskRegionInfo);
                }
                setFloatParam(NvsConstants.KEY_MASK_FEATHER_WIDTH, (float) object.getFloatValAtTime(NvsConstants.KEY_MASK_FEATHER_WIDTH, atTime + offsetTime));
                setBooleanParam(NvsConstants.KEY_MASK_INVERSE_REGION, object.getBooleanValAtTime(NvsConstants.KEY_MASK_INVERSE_REGION, atTime + offsetTime));
                setBooleanParam(NvsConstants.KEY_MASK_KEEP_RGB, object.getBooleanValAtTime(NvsConstants.KEY_MASK_KEEP_RGB, atTime + offsetTime));
            } else if (NvsConstants.PROPERTY_FX.equals(videoFxName)) {
                NvsMaskRegionInfo arbDataValAtTime = (NvsMaskRegionInfo) object.getArbDataValAtTime(NvsConstants.KEY_PROPERTY_MASK_REGION_INFO, new NvsMaskRegionInfo(), atTime + offsetTime);
                if (arbDataValAtTime != null) {
                    MeicamMaskRegionInfo meicamMaskRegionInfo = new MeicamMaskRegionInfo();
                    meicamMaskRegionInfo.setRegionInfo(arbDataValAtTime);
                    setArbDataVal(NvsConstants.KEY_PROPERTY_MASK_REGION_INFO, meicamMaskRegionInfo);
                }
                setFloatParam(NvsConstants.KEY_PROPERTY_MASK_FEATHER_WIDTH, (float) object.getFloatValAtTime(NvsConstants.KEY_PROPERTY_MASK_FEATHER_WIDTH, atTime + offsetTime));
                setBooleanParam(NvsConstants.KEY_PROPERTY_MASK_INVERSE_REGION, object.getBooleanValAtTime(NvsConstants.KEY_PROPERTY_MASK_INVERSE_REGION, atTime + offsetTime));
            }
        }
    }

    public void updateControlPoint() {
        NvsFx object = getObject();
        String[] fxKeys = null;
        long realAtTime = getAtTime() + getOffsetTime();
        if (object instanceof NvsTimelineCaption) {
            fxKeys = new String[]{CAPTION_SCALE_X, CAPTION_SCALE_Y, CAPTION_TRANS_X, CAPTION_TRANS_Y, CAPTION_ROTATION_Z};
        } else if (object instanceof NvsTimelineAnimatedSticker) {
            fxKeys = new String[]{STICKER_SCALE, STICKER_TRANS_X, STICKER_TRANS_Y, STICKER_ROTATION_Z};
        } else if (object instanceof NvsVideoFx) {
            fxKeys = new String[]{SCALE_X, SCALE_Y, TRANS_X, TRANS_Y, ROTATION};
        }
        if (fxKeys == null) {
            return;
        }
        MeicamKeyframeControlPoints controlPoints = getControlPoints();
        float[] valuePair = new float[2];
        if (mNext != null) {
            NvsControlPointPair frameControlPoint = findFrameControlPoint(mNext, this, realAtTime, fxKeys, valuePair);
            if (frameControlPoint != null) {
                NvsPointD forwardControlPoint = frameControlPoint.forwardControlPoint;
                long nextAtTime = mNext.getAtTime() + mNext.getOffsetTime();
                float pointX = (float) linearScale_y_x(nextAtTime,
                        realAtTime, (float) forwardControlPoint.x);
                float pointY = (float) linearScale_y_x(valuePair[1],
                        valuePair[0], (float) forwardControlPoint.y);
                controlPoints.setForwardControlPoint(new FloatPoint(pointX, pointY));
            }
        }
        if (mFont != null) {
            NvsControlPointPair frameControlPoint = findFrameControlPoint(this, mFont, realAtTime, fxKeys, valuePair);
            if (frameControlPoint != null) {
                NvsPointD backwardControlPoint = frameControlPoint.backwardControlPoint;
                long fontAtTime = mFont.getAtTime() + mFont.getOffsetTime();
                float pointX = (float) linearScale_y_x(realAtTime,
                        fontAtTime, (float) backwardControlPoint.x);
                float pointY = (float) linearScale_y_x(valuePair[1],
                        valuePair[0], (float) backwardControlPoint.y);
                controlPoints.setBackwardControlPoint(new FloatPoint(pointX, pointY));
            }
        }
    }

    /**
     * Find frame control point nvs control point pair.
     * 查找控制点，该方法会返回多个嘻嘻
     *
     * @param next       the next 下个点
     * @param font       the font 前点
     * @param realAtTime the real at time 当前点的时间戳
     * @param fxKeys     the fx keys 特效字段
     * @param floats     the floats 特效value对
     * @return the nvs control point pair 控制点
     */
    private NvsControlPointPair findFrameControlPoint(MeicamKeyFrame next,
                                                      MeicamKeyFrame font,
                                                      long realAtTime, String[] fxKeys, float[] floats) {
        NvsFx object = getObject();
        if (object == null) {
            return null;
        }
        MeicamFxParam<?> nextFxParam;
        MeicamFxParam<?> fxParam;
        for (String fxKey : fxKeys) {
            nextFxParam = next.getFxParam(fxKey);
            fxParam = font.getFxParam(fxKey);
            if (nextFxParam != null && fxParam != null) {
                floats[1] = nextFxParam.getFloatValue();
                floats[0] = fxParam.getFloatValue();
                if (!isFloatEqual(floats[1], floats[0])) {
                    NvsControlPointPair frameControlPoint = object.getKeyFrameControlPoint(fxKey, realAtTime);
                    if (frameControlPoint != null) {
                        return frameControlPoint;
                    }
                }
            }
        }
        return null;
    }

    private boolean isFloatEqual(float a, float b) {
        return Math.abs(a - b) <= 1e-6;
    }


    public void setCurrentKeyFrameTime(long atTime) {
        NvsFx object = getObject();
        if (object instanceof NvsTimelineCaption) {
            ((NvsTimelineCaption) object).setCurrentKeyFrameTime(atTime);
        } else if (object instanceof NvsTimelineAnimatedSticker) {
            ((NvsTimelineAnimatedSticker) object).setCurrentKeyFrameTime(atTime);
        }
    }
}
