package com.meishe.myvideo.fragment;

import android.view.View;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.constants.Constants;
import com.meishe.base.model.BaseFragment;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.SizeUtils;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamTimelineVideoFxClip;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.interfaces.BottomEventListener;
import com.meishe.myvideo.view.MYSeekBarTextView;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;

import java.util.ArrayList;
import java.util.List;

import static com.meishe.engine.constant.NvsConstants.BLURNAME;
import static com.meishe.engine.constant.NvsConstants.KEY_UNIT_SIZE;
import static com.meishe.engine.constant.NvsConstants.MOSAICNAME;


/**
 * The type Water effect fragment.
 * 水印效果fragment
 * 水印并列的特效 包含：模糊和马赛克
 */
public class WaterEffectFragment extends BaseFragment {

    private MYSeekBarTextView mSbMosaicLevel, mSbMosaicNum, mSbBlurLevel;
    private LinearLayout mLlBlurContainer;
    private WaterEffectAdapter mAdapter;
    private BottomEventListener mEventListener;
    private View mLlMosaicLevelContainer, mLlMosaicNumContainer;
    private View mIvConfirmView;

    public WaterEffectFragment() {
    }

    public static WaterEffectFragment getInstance(BottomEventListener listener) {
        WaterEffectFragment fragment = new WaterEffectFragment();
        fragment.mEventListener = listener;
        return fragment;
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_water_effect;
    }

    @Override
    protected void onLazyLoad() {
    }

    @Override
    protected void initView(View rootView) {
        RecyclerView rvWaterEffectList = rootView.findViewById(R.id.recyclerView);
        mSbMosaicLevel = rootView.findViewById(R.id.sb_mosaic_level);
        mSbMosaicNum = rootView.findViewById(R.id.sb_mosaic_number);
        mSbBlurLevel = rootView.findViewById(R.id.sb_blur_level);
        mLlBlurContainer = rootView.findViewById(R.id.ll_blur);
        mIvConfirmView = rootView.findViewById(R.id.iv_confirm);
        TextView tvContent = rootView.findViewById(R.id.tv_content);
        tvContent.setText(R.string.fragment_menu_water_effect);
        mLlMosaicLevelContainer = rootView.findViewById(R.id.ll_mosaic_level_layout);
        mLlMosaicNumContainer = rootView.findViewById(R.id.ll_mosaic_num_layout);
        LinearLayoutManager layoutManager = new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false);
        rvWaterEffectList.setLayoutManager(layoutManager);
        mAdapter = new WaterEffectAdapter();
        rvWaterEffectList.setAdapter(mAdapter);
        rvWaterEffectList.addItemDecoration(new ItemDecoration(SizeUtils.dp2px(5), SizeUtils.dp2px(12), SizeUtils.dp2px(5), 0));
        initListener();
    }

    private void initListener() {
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                mSbBlurLevel.setProgress(100);
                mSbMosaicNum.setProgress(100);
                mSbMosaicLevel.setProgress(100);
                mAdapter.selected(position);
                if (position == 0) {
                    closeAllSeekBar();
                    MeicamTimeline currentTimeline = EditorEngine.getInstance().getCurrentTimeline();
                    currentTimeline.removeTimelineFxFromClipList(0);
                } else if (position == 1) {
                    switchToMosaicViews();
                } else {
                    switchToBlurViews();
                }
                if (mEventListener != null) {
                    mEventListener.onItemClick(mAdapter.getItem(position), false);
                }
            }
        });
        mSbMosaicLevel.setOnSeekBarChangeListener(new MYSeekBarTextView.OnSeekBarListener() {
            @Override
            public void onStopTrackingTouch(int progress, String name) {
                if (mEventListener != null) {
                    mEventListener.onStopTrackingTouch(0);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (mEventListener != null) {
                    mEventListener.onProgressChanged(progress, fromUser, Constants.MOSAIC_DEGREE);
                }
            }
        });
        mSbMosaicNum.setOnSeekBarChangeListener(new MYSeekBarTextView.OnSeekBarListener() {
            @Override
            public void onStopTrackingTouch(int progress, String name) {
                if (mEventListener != null) {
                    mEventListener.onStopTrackingTouch(0);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (mEventListener != null) {
                    mEventListener.onProgressChanged(progress, fromUser, Constants.MOSAIC_NUM);
                }
            }
        });
        mSbBlurLevel.setOnSeekBarChangeListener(new MYSeekBarTextView.OnSeekBarListener() {
            @Override
            public void onStopTrackingTouch(int progress, String name) {
                if (mEventListener != null) {
                    mEventListener.onStopTrackingTouch(0);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (mEventListener != null) {
                    mEventListener.onProgressChanged(progress, fromUser, Constants.BLUR);
                }
            }
        });
        mIvConfirmView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mEventListener != null) {
                    mEventListener.onDismiss(true);
                }
            }
        });
    }

    @Override
    protected void initData() {
        List<IBaseInfo> waterMarkEffectList = new ArrayList<>();
        IBaseInfo waterMarkEffectInfo = new BaseInfo();
        waterMarkEffectInfo.setType(Constants.NONE);
        waterMarkEffectInfo.setName(getString(R.string.top_menu_no));
        waterMarkEffectInfo.setCoverId(R.mipmap.ic_water_mark_no);
        waterMarkEffectList.add(waterMarkEffectInfo);
        waterMarkEffectInfo = new BaseInfo();
        waterMarkEffectInfo.setType(Constants.MOSAIC);
        waterMarkEffectInfo.setName(getString(R.string.effect_mosaic));
        waterMarkEffectInfo.setCoverId(R.mipmap.ic_mosaic);
        waterMarkEffectList.add(waterMarkEffectInfo);
        waterMarkEffectInfo = new BaseInfo();
        waterMarkEffectInfo.setType(Constants.BLUR);
        waterMarkEffectInfo.setName(getString(R.string.effect_blur));
        waterMarkEffectInfo.setCoverId(R.mipmap.ic_blur);
        waterMarkEffectList.add(waterMarkEffectInfo);

        mAdapter.setNewData(waterMarkEffectList);
        updateSelected();
    }


    /**
     * Cancel selected item.
     * 取消选中项
     */
    public void unSelected() {
        if (mAdapter != null) {
            mAdapter.selected(0);
        }
        closeAllSeekBar();
    }

    /**
     * Update selected.
     * 更新选中
     */
    public void updateSelected() {
        if (mSbBlurLevel == null || mAdapter == null) {
            return;
        }
        MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
        if (timeline == null) {
            LogUtils.e("timeline is null");
            return;
        }
        MeicamTimelineVideoFxClip meicamTimelineVideoFxClip = timeline.getTimelineFxFromClipList(0);
        if (meicamTimelineVideoFxClip != null) {

            if (meicamTimelineVideoFxClip.getDesc().equals(BLURNAME)) {
                switchToBlurViews();
                float floatVal = meicamTimelineVideoFxClip.getFloatVal(NvsConstants.KEY_UNIT_RADIUS, 0.1F);
                mSbBlurLevel.setProgress((int) (floatVal / 0.64f));
                mSbMosaicNum.setProgress(100);
                mSbMosaicLevel.setProgress(100);
                mAdapter.selected(2);
            } else if (meicamTimelineVideoFxClip.getDesc().equals(MOSAICNAME)) {
                switchToMosaicViews();
                float floatVal = meicamTimelineVideoFxClip.getFloatVal(KEY_UNIT_SIZE, 0.1F);
                mSbMosaicNum.setProgress((int) (floatVal * 1000));
                mSbBlurLevel.setProgress(100);
                mSbMosaicLevel.setProgress((int) (meicamTimelineVideoFxClip.getIntensity() * 100));
                mAdapter.selected(1);
            }
        } else {
            mAdapter.selected(0);
            mSbBlurLevel.setProgress(100);
            mSbMosaicNum.setProgress(100);
            mSbMosaicLevel.setProgress(100);
            closeAllSeekBar();
        }
    }


    private void switchToMosaicViews() {
        try {
            mLlMosaicNumContainer.setVisibility(View.VISIBLE);
            mLlMosaicLevelContainer.setVisibility(View.VISIBLE);
            mLlBlurContainer.setVisibility(View.GONE);
        } catch (Exception ignore) {
        }
    }

    private void switchToBlurViews() {
        try {
            mLlBlurContainer.setVisibility(View.VISIBLE);
            mLlMosaicNumContainer.setVisibility(View.GONE);
            mLlMosaicLevelContainer.setVisibility(View.INVISIBLE);
        } catch (Exception ignore) {
        }
    }

    private void closeAllSeekBar() {
        try {
            mLlBlurContainer.setVisibility(View.GONE);
            mLlMosaicNumContainer.setVisibility(View.GONE);
            mLlMosaicLevelContainer.setVisibility(View.GONE);
        } catch (Exception ignore) {

        }
    }

    private void showOrHideMosaicViews(boolean visibility) {
        if (visibility) {
            mLlMosaicNumContainer.setVisibility(View.VISIBLE);
        } else {
            mLlMosaicNumContainer.setVisibility(View.GONE);
        }
    }

    private static class WaterEffectAdapter extends BaseQuickAdapter<IBaseInfo, BaseViewHolder> {
        private int mSelectedPosition = -1;

        private WaterEffectAdapter() {
            super(R.layout.water_mark_effect_item);
        }

        /**
         * 选中某一项
         * Selected item .
         *
         * @param position The index of list
         */
        public void selected(int position) {
            if (mSelectedPosition >= 0) {
                notifyItemChanged(mSelectedPosition);
            }
            mSelectedPosition = position;
            if (position >= 0 && position < getData().size()) {
                notifyItemChanged(position);
            }
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, IBaseInfo item) {
            helper.setImageResource(R.id.iv_cover, item.getCoverId());
            helper.setText(R.id.tv_name, item.getName());
            View vMask = helper.getView(R.id.v_mask);
            if (helper.getAdapterPosition() == mSelectedPosition) {
                vMask.setBackground(CommonUtils.getRadiusDrawable(SizeUtils.dp2px(2), mContext.getResources().getColor(R.color.color_fffc2b55), SizeUtils.dp2px(4), -1));
            } else {
                vMask.setBackgroundResource(0);
            }
        }
    }
}
