package com.meishe.capturemodule.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.meishe.base.utils.SizeUtils;
import com.meishe.base.utils.Utils;
import com.meishe.capturemodule.R;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2023/4/25 18:26
 * @Description : 拍摄音乐控制视图 The capture music control view
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CaptureMusicControlView extends LinearLayout {
   private TextView mSelectMusicTextView;
   private View mDeleteMusicView;
   private OnEventChangedListener mOnEventChangedListener;
   private View mRootView;
   private int maxTextViewSize;
   private int minTextViewSize;

   /**
    * Sets on event changed listener.
    *
    * @param listener the listener
    */
   public void setOnEventChangedListener(OnEventChangedListener listener) {
      this.mOnEventChangedListener = listener;
   }

   /**
    * Instantiates a new Capture music control view.
    *
    * @param context the context
    */
   public CaptureMusicControlView(Context context) {
      this(context, null);
   }

   /**
    * Instantiates a new Capture music control view.
    *
    * @param context the context
    * @param attrs   the attrs
    */
   public CaptureMusicControlView(Context context, @Nullable AttributeSet attrs) {
      this(context, attrs, 0);
   }

   /**
    * Instantiates a new Capture music control view.
    *
    * @param context      the context
    * @param attrs        the attrs
    * @param defStyleAttr the def style attr
    */
   public CaptureMusicControlView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
      super(context, attrs, defStyleAttr);
      initView(context);
   }

   private void initView(Context context) {
      mRootView = LayoutInflater.from(context).inflate(R.layout.capture_view_music_controller, this);
      mSelectMusicTextView = mRootView.findViewById(R.id.tv_select_music);
      mDeleteMusicView = mRootView.findViewById(R.id.iv_delete_music);
      mRootView.setOnClickListener(new OnClickListener() {
         @Override
         public void onClick(View v) {
            if (Utils.isFastClick()) {
               return;
            }
            if (mOnEventChangedListener != null) {
               mOnEventChangedListener.onSelectClicked();
            }
         }
      });
      mDeleteMusicView.setOnClickListener(new OnClickListener() {
         @Override
         public void onClick(View v) {
            if (Utils.isFastClick()) {
               return;
            }
            if (mOnEventChangedListener != null
                    && mOnEventChangedListener.onDeleteClicked()) {   mDeleteMusicView.setVisibility(View.GONE);
               mSelectMusicTextView.setText(context.getResources().getText(R.string.capture_hint_select_music));
               ViewGroup.LayoutParams layoutParams = mSelectMusicTextView.getLayoutParams();
               layoutParams.width = ViewGroup.LayoutParams.WRAP_CONTENT;
               mSelectMusicTextView.setLayoutParams(layoutParams);

            }

         }
      });

      maxTextViewSize = SizeUtils.dp2px(138);
      minTextViewSize = SizeUtils.dp2px(44);
   }

   /**
    * Set text.
    *
    * @param text the text
    */
   public void setText(String text){
      mSelectMusicTextView.setText(text);
      ViewGroup.LayoutParams layoutParams = mSelectMusicTextView.getLayoutParams();
      layoutParams.width = ViewGroup.LayoutParams.WRAP_CONTENT;
      mDeleteMusicView.setVisibility(View.VISIBLE);
      mSelectMusicTextView.setLayoutParams(layoutParams);
      mSelectMusicTextView.post(() -> {
         int width = mSelectMusicTextView.getWidth();
         ViewGroup.LayoutParams layoutParams1 = mSelectMusicTextView.getLayoutParams();
         if (width > maxTextViewSize) {
            layoutParams1.width = maxTextViewSize;
            mSelectMusicTextView.setLayoutParams(layoutParams1);
         } else if (width < minTextViewSize) {
            layoutParams1.width = minTextViewSize;
            mSelectMusicTextView.setLayoutParams(layoutParams1);
         }
      });
   }

   /**
    * Set select able.
    *
    * @param canSelect the can select
    */
   public void setSelectAble(boolean canSelect){
      mRootView.setClickable(canSelect);
   }

   /**
    * The interface On event changed listener.
    */
   public interface OnEventChangedListener{
      /**
       * On select clicked.
       *
       */
      void onSelectClicked();


      /**
       * On delete clicked boolean.
       *
       * @return the boolean
       */
      boolean onDeleteClicked();
   }
}
