package com.meishe.user.view;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.meishe.third.pop.XPopup;
import com.meishe.third.pop.core.BottomPopupView;
import com.meishe.user.R;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/03/10 17:33
 * @Description :管理云草稿的弹窗 Manage the cloud dialog for drafts
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class ManageCloudDraftPop extends BottomPopupView implements View.OnClickListener {
    private ManageListener mListener;
    private boolean mNeedUpload = true;

    public static ManageCloudDraftPop create(Context context,boolean needUpload, ManageListener listener) {
        ManageCloudDraftPop manageCloudDraftPop = (ManageCloudDraftPop) new XPopup.Builder(context)
                .asCustom(new ManageCloudDraftPop(context)
                        .setNeedUpload(needUpload)
                        .setManageListener(listener));
        return manageCloudDraftPop;

    }

    public ManageCloudDraftPop(@NonNull Context context) {
        super(context);
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_manage_draft_cloud;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        findViewById(R.id.tv_delete).setOnClickListener(this);
        TextView uploadTextView = findViewById(R.id.tv_download);
        ImageView uploadImage = findViewById(R.id.iv_download);
        uploadTextView.setOnClickListener(this);
        if (mNeedUpload) {
            uploadTextView.setTextColor(getResources().getColor(R.color.white_8));
            uploadImage.setImageResource(R.mipmap.ic_draft_download);
        } else {
            uploadTextView.setTextColor(getResources().getColor(R.color.white_5));
            uploadImage.setImageResource(R.mipmap.ic_draft_download_unselected);
        }
    }

    /**
     * 设置事件监听
     * Set event listener
     *
     * @param listener ManageListener the listener
     */
    public ManageCloudDraftPop setManageListener(ManageListener listener) {
        mListener = listener;
        return this;
    }

    public ManageCloudDraftPop setNeedUpload(boolean needUpload){
        mNeedUpload = needUpload;
        return this;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.tv_delete) {
            if (mListener != null) {
                mListener.onDelete();
            }
            dismiss();
        } else if (id == R.id.tv_download) {
            if (mListener != null && mNeedUpload) {
                mListener.onUpload();
            }
            dismiss();
        }
    }

    public interface ManageListener {

        /**
         * 删除
         * Delete
         */
        void onDelete();

        /**
         * 上传
         * On upload.
         */
        void onUpload();
    }
}
