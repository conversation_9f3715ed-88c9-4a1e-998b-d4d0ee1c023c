package com.meishe.engine;

import android.net.Uri;
import android.text.TextUtils;

import com.meicam.sdk.NvsRational;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsVideoResolution;
import com.meishe.base.utils.AndroidVersionUtils;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.MediaUtils;
import com.meishe.base.utils.Utils;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.engine.observer.EngineCallbackObserver;
import com.meishe.engine.util.PathUtils;

import java.util.Hashtable;

/**
 * 时间线合成工具类
 */
public class TimelineCompiler {
   private static final String TAG = "TimelineCompiler";
   private NvsStreamingContext mStreamingContext;
   private MeicamTimeline mTimeline;
   private String mVideoSavePath;
   //生成视频配置的帧率
   private Hashtable<String, Object> mParamsTable;
   //分辨率
   private int mCompileResolutionNum = 720;
   /**
    * The resolution coefficient of 720p is the benchmark of 1.0
    * 分辨率系数 720p为基准1.0
    */
   private float mBaseResolution = 1.0f;
   /**
    * The frame rate coefficient corresponds to 30 frames as a 1.0 reference
    * 帧率系数对应 30帧作为1.0基准
    * <p>
    * 实际帧率并不会影响到视频文件的大小
    */
   private float mBaseFrameRate = 1.0f;

   private boolean isCompile = false;
   private EngineCallbackObserver mCallbackObserver;

   private CompileListener mCompileListener;


   public void setCompileListener(CompileListener compileListener) {
      this.mCompileListener = compileListener;
   }

   public void onDestroy() {
      if (mCallbackObserver != null) {
         EngineCallbackManager.get().unregisterCallbackObserver(mCallbackObserver);
         if (isCompile) {
            stopCompileVideo();
         }
      }
   }



   /**
    * 初始化时间线
    * init Timeline
    */
   public TimelineCompiler initTimeline() {
      mStreamingContext = EditorEngine.getInstance().getStreamingContext();
      MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
      String coverImagePath = timeline.getCoverImagePath();
      if (!TextUtils.isEmpty(coverImagePath)) {
         mTimeline = EditorEngine.getInstance().createSingleClipTimelineExt(timeline.getVideoResolution());
         MeicamVideoTrack videoTrack = mTimeline.appendVideoTrack();
         if (videoTrack != null) {
            NvsRational videoFps = timeline.getVideoFps();
            videoTrack.appendVideoClip(coverImagePath, 0, (long) (1000000F / videoFps.num));
            videoTrack.addTimelineClip(timeline);
         }
      } else {
         mTimeline = timeline;
      }
      mParamsTable = new Hashtable<>(2);
      EngineCallbackManager.get().registerCallbackObserver(mCallbackObserver = new EngineCallbackObserver() {
         @Override
         public boolean isActive() {
            return true;
         }

         @Override
         public void onCompileProgress(NvsTimeline nvsTimeline, int i) {
            if (mCompileListener != null) {
               mCompileListener.onCompileProgress(i);
            }
         }

         @Override
         public void onCompileFinished(NvsTimeline nvsTimeline) {
         }

         @Override
         public void onCompileFailed(NvsTimeline nvsTimeline) {
            deleteCompileFailedData();
         }

         @Override
         public void onCompileCompleted(NvsTimeline nvsTimeline, boolean b) {
            if (!b) {
               //jumpToCompile();
               mStreamingContext.setCompileConfigurations(null);
               PathUtils.scanInnerFile(mVideoSavePath, nvsTimeline.getDuration(), new PathUtils.OnScanCallBack() {
                  @Override
                  public void onSuccess(String filePath) {
                     if (mCompileListener != null) {
                        mCompileListener.onCompileEnd(true, filePath);
                     }
                  }

                  @Override
                  public void onFail() {
                     if (mCompileListener != null) {
                        mCompileListener.onCompileEnd(false, null);
                     }
                  }
               });
               //路径置空，防止，在点击返回键等操作的时候删除文件
               mVideoSavePath = null;
            } else {
               deleteCompileFailedData();
               if (mCompileListener != null) {
                  mCompileListener.onCompileCancel( );
               }
            }
            isCompile = false;
         }
      });
      return this;
   }


   /**
    * 导出视频
    * compile Video
    */
   public void compileVideo() {
      if (mTimeline == null) {
         return;
      }
      compileVideo(0, mTimeline.getDuration());
   }

   /**
    * 导出视频
    * compile Video
    */
   public void compileVideo(long startPoint, long endPoint) {
      if (mTimeline == null) {
         return;
      }
      mVideoSavePath = PathUtils.getVideoSavePathNew_Q(PathUtils.getVideoSaveName(), mTimeline.getDuration());
      if (TextUtils.isEmpty(mVideoSavePath)) {
         LogUtils.e("Video save path is null!");
         return;
      }
      NvsVideoResolution videoRes = mTimeline.getVideoResolution();
      int imageWidth = videoRes.imageWidth;
      int imageHeight = videoRes.imageHeight;
      int customHeight = EditorEngine.getInstance().getCustomHeight(mCompileResolutionNum, mTimeline.getMakeRatio());
      LogUtils.d("timeline Width=" + imageWidth + ", height = " + imageHeight + ", customHeight = " + customHeight);
      boolean isCompiling = EditorEngine.getInstance().compileTimeline(mTimeline, startPoint, endPoint,
              mVideoSavePath, NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_CUSTOM, customHeight,
              NvsStreamingContext.COMPILE_BITRATE_GRADE_HIGH,
              NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_BUDDY_HOST_VIDEO_FRAME
                      | NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_DISABLE_ALIGN_VIDEO_SIZE
                      | NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_IGNORE_TIMELINE_VIDEO_SIZE, mParamsTable);
      if (isCompiling) {
         if (mCompileListener != null) {
            mCompileListener.onCompileStart();
         }
         isCompile = true;
      }
   }


   /**
    * 停止视频导出，并关闭
    * Stop the video export and close
    */
   public void stopCompileVideo() {
      int state = mStreamingContext.getStreamingEngineState();
      if (state == NvsStreamingContext.STREAMING_ENGINE_STATE_COMPILE) {
         FileUtils.delete(mVideoSavePath);
         mStreamingContext.stop();
         mStreamingContext.setCompileConfigurations(null);
      }
   }

   /**
    * 删除合成失败的数据
    * Delete the compile failed data
    */
   private void deleteCompileFailedData() {
      if (AndroidVersionUtils.isAboveAndroid_Q()) {
         if (!TextUtils.isEmpty(mVideoSavePath)) {
            MediaUtils.deleteVideoRecord_Q(Utils.getApp().getApplicationContext(), Uri.parse(mVideoSavePath));
         }
      } else {
         FileUtils.delete(mVideoSavePath);
      }
   }

   public interface CompileListener {
      /**
       * 导出编译开始
       * Export compilation start
       */
      void onCompileStart();

      /**
       * 导出编译中
       * Export under compilation
       *
       * @param progress the compile progress
       */
      void onCompileProgress(int progress);

      /**
       * 导出编译结束
       * Export compilation end
       *
       * @param complete true is success,false not
       * @param filePath the  filePath
       */
      void onCompileEnd(boolean complete, String filePath);

      /**
       * 导出取消
       */
      void onCompileCancel();

      /**
       * 是否是活动的
       * Is active or not
       *
       * @return true:yes; false:no
       */
      boolean isActive();
   }
}
