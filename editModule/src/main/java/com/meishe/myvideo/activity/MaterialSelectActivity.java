package com.meishe.myvideo.activity;

import static com.meishe.logic.constant.PagerConstants.BUNDLE_DATA;
import static com.meishe.logic.constant.PagerConstants.FROM_DRAFT_EDIT;
import static com.meishe.logic.constant.PagerConstants.FROM_MATERIAL_SELECTED;
import static com.meishe.logic.constant.PagerConstants.FROM_PAGE;
import static com.meishe.logic.constant.PagerConstants.MEDIA_DATA;
import static com.meishe.logic.constant.PagerConstants.MEDIA_MAX_NUM;
import static com.meishe.logic.constant.PagerConstants.MEDIA_REQUEST_CODE_PREVIEW;
import static com.meishe.logic.constant.PagerConstants.MEDIA_TAG;
import static com.meishe.logic.constant.PagerConstants.SELECTED_TYPE;
import static com.meishe.logic.constant.PagerConstants.TYPE_ADD_SOME;
import static com.meishe.logic.constant.PagerConstants.TYPE_DEFAULT;
import static com.meishe.logic.constant.PagerConstants.TYPE_ONE_FINISH;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.deep.foresight.libspi.IDataChangeListener;
import com.meishe.base.adapter.CommonFragmentAdapter;
import com.meishe.base.bean.MediaData;
import com.meishe.base.bean.MediaTag;
import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseActivity;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;
import com.meishe.engine.util.ConvertFileManager;
import com.meishe.engine.util.WhiteList;
import com.meishe.engine.view.ConvertProgressPop;
import com.meishe.myvideo.R;
import com.meishe.myvideo.fragment.MaterialSelectFragment;
import com.meishe.myvideo.util.ConfigUtil;
import com.meishe.third.tablayout.SlidingTabLayout;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 此类为素材选择类
 * This class selects a class for the material
 */
public class MaterialSelectActivity extends BaseActivity {
    private static String TAG = "MaterialSelectActivity";
    private ImageView mIvBack;
    private TextView mTvTitle;
    private TextView mTvNext;
    private final List<Fragment> mFragmentList = new ArrayList<>(4);
    private List<String> mTabTitleList;
    private ArrayList<MediaData> mSelectedMaterialList;
    /**
     * 来自的页面，保留字段
     * From page
     */
    private int mFromPage;
    private int mSelectedType = TYPE_DEFAULT;

    @Override
    protected int bindLayout() {
        return R.layout.activity_material_select;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        Intent intent = getIntent();
        if (intent != null) {
            Bundle bundle = intent.getExtras();
            if (bundle != null) {
                mFromPage = bundle.getInt(FROM_PAGE);
                mSelectedType = bundle.getInt(SELECTED_TYPE, TYPE_DEFAULT);
            }
        }
        mSelectedMaterialList = new ArrayList<>();
        mTabTitleList = Arrays.asList(getResources().getStringArray(R.array.select_media));
        mFragmentList.clear();
        mFragmentList.add(MaterialSelectFragment.create(MediaData.TYPE_ALL, mSelectedType, true, mMediaListener));
        mFragmentList.add(MaterialSelectFragment.create(MediaData.TYPE_VIDEO, mSelectedType, true, mMediaListener));
        mFragmentList.add(MaterialSelectFragment.create(MediaData.TYPE_PHOTO, mSelectedType, true, mMediaListener));
        mFragmentList.add(MaterialSelectFragment.createWithExternalData(MediaData.TYPE_BUSINESS, mSelectedType, true, mMediaListener));
    }

    @Override
    protected void initView() {
        mIvBack = findViewById(R.id.iv_back);
        mTvTitle = findViewById(R.id.tv_title);
        SlidingTabLayout tabLayout = findViewById(R.id.tl_select_media);
        ViewPager viewPager = findViewById(R.id.vp_select_media);
        mTvNext = findViewById(R.id.tv_start_edit);

        viewPager.setOffscreenPageLimit(3);
        viewPager.setAdapter(new CommonFragmentAdapter(getSupportFragmentManager(), mFragmentList));
        tabLayout.setViewPager(viewPager, mTabTitleList);
        initListener();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        for (Fragment fragment : mFragmentList) {
            fragment.onActivityResult(requestCode, resultCode, data);
        }
    }

    private void initListener() {
        mIvBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        mTvNext.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Utils.isFastClick()) {
                    return;
                }
                if (!ConfigUtil.needConvert()) {
                    gotoNext();
                    return;
                }
                boolean needConvert = false;
                ConvertFileManager.ConvertParam convertParam = new ConvertFileManager.ConvertParam();
                for (MediaData mediaData : mSelectedMaterialList) {
                    String path = mediaData.getPath();
                    if (WhiteList.isCovert4KFileWhiteList(path)) {
                        needConvert = true;
                        convertParam.appendParam(path, "", false);
                    }
                }
                if (needConvert) {
                    convert(MaterialSelectActivity.this, convertParam, new ConvertFileManager.EventListener() {
                        @Override
                        public void onConvertFinish(ConvertFileManager.ConvertParam convertParam, boolean convertSuccess) {
                            if (convertSuccess) {
                                Map<String, ConvertFileManager.ConvertParam.Param> paramMap =
                                        convertParam.getParamMap();
                                if (paramMap != null && !paramMap.isEmpty()) {
                                    for (MediaData mediaData : mSelectedMaterialList) {
                                        ConvertFileManager.ConvertParam.Param param = paramMap.get(mediaData.getPath());
                                        if (param != null) {
                                            String dstFile = param.getDstFile();
                                            if (!TextUtils.isEmpty(dstFile)) {
                                                mediaData.setPath(dstFile);
                                            }
                                        }
                                    }
                                }
                                gotoNext();
                            } else {
                                ToastUtils.make()
                                        .setGravity(Gravity.CENTER, 0, 0)
                                        .setDurationIsLong(false)
                                        .show(R.string.convert_failed);
                            }
                        }
                    });
                } else {
                    gotoNext();
                }
            }
        });
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        overridePendingTransition(0, 0);
    }

    /**
     * Convert.
     * 转码
     *
     * @param context the context 上下文
     * @param convertParam  the convert parameter 转码参数
     * @param listener  the listener 转码回调
     */
    private void convert(Context context, ConvertFileManager.ConvertParam convertParam, ConvertFileManager.EventListener listener) {
        ConvertProgressPop.create(context, convertParam, listener).show();
    }

    private void gotoNext() {
        if (mSelectedType == TYPE_DEFAULT) {
            Intent it = new Intent(this, DraftEditActivity.class);
            it.putExtra(FROM_PAGE, FROM_MATERIAL_SELECTED);
            it.putParcelableArrayListExtra(BUNDLE_DATA, mSelectedMaterialList);
            startActivity(it);
        } else if (mSelectedType == TYPE_ONE_FINISH) {
            /*
             * 单选一个素材没有处理。
             * A single story is not processed
             * */
            Intent it = new Intent();
            it.putExtra(BUNDLE_DATA, mSelectedMaterialList.get(0));
            setResult(RESULT_OK, it);
        } else if (mSelectedType == TYPE_ADD_SOME) {
            if (mFromPage == FROM_DRAFT_EDIT) {
                Intent it = new Intent();
                it.putParcelableArrayListExtra(BUNDLE_DATA, mSelectedMaterialList);
                setResult(RESULT_OK, it);
            }
        }
        finish();
    }

    /**
     * 处理素材选中
     * deal material selected
     *
     * @param mediaData The MediaData
     */
    private void dealMaterialSelected(MediaData mediaData) {
        MediaData selectedMedia;
        if (mSelectedType == TYPE_ONE_FINISH || mSelectedMaterialList.size() == 0) {
            mSelectedMaterialList.clear();
            mSelectedMaterialList.add(selectedMedia = getMaterialWidthTag(mediaData));
            updateSelectedNumber(0, selectedMedia, false);
        } else {
            int currentType = ((MediaTag) mediaData.getTag()).getType();
            for (int i = 0; i < mSelectedMaterialList.size(); i++) {
                selectedMedia = mSelectedMaterialList.get(i);
                if (selectedMedia.getId() == mediaData.getId()) {
                    selectedMedia.setState(mediaData.isState());
                    if (!selectedMedia.isState()) {
                        MediaTag[] mediaTags = (MediaTag[]) selectedMedia.getTag();
                        MediaTag tag;
                        for (int j = 0; j < mFragmentList.size(); j++) {
                            tag = mediaTags[j];
                            if (tag.getType() == currentType) {
                                /*
                                 * 当前页面已经取消过了
                                 * The current page has been cancelled
                                 * */
                                continue;
                            }
                            /*
                             * 通知其他子页面，取消选中
                             * Notify other subpages to deselect them
                             * */
                            ((MaterialSelectFragment) mFragmentList.get(j)).dealSelectedState(tag.getIndex(), false);
                        }
                        /*
                         * 未选中，则在选中集合中删除
                         * Unchecked, the selected collection is deleted
                         * */
                        mSelectedMaterialList.remove(i);
                        updateSelectedNumber(i, selectedMedia, true);
                        return;
                    }
                }
            }
            /*
             * 没有选中，则添加选中集合。
             * If not selected, the selected collection is added.
             * */
            mSelectedMaterialList.add(selectedMedia = getMaterialWidthTag(mediaData));
            updateSelectedNumber(mSelectedMaterialList.size() - 1, selectedMedia, false);
        }
    }

    /**
     * 更新选中的数字
     * Update the selected number
     *
     * @param startIndex         int start index 起始索引
     * @param data               The MediaData 媒体数据
     * @param useSelectedListTag true ,use the MediaTag of MediaData in the mSelectedMaterialList,
     *                           false ,use the MediaTag of data
     *                           在mSelectedMaterialList中使用MediaData的MediaTag，
     *                           false，使用数据的中介
     */
    private void updateSelectedNumber(int startIndex, MediaData data, boolean useSelectedListTag) {
        MediaTag[] mediaTags = (MediaTag[]) data.getTag();
        MediaTag tag;
        for (int i = startIndex; i < mSelectedMaterialList.size(); i++) {
            if (useSelectedListTag) {
                mediaTags = (MediaTag[]) mSelectedMaterialList.get(i).getTag();
            }
            for (int j = 0; j < mFragmentList.size(); j++) {
                tag = mediaTags[j];
                /*
                 * 更新选中索引
                 * Update selected index
                 * */
                if (tag != null) {
                    ((MaterialSelectFragment) mFragmentList.get(j)).updateSelectedNumber(tag.getIndex(), i + 1);
                }
            }
        }
    }

    /**
     * 获取带有其他子页面Tag的素材
     * Gets the material with the other sub-page tags
     *
     * @param mediaData The MediaData
     */
    private MediaData getMaterialWidthTag(MediaData mediaData) {
        MediaTag[] mediaTags = new MediaTag[mFragmentList.size()];
        MediaTag tag = (MediaTag) mediaData.getTag();
        //如果为空，肯定有问题检查之 If it is empty, there must be a problem checking it.
        //获取mediaData中的tag，并获取其他子页面的tag Gets the tags in the mediaData and the tags for the other child pages.
        if (tag.getType() == MediaData.TYPE_ALL) {
            mediaTags[0] = tag;
            mediaTags[1] = ((MaterialSelectFragment) mFragmentList.get(1)).dealSelected(mediaData.getThumbPath());
            mediaTags[2] = ((MaterialSelectFragment) mFragmentList.get(2)).dealSelected(mediaData.getThumbPath());
        } else if (tag.getType() == MediaData.TYPE_VIDEO) {
            mediaTags[1] = tag;
            mediaTags[0] = ((MaterialSelectFragment) mFragmentList.get(0)).dealSelected(mediaData.getThumbPath());
            mediaTags[2] = ((MaterialSelectFragment) mFragmentList.get(2)).dealSelected(mediaData.getThumbPath());
        } else if (tag.getType() == MediaData.TYPE_PHOTO) {
            mediaTags[2] = tag;
            mediaTags[0] = ((MaterialSelectFragment) mFragmentList.get(0)).dealSelected(mediaData.getThumbPath());
            mediaTags[1] = ((MaterialSelectFragment) mFragmentList.get(1)).dealSelected(mediaData.getThumbPath());
        } else {
            mediaTags[3] = tag;
            ((MaterialSelectFragment) mFragmentList.get(3)).dealSelected(mediaData.getThumbPath());
        }
        MediaData newData = mediaData.copy();
        newData.setTag(mediaTags);
        return newData;
    }

    private final MaterialSelectFragment.MediaChangeListener mMediaListener = new MaterialSelectFragment.MediaChangeListener() {
        @Override
        public void onMediaChange(MediaData mediaData) {
            dealMaterialSelected(mediaData);
            if (mSelectedMaterialList.size() > 0) {
                if (mTvNext.getVisibility() != View.VISIBLE) {
                    mTvNext.setVisibility(View.VISIBLE);
                }
                mTvTitle.setText(getResources().getQuantityString(R.plurals.setSelectMedia,
                        mSelectedMaterialList.size(), mSelectedMaterialList.size()));
            } else if (mTvNext.getVisibility() == View.VISIBLE) {
                mTvNext.setVisibility(View.GONE);
                mTvTitle.setText(R.string.select_media);
            }
        }

        @Override
        public void onMediaPreView(MediaData mediaData) {
            if (Utils.isFastClick()) {
                return;
            }
            if (mediaData == null) {
                LogUtils.e("mediaData is null !");
                return;
            }
            Bundle bundle = new Bundle();
            if (mSelectedType == TYPE_ONE_FINISH) {
                mediaData.setPosition(1);
                if (mediaData.isState()) {
                    bundle.putInt(MEDIA_MAX_NUM, 1);
                } else {
                    bundle.putInt(MEDIA_MAX_NUM, 0);
                }
            } else {
                bundle.putInt(MEDIA_MAX_NUM, mSelectedMaterialList == null ? 0 : mSelectedMaterialList.size());
            }
            bundle.putParcelable(MEDIA_DATA, mediaData);
            bundle.putParcelable(MEDIA_TAG, ((MediaTag) mediaData.getTag()));

            //跳转预览页面
            //Jump to preview page
            AppManager.getInstance().jumpActivityForResult(MaterialSelectActivity.this, MaterialPreviewActivity.class, bundle, MEDIA_REQUEST_CODE_PREVIEW);
        }
    };
}