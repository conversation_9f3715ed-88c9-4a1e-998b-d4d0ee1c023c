package com.meishe.business.assets.fragment;

import static com.meishe.engine.asset.bean.AssetInfo.ASSET_CAPTION_STYLE;
import static com.meishe.engine.asset.bean.AssetInfo.ASSET_CUSTOM_CAPTION_BUBBLE;
import static com.meishe.engine.asset.bean.AssetInfo.ASSET_CUSTOM_CAPTION_FLOWER;
import static com.meishe.engine.asset.bean.AssetInfo.ASSET_CUSTOM_STICKER_PACKAGE;
import static com.meishe.logic.constant.PagerConstants.BUNDLE_KEY_ASSET_SBU_TYPE;

import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.model.BaseMvpFragment;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.NetUtils;
import com.meishe.base.utils.SizeUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.view.PullToRefreshAndPushToLoadView;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.business.R;
import com.meishe.business.assets.ConfigUtil;
import com.meishe.business.assets.fragment.adapter.CommonAdapter;
import com.meishe.business.assets.iview.AssetsView;
import com.meishe.business.assets.presenter.FlowPresenter;
import com.meishe.business.assets.view.AssetsTypeTabView;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.bean.AssetsConstants;
import com.meishe.logic.constant.PagerConstants;
import com.meishe.third.adpater.BaseQuickAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * 瀑布流公共类
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: ChuChenGuang
 * @CreateDate: 2021/3/10 16:37
 * @Description: GridLayout布局的资源管理页面 Resource management page of GridLayout layout
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public abstract class FlowFragment<P extends FlowPresenter> extends BaseMvpFragment<P> implements AssetsView {
    protected int mAssetType;
    protected int mAssetTypeNew;
    private int mAssetSubType;
    protected int mCategoryId = 0;
    protected int mKind = -1;
    private RecyclerView mRvAssetList;
    protected CommonAdapter mAdapter;
    private final long duration = 500;
    private long lastClickTime = 0;
    protected boolean needSelected = true;
    protected int spanCount = 5;
    protected boolean needDisplayName = true;
    protected int leftItemDecoration = SizeUtils.dp2px(3);
    protected int topItemDecoration = SizeUtils.dp2px(12);
    private TextView mHintText;
    private PullToRefreshAndPushToLoadView mRefreshLayout;
    private String mDownloadingTag;
    private String mSelectedId;

    public FlowFragment() {
    }

    private void sortType(int assetType) {
        if (!ConfigUtil.isNewAssets()) {
            if (assetType == ASSET_CUSTOM_CAPTION_FLOWER) {
                mCategoryId = 5;
                mAssetTypeNew = ASSET_CUSTOM_CAPTION_FLOWER;
            } else if (assetType == ASSET_CUSTOM_CAPTION_BUBBLE) {
                mCategoryId = 6;
                mAssetTypeNew = ASSET_CUSTOM_CAPTION_BUBBLE;
            } else if (assetType == ASSET_CUSTOM_STICKER_PACKAGE) {
                mAssetTypeNew = ASSET_CUSTOM_STICKER_PACKAGE;
            }
        } else {
            if (assetType == ASSET_CUSTOM_CAPTION_FLOWER) {
                mAssetTypeNew = AssetsConstants.AssetsTypeData.CAPTION_FLOWER.type;
                mCategoryId = AssetsConstants.AssetsTypeData.CAPTION_FLOWER.category;
                mKind = AssetsConstants.AssetsTypeData.CAPTION_FLOWER.kind;
            } else if (assetType == ASSET_CUSTOM_CAPTION_BUBBLE) {
                mAssetTypeNew = AssetsConstants.AssetsTypeData.CAPTION_BUBBLE.type;
                mCategoryId = AssetsConstants.AssetsTypeData.CAPTION_BUBBLE.category;
                mKind = AssetsConstants.AssetsTypeData.CAPTION_BUBBLE.kind;
            } else if (mAssetType == ASSET_CUSTOM_STICKER_PACKAGE) {
                mAssetTypeNew = AssetsConstants.AssetsTypeData.STICKER_CUSTOM.type;
                mCategoryId = AssetsConstants.AssetsTypeData.STICKER_CUSTOM.category;
                mKind = AssetsConstants.AssetsTypeData.STICKER_CUSTOM.kind;
            } else if (assetType == ASSET_CAPTION_STYLE) {
                mAssetTypeNew = AssetsConstants.AssetsTypeData.CAPTION_STYLE.type;
                mCategoryId = AssetsConstants.AssetsTypeData.CAPTION_STYLE.category;
                mKind = AssetsConstants.AssetsTypeData.CAPTION_STYLE.kind;
            }
        }
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_common_list_center;
    }

    @Override
    protected void onLazyLoad() {
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putInt(BUNDLE_KEY_ASSET_SBU_TYPE, mAssetSubType);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        AssetsManager.get().clearCache();
    }

    @Override
    protected void initView(View rootView) {
        mRvAssetList = rootView.findViewById(R.id.recyclerView);
        AssetsTypeTabView mTypeTab = rootView.findViewById(R.id.ttv_tab_type1);
        mHintText = rootView.findViewById(R.id.tv_hint);
        GridLayoutManager gridLayoutManager = new GridLayoutManager(getContext(), spanCount);
        mRvAssetList.setLayoutManager(gridLayoutManager);
        mRvAssetList.setHasFixedSize(true);
        CommonAdapter adapter = getAdapter();
        mAdapter = adapter == null ? new FlowCommonAdapter() : adapter;
        mRvAssetList.setAdapter(mAdapter);
        mRvAssetList.addItemDecoration(new ItemDecoration(leftItemDecoration, topItemDecoration, leftItemDecoration, 0));
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                //这里添加一个防连击的操作 Add an anti combo action here.
                long currentTime = System.currentTimeMillis();
                if (currentTime - lastClickTime > duration) {
                    AssetInfo info = mAdapter.getItem(position);
                    if (info != null) {
                        if ((!info.isHadDownloaded() || info.needUpdate())) {
                            downloadAsset(info, position);
                        } else {
                            setSelected(position);
                            onAdapterItemClick(position);
                            mPresenter.clickAssetItem(info);
                        }
                    }
                }
                lastClickTime = currentTime;
            }
        });
        mTypeTab.setItemClickedListener(new AssetsTypeTabView.ItemClickedListener() {
            @Override
            public void onItemClicked(int position) {
                mAssetSubType = position;
                mAdapter.setAssetSubType(position);
                loadData(false);
            }
        });

        mRefreshLayout = rootView.findViewById(R.id.ptl_recyclerView);
        mRefreshLayout.setCanLoadMore(true);
        mRefreshLayout.setCanRefresh(true);
        mRefreshLayout.finishRefreshing();
        mRefreshLayout.setOnRefreshAndLoadMoreListener(new PullToRefreshAndPushToLoadView.PullToRefreshAndPushToLoadMoreListener() {
            @Override
            public void onRefresh() {
                loadData(true);
            }

            @Override
            public void onLoadMore() {
                if (!loadMoreData(false)) {
                    finishLoading();
                }
            }
        });

        if (mAssetType == ASSET_CUSTOM_STICKER_PACKAGE) {
            mTypeTab.setVisibility(View.GONE);
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            mAssetSubType = savedInstanceState.getInt(BUNDLE_KEY_ASSET_SBU_TYPE);
        }
        return super.onCreateView(inflater, container, savedInstanceState);
    }

    @Override
    protected void initData() {
        Bundle bundle = getArguments();
        if (bundle != null) {
            mAssetType = bundle.getInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE, 0);
            mAssetTypeNew = bundle.getInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE_NEW);
            mCategoryId = bundle.getInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE_CATEGORY);
            mKind = bundle.getInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE_KIND);
        }
        sortType(mAssetType);
        loadData(true);
    }

    /**
     * 下载特效资源包
     * Download the effects resource pack
     *
     * @param assetInfo asset info
     * @param position  the index of asset info int the list
     */
    private void downloadAsset(final AssetInfo assetInfo, final int position) {
        mDownloadingTag = assetInfo.getPackageId();
        assetInfo.setDownloadProgress(0);
        mAdapter.notifyItemChanged(position);
        mPresenter.downloadAsset(assetInfo, position);
    }

    private boolean loadMoreData(final boolean needUpdate) {
        return mPresenter.loadMoreData(mAssetTypeNew, mAssetSubType, mCategoryId, mKind, needUpdate);
    }

    private void loadData(final boolean needUpdate) {
        mHintText.setCompoundDrawables(null, null, null, null);
        mPresenter.setPageSize(25);
        mPresenter.loadData(mAssetTypeNew, mAssetSubType, mCategoryId, mKind, needUpdate);
    }

    @Override
    public boolean isActive() {
        return isAdded();
    }

    @Override
    public void onNewDataBack(List<AssetInfo> list, int subType, boolean needUpdate) {
        if (!CommonUtils.isEmpty(list)) {
            mAdapter.setNewData(list);
        }
        changViewState(needUpdate);
        finishRefresh();
        finishLoading();
        checkSelected();
    }

    @Override
    public void onMoreDataBack(List<AssetInfo> list, int subType, boolean needUpdate) {
        if (!CommonUtils.isEmpty(list)) {
            mAdapter.addData(list);
        }
        changViewState(needUpdate);
        finishRefresh();
        finishLoading();
        checkSelected();
        mAdapter.loadMoreComplete();
    }

    @Override
    public void onDataError(int subType, boolean needUpdate) {
        mAdapter.setNewData(new ArrayList<>());
        changViewState(needUpdate);
        finishRefresh();
        finishLoading();
    }

    @Override
    public int getItemCount() {
        return mAdapter == null ? 0 : mAdapter.getItemCount();
    }

    @Override
    public void onDownloadProgress(int position) {
        mAdapter.notifyItemChanged(position);
    }

    @Override
    public void onDownloadFinish(int position, AssetInfo assetInfo) {
        if (TextUtils.equals(mDownloadingTag, assetInfo.getPackageId())) {
            setSelected(position);
            onAdapterItemClick(position);
        } else {
            mAdapter.notifyItemChanged(position);
        }
    }

    @Override
    public void onDownloadError(int position) {
        AssetInfo item = mAdapter.getItem(position);
        if (item != null) {
            item.setHadDownloaded(true);
        }
        ToastUtils.make().setGravity(Gravity.CENTER, 0, 0).show(R.string.download_fail);
        mAdapter.notifyItemChanged(position);
    }

    private void changViewState(final boolean needUpdate) {
        if (mAdapter.getItemCount() <= 0) {
            mHintText.setVisibility(View.VISIBLE);
            mRvAssetList.setVisibility(View.GONE);
            if (!NetUtils.isNetworkAvailable(getContext())) {
                mHintText.setText(R.string.user_hint_assets_net_error_refresh);
                Drawable drawable = getResources().getDrawable(R.mipmap.ic_assets_data_update);
                drawable.setBounds(new Rect(0, 4, drawable.getIntrinsicHeight(), drawable.getIntrinsicHeight() + 4));
                mHintText.setCompoundDrawables(null, null, drawable, null);
                mHintText.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        mHintText.setCompoundDrawables(null, null, null, null);
                        loadData(needUpdate);
                    }
                });
            } else {
                mHintText.setText(AssetsManager.get().getErrorMsg(getContext(), mAssetSubType));
            }
        } else {
            mHintText.setVisibility(View.GONE);
            mRvAssetList.setVisibility(View.VISIBLE);
            if (!NetUtils.isNetworkAvailable(getContext()) && (mRefreshLayout.isRefreshing() || mRefreshLayout.isLoading())) {
                ToastUtils.showShort(getContext().getResources().getString(R.string.user_hint_assets_net_error));
            }
        }
    }

    private void finishLoading() {
        if (mRefreshLayout.isLoading()) {
            mRefreshLayout.finishLoading();
        }
    }

    private void finishRefresh() {
        if (mRefreshLayout.isRefreshing()) {
            mRefreshLayout.finishRefreshing();
        }
    }

    /**
     * 检查选中
     * Check selected item
     */
    private void checkSelected() {
        mAdapter.selected(getAssetId());
    }

    /**
     * Sets item selected.
     * 设置选中
     *
     * @param position the position
     */
    public void setSelected(int position) {
        if (mAdapter != null) {
            mAdapter.selected(position);
            setSelectedId(mAdapter.getSelectedId());
        }
    }

    /**
     * Sets item selected.
     * 设置选中
     *
     * @param uuid String the uuid
     */
    public void setSelected(String uuid) {
        if (mAdapter != null) {
            mAdapter.selected(uuid);
            setSelectedId(uuid);
        }
    }

    private void setSelectedId(String selectedId) {
        this.mSelectedId = selectedId;
    }

    private class FlowCommonAdapter extends CommonAdapter {
        private FlowCommonAdapter() {
            super(getItemLayoutResId(), needSelected, needDisplayName);
        }
    }

    protected abstract int getItemLayoutResId();

    protected abstract void onAdapterItemClick(int position);

    protected CommonAdapter getAdapter() {
        return null;
    }

    protected String getAssetId() {
        return mSelectedId;
    }

}
