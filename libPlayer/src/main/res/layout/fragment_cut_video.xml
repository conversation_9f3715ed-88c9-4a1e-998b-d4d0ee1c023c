<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_main_bg">

    <RelativeLayout
        android:layout_marginTop="2dp"
        android:layout_marginLeft="2dp"
        android:layout_marginRight="2dp"
        android:id="@+id/playerLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="40dp">
        <com.meicam.sdk.NvsLiveWindowExt
            android:id="@+id/liveWindow"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"/>
        <com.meishe.player.view.CutRectLayout
            android:id="@+id/cut_view"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true">
        </com.meishe.player.view.CutRectLayout>
        <com.meishe.player.view.TestRectView
            android:id="@+id/test"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
        <com.meishe.player.view.TestRectView
            android:id="@+id/test1"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </RelativeLayout>
    <RelativeLayout
        android:id="@+id/playBarLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/video_fragment_play_bar_height"
        android:orientation="horizontal"
        android:layout_alignParentBottom="true">
        <RelativeLayout
            android:id="@+id/playLayout"
            android:layout_width="wrap_content"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/playImage"
                android:layout_width="@dimen/video_fragment_play_button_width"
                android:layout_height="@dimen/video_fragment_play_button_height"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/video_fragment_play_button_margin_left"
                android:background="@mipmap/icon_play" />
        </RelativeLayout>

        <TextView
            android:layout_centerVertical="true"
            android:layout_toRightOf="@+id/playLayout"
            android:id="@+id/currentPlaytime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/zeroZZZ"
            android:textColor="#CCFFFFFF"
            android:textSize="@dimen/video_fragment_play_time_text_size"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="@dimen/video_fragment_current_time_text_margin_left"
            android:layout_marginRight="@dimen/video_fragment_current_time_text_margin_right"/>

        <SeekBar
            android:layout_centerVertical="true"
            android:layout_toRightOf="@+id/currentPlaytime"
            android:layout_toLeftOf="@+id/totalDuration"
            android:id="@+id/playSeekBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:progressDrawable="@drawable/play_seek_bar"
            android:maxHeight="@dimen/video_fragment_seek_bar_max_min_height"
            android:minHeight="@dimen/video_fragment_seek_bar_max_min_height"
            android:thumb="@drawable/custom_play_seekbar_ball" />
        <TextView
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:id="@+id/totalDuration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/zeroZZZ"
            android:textColor="#CCFFFFFF"
            android:textSize="@dimen/video_fragment_play_time_text_size"
            android:layout_marginLeft="@dimen/video_fragment_total_time_text_margin_left"
            android:layout_marginRight="@dimen/video_fragment_total_time_text_margin_right"/>
    </RelativeLayout>
</RelativeLayout>
