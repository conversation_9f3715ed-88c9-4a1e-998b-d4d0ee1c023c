<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:background="@drawable/toast_bg_upload"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tv_toast_success"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dp_px_45"
        android:paddingBottom="@dimen/dp_px_45"
        android:layout_marginLeft="@dimen/dp_px_75"
        android:layout_marginRight="@dimen/dp_px_75"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_42" />
</LinearLayout>