package com.meishe.engine.util;

import android.os.Build;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsSize;
import com.meicam.sdk.NvsStreamingContext;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.bean.CommonData;

import static com.meicam.sdk.NvsStreamingContext.AV_FILEINFO_EXTRA_INFO;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: Chu<PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2021/2/22 16:39
 * @Description:
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class WhiteList {
    /**
     * 设置做大 icon Reader
     * need set max icon reader white list boolean.
     *
     * @return the boolean
     */
    public static boolean isNeedSetMaxIconReaderWhiteList() {
        String currOs = Build.MODEL;
        if ("MIX 2S".equals(currOs)) {
            return true;
        }
        return false;
    }

    /**
     * 转码4k素材的白名单
     * Is covert 4 k file white list boolean.
     *
     * @param path the path
     * @return the boolean
     */
    public static boolean isCovert4KFileWhiteList(String path) {
        return checkVideoAssetIs4K(path);
    }

    /**
     * 是否在构建timeline的白名单中
     * Is in timeline white list;
     *
     * @param path the path
     * @return the boolean
     */
    public static boolean isInTimelineWhiteList(String path) {
        if (TextUtils.isEmpty(path)) {
            return false;
        }
        if (CommonData.SUPPORT_HDR) {
            return true;
        }
        return !isHDR(path);
    }


    /**
     * 是否是HDR格式的视频
     * Is it HDR video
     *
     * @param path the path
     * @return the boolean
     */
    public static boolean isHDR(@NonNull String path) {
        NvsStreamingContext instance = NvsStreamingContext.getInstance();
        if (instance == null) {
            LogUtils.e("NvsStreamingContext is null!");
            return false;
        }
        NvsAVFileInfo avFileInfo = instance.getAVFileInfo(path, AV_FILEINFO_EXTRA_INFO);
        if (avFileInfo == null) {
            return false;
        }
        return avFileInfo.getVideoStreamColorTranfer(0) != 0;
    }

    /**
     * 判断素材是否是4k
     * Check video asset is 4 k boolean.
     *
     * @param path the path
     * @return the boolean
     */
    public static boolean checkVideoAssetIs4K(String path) {
        NvsAVFileInfo avFileInfo = NvsStreamingContext.getInstance().getAVFileInfo(path);
        if (avFileInfo == null) {
            return false;
        }
        return checkVideoAssetIs4K(avFileInfo);
    }

    /**
     * 判断素材是否是4k
     * Check video asset is 4 k boolean.
     *
     * @param avFileInfo the av file info
     * @return the boolean
     */
    public static boolean checkVideoAssetIs4K(NvsAVFileInfo avFileInfo) {
        if (avFileInfo == null) {
            return false;
        }
        if (avFileInfo.getAVFileType() == NvsAVFileInfo.AV_FILE_TYPE_AUDIOVIDEO) {
            NvsSize nvsSize = avFileInfo.getVideoStreamDimension(0);
            if (nvsSize == null) {
                return false;
            }
            return nvsSize.height >= nvsSize.width ? (nvsSize.width >= 2160) : (nvsSize.height >= 2160);
        }
        return false;
    }

    /**
     * flv视频分割
     * Is flv asset split boolean.
     *
     * @param path the path
     * @return the boolean
     */
    public static boolean isFLVAssetSplit(String path) {
        String currOs = Build.MODEL;
        String fileName = FileUtils.getFileSuffix(path);
        if ("PDPM00".equals(currOs) && (!TextUtils.isEmpty(fileName)) && "FLV".equals(fileName.toUpperCase())) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否是gif图片
     * Is gif boolean.
     *
     * @param path the path
     * @return the boolean
     */
    public static boolean isGif(String path) {
        String fileName = FileUtils.getFileSuffix(path);
        if (!TextUtils.isEmpty(fileName) && "GIF".equals(fileName.toUpperCase())) {
            return true;
        }
        return false;
    }

    /**
     * 是否需要复制到图库
     *
     * @return the boolean
     */
    public static boolean isNeedCopyCompileVideo() {
        String currOs = Build.MODEL;
       /* if (AndroidVersionUtils.isAboveAndroid_Q() && "PFJM10".equals(currOs)) {
            return true;
        }*/
        return Build.VERSION.SDK_INT > Build.VERSION_CODES.Q;
    }
}
