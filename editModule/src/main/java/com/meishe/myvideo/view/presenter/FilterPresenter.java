package com.meishe.myvideo.view.presenter;

import com.meishe.base.utils.StringUtils;
import com.meishe.business.assets.presenter.AssetsPresenter;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.event.MessageEvent;
import com.meishe.myvideo.view.interf.FilterView;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;

import static com.meishe.myvideo.event.MessageEvent.MESSAGE_APPLY_ALL_FILTER;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_REMOVE_CLIP_FILTER;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_REMOVE_TIMELINE_FILTER;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_CHANGE_CLIP_FILTER_FINISH;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_CHANGE_CLIP_FILTER_PROGRESS;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_CHANGE_TIMELINE_FILTER_FINISH;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_CHANGE_TIMELINE_FILTER_PROGRESS;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_UPDATE_FILTER_PROGRESS;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_UPDATE_SELECT_POSITION;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_WIDTH_CONFIRM_EFFECT;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 滤镜 Presenter
 * Filter Presenter
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/28 15:22
 * @Description :Presenter of filter menu view
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class FilterPresenter extends AssetsPresenter<FilterView> {

    @Override
    public void attachView(FilterView filterView) {
        super.attachView(filterView);
        EventBus.getDefault().register(this);
    }

    @Override
    public void detachView() {
        super.detachView();
        EventBus.getDefault().unregister(this);
    }

    @Override
    protected List<AssetInfo> handleDataInFirstPage(List<AssetInfo> list) {
        AssetInfo effectInfo = new AssetInfo();
        effectInfo.setName(StringUtils.getString(R.string.no));
        effectInfo.setCoverId(R.mipmap.ic_no);
        effectInfo.setHadDownloaded(true);
        effectInfo.setType(AssetInfo.ASSET_FILTER);
        effectInfo.setEffectMode(BaseInfo.EFFECT_MODE_BUILTIN);
        list.add(0, effectInfo);
        return list;
    }

    public void loadData(final int subType, final boolean needForceUpdate) {
        setPageSize(10);
        loadData(AssetInfo.ASSET_FILTER, subType, 1, -1, needForceUpdate);

    }

    public boolean loadMoreData(final int subType, final boolean needForceUpdate) {
        return loadMoreData(AssetInfo.ASSET_FILTER, subType, 1, -1, needForceUpdate);
    }


    /**
     * On message event.
     * 消息事件
     * @param event the event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(MessageEvent event) {
        int eventType = event.getEventType();
        if (eventType == MESSAGE_TYPE_UPDATE_FILTER_PROGRESS) {
            float floatValue = event.getFloatValue();
            getView().setProgress(floatValue);
        } else if (eventType == MESSAGE_TYPE_UPDATE_SELECT_POSITION) {
            int intValue = event.getIntValue();
            getView().updateSelectPosition(intValue);
        }
    }


    public void onItemClicked(IBaseInfo info) {
        if (StringUtils.getString(R.string.top_menu_no).equals(info.getName())) {
            if (getView().needShowApplyAll()) {
                MessageEvent.sendEvent(info, MESSAGE_REMOVE_CLIP_FILTER);
            } else {
                MessageEvent.sendEvent(info, MESSAGE_REMOVE_TIMELINE_FILTER);
            }
        } else {
            MessageEvent.sendEvent(info, MESSAGE_TYPE_WIDTH_CONFIRM_EFFECT);
        }
    }


    public void onProgressChanged(float progress, String tag, boolean isFromUser) {
        if (!isFromUser) {
            return;
        }
        MessageEvent messageEvent = new MessageEvent();
        int eventType;
        if (getView().needShowApplyAll()) {
            eventType = MESSAGE_TYPE_CHANGE_CLIP_FILTER_PROGRESS;
            messageEvent.setFloatValue(progress / 100F);
        } else {
            eventType = MESSAGE_TYPE_CHANGE_TIMELINE_FILTER_PROGRESS;
            messageEvent.setFloatValue(progress / 100F);
        }
        messageEvent.setEventType(eventType);
        EventBus.getDefault().post(messageEvent);
    }


    public void onStopTrackingTouch() {
        if (getView().needShowApplyAll()) {
            MessageEvent messageEvent = new MessageEvent();
            messageEvent.setEventType(MESSAGE_TYPE_CHANGE_CLIP_FILTER_FINISH);
            EventBus.getDefault().post(messageEvent);
        } else {
            MessageEvent messageEvent = new MessageEvent();
            messageEvent.setEventType(MESSAGE_TYPE_CHANGE_TIMELINE_FILTER_FINISH);
            EventBus.getDefault().post(messageEvent);
        }
    }

    public void applyToAll() {
        MessageEvent.sendEvent(0, MESSAGE_APPLY_ALL_FILTER);
    }

    public void confirm() {

    }
}
