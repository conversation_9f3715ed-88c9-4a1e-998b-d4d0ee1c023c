package com.meishe.myvideo.view.menu;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PointF;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import com.meishe.base.utils.LogUtils;

/**
 * author：yangtailin on 2020/7/1 17:15
 * MY减少视图类
 * My reduces view classes
 */
public class MYCutView extends View {
    private final static String TAG = "MYCutView";
    private final static int RECT_L_T = 1;
    private final static int RECT_L_B = 2;
    private final static int RECT_R_T = 3;
    private final static int RECT_R_B = 4;
    private Rect mDrawRect = new Rect();
    private int mTouchRect = -1;
    private Paint mPaint;
    private Paint mCornerPaint;
    private final static int ANGEL_LENGTH = 30;
    private final static int PADDING = 10;
    private int mPadding = PADDING;
    private int mAngelLength = ANGEL_LENGTH;
    private int mStrokeWidth = 4;
    private OnTransformListener mOnTransformListener;
    private float mTouchX = 0;
    private float mTouchY = 0;

    private final static int ONE_FINGER = 1;
    private final static int TWO_FINGER = 2;
    private boolean mIsTwoFingerEvent = false;
    private double mTwoFingerStartLength;
    private PointF mTwoFingerOldPoint = new PointF();
    private PointF mDownPointF = new PointF(0, 0);
    private PointF mMovePointF = new PointF(0, 0);
    private double mTwoFingerEndLength;

    public MYCutView(Context context) {
        super(context);
    }

    public MYCutView(Context context, AttributeSet attrs) {
        super(context, attrs);
        mPaint = new Paint();
        mPaint.setColor(Color.WHITE);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(2);

        mCornerPaint = new Paint();
        mCornerPaint.setColor(Color.WHITE);
        mCornerPaint.setStyle(Paint.Style.STROKE);
        mCornerPaint.setStrokeWidth(6);
        initView();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        mDrawRect.left = 0;
        mDrawRect.top = 0;
        mDrawRect.bottom = MeasureSpec.getSize(heightMeasureSpec);
        mDrawRect.right = MeasureSpec.getSize(widthMeasureSpec);
    }

    private void initView() {

    }

    @Override
    protected void onDraw(Canvas canvas) {
        Path path = new Path();
        //绘制边框 Bound box
        path.moveTo(mDrawRect.left + mPadding, mDrawRect.top + mPadding);
        path.lineTo(mDrawRect.right - mPadding, mDrawRect.top + mPadding);
        path.lineTo(mDrawRect.right - mPadding, mDrawRect.bottom - mPadding);
        path.lineTo(mDrawRect.left + mPadding, mDrawRect.bottom - mPadding);
        path.lineTo(mDrawRect.left + mPadding, mDrawRect.top + mPadding);
        canvas.drawPath(path, mPaint);

        //绘制中线 Draw the midline
        int width = mDrawRect.right - mDrawRect.left;
        int height = mDrawRect.bottom - mDrawRect.top;
        //竖线 long string
        path.moveTo(mDrawRect.left + (width - mPadding * 2) * 1.0F / 3, mDrawRect.top + mPadding);
        path.lineTo(mDrawRect.left + (width - mPadding * 2) * 1.0F / 3, mDrawRect.bottom - mPadding);
        canvas.drawPath(path, mPaint);

        path.moveTo(mDrawRect.left + (width - mPadding * 2) * 1.0F / 3 * 2, mDrawRect.top + mPadding);
        path.lineTo(mDrawRect.left + (width - mPadding * 2) * 1.0F / 3 * 2, mDrawRect.bottom - mPadding);
        canvas.drawPath(path, mPaint);

        //横线 transverse line
        path.moveTo(mDrawRect.left + mPadding, mDrawRect.top + (height - mPadding * 2) * 1.0F / 3 * 2);
        path.lineTo(mDrawRect.right - mPadding, mDrawRect.top + (height - mPadding * 2) * 1.0F / 3 * 2);
        canvas.drawPath(path, mPaint);

        path.moveTo(mDrawRect.left + mPadding, mDrawRect.top + (height - mPadding * 2) * 1.0F / 3);
        path.lineTo(mDrawRect.right - mPadding, mDrawRect.top + (height - mPadding * 2) * 1.0F / 3);
        canvas.drawPath(path, mPaint);

        //绘制左上角 Draw top left corner
        path.reset();
        path.moveTo(mDrawRect.left + mAngelLength + mPadding + mStrokeWidth / 2, mDrawRect.top + mPadding + mStrokeWidth / 2);
        path.lineTo(mDrawRect.left + mPadding + mStrokeWidth / 2, mDrawRect.top + mPadding + mStrokeWidth / 2);
        path.lineTo(mDrawRect.left + mPadding + mStrokeWidth / 2, mDrawRect.top + mAngelLength + mPadding + +mStrokeWidth / 2);
        canvas.drawPath(path, mCornerPaint);

        //绘制右上角 Draw the upper right corner
        path.moveTo(mDrawRect.right - mAngelLength - mPadding - mStrokeWidth / 2, mDrawRect.top + mPadding + mStrokeWidth / 2);
        path.lineTo(mDrawRect.right - mPadding - mStrokeWidth / 2, mDrawRect.top + mPadding + mStrokeWidth / 2);
        path.lineTo(mDrawRect.right - mPadding - mStrokeWidth / 2, mDrawRect.top + mPadding + mAngelLength + mStrokeWidth / 2);
        canvas.drawPath(path, mCornerPaint);

        //绘制右下角 Draw the lower right corner
        path.moveTo(mDrawRect.right - mPadding - mStrokeWidth / 2, mDrawRect.bottom - mPadding - mStrokeWidth / 2 - mAngelLength);
        path.lineTo(mDrawRect.right - mPadding - mStrokeWidth / 2, mDrawRect.bottom - mPadding - mStrokeWidth / 2);
        path.lineTo(mDrawRect.right - mPadding - mStrokeWidth / 2 - mAngelLength, mDrawRect.bottom - mPadding - mStrokeWidth / 2);
        canvas.drawPath(path, mCornerPaint);

        //绘制左下角 Draw the lower left corner
        path.moveTo(mDrawRect.left + mPadding + mStrokeWidth / 2, mDrawRect.bottom - mPadding - mStrokeWidth / 2 - mAngelLength);
        path.lineTo(mDrawRect.left + mPadding + mStrokeWidth / 2, mDrawRect.bottom - mPadding - mStrokeWidth / 2);
        path.lineTo(mDrawRect.left + mPadding + mStrokeWidth / 2 + mAngelLength, mDrawRect.bottom - mPadding - mStrokeWidth / 2);
        canvas.drawPath(path, mCornerPaint);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        int pointerCount = event.getPointerCount();
        if (pointerCount > TWO_FINGER) {
            return false;
        }

        if (((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_DOWN) && (pointerCount == ONE_FINGER)) {
            mIsTwoFingerEvent = false;
        }

        if (pointerCount == TWO_FINGER) {
            mIsTwoFingerEvent = true;
            return twoFingerTouch(event);
        } else {
            return oneFingerTouch(event);
        }
    }

    private boolean oneFingerTouch(MotionEvent event) {
        int action = event.getAction();
        if (action == MotionEvent.ACTION_DOWN) {
            mTouchRect = getTouchRect(event);
            if (mTouchRect > 0) {
                mTouchX = (int) event.getX();
                mTouchY = (int) event.getY();
            }
            return true;
        } else if (action == MotionEvent.ACTION_MOVE) {
            if (mTouchRect == RECT_L_T) {
                mDrawRect.left = (int) event.getX();
                mDrawRect.top = (int) event.getY();
                mDrawRect.right = getWidth();
                mDrawRect.bottom = getHeight();
            } else if (mTouchRect == RECT_L_B) {
                mDrawRect.left = (int) event.getX();
                mDrawRect.top = 0;
                mDrawRect.right = getWidth();
                mDrawRect.bottom = (int) event.getY();
            } else if (mTouchRect == RECT_R_T) {
                mDrawRect.top = (int) event.getY();
                mDrawRect.left = 0;
                mDrawRect.right = (int) event.getX();
                mDrawRect.bottom = getHeight();
            } else if (mTouchRect == RECT_R_B) {
                mDrawRect.left = 0;
                mDrawRect.top = 0;
                mDrawRect.right = (int) event.getX();
                mDrawRect.bottom = (int) event.getY();
            }
            if (mTouchRect > 0) {
                invalidate();
            } else {
                if (mOnTransformListener != null) {
                    mOnTransformListener.onTrans(event.getX() - mTouchX, event.getY() - mTouchY);
                }
            }
        } else if (action == MotionEvent.ACTION_UP) {
            mDrawRect.left = 0;
            mDrawRect.top = 0;
            mDrawRect.right = getWidth();
            mDrawRect.bottom = getHeight();
            invalidate();
            if (mOnTransformListener != null) {
                mOnTransformListener.onTransEnd();
            }
        }
        return super.onTouchEvent(event);
    }

    private boolean twoFingerTouch(MotionEvent event) {
        if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_POINTER_DOWN) {
            float xLen = event.getX(0) - event.getX(1);
            float yLen = event.getY(0) - event.getY(1);
            mTwoFingerStartLength = Math.sqrt((xLen * xLen) + (yLen * yLen));
            mTwoFingerOldPoint.set(xLen, yLen);
        } else if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_MOVE) {
            float xLen = event.getX(0) - event.getX(1);
            float yLen = event.getY(0) - event.getY(1);
            float oldDegree = (float) Math.toDegrees(Math.atan2(mTwoFingerOldPoint.x, mTwoFingerOldPoint.y));
            float newDegree = (float) Math.toDegrees(Math.atan2((event.getX(0) - event.getX(1)), (event.getY(0) - event.getY(1))));
            mTwoFingerEndLength = Math.sqrt(xLen * xLen + yLen * yLen);

            float scalePercent = (float) (mTwoFingerEndLength / mTwoFingerStartLength);
            float degree = newDegree - oldDegree;

            if(mOnTransformListener != null) {
                mOnTransformListener.onScaleAndRotate(scalePercent, degree);
            }
            mTwoFingerStartLength = mTwoFingerEndLength;
            mTwoFingerOldPoint.set(xLen, yLen);
        } else if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_UP) {
            if(mOnTransformListener != null) {
                mOnTransformListener.onTransEnd();
            }
        }
        return super.onTouchEvent(event);
    }

    private int getTouchRect(MotionEvent event) {
        if (isInLeftTop(event)) {
           LogUtils.d( "getTouchRect: RECT_L_T");
            return RECT_L_T;
        } else if (isInLeftBottom(event)) {
           LogUtils.d( "getTouchRect: RECT_L_B");
            return RECT_L_B;
        } else if (isInRightBottom(event)) {
           LogUtils.d( "getTouchRect: RECT_R_B");
            return RECT_R_B;
        } else if (isInRightTop(event)) {
           LogUtils.d( "getTouchRect: RECT_R_T");
            return RECT_R_T;
        }
        return -1;
    }

    /**
     * 触摸区域的范围
     * The range of the touch area.
     */
    private final static int TOUCH_RECT_SIZE = 50;

    private boolean isInLeftTop(MotionEvent event) {
        float touchX = event.getX();
        float touchY = event.getY();
        return (touchX >= 0 && touchX <= TOUCH_RECT_SIZE && touchY >= 0 && touchY <= TOUCH_RECT_SIZE);
    }

    private boolean isInRightTop(MotionEvent event) {
        float touchX = event.getX();
        float touchY = event.getY();
        return (touchX >= (getWidth() - TOUCH_RECT_SIZE) && touchX <= getWidth() && touchY >= 0 && touchY <= TOUCH_RECT_SIZE);
    }

    private boolean isInLeftBottom(MotionEvent event) {
        float touchX = event.getX();
        float touchY = event.getY();
        return (touchX >= 0 && touchX <= TOUCH_RECT_SIZE && touchY >= (getHeight() - TOUCH_RECT_SIZE) && touchY <= getHeight());
    }

    private boolean isInRightBottom(MotionEvent event) {
        float touchX = event.getX();
        float touchY = event.getY();
        return (touchX >= (getWidth() - TOUCH_RECT_SIZE) && touchX <= getWidth() && touchY >= (getHeight() - TOUCH_RECT_SIZE) && touchY <= getHeight());
    }

    /**
     * Sets on transform listener.
     * 设置转换监听器
     * @param listener the listener 监听
     */
    public void setOnTransformListener(OnTransformListener listener) {
        mOnTransformListener = listener;
    }

    /**
     * The interface On transform listener.
     * 变换监听接口
     */
    public interface OnTransformListener {
        void onTrans(float deltaX, float deltaY);

        /**
         * On scale and rotate.
         * 缩放和旋转
         * @param scale  the scale  缩放
         * @param degree the degree 度
         */
        void onScaleAndRotate(float scale, float degree);

        /**
         * On trans end.
         *  变换完成
         */
        void onTransEnd();
    }
}
