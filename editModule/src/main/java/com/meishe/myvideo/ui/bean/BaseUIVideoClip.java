package com.meishe.myvideo.ui.bean;

import static com.meishe.engine.bean.CommonData.CLIP_COMMON;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/6/16 13:55
 * 基础视频剪辑类
 * Basic video clip class
 */
public class BaseUIVideoClip extends BaseUIClip {
    private long inPoint;
    private long outPoint;

    public BaseUIVideoClip(int trackIndex) {
        super(CLIP_COMMON, trackIndex);
    }

    @Override
    public int getSubType() {
        return 0;
    }

    @Override
    public int getClipIndexInTrack() {
        return 0;
    }

    @Override
    public long getInPoint() {
        return inPoint;
    }

    @Override
    public void setInPoint(long inPoint) {
        this.inPoint = inPoint;
    }

    @Override
    public void setOutPoint(long outPoint) {
        this.outPoint = outPoint;
    }

    @Override
    public String getIconFilePath() {
        return null;
    }

    @Override
    public String getFilePath() {
        return null;
    }

    @Override
    public float[] getRecordArray() {
        return new float[0];
    }

    @Override
    public void setDuration(long duration) {

    }

    @Override
    public long getDuration() {
        return outPoint - inPoint;
    }

    @Override
    public long getFadeIn() {
        return 0;
    }

    @Override
    public long getFadeOut() {
        return 0;
    }

    @Override
    public String getDisplayName() {
        return null;
    }

    @Override
    public void setDisplayName(String name) {

    }

    @Override
    public boolean canExceedLength() {
        return true;
    }

    @Override
    public boolean canDrag() {
        return true;
    }

    @Override
    public int getBackGroundColor() {
        return 0;
    }
}
