<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        tools:ignore="UseCompoundDrawables">

        <RelativeLayout
            android:layout_width="@dimen/dp_px_66"
            android:layout_height="@dimen/dp_px_66">

            <ImageView
                android:id="@+id/icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:contentDescription="@null" />

            <ImageView
                android:id="@+id/icon_stop"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:contentDescription="@null"
                android:src="@mipmap/plug_stop" />
        </RelativeLayout>


        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_px_27"
            android:contentDescription="@null"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/white_8"
            android:textSize="@dimen/menu_item_text_size" />
    </LinearLayout>

</FrameLayout>