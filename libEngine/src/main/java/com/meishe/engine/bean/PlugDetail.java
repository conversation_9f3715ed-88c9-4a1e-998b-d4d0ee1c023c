package com.meishe.engine.bean;

import static com.meishe.engine.bean.MeicamVideoFx.INVALID_VALUE;
import static com.meishe.engine.bean.MeicamVideoFx.INVALID_VALUE_INT;

import com.meishe.base.constants.Constants;
import com.meishe.base.utils.ResourceUtils;
import com.meishe.base.utils.Utils;

import java.io.Serializable;
import java.util.List;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: Chu<PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2022/6/15 15:49
 * @Description: 插件细节 The plug detail
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class PlugDetail implements Serializable {
    public List<Param> paramList;

    public static class Param implements Serializable {
        private String name;
        private String enName;
        public String coverPath;
        public String paramName;
        public String valueDefault;
        public String valueType;
        public float valueMin;
        public float valueMax;
        public List<Type> modeList;

        public Param(String coverPath, String paramName) {
            this.coverPath = coverPath;
            this.paramName = paramName;
        }

        public Param(String name, String enName, String coverPath, String paramName) {
            this.name = name;
            this.enName = enName;
            this.coverPath = coverPath;
            this.paramName = paramName;
        }

        public boolean isAngel() {
            return paramName.contains("Angle");
        }

        public int getResId(String path) {
            return ResourceUtils.getMipmapIdByName(path);
        }

        public void setName(String name) {
            this.name = name;
            this.enName = name;
        }

        public String getName() {
            return Utils.isZh() ? name : enName;
        }

        public class Type implements Serializable {
            public String paramValue;
            private String modeName;
            private String modeEnName;

            public String getName() {
                return Utils.isZh() ? modeName : modeEnName;
            }
        }
    }

    public void setPlugDetail(MeicamTimelineVideoFxClip clip) {
        if (clip == null) {
            return;
        }
        for (Param param : paramList) {
            String valueType = param.valueType;
            if (Constants.PlugType.BOOL.equals(valueType)) {
                //bool形式的，需要UI checkBox
                //bool type，need UI checkBox
                if (clip.getParamVal(param.paramName)) {
                    boolean value = clip.getBooleanVal(param.paramName);
                    param.valueDefault = String.valueOf(value);
                }
            } else if (Constants.PlugType.FLOAT.equals(valueType)) {
                //float形式的，需要UI 轮盘
                //float type，need UI of wheel
                if (clip.getFloatVal(param.paramName) != null) {
                    float value = clip.getFloatVal(param.paramName);
                    param.valueDefault = String.valueOf(value);
                }

            } else if (Constants.PlugType.POSITION2D_X.equals(valueType)) {
                //POSITION2D_X，需要UI 轮盘
                //POSITION2D_X，need UI of wheel
                if (clip.getPosition2DVal(param.paramName) != null) {
                    MeicamPosition2D value = clip.getPosition2DVal(param.paramName);
                    param.valueDefault = String.valueOf(value.x);
                }

            } else if (Constants.PlugType.POSITION2D_Y.equals(valueType)) {
                //POSITION2D_Y，需要UI 轮盘
                //POSITION2D_Y，need UI of wheel
                if (clip.getPosition2DVal(param.paramName) != null) {
                    MeicamPosition2D value = clip.getPosition2DVal(param.paramName);
                    param.valueDefault = String.valueOf(value.y);
                }
            } else if (Constants.PlugType.PATH.equals(valueType)
                    || Constants.PlugType.STRING.equals(valueType)
                    || Constants.PlugType.CURVE.equals(valueType)
                    || Constants.PlugType.COLOR.equals(valueType)) {
                //文件路径 UI上需要添加路径入口
                //q曲线调节 UI上需要调节曲线入口
                //CO 颜色 形式的 边缘增强
                //The file path， Path entry needs to be added on the UI.
                //The curve adjust. The curve adjust entry needs to be added on the UI.
                //Edge enhancement in CO color form.
                if (clip.getStringVal(param.paramName) != null) {
                    param.valueDefault = clip.getStringVal(param.paramName);
                }
            } else if (Constants.PlugType.MENU.equals(valueType)) {
                if (clip.getMenuVal(param.paramName) != null) {
                    param.valueDefault = clip.getMenuVal(param.paramName);
                }
            } else if (Constants.PlugType.INT.equals(valueType) ||
                    Constants.PlugType.INT_CHOOSE.equals(valueType)) {
                if (clip.getIntVal(param.paramName) != INVALID_VALUE_INT) {
                    param.valueDefault = String.valueOf(clip.getIntVal(param.paramName));
                }
            }
        }

    }

    public void setPlugDetail(MeicamVideoFx clip) {
        if (clip == null) {
            return;
        }
        for (Param param : paramList) {
            String valueType = param.valueType;
            if (Constants.PlugType.BOOL.equals(valueType)) {
                //bool形式的，需要UI checkBox
                //bool type，need UI checkBox
                if (clip.getParamVal(param.paramName)) {
                    boolean value = clip.getBooleanVal(param.paramName);
                    param.valueDefault = String.valueOf(value);
                }
            } else if (Constants.PlugType.FLOAT.equals(valueType)) {
                //float形式的，需要UI 轮盘
                //float type，need UI of wheel
                if (clip.getFloatVal(param.paramName) != INVALID_VALUE) {
                    float value = clip.getFloatVal(param.paramName);
                    param.valueDefault = String.valueOf(value);
                }

            } else if (Constants.PlugType.POSITION2D_X.equals(valueType)) {
                //POSITION2D_X，需要UI 轮盘
                //POSITION2D_X，need UI of wheel
                if (clip.getPosition2DVal(param.paramName) != null) {
                    MeicamPosition2D value = clip.getPosition2DVal(param.paramName);
                    param.valueDefault = String.valueOf(value.x);
                }

            } else if (Constants.PlugType.POSITION2D_Y.equals(valueType)) {
                //POSITION2D_Y，需要UI 轮盘
                //POSITION2D_Y，need UI of wheel
                if (clip.getPosition2DVal(param.paramName) != null) {
                    MeicamPosition2D value = clip.getPosition2DVal(param.paramName);
                    param.valueDefault = String.valueOf(value.y);
                }
            } else if (Constants.PlugType.PATH.equals(valueType)
                    || Constants.PlugType.STRING.equals(valueType)
                    || Constants.PlugType.CURVE.equals(valueType)
                    || Constants.PlugType.COLOR.equals(valueType)) {
                //文件路径 UI上需要添加路径入口
                //q曲线调节 UI上需要调节曲线入口
                //CO 颜色 形式的 边缘增强
                //The file path， Path entry needs to be added on the UI.
                //The curve adjust. The curve adjust entry needs to be added on the UI.
                //Edge enhancement in CO color form.
                if (clip.getStringVal(param.paramName) != null) {
                    param.valueDefault = clip.getStringVal(param.paramName);
                }
            } else if (Constants.PlugType.MENU.equals(valueType)) {
                if (clip.getMenuVal(param.paramName) != null) {
                    param.valueDefault = clip.getMenuVal(param.paramName);
                }
            } else if (Constants.PlugType.INT.equals(valueType)
                    || Constants.PlugType.INT_CHOOSE.equals(valueType)) {
                if (clip.getIntVal(param.paramName) != INVALID_VALUE_INT) {
                    param.valueDefault = String.valueOf(clip.getIntVal(param.paramName));
                }
            }
        }

    }
}
