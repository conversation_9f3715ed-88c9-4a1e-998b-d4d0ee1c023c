package com.meishe.player.fragment;

import android.graphics.Bitmap;
import android.graphics.Matrix;
import android.graphics.RectF;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsRational;
import com.meicam.sdk.NvsSize;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsVideoResolution;
import com.meishe.base.bean.FloatPoint;
import com.meishe.base.model.BaseFragment;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.player.BuildConfig;
import com.meishe.player.R;
import com.meishe.player.view.RectSelectView;
import com.meishe.player.view.TestRectView;
import com.meishe.player.view.bean.TransformData;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/11/19 15:29
 * @Description :可以选择视频区域的fragment 类 The fragment for selecting video rect.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class RectSelectVideoFragment extends BaseFragment {
    private RectSelectView mRectSelectView;

    /**
     * 是否显示区域选择视图
     * Display Area Selection View
     */
    private boolean mIsShowRectSelectView;
    private float mRectRatio;
    private TransformData mTransFormData;
    private float mMinLiveWindowScale;
    private TestRectView mTestView;

    public static final String INTENT_KEY_FILE_PATH = "filePath";
    private ImageView mNvsLiveWindow;
    private RelativeLayout mPlayerLayout;
    private String mFilePath;
    private int[] mFileSize;

    /**
     * Instantiates a new Rect select video fragment.
     */
    public RectSelectVideoFragment() {
        super();
    }

    /**
     * New instance rect select video fragment.
     *
     * @param filePath the file path
     * @return the rect select video fragment
     */
    public static RectSelectVideoFragment newInstance(String filePath) {
        RectSelectVideoFragment fragment = new RectSelectVideoFragment();
        Bundle args = new Bundle();
        args.putString(INTENT_KEY_FILE_PATH, filePath);
        fragment.setArguments(args);
        return fragment;
    }

    /**
     * Bind layout int.
     * 绑定布局
     *
     * @return the int
     */
    @Override
    protected int bindLayout() {
        return R.layout.player_rect_select_fragment;
    }

    @Override
    protected void onLazyLoad() {

    }

    private float mTransX;
    private float mTransY;
    private float mScale = 1F;

    private float mRotation;

    /**
     * live window的中心点
     * The center of live window
     */
    private final FloatPoint mLiveWindowCenterPoint = new FloatPoint();
    private final FloatPoint mOriginalLiveWindowCenterPoint = new FloatPoint();

    /**
     * 选择区域的四个点在屏幕上的坐标
     * The select rect point list
     */
    private final List<FloatPoint> mSelectRectPointList = new ArrayList<>();

    /**
     * liveWindow 的位置的四个点的坐标
     * The coordinates of the four points at the location of liveWindow
     */
    private final List<FloatPoint> mOriginalLiveWindowPointList = new ArrayList<>();

    /**
     * Init view.
     * 初始化视图
     *
     * @param mRootView the m root view
     */
    @Override
    protected void initView(View mRootView) {
        Bundle arguments = getArguments();
        if (arguments != null) {
            mFilePath = arguments.getString(INTENT_KEY_FILE_PATH);
        }
        mPlayerLayout = mRootView.findViewById(R.id.fragment_base_parent);
        mNvsLiveWindow = mRootView.findViewById(R.id.fragment_base_live_window);
        setLiveWindowRatio();
        Bitmap bitmap = getBitmap(mFilePath);
        mNvsLiveWindow.setImageBitmap(bitmap);
        mRectSelectView = mRootView.findViewById(R.id.rect_selector);
        mTestView = mRootView.findViewById(R.id.test);
        if (mIsShowRectSelectView) {
            mRectSelectView.setVisibility(View.VISIBLE);
            mRectSelectView.setRatio(mRectRatio);
            mRectSelectView.setEventListener(new RectSelectView.EventListener() {
                @Override
                public void onActionDown() {

                }

                @Override
                public void onActionMove(float deltaX, float deltaY) {
                    if (canTrans(deltaX, 0, 1.0F, 0)) {
                        mTransX += deltaX;
                        mLiveWindowCenterPoint.x += deltaX;
                    }
                    if (canTrans(0, deltaY, 1.0F, 0)) {
                        mTransY += deltaY;
                        mLiveWindowCenterPoint.y += deltaY;
                    }
                    transLiveWindow(mTransX, mTransY);
                }

                @Override
                public void onScaleAndRotate(float scale, float degree) {
                    if (scale < 1.0F && !canTrans(0, 0, 1, -degree)) {
                        return;
                    }
                    //暂时不支持旋转 Rotation not currently supported.
                    degree = 0;

                    float newDegree = mRotation - degree;
                    if (newDegree > 45 && degree < 0) {
                        return;
                    }
                    newDegree = (int) newDegree;
                    rotateLiveWindow(newDegree);

                    double scaleValue = computeScale(scale, -degree);
                    float newScale = mScale * scale;
                    if (newScale < scaleValue && scaleValue > 1.0F) {
                        newScale = (float) scaleValue;
                    }

                    if (scaleValue != 1.0F && scale < 1.0F) {
                        return;
                    }

                    if (newScale < mMinLiveWindowScale) {
                        newScale = mMinLiveWindowScale;
                    }
                    scaleLiveWindow((float) newScale);
                }

                @Override
                public void onActionUp() {

                }

                @Override
                public void onClick() {

                }
            });
        }
    }

    @Override
    protected void initData() {

    }

    /**
     * Get bitmap
     *
     * @param filePath   要加载的图片路径
     * @return the bitmap
     */
    public static Bitmap getBitmap(String filePath) {
        MeicamTimeline timeline = EditorEngine.getInstance().createSingleClipTimelineExt(filePath);
        return timeline.grabImageFromTimeline(EditorEngine.getInstance().getStreamingContext(), 0, new NvsRational(1, 1));
    }

    /**
     * Sets live window ratio.
     * 设置活动窗口比率
     */
    private void setLiveWindowRatio() {
        int[] fileSize = getFileSize(mFilePath);
        if (fileSize == null) {
            LogUtils.e("fileSize is null!");
            return;
        }
        final NvsVideoResolution videoRes = new NvsVideoResolution();
        videoRes.imageWidth = fileSize[0];
        videoRes.imageHeight = fileSize[1];
        if (mPlayerLayout.getWidth() == 0 && mPlayerLayout.getHeight() == 0) {
            mPlayerLayout.post(new Runnable() {
                @Override
                public void run() {
                    setLiveWindowRatio(videoRes);
                    onLiveWindowSizeChanged();
                }
            });
        } else {
            setLiveWindowRatio(videoRes);
            onLiveWindowSizeChanged();
        }

    }

    private int[] getFileSize(String mFilePath) {
        if (mFileSize != null) {
            return mFileSize;
        }

        NvsAVFileInfo avFileInfo = NvsStreamingContext.getInstance().getAVFileInfo(mFilePath);
        if (avFileInfo == null) {
            LogUtils.e("avFileInfo is null!");
            return null;
        }
        mFileSize = new int[2];
        int videoStreamRotation = avFileInfo.getVideoStreamRotation(0);
        NvsSize dimension = avFileInfo.getVideoStreamDimension(0);
        final NvsVideoResolution videoRes = new NvsVideoResolution();
        if (videoStreamRotation == 1 || videoStreamRotation == 3) {
            mFileSize[0] = dimension.height;
            mFileSize[1]  = dimension.width;
        } else {
            mFileSize[0]  = dimension.width;
            mFileSize[1]  = dimension.height;
        }
        return mFileSize;
    }

    /**
     * Sets live window ratio.
     * 设置活动窗口比率
     *
     * @param resolution timeline 的分辨率
     */
    public void setLiveWindowRatio(@NonNull NvsVideoResolution resolution) {
        ViewGroup.LayoutParams layoutParams = mNvsLiveWindow.getLayoutParams();
        int layoutWidth = mPlayerLayout.getWidth();
        int layoutHeight = mPlayerLayout.getHeight();
        float layoutRatio = layoutWidth * 1F / layoutHeight;
        float timelineRatio = resolution.imageWidth * 1.0F / resolution.imageHeight;
        if (timelineRatio > layoutRatio) {
            layoutParams.height = (int) (layoutWidth / timelineRatio);
        } else {
            layoutParams.width = (int) (layoutHeight * timelineRatio);
        }
        mNvsLiveWindow.setLayoutParams(layoutParams);
    }

    private void scaleLiveWindow(float newScale) {
        mScale = newScale;
        mNvsLiveWindow.setScaleX(newScale);
        mNvsLiveWindow.setScaleY(newScale);
    }

    private void rotateLiveWindow(float degree){
        mRotation = degree;
        mNvsLiveWindow.setRotation(degree);
    }

    private float getSuitLiveWindowScale(float[] rectSize) {
        int liveWindowWidth = mNvsLiveWindow.getWidth();
        int liveWindowHeight = mNvsLiveWindow.getHeight();
        float widthScale = rectSize[0] / liveWindowWidth;
        float heightScale = rectSize[1] / liveWindowHeight;
        float scale = widthScale;
        if (scale < heightScale) {
            scale = heightScale;
        }
        return scale;
    }


    /**
     * Show rect select view.
     * 显示区域选择视图
     *
     * @param ratio         the ratio
     * @param transformData the transform data
     */
    public void showRectSelectView(float ratio, TransformData transformData) {
        mIsShowRectSelectView = true;
        mRectRatio = ratio;
        mTransFormData = transformData;
    }

    protected void onLiveWindowSizeChanged() {
        if (mIsShowRectSelectView) {
            initRectSelectState();
        }
    }

    private void initRectSelectState() {
        mRectSelectView.post(new Runnable() {
            @Override
            public void run() {
                float[] rectSize = mRectSelectView.getRectSize();
                initLiveWindowAndRectPointData();
                //liveWindow 放大到符合裁剪区域的大小
                //The live window zooms in to fit the crop region
                mMinLiveWindowScale = getSuitLiveWindowScale(rectSize);
                getTransFromDataInView();
                transLiveWindow();
            }
        });
    }

    private void transLiveWindow() {
        mTransX = mTransFormData.getTransX();
        mTransY = mTransFormData.getTransY();
        mRotation = mTransFormData.getRotation();
        mLiveWindowCenterPoint.x += mTransX;
        mLiveWindowCenterPoint.y += mTransY;
        transLiveWindow(mTransX, mTransY);
        mNvsLiveWindow.setTranslationX(mTransX);
        mNvsLiveWindow.setTranslationY(mTransY);
        scaleLiveWindow(mTransFormData.getScale());
        rotateLiveWindow(mRotation);
    }


    private void transLiveWindow(float transX, float transY) {
        mNvsLiveWindow.setTranslationX(transX);
        mNvsLiveWindow.setTranslationY(transY);
    }

    private void initLiveWindowAndRectPointData() {
        int[] location = new int[2];
        mNvsLiveWindow.getLocationOnScreen(location);
        int liveWindowWidth = mNvsLiveWindow.getWidth();
        int liveWindowHeight = mNvsLiveWindow.getHeight();
        mLiveWindowCenterPoint.x = location[0] + liveWindowWidth / 2F;
        mLiveWindowCenterPoint.y = location[1] + liveWindowHeight / 2F;
        mOriginalLiveWindowCenterPoint.x = location[0] + liveWindowWidth / 2F;
        mOriginalLiveWindowCenterPoint.y = location[1] + liveWindowHeight / 2F;
        mOriginalLiveWindowPointList.add(new FloatPoint(location[0], location[1]));
        mOriginalLiveWindowPointList.add(new FloatPoint(location[0], location[1] + liveWindowHeight));
        mOriginalLiveWindowPointList.add(new FloatPoint(location[0] + liveWindowWidth, location[1] + liveWindowHeight));
        mOriginalLiveWindowPointList.add(new FloatPoint(location[0] + liveWindowWidth, location[1]));
        RectF rect = mRectSelectView.getRect();
        mPlayerLayout.getLocationOnScreen(location);
        mSelectRectPointList.add(new FloatPoint(location[0] + rect.left, location[1] + rect.top));
        mSelectRectPointList.add(new FloatPoint(location[0] + rect.left, location[1] + rect.bottom));
        mSelectRectPointList.add(new FloatPoint(location[0] + rect.right, location[1] + rect.bottom));
        mSelectRectPointList.add(new FloatPoint(location[0] + rect.right, location[1] + rect.top));
        if (BuildConfig.DEBUG) {
            mTestView.setRectPoint(mSelectRectPointList);
        }
    }

    /**
     * Get trans from data in view float [ ].
     * 获取View的transform值，作为输入
     */
    private void getTransFromDataInView() {
        if (mTransFormData != null) {
            int[] fileSize = getFileSize(mFilePath);
            mTransFormData = parseTransToView(fileSize[0], fileSize[1], fileSize, mRectSelectView.getRectSize(), mTransFormData);
            mTransFormData.setScale(mTransFormData.getScale() * mMinLiveWindowScale);
        }

    }

    /**
     * 转换transform 数据为view范围内的transfrom
     * Convert transform data to transfrom within view scope
     *
     * @param timelineWidth  时间线的宽
     * @param timelineHeight 时间线的高
     * @param fileSize       文件尺寸
     * @param rectSize       裁剪区域的宽高
     * @param transFormData  转换数据
     * @return view坐标系下的转换数据
     */
    private TransformData parseTransToView(int timelineWidth, int timelineHeight, int[] fileSize,
                                           float[] rectSize, TransformData transFormData) {
        if (fileSize == null) {
            return transFormData;
        }
        float transXInTimeline = transFormData.getTransX();
        float transYInTimeline = transFormData.getTransY();

        int width = fileSize[0];
        int height = fileSize[1];
        float fileRatio = width * 1F / height;
        float timelineRatio = timelineWidth * 1F / timelineHeight;

        float fileWidthInTimeline;
        float fileHeightInTimeline;
        //文件宽对齐 File width alignment
        if (fileRatio > timelineRatio) {
            fileWidthInTimeline = timelineWidth;
            fileHeightInTimeline = fileWidthInTimeline / fileRatio;
        } else {//高对齐 High alignment
            fileHeightInTimeline = timelineHeight;
            fileWidthInTimeline = fileHeightInTimeline * fileRatio;
        }
        float rectWidthInTimeline;
        float rectHeightInTimeline;
        float rectRatio = rectSize[0] / rectSize[1];
        //裁剪区域宽对齐 Cropped area width alignment
        if (rectRatio > fileRatio) {
            rectWidthInTimeline = fileWidthInTimeline;
            rectHeightInTimeline = rectWidthInTimeline / rectRatio;
        } else {
            rectHeightInTimeline = fileHeightInTimeline;
            rectWidthInTimeline = rectHeightInTimeline * rectRatio;
        }

        float transXInView = transXInTimeline / rectWidthInTimeline * rectSize[0];
        float transYInView = transYInTimeline / rectHeightInTimeline * rectSize[1];
        transFormData.setTransX(transXInView * fileWidthInTimeline / 2F);
        transFormData.setTransY(transYInView * fileHeightInTimeline / 2F);
        return transFormData;
    }

    /**
     * Gets trans from data in file.
     * 获取文件的transform值，作为输出
     *
     * @return the trans from data in file 文件的transform 数据
     */
    public TransformData getTransFromDataInFile() {
        if (mNvsLiveWindow != null && mTransFormData != null) {
            mTransFormData.setTransX(mTransX);
            mTransFormData.setTransY(mTransY);
            mTransFormData.setRegion(updateRegionData());
            mTransFormData.setScale(mScale / mMinLiveWindowScale);
            mTransFormData.setRectSize(mRectSelectView.getRectSize());
            mTransFormData.setRotation(-mRotation);
            return mTransFormData;
        }
        return null;
    }

    private RectF updateRegionData() {
        float[] rectSize = mRectSelectView.getRectSize();
        return getRectEx(rectSize[0], rectSize[1], mNvsLiveWindow.getWidth(), mNvsLiveWindow.getHeight());
    }

    private RectF getRectEx(float rectWidth, float rectHeight, int imageWidth, int imageHeight) {
        imageWidth = (int) (imageWidth * mMinLiveWindowScale);
        imageHeight = (int) (imageHeight * mMinLiveWindowScale);
        RectF rectF = new RectF();
        float imageRatio = imageWidth * 1.0F / imageHeight;
        float rectRatio = rectWidth / rectHeight;
        if (rectRatio > imageRatio) {
            //宽对齐 Wide alignment
            rectF.right = rectWidth / imageWidth;
            rectF.left = -rectF.right;

            float scale = rectWidth / imageWidth;
            float timelineImageHeight = imageHeight * scale;

            rectF.top = rectHeight / timelineImageHeight;
            rectF.bottom = -rectF.top;

        } else {
            // 高对齐High alignment
            rectF.top = rectHeight/ imageHeight;
            rectF.bottom = -rectF.top;

            float scale = rectHeight / imageHeight;
            float timelineImageWidth = imageWidth * scale;
            rectF.right = rectWidth / timelineImageWidth;
            rectF.left = -rectF.right;
        }
        return rectF;
    }

    /**
     * 计算缩放值
     *
     * @param newScale 最新的缩放值
     * @return 缩放值，这个值是相对于原始值的缩放值
     */
    private double computeScale(float newScale, float degree) {
        //获取4个角的顶点数据， 并进行坐标转换
        // Obtain vertex data for 4 corners and perform coordinate conversion.
        float tempScale = newScale * mScale;
        float tempDegree = degree + mRotation;
        FloatPoint pointLT = transformData(new FloatPoint(mOriginalLiveWindowPointList.get(0)), mLiveWindowCenterPoint, tempScale, mTransX, mTransY, tempDegree);
        FloatPoint pointLB = transformData(new FloatPoint(mOriginalLiveWindowPointList.get(1)), mLiveWindowCenterPoint, tempScale, mTransX, mTransY, tempDegree);
        FloatPoint pointRB = transformData(new FloatPoint(mOriginalLiveWindowPointList.get(2)), mLiveWindowCenterPoint, tempScale, mTransX, mTransY, tempDegree);
        FloatPoint pointRT = transformData(new FloatPoint(mOriginalLiveWindowPointList.get(3)), mLiveWindowCenterPoint, tempScale, mTransX, mTransY, tempDegree);
        float halfWidth = mNvsLiveWindow.getWidth() / 2F;
        float halfHeight = mNvsLiveWindow.getHeight() / 2F;
        //左上角 top left corner
        FloatPoint point = mSelectRectPointList.get(0);
        boolean inRect = isInRect(pointLT, pointRT, pointRB, pointLB, point);
        double scale = 1.0d;
        if (!inRect) {
            //计算左上角的点到矩形上边的距离 Calculates the distance from the point in the upper left corner to the upper edge of the rectangle
            double cos = Math.cos(Math.toRadians(90 - angle(mLiveWindowCenterPoint, point, pointLT, pointRT)));
            float centerToPoint = lineSpace(mLiveWindowCenterPoint.x, mLiveWindowCenterPoint.y, point.x, point.y);
            double pointToLineLR = centerToPoint * cos;
            //计算左上角的点到矩形左边的距离 Calculate the distance from the point in the upper left corner to the left of the rectangle
            cos = Math.cos(Math.toRadians(90 - angle(mLiveWindowCenterPoint, point, pointLT, pointLB)));
            double pointToLineTB = centerToPoint * cos;

            double scaleX = pointToLineTB / halfWidth;
            double scaleY = pointToLineLR / halfHeight;
            scale = Math.max(scaleX, scaleY);
            if (Double.isNaN(scale)) {
                scale = 1.0d;
            }
        }
        double scaleTemp = scale;
        //右上角 Upper right corner
        point = mSelectRectPointList.get(3);
        inRect = isInRect(pointLT, pointRT, pointRB, pointLB, point);
        if (!inRect) {
            //计算右上角的点到矩形右边的距离 Calculate the distance from the point in the upper right corner to the right of the rectangle
            double cos = Math.cos(Math.toRadians(90 - angle(mLiveWindowCenterPoint, point, pointRT, pointRB)));
            float centerToPoint = lineSpace(mLiveWindowCenterPoint.x, mLiveWindowCenterPoint.y, point.x, point.y);
            double pointToLineTB = centerToPoint * cos;
            //计算右上角的点到矩形上边的距离 Calculate the distance from the point in the upper right corner to the upper edge of the rectangle
            cos = Math.cos(Math.toRadians(90 - angle(mLiveWindowCenterPoint, point, pointRT, pointLT)));
            double pointToLineRL = centerToPoint * cos;

            double scaleX = pointToLineRL / halfHeight;
            double scaleY = pointToLineTB / halfWidth;
            scaleTemp = Math.max(scaleX, scaleY);
            if (Double.isNaN(scaleTemp)) {
                scaleTemp = 1.0d;
            }
        }
        scale = Math.max(scale, scaleTemp);
        //右下角 Lower right corner
        point = mSelectRectPointList.get(2);
        inRect = isInRect(pointLT, pointRT, pointRB, pointLB, point);
        if (!inRect) {
            //计算右下角的点到矩形下边的距离 Calculate the distance from the point in the lower right corner to the bottom of the rectangle
            double cos = Math.cos(Math.toRadians(90 - angle(mLiveWindowCenterPoint, point, pointRB, pointLB)));
            float centerToPoint = lineSpace(mLiveWindowCenterPoint.x, mLiveWindowCenterPoint.y, point.x, point.y);
            double pointToLineRL = centerToPoint * cos;
            //计算右下角的点到矩形右边的距离 Calculate the distance from the point in the lower right corner to the right of the rectangle
            cos = Math.cos(Math.toRadians(90 - angle(mLiveWindowCenterPoint, point, pointRB, pointRT)));
            double pointToLineTB = centerToPoint * cos;

            double scaleX = pointToLineRL / halfHeight;
            double scaleY = pointToLineTB / halfWidth;
            scaleTemp = Math.max(scaleX, scaleY);
            if (Double.isNaN(scaleTemp)) {
                scaleTemp = 1.0d;
            }
        }
        scale = Math.max(scale, scaleTemp);
        //左下角 left lower corner
        point = mSelectRectPointList.get(1);
        inRect = isInRect(pointLT, pointRT, pointRB, pointLB, point);
        if (!inRect) {
            //计算左下角的点到矩形下边的距离 Calculate the distance from the point in the lower left corner to the bottom of the rectangle
            double cos = Math.cos(Math.toRadians(90 - angle(mLiveWindowCenterPoint, point, pointLB, pointRB)));
            float centerToPoint = lineSpace(mLiveWindowCenterPoint.x, mLiveWindowCenterPoint.y, point.x, point.y);
            double pointToLineLR = centerToPoint * cos;
            //计算左下角的点到矩形左边的距离 Calculate the distance from the point in the lower left corner to the left side of the rectangle
            cos = Math.cos(Math.toRadians(90 - angle(mLiveWindowCenterPoint, point, pointLT, pointLB)));
            double pointToLineTB = centerToPoint * cos;

            double scaleX = pointToLineLR / halfHeight;
            double scaleY = pointToLineTB / halfWidth;
            scaleTemp = Math.max(scaleX, scaleY);
            if (Double.isNaN(scaleTemp)) {
                scaleTemp = 1.0d;
            }
        }
        scale = Math.max(scale, scaleTemp);
        return scale;
    }

    /**
     * 计算向量夹角，此计算值为锐角
     * Calculate the angle between vectors, which is the acute angle
     *
     * @param pointA1 点A1
     * @param pointA2 点A2
     * @param pointB1 点B1
     * @param pointB2 点B2
     * @return the angle
     */
    public static double angle(FloatPoint pointA1, FloatPoint pointA2, FloatPoint pointB1, FloatPoint pointB2) {
        float x1 = pointA1.x - pointA2.x;
        float y1 = pointA1.y - pointA2.y;
        float x2 = pointB1.x - pointB2.x;
        float y2 = pointB1.y - pointB2.y;
        double value = (x1 * x2 + y1 * y2) / (Math.sqrt(x1 * x1 + y1 * y1) * Math.sqrt(x2 * x2 + y2 * y2)); // 余弦值
        return Math.toDegrees(Math.acos(Math.abs(value)));// 角度
    }


    /**
     * Is in rect boolean.
     * 判断点p是否在p1p2p3p4的矩形内
     *
     * @param p1 the p 1
     * @param p2 the p 2
     * @param p3 the p 3
     * @param p4 the p 4
     * @param p  the p
     * @return the boolean
     */
    public boolean isInRect(FloatPoint p1, FloatPoint p2, FloatPoint p3, FloatPoint p4, FloatPoint p) {
        return getCross(p1, p2, p) * getCross(p3, p4, p) >= 0 && getCross(p2, p3, p) * getCross(p4, p1, p) >= 0;
    }

    /**
     * Gets cross.
     * 计算 |p1 p2| X |p1 p|
     *
     * @param p1 the p 1
     * @param p2 the p 2
     * @param p  the p
     * @return the cross
     */
    public float getCross(FloatPoint p1, FloatPoint p2, FloatPoint p) {
        return (p2.x - p1.x) * (p.y - p1.y) - (p.x - p1.x) * (p2.y - p1.y);
    }

    /**
     * Line space float.
     * 计算两点之间的距离
     *
     * @param x1 the x 1
     * @param y1 the y 1
     * @param x2 the x 2
     * @param y2 the y 2
     * @return the float
     */
    public float lineSpace(float x1, float y1, float x2, float y2) {
        return (float) Math.sqrt((x1 - x2) * (x1 - x2) + (y1 - y2)
                * (y1 - y2));
    }


    private boolean canTrans(float deltaX, float deltaY, float deltaScale, float degree) {
        if (mOriginalLiveWindowPointList.isEmpty()) {
            return false;
        }
        //获取4个角的顶点数据，并进行坐标转换
        // Obtain vertex data for 4 corners and perform coordinate conversion.
        float tempScale = mScale * deltaScale;
        float tempTransX = mTransX + deltaX;
        float tempTransY = mTransY + deltaY;
        float tempDegree = mRotation + degree;
        FloatPoint pointLT = transformData(new FloatPoint(mOriginalLiveWindowPointList.get(0)), mLiveWindowCenterPoint, tempScale, tempTransX, tempTransY, tempDegree);
        FloatPoint pointLB = transformData(new FloatPoint(mOriginalLiveWindowPointList.get(1)), mLiveWindowCenterPoint, tempScale, tempTransX, tempTransY, tempDegree);
        FloatPoint pointRB = transformData(new FloatPoint(mOriginalLiveWindowPointList.get(2)), mLiveWindowCenterPoint, tempScale, tempTransX, tempTransY, tempDegree);
        FloatPoint pointRT = transformData(new FloatPoint(mOriginalLiveWindowPointList.get(3)), mLiveWindowCenterPoint, tempScale, tempTransX, tempTransY, tempDegree);
        if (BuildConfig.DEBUG) {
            List<FloatPoint> data = new ArrayList<>();
            data.add(pointLT);
            data.add(pointLB);
            data.add(pointRB);
            data.add(pointRT);
            mTestView.setRectPoint(data);
        }
        //判断四个顶点是否超出LiveWindow坐标范围外
        // Determine if the four vertices are outside the LiveWindow coordinate range.
        for (FloatPoint pointF : mSelectRectPointList) {
            if (!isInRect(pointLT, pointRT, pointRB, pointLB, pointF)) {
                return false;
            }
        }
        return true;
    }


    private FloatPoint transformData(FloatPoint point, FloatPoint centerPoint, float scale, float transX, float transY) {
        point = transformData(point, transX, transY);
        float[] src = new float[]{point.x, point.y};
        Matrix matrix = new Matrix();
        matrix.setScale(scale, scale, centerPoint.x, centerPoint.y);
        matrix.mapPoints(src);
        point.x = src[0];
        point.y = src[1];
        return point;
    }

    private FloatPoint transformData(FloatPoint point, FloatPoint centerPoint, float scale, float transX, float transY, float degree) {
        point = transformData(point, transX, transY);
        float[] src = new float[]{point.x, point.y};
        Matrix matrix = new Matrix();
        matrix.setRotate(degree, centerPoint.x, centerPoint.y);
        matrix.mapPoints(src);
        matrix.setScale(scale, scale, centerPoint.x, centerPoint.y);
        matrix.mapPoints(src);
        point.x = src[0];
        point.y = src[1];
        return point;
    }

    private FloatPoint transformData(FloatPoint point, FloatPoint centerPoint, float scale, float degree) {
        float[] src = new float[]{point.x, point.y};
        Matrix matrix = new Matrix();
        matrix.setRotate(degree, centerPoint.x, centerPoint.y);
        matrix.mapPoints(src);
        matrix.setScale(scale, scale, centerPoint.x, centerPoint.y);
        matrix.mapPoints(src);
        point.x = Math.round(src[0]);
        point.y = Math.round(src[1]);
        return point;
    }

    private FloatPoint transformData(FloatPoint point, float transX, float transY) {
        FloatPoint newPoint = new FloatPoint();
        float[] src = new float[]{point.x, point.y};
        Matrix matrix = new Matrix();
        matrix.setTranslate(transX, transY);
        matrix.mapPoints(src);
        newPoint.x = src[0];
        newPoint.y = src[1];
        return newPoint;
    }

}
