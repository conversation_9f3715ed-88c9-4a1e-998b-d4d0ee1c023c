package com.meishe.myvideo.downLoad;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * Created by wnw on 16-5-22.
 * AssetListDecoration
 * 资源装饰类
 */
public class AssetListDecoration extends RecyclerView.ItemDecoration{

    private final Drawable mDivider;
    private int mOrientation;

    /**
     * The constant HORIZONTAL_LIST.
     * 常数HORIZONTAL_LIST
     */
    public static final int HORIZONTAL_LIST = LinearLayoutManager.HORIZONTAL;
    /**
     * The constant VERTICAL_LIST.
     * 常数VERTICAL_LIST
     */
    public static final int VERTICAL_LIST = LinearLayoutManager.VERTICAL;

    /**
     * The constant ATRRS.
     * 常数ATRRS
     */
    /**
    * 通过获取系统属性中的listDivider来添加，在系统中的AppTheme中设置
    * Add by obtaining the listDivider in the system properties and set in AppTheme in the system
    */
    public static final int[] ATRRS  = new int[]{
            android.R.attr.listDivider
    };

    /**
     * Instantiates a new Asset list decoration.
     * 实例化一个新的资产列表装饰
     * @param context     the context
     * @param orientation the orientation
     */
    public AssetListDecoration(Context context, int orientation) {
        final TypedArray ta = context.obtainStyledAttributes(ATRRS);
        this.mDivider = ta.getDrawable(0);
        ta.recycle();
        setOrientation(orientation);
    }

    /**
     * Set orientation.
     * 设置定向
     * @param orientation the orientation
     */
    public void setOrientation(int orientation){
        if (orientation != HORIZONTAL_LIST && orientation != VERTICAL_LIST){
            throw new IllegalArgumentException("invalid orientation");
        }
        mOrientation = orientation;
    }

    @Override
    public void onDraw(@NonNull Canvas c, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        if (mOrientation == HORIZONTAL_LIST){
            drawVerticalLine(c, parent, state);
        }else {
            drawHorizontalLine(c, parent, state);

        }
    }

    /**
     * Draw horizontal line.
     * 画水平线
     * @param c      the c
     * @param parent the parent
     * @param state  the state
     */
    public void drawHorizontalLine(Canvas c, RecyclerView parent, RecyclerView.State state){
        int left = parent.getPaddingLeft();
        int right = parent.getWidth() - parent.getPaddingRight();

        final int childCount = parent.getChildCount();
        for (int i = 0; i < childCount; i++){
            final View child = parent.getChildAt(i);

            /*
            * 获得child的布局信息
            * Get child layout information
            * */
            final RecyclerView.LayoutParams params = (RecyclerView.LayoutParams)child.getLayoutParams();
            final int top = child.getBottom() + params.bottomMargin;
            final int bottom = top + mDivider.getIntrinsicHeight();

            mDivider.setBounds(left, top, right, bottom);
            mDivider.draw(c);
        }
    }


    /**
     * Draw vertical line.
     * 画垂直线
     * @param c      the c
     * @param parent the parent
     * @param state  the state
     */
    public void drawVerticalLine(Canvas c, RecyclerView parent, RecyclerView.State state){
        int top = parent.getPaddingTop();
        int bottom = parent.getHeight() - parent.getPaddingBottom();

        final int childCount = parent.getChildCount();
        for (int i = 0; i < childCount; i++){
            final View child = parent.getChildAt(i);

            /*
             * 获得child的布局信息
             * Get child layout information
             * */
            final RecyclerView.LayoutParams params = (RecyclerView.LayoutParams)child.getLayoutParams();
            final int left = child.getRight() + params.rightMargin;
            final int right = left + mDivider.getIntrinsicWidth();

            mDivider.setBounds(left, top, right, bottom);
            mDivider.draw(c);
        }
    }

    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        if(mOrientation == HORIZONTAL_LIST){
            /*
            * 画横线，就是往下偏移一个分割线的高度
            * Draw a horizontal line, which is offset by the height of a dividing line
            * */
            outRect.set(0, 0, 0, mDivider.getIntrinsicHeight());
        }else {
            /*
            * 画竖线，就是往右偏移一个分割线的宽度
            * Draw a vertical line, which is offset by the width of a dividing line to the right
            * */
            outRect.set(0, 0, mDivider.getIntrinsicWidth(), 0);
        }
    }
}
