package com.meishe.speaker.bean;

import androidx.annotation.NonNull;

import com.meishe.engine.bean.MeicamAudioClip;
import com.meishe.engine.bean.MeicamVideoClip;


/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate :2021/3/1 15:38
 * @Description :讯飞请求听写参数类 Voice params
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class VoiceParam {
    private String path;
    private double speed = 1.0;
    private long inP;
    private long outP;
    private long trInP;
    private long trOutP;

    public static VoiceParam create(MeicamVideoClip videoClip) {
        long outPoint = (long) (videoClip.getOutPoint() * videoClip.getSpeed());
        return new VoiceParam()
                .setPath(videoClip.getFilePath())
                .setInP(videoClip.getInPoint())
                .setOutP(outPoint)
                .setSpeed(videoClip.getSpeed())
                .setTrInP(videoClip.getTrimIn())
                .setTrOutP(videoClip.getTrimOut());
    }

    public static VoiceParam create(MeicamAudioClip audioClip) {
        return new VoiceParam()
                .setPath(audioClip.getFilePath())
                .setInP(audioClip.getInPoint())
                .setOutP(audioClip.getOutPoint())
                .setTrInP(audioClip.getTrimIn())
                .setTrOutP(audioClip.getTrimOut());
    }

    public String getPath() {
        return path;
    }

    public VoiceParam setPath(String path) {
        this.path = path;
        return this;
    }

    public long getInP() {
        return inP;
    }

    public VoiceParam setInP(long inP) {
        this.inP = inP;
        return this;
    }

    public double getSpeed() {
        return speed;
    }

    public VoiceParam setSpeed(double speed) {
        this.speed = speed;
        return this;
    }

    public long getOutP() {
        return outP;
    }

    public VoiceParam setOutP(long outP) {
        this.outP = outP;
        return this;
    }

    public long getTrInP() {
        return trInP;
    }

    public VoiceParam setTrInP(long trInP) {
        this.trInP = trInP;
        return this;
    }

    public long getTrOutP() {
        return trOutP;
    }

    public VoiceParam setTrOutP(long trOutP) {
        this.trOutP = trOutP;
        return this;
    }

    @NonNull
    @Override
    public String toString() {
        return "VoiceParam(path=" + path + ",inP=" + inP + ",outP=" + outP + ",trInP=" + trInP
                + ",trOutP=" + trOutP + ")";
    }
}
