package com.meishe.user.view.fragment;

import android.view.Gravity;

import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.view.PullToRefreshAndPushToLoadView;
import com.meishe.draft.DraftManager;
import com.meishe.draft.observer.DraftObserver;
import com.meishe.engine.DownloadManager;
import com.meishe.engine.observer.DownLoadObserver;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.user.CloudDraftManager;
import com.meishe.user.R;
import com.meishe.user.bean.CloudDraftData;
import com.meishe.user.view.DownloadConfirmPop;
import com.meishe.user.view.ManageCloudDraftPop;
import com.meishe.user.view.iview.CloudUpDownloadView;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/9 13:28
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CloudUploadFragment extends BaseCloudUpDownloadFragment implements CloudUpDownloadView {
    private int mCurrentClickedPosition;
    private OnDataUpdateListener mOnDataUpdateListener;
    private DraftObserver mDraftObserver;
    private DownLoadObserver<CloudDraftData> mDownloadObserver;

    public void setOnDataUpdateListener(OnDataUpdateListener listener) {
        this.mOnDataUpdateListener = listener;
    }

    public static CloudUploadFragment create(OnDataUpdateListener listener) {
        CloudUploadFragment cloudUploadFragment = new CloudUploadFragment();
        cloudUploadFragment.setOnDataUpdateListener(listener);
        return cloudUploadFragment;
    }

    @Override
    public void initListener() {
        DraftManager.getInstance().registerDraftObserver(mDraftObserver = new DraftObserver() {
            @Override
            public void onDraftChanged() {
                if (mPresenter != null) {
                    mPresenter.getDraftList(0);
                }
            }
        });
        CloudDraftManager.getInstance().registerDownloadObserver(mDownloadObserver = new DownLoadObserver<CloudDraftData>() {

            @Override
            public void onSuccess(String tag, DownloadManager.DownloadParam<CloudDraftData> param) {
                mPresenter.handleDownloadData(tag, param, mDraftObserver);
            }
        });
        mAdapter.setOnItemClickListener((adapter, view, position) -> {
            mCurrentClickedPosition = position;
            CloudDraftData item = mAdapter.getItem(position);
            if (item.getState() != CloudDraftData.STATE_DOWNLOADED) {
                if (item.isHaveOld()) {
                    showReplaceDialog();
                } else {
                    downloadDraft(position, item);
                }
            }
        });
        mAdapter.setOnItemChildClickListener((adapter, view, position) -> {
            if (view.getId() == R.id.more_click_view) {
                mCurrentClickedPosition = position;
                showManageDraftDialog(true);
            } else if (view.getId() == R.id.iv_select_clicked) {
                CloudDraftData item = mAdapter.getItem(position);
                item.setSelected(!item.isSelected());
                mAdapter.notifyItemChanged(position);
                if (mOnDataUpdateListener != null) {
                    mOnDataUpdateListener.onDataSelected();
                }
            }
        });
        mRefreshLayout.setOnRefreshAndLoadMoreListener(new PullToRefreshAndPushToLoadView.PullToRefreshAndPushToLoadMoreListener() {
            @Override
            public void onRefresh() {
                mPresenter.getDraftList(0);
            }

            @Override
            public void onLoadMore() {
                if (!loadMoreData(false)) {
                    finishLoading();
                }
            }
        });
    }


    private void downloadDraft(int position, CloudDraftData item) {
        item.setDownloadFolder(mPresenter.getDownloadFolder(item));
        item.setFootageFolder(mPresenter.getFootageDownloadFolder());
        if (mPresenter.downloadDraft(item)) {
            item.setProgress(0);
            item.setState(CloudDraftData.STATE_DOWNLOADING);
            mAdapter.notifyItemChanged(position);
        }
    }

    private void showReplaceDialog() {
        DownloadConfirmPop.Builder builder = new DownloadConfirmPop.Builder();
        builder.build(getActivity(), new IUserPlugin.OnEventListener<Object>() {
            @Override
            public void onConfirm(Object obj) {
                //覆盖下载
                //Replace old draft.
                CloudDraftData item = mAdapter.getItem(mCurrentClickedPosition);
                downloadDraft(mCurrentClickedPosition, item);
            }

            @Override
            public void onCancel() {
                //下载为新草稿
                //Download and create new draft.
                CloudDraftData item = mAdapter.getItem(mCurrentClickedPosition);
                item.setNewUuid(String.valueOf(UUID.randomUUID()).toUpperCase());
                downloadDraft(mCurrentClickedPosition, item);

            }
        }).show();
    }

    @Override
    public void onResume() {
        super.onResume();
        mPresenter.getDraftList(TYPE_UPLOADED);
    }

    @Override
    protected void onLazyLoad() {
        super.onLazyLoad();
    }

    @Override
    public void refreshData(boolean isByUser) {
        if (isByUser) {
            mRefreshLayout.autoRefresh();
        } else {
            mPresenter.getDraftList(TYPE_UPLOADED);
        }
    }

    @Override
    public void onDelete() {
        deleteDraft();
    }

    @Override
    public void onDownload() {
        downloadDraft();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        DraftManager.getInstance().unregisterDraftObserver(mDraftObserver);
        CloudDraftManager.getInstance().unRegisterDownloadObserver(mDownloadObserver);
    }

    @Override
    protected void initData() {
    }

    @Override
    public void onNewDataBack(List<CloudDraftData> data) {
        super.onNewDataBack(data);
        int itemCount = mAdapter.getItemCount();
        if (mOnDataUpdateListener != null) {
            mOnDataUpdateListener.onDataUpDate(itemCount);
        }
    }

    @Override
    public void onMoreDataBack(List<CloudDraftData> data) {
        super.onMoreDataBack(data);
        int itemCount = mAdapter.getItemCount();
        if (mOnDataUpdateListener != null) {
            mOnDataUpdateListener.onDataUpDate(itemCount);
        }
    }

    @Override
    public void onDownloadFailed(String tag) {
        List<CloudDraftData> data = mAdapter.getData();
        if (CommonUtils.isEmpty(data)) {
            return;
        }
        for (int index = 0; index < data.size(); index++) {
            CloudDraftData datum = data.get(index);
            if (tag.equals(datum.getUuid())) {
                datum.setState(CloudDraftData.STATE_UPLOADED);
                mAdapter.notifyItemChanged(index);
            }
        }
        ToastUtils.make().setGravity(Gravity.CENTER, 0, 0).show(R.string.download_fail);
    }

    @Override
    public void onDownloadProgress(String tag, int progress) {
        List<CloudDraftData> data = mAdapter.getData();
        if (CommonUtils.isEmpty(data)) {
            return;
        }
        for (int index = 0; index < data.size(); index++) {
            CloudDraftData datum = data.get(index);
            if (tag.equals(datum.getUuid())) {
                datum.setProgress(progress);
                mAdapter.notifyItemChanged(index, 1);
            }
        }
    }

    @Override
    public void onDownloadSuccess(String tag, CloudDraftData draftData) {
        List<CloudDraftData> data = mAdapter.getData();
        for (int index = 0; index < data.size(); index++) {
            CloudDraftData datum = data.get(index);
            if (tag.equals(datum.getUuid())) {
                datum.setState(draftData.getState());
                mAdapter.notifyItemChanged(index);
            }
        }
    }

    /**
     * 显示管理草稿的弹窗
     * Displays the dialog for the administration draft
     */
    private void showManageDraftDialog(boolean needUpload) {
        if (getContext() == null) {
            return;
        }
        ManageCloudDraftPop.create(getContext(), needUpload, new ManageCloudDraftPop.ManageListener() {
            @Override
            public void onDelete() {
                CloudDraftData item = mAdapter.getItem(mCurrentClickedPosition);
                item.setState(CloudDraftData.STATE_DELETING);
                mAdapter.notifyItemChanged(mCurrentClickedPosition);
                List<CloudDraftData> data = new ArrayList<>();
                data.add(item);
                mPresenter.deleteDraft(data, new CloudDraftManager.CloudDeleteCallBack() {
                    @Override
                    public void onSuccess() {
                        mAdapter.remove(mCurrentClickedPosition);
                    }

                    @Override
                    public void onError() {
                        ToastUtils.showShort("删除失败");
                    }
                });
            }

            @Override
            public void onUpload() {
                CloudDraftData item = mAdapter.getItem(mCurrentClickedPosition);
                if (item.isHaveOld()) {
                    showReplaceDialog();
                } else {
                    downloadDraft(mCurrentClickedPosition, item);
                }
            }
        }).show();
    }

    private boolean loadMoreData(final boolean needUpdate) {
        return mPresenter.loadMoreData();
    }

    @Override
    public void deleteDraft() {
        if (mAdapter == null) {
            return;
        }
        List<CloudDraftData> deleteList = new ArrayList<>();
        List<CloudDraftData> data = mAdapter.getData();
        for (CloudDraftData datum : data) {
            if (datum.isSelected()) {
                deleteList.add(datum);
                datum.setState(CloudDraftData.STATE_DELETING);
                mAdapter.notifyItemChanged(mCurrentClickedPosition);
            }
        }
        mPresenter.deleteDraft(deleteList, new CloudDraftManager.CloudDeleteCallBack() {
            @Override
            public void onSuccess() {
                List<CloudDraftData> data = mAdapter.getData();
                for (int index = 0; index < data.size(); index++) {
                    CloudDraftData datum = data.get(index);
                    if (datum.isSelected()) {
                        mAdapter.remove(index);
                    }
                }
            }

            @Override
            public void onError() {

            }
        });
    }

    private void downloadDraft(){
        if (mAdapter == null) {
            return;
        }
        List<CloudDraftData> data = mAdapter.getData();
        for (int index = 0; index < data.size(); index++) {
            CloudDraftData dataItem = data.get(index);
            if (dataItem.isSelected()) {
                downloadDraft(index, dataItem);
            }
        }
    }

    public interface OnDataUpdateListener {
        void onDataUpDate(int count);
        void onDataSelected();
    }
}
