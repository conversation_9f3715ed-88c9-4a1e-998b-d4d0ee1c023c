<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/iv_cover"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:contentDescription="@null" />

    <ImageView
        android:id="@+id/iv_portrait"
        android:layout_width="@dimen/dp_px_65"
        android:layout_height="@dimen/dp_px_65"
        android:layout_below="@+id/tv_description"
        android:layout_marginTop="@dimen/dp_px_18"
        android:contentDescription="@null"
        android:scaleType="centerCrop" />

    <TextView
        android:id="@+id/tv_template_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/iv_cover"
        android:layout_marginTop="@dimen/dp_px_20"
        android:layout_marginRight="@dimen/dp_px_42"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_39" />

    <TextView
        android:id="@+id/tv_description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_template_name"
        android:layout_marginTop="@dimen/dp_px_18"
        android:layout_marginRight="@dimen/dp_px_42"
        android:singleLine="true"
        android:textColor="@color/white_d1d"
        android:textSize="@dimen/sp_px_33" />

    <TextView
        android:id="@+id/tv_used_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@+id/iv_cover"
        android:layout_marginStart="@dimen/dp_px_20"
        android:layout_marginLeft="@dimen/dp_px_20"
        android:layout_marginEnd="@dimen/dp_px_20"
        android:layout_marginRight="@dimen/dp_px_20"
        android:layout_marginBottom="@dimen/dp_px_25"
        android:background="@drawable/bg_rectangle_round_black_80282828"
        android:paddingStart="@dimen/dp_px_15"
        android:paddingLeft="@dimen/dp_px_15"
        android:paddingTop="@dimen/dp_px_5"
        android:paddingEnd="@dimen/dp_px_15"
        android:paddingRight="@dimen/dp_px_15"
        android:paddingBottom="@dimen/dp_px_5"
        android:singleLine="true"
        android:textColor="@color/white_d1d"
        android:textSize="@dimen/sp_px_30" />

    <TextView
        android:id="@+id/tv_user_name"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_45"
        android:layout_alignTop="@+id/iv_portrait"
        android:layout_marginStart="@dimen/dp_px_18"
        android:layout_marginLeft="@dimen/dp_px_18"
        android:layout_marginTop="@dimen/dp_px_10"
        android:layout_toEndOf="@+id/iv_portrait"
        android:layout_toRightOf="@+id/iv_portrait"
        android:singleLine="true"
        android:textColor="@color/white_d1d"
        android:textSize="@dimen/sp_px_30" />

    <TextView
        android:id="@+id/tv_asset_purchased"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@+id/iv_cover"
        android:layout_alignEnd="@+id/iv_cover"
        android:layout_marginStart="@dimen/dp_px_20"
        android:layout_marginEnd="@dimen/dp_px_23"
        android:layout_marginBottom="@dimen/dp_px_25"
        android:background="@drawable/bg_rectangle_round_black_80282828"
        android:paddingStart="@dimen/dp_px_15"
        android:paddingLeft="@dimen/dp_px_15"
        android:paddingTop="@dimen/dp_px_5"
        android:paddingEnd="@dimen/dp_px_15"
        android:paddingRight="@dimen/dp_px_15"
        android:paddingBottom="@dimen/dp_px_5"
        android:singleLine="true"
        android:text="@string/asset_purchased"
        android:textColor="@color/white_d1d"
        android:textSize="@dimen/sp_px_30"
        android:visibility="gone"/>
</RelativeLayout>
