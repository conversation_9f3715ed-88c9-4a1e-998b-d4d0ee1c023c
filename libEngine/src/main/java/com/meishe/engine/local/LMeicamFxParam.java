package com.meishe.engine.local;


import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/7/3 21:46
 */
public class LMeicamFxParam<T> implements Cloneable, Serializable {
    String type;
    String key;
    /**
     * 这个可能是float 或者 boolean 或者是一组数字（region）
     */
    T value;


    public LMeicamFxParam(String type, String key, T value) {
        this.key = key;
        setValue(value);
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public T getValue() {
        return value;
    }

    public void setValue(T value) {
        if (value instanceof Float && Float.isNaN((Float) value)) {
            return;
        }
        if (value instanceof Double && Double.isNaN((Double) value)) {
            return;
        }
        this.value = value;
    }

    @Override
    public boolean equals(Object obj) {
        return super.equals(obj);
    }

}
