package com.meishe.engine.command;

import com.meishe.annotation.Undo;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.ClipInfo;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.engine.bean.MeicamCompoundCaptionClip;
import com.meishe.engine.bean.MeicamStickerCaptionTrack;
import com.meishe.engine.bean.MeicamStickerClip;
import com.meishe.engine.bean.MeicamTimeline;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/8/24 16:07
 * @Description :字幕贴纸轨道命令 The caption sticker track command.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CaptionStickerTrackCommand {
    private static String TAG = "captionStickerTrack";


    public static boolean removeClip(MeicamStickerCaptionTrack captionTrack, boolean removeNvsObject, long inPoint, boolean... needSavaOperate) {
        ClipInfo<?> clipInfo = captionTrack.getCaptionStickerClip(inPoint);
        return removeClip(captionTrack, clipInfo, removeNvsObject, needSavaOperate);
    }

    public static boolean removeClip(MeicamStickerCaptionTrack captionTrack, long inPoint, boolean... needSavaOperate) {
        return removeClip(captionTrack, true, inPoint, needSavaOperate);
    }

    /**
     * Remove caption boolean.
     * 删除字幕
     *
     * @param captionTrack    the caption track 字幕轨道
     * @param clipInfo        the clip info
     * @param removeNvsObject the remove nvs object true移除 false不移除
     * @param needSavaOperate the need sava operate 是否需要保存操作
     * @return the boolean 是否删除成功
     */
    @Undo(className = "CaptionStickerRemoveCommand", function = "addClip",
            param = {"ClipInfo<?>|clipInfo", "boolean|addNvsObject", "boolean|needSaveOperate"})
    public static boolean removeClip(MeicamStickerCaptionTrack captionTrack, ClipInfo<?> clipInfo, boolean removeNvsObject, boolean... needSavaOperate) {
        boolean success = captionTrack.removeStickerCaptionClip(clipInfo, removeNvsObject);
        if (success && CommandUtil.needSaveOperate(needSavaOperate)) {
            String tag = getTag(captionTrack);
            String tagAndKey = tag + clipInfo.getInPoint();
            // 此处要使用clone数据，因为如果后边还有更改inPoint、outPoint的指令，对象里面的inPoint、outPoint会发生变化，之后调用saveOperation就会把更改后的inPoint和outPoint保存
            // removeClip undo redo的时候就会找不对对应的clip,另外undo、redo的时候底层对象已经为空了，需要移除或者添加底层对象,所以第二个参数为true。
            Object[] unDoParam = new Object[]{clipInfo.clone(false), true, false};
            Object[] redoParam = new Object[]{clipInfo.clone(false), true};
            CommandUtil.saveOperate("CaptionStickerRemoveCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        return success;
    }


    @Undo(className = "StickerAddCommand", function = "removeClip",
            param = {"long|inPoint", " boolean...|needSavaOperate"})
    public static MeicamStickerClip addSticker(MeicamStickerCaptionTrack track, long inPoint, long outPoint, String packageId, boolean isCustomSticker, String customAnimatedStickerImagePath, boolean... needSavaOperate) {
        MeicamStickerClip stickerClip = track.addSticker(inPoint, outPoint, packageId, isCustomSticker, customAnimatedStickerImagePath);
        if (stickerClip == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSavaOperate)) {
            String tag = getTag(track);
            String tagAndKey = tag + inPoint;
            Object[] unDoParam = new Object[]{inPoint, new boolean[]{false}};
            Object[] redoParam = new Object[]{inPoint, outPoint, packageId, isCustomSticker, customAnimatedStickerImagePath};
            CommandUtil.saveOperate("StickerAddCommand", unDoParam, redoParam, tag, tagAndKey);
        }

        return stickerClip;
    }

    @Undo(className = "StickerAddCommand1", function = "removeClip",
            param = {"long|inPoint", " boolean...|needSavaOperate"})
    public static MeicamStickerClip addSticker(MeicamStickerCaptionTrack track, MeicamStickerClip stickerClip, boolean... needSavaOperate) {
        MeicamStickerClip newStickerClip = track.addSticker(stickerClip);
        if (newStickerClip == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSavaOperate)) {
            String tag = getTag(track);
            String tagAndKey = tag + stickerClip.getInPoint();
            Object[] unDoParam = new Object[]{newStickerClip.getInPoint(), new boolean[]{false}};
            Object[] redoParam = new Object[]{newStickerClip};
            CommandUtil.saveOperate("StickerAddCommand1", unDoParam, redoParam, tag, tagAndKey);
        }
        return newStickerClip;
    }

    @Undo(className = "CompCaptionAddCommand", function = "removeClip",
            param = {"long|inPoint", " boolean...|needSavaOperate"})
    public static MeicamCompoundCaptionClip addCompoundCaption(MeicamStickerCaptionTrack track, MeicamCompoundCaptionClip captionClip, boolean... needSavaOperate) {
        MeicamCompoundCaptionClip compCaption = track.addCompoundCaption(captionClip);
        if (compCaption == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSavaOperate)) {
            String tag = getTag(track);
            String tagAndKey = tag + captionClip.getInPoint();

            Object[] unDoParam = new Object[]{compCaption.getInPoint(), new boolean[]{false}};
            Object[] redoParam = new Object[]{compCaption};
            CommandUtil.saveOperate("CompCaptionAddCommand", unDoParam, redoParam, tag, tagAndKey);
        }

        return compCaption;
    }

    @Undo(className = "CompCaptionAddCommand1", function = "removeClip",
            param = {"long|inPoint", " boolean...|needSavaOperate"})
    public static MeicamCompoundCaptionClip addCompoundCaption(MeicamStickerCaptionTrack track, long inPoint, long outPoint, String style, boolean... needSavaOperate) {
        MeicamCompoundCaptionClip compCaption = track.addCompoundCaption(inPoint, outPoint, style);
        if (compCaption == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSavaOperate)) {
            String tag = getTag(track);
            String tagAndKey = tag + inPoint;

            Object[] unDoParam = new Object[]{compCaption.getInPoint(), new boolean[]{false}};
            Object[] redoParam = new Object[]{inPoint, outPoint, style};
            CommandUtil.saveOperate("CompCaptionAddCommand1", unDoParam, redoParam, tag, tagAndKey);
        }

        return compCaption;
    }

    @Undo(className = "CaptionAddModularCommand", function = "removeClip",
            param = {"long|inPoint", " boolean...|needSavaOperate"})
    public static MeicamCaptionClip addModularCaption(MeicamStickerCaptionTrack track, String text, long inPoint, long outPoint, boolean... needSavaOperate) {
        MeicamCaptionClip captionClip = track.addModularCaption(text, inPoint, outPoint);
        if (captionClip == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSavaOperate)) {
            String tag = getTag(track);
            String tagAndKey = tag + inPoint;
            Object[] unDoParam = new Object[]{inPoint, new boolean[]{false}};
            Object[] redoParam = new Object[]{text, inPoint, outPoint};
            CommandUtil.saveOperate("CaptionAddModularCommand", unDoParam, redoParam, tag, tagAndKey);
        }

        return captionClip;
    }

    @Undo(className = "CaptionAddModularCommand1", function = "removeClip",
            param = {"long|inPoint", " boolean...|needSavaOperate"})
    public static MeicamCaptionClip addModularCaption(MeicamStickerCaptionTrack track, MeicamCaptionClip captionClip, boolean... needSavaOperate) {
        MeicamCaptionClip newCaption = track.addModularCaption(captionClip);
        if (newCaption == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSavaOperate)) {
            String tag = getTag(track);
            String tagAndKey = tag + captionClip.getInPoint();
            Object[] unDoParam = new Object[]{captionClip.getInPoint(), new boolean[]{false}};
            Object[] redoParam = new Object[]{captionClip};
            CommandUtil.saveOperate("CaptionAddModularCommand1", unDoParam, redoParam, tag, tagAndKey);
        }
        return newCaption;
    }

    @Undo(className = "CaptionAddNormalCommand", function = "removeClip",
            param = {"long|inPoint", " boolean...|needSavaOperate"})
    public static MeicamCaptionClip addNormalCaption(MeicamStickerCaptionTrack track, String text, long inPoint, long outPoint, boolean... needSavaOperate) {
        MeicamCaptionClip captionClip = track.addNormalCaption(text, inPoint, outPoint);
        if (captionClip == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSavaOperate)) {
            String tag = getTag(track);
            String tagAndKey = tag + inPoint;
            Object[] unDoParam = new Object[]{inPoint, new boolean[]{false}};
            Object[] redoParam = new Object[]{text, inPoint, outPoint};
            CommandUtil.saveOperate("CaptionAddNormalCommand", unDoParam, redoParam, tag, tagAndKey);
        }

        return captionClip;
    }

    /**
     * Add clip.
     * 添加贴纸或字幕片段
     *
     * @param track the track
     * @param clip  the clip
     */
    @Undo(className = "CaptionStickerAddCommand", function = "removeClip",
            param = {"ClipInfo<?>|clipInfo", "boolean|addNvsObject", "boolean...|needSaveOperate"})
    public static ClipInfo<?> addClip(MeicamStickerCaptionTrack track, ClipInfo<?> clip, boolean addNvsObject, boolean needSaveOperate) {
        if (needSaveOperate) {
            String tag = getTag(track);
            String tagAndKey = tag + clip.getInPoint();
            // 此处要使用clone数据，因为如果后边还有更改inPoint、outPoint的指令，对象里面的inPoint、outPoint会发生变化，之后调用saveOperation就会把更改后的inPoint和outPoint保存
            // 执行addClip undo redo的时候就会找不对对应的clip，另外undo、redo的时候底层对象已经为空了，需要移除或者添加底层对象，所以说第二个参数为true。
            Object[] unDoParam = new Object[]{clip.clone(false), true, new boolean[]{false}};
            Object[] redoParam = new Object[]{clip.clone(false), true};
            CommandUtil.saveOperate("CaptionStickerAddCommand", unDoParam, redoParam, tag, tagAndKey);
        }

        return track.addCaptionSticker(clip, addNvsObject);
    }

    @Undo(className = "CaptionStickerReSortCommand", function = "reSort",
            param = {"boolean...|needSaveOperate"})
    public static void reSort(MeicamStickerCaptionTrack track, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(track);
            Object[] unDoParam = new Object[]{new boolean[]{false}};
            Object[] redoParam = new Object[]{};
            CommandUtil.saveOperate("CaptionStickerReSortCommand", unDoParam, redoParam, tag, tag);
        }
        track.reSort();
    }


    private static String getTag(MeicamStickerCaptionTrack captionTrack) {
        return TAG + captionTrack.getIndex();
    }

    /**
     * Get it by tag meicam sticker caption track.
     *
     * @param tag the tag
     * @return the meicam sticker caption track
     */
    public static MeicamStickerCaptionTrack getItByTag(String tag) {
        int trackIndex = Integer.parseInt(tag.replaceAll(TAG, ""));
        MeicamTimeline currentTimeline = EditorEngine.getInstance().getCurrentTimeline();
        if (currentTimeline == null) {
            return null;
        }
        MeicamStickerCaptionTrack stickCaptionTrack = currentTimeline.findStickCaptionTrack(trackIndex);
        if (stickCaptionTrack == null) {
            stickCaptionTrack = currentTimeline.addStickCaptionTrack(trackIndex);
        }
        return stickCaptionTrack;
    }

}
