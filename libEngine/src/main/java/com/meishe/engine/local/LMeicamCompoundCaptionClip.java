package com.meishe.engine.local;

import android.graphics.PointF;

import androidx.annotation.NonNull;

import com.google.gson.annotations.SerializedName;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.util.DeepCopyUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/7/4 11:57
 */
public class LMeicamCompoundCaptionClip extends LClipInfo implements Cloneable, Serializable {
    @SerializedName("compoundCaptionItems")
    private List<LMeicamCompoundCaptionItem> mCompoundCaptionItems = new ArrayList<>();
    @SerializedName("styleId")
    private String styleDesc;
    private float scaleX = 1;
    private float scaleY = 1;
    private float zValue;
    private float rotation = 0;
    private float translationX = 0;
    private float translationY = 0;
    //用于更新子轨的文字
    private int itemSelectedIndex = 0;
    /**
     * The anchor
     * 锚点
     */
    private PointF assetAnchor;
    public LMeicamCompoundCaptionClip(String styleDesc) {
        super(CommonData.CLIP_COMPOUND_CAPTION);
        this.styleDesc = styleDesc;
    }

    @Override
    public float getzValue() {
        return zValue;
    }

    @Override
    public void setzValue(float zValue) {
        this.zValue = zValue;
    }

    public List<LMeicamCompoundCaptionItem> getCompoundCaptionItems() {
        return mCompoundCaptionItems;
    }

    public void setCompoundCaptionItems(List<LMeicamCompoundCaptionItem> compoundCaptionItems) {
        this.mCompoundCaptionItems = compoundCaptionItems;
    }

    public String getStyleDesc() {
        return styleDesc;
    }

    public void setStyleDesc(String styleDesc) {
        this.styleDesc = styleDesc;
    }

    public float getScaleX() {
        return scaleX;
    }

    public void setScaleX(float scaleX) {
        if (Float.isNaN(scaleX)) {
            return;
        }
        this.scaleX = scaleX;
    }

    public float getScaleY() {
        return scaleY;
    }

    public void setScaleY(float scaleY) {
        if (Float.isNaN(scaleY)) {
            return;
        }
        this.scaleY = scaleY;
    }

    public float getRotation() {
        return rotation;
    }

    public void setRotation(float rotation) {
        if (Float.isNaN(rotation)) {
            return;
        }
        this.rotation = rotation;
    }

    public float getTranslationX() {
        return translationX;
    }

    public void setTranslationX(float translationX) {
        if (Float.isNaN(translationX)) {
            return;
        }
        this.translationX = translationX;
    }

    public float getTranslationY() {
        return translationY;
    }

    public void setTranslationY(float translationY) {
        if (Float.isNaN(translationY)) {
            return;
        }
        this.translationY = translationY;
    }


    public int getItemSelectedIndex() {
        return itemSelectedIndex;
    }

    public void setItemSelectedIndex(int itemSelectedIndex) {
        this.itemSelectedIndex = itemSelectedIndex;
    }

    public PointF getAssetAnchor() {
        return assetAnchor;
    }

    public void setAssetAnchor(PointF assetAnchor) {
        this.assetAnchor = assetAnchor;
    }

    @NonNull
    @Override
    public Object clone() {
        return DeepCopyUtil.deepClone(this);
    }

}
