<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:gravity="bottom"
    android:layout_height="@dimen/dp_px_800"
    android:background="@color/black"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_marginBottom="@dimen/dp_px_160"
        android:layout_height="@dimen/dp_px_140"
        android:gravity="center_vertical"
        android:orientation="horizontal">


        <TextView
            android:id="@+id/tv_start"
            android:layout_width="@dimen/dp_px_100"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/dp_px_75"
            android:layout_marginStart="@dimen/dp_px_108"
            android:gravity="center"
            android:text="@string/num_0"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_px_30" />

        <com.meishe.base.view.SeekBarTextView
            android:id="@+id/seek_bar_textview"
            android:layout_marginTop="@dimen/dp_px_15"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_toStartOf="@+id/tv_end"
            android:layout_toEndOf="@+id/tv_start" />

        <TextView
            android:id="@+id/tv_end"
            android:layout_marginTop="@dimen/dp_px_75"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/dp_px_108"
            android:gravity="center"
            android:text="@string/num_200"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_px_30" />

    </RelativeLayout>


    <View
        android:id="@+id/v_bottom_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_1"
        android:background="@color/gray_363" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_px_45"
        android:layout_marginBottom="@dimen/dp_px_138">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_gravity="center_horizontal"
            android:gravity="center"
            android:text="@string/tv_view_edit_volume_title"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_px_42" />

        <ImageView
            android:id="@+id/iv_confirm"
            android:layout_width="@dimen/dp_px_60"
            android:layout_height="@dimen/dp_px_60"
            android:layout_alignParentEnd="true"
            android:layout_gravity="end"
            android:layout_marginEnd="@dimen/dp_px_45"
            android:background="@mipmap/ic_confirm"
            android:contentDescription="@null" />
    </RelativeLayout>


</LinearLayout>