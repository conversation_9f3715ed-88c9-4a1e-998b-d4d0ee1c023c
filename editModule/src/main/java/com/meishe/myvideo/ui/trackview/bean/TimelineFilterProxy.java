package com.meishe.myvideo.ui.trackview.bean;

import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.MeicamTimelineVideoFilterAndAdjustClip;
import com.meishe.engine.command.ClipCommand;
import com.meishe.myvideo.R;
import com.meishe.myvideo.ui.bean.BaseUIClip;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/5/25 11:04
 * @Description :滤镜代理 filter proxy
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class TimelineFilterProxy extends BaseUIClip {

    private MeicamTimelineVideoFilterAndAdjustClip mFilterAndAdjustClip;

    public TimelineFilterProxy(MeicamTimelineVideoFilterAndAdjustClip clip, int trackIndex) {
        super(CommonData.CLIP_FILTER, trackIndex);
        if(clip != null){
            super.setInPoint(clip.getInPoint());
            super.setOutPoint(clip.getOutPoint());
        }
        this.mFilterAndAdjustClip = clip;
    }

    @Override
    public int getSubType() {
        return INVALID;
    }

    @Override
    public int getClipIndexInTrack() {
        if (mFilterAndAdjustClip != null) {
            return mFilterAndAdjustClip.getIndex();
        }
        return INVALID;
    }

    @Override
    public long getInPoint() {
        if (mFilterAndAdjustClip != null) {
            return mFilterAndAdjustClip.getInPoint();
        }
        return super.getInPoint();
    }

    @Override
    public void setInPoint(long inPoint) {
        if (mFilterAndAdjustClip != null) {
            ClipCommand.setInPoint(mFilterAndAdjustClip, inPoint);
        }
        super.setInPoint(inPoint);
    }

    @Override
    public void setOutPoint(long outPoint) {
        if (mFilterAndAdjustClip != null) {
            ClipCommand.setOutPoint(mFilterAndAdjustClip, outPoint);
        }
        super.setOutPoint(outPoint);
    }

    @Override
    public long getOutPoint() {
        if (mFilterAndAdjustClip != null) {
            return mFilterAndAdjustClip.getOutPoint();
        }
        return super.getOutPoint();
    }

    @Override
    public double getSpeed() {
        return super.getSpeed();
    }

    @Override
    public String getIconFilePath() {
        return "";
    }

    @Override
    public String getFilePath() {
        return "";
    }

    @Override
    public float[] getRecordArray() {
        return new float[0];
    }

    @Override
    public void setDuration(long duration) {
        super.setDuration(duration);
    }

    @Override
    public long getDuration() {
        if (mFilterAndAdjustClip != null) {
           return mFilterAndAdjustClip.getOutPoint() - mFilterAndAdjustClip.getInPoint();
        }
        return super.getDuration();
    }

    @Override
    public long getFadeIn() {
        return 0;
    }

    @Override
    public long getFadeOut() {
        return 0;
    }

    @Override
    public String getDisplayName() {
        return mFilterAndAdjustClip.getText();
    }

    @Override
    public boolean canExceedLength() {
        return true;
    }

    @Override
    public boolean canDrag() {
        return true;
    }

    @Override
    public int getBackGroundColor() {
        return R.color.track_background_color_filter;
    }
}
