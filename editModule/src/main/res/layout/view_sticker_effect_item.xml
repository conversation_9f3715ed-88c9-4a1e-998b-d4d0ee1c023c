<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">


    <RelativeLayout
        android:id="@+id/rl_more"
        android:layout_width="@dimen/dp64"
        android:layout_height="@dimen/dp64"
        android:background="@drawable/asset_effect_bg"
        android:visibility="gone">

        <ImageView
            android:layout_width="@dimen/dp20"
            android:layout_height="@dimen/dp20"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dp15"
            android:background="@mipmap/ic_more" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp17"
            android:layout_alignParentBottom="true"
            android:background="@color/effect_name_bg">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:ellipsize="end"
                android:gravity="center"
                android:singleLine="true"
                android:text="@string/more"
                android:textColor="@color/white_8"
                android:textSize="@dimen/menu_item_text_size" />

        </RelativeLayout>

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_content"
        android:layout_width="@dimen/dp64"
        android:layout_height="@dimen/dp64"
        android:background="@drawable/asset_effect_bg">

        <ImageView
            android:id="@+id/icon"
            android:layout_width="@dimen/dp35"
            android:layout_height="@dimen/dp35"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dp8"/>

        <ImageView
            android:id="@+id/iv_select_bg"
            android:layout_width="@dimen/dp64"
            android:layout_height="@dimen/dp64" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp17"
            android:layout_alignParentBottom="true"
            android:background="@color/effect_name_bg">

            <TextView
                android:id="@+id/name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:ellipsize="end"
                android:gravity="center"
                android:singleLine="true"
                android:textColor="@color/white_8"
                android:textSize="@dimen/menu_item_text_size" />

        </RelativeLayout>
    </RelativeLayout>

</RelativeLayout>