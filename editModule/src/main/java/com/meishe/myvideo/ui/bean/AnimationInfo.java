package com.meishe.myvideo.ui.bean;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.meishe.engine.bean.AnimationData;

import static com.meishe.engine.asset.bean.AssetInfo.ASSET_ANIMATION_GROUP;
import static com.meishe.engine.asset.bean.AssetInfo.ASSET_ANIMATION_IN;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/4/12 17:32
 * @Description :动画信息 Animation info
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class AnimationInfo {
    public static int ANIMATION_IN = 0;
    public static int ANIMATION_OUT = 1;
    public static int ANIMATION_GROUP = 2;
    /**
     * 动画类型
     * Animation type
     */
    private int type = -1;
    /**
     * 组合动画和入动画的入点
     * In point
     */
    private long inPoint;
    /**
     * 组合动画和入动画的出点
     * Out point
     */
    private long outPoint;
    /**
     * 出动画的入点
     */
    private long inPoint2;
    /**
     * 出动画的出点
     */
    private long outPoint2;

    /**
     * ASSET_ANIMATION_IN 代表是入动画 ASSET_ANIMATION_GROUP代表是组合动画
     * ASSET_ ANIMATION_ IN stands for input animation ASSET_ ANIMATION_ GROUP stands for composite animation
     */
    private int isAnimationIn = -1;

    public long getInPoint() {
        return inPoint;
    }

    public void setInPoint(long inPoint) {
        this.inPoint = inPoint;
    }

    public long getOutPoint() {
        return outPoint;
    }

    public void setOutPoint(long outPoint) {
        this.outPoint = outPoint;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }


    public boolean isEmpty() {
        return (outPoint - inPoint <= 0) && (outPoint2 - inPoint2 <= 0);
    }

    public long getInPoint2() {
        return inPoint2;
    }

    public void setInPoint2(long inPoint2) {
        this.inPoint2 = inPoint2;
    }

    public long getOutPoint2() {
        return outPoint2;
    }

    public void setOutPoint2(long outPoint2) {
        this.outPoint2 = outPoint2;
    }

    public boolean isAnimationIn() {
        return isAnimationIn == ASSET_ANIMATION_IN;
    }

    public void setIsAnimationIn(boolean isAnimationIn) {
        this.isAnimationIn = isAnimationIn ? ASSET_ANIMATION_IN : ASSET_ANIMATION_GROUP;
    }

    /**
     * 是否存在 组合动画 或者入动画
     * Whether there is composite animation or input animation
     *
     * @return the boolean
     */
    public boolean hasGroupOrInAnimation() {
        return inPoint == 0 && outPoint > 0;
    }

    /**
     * 是否存在出动画
     * Whether there is animation
     *
     * @return the boolean
     */
    public boolean hasOutAnimation() {
        return inPoint2 != 0 || outPoint != 0;
    }

    public void setTempType() {
        if (getInPoint() > 0) {
            setType(AnimationInfo.ANIMATION_OUT);
        } else if (getOutPoint() == (getOutPoint() - getInPoint())) {
            setType(AnimationInfo.ANIMATION_GROUP);
        } else {
            setType(AnimationInfo.ANIMATION_IN);
        }
    }

    public AnimationInfo copy(AnimationData animationData) {
        this.setInPoint(animationData.getInPoint());
        this.setOutPoint(animationData.getOutPoint());
        this.setInPoint2(animationData.getInPoint2());
        this.setOutPoint2(animationData.getOutPoint2());
        this.setIsAnimationIn(animationData.getIsAnimationIn());
        return this;
    }

    public AnimationInfo copy() {
        Gson gson = new Gson();
        return gson.fromJson(gson.toJson(this), getClass());
    }

    public void clear() {
        inPoint = 0;
        outPoint = 0;
        type = -1;
    }

    @NonNull
    @Override
    public String toString() {
        return "AnimationInfo{type=" + type + ",inPoint=" + inPoint + ",outPoint=" + outPoint + "}";
    }
}
