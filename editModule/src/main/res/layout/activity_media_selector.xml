<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/black">

    <!-- 顶部导航栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_120"
        android:background="@color/black"
        android:paddingTop="@dimen/dp_px_20">

        <!-- 左侧关闭按钮 -->
        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="@dimen/dp_px_80"
            android:layout_height="@dimen/dp_px_80"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/dp_px_20"
            android:layout_marginLeft="@dimen/dp_px_20"
            android:padding="@dimen/dp_px_20"
            android:src="@mipmap/ic_close_white"
            android:contentDescription="@string/close"
            android:background="?android:attr/selectableItemBackgroundBorderless" />

        <!-- 中间的切换按钮容器 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_px_80"
            android:layout_centerInParent="true"
            android:orientation="horizontal"
            android:background="@drawable/bg_selector_button_container">

            <!-- 相机按钮 -->
            <ImageView
                android:id="@+id/iv_camera"
                android:layout_width="@dimen/dp_px_120"
                android:layout_height="@dimen/dp_px_80"
                android:padding="@dimen/dp_px_20"
                android:src="@drawable/selector_camera_button"
                android:contentDescription="@string/camera"
                android:background="?android:attr/selectableItemBackgroundBorderless" />

            <!-- 手机按钮 -->
            <ImageView
                android:id="@+id/iv_phone"
                android:layout_width="@dimen/dp_px_120"
                android:layout_height="@dimen/dp_px_80"
                android:padding="@dimen/dp_px_20"
                android:src="@drawable/selector_phone_button"
                android:contentDescription="@string/phone"
                android:background="?android:attr/selectableItemBackgroundBorderless" />

        </LinearLayout>

    </RelativeLayout>

    <!-- Fragment容器 -->
    <FrameLayout
        android:id="@+id/fl_fragment_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

</LinearLayout>
