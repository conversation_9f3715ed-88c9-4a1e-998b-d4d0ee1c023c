package com.czc.cutsame.util;

import android.graphics.PointF;
import android.graphics.RectF;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.czc.cutsame.bean.TransformData;
import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsMaskRegionInfo;
import com.meicam.sdk.NvsSize;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsVideoClip;
import com.meicam.sdk.NvsVideoFx;
import com.meicam.sdk.NvsVideoResolution;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.MeicamMaskRegionInfo;
import com.meishe.engine.bean.MeicamPosition2D;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoFx;
import com.meishe.engine.bean.template.MeicamNvsTemplateFootageCorrespondingClipInfo;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.editor.EditorController;

import java.util.ArrayList;
import java.util.List;

import static com.meishe.engine.bean.CommonData.TYPE_RAW_BUILTIN;
import static com.meishe.engine.bean.MeicamVideoFx.INVALID_VALUE;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_CROPPER;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_CROPPER_EXT;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_CROPPER_TRANSFORM;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_POST_CROPPER_TRANSFORM;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: ChuChenGuang
 * @CreateDate: 2021 /4/27 10:03
 * @Description: 模板数据适配调整工具 The tool for adjusting the template data.
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class NvTemplateDataAdjustTool {
    /**
     * Adjust timeline data.
     * 调整timeline 数据
     *
     * @param context  the context 上下文
     * @param timeline the timeline timeline
     */
    public static void adjustTimelineData(NvsStreamingContext context, MeicamTimeline timeline, String templateId) {
        if (timeline == null) {
            return;
        }
        if (timeline.isVariantImageSize()) {
            //小图模式不支持属性特技和raw特技
            LogUtils.d("Small graphical model does not support attribute stunts and raw stunts");
            return;
        }
        List<MeicamNvsTemplateFootageCorrespondingClipInfo> clipInfos =
                EditorController.getInstance().getTemplateVideoClip(templateId);
        for (MeicamNvsTemplateFootageCorrespondingClipInfo clipInfo : clipInfos) {
            MeicamVideoClip meicamVideoClip = EditorController.getInstance().getVideoClipByTemplateFootageCorrespondingClipInfo(timeline,
                    clipInfo.getClipIndexInTimelineList(), clipInfo.getClipTrackIndexInTimelineList(), clipInfo.trackIndex, clipInfo.clipIndex);
            adjustVideoClipData(context, timeline, meicamVideoClip, false);
        }
    }

    /**
     * Adjust video clip data.
     * 调价视频比例信息
     *
     * @param context      the context 上下文
     * @param nvsTimeline  the nvs timeline 时间线
     * @param nvsVideoClip the nvs video clip videoClip
     * @param revoke       the revoke 是否撤销
     */
    public static void adjustVideoClipData(NvsStreamingContext context, MeicamTimeline nvsTimeline, MeicamVideoClip nvsVideoClip, boolean revoke) {
        if (context == null || nvsTimeline == null || nvsVideoClip == null) {
            LogUtils.e("parameter is error");
            return;
        }
        if (nvsTimeline.isVariantImageSize()) {
            //小图模式不支持属性特技和raw特技
            LogUtils.d("Small graphical model does not support attribute stunts and raw stunts");
            return;
        }
        PointF videoSize = getClipPixelSize(nvsVideoClip.getFilePath(), context);
        if (videoSize == null) {
            LogUtils.e("Error getting selected material frame");
            return;
        }

        nvsVideoClip.setOriginalWidth((int) videoSize.x);
        nvsVideoClip.setOriginalHeight((int) videoSize.y);

        NvsVideoResolution videoResolution = nvsTimeline.getVideoResolution();
        float[] rawFileSizeInTemplate = getRealFileSizeInTemplate(context, nvsVideoClip, videoSize, videoResolution);
        if (rawFileSizeInTemplate == null) {
            rawFileSizeInTemplate = getRawFileSizeInTemplate(context, nvsVideoClip, videoSize, videoResolution);
        }
        float originVideoWidth = rawFileSizeInTemplate[0];
        float originVideoHeight = rawFileSizeInTemplate[1];
        if (originVideoWidth == 0 || originVideoHeight == 0) {
            LogUtils.e("Error getting original material frame");
            return;
        }

        nvsVideoClip.setAttachment(CommonData.ATTACHMENT_KEY_VIDEO_RATIO, originVideoWidth / originVideoHeight);

        /**
         * 重新设置pan 和scan值，第三方生成的模板pan和scan有可能和美映的不一样
         * Reset the pan and scan values.
         * The templates pan and scan generated by the third party may be different from those of MYVideo
         */
        //resetPanAndScan(nvsVideoClip);
        float[] panAndScan = getPanAndScan(nvsVideoClip);

        /*
         * 添加一个默认蒙版，用于适配视频比例
         * Add a default mask to fit the video scale
         */
        addCutEffect(nvsVideoClip, calculateTransForm(nvsVideoClip, videoSize, originVideoWidth, originVideoHeight), videoResolution);
        boolean hasMaskGenerator = false;
        int rawFxCount = nvsVideoClip.getRawFxCount();
        for (int i = 0; i < rawFxCount; i++) {
            MeicamVideoFx fx = nvsVideoClip.getRawFxByIndex(i);
            if (fx != null && NvsConstants.Crop.NAME.equals(fx.getDesc())) {
                hasMaskGenerator = true;
            }
        }
        MeicamVideoFx propertyVideoFx = nvsVideoClip.findPropertyVideoFx();
        if (propertyVideoFx != null) {
            Object objectVal = propertyVideoFx.getObjectVal(NvsConstants.KEY_PROPERTY_MASK_REGION_INFO);
            if (objectVal instanceof MeicamMaskRegionInfo) {
                hasMaskGenerator = true;
            }
        }
        //如果没有蒙版数据，就不要再往下操作了 If there is no masked data, do not proceed further.
        if (!hasMaskGenerator) {
            return;
        }

        /// 创建模板时素材比例 Material proportion when creating a template.
        float assetAspectRatio = originVideoWidth / originVideoHeight;
        //填入模板素材，宽高对齐
        PointF originVideoSizeInSelected = assetSizeInBox(videoSize, assetAspectRatio);
        float rateX = originVideoSizeInSelected.x / videoSize.x;
        float rateY = originVideoSizeInSelected.y / videoSize.y;
        if (panAndScan[1] == 1) {
            rateX = 1;
            rateY = 1;
        }
        //填入模板素材 比例 Fill in template materials, align width and height.
        float boxSizeRate = videoSize.x / videoSize.y;
        boolean shouldAdjustX = boxSizeRate > assetAspectRatio;
        if (revoke) {
            rateX = 1.0f / rateX;
            rateY = 1.0f / rateY;
        }

        for (int i = 0; i < rawFxCount; i++) {
            MeicamVideoFx fx = nvsVideoClip.getRawFxByIndex(i);
            if (fx != null && NvsConstants.KEY_MASK_GENERATOR.equals(fx.getDesc())) {
                adjustMaskGeneratorData(fx, NvsConstants.KEY_MASK_REGION_INFO, shouldAdjustX, rateX, rateY);
            }
        }

        if (propertyVideoFx != null) {
            adjustMaskGeneratorData(propertyVideoFx, NvsConstants.KEY_PROPERTY_MASK_REGION_INFO, shouldAdjustX, rateX, rateY);
        }
    }

    /**
     * Reset pan and scan.
     * 重新设置pan 和scan值
     *
     * @param nvsVideoClip the nvs video clip
     */
    private static void resetPanAndScan(MeicamVideoClip nvsVideoClip) {
        MeicamVideoFx property = nvsVideoClip.findPropertyVideoFx();
        if (property != null) {
            property.setFloatVal("Scan Value", 0);
            property.setFloatVal("Pan Value", 0);
        }
        if (!nvsVideoClip.getVideoType().equals(CommonData.CLIP_VIDEO)) {
            nvsVideoClip.setImageMotionMode(NvsVideoClip.CLIP_MOTIONMODE_LETTERBOX_ZOOMIN);
            nvsVideoClip.setImageMotionAnimationEnabled(false);
            nvsVideoClip.setScan(0);
            nvsVideoClip.setSpan(0);
        }
        nvsVideoClip.enableROI(false);
    }


    private static float[] getPanAndScan(MeicamVideoClip nvsVideoClip) {
        float[] panAndScan = new float[2];
        MeicamVideoFx property = nvsVideoClip.findPropertyVideoFx();
        if (property != null) {
            panAndScan[1] = property.getFloatVal(NvsConstants.SCAN_VALUE);
            panAndScan[0] = property.getFloatVal(NvsConstants.PAN_VALUE);
        }
        if ( panAndScan[0] == 0 && panAndScan[1] == 0) {
            panAndScan[1] = nvsVideoClip.getScan();
            panAndScan[0] = nvsVideoClip.getSpan();
        }
        return panAndScan;
    }

    /**
     * Get raw file size in template float [ ].
     * 获取模板中定义的原始宽高
     *
     * @param context         the context 流媒体上下文
     * @param videoClip       the video clip video clip信息
     * @param videoResolution the video resolution 分辨率
     * @return the float [ ] 文件的原始宽高
     */
    public static float[] getRawFileSizeInTemplate(NvsStreamingContext context, MeicamVideoClip videoClip, NvsVideoResolution videoResolution) {
        return getRawFileSizeInTemplate(context, videoClip, null, videoResolution);
    }

    private static float[] getRealFileSizeInTemplate(NvsStreamingContext context, MeicamVideoClip nvsVideoClip, PointF videoSize, NvsVideoResolution videoResolution) {
        float[] size = new float[2];
        if (context == null || nvsVideoClip == null || videoResolution == null) {
            return null;
        }
        MeicamVideoFx cropperVideoFx = nvsVideoClip.getVideoFxByType(TYPE_RAW_BUILTIN, SUB_TYPE_CROPPER_EXT);
        if (cropperVideoFx != null) {
            float top = cropperVideoFx.getFloatVal(NvsConstants.Crop.BOUNDING_TOP, 0);
            float bottom = cropperVideoFx.getFloatVal(NvsConstants.Crop.BOUNDING_BOTTOM, 0);
            float left = cropperVideoFx.getFloatVal(NvsConstants.Crop.BOUNDING_LEFT, 0);
            float right = cropperVideoFx.getFloatVal(NvsConstants.Crop.BOUNDING_RIGHT, 0);
            size[0] = Math.abs(right - left);
            size[1] = Math.abs(top - bottom);
            return size;
        }
        return null;
    }

    private static float[] getRawFileSizeInTemplate(NvsStreamingContext context, MeicamVideoClip nvsVideoClip, PointF videoSize, NvsVideoResolution videoResolution) {
        float[] size = new float[2];
        String clipRawWidth = nvsVideoClip.getTemplateAttachment(NvsVideoFx.TEMPLATE_KEY_CLIP_RAW_WIDTH);
        String clipRawHeight = nvsVideoClip.getTemplateAttachment(NvsVideoFx.TEMPLATE_KEY_CLIP_RAW_HEIGHT);
        if (TextUtils.isEmpty(clipRawWidth) || TextUtils.isEmpty(clipRawHeight)) {
            if (videoSize == null) {
                videoSize = getClipPixelSize(nvsVideoClip.getFilePath(), context);
            }
            if (videoSize == null) {
                LogUtils.e("Error getting selected material frame");
                size[0] = nvsVideoClip.getOriginalWidth();
                size[1] = nvsVideoClip.getOriginalHeight();
                return size;
            }
            size[0] = videoSize.x;
            size[1] = videoSize.y;
            MeicamVideoFx property = nvsVideoClip.findPropertyVideoFx();
            if (property != null) {
                float scanValue = property.getNvsFloatVal("Scan Value");
                LogUtils.d("scanValue = " + scanValue);
                if (scanValue != INVALID_VALUE && scanValue > 0) {
                    size[0] = videoResolution.imageWidth * scanValue;
                    size[1] = videoResolution.imageHeight;
                }
            }
        } else {
            size[0] = Float.parseFloat(clipRawWidth);
            size[1] = Float.parseFloat(clipRawHeight);
        }
        return size;
    }

    private static void adjustMaskGeneratorData(MeicamVideoFx maskFx, String key, boolean shouldAdjustX, float rateX, float rateY) {
        Object object = maskFx.getObjectVal(key);
        if (!(object instanceof MeicamMaskRegionInfo)) {
            return;
        }
        MeicamMaskRegionInfo regionInfo = (MeicamMaskRegionInfo) object;
        List<MeicamMaskRegionInfo.RegionInfo> regionInfos = regionInfo.getLocalRegionInfoArray();
        if (regionInfos == null || (!CommonUtils.isIndexAvailable(regionInfos.size() - 1, regionInfos))) {
            return;
        }
        MeicamMaskRegionInfo.RegionInfo info = regionInfos.get(regionInfos.size() - 1);
        if (NvsMaskRegionInfo.MASK_REGION_TYPE_ELLIPSE2D == info.getType()) {
            //圆形 circular
            MeicamMaskRegionInfo.Ellipse2D ellipse2D = info.getEllipse2D();
            MeicamMaskRegionInfo.Transform2D transform2D = info.getTransform2D();
            if (shouldAdjustX) {
                ellipse2D.setA(ellipse2D.getA() * rateX);
                MeicamPosition2D nvsPosition2D = ellipse2D.getCenter();
                nvsPosition2D.x *= rateX;
                MeicamPosition2D anchor = transform2D.getAnchor();
                anchor.x *= rateX;
            } else {
                ellipse2D.setB(ellipse2D.getB() * rateY);
                MeicamPosition2D nvsPosition2D = ellipse2D.getCenter();
                nvsPosition2D.y *= rateY;
                MeicamPosition2D anchor = transform2D.getAnchor();
                anchor.y *= rateY;
            }
            info.setEllipse2D(ellipse2D);
        } else {
            List<MeicamPosition2D> adjustArray = new ArrayList<>();
            List<MeicamPosition2D> position2DS = info.getPoints();
            for (MeicamPosition2D nvsPosition2D : position2DS) {
                if (shouldAdjustX) {
                    nvsPosition2D.x *= rateX;
                } else {
                    nvsPosition2D.y *= rateY;
                }
                adjustArray.add(nvsPosition2D);
            }
            info.setPoints(adjustArray);
        }
        maskFx.setObjectVal(key, regionInfo);
    }

    private static PointF getClipPixelSize(String filePath, NvsStreamingContext context) {
        if (TextUtils.isEmpty(filePath)) {
            return null;
        }
        PointF videoSize = new PointF();
        NvsAVFileInfo avfileInfo = context.getAVFileInfo(filePath);
        if (avfileInfo != null) {
            NvsSize nvsSize = avfileInfo.getVideoStreamDimension(0);
            int streamRotation = avfileInfo.getVideoStreamRotation(0);
            if (streamRotation == 1 || streamRotation == 3) {
                videoSize.x = nvsSize.height;
                videoSize.y = nvsSize.width;
            } else {
                videoSize.x = nvsSize.width;
                videoSize.y = nvsSize.height;
            }
        } else {
            return null;
        }
        return videoSize;
    }


    private static PointF assetSizeInBox(PointF boxSize, float assetAspectRatio) {
        PointF pointF = new PointF();
        float boxSizeRate = boxSize.x / boxSize.y;
        if (boxSizeRate > assetAspectRatio) {
            //高对齐 High alignment
            pointF.y = boxSize.y;
            pointF.x = pointF.y * assetAspectRatio;
        } else {
            //宽对齐 Wide alignment
            pointF.x = boxSize.x;
            pointF.y = pointF.x / assetAspectRatio;
        }
        return pointF;
    }

    /**
     * 添加裁剪需要的特效
     * Add the effects needed for clipping
     *
     * @param videoClip    the video clip video clip数据
     * @param transformData the transform data video clip的旋转，位移，缩放，区域值等信息
     * @param resolution    the resolution timeline的分辨率
     */
    public static void addCutEffect(@NonNull MeicamVideoClip videoClip, @NonNull TransformData transformData, @NonNull NvsVideoResolution resolution) {

        //删除非裁剪用特效，在裁剪添加完后，再恢复这些特技
        //Delete special effects for non clipping, and restore these special effects after clipping and adding.
        MeicamVideoFx transformVideoFx = videoClip.getVideoFxByType(TYPE_RAW_BUILTIN, SUB_TYPE_CROPPER_TRANSFORM);
        int rawFxCount = videoClip.getRawFxCount();
        int index = Math.min(rawFxCount, 2);
        if (transformVideoFx == null) {
            transformVideoFx = videoClip.insertVideoFx(TYPE_RAW_BUILTIN, SUB_TYPE_CROPPER_TRANSFORM, NvsConstants.FX_TRANSFORM_2D, index, true);
            transformVideoFx.setBooleanVal(NvsConstants.KEY_CROPPER_IS_NORMALIZED_COORD, true);
            transformVideoFx.setBooleanVal(NvsConstants.KEY_CROPPER_FORCE_IDENTICAL_POSITION, true);
        }

        videoClip.enableRawSourceMode(true);

        parseTransToTimeline(resolution.imageWidth, resolution.imageHeight, videoClip.getFilePath(),
                transformData.getRectViewSize(), transformData);
        LogUtils.d("onConfirm: transFromData = " + transformData);
        transformVideoFx.setFloatVal(NvsConstants.KEY_CROPPER_TRANS_X, transformData.getTransX());
        transformVideoFx.setFloatVal(NvsConstants.KEY_CROPPER_TRANS_Y, transformData.getTransY());
        transformVideoFx.setFloatVal(NvsConstants.KEY_CROPPER_SCALE_X, transformData.getScale());
        transformVideoFx.setFloatVal(NvsConstants.KEY_CROPPER_SCALE_Y, transformData.getScale());
        transformVideoFx.setFloatVal(NvsConstants.KEY_CROPPER_ROTATION, transformData.getRotation());

        MeicamVideoFx cropperVideoFx = videoClip.getVideoFxByType(TYPE_RAW_BUILTIN, SUB_TYPE_CROPPER_EXT);
        if (cropperVideoFx == null) {
            cropperVideoFx = videoClip.insertVideoFx(TYPE_RAW_BUILTIN, SUB_TYPE_CROPPER_EXT, NvsConstants.Crop.NAME, index + 1, true);
            videoClip.enableRawSourceMode(true);
        }
        if (cropperVideoFx == null) {
            LogUtils.e("PropertyVideoFx is null");
            return;
        }

        cropperVideoFx.setFloatVal(NvsConstants.KEY_CROPPER_ASSET_ASPECT_RATIO, transformData.getRectRatio());

        RectF region = transformData.getRegion();

        float halfWidth =  videoClip.getOriginalWidth() / 2F;
        float halfHeight = videoClip.getOriginalHeight() / 2F;

        float bLeft = region.left * halfWidth;
        float bTop =  region.top * halfHeight;
        float bRight = region.right * halfWidth;
        float bBottom = region.bottom * halfHeight;

        cropperVideoFx.setFloatVal(NvsConstants.Crop.BOUNDING_LEFT, bLeft);
        cropperVideoFx.setFloatVal(NvsConstants.Crop.BOUNDING_TOP, bTop);
        cropperVideoFx.setFloatVal(NvsConstants.Crop.BOUNDING_RIGHT, bRight);
        cropperVideoFx.setFloatVal(NvsConstants.Crop.BOUNDING_BOTTOM, bBottom);

        MeicamVideoFx fullTransformFx = videoClip.getVideoFxByType(TYPE_RAW_BUILTIN, SUB_TYPE_POST_CROPPER_TRANSFORM);
        if (fullTransformFx == null) {
            fullTransformFx = videoClip.appendFx(TYPE_RAW_BUILTIN, SUB_TYPE_POST_CROPPER_TRANSFORM, NvsConstants.FX_TRANSFORM_2D);
        }
        float cropFxWidth = Math.abs(bRight - bLeft);
        float cropFxHeight = Math.abs(bBottom - bTop);
        videoClip.setRectWidth(cropFxWidth);
        videoClip.setRectHeight(cropFxHeight);

        PointF timelineSize = new PointF(resolution.imageWidth, resolution.imageHeight);
        PointF fileSizeInTimeline = assetSizeInBox(timelineSize, halfWidth / halfHeight);
        float scaleFile = fileSizeInTimeline.x / halfWidth / 2F;
        float rectSizeInTimelineWidthBeforeScale = cropFxWidth /(2F * halfWidth) * fileSizeInTimeline.x;
        float rectSizeInTimelineHeightBeforeScale = cropFxHeight /(2F * halfHeight) * fileSizeInTimeline.y;
        PointF rectSizeInTimelineAfterScale = assetSizeInBox(timelineSize, cropFxWidth / cropFxHeight);
        double ratioW = rectSizeInTimelineAfterScale.x / rectSizeInTimelineWidthBeforeScale;
        double ratioH = rectSizeInTimelineAfterScale.y / rectSizeInTimelineHeightBeforeScale;
        double scale = ratioW;
        if (ratioW - 1F < 0.001) {
            scale = ratioH;
        }
        fullTransformFx.setFloatVal(NvsConstants.KEY_CROPPER_SCALE_X, (float) (scale * scaleFile));
        fullTransformFx.setFloatVal(NvsConstants.KEY_CROPPER_SCALE_Y, (float) (scale * scaleFile));
    }


    private static List<MeicamPosition2D> getPosition2D(RectF rectF) {
        List<MeicamPosition2D> positions = new ArrayList<>();
        positions.add(new MeicamPosition2D(rectF.left, rectF.top));
        positions.add(new MeicamPosition2D(rectF.left, rectF.bottom));
        positions.add(new MeicamPosition2D(rectF.right, rectF.bottom));
        positions.add(new MeicamPosition2D(rectF.right, rectF.top));

        return positions;
    }

    /**
     * 转换transform 数据为timeline范围内的transform
     * Convert the transform data to transform within the timeline range
     *
     * @param timelineWidth  时间线的宽
     * @param timelineHeight 时间线的高
     * @param filePath       文件路径
     * @param rectSize       裁剪区域的宽高
     * @param transformData  转换数据
     */
    private static void parseTransToTimeline(int timelineWidth, int timelineHeight,
                                             String filePath, float[] rectSize, TransformData transformData) {
        NvsAVFileInfo avFileInfo = NvsStreamingContext.getInstance().getAVFileInfo(filePath);
        if (avFileInfo == null) {
            return;
        }
        float transXInView = transformData.getTransX();
        float transYInView = transformData.getTransY();
        int videoStreamRotation = avFileInfo.getVideoStreamRotation(0);
        NvsSize dimension = avFileInfo.getVideoStreamDimension(0);
        int height;
        int width;
        if (videoStreamRotation % 2 == 0) {
            height = dimension.height;
            width = dimension.width;
        } else {
            width = dimension.height;
            height = dimension.width;
        }

        float fileRatio = width * 1F / height;
        float timelineRatio = timelineWidth * 1F / timelineHeight;

        float fileWidthInTimeline;
        float fileHeightInTimeline;
        if (fileRatio > timelineRatio) {
            //文件宽对齐 File width alignment
            fileWidthInTimeline = timelineWidth;
            fileHeightInTimeline = fileWidthInTimeline / fileRatio;
        } else {//高对齐 High alignment
            fileHeightInTimeline = timelineHeight;
            fileWidthInTimeline = fileHeightInTimeline * fileRatio;
        }
        float rectWidthInTimeline;
        float rectHeightInTimeline;
        float rectRatio = rectSize[0] / rectSize[1];
        if (rectRatio > fileRatio) {
            //裁剪区域宽对齐 Cropped area width alignment
            rectWidthInTimeline = fileWidthInTimeline;
            rectHeightInTimeline = rectWidthInTimeline / rectRatio;
        } else {
            rectHeightInTimeline = fileHeightInTimeline;
            rectWidthInTimeline = rectHeightInTimeline * rectRatio;
        }

        float transXInTimeline = transXInView / rectSize[0] * rectWidthInTimeline;
        float transYInTimeline = transYInView / rectSize[1] * rectHeightInTimeline;
        //Mask Generator方案需要归一化 The Mask Generator scheme requires normalization.
        transformData.setTransX(transXInTimeline / fileWidthInTimeline * 2);
        //Timeline 坐标轴反向 Timeline Axis Reverse.
        transformData.setTransY(-transYInTimeline / fileHeightInTimeline * 2);
    }

    private static TransformData calculateTransForm(MeicamVideoClip videoClip, PointF videoSize, float originVideoWidth, float originVideoHeight) {
        TransformData transformData = new TransformData();
        if (originVideoWidth == 0 || originVideoHeight == 0) {
            LogUtils.e("OriginVideo size is null! Please fix the template");
            return transformData;
        }
        
        MeicamVideoFx cropperVideoFx = videoClip.getVideoFxByType(TYPE_RAW_BUILTIN, SUB_TYPE_CROPPER_EXT);
        MeicamVideoFx transformVideoFx = videoClip.getVideoFxByType(TYPE_RAW_BUILTIN, SUB_TYPE_CROPPER_TRANSFORM);
        if (transformVideoFx != null && cropperVideoFx != null) {
            transformData.setTransX(transformVideoFx.getFloatVal(NvsConstants.KEY_CROPPER_TRANS_X));
            transformData.setTransY(transformVideoFx.getFloatVal(NvsConstants.KEY_CROPPER_TRANS_Y));
            transformData.setScale(transformVideoFx.getFloatVal(NvsConstants.KEY_CROPPER_SCALE_X));
            transformData.setRotation(transformVideoFx.getFloatVal(NvsConstants.KEY_CROPPER_ROTATION));
        }

        transformData.setRectSize(new float[]{originVideoWidth, originVideoHeight});
        float rectRatio = originVideoWidth / originVideoHeight;
        transformData.setRectRatio(rectRatio);
        PointF pointF = assetSizeInBox(new PointF(videoSize.x, videoSize.y), originVideoWidth / originVideoHeight);
        transformData.setRegion(getRect(pointF.x, pointF.y, videoSize.x, videoSize.y));
        return transformData;
    }

    private static RectF getRect(float rectWidth, float rectHeight, float imageWidth, float imageHeight) {
        RectF rectF = new RectF();
        float imageRatio = imageWidth / imageHeight;
        float rectRatio = rectWidth / rectHeight;
        if (rectRatio > imageRatio) {
            //宽对齐 Wide alignment
            rectF.right = 1;
            rectF.left = -rectF.right;
            rectF.top = rectHeight / imageHeight;
            rectF.bottom = -rectF.top;

        } else {
            // 高对齐 High alignment
            rectF.top = 1;
            rectF.bottom = -rectF.top;
            rectF.right = rectWidth / imageWidth;
            rectF.left = -rectF.right;
        }
        return rectF;
    }

    /**
     * Gets init cut data.
     *
     * @param selectedClipInfo the selected clip info
     * @return the init cut data
     */
    public static TransformData getInitCutData(MeicamNvsTemplateFootageCorrespondingClipInfo selectedClipInfo) {
        TransformData transformData = new TransformData();
        if (selectedClipInfo == null) {
            return transformData;
        }
        MeicamVideoClip mVideoClip = EditorController.getInstance().getVideoClipByTemplateFootageCorrespondingClipInfo(selectedClipInfo);
        if (mVideoClip == null) {
            return transformData;
        }

        MeicamVideoFx transformVideoFx = mVideoClip.getVideoFxByType(TYPE_RAW_BUILTIN, SUB_TYPE_CROPPER_TRANSFORM);
        MeicamVideoFx cropperVideoFx = mVideoClip.getVideoFxByType(TYPE_RAW_BUILTIN, SUB_TYPE_CROPPER);
        if (cropperVideoFx == null) {
            cropperVideoFx = mVideoClip.getVideoFxByType(TYPE_RAW_BUILTIN, SUB_TYPE_CROPPER_EXT);
        }
        if (transformVideoFx != null && cropperVideoFx != null) {
            transformData.setTransX(transformVideoFx.getFloatVal(NvsConstants.KEY_CROPPER_TRANS_X));
            transformData.setTransY(-transformVideoFx.getFloatVal(NvsConstants.KEY_CROPPER_TRANS_Y));
            transformData.setScale(transformVideoFx.getFloatVal(NvsConstants.KEY_CROPPER_SCALE_X));
            transformData.setRotation(-transformVideoFx.getFloatVal(NvsConstants.KEY_CROPPER_ROTATION));
            float cropperAssetAspectRatio = cropperVideoFx.getFloatVal(NvsConstants.KEY_CROPPER_ASSET_ASPECT_RATIO);
            transformData.setRectRatio(cropperAssetAspectRatio);
        }
        return transformData;
    }
}
