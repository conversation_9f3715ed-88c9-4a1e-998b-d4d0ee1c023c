<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/fl_fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.meishe.capturemodule.view.CaptureMusicControlView
        android:id="@+id/select_music_control_View"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/iv_close"
        app:layout_constraintBottom_toBottomOf="@+id/iv_close"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />


    <ImageView
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/dp_px_42"
        android:paddingEnd="@dimen/dp_px_75"
        android:paddingBottom="@dimen/dp_px_75"
        android:paddingTop="@dimen/dp_px_75"
        android:layout_marginTop="@dimen/dp_px_66"
        android:src="@mipmap/icon_back_preview" />

    <LinearLayout
        app:layout_constraintBottom_toTopOf="@+id/tv_save_local"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_px_52"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_play"
            android:layout_width="@dimen/dp_px_60"
            android:layout_height="@dimen/dp_px_60"
            android:layout_marginStart="@dimen/dp_px_30"
            android:contentDescription="@null"
            app:srcCompat="@mipmap/capture_control_bar_ic_play" />

        <TextView
            android:id="@+id/tv_play_time"
            android:text="@string/zeroZZZ"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/sp_px_30"
            android:layout_marginStart="@dimen/dp_px_33"
            android:layout_marginEnd="-2dp"
            android:textColor="@color/white" />

        <SeekBar
            android:id="@+id/seek_bar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:maxHeight="2dp"
            android:minHeight="2dp"
            android:progressDrawable="@drawable/capture_size_seekbar"
            android:thumb="@drawable/custom_voice_seekbar_ball" />

        <TextView
            android:id="@+id/tv_play_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/sp_px_30"
            android:layout_marginStart="-2dp"
            android:layout_marginEnd="@dimen/dp_px_60"
            android:textColor="@color/white" />

    </LinearLayout>

    <TextView
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_to_edit"
        android:id="@+id/tv_save_local"
        android:layout_width="@dimen/dp_px_282"
        android:layout_height="@dimen/dp_px_99"
        android:layout_marginEnd="@dimen/dp_px_36"
        android:layout_marginBottom="@dimen/dp_px_120"
        android:background="@drawable/bg_corners_white"
        android:gravity="center"
        android:text="@string/tv_save_local"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_px_33" />

    <TextView
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:id="@+id/tv_to_edit"
        android:layout_width="@dimen/dp_px_282"
        android:layout_height="@dimen/dp_px_99"
        android:layout_marginEnd="@dimen/dp_px_51"
        android:layout_marginBottom="@dimen/dp_px_120"
        android:background="@drawable/bg_corners_ff365e"
        android:gravity="center"
        android:text="@string/tv_to_edit"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_33" />

    <FrameLayout
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:id="@+id/progress"
        android:layout_width="@dimen/dp_px_270"
        android:layout_height="@dimen/dp_px_270"
        android:visibility="gone"
        android:background="@drawable/bg_4d000000_6">

        <ImageView
            android:id="@+id/iv_progress"
            android:layout_width="@dimen/dp_px_113"
            android:layout_height="@dimen/dp_px_113"
            android:layout_gravity="center"
            android:contentDescription="@null"/>
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>