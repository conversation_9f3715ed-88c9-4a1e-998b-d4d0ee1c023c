package com.meishe.user.view.adapter;

import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.meishe.base.utils.ImageLoader;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;
import com.meishe.user.R;
import com.meishe.user.bean.CloudDraftData;
import com.meishe.user.view.CustomProgress;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/9 14:06
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CloudUpDownloadAdapter extends BaseQuickAdapter<CloudDraftData, BaseViewHolder> {
    private static final int STATE_MANAGER = 1;
    private static final int STATE_COMMON = 0;

    private int mState = STATE_COMMON;
    private int mItemSize;
    private boolean isDownloading;

    public CloudUpDownloadAdapter() {
        super(R.layout.item_cloud_draft_up_download_layout);
    }

    public void setIsDownloading(boolean isDownloading){
        this.isDownloading = isDownloading;
    }

    @NonNull
    @Override
    public BaseViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        BaseViewHolder holder = super.onCreateViewHolder(parent, viewType);
        holder.addOnClickListener(R.id.more_click_view);
        holder.addOnClickListener(R.id.iv_select_clicked);
        if (mItemSize > 0) {
            View view = holder.getView(R.id.iv_cover);
            ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
            layoutParams.height = mItemSize;
            layoutParams.width = mItemSize;
            view.setLayoutParams(layoutParams);
        }
        return holder;
    }

    @Override
    protected void convertPayloads(@NonNull BaseViewHolder helper, CloudDraftData item, @NonNull List<Object> payloads) {
        if (payloads.size() < 1) {
            convert(helper,item);
        } else {
            for (Object payload : payloads) {
                // 刷新倒计时 Refresh countdown
                if (payload instanceof Integer && ((int) payload) == 1) {
                    CustomProgress progressBar = helper.getView(R.id.progress_bar);
                    visibleView(progressBar);
                    int progress = progressBar.getProgress();
                    if (progress > item.getProgress()) {
                        progressBar.setProgress(progress);
                    } else {
                        progressBar.setProgress(item.getProgress());
                    }
                }
            }
        }
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, CloudDraftData item) {
        if (item == null) {
            return;
        }
        ImageView coverView = helper.getView(R.id.iv_cover);
        ImageLoader.loadUrl(mContext, item.getCover(), coverView);
        CustomProgress progressBar = helper.getView(R.id.progress_bar);
        View icon = helper.getView(R.id.iv_cloud_icon);
        ImageView selectView = helper.getView(R.id.iv_select);
        View selectViewClick = helper.getView(R.id.iv_select_clicked);
        if (item.isTranscoding()) {
            goneView(icon);
            goneView(selectViewClick);
            goneView(progressBar);
        } else {
            if (mState == STATE_MANAGER) {
                //下载中和下载完成不能选 Downloading and completing cannot be selected
                if (!isDownloading && (item.getState() == CloudDraftData.STATE_DOWNLOADED ||
                        item.getState() == CloudDraftData.STATE_DOWNLOADING)) {
                    goneView(selectView);
                    goneView(selectViewClick);
                } else {
                    visibleView(selectView);
                    visibleView(selectViewClick);
                }
            } else {
                goneView(selectView);
                goneView(selectViewClick);
            }
            if (item.getState() == CloudDraftData.STATE_DOWNLOADING
                    || item.getState() == CloudDraftData.STATE_DELETING
                    || item.getState() == CloudDraftData.STATE_UPLOADING) {
                visibleView(progressBar);
                progressBar.setProgress(item.getProgress());
                goneView(icon);
            } else if (item.getState() == CloudDraftData.STATE_UPLOADED) {
                visibleView(icon);
                goneView(progressBar);
            } else if (item.getState() == CloudDraftData.STATE_DOWNLOADED) {
                goneView(icon);
                goneView(progressBar);
            } else {
                goneView(icon);
                goneView(progressBar);
            }
        }

        if (item.isSelected()) {
            selectView.setImageResource(R.drawable.bg_draft_round_red);
        } else {
            selectView.setImageResource(0);
        }
        TextView view = helper.getView(R.id.tv_name);
        view.setText(item.getName());
    }

    private void goneView(View view) {
        if (view.getVisibility() != View.GONE) {
            view.setVisibility(View.GONE);
        }
    }

    private void visibleView(View view) {
        if (view.getVisibility() != View.VISIBLE) {
            view.setVisibility(View.VISIBLE);
        }
    }

    public void exitManagerState() {
        mState = STATE_COMMON;
        for (CloudDraftData mDatum : mData) {
            mDatum.setSelected(false);
        }
        setNewData(mData);
    }

    public void goManagerState() {
        mState = STATE_MANAGER;
        setNewData(mData);
    }

    public void setItemSize(int itemSize) {
        mItemSize = itemSize;
    }
}
