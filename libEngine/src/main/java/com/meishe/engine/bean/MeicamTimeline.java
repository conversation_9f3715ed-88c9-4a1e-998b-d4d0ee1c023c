package com.meishe.engine.bean;


import static com.meishe.base.constants.Constants.TRACK_INDEX_MAIN;
import static com.meishe.engine.bean.CommonData.TYPE_PACKAGE;
import static com.meishe.engine.bean.CommonData.TYPE_RAW_BUILTIN;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_POST_CROPPER_TRANSFORM;
import static com.meishe.engine.constant.NvsConstants.TYPE_BUILD_IN;

import android.graphics.Bitmap;
import android.graphics.PointF;
import android.text.TextUtils;

import androidx.annotation.IntDef;

import com.google.gson.annotations.SerializedName;
import com.meicam.sdk.NvsAudioResolution;
import com.meicam.sdk.NvsAudioTrack;
import com.meicam.sdk.NvsLiveWindow;
import com.meicam.sdk.NvsLiveWindowExt;
import com.meicam.sdk.NvsRational;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsTimelineAnimatedSticker;
import com.meicam.sdk.NvsTimelineCaption;
import com.meicam.sdk.NvsTimelineCompoundCaption;
import com.meicam.sdk.NvsTimelineVideoFx;
import com.meicam.sdk.NvsTrackCaption;
import com.meicam.sdk.NvsVideoClip;
import com.meicam.sdk.NvsVideoResolution;
import com.meicam.sdk.NvsVideoTrack;
import com.meishe.base.constants.Constants;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.BuildConfig;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.EngineCallbackManager;
import com.meishe.engine.adapter.LGsonContext;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.bean.bridges.FileInfoBridge;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.local.LMeicamAudioTrack;
import com.meishe.engine.local.LMeicamResource;
import com.meishe.engine.local.LMeicamStickerCaptionTrack;
import com.meishe.engine.local.LMeicamTimelineVideoFxClip;
import com.meishe.engine.local.LMeicamTimelineVideoFxTrack;
import com.meishe.engine.local.LMeicamVideoTrack;
import com.meishe.engine.local.LMeicamWaterMark;
import com.meishe.engine.local.LTimelineData;
import com.meishe.engine.util.TimelineUtil;
import com.meishe.engine.util.gson.GsonContext;
import com.meishe.logic.utils.UMengUtils;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Hashtable;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * Created by CaoZhiChao on 2020/7/3 17:09
 */
public class MeicamTimeline extends NvsObject<NvsTimeline> implements TimelineDataParserAdapter<LTimelineData> {
    private long duration;
    @SerializedName("resources")
    private List<MeicamResource> resourceList;
    @SerializedName("videoTracks")
    private List<MeicamVideoTrack> videoTrackList;
    @SerializedName("audioTracks")
    private List<MeicamAudioTrack> audioTrackList;
    @SerializedName("stickerCaptionTracks")
    private final List<MeicamStickerCaptionTrack> stickerCaptionTrackList;
    /**
     * List of tracks for timeline videoFx
     * <p>
     * timeline特效轨道数组
     */
    private final List<MeicamTimelineVideoFxTrack> timelineVideoFxTracks;
    /**
     * Timeline track for filters and adjustments
     * 滤镜和调节的timeline轨道
     */
    private final List<MeicamTimelineVideoFxTrack> filterAndAdjustTimelineTracks;

    /**
     * List of timeline videoFx
     * Currently, it is mainly used to add blur, mosaic and so on and only one videoFx is in this list.
     * <p>
     * timeline特效轨道数组, 目前主要用来添加模糊、马赛克等.目前添加了一个
     */
    private List<MeicamTimelineVideoFxClip> timelineVideoFxClips;
    @SerializedName("waterMark")
    private MeicamWaterMark waterMark;
    @SerializedName("theme")
    private MeicamTheme theme;


    /**
     * 调节特效
     * Adjust effect
     */
    private Map<String, MeicamTimelineVideoFxClip> adjustData = new HashMap<>();

    /**
     * filterFx in timeline
     * <p>
     * timeline中的滤镜特效
     */
    private MeicamTimelineVideoFx filterFx;
    @SerializedName("projectId")
    private String mProjectId = String.valueOf(UUID.randomUUID()).toUpperCase();
    @SerializedName("projectName")
    private String mProjectName;
    @SerializedName("lastModifiedTime")
    private String mLastModifiedTime;
    @SerializedName("projectDuring")
    private String mProjectDuring;
    @SerializedName("aspectRatioMode")
    private int mMakeRatio = NvsConstants.AspectRatio.AspectRatio_NoFitRatio;//默认不适配比例
    @SerializedName("coverImagePath")
    private String mCoverImagePath;//封面图

    /**
     * 封面信息配置路径
     * The cover data config path
     */
    private String coverDataConfigPath;

    /**
     * 分别率
     * The video resolution
     */
    @SerializedName("videoResolution")
    private NvsVideoResolution nvsVideoResolution;

    /**
     * 帧率
     * videoFps
     */
    @SerializedName("rational")
    private NvsRational nvsRational;

    transient private String mDraftDir;
    @SerializedName("isAddTitleTheme")
    private boolean isAddTitleTheme;
    @SerializedName("titleThemeDuration")
    private long titleThemeDuration = 0;

    private int realMainTrackIndex;

    private boolean isCloud;

    transient private MeicamCoverData mCoverData;

    private MeicamTimeline() {
        resourceList = new ArrayList<>();
        videoTrackList = new ArrayList<>();
        audioTrackList = new ArrayList<>();
        stickerCaptionTrackList = new ArrayList<>();
        timelineVideoFxTracks = new ArrayList<>();
        filterAndAdjustTimelineTracks = new ArrayList<>();
        timelineVideoFxClips = new ArrayList<>();
        waterMark = new MeicamWaterMark(null, null);
    }

    /**
     * 用于转换业务TimelineData数据
     * Used to transform business timeline data
     *
     * @return
     */
    public String toJson() {
        return GsonContext.getInstance().toJson(this);
    }

    /**
     * 从业务数据恢复TimelineData，不需要转换
     * Restore timeline data from business data without conversion
     *
     * @param jsonData The json data
     * @return The object
     */
    public Object fromJson(String jsonData) {
        return GsonContext.getInstance().fromJson(jsonData, MeicamTimeline.class);
    }


    /**
     * 用于转换草稿数据
     * Used to convert draft data
     *
     * @return The draft string json data
     */
    public String toDraftJson() {
        LTimelineData lTimelineData = parseToLocalData();
        return LGsonContext.getInstance().toJson(lTimelineData);
    }


    public long getDuration() {
        if (getObject() != null) {
            return getObject().getDuration();
        }
        return duration;
    }

    /**
     * 获取timeline flag
     * Gets creation flags.
     *
     * @return the creation flags
     */
    public int getCreationFlags() {
        if (getObject() != null) {
            return getObject().getCreationFlags();
        }
        return 0;
    }

    /**
     * 判断是否是小图模式
     * Is variant image size boolean.
     *
     * @return the boolean
     */
    public boolean isVariantImageSize() {
        int timelineFlag = getCreationFlags();
        return (timelineFlag & NvsStreamingContext.CREATE_TIMELINE_FLAG_VARIANT_IMAGE_SIZE) != 0;
    }

    public void setDuration(long duration) {
        this.duration = duration;
    }

    List<MeicamResource> getResourceList() {
        return resourceList;
    }

    void setResourceList(List<MeicamResource> meicamResourceList) {
        resourceList = meicamResourceList;
    }

    List<MeicamVideoTrack> getVideoTrackList() {
        return videoTrackList;
    }

    void setVideoTrackList(List<MeicamVideoTrack> meicamVideoTrackList) {
        videoTrackList = meicamVideoTrackList;
    }

    List<MeicamAudioTrack> getAudioTrackList() {
        return audioTrackList;
    }

    void setAudioTrackList(List<MeicamAudioTrack> meicamAudioTrackList) {
        audioTrackList = meicamAudioTrackList;
    }

    List<MeicamStickerCaptionTrack> getStickerCaptionTrackList() {
        return stickerCaptionTrackList;
    }

    List<MeicamTimelineVideoFxClip> getTimelineVideoFxClips() {
        return timelineVideoFxClips;
    }

    void setTimelineVideoFxClips(List<MeicamTimelineVideoFxClip> timelineVideoFxClips) {
        this.timelineVideoFxClips = timelineVideoFxClips;
    }

    Map<String, MeicamTimelineVideoFxClip> getAdjustData() {
        return adjustData;
    }

    void setAdjustData(Map<String, MeicamTimelineVideoFxClip> adjustData) {
        this.adjustData = adjustData;
    }

    /**
     * Get meicam sticker caption track count int.
     * 获取字幕贴纸轨道数量
     *
     * @return the int
     */
    public int getStickerCaptionTrackCount() {
        return stickerCaptionTrackList.size();
    }

    List<MeicamTimelineVideoFxTrack> getTimelineVideoFxTrackList() {
        return timelineVideoFxTracks;
    }

    public List<MeicamTimelineVideoFxTrack> getFilterAndAdjustTimelineTracks() {
        return filterAndAdjustTimelineTracks;
    }

    public MeicamWaterMark getMeicamWaterMark() {
        if (waterMark == null) {
            waterMark = new MeicamWaterMark(null, null);
        }
        return waterMark;
    }

    public MeicamTheme getMeicamTheme() {
        return theme;
    }

    public String getProjectId() {
        return mProjectId;
    }

    public void setProjectId(String projectId) {
        mProjectId = projectId;
    }

    public String getProjectDuring() {
        return mProjectDuring;
    }

    public void setProjectDuring(String mProjectDuring) {
        this.mProjectDuring = mProjectDuring;
    }

    public String getProjectName() {
        return mProjectName;
    }

    public void setProjectName(String projectName) {
        mProjectName = projectName;
    }

    public String getLastModifiedTime() {
        return mLastModifiedTime;
    }

    public void setLastModifiedTime(String lastModifiedTime) {
        mLastModifiedTime = lastModifiedTime;
    }

    public int getMakeRatio() {
        return mMakeRatio;
    }

    public void setMakeRatio(int makeRatio) {
        mMakeRatio = makeRatio;
    }

    public String getCoverImagePath() {
        return mCoverImagePath;
    }

    public void setCoverImagePath(String coverImagePath) {
        mCoverImagePath = coverImagePath;
    }

    public NvsVideoResolution getVideoResolution() {
        if (nvsVideoResolution == null) {
            //创建timeline的时候就不一定设置NvsVideoResolution，所以上层数据可能为null
            //NvsVideoResolution is not necessarily set when creating a timeline,
            // so the upper data may be null
            NvsTimeline nvsTimeline = getObject();
            if (nvsTimeline != null && nvsTimeline.getVideoRes() != null) {
                nvsVideoResolution = nvsTimeline.getVideoRes();
            }
        }
        if (nvsVideoResolution == null) {
            MeicamVideoTrack videoTrack = getVideoTrack(TRACK_INDEX_MAIN);
            if (videoTrack != null) {
                MeicamVideoClip videoClip = videoTrack.getVideoClip(0);
                if (videoClip != null) {
                    nvsVideoResolution = TimelineUtil.getVideoEditResolutionByClip(videoClip.getFilePath());
                }
            }
        }
        if (nvsVideoResolution == null) {
            nvsVideoResolution = new NvsVideoResolution();
            nvsVideoResolution.imagePAR = new NvsRational(1, 1);
            nvsVideoResolution.imageWidth = 720;
            nvsVideoResolution.imageHeight = 1080;
        }
        return nvsVideoResolution;
    }

    public NvsRational getVideoFps() {
        NvsTimeline object = getObject();
        if (object != null) {
            return object.getVideoFps();
        }
        return null;
    }

    public void setVideoResolution(NvsVideoResolution videoResolution) {
        if (videoResolution == null) {
            LogUtils.e("videoResolution is null");
            return;
        }
        TimelineUtil.alignedResolution(videoResolution);
        changeVideoSize(videoResolution.imageWidth, videoResolution.imageHeight);

        this.nvsVideoResolution = videoResolution;
    }

    /**
     * Change video size.
     * 改变liveWindrow大小
     *
     * @param imageWidth  the image width
     * @param imageHeight the image height
     */
    public void changeVideoSize(int imageWidth, int imageHeight) {
        NvsTimeline object = getObject();
        if (object != null) {
            object.changeVideoSize(imageWidth, imageHeight);
            NvsVideoResolution resolution = new NvsVideoResolution();
            resolution.imageWidth = imageWidth;
            resolution.imageHeight = imageHeight;
            resolution.imagePAR = new NvsRational(1, 1);
            nvsVideoResolution = resolution;
            postChangeTimelineSize(imageWidth, imageHeight);
        }
    }

    private void postChangeTimelineSize(int timelineWidth, int timelineHeight) {
        for (MeicamVideoTrack videoTrack : videoTrackList) {
            int clipCount = videoTrack.getClipCount();
            if (clipCount > 0) {
                for (int index = 0; index < clipCount; index++) {
                    MeicamVideoClip videoClip = videoTrack.getVideoClip(index);
                    adjustVideoClipRect(videoClip, timelineWidth, timelineHeight);
                }
            }
        }
    }

    private void adjustVideoClipRect(MeicamVideoClip videoClip, int timelineWidth, int timelineHeight) {
        MeicamVideoFx fullTransformFx = videoClip.getVideoFxByType(TYPE_RAW_BUILTIN, SUB_TYPE_POST_CROPPER_TRANSFORM);
        if (fullTransformFx == null) {
            return;
        }
        float cropFxWidth = videoClip.getRectWidth();
        float cropFxHeight = videoClip.getRectHeight();
        int originalHeight = videoClip.getOriginalHeight();
        int originalWidth = videoClip.getOriginalWidth();

        PointF timelineSize = new PointF(timelineWidth, timelineHeight);
        PointF fileSizeInTimeline = assetSizeInBox(timelineSize, originalWidth * 1F / originalHeight);
        float scaleFile = fileSizeInTimeline.x / originalWidth;
        float rectSizeInTimelineWidthBeforeScale = cropFxWidth / originalWidth * fileSizeInTimeline.x;
        float rectSizeInTimelineHeightBeforeScale = cropFxHeight / originalHeight * fileSizeInTimeline.y;
        PointF rectSizeInTimelineAfterScale = assetSizeInBox(timelineSize, cropFxWidth / cropFxHeight);
        double ratioW = rectSizeInTimelineAfterScale.x / rectSizeInTimelineWidthBeforeScale;
        double ratioH = rectSizeInTimelineAfterScale.y / rectSizeInTimelineHeightBeforeScale;
        double scale = ratioW;
        if (ratioW - 1F < 0.001) {
            scale = ratioH;
        }

        fullTransformFx.setFloatVal(NvsConstants.KEY_CROPPER_SCALE_X, (float) (scale * scaleFile));
        fullTransformFx.setFloatVal(NvsConstants.KEY_CROPPER_SCALE_Y, (float) (scale * scaleFile));
    }

    private static PointF assetSizeInBox(PointF boxSize, float assetAspectRatio) {
        PointF pointF = new PointF();
        float boxSizeRate = boxSize.x / boxSize.y;
        if (boxSizeRate > assetAspectRatio) {
            //高对齐
            pointF.y = boxSize.y;
            pointF.x = pointF.y * assetAspectRatio;
        } else {//宽对齐
            pointF.x = boxSize.x;
            pointF.y = pointF.x / assetAspectRatio;
        }
        return pointF;
    }

    public void setFilterFx(MeicamTimelineVideoFx filterFx) {
        this.filterFx = filterFx;
    }

    public MeicamTimelineVideoFx getFilterFx() {
        return filterFx;
    }


    public String getDraftDir() {
        return mDraftDir;
    }

    public void setDraftDir(String draftDir) {
        this.mDraftDir = draftDir;
    }


    public NvsRational getNvsRational() {
        return nvsRational;
    }

    public void setNvsRational(NvsRational nvsRational) {
        this.nvsRational = nvsRational;
    }

    public int getRealMainTrackIndex() {
        return realMainTrackIndex;
    }

    public String getCoverDataConfigPath() {
        return coverDataConfigPath;
    }

    public void setCoverDataConfigPath(String coverDataConfigPath) {
        this.coverDataConfigPath = coverDataConfigPath;
    }

    @Override
    public LTimelineData parseToLocalData() {
        LTimelineData localData = new LTimelineData();
        try {
            parseResources(localData);
        } catch (Exception e) {
            LogUtils.e(e);
        }
        for (MeicamVideoTrack meicamVideoTrack : videoTrackList) {
            localData.getMeicamVideoTrackList().add(meicamVideoTrack.parseToLocalData());
        }

        for (MeicamAudioTrack meicamAudioTrack : audioTrackList) {
            localData.getMeicamAudioTrackList().add(meicamAudioTrack.parseToLocalData());
        }
        for (MeicamStickerCaptionTrack stickerCaptionTrack : stickerCaptionTrackList) {
            localData.getMeicamStickerCaptionTrackList().add(stickerCaptionTrack.parseToLocalData());
        }
        for (MeicamTimelineVideoFxTrack videoFxTrack : timelineVideoFxTracks) {
            localData.getMeicamTimelineVideoFxTrackList().add(videoFxTrack.parseToLocalData());
        }
        for (MeicamTimelineVideoFxTrack videoFxTrack : filterAndAdjustTimelineTracks) {
            localData.getMeicamTimelineVideoFxTrackList().add(videoFxTrack.parseToLocalData());
        }
        for (MeicamTimelineVideoFxClip meicamTimelineVideoFxClip : timelineVideoFxClips) {
            localData.getMeicamTimelineVideoFxClipList().add(meicamTimelineVideoFxClip.parseToLocalData());
        }

        if (waterMark != null) {
            localData.setMeicamWaterMark(waterMark.parseToLocalData());
        }

        if (theme != null) {
            localData.setMeicamTheme(theme.parseToLocalData());
        }
//        ------------------------------------------1.3.5以后可以删掉------------------------------------------
//        ------------------------------------------1.3.5 Can be deleted later-----------------------------------------
        if (adjustData != null && !adjustData.isEmpty()) {
            MeicamAdjustData adjustData = new MeicamAdjustData();
            Map<String, List<String>> adjustFxAndKeyMap = NvsConstants.getAdjustFxAndKeyMap();
            Set<Map.Entry<String, List<String>>> entries = adjustFxAndKeyMap.entrySet();
            for (Map.Entry<String, List<String>> entry : entries) {
                MeicamTimelineVideoFxClip meicamVideoFx = this.adjustData.get(entry.getKey());
                List<String> value = entry.getValue();
                if (meicamVideoFx != null) {
                    for (String key : value) {
                        Float floatVal = meicamVideoFx.getFloatVal(key);
                        if (floatVal == null || floatVal < 0) {
                            floatVal = 0F;
                        }
                        adjustData.setValue(key, floatVal);
                    }
                } else {
                    for (String key : value) {
                        adjustData.setValue(key, 0);
                    }
                }
            }
            localData.setMeicamAdjustData(adjustData.parseToLocalData());
        }


        if (filterFx != null) {
            localData.setFilterFx(filterFx.parseToLocalData());
        }
        //---------------------------------------------------------------------------------------
        if (nvsVideoResolution == null) {
            UMengUtils.generateCustomLog("parseToLocalData  nvsVideoResolution==null");
        }

        localData.setVideoResolution(nvsVideoResolution);
        localData.setNvsRational(nvsRational);
        localData.setProjectId(mProjectId);
        localData.setProjectName(mProjectName);
        localData.setLastModifiedTime(mLastModifiedTime);
        localData.setProjectDuring(mProjectDuring);
        localData.setCoverImagePath(mCoverImagePath);
        localData.setDuration(duration);
        localData.setAddTitleTheme(isAddTitleTheme);
        localData.setTitleThemeDuration(titleThemeDuration);
        localData.setMakeRatio(mMakeRatio);
        localData.setCoverDataConfigPath(coverDataConfigPath);
        return localData;
    }


    /**
     * Parse resources
     *
     * @param localData the localData
     */
    private void parseResources(LTimelineData localData) {

        resourceList.clear();
        /*
         * videoClip和转场
         * videoClip and transition
         */
        for (MeicamVideoTrack meicamVideoTrack : videoTrackList) {
            int count = meicamVideoTrack.getClipCount();
            if (count > 0) {
                for (int index = 0; index < count; index++) {
                    meicamVideoTrack.getVideoClip(index).parseToResourceId(this);
                }
            }
            count = meicamVideoTrack.getTransitionCount();
            if (count > 0) {
                for (int index = 0; index < count; index++) {
                    meicamVideoTrack.getTransition(index).parseToResourceId(this);
                }
            }
        }
        /*
         * AudioClip
         * 音频
         */
        if (!audioTrackList.isEmpty()) {
            for (MeicamAudioTrack meicamAudioTrack : audioTrackList) {
                int clipCount = meicamAudioTrack.getClipCount();
                if (clipCount > 0) {
                    for (int index = 0; index < clipCount; index++) {
                        meicamAudioTrack.getAudioClip(index).parseToResourceId(this);
                    }
                }
            }
        }
        /*
         * Sticker
         * 贴纸
         */
        if (!stickerCaptionTrackList.isEmpty()) {
            for (MeicamStickerCaptionTrack stickerCaptionTrack : stickerCaptionTrackList) {
                int clipCount = stickerCaptionTrack.getClipCount();
                if (clipCount > 0) {
                    for (int index = 0; index < clipCount; index++) {
                        ClipInfo<?> captionStickerClip = stickerCaptionTrack.getCaptionStickerClip(index);
                        if (captionStickerClip instanceof MeicamStickerClip) {
                            ((MeicamStickerClip) captionStickerClip).parseToResourceId(this);
                        }
                    }
                }
            }
        }
        /*
         * water mark
         * 水印
         */
        if (waterMark != null) {
            waterMark.parseToResourceId(this);
        }
        if (!CommonUtils.isEmpty(resourceList)) {
            for (MeicamResource meicamResource : resourceList) {
                localData.getMeicamResourceList().add(meicamResource.parseToLocalData());
            }
        }
    }

    @Override
    public void recoverFromLocalData(LTimelineData local) {
        List<LMeicamResource> lResourceList = local.getMeicamResourceList();
        if (!CommonUtils.isEmpty(lResourceList)) {
            resourceList = new ArrayList<>();
            for (LMeicamResource meicamResource : lResourceList) {
                MeicamResource resource = new MeicamResource();
                resource.recoverFromLocalData(meicamResource);
                resourceList.add(resource);
            }
        }
        List<LMeicamVideoTrack> lMeicamVideoTrackList = local.getMeicamVideoTrackList();
        if (CommonUtils.isEmpty(lMeicamVideoTrackList)) {
            return;
        }

        for (LMeicamVideoTrack lMeicamVideoTrack : lMeicamVideoTrackList) {
            MeicamVideoTrack meicamVideoTrack = appendVideoTrack();
            if (meicamVideoTrack != null) {
                meicamVideoTrack.recoverFromLocalData(lMeicamVideoTrack);
            }
        }

        List<LMeicamAudioTrack> lMeicamAudioTrackList = local.getMeicamAudioTrackList();
        if (!CommonUtils.isEmpty(lMeicamAudioTrackList)) {
            for (LMeicamAudioTrack lMeicamAudioTrack : lMeicamAudioTrackList) {
                MeicamAudioTrack meicamAudioTrack = appendAudioTrack();
                if (meicamAudioTrack != null) {
                    meicamAudioTrack.recoverFromLocalData(lMeicamAudioTrack);
                }
            }
        }
        List<LMeicamStickerCaptionTrack> meicamStickerCaptionTrackList = local.getMeicamStickerCaptionTrackList();
        if (!CommonUtils.isEmpty(meicamStickerCaptionTrackList)) {
            int trackIndex = 0;
            for (int index = 0; index < meicamStickerCaptionTrackList.size(); index++) {
                MeicamStickerCaptionTrack meicamStickerCaptionTrack = addStickCaptionTrack(trackIndex, false);
                if (meicamStickerCaptionTrack != null) {
                    meicamStickerCaptionTrack.recoverFromLocalData(meicamStickerCaptionTrackList.get(index));
                    trackIndex++;
                }
            }
        }
        List<LMeicamTimelineVideoFxTrack> meicamTimelineVideoFxTrackList = local.getMeicamTimelineVideoFxTrackList();
        if (!CommonUtils.isEmpty(meicamTimelineVideoFxTrackList)) {
            for (LMeicamTimelineVideoFxTrack lMeicamTimelineVideoFxTrack : meicamTimelineVideoFxTrackList) {
                if (CommonData.TRACK_TIMELINE_FX.equals(lMeicamTimelineVideoFxTrack.getType())) {
                    MeicamTimelineVideoFxTrack timelineVideoFxTrack = addTimelineFxTrack(lMeicamTimelineVideoFxTrack.getIndex(), false);
                    if (timelineVideoFxTrack != null) {
                        timelineVideoFxTrack.recoverFromLocalData(lMeicamTimelineVideoFxTrack);
                    }
                } else {
                    MeicamTimelineVideoFxTrack timelineVideoFliterAndAdjustTrack = addFilterAndAdjustTrack(lMeicamTimelineVideoFxTrack.getIndex(), false);
                    if (timelineVideoFliterAndAdjustTrack != null) {
                        timelineVideoFliterAndAdjustTrack.recoverFromLocalData(lMeicamTimelineVideoFxTrack);
                    }
                }
            }
        }

        List<LMeicamTimelineVideoFxClip> timelineVideoFxClipList = local.getMeicamTimelineVideoFxClipList();
        if (!CommonUtils.isEmpty(timelineVideoFxClipList)) {
            for (LMeicamTimelineVideoFxClip lVideoFxClip : timelineVideoFxClipList) {
                MeicamTimelineVideoFxClip timelineVideoFxClip = addTimelineVideoFxInClipList(lVideoFxClip.getClipType(), lVideoFxClip.getInPoint(),
                        lVideoFxClip.getOutPoint() - lVideoFxClip.getInPoint(), lVideoFxClip.getDesc());
                if (timelineVideoFxClip != null) {
                    timelineVideoFxClip.recoverFromLocalData(lVideoFxClip);
                }
            }
        }

        LMeicamWaterMark lWaterMark = local.getMeicamWaterMark();
        if (lWaterMark != null) {
            MeicamWaterMark meicamWaterMark = addWatermark(lWaterMark.getWatermarkPath(), lWaterMark.getWatermarkW(), lWaterMark.getWatermarkH(),
                    lWaterMark.getWatermarkX(), lWaterMark.getWatermarkY());
            if (meicamWaterMark != null) {
                meicamWaterMark.recoverFromLocalData(lWaterMark);
            }
        }

        //TODO 主题暂时不添加
        setAddTitleTheme(false);
//        LMeicamAdjustData meicamAdjustData = local.getMeicamAdjustData();
//        if (meicamAdjustData != null) {
//            Map<String, String> keyToAdjustFxMap = NvsConstants.getKeyToAdjustFxMap();
//            Set<Map.Entry<String, String>> entries = keyToAdjustFxMap.entrySet();
//            Map<String, Float> keyToValueMap = meicamAdjustData.getKeyToValueMap();
//            for (Map.Entry<String, String> entry : entries) {
//                MeicamTimelineVideoFxTrack timelineVideoFliterAndAdjustTrack = addTimelineFilterAndAdjustTrack();
//                if (timelineVideoFliterAndAdjustTrack != null) {
//                    MeicamTimelineVideoFilterAndAdjustClip meicamTimelineVideoFilterAndAdjustClip =
//                            timelineVideoFliterAndAdjustTrack.addFilterAndAdjustClip(MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_ADJUST, 0, getDuration());
//                    MeicamTimelineVideoFxClip adjustVideoFx = meicamTimelineVideoFilterAndAdjustClip.addAdjustTimelineFx(TYPE_BUILD_IN, MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FILTER,
//                            entry.getValue());
//                    if (adjustVideoFx != null) {
//                        Float aFloat = keyToValueMap.get(entry.getKey());
//                        if (aFloat == null || aFloat < 0) {
//                            aFloat = 0F;
//                        }
//                        adjustVideoFx.setFloatVal(entry.getKey(), aFloat);
//                    }
//                }
//            }
//            adjustData = null;
//        }

        //LMeicamTimelineVideoFx filterFx = (LMeicamTimelineVideoFx) local.getFilterFx();
        //if (filterFx != null) {
           /* MeicamTimelineVideoFxTrack timelineVideoFliterAndAdjustTrack = addFilterAndAdjustTrack(0);
            if (timelineVideoFliterAndAdjustTrack != null) {
                MeicamTimelineVideoFilterAndAdjustClip meicamTimelineVideoFilterAndAdjustClip =
                        timelineVideoFliterAndAdjustTrack.addFilterAndAdjustClip(MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FILTER, filterFx.getInPoint(), filterFx.getOutPoint() - filterFx.getInPoint());
                meicamTimelineVideoFilterAndAdjustClip.addAdjustTimelineFx(filterFx.getType(), MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FILTER,
                        filterFx.getDesc());
                setFilterFx(null);
            }*/
//
//            MeicamTimelineVideoFx videoFx = buildTimelineFilter(filterFx.getType(),
//                    filterFx.getDesc(), TRACK_INDEX_MAIN);
//            if (videoFx != null) {
//                videoFx.recoverFromLocalData(filterFx);
//            }
//            setFilterFx(videoFx);
        //}
        setVideoResolution(local.getVideoResolution());
        if (nvsVideoResolution == null) {
            UMengUtils.generateCustomLog("parseToTimelineData  nvsVideoResolution==null");
        }
        setNvsRational(local.getNvsRational());
        setProjectId(local.getProjectId());
        setProjectName(local.getProjectName());
        setLastModifiedTime(local.getLastModifiedTime());
        setProjectDuring(local.getProjectDuring());
        setCoverImagePath(local.getCoverImagePath());
        setDuration(local.getDuration());
        setAddTitleTheme(local.isAddTitleTheme());
        setTitleThemeDuration(local.getTitleThemeDuration());
        setMakeRatio(local.getMakeRatio());
        setCoverDataConfigPath(local.getCoverDataConfigPath());

    }

    @Override
    public void recoverFromTimelineData(com.meicam.sdk.NvsObject nvsObject) {
        if (!(nvsObject instanceof NvsTimeline)) {
            return;
        }
        NvsTimeline nvsTimeline = (NvsTimeline) nvsObject;
        nvsTimeline.enableRenderOrderByZValue(true);
        this.nvsVideoResolution = nvsTimeline.getVideoRes();
        this.nvsRational = nvsTimeline.getVideoFps();
        /*
         * Restore the video track
         * 恢复视频轨道
         **/
        int videoTrackCount = nvsTimeline.videoTrackCount();
        for (int index = 0; index < videoTrackCount; index++) {
            NvsVideoTrack nvsVideoTrack = nvsTimeline.getVideoTrackByIndex(index);
            if (nvsVideoTrack != null && nvsVideoTrack.getClipCount() <= 0) {
                nvsTimeline.removeVideoTrack(index);
                realMainTrackIndex++;
                index--;
            } else {
                break;
            }
        }
        videoTrackCount = nvsTimeline.videoTrackCount();
        for (int i = 0; i < videoTrackCount; i++) {
            NvsVideoTrack nvsVideoTrack = nvsTimeline.getVideoTrackByIndex(i);
            MeicamVideoTrack meicamVideoTrack = appendVideoTrack(nvsVideoTrack);
            meicamVideoTrack.recoverFromTimelineData(nvsVideoTrack);
        }

        /*
         * Restore the audio
         * 恢复音频
         **/
        int audioTrackCount = nvsTimeline.audioTrackCount();
        for (int i = 0; i < audioTrackCount; i++) {
            NvsAudioTrack nvsAudioTrack = nvsTimeline.getAudioTrackByIndex(i);
            MeicamAudioTrack meicamAudioTrack = appendMeicamAudioTrack(nvsAudioTrack);
            if (meicamAudioTrack != null) {
                meicamAudioTrack.recoverFromTimelineData(nvsAudioTrack);
            }
        }

        /*
         * Restore subtitles, stickers, combined subtitles
         * 恢复字幕,贴纸，组合字幕
         **/
        List<ClipInfo<?>> stickerCaptionList = new ArrayList<>();
        NvsTimelineCaption timelineCaption = nvsTimeline.getFirstCaption();
        if (timelineCaption != null) {
            while (timelineCaption != null) {
                stickerCaptionList.add(new MeicamCaptionClip(timelineCaption, timelineCaption.getText(), timelineCaption.getInPoint(), timelineCaption.getOutPoint()));
                timelineCaption = nvsTimeline.getNextCaption(timelineCaption);
            }
        }

//        getAllTrackCaption(stickerCaptionList, nvsTimeline);


        NvsTimelineAnimatedSticker timelineAnimatedSticker = nvsTimeline.getFirstAnimatedSticker();
        if (timelineAnimatedSticker != null) {
            while (timelineAnimatedSticker != null) {
                stickerCaptionList.add(new MeicamStickerClip(timelineAnimatedSticker, timelineAnimatedSticker.getInPoint(), timelineAnimatedSticker.getOutPoint(), null, false, null));
                timelineAnimatedSticker = nvsTimeline.getNextAnimatedSticker(timelineAnimatedSticker);
            }
        }

        NvsTimelineCompoundCaption timelineCompoundCaption = nvsTimeline.getFirstCompoundCaption();
        if (timelineCompoundCaption != null) {
            while (timelineCompoundCaption != null) {
                stickerCaptionList.add(new MeicamCompoundCaptionClip(timelineCompoundCaption));
                timelineCompoundCaption = nvsTimeline.getNextCaption(timelineCompoundCaption);
            }
        }

        //List<ClipInfo<?>> allClips = new ArrayList<>();
       /* if (!stickerCaptionList.isEmpty()) {
            allClips.addAll(stickerCaptionList);
        }*/
        /*
         * 滤镜和调节数据
         * Filter and adjust data
         **/
        List<ClipInfo<?>> filterAndAdjustList = new ArrayList<>();
        /*
         * 马赛克和模糊数据
         * Mosaic and blur data
         **/
        List<ClipInfo<?>> maskList = new ArrayList<>();
        /*
         * 特效数据
         * Effect data
         **/
        List<ClipInfo<?>> effectList = new ArrayList<>();

        NvsTimelineVideoFx nvsTimelineVideoFx = nvsTimeline.getFirstTimelineVideoFx();
        while (nvsTimelineVideoFx != null) {
            MeicamTimelineVideoFxClip videoFxClip = new MeicamTimelineVideoFxClip(nvsTimelineVideoFx, "", nvsTimelineVideoFx.getInPoint(), nvsTimelineVideoFx.getOutPoint() - nvsTimelineVideoFx.getInPoint(), "");
            //allClips.add(videoFxClip);
            String packageId = nvsTimelineVideoFx.getTimelineVideoFxPackageId();
            if (!TextUtils.isEmpty(packageId)) {
                //特效
                //Effect
                FileInfoBridge.FileInfo fileInfo = FileInfoBridge.getFileInfo(packageId);
                if (fileInfo != null) {
                    if (fileInfo.category == 1) {
                        //滤镜
                        //Filter
                        filterAndAdjustList.add(videoFxClip);
                        videoFxClip.setSubType(MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FILTER);
                    } else if (fileInfo.category == 2) {
                        //特效
                        //Effect
                        videoFxClip.setSubType(MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FX);
                        effectList.add(videoFxClip);
                    }
                } else {
                    //默认是特效
                    //The default is effect
                    videoFxClip.setSubType(MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FX);
                    effectList.add(videoFxClip);
                }
            } else {
                String fxName = nvsTimelineVideoFx.getBuiltinTimelineVideoFxName();
                if (!TextUtils.isEmpty(fxName)) {
                    switch (fxName) {
                        case NvsConstants.MOSAICNAME:
                            videoFxClip.setSubType(MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_MOSAIC);
                            maskList.add(videoFxClip);
                            break;
                        case NvsConstants.BLURNAME:
                            videoFxClip.setSubType(MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_BLUR);
                            maskList.add(videoFxClip);
                            break;
                        case NvsConstants.FX_SHARPEN:
                        case NvsConstants.FX_VIGNETTE:
                        case NvsConstants.ADJUST_TYPE_BASIC_IMAGE_ADJUST:
                        case NvsConstants.ADJUST_TINT:
                            //是调节的参数
                            //This is adjust data
                            videoFxClip.setSubType(MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_ADJUST);
                            filterAndAdjustList.add(videoFxClip);
                            break;
                        default:
                            effectList.add(videoFxClip);
                            break;
                    }
                }
            }
            nvsTimelineVideoFx = nvsTimeline.getNextTimelineVideoFx(nvsTimelineVideoFx);
        }

        /*
         * Restore filters, adjustments and mosaic
         * 恢复滤镜和调节,马赛克信息
         **/
        Set<Float> zValues = new HashSet<>();
        if (!effectList.isEmpty()) {
            for (ClipInfo<?> clipInfo : stickerCaptionList) {
                zValues.add(clipInfo.getZValue());
            }
        }
        boolean needResetTrackIndex = zValues.size() > 1;

        if (needResetTrackIndex) {
            resetClipTrackIndex(stickerCaptionList);
        }
        for (ClipInfo<?> clipInfo : stickerCaptionList) {
            int trackIndex;
            if (needResetTrackIndex) {
                trackIndex = clipInfo.getTrackIndex();
            } else {
                trackIndex = EditorEngine.getTrackIndex(this, clipInfo.getInPoint(), clipInfo.getOutPoint());
            }
            MeicamStickerCaptionTrack meicamStickerCaptionTrack = addStickCaptionTrack(trackIndex, false);
            if (meicamStickerCaptionTrack != null) {
                meicamStickerCaptionTrack.setZValue(clipInfo.getZValue());
                meicamStickerCaptionTrack.recoverFromTimelineData((com.meicam.sdk.NvsObject) clipInfo.getObject());
            }
        }

        resetClipTrackIndex(filterAndAdjustList);
        for (ClipInfo<?> clipInfo : filterAndAdjustList) {
            if (clipInfo instanceof MeicamTimelineVideoFxClip) {
                addFilterOrAdjust((MeicamTimelineVideoFxClip) clipInfo);
            }
        }

        resetClipTrackIndex(maskList);
        for (ClipInfo<?> clipInfo : maskList) {
            if (clipInfo instanceof MeicamTimelineVideoFxClip) {
                NvsTimelineVideoFx object = (NvsTimelineVideoFx) clipInfo.getObject();
                MeicamTimelineVideoFxClip meicamTimelineVideoFxClip = addMeicamTimelineVideoFxInClipList(object);
                if (meicamTimelineVideoFxClip != null) {
                    meicamTimelineVideoFxClip.setZValue(clipInfo.getZValue());
                    meicamTimelineVideoFxClip.recoverFromTimelineData(object);
                }
            }
        }
        zValues.clear();
        if (!effectList.isEmpty()) {
            for (ClipInfo<?> clipInfo : effectList) {
                zValues.add(clipInfo.getZValue());
            }
        }
        needResetTrackIndex = zValues.size() > 1;
        if (needResetTrackIndex) {
            resetClipTrackIndex(effectList);
        }
        for (ClipInfo<?> clipInfo : effectList) {
            if (clipInfo instanceof MeicamTimelineVideoFxClip) {
                int trackIndex;
                if (needResetTrackIndex) {
                    trackIndex = clipInfo.getTrackIndex();
                } else {
                    trackIndex = EditorEngine.getEffectTrackIndex(this, clipInfo.getInPoint(), clipInfo.getOutPoint());
                }
                MeicamTimelineVideoFxTrack timelineVideoFxTrack = addTimelineFxTrack(trackIndex, false);
                if (timelineVideoFxTrack != null) {
                    timelineVideoFxTrack.setZValue(clipInfo.getZValue());
                    timelineVideoFxTrack.recoverFromTimelineData((com.meicam.sdk.NvsObject) clipInfo.getObject());
                }
            }
        }
        //最后重置zValue
        //Set z value at last
        //resetClipZValue(allClips);
        setDuration(nvsTimeline.getDuration());
    }

    private void getAllTrackCaption(List<ClipInfo<?>> stickerCaptionList, NvsTimeline nvsTimeline) {
        for (int i = 0; i < nvsTimeline.videoTrackCount(); i++) {
            NvsVideoTrack nvsVideoTrack = nvsTimeline.getVideoTrackByIndex(i);
            NvsTrackCaption nvsTrackCaption = nvsVideoTrack.getFirstCaption();
            while (nvsTrackCaption != null) {
                stickerCaptionList.add(new MeicamCaptionClip(nvsTrackCaption,
                        nvsTrackCaption.getText(), nvsTrackCaption.getInPoint(), nvsTrackCaption.getOutPoint()));
                nvsTrackCaption = nvsVideoTrack.getNextCaption(nvsTrackCaption);
            }
            for (int j = 0; j < nvsVideoTrack.getClipCount(); j++) {
                NvsVideoClip nvsVideoClip = nvsVideoTrack.getClipByIndex(j);
                NvsTimeline timeline = nvsVideoClip.getInternalTimeline();
                if (timeline != null) {
                    getAllTrackCaption(stickerCaptionList, timeline);
                }
            }
        }
    }


    public void resetClipTrackIndex(List<ClipInfo<?>> filterList) {
        Collections.sort(filterList, new Comparator<ClipInfo<?>>() {
            @Override
            public int compare(ClipInfo<?> o1, ClipInfo<?> o2) {
                return (int) (o1.getZValue() - o2.getZValue());
            }
        });
        int mLastZValue = 0;
        int lastRightZValue = 0;
        for (int index = 0; index < filterList.size(); index++) {
            ClipInfo<?> videoFx = filterList.get(index);
            if (index == 0) {
                mLastZValue = (int) videoFx.getZValue();
                videoFx.setTrackIndex(lastRightZValue);
                continue;
            }
            if ((int) videoFx.getZValue() != mLastZValue) {
                lastRightZValue++;
            }
            mLastZValue = (int) videoFx.getZValue();
            videoFx.setTrackIndex(lastRightZValue);
        }
    }

    public void resetClipZValue(List<ClipInfo<?>> filterList) {
        Collections.sort(filterList, new Comparator<ClipInfo<?>>() {
            @Override
            public int compare(ClipInfo<?> o1, ClipInfo<?> o2) {
                return (int) (o1.getZValue() - o2.getZValue());
            }
        });
        int mLastZValue = 0;
        int lastRightZValue = 0;
        for (int index = 0; index < filterList.size(); index++) {
            ClipInfo<?> videoFx = filterList.get(index);
            if (index == 0) {
                mLastZValue = (int) videoFx.getZValue();
                videoFx.setZValue(lastRightZValue);
                continue;
            }
            if ((int) videoFx.getZValue() != mLastZValue) {
                lastRightZValue++;
            }
            mLastZValue = (int) videoFx.getZValue();
            videoFx.setZValue(lastRightZValue);
        }
    }

    private void addFilterOrAdjust(MeicamTimelineVideoFxClip clipInfo) {
        /* int trackIndex = isFilter? EditorEngine.getFilterAndAdjustTimelineFxTrackIndex(this, clipInfo.getInPoint(), clipInfo.getOutPoint()) : (int) clipInfo.getZValue();*/
        MeicamTimelineVideoFxTrack addFilterAndAdjustTrack = addFilterAndAdjustTrack((int) clipInfo.getTrackIndex(), false);
        if (addFilterAndAdjustTrack != null) {
            addFilterAndAdjustTrack.setZValue(clipInfo.getZValue());
            addFilterAndAdjustTrack.recoverFromTimelineData(clipInfo.getObject());
        }
    }

    public boolean isAddTitleTheme() {
        return isAddTitleTheme;
    }

    public void setAddTitleTheme(boolean addTitleTheme) {
        isAddTitleTheme = addTitleTheme;
    }

    public long getTitleThemeDuration() {
        return titleThemeDuration;
    }

    public void setTitleThemeDuration(long titleThemeLength) {
        this.titleThemeDuration = titleThemeLength;
    }

    public String getPlaceId(MeicamResource resource) {
        if (resource == null) {
            return "";
        }
        if (resourceList.contains(resource)) {
            return String.valueOf(resourceList.indexOf(resource));
        } else {
            resource.setId(String.valueOf(resourceList.size()));
            resourceList.add(resource);
            return resource.getId();
        }
    }


    /**
     * 添加字幕，贴纸组合字幕轨道
     * Add stick caption track meicam sticker caption track.
     *
     * @param trackIndex the track index
     * @return the meicam sticker caption track
     */
    public MeicamStickerCaptionTrack addStickCaptionTrack(int trackIndex) {
        return addStickCaptionTrack(trackIndex, true);
    }


    /**
     * 添加字幕，贴纸组合字幕轨道
     * Add stick caption track meicam sticker caption track.
     *
     * @param trackIndex       the track index
     * @param needResortZValue the need resort z value 是否需要重排ZValue
     * @return the meicam sticker caption track
     */
    private MeicamStickerCaptionTrack addStickCaptionTrack(int trackIndex, boolean needResortZValue) {
        if (trackIndex < 0) {
            return null;
        }
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("nvsTimeline is null");
            return null;
        }
        int trackSize = stickerCaptionTrackList.size();
        if (trackIndex >= trackSize) {
            LogUtils.e("addStickCaptionTrack: trackIndex is bigger than trackSize。 trackIndex = " + trackIndex + "  trackSize = " + trackSize);
            //尾部补充空轨道
            //Tail supplement empty track
            for (int index = trackSize; index <= trackIndex; index++) {
                stickerCaptionTrackList.add(new MeicamStickerCaptionTrack(nvsTimeline, index));
            }
        }
        MeicamStickerCaptionTrack meicamStickerCaptionTrack;
        if (trackSize == trackIndex) {
            meicamStickerCaptionTrack = stickerCaptionTrackList.get(trackIndex);
            if (needResortZValue) {
                resortTrackZValue(meicamStickerCaptionTrack);
            }
        } else {
            meicamStickerCaptionTrack = stickerCaptionTrackList.get(trackIndex);
        }
        return meicamStickerCaptionTrack;
    }

    /**
     * 重新设置track的zValue值
     */
    private void resortTrackZValue(TrackInfo<?> trackInfo) {
        if (BuildConfig.DEBUG) {
            List<TrackInfo<?>> sortTracks = new ArrayList<>();
            if (!stickerCaptionTrackList.isEmpty()) {
                sortTracks.addAll(stickerCaptionTrackList);
            }
            if (!filterAndAdjustTimelineTracks.isEmpty()) {
                sortTracks.addAll(filterAndAdjustTimelineTracks);
            }
            if (!timelineVideoFxTracks.isEmpty()) {
                sortTracks.addAll(timelineVideoFxTracks);
            }
            for (TrackInfo<?> sortTrack : sortTracks) {
                LogUtils.d("sortTrackByZValue before : sortTrack = " + sortTrack.getzValue()
                        + ", type = " + sortTrack.getType());
            }
        }

        if (trackInfo instanceof MeicamStickerCaptionTrack) {
            if (!stickerCaptionTrackList.isEmpty()) {
                int lastIndex = stickerCaptionTrackList.size() - 1;
                MeicamStickerCaptionTrack track = stickerCaptionTrackList.get(lastIndex);
                // 这里加100是为了保证字幕、贴纸和组合字幕永远在VideoFx上面
                //To ensure that subtitles, stickers and combined subtitles are always on videoFx,
                // we add the index by 100
                int zValue = lastIndex + 100;
                track.setZValue(zValue);
            }
        } else if (trackInfo instanceof MeicamTimelineVideoFxTrack) {
            String type = trackInfo.getType();
            if (CommonData.TRACK_TIMELINE_FILTER_ADJUST.equals(type)) {
                if (!filterAndAdjustTimelineTracks.isEmpty()) {
                    int lastIndex = filterAndAdjustTimelineTracks.size() - 1;
                    MeicamTimelineVideoFxTrack track = filterAndAdjustTimelineTracks.get(lastIndex);
                    int zValue = lastIndex + timelineVideoFxTracks.size();
                    track.setZValue(zValue);
                }
            } else if (CommonData.TRACK_TIMELINE_FX.equals(type)) {
                if (!timelineVideoFxTracks.isEmpty()) {
                    int lastIndex = timelineVideoFxTracks.size() - 1;
                    MeicamTimelineVideoFxTrack track = timelineVideoFxTracks.get(lastIndex);
                    int zValue = lastIndex + filterAndAdjustTimelineTracks.size();
                    track.setZValue(zValue);
                }
            }
        }
        if (BuildConfig.DEBUG) {
            List<TrackInfo<?>> sortTracks = new ArrayList<>();
            if (!stickerCaptionTrackList.isEmpty()) {
                sortTracks.addAll(stickerCaptionTrackList);
            }
            if (!filterAndAdjustTimelineTracks.isEmpty()) {
                sortTracks.addAll(filterAndAdjustTimelineTracks);
            }
            if (!timelineVideoFxTracks.isEmpty()) {
                sortTracks.addAll(timelineVideoFxTracks);
            }
            for (TrackInfo<?> sortTrack : sortTracks) {
                LogUtils.d("sortTrackByZValue after : sortTrack = " + sortTrack.getzValue()
                        + ", type = " + sortTrack.getType());
            }
        }
    }

    /**
     * 根据trackIndex找到字幕，贴纸轨道
     * Find stick caption track meicam sticker caption track.
     *
     * @param trackIndex the track index
     * @return the meicam sticker caption track
     */
    public MeicamStickerCaptionTrack findStickCaptionTrack(int trackIndex) {
        if (!CommonUtils.isIndexAvailable(trackIndex, stickerCaptionTrackList)) {
            return null;
        }
        return stickerCaptionTrackList.get(trackIndex);
    }

    /**
     * 根据trackIndex删除字幕，贴纸轨道
     * Find stick caption track meicam sticker caption track.
     *
     * @param trackIndex the track index
     * @return the meicam sticker caption track
     */
    public boolean removeStickCaptionTrack(int trackIndex) {
        if (!CommonUtils.isIndexAvailable(trackIndex, stickerCaptionTrackList)) {
            return false;
        }
        MeicamStickerCaptionTrack meicamStickerCaptionTrack = stickerCaptionTrackList.get(trackIndex);
        if (meicamStickerCaptionTrack.getClipCount() == 0) {
            for (int i = trackIndex + 1; i < stickerCaptionTrackList.size(); i++) {
                MeicamStickerCaptionTrack track = stickerCaptionTrackList.get(i);
                track.setIndex(track.getIndex() - 1);
            }
            stickerCaptionTrackList.remove(trackIndex);
        }
        return true;
    }

    /**
     * 添加水印
     * Add watermark meicam water mark.
     *
     * @param path   the path
     * @param width  the width
     * @param height the height
     * @param x      the x
     * @param y      the y
     * @return the meicam water mark
     */
    public MeicamWaterMark addWatermark(String path, int width, int height, int x, int y) {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("nvsTimeline is null");
            return null;
        }
        nvsTimeline.deleteWatermark();
        nvsTimeline.addWatermark(path, width, height, 1
                , NvsTimeline.NvsTimelineWatermarkPosition_TopLeft, x, y);
        if (waterMark != null) {
            waterMark.setDisplayWidth(width);
            waterMark.setDisplayHeight(height);
            waterMark.setWatermarkFilePath(path);
            waterMark.setMarginX(x);
            waterMark.setMarginY(y);
        } else {
            waterMark = new MeicamWaterMark(path, width, height, x, y);
        }

        return waterMark;
    }


    /**
     * 删除水印
     * Delete watermark.
     *
     * @param resetWaterMark the reset water mark  是否重置上层数据 true重置 false不重置
     */
    public void deleteWatermark(boolean resetWaterMark) {
        NvsTimeline object = getObject();
        if (object != null) {
            object.deleteWatermark();
        }
        if (resetWaterMark) {
            waterMark = new MeicamWaterMark(null, null);
        }
    }

    /**
     * Apply theme meicam theme.
     * 应用主题
     *
     * @param themeId the theme id
     * @return the meicam theme
     */
    public boolean applyTheme(String themeId) {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("nvsTimeline is null");
            return false;
        }
        nvsTimeline.removeCurrentTheme();
        boolean isSuccess = nvsTimeline.applyTheme(themeId);
        if (!isSuccess) {
            return false;
        }
        theme = new MeicamTheme(themeId);
        nvsTimeline.setThemeMusicVolumeGain(1.0f, 1.0f);
        return true;
    }


    /**
     * Clear timeline fx from track.
     * 清除时间线特效轨道
     */
    private void clearTimelineFxTrack() {
        int fxTrackCount = getTimelineFxTrackCount();
        if (fxTrackCount > 0) {
            for (int index = fxTrackCount - 1; index >= 0; index--) {
                MeicamTimelineVideoFxTrack timelineFxTrack = getTimelineFxTrack(index);
                timelineFxTrack.clearClip();
            }
        }
        timelineVideoFxTracks.clear();
    }


    /**
     * Clear timeline filter and adjust from track.
     * 清除时间线滤镜和调节轨道
     */
    private void clearTimelineFilterAndAdjustTrack() {
        int fxTrackCount = getFilterAndAdjustTimelineTracksCount();
        if (fxTrackCount > 0) {
            for (int index = fxTrackCount - 1; index >= 0; index--) {
                MeicamTimelineVideoFxTrack timelineFxTrack = getFilterAndAdjustTimelineTrack(index);
                timelineFxTrack.clearClip();
            }
        }
        filterAndAdjustTimelineTracks.clear();
    }


    /**
     * Clear timeline fx from clip list.
     * 从时间线特效clip list中删除特效
     */
    public void clearTimelineFxClipList() {
        int fxClipCount = getTimelineFxClipCount();
        if (fxClipCount > 0) {
            for (int index = fxClipCount - 1; index >= 0; index--) {
                removeTimelineFxFromClipList(index);
            }
        }
        timelineVideoFxClips.clear();
    }


    /**
     * Remove theme.
     * 删除主题
     */
    public void removeTheme() {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("nvsTimeline is null");
            return;
        }
        nvsTimeline.removeCurrentTheme();
        theme = null;
    }

    /**
     * Sets theme quiet.
     * 设置主题静音
     */
    public void setThemeQuiet() {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("nvsTimeline is null");
            return;
        }
        if (theme != null) {
            String themePackageId = theme.getThemePackageId();
            if (!TextUtils.isEmpty(themePackageId)) {
                nvsTimeline.setThemeMusicVolumeGain(0f, 0f);
            }
        }
    }


    /**
     * Restore theme volume.
     * 设置主题音量
     */
    public void restoreThemeVolume() {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("nvsTimeline is null");
            return;
        }
        if (theme != null) {
            String themePackageId = theme.getThemePackageId();
            if (!TextUtils.isEmpty(themePackageId)) {
                nvsTimeline.setThemeMusicVolumeGain(1.0f, 1.0f);
            }
        }
    }

    /**
     * Get timelineFx from TimelineFxClip
     * <P></l>
     * 从TimelineFxFromClip中移除特效
     *
     * @param index the index
     * @return 是否移除成功 Is success or not.true:yes; false:no.
     */
    public MeicamTimelineVideoFxClip getTimelineFxFromClipList(int index) {
        if (CommonUtils.isIndexAvailable(index, timelineVideoFxClips)) {
            return timelineVideoFxClips.get(index);
        }
        return null;
    }

    /**
     * 获取时间线特效片段的数量
     * Get timeline video fx clip count
     */
    public int getTimelineVideoFxClipCount() {
        return timelineVideoFxClips.size();
    }

    /**
     * Remove timelineFx from TimelineFxClip
     * <P></l>
     * 从TimelineFxFromClip中移除特效
     *
     * @param timelineVideoFx 特效 timeline VideoFx
     * @return 是否移除成功 Is success or not.true:yes; false:no.
     */
    public boolean removeTimelineFxFromClipList(MeicamTimelineVideoFxClip timelineVideoFx) {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null || timelineVideoFx == null) {
            return false;
        }
        nvsTimeline.removeTimelineVideoFx(timelineVideoFx.getObject());
        int index = timelineVideoFx.getIndex();
        if (CommonUtils.isIndexAvailable(index, timelineVideoFxClips)) {
            timelineVideoFxClips.remove(index);
            return true;
        }
        return false;
    }

    /**
     * Remove timelineFx from TimelineFxClip
     * <P></l>
     * 从TimelineFxFromClip中移除特效
     *
     * @param index 索引 the index
     * @return 是否移除成功 Is success or not.true:yes; false:no.
     */
    public boolean removeTimelineFxFromClipList(int index) {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            return false;
        }
        if (!CommonUtils.isIndexAvailable(index, timelineVideoFxClips)) {
            LogUtils.e("index is invalid");
            return false;
        }
        MeicamTimelineVideoFxClip timelineVideoFx = timelineVideoFxClips.get(index);
        nvsTimeline.removeTimelineVideoFx(timelineVideoFx.getObject());
        timelineVideoFxClips.remove(index);
        return true;
    }


    /**
     * Add timeline videoFx in MeicamTimelineVideoFxClipList
     * <P></l>
     * 往MeicamTimelineVideoFxClipList中添加timeline特效
     *
     * @param type        特效类型 type builtin:内建特效 package：包特效
     * @param inPoint     入点 inPoint
     * @param duration    时长 duration
     * @param videoFxName 特效名称 videoFx Name
     * @return 特效 timeline videoFx
     */
    public MeicamTimelineVideoFxClip addTimelineVideoFxInClipList(String type, long inPoint, long duration, String videoFxName) {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null || TextUtils.isEmpty(type)) {
            LogUtils.e("nvsTimeline is null");
            return null;
        }
        NvsTimelineVideoFx nvsTimelineVideoFx;
        if (CommonData.TYPE_BUILD_IN.equals(type)) {
            nvsTimelineVideoFx = nvsTimeline.addBuiltinTimelineVideoFx(inPoint, duration, videoFxName);
        } else {
            nvsTimelineVideoFx = nvsTimeline.addPackagedTimelineVideoFx(inPoint, duration, videoFxName);
        }
        return addMeicamTimelineVideoFxInClipList(nvsTimelineVideoFx);
    }

    /**
     * 添加马赛克上层数据
     * Add meicam timeline video fx in clip list meicam timeline video fx clip.
     *
     * @param nvsTimelineVideoFx the nvs timeline video fx
     * @return the meicam timeline video fx clip
     */
    MeicamTimelineVideoFxClip addMeicamTimelineVideoFxInClipList(NvsTimelineVideoFx nvsTimelineVideoFx) {
        if (nvsTimelineVideoFx == null) {
            return null;
        }
        if (timelineVideoFxClips == null) {
            timelineVideoFxClips = new ArrayList<>();
        }
        int fxType = nvsTimelineVideoFx.getTimelineVideoFxType();
        String desc = fxType == NvsTimelineVideoFx.TIMELINE_VIDEOFX_TYPE_BUILTIN
                ? nvsTimelineVideoFx.getBuiltinTimelineVideoFxName() : nvsTimelineVideoFx.getTimelineVideoFxPackageId();
        MeicamTimelineVideoFxClip videoFxClip = new MeicamTimelineVideoFxClip(nvsTimelineVideoFx,
                fxType == NvsTimelineVideoFx.TIMELINE_VIDEOFX_TYPE_BUILTIN ?
                        TYPE_BUILD_IN : TYPE_PACKAGE, nvsTimelineVideoFx.getInPoint(), nvsTimelineVideoFx.getOutPoint() - nvsTimelineVideoFx.getInPoint(), desc);
        videoFxClip.setIntensity(1.0F);
        videoFxClip.setIndex(timelineVideoFxClips.size());
        if (NvsConstants.MOSAICNAME.equals(desc)) {
            videoFxClip.setSubType(MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_MOSAIC);
        } else if (NvsConstants.BLURNAME.equals(desc)) {
            videoFxClip.setSubType(MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_BLUR);
        } else {
            videoFxClip.setSubType(MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FX);
        }
        timelineVideoFxClips.add(videoFxClip);
        return videoFxClip;
    }

    /**
     * Add timeline videoFx in MeicamTimelineVideoFxClipList
     * <p>
     * 添加clip
     *
     * @param fxClip      特效数据 video fx data
     * @param inPoint     入点 inPoint
     * @param duration    时长 duration
     * @param videoFxName 特效名称 videoFx Name
     * @return 特效 timeline videoFx
     */
    public MeicamTimelineVideoFxClip addTimelineVideoFxInClipList(MeicamTimelineVideoFxClip fxClip, long inPoint, long duration, String videoFxName) {
        MeicamTimelineVideoFxClip videoFxClip = addTimelineVideoFxInClipList(fxClip.getClipType(), inPoint, duration, videoFxName);
        if (videoFxClip != null) {
            videoFxClip.copyData(fxClip);
        }
        return videoFxClip;
    }


    /**
     * 创建 timeline 特效轨道
     *
     * @param trackIndex the track index 轨道index
     * @return The MeicamTimelineVideoFxTrack
     */
    public MeicamTimelineVideoFxTrack addTimelineFxTrack(int trackIndex) {
        return addTimelineFxTrack(trackIndex, true);
    }


    /**
     * 创建 timeline 特效轨道
     * Add timeline fx track meicam timeline video fx track.
     *
     * @param trackIndex       the track index 轨道index
     * @param needResortZValue the need resort z value 是否需要重排zValue
     * @return the meicam timeline video fx track
     */
    private MeicamTimelineVideoFxTrack addTimelineFxTrack(int trackIndex, boolean needResortZValue) {
        if (trackIndex < 0) {
            return null;
        }
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("nvsTimeline is null");
            return null;
        }
        int trackSize = timelineVideoFxTracks.size();
        if (trackIndex >= trackSize) {
            LogUtils.e("addTimelineFxTrack: trackIndex is bigger than trackSize。 trackIndex = " + trackIndex + "  trackSize = " + trackSize);
            //尾部补充空轨道
            //Tail supplement empty track
            for (int index = trackSize; index <= trackIndex; index++) {
                timelineVideoFxTracks.add(new MeicamTimelineVideoFxTrack(nvsTimeline, index, CommonData.TRACK_TIMELINE_FX));
            }
        }
        MeicamTimelineVideoFxTrack meicamStickerCaptionTrack;
        if (trackSize == trackIndex) {
            meicamStickerCaptionTrack = timelineVideoFxTracks.get(trackIndex);
            if (needResortZValue) {
                resortTrackZValue(meicamStickerCaptionTrack);
            }
        } else {
            meicamStickerCaptionTrack = timelineVideoFxTracks.get(trackIndex);
        }
        return meicamStickerCaptionTrack;
    }


    /**
     * 创建 滤镜 调节轨道
     * Add filter and adjust track meicam timeline video fx track.
     *
     * @param trackIndex the track index 轨道index
     * @return the meicam timeline video fx track
     */
    public MeicamTimelineVideoFxTrack addFilterAndAdjustTrack(int trackIndex) {
        return addFilterAndAdjustTrack(trackIndex, true);
    }


    /**
     * 创建 滤镜 调节轨道
     * Add filter and adjust track meicam timeline video fx track.
     *
     * @param trackIndex      the track index 轨道index
     * @param needResetZValue the need reset z value 是否需要重排zValue
     * @return the meicam timeline video fx track
     */
    private MeicamTimelineVideoFxTrack addFilterAndAdjustTrack(int trackIndex, boolean needResetZValue) {
        if (trackIndex < 0) {
            return null;
        }
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("nvsTimeline is null");
            return null;
        }
        int trackSize = filterAndAdjustTimelineTracks.size();
        if (trackIndex >= trackSize) {
            LogUtils.e("addTimelineFxTrack: trackIndex is bigger than trackSize。 trackIndex = " + trackIndex + "  trackSize = " + trackSize);
            //尾部补充空轨道
            //Tail supplement empty track
            for (int index = trackSize; index <= trackIndex; index++) {
                filterAndAdjustTimelineTracks.add(new MeicamTimelineVideoFxTrack(nvsTimeline, index, CommonData.TRACK_TIMELINE_FILTER_ADJUST));
            }
        }
        MeicamTimelineVideoFxTrack meicamStickerCaptionTrack;
        if (trackSize == trackIndex) {
            meicamStickerCaptionTrack = filterAndAdjustTimelineTracks.get(trackIndex);
            if (needResetZValue) {
                resortTrackZValue(meicamStickerCaptionTrack);
            }
        } else {
            meicamStickerCaptionTrack = filterAndAdjustTimelineTracks.get(trackIndex);
        }
        return meicamStickerCaptionTrack;
    }

    /**
     * Remove filter and adjust track.
     * 删除调节和滤镜轨道，这里只是删除了上层数据
     *
     * @param trackIndex the track index
     */
    public MeicamTimelineVideoFxTrack removeFilterAndAdjustTrack(int trackIndex) {
        if (CommonUtils.isIndexAvailable(trackIndex, filterAndAdjustTimelineTracks)) {
            for (int i = trackIndex + 1; i < filterAndAdjustTimelineTracks.size(); i++) {
                MeicamTimelineVideoFxTrack videoTrack = filterAndAdjustTimelineTracks.get(i);
                int trackIndexNew = videoTrack.getIndex() - 1;
                for (int j = 0; j < videoTrack.getFilterAndAdjustCount(); j++) {
                    videoTrack.getFilterAndAdjustClip(j).setTrackIndex(trackIndexNew);
                }
                videoTrack.setIndex(trackIndexNew);
            }
            return filterAndAdjustTimelineTracks.remove(trackIndex);
        }
        return null;
    }


    /**
     * remove timeline fx track
     * <p>
     * 移除timeline特效轨道
     *
     * @param trackIndex the index
     * @return MeicamTimelineVideoFxTrack
     */
    public MeicamTimelineVideoFxTrack removeTimelineFxTrack(int trackIndex) {
        if (CommonUtils.isIndexAvailable(trackIndex, timelineVideoFxTracks)) {
            for (int i = trackIndex + 1; i < timelineVideoFxTracks.size(); i++) {
                MeicamTimelineVideoFxTrack videoTrack = timelineVideoFxTracks.get(i);
                int trackIndexNew = videoTrack.getIndex() - 1;
                for (int j = 0; j < videoTrack.getClipCount(); j++) {
                    videoTrack.getClip(j).setTrackIndex(trackIndexNew);
                }
                videoTrack.setIndex(trackIndexNew);
            }

            return timelineVideoFxTracks.remove(trackIndex);
        }
        return null;
    }


    /**
     * Get timeline fx track
     * <p>
     * 获取timeline特效轨道
     *
     * @param index the index
     * @return MeicamTimelineVideoFxTrack
     */
    public MeicamTimelineVideoFxTrack getTimelineFxTrack(int index) {
        if (CommonUtils.isIndexAvailable(index, timelineVideoFxTracks)) {
            return timelineVideoFxTracks.get(index);
        }
        return null;
    }


    /**
     * Get timeline filter and adjust track
     * <p>
     * 获取timeline 滤镜 调节轨道
     *
     * @param index the index
     * @return MeicamTimelineVideoFxTrack
     */
    public MeicamTimelineVideoFxTrack getFilterAndAdjustTimelineTrack(int index) {
        if (CommonUtils.isIndexAvailable(index, filterAndAdjustTimelineTracks)) {
            return filterAndAdjustTimelineTracks.get(index);
        }
        return null;
    }


    /**
     * Get timeline fx track count
     * <p>
     * 获取timeline特效轨道数量
     *
     * @return timeline fx track count
     */
    public int getTimelineFxTrackCount() {
        return timelineVideoFxTracks == null ? 0 : timelineVideoFxTracks.size();
    }

    /**
     * Get timeline filter and adjust track count
     * <p>
     * 获取timeline滤镜和调节轨道数量
     *
     * @return timeline fx track count
     */
    public int getFilterAndAdjustTimelineTracksCount() {
        return filterAndAdjustTimelineTracks == null ? 0 : filterAndAdjustTimelineTracks.size();
    }

    /**
     * Get timeline fx clip count
     * <p>
     * 获取timeline特效Clip数量
     *
     * @return timeline fx clip count
     */
    public int getTimelineFxClipCount() {
        return timelineVideoFxClips == null ? 0 : timelineVideoFxClips.size();
    }

    /**
     * 在最后添加视频轨道
     * Add the video track at the end
     *
     * @return MeicamVideoTrack the video track
     */
    public MeicamVideoTrack appendVideoTrack() {
        return appendVideoTrack(videoTrackList.size());
    }

    /**
     * 添加指定索引的视频轨道,如果该轨道存在则失败
     * Constructs the video track with the specified index
     *
     * @param trackIndex the track index 轨道索引
     * @return MeicamVideoTrack the video track
     */
    public MeicamVideoTrack appendVideoTrack(int trackIndex) {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("nvsTimeline is null");
            return null;
        }
        if (trackIndex < 0 || trackIndex > videoTrackList.size()) {
            return null;
        }
        if (canAddVideoTrack(trackIndex)) {
            return insertVideoTrack(nvsTimeline, trackIndex);
        }
        return null;
    }

    /**
     * 插入指定索引的视频轨道
     * Insert the video track with the specified index
     *
     * @param trackIndex the track index 轨道索引
     * @return MeicamVideoTrack the video track
     */
    private MeicamVideoTrack insertVideoTrack(NvsTimeline timeline, int trackIndex) {
        NvsVideoTrack nvsVideoTrack = timeline.insertVideoTrack(trackIndex);
        if (nvsVideoTrack != null) {
            MeicamVideoTrack videoTrack = new MeicamVideoTrack(nvsVideoTrack, nvsVideoTrack.getIndex());
            videoTrackList.add(trackIndex, videoTrack);
            return videoTrack;
        }
        return null;
    }

    /**
     * 插入指定索引的视频轨道
     * Insert the video track with the specified index
     *
     * @param trackIndex the track index 轨道索引
     * @return MeicamVideoTrack the video track
     */
    public MeicamVideoTrack insertVideoTrack(int trackIndex) {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("nvsTimeline is null");
            return null;
        }
        if (trackIndex < 0 || trackIndex > videoTrackList.size()) {
            return null;
        }
        MeicamVideoTrack meicamVideoTrack = insertVideoTrack(nvsTimeline, trackIndex);
        if (meicamVideoTrack != null) {
            for (int i = trackIndex; i < videoTrackList.size(); i++) {
                MeicamVideoTrack videoTrack = videoTrackList.get(i);
                videoTrack.setIndex(videoTrack.getObject().getIndex());
            }
        } else {
            LogUtils.e("insert video track failed!");
        }
        return meicamVideoTrack;
    }

    /**
     * 恢复videoClip上层数据
     * Append meicam video track meicam video track.
     *
     * @param nvsVideoTrack the nvs video track
     * @return the meicam video track
     */
    private MeicamVideoTrack appendVideoTrack(NvsVideoTrack nvsVideoTrack) {
        int trackIndex = nvsVideoTrack.getIndex();
        MeicamVideoTrack videoTrack = new MeicamVideoTrack(nvsVideoTrack, trackIndex);
        videoTrackList.add(trackIndex, videoTrack);
        return videoTrack;
    }

    /**
     * 是否可以添加视频轨道
     * Can you add a video track
     *
     * @param trackIndex the track index g轨道索引
     * @return boolean true can 可以，false not 不可以
     */
    private boolean canAddVideoTrack(int trackIndex) {
        for (MeicamVideoTrack item : videoTrackList) {
            if (item.getIndex() == trackIndex) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取视频轨道
     * Get meicam video track
     *
     * @param trackIndex the track index 轨道索引
     * @return MeicamVideoTrack 视频轨道
     */
    public MeicamVideoTrack getVideoTrack(int trackIndex) {
        if (!CommonUtils.isIndexAvailable(trackIndex, videoTrackList)) {
            return null;
        }
        return videoTrackList.get(trackIndex);
    }

    /**
     * 获取视频轨道数量
     * Get meicam video track count
     *
     * @return video track count 视频轨道数量
     */
    public int videoTrackCount() {
        return videoTrackList == null ? 0 : videoTrackList.size();
    }

    /**
     * 移除视频轨道
     * Remove meicam video track
     *
     * @param trackIndex the track index 轨道索引
     * @return MeicamVideoTrack 视频轨道
     */
    public MeicamVideoTrack removeVideoTrack(int trackIndex) {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("nvsTimeline is null");
            return null;
        }
        if (trackIndex < 0 || trackIndex > videoTrackList.size()) {
            return null;
        }
        if (nvsTimeline.removeVideoTrack(trackIndex)) {
            for (int i = trackIndex + 1; i < videoTrackList.size(); i++) {
                MeicamVideoTrack videoTrack = videoTrackList.get(i);
                videoTrack.setIndex(videoTrack.getIndex() - 1);
            }
            return videoTrackList.remove(trackIndex);
        }
        return null;
    }

    /**
     * 移除所有视频轨道
     * Clear meicam video track
     */
    public void clearVideoTrack() {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("nvsTimeline is null");
            return;
        }
        int count = nvsTimeline.videoTrackCount();
        for (int i = count - 1; i >= 0; i--) {
            nvsTimeline.removeVideoTrack(i);
        }
        videoTrackList.clear();
    }

    /**
     * 移除所有音频轨道
     * Clear meicam audio track
     */
    public void clearAudioTrack() {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("nvsTimeline is null");
            return;
        }
        int count = nvsTimeline.audioTrackCount();
        for (int i = count - 1; i >= 0; i--) {
            nvsTimeline.removeAudioTrack(i);
        }
        audioTrackList.clear();
    }


    /**
     * 在最后添加音频轨道
     * Add the audio track at the end
     *
     * @return MeicamVideoTrack the audio track
     */
    public MeicamAudioTrack appendAudioTrack() {
        return appendAudioTrack(audioTrackList.size());
    }

    /**
     * 添加指定索引的音频轨道
     * Constructs the audio track with the specified index
     *
     * @param trackIndex the track index 轨道索引
     * @return MeicamAudioTrack the audio track
     */
    public MeicamAudioTrack appendAudioTrack(int trackIndex) {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("nvsTimeline is null");
            return null;
        }

        if (canAddAudioTrack(trackIndex)) {
            NvsAudioTrack nvsAudioTrack = nvsTimeline.insertAudioTrack(trackIndex);
            if (nvsAudioTrack != null) {
                MeicamAudioTrack audioTrack = new MeicamAudioTrack(nvsAudioTrack, nvsAudioTrack.getIndex());
                audioTrackList.add(trackIndex, audioTrack);
                return audioTrack;
            }
        }
        return null;
    }

    /**
     * 恢复上层数据
     * Append meicam audio track meicam audio track.
     *
     * @param nvsAudioTrack the nvs audio track
     * @return the meicam audio track
     */
    public MeicamAudioTrack appendMeicamAudioTrack(NvsAudioTrack nvsAudioTrack) {
        int trackIndex = nvsAudioTrack.getIndex();
        MeicamAudioTrack audioTrack = new MeicamAudioTrack(nvsAudioTrack, trackIndex);
        audioTrackList.add(trackIndex, audioTrack);
        return audioTrack;
    }

    /**
     * 是否可以添加音频轨道
     * Can you add a audio track
     *
     * @param trackIndex the track index 轨道索引
     * @return boolean true can 可以，false not 不可以
     */
    private boolean canAddAudioTrack(int trackIndex) {
        for (MeicamAudioTrack item : audioTrackList) {
            if (item.getIndex() == trackIndex) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取音频轨道
     * Get meicam audio track
     *
     * @param trackIndex the track index 轨道索引
     * @return MeicamAudioTrack 音频轨道
     */
    public MeicamAudioTrack getAudioTrack(int trackIndex) {
        int trackSize = audioTrackList.size();
        if (trackIndex >= trackSize) {
            //尾部补充空轨道
            //Tail supplement empty track
            NvsTimeline object = getObject();
            if (object != null) {
                for (int index = trackSize; index <= trackIndex; index++) {
                    NvsAudioTrack audioTrack = object.appendAudioTrack();
                    if (audioTrack != null) {
                        audioTrackList.add(new MeicamAudioTrack(audioTrack, index));
                    }
                }
            }
        }
        if (!CommonUtils.isIndexAvailable(trackIndex, audioTrackList)) {
            return null;
        }
        return audioTrackList.get(trackIndex);
    }

    /**
     * 获取音频轨道数量
     * Get meicam audio track count
     *
     * @return audio track count 音频轨道数量
     */
    public int getAudioTrackCount() {
        return audioTrackList.size();
    }

    /**
     * 移除音频轨道
     * Remove meicam audio track
     *
     * @param trackIndex the track index 轨道索引
     * @return MeicamAudioTrack 音频轨道
     */
    public MeicamAudioTrack removeAudioTrack(int trackIndex) {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("nvsTimeline is null");
            return null;
        }
        if (trackIndex < 0 || trackIndex > audioTrackList.size()) {
            return null;
        }
        if (nvsTimeline.removeAudioTrack(trackIndex)) {
            for (int i = trackIndex + 1; i < audioTrackList.size(); i++) {
                MeicamAudioTrack audioTrack = audioTrackList.get(i);
                audioTrack.setIndex(audioTrack.getIndex() - 1);
            }
            return audioTrackList.remove(trackIndex);
        }
        return null;
    }

    /**
     * Get current position
     * 获取当前的时间点
     *
     * @return current position 当前的时间点
     */
    public long getCurrentPosition() {
        NvsStreamingContext instance = NvsStreamingContext.getInstance();
        if (instance == null) {
            LogUtils.e("NvsStreamingContext is null!");
            return 0;
        }
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("nvsTimeline is null");
            return 0;
        }
        return instance.getTimelineCurrentPosition(nvsTimeline);
    }


    /**
     * Compile timeline
     * <p>
     * <p>
     * 导出视频
     *
     * @param streamingContext  the streamingContext 流媒体上下文
     * @param startTime         start time
     * @param endTime           end time
     * @param outputFilePath    path for save video 视频保存路径
     * @param resolutionGrade   resolution grade 分辨率等级
     * @param videoBitrateGrade video bitrate grade 码率级别
     * @param customHeight      custom height，
     *                          It will go into effect only when resolutionGrade is
     *                          {NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_CUSTOM}
     *                          自定义高度，只有在resolutionGrade
     *                          是NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_CUSTOM的时候才生效
     * @param flags             compile flags
     * @param compileConfig     config of compile 导出配置参数
     * @return Is compiling start success or not. true:yes;false no.
     */
    public boolean compileTimeline(NvsStreamingContext streamingContext, long startTime,
                                   long endTime,
                                   String outputFilePath,
                                   int resolutionGrade,
                                   int customHeight,
                                   int videoBitrateGrade,
                                   int flags, Hashtable<String, Object> compileConfig) {
        if (streamingContext == null) {
            LogUtils.e("streamingContext is null");
            return false;
        }
        if (getObject() == null) {
            LogUtils.e("timeline is null");
            return false;
        }

        /*
         *人脸检测设置为Image模式
         * Face detection is set to image mode
         */
        changeARSenseMode(NvsStreamingContext.HUMAN_DETECTION_FEATURE_SEMI_IMAGE_MODE);
        streamingContext.setCompileConfigurations(compileConfig);
        if (resolutionGrade == NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_CUSTOM) {
            NvsVideoResolution videoRes = getVideoResolution();
            int imageWidth = videoRes.imageWidth;
            int imageHeight = videoRes.imageHeight;
            LogUtils.d("timeline Width=" + imageWidth + ", height = " + imageHeight + ", customHeight = " + customHeight);
            streamingContext.setCustomCompileVideoHeight(customHeight);
        }

        return (streamingContext.compileTimeline(getObject(), startTime, endTime,
                outputFilePath, resolutionGrade,
                videoBitrateGrade, flags));

    }

    /**
     * 播放
     * Play
     *
     * @param streamingContext the streamingContext 流媒体上下文
     * @param startTime        the start position
     * @param endTime          the end position
     */
    public boolean playBack(NvsStreamingContext streamingContext, long startTime, long endTime, int flag) {
        if (getObject() == null || streamingContext == null) {
            return false;
        }
        /*
         *人脸检测设置为video模式
         * Face detection is set to video mode
         */
        if (EditorEngine.getInstance().isUseFaceShape() && changeARSenseMode(NvsStreamingContext.HUMAN_DETECTION_FEATURE_SEMI_IMAGE_MODE)) {
            flag |= NvsStreamingContext.STREAMING_ENGINE_PLAYBACK_FLAG_BUDDY_HOST_VIDEO_FRAME;
        }
        if (Constants.NEED_100_SPEED) {
            flag |= NvsStreamingContext.STREAMING_ENGINE_PLAYBACK_FLAG_SPEED_COMP_MODE;
        }
        flag |= NvsStreamingContext.STREAMING_ENGINE_PLAYBACK_FLAG_LOW_PIPELINE_SIZE;
        if (EditorEngine.getInstance().isUseSegmentation()) {
            flag |= NvsStreamingContext.STREAMING_ENGINE_PLAYBACK_FLAG_BUDDY_ORIGIN_VIDEO_FRAME;
        }
        return streamingContext.playbackTimeline(getObject(), startTime, endTime, NvsStreamingContext.VIDEO_PREVIEW_SIZEMODE_LIVEWINDOW_SIZE, false, flag | NvsStreamingContext.STREAMING_ENGINE_PLAYBACK_FLAG_DISABLE_DROP_FRAME_MODE);
    }

    /**
     * 播放
     * Play
     *
     * @param streamingContext the streamingContext 流媒体上下文
     * @param startTime        the start position
     * @param endTime          the end position
     */
    public boolean playBack(NvsStreamingContext streamingContext, long startTime, long endTime) {
        return playBack(streamingContext, startTime, endTime, 0);
    }

    /**
     * 设置人脸检测模式
     * <p>
     * Set mode of detection.
     *
     * @param mode the Mode {@code NvsStreamingContext.HUMAN_DETECTION_FEATURE_VIDEO_MODE
     *             and NvsStreamingContext.HUMAN_DETECTION_FEATURE_IMAGE_MODE}
     * @return Is success or not. true：yes； false:no
     */
    public boolean changeARSenseMode(int mode) {
        boolean isSuccess = false;
        int count = videoTrackCount();
        if (count > 0) {
            for (int index = 0; index < count; index++) {
                MeicamVideoTrack videoTrack = getVideoTrack(index);
                int clipCount = videoTrack.getClipCount();
                if (clipCount > 0) {
                    for (int i = 0; i < clipCount; i++) {
                        if (videoTrack.getVideoClip(i).setDetectionMode(mode)) {
                            isSuccess = true;
                        }
                    }
                }
            }
        }
        return isSuccess;
    }


    /**
     * Export template info boolean.
     * 导出模板信息
     *
     * @param streamingContext the streamingContext 流媒体上下文
     * @param uuid             the uuid
     * @param ratio            the ratio
     * @return the boolean
     */
    public boolean exportTemplateInfo(NvsStreamingContext streamingContext, String uuid, int ratio) {
        if (streamingContext == null) {
            LogUtils.e("streamingContext is null");
            return false;
        }
        if (getObject() == null) {
            LogUtils.e("timeline is null");
            return false;
        }
        return streamingContext.exportTemplateInfo(uuid, getObject(), ratio);
    }

    /**
     * Export template info boolean.
     * 导出模板信息
     *
     * @param streamingContext the streamingContext 流媒体上下文
     * @param uuid             the uuid
     * @param ratio            the ratio
     * @return the boolean
     */
    public boolean exportProjectInfo(NvsStreamingContext streamingContext, String uuid, int ratio) {
        if (streamingContext == null) {
            LogUtils.e("streamingContext is null");
            return false;
        }
        if (getObject() == null) {
            LogUtils.e("timeline is null");
            return false;
        }
        return streamingContext.exportProjectInfo(uuid, getObject(), ratio);
    }

    /**
     * 连接时间线和实时预览图像窗口
     * Connect timeline with live window
     *
     * @param streamingContext the streamingContext 流媒体上下文
     * @param liveWindow       the live window
     * @return Success or not. true:yes; false :no.
     */
    public boolean connectToLiveWindow(NvsStreamingContext streamingContext, NvsLiveWindowExt liveWindow) {
        if (streamingContext != null && getObject() != null && liveWindow != null) {
            return streamingContext.connectTimelineWithLiveWindowExt(getObject(), liveWindow);
        }
        return false;
    }

    /**
     * 连接时间线和实时预览图像窗口
     * Connect timeline with live window
     *
     * @param streamingContext the streamingContext 流媒体上下文
     * @param liveWindow       the live window
     * @return Success or not. true:yes; false :no.
     */
    public boolean connectToLiveWindow(NvsStreamingContext streamingContext, NvsLiveWindow liveWindow) {
        if (streamingContext != null && getObject() != null && liveWindow != null) {
            return streamingContext.connectTimelineWithLiveWindow(getObject(), liveWindow);
        }
        return false;
    }

    /**
     * 预览时间线的某个时间点
     * Seek to the position of timeline
     *
     * @param streamingContext the streamingContext 流媒体上下文
     * @param timestamp        the timestamp
     * @param seekShowMode     the seek show mode
     */
    public boolean seekTimeline(NvsStreamingContext streamingContext, long timestamp, int seekShowMode) {
        NvsTimeline object = getObject();
        if (object == null || streamingContext == null) {
            return false;
        }

        if (streamingContext.getStreamingEngineState() == NvsStreamingContext.STREAMING_ENGINE_STATE_PLAYBACK) {
            streamingContext.stop(NvsStreamingContext.STREAMING_ENGINE_RECORDING_FLAG_SOFTWARE_VIDEO_INTRA_FRAME_ONLY_FAST_STOP);
        }

        object.setAttachment(NvsConstants.KEY_ATTACHMENT_RESET_PLAY_POINT, null);
        object.setAttachment(NvsConstants.KEY_ATTACHMENT_LOOP_PLAY_POINT_START, null);
        object.setAttachment(NvsConstants.KEY_ATTACHMENT_LOOP_PLAY_POINT_END, null);
        //int flag = NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER;

        int flag = 0;
        /*
         *人脸检测设置为Image模式
         * Face detection is set to image mode
         */
        if (EditorEngine.getInstance().isUseFaceShape() && changeARSenseMode(NvsStreamingContext.HUMAN_DETECTION_FEATURE_SEMI_IMAGE_MODE)) {
            flag |= NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_BUDDY_HOST_VIDEO_FRAME;
        }
        flag = seekShowMode > 0 ? flag | seekShowMode : flag;
        return streamingContext.seekTimeline(object, timestamp, NvsStreamingContext.VIDEO_PREVIEW_SIZEMODE_LIVEWINDOW_SIZE, flag);

    }


    /**
     * Grab image from timeline bitmap.
     * 从时间线中获取图片
     *
     * @param streamingContext the streamingContext 流媒体上下文
     * @param position         the position 当前位置
     * @param nvsRational      the nvs rational
     * @return the bitmap
     */
    public Bitmap grabImageFromTimeline(NvsStreamingContext streamingContext, long position, NvsRational nvsRational) {

        return grabImageFromTimeline(streamingContext, position, nvsRational, 0);
    }

    /**
     * Grab image from timeline bitmap.
     * 从时间线中获取图片
     *
     * @param streamingContext the streamingContext 流媒体上下文
     * @param position         the position 当前位置
     * @param nvsRational      the nvs rational
     * @return the bitmap
     */
    public Bitmap grabImageFromTimeline(NvsStreamingContext streamingContext, long position, NvsRational nvsRational, int flag) {
        if (streamingContext == null) {
            LogUtils.e("streamingContext is null");
            return null;
        }
        if (getObject() == null) {
            LogUtils.e("NvsTimeline is null");
            return null;
        }
        if (EditorEngine.getInstance().isUseFaceShape()) {
            flag |= NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_BUDDY_HOST_VIDEO_FRAME;
        }
        return streamingContext.grabImageFromTimeline(getObject(), position, nvsRational, flag);
    }

    /**
     * Grab image from timeline bitmap async.
     * 从时间线中获取图片,异步方法
     *
     * @param streamingContext the streamingContext 流媒体上下文
     * @param position         the position 当前位置
     * @param nvsRational      the nvs rational
     * @return the bitmap
     */
    public boolean grabImageFromTimelineAsync(NvsStreamingContext streamingContext, long position, NvsRational nvsRational, int flag) {
        if (streamingContext == null) {
            LogUtils.e("streamingContext is null");
            return false;
        }
        if (getObject() == null) {
            LogUtils.e("NvsTimeline is null");
            return false;
        }
        //必须重新设置一下callback，同步方法执行的时候，callback会脱绑
        //The callback should be reset. When the synchronization method is executed, the callback will be unbound.
        EngineCallbackManager.get().setImageGrabberCallback();
        return streamingContext.grabImageFromTimelineAsync(getObject(), position, nvsRational, flag);
    }

    /**
     * Remove timeline.
     * 移除timeline
     *
     * @param streamingContext the streaming context
     */
    public boolean removeTimeline(NvsStreamingContext streamingContext) {

        return streamingContext.removeTimeline(getObject());
    }


    /**
     * Gets first caption.
     * Get the first subtitle.Subtitle bottom sorting, the smaller Zvalue is, the more advanced, InPoint is smaller, the more advanced
     * 获取第一个字幕。字幕底层排序，Zvalue越小越靠前，inPoint越小越靠前
     *
     * @return the first caption
     */
    public MeicamCaptionClip getFirstCaption() {
        int stickerCaptionTrackCount = getStickerCaptionTrackCount();
        for (int index = 0; index < stickerCaptionTrackCount; index++) {
            MeicamStickerCaptionTrack stickerCaptionTrack = findStickCaptionTrack(index);
            if (stickerCaptionTrack == null) {
                continue;
            }
            int clipCount = stickerCaptionTrack.getClipCount();
            for (int i = 0; i < clipCount; i++) {
                ClipInfo<?> clip = stickerCaptionTrack.getCaptionStickerClip(index);
                if (clip instanceof MeicamCaptionClip) {
                    return (MeicamCaptionClip) clip;
                }
            }
        }
        return null;
    }

    /**
     * release
     * 释放timeline资源
     */
    public void release() {
        NvsTimeline object = getObject();
        if (object == null) {
            LogUtils.e("timeline is null");
            return;
        }
        NvsStreamingContext.getInstance().removeTimeline(object);
    }

    public boolean isCloud() {
        return isCloud;
    }

    public void setCloud(boolean cloud) {
        isCloud = cloud;
    }


    /**
     * 删除封面字幕
     * Remove cover caption.
     *
     * @param needClearTrack the need clear track 是否需要删除轨道
     */
    public void removeCoverCaption(boolean needClearTrack) {
        int trackCount = getStickerCaptionTrackCount();
        for (int index = trackCount - 1; index >= 0; index--) {
            MeicamStickerCaptionTrack stickCaptionTrack = findStickCaptionTrack(index);
            if (stickCaptionTrack == null) {
                return;
            }
            int clipCount = stickCaptionTrack.getClipCount();
            if (clipCount > 0) {
                for (int clipIndex = clipCount - 1; clipIndex >= 0; clipIndex--) {
                    ClipInfo<?> captionStickerClip = stickCaptionTrack.getCaptionStickerClip(clipIndex);
                    if (captionStickerClip != null && captionStickerClip.getAttachment(CommonData.ATTACHMENT_KEY_IS_COVER_CAPTION) != null) {
                        stickCaptionTrack.removeStickerCaptionClip(captionStickerClip);
                    }
                }
            }
            if (needClearTrack) {
                int count = stickCaptionTrack.getClipCount();
                if (count <= 0) {
                    removeStickCaptionTrack(stickCaptionTrack.getIndex());
                }
            }
        }
    }

    public void setCoverData(MeicamCoverData coverData) {
        this.mCoverData = coverData;
    }

    public void loadCoverData(Map<String, ClipInfo<?>> captionClipMap){
        MeicamStickerCaptionTrack track = addStickCaptionTrack(getStickerCaptionTrackCount());
        //准备一个封面字幕轨道
        //Prepare a track for cover caption
        if (track == null) {
            return;
        }
        if (captionClipMap == null) {
            return;
        }
        long duration = getDuration();
        Set<Map.Entry<String, ClipInfo<?>>> entries = captionClipMap.entrySet();
        if (!entries.isEmpty()) {
            for (Map.Entry<String, ClipInfo<?>> entry : entries) {
                ClipInfo<?> captionClip = entry.getValue();
                //修正字幕出点
                //Fix the caption out point
                if (captionClip != null) {
                    captionClip.setOutPoint(duration);
                }
                if (captionClip instanceof MeicamCaptionClip) {
                    track.addModularCaption((MeicamCaptionClip) captionClip);
                } else if (captionClip instanceof MeicamCompoundCaptionClip) {
                    track.addCompoundCaption((MeicamCompoundCaptionClip) captionClip);
                }
            }
        }
    }

    public Map<String, ClipInfo<?>> getCoverCaption() {
        MeicamStickerCaptionTrack stickCaptionTrack = findStickCaptionTrack(getStickerCaptionTrackCount() - 1);
        if (stickCaptionTrack == null) {
            return null;
        }
        int clipCount = stickCaptionTrack.getClipCount();
        Map<String, ClipInfo<?>> captionClipMap = new LinkedHashMap<>();
        for (int index = 0; index < clipCount; index++) {
            ClipInfo<?> clipInfo = stickCaptionTrack.getCaptionStickerClip(index);
            Object attachment = clipInfo.getAttachment(CommonData.ATTACHMENT_KEY_IS_COVER_CAPTION);
            if (!(attachment instanceof String)) {
                continue;
            }
            captionClipMap.put((String) attachment, (ClipInfo<?>) clipInfo.clone());
        }
        return captionClipMap;
    }


    public MeicamCoverData getCoverData() {
        if (mCoverData == null) {
            mCoverData = new MeicamCoverData();
        }
        return mCoverData;
    }

    /**
     * 时间线构建者 The timeline builder
     */
    public static class TimelineBuilder {
        /**
         * 普通构建
         * Normal build
         **/
        public static final int BUILD_NORMAL = 0;
        /**
         * 草稿构建
         * draft build
         **/
        public static final int BUILD_FORM_DRAFT = 1;
        /**
         * 从时间线构建
         * build from old timeline
         **/
        public static final int BUILD_FORM_TIMELINE = 2;
        /**
         * 从模板构建
         * build from template
         **/
        public static final int BUILD_FORM_TEMPLATE = 4;
        /**
         * 用NvsTimeline构建
         * build from NvsTimeline
         */
        public static final int BUILD_FORM_NVSTIMELINE = 5;

        private final int buildType;
        private final NvsStreamingContext streamingContext;
        private NvsVideoResolution videoResolution;
        private NvsRational nvsRational;
        private LTimelineData lTimelineData;
        private MeicamTimeline timelineData;
        private MeicamTimeline editTimeline;
        private NvsTimeline nvsTimeline;
        private String templateId;
        private List<NvsStreamingContext.templateFootageInfo> templateFootAgeInfoList;

        public TimelineBuilder(NvsStreamingContext streamingContext, @BuildType int buildType) {
            this.streamingContext = streamingContext;
            this.buildType = buildType;
        }

        /**
         * 设置视频分辨率
         * Set video resolution
         *
         * @param videoResolution the video resolution 视频分辨率
         */
        public TimelineBuilder setVideoResolution(NvsVideoResolution videoResolution) {
            this.videoResolution = videoResolution;
            return this;
        }

        /**
         * 设置视频比例
         * Set video national
         *
         * @param nvsRational the video national 视频比例
         */
        public TimelineBuilder setNvsRational(NvsRational nvsRational) {
            this.nvsRational = nvsRational;
            return this;
        }

        /**
         * 设置草稿数据
         * Set draft data
         *
         * @param lTimeline the draft data草稿数据
         */
        public TimelineBuilder setDraftData(LTimelineData lTimeline) {
            this.lTimelineData = lTimeline;
            return this;
        }

        /**
         * 数据恢复
         * Sets nvs timeline.
         *
         * @param nvsTimeline the nvs timeline
         * @return the nvs timeline
         */
        public TimelineBuilder setNvsTimeline(NvsTimeline nvsTimeline) {
            this.nvsTimeline = nvsTimeline;
            return this;
        }

        /**
         * 设置编辑用的旧的时间线
         * Set edit timeline data
         *
         * @param timelineData edit timeline 旧的时间线
         */
        public TimelineBuilder setTimelineData(MeicamTimeline timelineData) {
            this.timelineData = timelineData;
            return this;
        }

        /**
         * 设置编辑的时间线
         * Set edit timeline data
         *
         * @param meicamTimeline edit timeline 要编辑的时间线
         */
        public TimelineBuilder setEditTimeline(MeicamTimeline meicamTimeline) {
            this.editTimeline = meicamTimeline;
            return this;
        }

        /**
         * 设置模板id
         * Set template id
         *
         * @param templateId the template id 模板标识
         */
        public TimelineBuilder setTemplateId(String templateId) {
            this.templateId = templateId;
            return this;
        }

        /**
         * 设置模板信息
         * Set template foot age info list
         *
         * @param templateFootAgeInfoList the info list
         */
        public TimelineBuilder setTemplateFootageInfo(List<NvsStreamingContext.templateFootageInfo> templateFootAgeInfoList) {
            this.templateFootAgeInfoList = templateFootAgeInfoList;
            return this;
        }

        public MeicamTimeline build() {
            MeicamTimeline timeline;
            switch (buildType) {
                case BUILD_NORMAL:
                    timeline = buildNormal();
                    break;
                case BUILD_FORM_DRAFT:
                    timeline = buildFromDraft();
                    break;
                case BUILD_FORM_TIMELINE:
                    timeline = buildFromTimeline();
                    break;
                case BUILD_FORM_TEMPLATE:
                    timeline = buildFromTemplate();
                    break;
                case BUILD_FORM_NVSTIMELINE:
                    timeline = buildFromNvsTimeline();
                    break;
                default:
                    timeline = null;
                    break;
            }
            return timeline;
        }


        private MeicamTimeline buildNormal() {
            if (nvsRational == null) {
                return createTimeline(videoResolution, streamingContext);
            }
            return createTimeline(videoResolution, nvsRational, streamingContext);
        }

        private MeicamTimeline buildFromDraft() {
            return recoverTimeline(lTimelineData, streamingContext);
        }

        private MeicamTimeline buildFromTimeline() {
            return recoverTimeline(this.timelineData);
        }

        private MeicamTimeline buildFromTemplate() {
            MeicamTimeline timeline = createTimeline(streamingContext, templateId, templateFootAgeInfoList);
//            loadTimelineFromTemplate(timeline);
            if (timeline != null) {
                return recoverMeicamTimeline(timeline.getObject());
            }
            return null;

        }

        private MeicamTimeline buildFromNvsTimeline() {
            return recoverMeicamTimeline(nvsTimeline);
        }


        /**
         * 从模板创建时间线，加载其数据到上层
         * Create a timeline from the template and load its data to the meicam data
         */
        private void loadTimelineFromTemplate(MeicamTimeline timeline) {
            if (timeline == null) {
                return;
            }
            NvsTimeline nvsTimeline = timeline.getObject();
            int trackCount = nvsTimeline.videoTrackCount();
            for (int i = 0; i < trackCount; i++) {
                NvsVideoTrack videoTrack = nvsTimeline.getVideoTrackByIndex(i);
                if (videoTrack == null) {
                    continue;
                }
                MeicamVideoTrack meicamVideoTrack = new MeicamVideoTrack(videoTrack, videoTrack.getIndex());
                meicamVideoTrack.setObject(videoTrack);
                timeline.getVideoTrackList().add(meicamVideoTrack);
                for (int j = 0; j < videoTrack.getClipCount(); j++) {
                    NvsVideoClip nvsVideoClip = videoTrack.getClipByIndex(j);
                    String filePath = nvsVideoClip.getFilePath();
                    String videoType;
                    if (CommonData.EMPTY_THUMBNAIL_IMAGE.equals(filePath) || CommonData.IMAGE_BLACK_HOLDER.equals(filePath)) {
                        videoType = CommonData.CLIP_HOLDER;
                    } else {
                        videoType = nvsVideoClip.getVideoType() == NvsVideoClip.VIDEO_CLIP_TYPE_IMAGE ? CommonData.CLIP_IMAGE : CommonData.CLIP_VIDEO;
                    }
                    MeicamVideoClip videoClip = new MeicamVideoClip(nvsVideoClip, videoType);
                    videoClip.outPoint = nvsVideoClip.getOutPoint();
                    videoClip.setObject(nvsVideoClip);
                    videoClip.setIndex(nvsVideoClip.getIndex());
                    videoClip.loadData();
                    meicamVideoTrack.getVideoClipList().add(videoClip);
                }
            }
            NvsTimelineCaption timelineCaption = nvsTimeline.getFirstCaption();
            if (timelineCaption != null) {
                MeicamStickerCaptionTrack meicamStickerCaptionTrack = timeline.addStickCaptionTrack(0, false);
                if (meicamStickerCaptionTrack != null) {
                    List<ClipInfo<?>> clipInfoList = meicamStickerCaptionTrack.getClipInfoList();
                    while (timelineCaption != null) {
                        clipInfoList.add(createCaptionClip(timelineCaption));
                        timelineCaption = nvsTimeline.getNextCaption(timelineCaption);
                    }
                }
            }
            NvsTimelineCompoundCaption compoundCaption = nvsTimeline.getFirstCompoundCaption();
            if (compoundCaption != null) {
                int index = timeline.getStickerCaptionTrackCount() - 1;
                if (index < 0) {
                    index = 0;
                }
                MeicamStickerCaptionTrack meicamStickerCaptionTrack = timeline.addStickCaptionTrack(index, false);
                if (meicamStickerCaptionTrack != null) {
                    List<ClipInfo<?>> clipInfoList = meicamStickerCaptionTrack.getClipInfoList();
                    while (compoundCaption != null) {
                        clipInfoList.add(createCompoundCaptionClip(compoundCaption));
                        compoundCaption = nvsTimeline.getNextCaption(compoundCaption);
                    }
                }
            }

        }

        /**
         * 创建字幕片段
         * <p>
         * Create caption clip
         *
         * @param timelineCaption the timeline caption
         */
        private MeicamCaptionClip createCaptionClip(NvsTimelineCaption timelineCaption) {
            MeicamCaptionClip meicamCaptionClip = new MeicamCaptionClip(timelineCaption, timelineCaption.getText(),
                    timelineCaption.getInPoint(), timelineCaption.getOutPoint());
            meicamCaptionClip.setZValue(timelineCaption.getZValue());
            meicamCaptionClip.setTrackIndex((int) timelineCaption.getZValue());
            return meicamCaptionClip;
        }

        /**
         * 创建字幕片段
         * <p>
         * Create caption clip
         *
         * @param timelineCaption the timeline caption
         */
        private MeicamCompoundCaptionClip createCompoundCaptionClip(NvsTimelineCompoundCaption timelineCaption) {
            MeicamCompoundCaptionClip meicamCaptionClip = new MeicamCompoundCaptionClip(timelineCaption);
            meicamCaptionClip.setZValue(timelineCaption.getZValue());
            meicamCaptionClip.setTrackIndex((int) timelineCaption.getZValue());
            meicamCaptionClip.loadData();
            return meicamCaptionClip;
        }

        /**
         * 创建时间线
         * Create timeline
         *
         * @param videoResolution  the video resolution 视频分辨率
         * @param streamingContext the streamingContext 流媒体上下文
         * @return MeicamTimeline 时间线
         */
        private MeicamTimeline createTimeline(NvsVideoResolution videoResolution, NvsStreamingContext streamingContext) {
            //帧率  每秒帧数越多所显示的动作就会越流畅
            //The more frames per second, the smoother the displayed action will be
            NvsRational rational = new NvsRational(CommonData.DEFAULT_TIMELINE_FPS, 1);
            if (NvsConstants.sHdrBitDepth > 0) {
                videoResolution.bitDepth = NvsConstants.sHdrBitDepth;
            }
            return createTimeline(videoResolution, rational, streamingContext);
        }


        /**
         * Create timeline meicam timeline.
         * <p>
         * 模板创建时间线
         *
         * @param context          the context
         * @param templateId       the template id 模板ID
         * @param templateFootAges the template foot ages 模板片段集合
         * @return the meicam timeline
         */
        private MeicamTimeline createTimeline(NvsStreamingContext context, String templateId, List<NvsStreamingContext.templateFootageInfo> templateFootAges) {
            if (context == null) {
                return null;
            }
            NvsTimeline nvsTimeline = context.createTimeline(templateId, templateFootAges);
            if (nvsTimeline == null) {
                return null;
            }
            MeicamTimeline meicamTimeline = new MeicamTimeline();
            meicamTimeline.setObject(nvsTimeline);
            return meicamTimeline;
        }

        /**
         * 创建时间线
         * Create timeline
         *
         * @param videoResolution  the video resolution 视频分辨率
         * @param rational         the rational 视频帧率
         * @param streamingContext the streamingContext 流媒体上下文
         * @return MeicamTimeline 时间线
         */
        private MeicamTimeline createTimeline(NvsVideoResolution videoResolution, NvsRational rational, NvsStreamingContext streamingContext) {
            if (videoResolution != null && streamingContext != null) {
                NvsAudioResolution nvsAudioResolution = new NvsAudioResolution();
                //采样率
                //sample rate
                nvsAudioResolution.sampleRate = 44100;
                //声道数
                //channel count
                nvsAudioResolution.channelCount = 2;
                checkVideoResolution(videoResolution);
                NvsTimeline timeline = streamingContext.createTimeline(videoResolution, rational, nvsAudioResolution);
                if (timeline != null) {
                    timeline.enableRenderOrderByZValue(true);
                    return createMeicamTimeline(timeline);
                } else {
                    LogUtils.e("createTimeline timeline failed!!!");
                }
            } else {
                LogUtils.e("videoResolution or rational is null,createTimeline timeline failed!!!");
            }
            return null;
        }

        /**
         * 恢复时间线
         * Create timeline
         *
         * @return MeicamTimeline 时间线
         */
        private MeicamTimeline createMeicamTimeline(NvsTimeline nvsTimeline) {
            MeicamTimeline meicamTimeline = new MeicamTimeline();
            meicamTimeline.setNvsRational(nvsTimeline.getVideoFps());
            meicamTimeline.setVideoResolution(nvsTimeline.getVideoRes());
            meicamTimeline.setObject(nvsTimeline);
            return meicamTimeline;
        }

        /**
         * 检查视频比例，如果宽高不符合要求，修正
         * Check the video proportion.
         * If the width and height do not meet the requirements, correct them.
         *
         * @param videoResolution 视频比例，the video resolution
         */
        private void checkVideoResolution(NvsVideoResolution videoResolution) {
            if (videoResolution != null) {
                videoResolution.imageWidth = alignedData(videoResolution.imageWidth, 4);
                videoResolution.imageHeight = alignedData(videoResolution.imageHeight, 2);
                LogUtils.d("width = " + videoResolution.imageWidth + ", height = " + videoResolution.imageHeight);
            }
        }

        /**
         * 从草稿恢复时间线
         * Recover timeline from draft data
         *
         * @param streamingContext the streamingContext 流媒体上下文
         * @param lTimeline        the draft data 草稿数据
         * @return MeicamTimeline 时间线
         */
        private MeicamTimeline recoverTimeline(LTimelineData lTimeline, NvsStreamingContext streamingContext) {
            if (lTimeline != null) {
                MeicamTimeline timeline = createTimeline(lTimeline.getVideoResolution(), lTimeline.getNvsRational(), streamingContext);
                if (timeline != null) {
                    timeline.recoverFromLocalData(lTimeline);
                }
                return timeline;
            }
            return null;
        }

        public MeicamTimeline recoverMeicamTimeline(NvsTimeline nvsTimeline) {
            if (nvsTimeline != null) {
                MeicamTimeline timeline = createMeicamTimeline(nvsTimeline);
                timeline.recoverFromTimelineData(nvsTimeline);
                return timeline;
            }
            return null;
        }

        /**
         * 整数对齐
         * Integer alignment
         *
         * @param data，源数据
         * @param num      对齐的数据
         * @return the value
         */
        private int alignedData(int data, int num) {
            return data - data % num;
        }

        /**
         * 恢复时间线
         * Recover timeline from draft data
         *
         * @param timelineData the old timeline data 旧timeline数据
         * @return MeicamTimeline 时间线
         */
        private MeicamTimeline recoverTimeline(MeicamTimeline timelineData) {
            if (timelineData == null || editTimeline == null) {
                return editTimeline;
            }
            editTimeline.clear();
            /*恢复视频比例
            * Restore the video resolution
            * */
            NvsVideoResolution videoResolution = timelineData.getVideoResolution();
            if (videoResolution != null) {
                editTimeline.changeVideoSize(videoResolution.imageWidth, videoResolution.imageHeight);
            }
            editTimeline.setMakeRatio(timelineData.mMakeRatio);
            List<MeicamVideoTrack> videoTrackList = timelineData.getVideoTrackList();
            if (videoTrackList != null) {
                /*恢复视频轨道以及相关实例
                * Restore the video track and related object
                * */
                for (MeicamVideoTrack videoTrackData : videoTrackList) {
                    MeicamVideoTrack videoTrack = editTimeline.appendVideoTrack(videoTrackData.getIndex());
                    if (videoTrack != null) {
                        videoTrack.setShow(videoTrackData.isShow());
                        videoTrack.setVolume(videoTrackData.getVolume());
                        videoTrack.setIsMute(videoTrackData.isMute(), true);
                        List<MeicamVideoClip> videoClipList = videoTrackData.getVideoClipList();
                        if (videoClipList != null) {
                            for (MeicamVideoClip videoClipData : videoClipList) {
                                videoTrack.addVideoClip(videoClipData, videoClipData.getInPoint(),
                                        videoClipData.getTrimIn(), videoClipData.getTrimOut());
                            }
                        }
                        List<MeicamTransition> transitionInfoList = videoTrackData.getTransitionInfoList();
                        if (transitionInfoList != null) {
                            for (MeicamTransition transitionData : transitionInfoList) {
                                videoTrack.buildTransition(transitionData, transitionData.getIndex());
                            }
                        }
                    }
                }
            }
            /*恢复主题
            * Restore theme
            * */
            MeicamTheme meicamTheme = timelineData.getMeicamTheme();
            if (meicamTheme != null && (!TextUtils.isEmpty(meicamTheme.getThemePackageId()))) {
                boolean isSuccess = editTimeline.applyTheme(meicamTheme.getThemePackageId());
                if (isSuccess) {
                    //主题添加成功，删除所有的音频轨道
                    //Theme added successfully, delete all audio tracks
                    List<MeicamAudioTrack> meicamAudioTracks = timelineData.getAudioTrackList();
                    if (meicamAudioTracks != null) {
                        meicamAudioTracks.clear();
                    }
                }
            }
            /*恢复水印
            * Restore the water mark
            * */
            MeicamWaterMark waterMark = timelineData.getMeicamWaterMark();
            if (waterMark != null) {
                editTimeline.addWatermark(waterMark.getWatermarkFilePath(), waterMark.getDisplayWidth(), waterMark.getDisplayHeight()
                        , waterMark.getMarginX(), waterMark.getMarginY());
            }
            List<MeicamAudioTrack> audioTrackList = timelineData.getAudioTrackList();
            if (audioTrackList != null) {
                /*恢复音频轨道以及其相关
                * Restore audio track and related object
                * */
                for (MeicamAudioTrack audioTrackData : audioTrackList) {
                    MeicamAudioTrack audioTrack = editTimeline.appendAudioTrack(audioTrackData.getIndex());
                    if (audioTrack != null) {
                        List<MeicamAudioClip> audioClipList = audioTrackData.getAudioClipList();
                        for (MeicamAudioClip audioClipData : audioClipList) {
                            audioTrack.addAudioClip(audioClipData);
                        }
                    }
                }
            }

            List<MeicamTimelineVideoFxTrack> timelineVideoFxTrackList = timelineData.getTimelineVideoFxTrackList();
            if (timelineVideoFxTrackList != null) {
                /*恢复时间线视频特效轨道以及相关，目前包括特效
                * Restore timeline video effects track and related, including effects at present.
                * */
                for (MeicamTimelineVideoFxTrack videoFxTrackData : timelineVideoFxTrackList) {
                    MeicamTimelineVideoFxTrack videoFxTrack = editTimeline.addTimelineFxTrack(videoFxTrackData.getIndex(), false);
                    int clipCount = videoFxTrackData.getClipCount();
                    if (videoFxTrack != null && clipCount > 0) {
                        for (int index = 0; index < clipCount; index++) {
                            MeicamTimelineVideoFxClip clip = videoFxTrackData.getClip(index);
                            videoFxTrack.addFxClip(clip, clip.getInPoint(), clip.getOutPoint() - clip.getInPoint(), clip.getDesc());
                        }
                    }
                }
            }

            List<MeicamTimelineVideoFxTrack> filterAndAdjustTimelineTracks = timelineData.getFilterAndAdjustTimelineTracks();
            if (filterAndAdjustTimelineTracks != null) {
                /*恢复时间线视频特效轨道以及相关，目前包括滤镜 调节
                * Restore timeline video effects track and related, including filter and adjustment.
                * */
                for (MeicamTimelineVideoFxTrack fxTrack : filterAndAdjustTimelineTracks) {
                    MeicamTimelineVideoFxTrack videoFxTrack = editTimeline.addFilterAndAdjustTrack(fxTrack.getIndex());
                    int clipCount = fxTrack.getFilterAndAdjustCount();
                    for (int index = 0; index < clipCount; index++) {
                        MeicamTimelineVideoFilterAndAdjustClip oldAdjustClip = fxTrack.getFilterAndAdjustClip(index);
                        videoFxTrack.addFilterAndAdjustClip(oldAdjustClip);
                    }
                }
            }


            List<MeicamTimelineVideoFxClip> timelineVideoFxClips = timelineData.getTimelineVideoFxClips();
            if (timelineVideoFxClips != null) {
                /*恢复时间线特效,马赛克，目前包括马赛克、模糊
                * Restoration timeline  effects, mosaic, currently including mosaic, blur.
                * */
                for (MeicamTimelineVideoFxClip videoFxClipData : timelineVideoFxClips) {
                    editTimeline.addTimelineVideoFxInClipList(videoFxClipData, videoFxClipData.getInPoint(),
                            videoFxClipData.getOutPoint() - videoFxClipData.getInPoint(), videoFxClipData.getDesc());
                }
            }


            /*恢复字幕，贴纸，组合字幕
            * Restore caption、sticker and compound caption.
            * */
            List<MeicamStickerCaptionTrack> meicamStickerCaptionTracks = timelineData.getStickerCaptionTrackList();
            if (meicamStickerCaptionTracks != null) {
                for (MeicamStickerCaptionTrack meicamStickerCaptionTrackData : meicamStickerCaptionTracks) {
                    MeicamStickerCaptionTrack meicamStickerCaptionTrack = editTimeline.addStickCaptionTrack(meicamStickerCaptionTrackData.getIndex(), false);
                    if (meicamStickerCaptionTrack != null) {
                        List<ClipInfo<?>> clipInfos = meicamStickerCaptionTrackData.getClipInfoList();
                        for (ClipInfo<?> clipInfo : clipInfos) {
                            meicamStickerCaptionTrack.addCaptionSticker(clipInfo, true);
                        }
                    }
                }
            }

            List<MeicamResource> resourceList = timelineData.getResourceList();
            if (resourceList != null) {
                editTimeline.setResourceList(resourceList);
            }

            return editTimeline;
        }

        @IntDef({BUILD_NORMAL, BUILD_FORM_DRAFT, BUILD_FORM_TIMELINE, BUILD_FORM_TEMPLATE, BUILD_FORM_NVSTIMELINE})
        @Retention(RetentionPolicy.SOURCE)
        public @interface BuildType {
        }
    }

    private void clear() {
        clearVideoTrack();
        clearAudioTrack();
        clearCaptionSticker();
        clearTimelineFxTrack();
        clearTimelineFilterAndAdjustTrack();
        clearTimelineFxClipList();
        removeTheme();
    }


    /**
     * 清除所有的 字幕，贴纸，组合字幕
     * clear All caption sticker compoundCaption
     */
    private void clearCaptionSticker() {
        NvsTimeline nvsTimeline = getObject();
        if (nvsTimeline == null) {
            LogUtils.e("nvsTimeline is null");
            return;
        }

        NvsTimelineAnimatedSticker sticker = nvsTimeline.getFirstAnimatedSticker();
        while (sticker != null) {
            sticker = nvsTimeline.removeAnimatedSticker(sticker);
        }

        NvsTimelineCompoundCaption compoundCaption = nvsTimeline.getFirstCompoundCaption();
        while (compoundCaption != null) {
            compoundCaption = nvsTimeline.removeCompoundCaption(compoundCaption);
        }

        NvsTimelineCaption deleteCaption = nvsTimeline.getFirstCaption();
        while (deleteCaption != null) {
            int capCategory = deleteCaption.getCategory();
            LogUtils.e("capCategory = " + capCategory);
            int roleTheme = deleteCaption.getRoleInTheme();
            if (capCategory == NvsTimelineCaption.THEME_CATEGORY
                    && roleTheme != NvsTimelineCaption.ROLE_IN_THEME_GENERAL) {//主题字幕不作删除
                deleteCaption = nvsTimeline.getNextCaption(deleteCaption);
            } else {
                deleteCaption = nvsTimeline.removeCaption(deleteCaption);
            }
        }
        if (stickerCaptionTrackList != null) {
            stickerCaptionTrackList.clear();
        }
    }

    /**
     * Update track data.
     * 更新native 数据到上层
     * @param trackIndex the track index 轨道index
     * @param type       the type 轨道类型
     */
    public void updateTrackData(int trackIndex, String type) {
        int trackCount;
        if (CommonData.CLIP_TIMELINE_FX.equals(type)) {
            trackCount = getTimelineFxTrackCount();
            if (trackCount > trackIndex) {
                MeicamTimelineVideoFxTrack timelineFxTrack = getTimelineFxTrack(trackIndex);
                int clipCount = timelineFxTrack.getClipCount();
                if (clipCount > 0) {
                    for (int i = 0; i < clipCount; i++) {
                        MeicamTimelineVideoFxClip clip = timelineFxTrack.getClip(i);
                        clip.loadData();
                    }
                }
            }
        } if (CommonData.CLIP_FILTER.equals(type)||CommonData.CLIP_ADJUST.equals(type)) {
            trackCount = getFilterAndAdjustTimelineTracksCount();
            if (trackCount > trackIndex) {
                MeicamTimelineVideoFxTrack timelineFxTrack = getFilterAndAdjustTimelineTrack(trackIndex);
                int clipCount = timelineFxTrack.getFilterAndAdjustCount();
                if (clipCount > 0) {
                    for (int i = 0; i < clipCount; i++) {
                        MeicamTimelineVideoFilterAndAdjustClip clip = timelineFxTrack.getFilterAndAdjustClip(i);
                        clip.loadData();
                    }
                }
            }
        } else if (CommonData.CLIP_STICKER.equals(type)
                || CommonData.CLIP_COMPOUND_CAPTION.equals(type)
                || CommonData.CLIP_CAPTION.equals(type)) {
            trackCount = getStickerCaptionTrackCount();
            if (trackCount > trackIndex) {
                MeicamStickerCaptionTrack stickCaptionTrack = findStickCaptionTrack(trackIndex);
                int clipCount = stickCaptionTrack.getClipCount();
                if (clipCount > 0) {
                    for (int i = 0; i < clipCount; i++) {
                        ClipInfo<?> clip = stickCaptionTrack.getCaptionStickerClip(i);
                        clip.loadData();
                    }
                }
            }
        } else if (CommonData.CLIP_VIDEO.equals(type) || CommonData.CLIP_IMAGE.equals(type)) {
            trackCount = videoTrackCount();
            trackIndex += 1;
            if (trackCount > trackIndex) {
                MeicamVideoTrack videoTrack = getVideoTrack(trackIndex);
                int clipCount = videoTrack.getClipCount();
                if (clipCount > 0) {
                    for (int i = 0; i < clipCount; i++) {
                        videoTrack.getVideoClip(i).loadData();
                    }
                }

            }
        } else if (CommonData.CLIP_AUDIO.equals(type)) {
            trackCount = getAudioTrackCount();
            if (trackCount > trackIndex) {
                MeicamAudioTrack audioTrack = getAudioTrack(trackIndex);
                int clipCount = audioTrack.getClipCount();
                if (clipCount > 0) {
                    for (int i = 0; i < clipCount; i++) {
                        audioTrack.getAudioClip(i).loadData();
                    }
                }
            }
        }
    }

}
