<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/editor_thumbnail_trans_root_layout"
    android:layout_width="wrap_content"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/rect_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/inner_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:id="@+id/layout_cover_timeline_speed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="3dp"
        android:layout_marginTop="3dp"
        android:background="@drawable/bg_audio_draw_text"
        android:gravity="center"
        android:visibility="gone"
        android:orientation="horizontal"
        android:paddingLeft="3dp"
        android:paddingRight="3dp">

        <ImageView
            android:layout_width="7dp"
            android:layout_height="7dp"
            android:layout_gravity="center_vertical"
            android:background="@mipmap/sub_menu_icon_edit_speed" />

        <TextView
            android:id="@+id/tv_cover_timeline_speed_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="2dp"
            android:singleLine="true"
            android:textColor="@color/white_8"
            android:textSize="8sp" />
    </LinearLayout>


</RelativeLayout>