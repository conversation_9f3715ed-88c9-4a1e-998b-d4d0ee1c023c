<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_px_480">

    <ImageView
        android:id="@+id/iv_apply_all"
        android:layout_width="@dimen/dp_px_50"
        android:layout_height="@dimen/dp_px_50"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/dp_px_30"
        android:layout_marginLeft="@dimen/dp_px_30"
        android:layout_marginTop="@dimen/dp_px_30"
        android:background="@mipmap/ic_multi_trans"
        android:contentDescription="@null" />

    <TextView
        android:id="@+id/tv_apply_all"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_110"
        android:layout_toEndOf="@+id/iv_apply_all"
        android:layout_toRightOf="@+id/iv_apply_all"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp_px_30"
        android:paddingLeft="@dimen/dp_px_30"
        android:paddingEnd="@dimen/dp_px_0"
        android:paddingRight="@dimen/dp_px_0"
        android:text="@string/apply_all_clip"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_30" />


    <com.meishe.myvideo.view.MYMultiColorView
        android:id="@+id/multi_color_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_px_39"
        android:layout_marginLeft="@dimen/dp_px_39"
        android:layout_marginTop="@dimen/dp_px_99" />

    <View
        android:id="@+id/v_bottom_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_1"
        android:layout_below="@+id/multi_color_view"
        android:layout_marginTop="@dimen/dp_px_45"
        android:background="@color/menu_divide_color" />


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_225"
        android:layout_below="@+id/v_bottom_line"
        android:layout_centerHorizontal="true"
        android:paddingTop="@dimen/dp_px_48"
        android:text="@string/sub_menu_canvas_color"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_42" />

    <ImageView
        android:id="@+id/iv_confirm"
        android:layout_width="@dimen/dp_px_69"
        android:layout_height="@dimen/dp_px_69"
        android:layout_below="@+id/v_bottom_line"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_gravity="right"
        android:layout_marginTop="@dimen/dp_px_48"
        android:layout_marginEnd="@dimen/dp_px_39"
        android:layout_marginRight="@dimen/dp_px_39"
        android:background="@mipmap/ic_confirm"
        android:contentDescription="@null" />
</merge>