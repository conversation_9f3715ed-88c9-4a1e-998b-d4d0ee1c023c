package com.meishe.myvideo.activity.iview;

import com.meishe.base.model.IBaseView;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>Z<PERSON>
 * @CreateDate :2020/12/17 15:22
 * @Description :导出视频view Compile View
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface CompileView extends IBaseView {
    /**
     * 导出编译开始
     * Export compilation start
     */
    void onCompileStart();

    /**
     * 导出编译中
     * Export under compilation
     *
     * @param progress the compile progress
     */
    void onCompileProgress(int progress);

    /**
     * 导出编译结束
     * Export compilation end
     *
     * @param complete true is success,false not
     */
    void onCompileEnd(boolean complete);

    /**
     * 是否是活动的
     * Is active or not
     *
     * @return true:yes; false:no
     */
    boolean isActive();
}
