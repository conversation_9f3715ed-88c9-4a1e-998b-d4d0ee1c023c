package com.meishe.player;

import android.app.Activity;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;

import androidx.fragment.app.FragmentManager;

import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseActivity;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.player.fragment.RectSelectVideoFragment;
import com.meishe.player.view.bean.TransformData;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * <AUTHOR> Cao<PERSON><PERSON><PERSON>hao
 * @CreateDate : 2020/11/27 16:29
 * @Description : 通用模板的视频裁剪 Cut video clip Activity
 * @Copyright : www.meishesdk.com Inc. All rights reserved.
 */
public class ImageRectCutActivity extends BaseActivity {
    public static final String VIDEO_PATH = "videoPath";
    public static final String RAW_RATIO = "raw_ratio";
    public static final String TRANSFORM_DATA = "transform_data";
    public static final String INTENT_RECT_DATA = "rectData";
    private RectSelectVideoFragment mBaseVideoFragment;
    private String mVideoPath;
    private float mRawRatio;
    private TransformData mTransformData;

    @Override
    protected int bindLayout() {
        return R.layout.activity_tailor;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        Bundle extras = getIntent().getExtras();
        if (null != extras) {
            mVideoPath = extras.getString(VIDEO_PATH);
            mRawRatio = extras.getFloat(RAW_RATIO);
            mTransformData = extras.getParcelable(TRANSFORM_DATA);
        }
        if (TextUtils.isEmpty(mVideoPath)) {
            LogUtils.e("initData: error! mVideoPath is empty!");
        }
    }

    @Override
    protected void initView() {
        addVideoFragment();
        ImageView activityTailorBack = findViewById(R.id.activity_tailor_back);
        activityTailorBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent();
                setResult(Activity.RESULT_CANCELED, intent);
                AppManager.getInstance().finishActivity();
            }
        });
        Button activityTailorSure = findViewById(R.id.activity_tailor_sure);
        activityTailorSure.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent();
                intent.putExtra(INTENT_RECT_DATA, mBaseVideoFragment.getTransFromDataInFile());
                intent.putExtra(VIDEO_PATH, mVideoPath);
                // 设置返回码和返回携带的数据 Sets the return code and returns the data carried
                setResult(Activity.RESULT_OK, intent);
                AppManager.getInstance().finishActivity();
            }
        });
        Drawable drawable = CommonUtils.getRadiusDrawable(-1, -1,
                getResources().getDimensionPixelOffset(R.dimen.dp_px_150), getResources().getColor(R.color.activity_tailor_button_background));
        activityTailorSure.setBackground(drawable);
    }

    private void addVideoFragment() {
        FragmentManager fragmentManager = getSupportFragmentManager();
        mBaseVideoFragment = RectSelectVideoFragment.newInstance(mVideoPath);
        if (mRawRatio != 0) {
            mBaseVideoFragment.showRectSelectView(mRawRatio, mTransformData);
        }
        fragmentManager.beginTransaction().add(R.id.activity_tailor_fragment_container, mBaseVideoFragment).commitAllowingStateLoss();
        fragmentManager.beginTransaction().show(mBaseVideoFragment);
    }
}