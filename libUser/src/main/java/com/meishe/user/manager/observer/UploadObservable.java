package com.meishe.user.manager.observer;

import android.database.Observable;

import com.meishe.base.utils.ThreadUtils;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/11 18:39
 * @Description :上传被监听对象 The observable for uploading.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class UploadObservable extends Observable<UploadObserver> {

    public void notifyStateChanged(String tag, int count){
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (int i = mObservers.size() - 1; i >= 0; i--) {
                    UploadObserver uploadObserver = mObservers.get(i);
                    if (uploadObserver.isAlive()) {
                        uploadObserver.onStateChanged(tag, count);
                    }
                }
            }
        });
    }
}
