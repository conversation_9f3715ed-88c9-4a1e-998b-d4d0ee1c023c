package com.meishe.myvideo.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Rect;
import android.os.Vibrator;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.view.MultiThumbnailSequenceView;
import com.meishe.myvideo.R;
import com.meishe.myvideo.ui.bean.BaseTrackClip;
import com.meishe.myvideo.ui.bean.ITrackClip;
import com.meishe.myvideo.ui.bean.KeyFrameInfo;
import com.meishe.myvideo.ui.bean.LineRegionClip;
import com.meishe.myvideo.ui.bean.ThumbnailClip;
import com.meishe.myvideo.ui.trackview.DragThumbnailView;
import com.meishe.myvideo.ui.trackview.EffectLineView;
import com.meishe.myvideo.ui.trackview.MultiThumbnailView;
import com.meishe.myvideo.ui.trackview.SpanView;
import com.meishe.myvideo.ui.trackview.adapter.OperationTimeDotAdapter;
import com.meishe.myvideo.ui.trackview.impl.OnThumbnailTrimListener;
import com.meishe.myvideo.ui.trackview.impl.OnTrackClickListener;
import com.meishe.myvideo.ui.trackview.impl.OperationListener;
import com.meishe.myvideo.util.PixelPerMicrosecondUtil;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;

import static com.meishe.base.constants.Constants.TIME_BASE;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/03/20 14:10
 * @Description :时间轴编辑区视图 time line edit view
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MYEditorTimeLine extends RelativeLayout implements PixelPerMicrosecondUtil.PixelPerMicrosecondChangeListener {
    private static final String TAG = "MYEditorTimeLine";
    private final long MILLISECOND = 1000000;
    private static final long MIN_DURATION = (long) (CommonData.TIMEBASE * 0.1f);
    private MultiThumbnailView mSvThumbnailView;
    /**
     * 每微秒显示的像素值 The value of pixels displayed per microsecond
     */
    private double mPixelPerMicrosecond = 0D;

    private MYTimelineEditorRecyclerView mRvTimeDotList;
    private OperationTimeDotAdapter mTimeDotAdapter;
    private ImageView mIvAddMaterial;
    private DragThumbnailView mFlDragThumbnail;
    private SpanView mSpanView;
    private OnScrollListener mScrollListener;
    private LinearLayout mLlToggleOriginalVoice;
    private View mAddCoverContainer;
    private int mScreenWidth;
    private static final long TIME_DELAY = 40;
    private ImageView mIvToggleOriginalVoice;
    private TextView mTvToggleOriginalVoice;
    private ImageView mIvAddCover;
    private EffectLineView mEffectLineView;
    private FrameLayout mFlMainTrackParent;

    private boolean mEnableThumbnailCoverEdit;
    private boolean mScrollFromUser;
    private boolean mCanCheckOverrun = true;
    private boolean mIsTrimming;
    private boolean openOriginalVoice;
    /**
     * 是否可交换片段
     * Whether thumbnail can move.
     */
    private boolean mEnableThumbnailMove = true;
    /**
     * 震动器
     * vibrator
     */
    private Vibrator mVibrator;
    private OperationListener mOperationListener;
    private OnTrackClickListener mTrackClickListener;
    private OnThumbnailTrimListener mThumbnailTrimListener;
    private OnCoverClickListener mOnCoverClickListener;

    public MYEditorTimeLine(Context context) {
        this(context, null);
    }

    public MYEditorTimeLine(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MYEditorTimeLine(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
        initData();
        initListener();
    }

    private LinearLayout mLeftButtonContainer;

    private void initView() {
        mScreenWidth = ScreenUtils.getScreenWidth();
        mVibrator = (Vibrator) getContext().getSystemService(Context.VIBRATOR_SERVICE);
        View view = LayoutInflater.from(getContext()).inflate(R.layout.timeline_editor_view_layout, this);
        mSvThumbnailView = view.findViewById(R.id.editor_multi_thumbnail_sequence_view);
        mRvTimeDotList = view.findViewById(R.id.editor_timeline_view_time_making_line_recycler);
        mLeftButtonContainer = view.findViewById(R.id.fl_original_container);
        mLlToggleOriginalVoice = view.findViewById(R.id.ll_toggle_original_voice);
        mTvToggleOriginalVoice = view.findViewById(R.id.tv_toggle_original_voice);
        mIvToggleOriginalVoice = view.findViewById(R.id.iv_toggle_original_voice);
        mIvAddCover = view.findViewById(R.id.iv_add_cover);
        mAddCoverContainer = view.findViewById(R.id.fl_add_cover_container);
        mIvAddMaterial = view.findViewById(R.id.editor_add_clip_img);
        mFlDragThumbnail = view.findViewById(R.id.fl_drag_thumbnail);
        mSpanView = view.findViewById(R.id.ll_span_view);
        mEffectLineView = view.findViewById(R.id.v_effect_line);
        mFlMainTrackParent = view.findViewById(R.id.editor_main_track_parent);
        mPixelPerMicrosecond = PixelPerMicrosecondUtil.getPixelPerMicrosecond(getContext());
        PixelPerMicrosecondUtil.addPixelPerMicrosecondChangeListener(this);
        mSvThumbnailView.setStartPadding(mScreenWidth / 2);
        mSvThumbnailView.setEndPadding(mScreenWidth / 2);
        mSpanView.setParams(mScreenWidth / 2, mPixelPerMicrosecond, MIN_DURATION);
        mSvThumbnailView.setPixelPerMicrosecond(mPixelPerMicrosecond);
        mSvThumbnailView.setThumbnailImageFillMode(MultiThumbnailView.THUMBNAIL_IMAGE_FILLMODE_ASPECTCROP);
        View firstChild = mLeftButtonContainer.getChildAt(0);
        if (firstChild != null) {
            firstChild.post(new Runnable() {
                @Override
                public void run() {
                    int childCount = mLeftButtonContainer.getChildCount();
                    int innerWidth = 0;
                    for (int i = 0; i < childCount; i++) {
                        innerWidth += mLeftButtonContainer.getChildAt(i).getWidth();
                    }
                    LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) firstChild.getLayoutParams();
                    layoutParams.leftMargin = mScreenWidth / 2 - innerWidth;
                    firstChild.setLayoutParams(layoutParams);
                }
            });
        }
    }

    private void initData() {
        mRvTimeDotList.setLayoutManager(new LinearLayoutManagerWrapper(getContext(), RecyclerView.HORIZONTAL, false));
        mTimeDotAdapter = new OperationTimeDotAdapter(mScreenWidth / 10, mScreenWidth / 2, mScreenWidth / 2);
        mRvTimeDotList.setAdapter(mTimeDotAdapter);
    }

    @SuppressLint("ClickableViewAccessibility")
    private void initListener() {
        setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                notShowSpanView(true);
            }
        });
        mIvAddMaterial.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mTrackClickListener != null) {
                    mTrackClickListener.onAddThumbnailClick();
                }
            }
        });
        mEffectLineView.setEventListener(new EffectLineView.EventListener() {
            @Override
            public void onPipClick(int trackIndex, long inPoint) {
                if (mTrackClickListener != null) {
                    mTrackClickListener.onPipIconClick(trackIndex, inPoint);
                }
            }
        });
        mSvThumbnailView.setOnScrollChangeListener(new MultiThumbnailView.OnScrollChangeListener() {
            private int oldScrollX;
            private int lastDx = 0;

            @Override
            public void onScrollChanged(MultiThumbnailView multiThumbnailView, int newDx, int oldDx) {
                if (oldDx == lastDx && newDx == oldScrollX) {
                    return;
                }
                lastDx = oldDx;
                if (oldScrollX >= 0 && oldScrollX != newDx) {
                    mRvTimeDotList.scrollBy(newDx - oldDx, 0);
                    if (mainSpanIsShow()) {
                        mSpanView.scrollBy(newDx - oldDx, 0);
                    }
                    mEffectLineView.scrollTo(newDx, 0);
                    mLeftButtonContainer.scrollTo(newDx, 0);
                }
                oldScrollX = newDx;
                long timestamp = lengthToDuration(newDx);
                if (checkSpanOverrunEnable() && mainSpanOverrun(newDx, timestamp)) {
                    notShowSpanView(true);
                }
                if (mEnableThumbnailCoverEdit) {
                    selectedThumbnail(findMainTrackClip(timestamp));
                } else if (!isTrimming()) {
                    checkKeyFrameSelected(timestamp);
                }
//                LogUtils.d("newDx=" + newDx + ",oldDx=" + oldDx + ",mScrollFromUser=" + isScrollFromUser() + ",checkOverrun=" + checkSpanOverrunEnable() + ",tmpTimeStamp=" + timestamp);
                if (mOperationListener != null && !isTrimming()) {
                    mOperationListener.onTimeScroll(timestamp, isScrollFromUser(), newDx, oldDx);
                }
            }
        });
        mSvThumbnailView.setOnTailViewClickListener(new MultiThumbnailView.OnTailViewClickListener() {
            @Override
            public void onClick(int index, ThumbnailClip.TailInfo tailInfo) {
                if (mTrackClickListener != null) {
                    mTrackClickListener.onThumbnailTailClick(index, tailInfo);
                }
            }
        });
        mFlDragThumbnail.setOnMoveListener(new DragThumbnailView.OnMoveListener() {
            @Override
            public boolean onMove(final int from, final int to) {
                if (!mEnableThumbnailMove) {
                    return false;
                }
                boolean canMove = true;
                // Log.d(TAG, "交换，form=" + from + ",to=" + to);
                if (mOperationListener != null) {
                    canMove = mOperationListener.onThumbnailMove(from, to);
                }
                if (canMove) {
                    mSvThumbnailView.moveThumbnail(from, to);
                    mSvThumbnailView.post(new Runnable() {
                        @Override
                        public void run() {
                            ITrackClip targetClip = mSvThumbnailView.findTargetClip(to);
                            if (targetClip != null) {
                                scrollToFromUser(durationToLength(targetClip.getInPoint()));
                                notShowSpanView();
//                                showSpanView(targetClip);
                            } else {
                                List<ITrackClip> thumbnailList = mSvThumbnailView.getThumbnailList();
                                LogUtils.e("move error,from=" + from + ",to=" + to + ",size="
                                        + (thumbnailList == null ? 0 : thumbnailList.size()));
                            }
                        }
                    });
                }
                return canMove;
            }
        });
        mSvThumbnailView.setOnTouchListener(mOnTouch);
        mSpanView.setOnHandleChangeListener(mHandleListener);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        if (ev.getAction() == MotionEvent.ACTION_UP) {
            if (isTrimming()) {
                return super.dispatchTouchEvent(ev);
            }
            int x = (int) ev.getX();
            int y = (int) ev.getY();
            checkOriginalVoiceRegion(x, y);
            checkCoverRegion(x, y);
        }
        return super.dispatchTouchEvent(ev);
    }

    private Rect mOriginalVoiceRect = new Rect();
    private Rect mCoverRect = new Rect();

    /**
     * 检查原声区域，用于处理点击事件，因为mSvThumbnailView消耗掉了触摸事件，原声区域无法接收到。
     * 如果把原声区域放在最后，可以有点击事件，但是ThumbnailView进行裁剪的时候无法遮盖住原声区域
     * Check original voice region ,used to handle click events.Because the ThumbnailView consumes
     * touch events, the original sound region (view) cannot be received
     *
     * @param x the touch x
     * @param y the touch y
     */
    private void checkOriginalVoiceRegion(int x, int y) {
        int viewX = mLlToggleOriginalVoice.getLeft() - mLeftButtonContainer.getScrollX();
        int viewY = mLlToggleOriginalVoice.getTop() + mLeftButtonContainer.getTop();
        mOriginalVoiceRect.set(viewX, viewY, viewX + mLlToggleOriginalVoice.getWidth(),
                viewY + mLlToggleOriginalVoice.getHeight());
        if (mOriginalVoiceRect.contains(x, y)) {
            toggleOriginalVoice(mIvToggleOriginalVoice.isSelected(), true);
            return;
        }
        viewX = mLeftButtonContainer.getLeft() - mLeftButtonContainer.getScrollX();
        viewY = mLeftButtonContainer.getTop();
        mOriginalVoiceRect.set(viewX, viewY, viewX + mLeftButtonContainer.getWidth(),
                viewY + mLeftButtonContainer.getHeight());
        if (mOriginalVoiceRect.contains(x, y)) {
            notShowSpanView(true);
        }
    }

    /**
     * 检查封面区域，用于处理点击事件，因为mSvThumbnailView消耗掉了触摸事件，封面区域无法接收到。
     * 如果把封面区域放在最后，可以有点击事件，但是ThumbnailView进行裁剪的时候无法遮盖住原声区域
     * Check cover region ,used to handle click events.Because the ThumbnailView consumes
     * touch events, the cover region (view) cannot be received
     *
     * @param x the touch x
     * @param y the touch y
     */
    private void checkCoverRegion(int x, int y) {
        Log.e(TAG, "checkCoverRegion: mIvAddCover.getLeft() = "+mAddCoverContainer.getLeft() );
        int viewX = mAddCoverContainer.getLeft() - mLeftButtonContainer.getScrollX();
        int viewY = mAddCoverContainer.getTop() + mLeftButtonContainer.getTop();
        mCoverRect.set(viewX, viewY, viewX + mAddCoverContainer.getWidth(),
                viewY + mAddCoverContainer.getHeight());
        if (mCoverRect.contains(x, y)) {
            if (mOnCoverClickListener != null) {
                mOnCoverClickListener.onCoverClicked();
            }
            return;
        }
        viewX = mLeftButtonContainer.getLeft() - mLeftButtonContainer.getScrollX();
        viewY = mLeftButtonContainer.getTop();
        mCoverRect.set(viewX, viewY, viewX + mLeftButtonContainer.getWidth(),
                viewY + mLeftButtonContainer.getHeight());
        if (mCoverRect.contains(x, y)) {
            notShowSpanView(true);
        }
    }


    /**
     * 检查是否选中关键帧
     * Check whether the keyframe is selected
     *
     * @param point the timestamp 时间点
     */
    public void checkKeyFrameSelected(long point) {
        ITrackClip mainSelectedClip = getMainSelectedClip();
        if (mainSelectedClip != null) {
            KeyFrameInfo keyFrameInfo = mainSelectedClip.getKeyFrameInfo();
            // if (keyFrameInfo.hasKeyFrame(point)) {
            mSvThumbnailView.checkThumbnailKeyFrame(mainSelectedClip, point);
        }
    }

    /**
     * 设置操作的时长
     * Set track duration
     *
     * @param duration long the main track duration 主轨道时长，微秒
     */
    public void setOperationDuration(long duration) {
        if (mainSpanIsShow()) {
            notShowSpanView(true);
        }
        changeOperationDuration(duration, false);
    }

    /**
     * 更改操作的时长
     * Change operation duration
     *
     * @param duration           long the main track duration 主轨道时长, 微秒
     * @param isRelativeDuration The relative time 相对时长
     */
    private void changeOperationDuration(long duration, boolean isRelativeDuration) {
        if (isRelativeDuration) {
            mTimeDotAdapter.updateDuration(mTimeDotAdapter.getDuration() + duration / MILLISECOND);
            //在updateDuration更新后再去取时长
            mFlDragThumbnail.setWidth(durationToLength(mTimeDotAdapter.getDuration() * MILLISECOND) + mScreenWidth);
        } else {
            mTimeDotAdapter.setData(PixelPerMicrosecondUtil.getScale(), duration / MILLISECOND);
            mFlDragThumbnail.setWidth(durationToLength(duration) + mScreenWidth);
        }
    }

    /**
     * 设置主轨道的缩图序列集合
     * Set main track list
     *
     * @param list the clip list 主轨道片段集合
     */
    public void setMainTrackList(List<ITrackClip> list) {
        if (mainSpanIsShow()) {
            notShowSpanView(true);
        }
        mSvThumbnailView.setThumbnailList(list);
        mFlDragThumbnail.setThumbnailList(list);
    }


    /**
     * 添加主轨道缩略图片段
     * Add main track clip
     *
     * @param index         int the index 添加到的位置索引
     * @param trackClipList List<ITrackClip> the clip list 要添加的片段集合
     */
    public void addMainTrackClip(int index, List<ITrackClip> trackClipList) {
        if (trackClipList != null) {
            long duration = 0;
            for (ITrackClip trackClip : trackClipList) {
                duration += trackClip.getOutPoint() - trackClip.getInPoint();
            }
            changeOperationDuration(duration, true);
            mSvThumbnailView.addThumbnail(index, trackClipList);
            mFlDragThumbnail.addThumbnail(index, trackClipList);
        }
    }

    /**
     * 在最后边添加主轨道缩略图片段
     * Add main track clip at the end
     *
     * @param trackClip ITrackClip the clip 要添加的片段
     */
    public void addMainTrackClip(ITrackClip trackClip) {
        addMainTrackClip(mSvThumbnailView.getThumbnailList().size(), trackClip);
    }

    /**
     * 添加主轨道缩略图片段
     * Add main track clip
     *
     * @param index     int the index 添加到的位置索引
     * @param trackClip ITrackClip  the clip 要添加的片段
     */
    public void addMainTrackClip(int index, ITrackClip trackClip) {
        if (trackClip != null) {
            long duration = trackClip.getOutPoint() - trackClip.getInPoint();
            changeOperationDuration(duration, true);
            //LogUtils.d("trackClip=" + trackClip + ",index=" + index + ",duration=" + duration);
            mSvThumbnailView.addThumbnail(index, trackClip, true);
            mFlDragThumbnail.addThumbnail(index, trackClip);
        }
    }

    /**
     * 分割主轨道片段
     * Split main track clip
     *
     * @param index     int the split index 要分割片段的索引
     * @param outP1     the out point of pre-clip 前片段的出点
     * @param trimOutP1 the trim out point of pre-clip 前片段的裁出点
     * @param inP2      the in point of post-clip 后片段的出点
     * @param trimInP2  the trim in point of post-clip 后片段的裁出点
     */
    public ITrackClip splitMainTrackClip(int index, long outP1, long trimOutP1, long inP2, long trimInP2) {
        ITrackClip trackClip = null;
        ITrackClip selectedClip = getMainSelectedClip();
        if (selectedClip != null && selectedClip.getIndexInTrack() == index) {
            trackClip = mSvThumbnailView.splitThumbnail(outP1, trimOutP1, inP2, trimInP2, selectedClip);
            if (trackClip != null) {
                mFlDragThumbnail.addThumbnail(trackClip.getIndexInTrack(), trackClip);
            }
        } else {
            Log.e(TAG, "split clip error,check it!!!");
        }
        return trackClip;
    }

    /**
     * 删除主轨道最后边的片段
     * Delete main track the last clip
     */
    public void deleteMainTrackClip() {
        deleteMainTrackClip(mSvThumbnailView.getThumbnailList().size() - 1);
    }

    /**
     * 删除主轨道片段
     * Delete main track clip
     *
     * @param index the delete index 索引
     */
    public void deleteMainTrackClip(int index) {
        deleteMainTrackClip(mSvThumbnailView.findTargetClip(index));
    }

    /**
     * 删除主轨道片段
     * Delete main track clip
     *
     * @param trackClip the track clip 要删除的片段
     */
    public void deleteMainTrackClip(ITrackClip trackClip) {
        if (trackClip != null) {
            mSvThumbnailView.deleteThumbnail(trackClip);
            changeOperationDuration(-(trackClip.getOutPoint() - trackClip.getInPoint()), true);
            mFlDragThumbnail.deleteThumbnail(trackClip.getIndexInTrack());
        }
        notShowSpanView(true);
    }

    /**
     * 更改所给片段的变速值
     * Update track clip speed
     *
     * @param trackClip         the track clip 要变速的片段
     * @param operationDuration the operation duration 操作总时长，时间线时长
     */
    public void changeMainTrackClipSpeed(ITrackClip trackClip, long operationDuration) {
        changeMainTrackClipInAndOut(trackClip, operationDuration);
        mSpanView.update(!TextUtils.isEmpty(trackClip.getSpeedInfo().getSpeedName()));
    }

    /**
     * 更改所给片段的道具icon
     * Update track clip prop
     */
    public void changeMainTrackClipProp() {
        mSpanView.displayFaceProp();
    }

    /**
     * 更改所给片段的音量icon
     * Update track clip volume
     */
    public void changeMainTrackClipVolume() {
        mSpanView.displayVolumeIcon();
    }

    /**
     * 更改所给片段的入出点
     * Update track clip in point and out point
     *
     * @param trackClip         the track clip 要更新入出点的片段
     * @param operationDuration the operation duration 操作总时长，时间线时长
     */
    public void changeMainTrackClipInAndOut(ITrackClip trackClip, long operationDuration) {
        mSvThumbnailView.updateThumbnail(trackClip, true);
        changeOperationDuration(operationDuration, false);
        /*
         * 变速的时候会发生滚动，造成时间点RecyclerView滚动不正确以及超过时间点拖动把手消失
         * Rolling occurs when shifting gears, resulting in incorrect scrolling of the RecyclerView
         * at the time point and the disappearance of the handle when dragging beyond the time point.
         */
        setScrollFromUser(false);
        relocationTimeDotPosition();
    }

    /**
     * 刷新某一个片段
     * Update the thumbnail clip
     *
     * @param trackClip           the track clip 片段
     * @param updateInAndOutPoint true update in and out point 更新出入点，false not 不更新.
     **/
    public void updateThumbnail(final ITrackClip trackClip, boolean updateInAndOutPoint) {
        mSvThumbnailView.updateThumbnail(trackClip, updateInAndOutPoint);
        if (trackClip != null && trackClip.equals(getMainSelectedClip())) {
            mSvThumbnailView.post(new Runnable() {
                @Override
                public void run() {
                    mSpanView.update(true);
                }
            });
        }
    }

    /**
     * 更新缩略图尾部信息
     * Update thumbnail tail info
     *
     * @param index the tail index 更新的索引
     */
    public void updateThumbnailTailInfo(int index) {
        mSvThumbnailView.updateThumbnailTailInfo(index);
    }

    /**
     * 设置缩略图尾部信息
     * Set thumbnail tail info
     *
     * @param index    the tail index 更新的索引
     * @param tailInfo the tail info 要设置尾部信息
     */
    public void setThumbnailTailInfo(int index, ThumbnailClip.TailInfo tailInfo) {
        mSvThumbnailView.setThumbnailTailInfo(index, tailInfo);
    }

    /**
     * 设置所有缩略图尾部信息
     * Set all thumbnail tail info
     *
     * @param tailInfo the tail info 要设置尾部信息
     */
    public void setAllThumbnailTailInfo(ThumbnailClip.TailInfo tailInfo) {
        mSvThumbnailView.setAllThumbnailTailInfo(tailInfo);
    }

    /**
     * 根据索引找到目标tail info
     * Find the target TailInfo according to the index
     *
     * @param index the index 索引
     */
    public ThumbnailClip.TailInfo findThumbnailTailInfo(int index) {
        return mSvThumbnailView.findThumbnailTailInfo(index);
    }

    /**
     * 设置缩略图片尾视图的可见性
     * Set tail view visibility
     *
     * @param visibility the visibility
     */
    public void setTailViewVisibility(int visibility) {
        mSvThumbnailView.setTailViewVisibility(visibility);
    }

    /**
     * 给缩略图添加关键帧标记
     * Adds a keyframe flag to the thumbnail clip
     *
     * @param inPoint the inPoint 入点
     */
    public void addKeyFrameTag(long inPoint) {
        ITrackClip trackClip = mSpanView.getTrackClip();
        if (trackClip != null) {
            mSvThumbnailView.addThumbnailKeyFrame(trackClip, inPoint);
        }
    }

    /**
     * 给缩略图添加关键帧标记
     * Delete the keyframe flag of the thumbnail clip
     *
     * @param inPoint the inPoint 入点
     */
    public void deleteKeyFrameTag(long inPoint) {
        ITrackClip trackClip = mSpanView.getTrackClip();
        if (trackClip != null) {
            mSvThumbnailView.deleteThumbnailKeyFrame(trackClip, inPoint);
        }
    }

    /**
     * 根据索引找到目标轨道片段
     * Find the target ITrackClip according to the index
     *
     * @param index the index 索引
     */
    public ITrackClip findMainTrackClip(int index) {
        return mSvThumbnailView.findTargetClip(index);
    }

    /**
     * 根据时间戳找到目标轨道片段
     * Find the target ITrackClip according to the timestamp
     *
     * @param timestamp the timestamp 时间戳
     */
    public ITrackClip findMainTrackClip(long timestamp) {
        return mSvThumbnailView.findTargetClip(timestamp);
    }

    /**
     * 找到主轨道最后的片段
     * Find the last ITrackClip
     */
    public ITrackClip findLastMainTrackClip() {
        return findMainTrackClip(mSvThumbnailView.getThumbnailList().size() - 1);
    }


    /**
     * 显示主轨道拖动把手
     * Show main track drag span view
     *
     * @param time the timestamp 时间戳
     */
    public void showSpanView(long time) {
        toggleSpanView(mSvThumbnailView.findTargetClip(time));
    }

    /**
     * 显示主轨道拖动把手
     * Show main track drag span view
     *
     * @param time the timestamp 时间戳
     */
    public void showSpanView(long time, boolean notify) {
        ITrackClip clip = mSvThumbnailView.findTargetClip(time);
        if (mainSpanIsShow() && mSpanView.isSameClip(clip)) {
            return;
        }
        showSpanView(clip, mSvThumbnailView.getScrollX(), notify);
    }

    /**
     * 更新主轨道把手的内容
     * Update SpanView content
     *
     * @param showDuration true show duration, false not ,真则显示时长，假则不显示
     */
    public void updateSpanViewContent(boolean showDuration) {
        mSpanView.displayDuration();
        mSpanView.displaySpeed(showDuration);
        mSpanView.displayFaceProp();
        mSpanView.displayVolumeIcon();
    }


    /**
     * 显示或者不显示主轨道拖动把手
     * Toggle main track drag span view
     *
     * @param uiClip the track clip 要显示的轨道片段
     */
    public void toggleSpanView(ITrackClip uiClip) {
        if (mainSpanIsShow() && mSpanView.isSameClip(uiClip)) {
            notShowSpanView(true);
            return;
        }
        showSpanView(uiClip);
    }

    /**
     * 显示主轨道拖动把手
     * Show main track drag span view
     *
     * @param uiClip the track clip 要显示的轨道片段
     */
    public void showSpanView(ITrackClip uiClip) {
        showSpanView(uiClip, mSvThumbnailView.getScrollX(), false);
    }

    /**
     * Set cover.
     * 设置封面
     * @param bitmap the bitmap 封面bitmap
     */
    public void setCover(Bitmap bitmap){
        mIvAddCover.setImageBitmap(bitmap);
    }

    /**
     * Set cover.
     * 设置封面
     * @param coverPath the cover path 封面路径
     */
    public void setCover(String coverPath){
        Bitmap bitmap = BitmapFactory.decodeFile(coverPath);
        if (bitmap != null) {
            mIvAddCover.setImageBitmap(null);
            mIvAddCover.setImageBitmap(bitmap);
        }
    }

    /**
     * 显示主轨道拖动把手
     * Show main track drag span view
     *
     * @param uiClip  the track clip 要显示的轨道片段
     * @param scrollX the scroll x 滚动的初始x坐标
     */
    private void showSpanView(final ITrackClip uiClip, final int scrollX, boolean notify) {
        if (uiClip == null || BaseTrackClip.CLIP_HOLDER.equals(uiClip.getType())) {
            Log.e(TAG, "showSpanView,uiClip is null or is holder");
            return;
        }
        ITrackClip lastClip = mSpanView.getTrackClip();
        if (lastClip != null && uiClip.getIndexInTrack() != lastClip.getIndexInTrack()) {
            enableThumbnailSpeed(lastClip, true);
            enableThumbnailKeyFrameTag(lastClip, false);
        }
        enableThumbnailSpeed(uiClip, false);
        if (!mainSpanIsShow()) {
            mSpanView.setVisibility(VISIBLE);
            setTailViewVisibility(View.GONE);
        }
        int difference = scrollX - durationToLength(uiClip.getInPoint());
        int leftMargin = 0;
        if (difference < mScreenWidth / 2) {
            leftMargin = mScreenWidth / 2 - difference;
            difference = 0;
        } else {
            difference = difference - mScreenWidth / 2 + mSpanView.getHandleWidth();
        }
        //LogUtils.d("difference=" + difference + ",leftMargin=" + leftMargin + ",getScrollX()=" + mSpanView.getScrollX());
        mSpanView.forceScrollTo(difference);
        mSpanView.setMargin(leftMargin);
        mSpanView.setTrackClip(uiClip, true);
        enableThumbnailKeyFrameTag(uiClip, true);
        if (mTrackClickListener != null && !mEnableThumbnailCoverEdit && notify) {
            /*from user 是扩展字段，暂时无用
            * The from user is an extension field and is temporarily unavailable.
            * */
            mTrackClickListener.onThumbnailClick(uiClip, true, true);
        }
    }

    /**
     * 隐藏主轨道拖动把手
     * Hide main track drag span view
     */
    public void notShowSpanView() {
        notShowSpanView(false);
    }

    /**
     * 隐藏主轨道拖动把手
     * Hide main track drag span view
     *
     * @param notify true notify to listener.真则通知监听者，假则不通知
     */
    public void notShowSpanView(boolean notify) {
        if (mainSpanIsShow()) {
            mSvThumbnailView.checkThumbnailKeyFrame(mSpanView.getTrackClip(), -1);
            enableThumbnailKeyFrameTag(mSpanView.getTrackClip(), false);
            if (!mEnableThumbnailCoverEdit) {
                enableThumbnailSpeed(mSpanView.getTrackClip(), true);
                setTailViewVisibility(View.VISIBLE);
            }
            mSpanView.setVisibility(INVISIBLE);
            if (mTrackClickListener != null && !mEnableThumbnailCoverEdit && notify) {
                /*from user 是扩展字段，暂时无用
                *The from user is an extension field and is temporarily unavailable.
                * */
                mTrackClickListener.onThumbnailClick(null, false, true);
            }
        }
    }

    /**
     * Show key frame.
     * 显示关键帧
     */
    public void showKeyFrame() {
        if (mainSpanIsShow()) {
            enableThumbnailKeyFrameTag(mSpanView.getTrackClip(), true);
        }
    }

    /**
     * Hide key frame.
     * 隐藏关键帧
     */
    public void hideKeyFrame() {
        if (mainSpanIsShow()) {
            enableThumbnailKeyFrameTag(mSpanView.getTrackClip(), false);
        }
    }

    /**
     * 主轨道拖动把手是否可见
     * Whether the main track drag handle is visible
     */
    public boolean mainSpanIsShow() {
        return mSpanView.getVisibility() == VISIBLE;
    }

    /**
     * 时间戳是否超过主轨道把手范围
     * Whether the timestamp exceeds the main track handle range
     *
     * @param dx    x坐标
     * @param stamp timestamp 时间戳
     */
    public boolean mainSpanOverrun(int dx, long stamp) {
        boolean overrun = true;
        if (mSpanView.getTrackClip() != null) {
            ITrackClip trackClip = mSpanView.getTrackClip();
            overrun = (stamp < trackClip.getInPoint() || stamp > trackClip.getOutPoint());
            if (overrun) {
                /*单纯以时间判断不准确，因为时长与长度之间的转换会有精度丢失，这里再用dx做一下判断
                * Judging solely based on time is not accurate because there may be a loss of accuracy
                * in the conversion between duration and length. Here, we use dx to make a judgment.
                * */
                overrun = dx < durationToLength(trackClip.getInPoint()) || dx > durationToLength(trackClip.getOutPoint());
            }
        }
        return overrun;
    }

    /**
     * 设置是否开启检查主轨道拖动把手越界消失
     * Whether open check main track drag handle out of bounds disappear
     *
     * @param enable true check span view overrun ,false not.真则检查拖动把手是否越界，假则不检查。
     */
    private void setCheckSpanOverrunEnable(boolean enable) {
        mCanCheckOverrun = enable;
    }

    /**
     * 是否开启检查主轨道拖动把手越界消失
     * Whether open check main track drag handle out of bounds disappear
     */
    private boolean checkSpanOverrunEnable() {
        return mCanCheckOverrun;
    }

    /**
     * 设置是否正在剪裁主轨道缩略图
     * Sets whether the main track thumbnail is being clipped
     *
     * @param trimming true is trimming ,false not
     */
    private void setTrimming(boolean trimming) {
        mIsTrimming = trimming;
    }

    /**
     * 设置是否正在剪裁主轨道缩略图
     * whether the main track thumbnail is being clipped
     */
    private boolean isTrimming() {
        return mIsTrimming;
    }

    /**
     * 主轨道被选中的频段
     * Get selected main track clip
     */
    public ITrackClip getMainSelectedClip() {
        return mainSpanIsShow() ? mSpanView.getTrackClip() : null;
    }

    private void selectedThumbnail(ITrackClip trackClip) {
        if (mOperationListener != null && trackClip != null && mSvThumbnailView.getSelectedCoverPosition() != trackClip.getIndexInTrack()) {
            mOperationListener.onSelectedChanged(trackClip);
        }
        mSvThumbnailView.selectedThumbnail(trackClip);
    }

    /**
     * 开启/关闭主轨道封面信息编辑模式。
     * Enable main track mask information edit mode
     */
    public void enableThumbnailCoverEditMode(boolean enable) {
        if (mEnableThumbnailCoverEdit != enable) {
            mEnableThumbnailCoverEdit = enable;
            if (mEnableThumbnailCoverEdit) {
                setTailViewVisibility(View.GONE);
                selectedThumbnail(findMainTrackClip(getCurrentTimestamp()));
            } else {
                setTailViewVisibility(View.VISIBLE);
                selectedThumbnail(null);
            }
        }
    }

    /**
     * 在开启主轨道封面编辑模式，获取到当前被选中的主轨道片段
     * The currently selected clip of the main track is obtained when the main track mask information
     * editing mode is enabled
     */
    public ITrackClip getSelectedThumbnailCover() {
        if (mEnableThumbnailCoverEdit) {
            return mSvThumbnailView.getSelectedThumbnailClip();
        }
        return null;
    }

    /**
     * 设置关键帧标记是否可用,可用会显示，不可用不会显示
     * Enable key frame tag
     *
     * @param enable true will show it,false not.真会显示，假则不显示
     */
    public void enableThumbnailKeyFrameTag(boolean enable) {
        mSvThumbnailView.enableThumbnailAnimation(enable);
    }

    /**
     * 设置片段是否可交换
     * Enable move
     *
     * @param enable true will move,false not.真可以移动，假则不可以
     */
    public void enableThumbnailMove(boolean enable) {
        this.mEnableThumbnailMove = enable;
    }

    /**
     * 设置所给缩略图序列的封面关键帧标记是否可用,可用会显示，不可用不会显示
     * Sets whether the cover key frame tag for the given thumbnail sequence is available
     *
     * @param trackClip the given thumbnail clip 所给的轨道缩略图片段
     * @param enable    enable true will show it,false not.真会显示，假则不显示
     */
    public void enableThumbnailKeyFrameTag(ITrackClip trackClip, boolean enable) {
        mSvThumbnailView.enableThumbnailKeyFrameTag(trackClip, enable);
    }

    /**
     * 设置动画信息是否可用,可用会显示，不可用不会显示
     * Enable speed info
     *
     * @param enable true will show it,false not.真会显示，假则不显示
     */
    public void enableThumbnailAnimation(boolean enable) {
        mSvThumbnailView.enableThumbnailAnimation(enable);
    }

    /**
     * 设置所给缩略图序列的封面动画信息是否可用,可用会显示，不可用不会显示
     * Sets whether the cover speed information for the given thumbnail sequence is available
     *
     * @param trackClip the given thumbnail clip 所给的轨道缩略图片段
     * @param enable    enable true will show it,false not.真会显示，假则不显示
     */
    public void enableThumbnailAnimation(ITrackClip trackClip, boolean enable) {
        mSvThumbnailView.enableThumbnailAnimation(trackClip, enable);
    }

    /**
     * 设置变速信息是否可用，可用会显示，不可用不会显示。
     * Enable speed info
     *
     * @param enable true will show it,false not.真会显示，假则不显示
     */
    public void enableThumbnailSpeed(boolean enable) {
        mSvThumbnailView.enableThumbnailSpeed(enable);
    }

    /**
     * 设置所给缩略图序列的封面变速信息是否可用,可用会显示，不可用不会显示
     * Sets whether the cover speed information for the given thumbnail sequence is available
     *
     * @param trackClip the given thumbnail clip 所给的轨道缩略图片段
     * @param enable    true will show it,false not.真会显示，假则不显示
     */
    public void enableThumbnailSpeed(ITrackClip trackClip, boolean enable) {
        mSvThumbnailView.enableThumbnailSpeed(trackClip, enable);
    }

    /**
     * 更新主轨道所给片段的动画信息
     * Updates the animation information of the clip given by the main track
     *
     * @param trackClip the track clip 轨道缩略图片段
     */
    public void updateThumbnailAnimationInfo(ITrackClip trackClip) {
        mSvThumbnailView.updateThumbnailAnimation(trackClip);
    }

    /**
     * 打开/关闭原声
     * Toggle original voice
     *
     * @param open true open false close.真则打开，假则关闭
     */
    public void toggleOriginalVoice(boolean open) {
        toggleOriginalVoice(open, false);
    }

    /**
     * 原声是否打开
     * Whether the original sound is turned on
     */
    public boolean originalVoiceIsOpen() {
        //根据mIvToggleOriginalVoice.isSelected()判断可能不对。
        // According to mIvToggleOriginalVoice. isSelected(), it may not be correct.
        return openOriginalVoice;
    }

    /**
     * 打开/关闭原声
     * Toggle original voice
     *
     * @param open   true open false close.真则打开，假则关闭
     * @param notify true notify listener ,false not.真则通知监听者，假则不
     */
    private void toggleOriginalVoice(boolean open, boolean notify) {
        mIvToggleOriginalVoice.setSelected(!open);
        if (mIvToggleOriginalVoice.isSelected()) {
            openOriginalVoice = false;
            mTvToggleOriginalVoice.setText(R.string.open_original_voice);
        } else {
            openOriginalVoice = true;
            mTvToggleOriginalVoice.setText(R.string.close_original_voice);
        }
        if (notify && mTrackClickListener != null) {
            mTrackClickListener.toggleOriginalVoice(open);
        }
    }

    /**
     * 获取当前的时间戳
     * Get current timestamp
     */
    public long getCurrentTimestamp() {
        return lengthToDuration(mSvThumbnailView.getScrollX());
    }

    /**
     * 滚动横向操作面板
     * smooth scroll to
     *
     * @param stamp the timestamp 要滚动到的时间戳
     */
    public void smoothScrollTo(long stamp) {
        smoothScrollTo(durationToLength(stamp));
    }

    /**
     * 滚动横向操作面板
     * smooth scroll to
     *
     * @param dx the scroll x 要滚动到的x坐标
     */
    public void smoothScrollTo(int dx) {
        setScrollFromUser(false);
        mSvThumbnailView.smoothScrollTo(dx, mSvThumbnailView.getScrollY());
    }

    /**
     * 以用户滚动操作面板的方式滚动横向操作面板
     * smooth scroll to by user
     *
     * @param dx the scroll x 要滚动到的x坐标
     */
    public void smoothScrollToFromUser(int dx) {
        setScrollFromUser(true);
        mSvThumbnailView.smoothScrollTo(dx, mSvThumbnailView.getScrollY());
    }

    /**
     * 以用户滚动操作面板的方式滚动横向操作面板
     * scroll to by user
     *
     * @param dx the scroll x 要滚动到的x坐标
     */
    public void scrollToFromUser(int dx) {
        setScrollFromUser(true);
        mSvThumbnailView.scrollTo(dx, mSvThumbnailView.getScrollY());
    }

    /**
     * 以用户滚动操作面板的方式滚动横向操作面板
     * scroll to by user
     *
     * @param stamp the timestamp 要滚动到的时间戳
     */
    public void scrollToFromUser(long stamp) {
        scrollToFromUser(durationToLength(stamp));
    }

    /**
     * 滚动横向操作面板
     * scroll to
     *
     * @param dx the scroll x 要滚动到的x坐标
     */
    public void scrollTo(int dx) {
        setScrollFromUser(false);
        mSvThumbnailView.scrollTo(dx, mSvThumbnailView.getScrollY());
    }

    /**
     * 滚动横向操作面板
     * scroll to
     *
     * @param stamp the timestamp 要滚动到的时间戳
     */
    public void scrollTo(long stamp) {
        scrollTo(durationToLength(stamp));
    }

    /**
     * 滚动横向操作面板
     * smooth scroll by
     *
     * @param dx the scroll by x 要滚动到的x坐标
     */
    public void smoothScrollBy(int dx) {
        setScrollFromUser(false);
        mSvThumbnailView.smoothScrollBy(dx, mSvThumbnailView.getScrollY());
    }

    /**
     * 以用户滚动操作面板的方式滚动横向操作面板
     * smooth scroll by by user
     *
     * @param dx the scroll x 要滚动到的x坐标
     */
    public void smoothScrollByFromUser(int dx) {
        setScrollFromUser(true);
        mSvThumbnailView.smoothScrollBy(dx, mSvThumbnailView.getScrollY());
    }

    /**
     * 滚动横向操作面板
     * scroll by
     *
     * @param dx the scroll by x 要滚动到的x坐标
     */
    public void scrollBy(int dx) {
        setScrollFromUser(false);
        mSvThumbnailView.scrollBy(dx, mSvThumbnailView.getScrollY());
    }

    /**
     * 设置是否是用户滚动
     * Set scroll from user
     *
     * @param fromUser true from user ,false not.真则以用户的方式，假则不是
     */
    private void setScrollFromUser(boolean fromUser) {
        mScrollFromUser = fromUser;
    }

    /**
     * 是否是用户滚动
     * Whether or not the user scrolls
     */
    private boolean isScrollFromUser() {
        return mScrollFromUser;
    }

    /**
     * 设置字幕线区域
     * Set caption line region
     *
     * @param region the caption line region 字幕线区域
     */
    public void setCaptionRegion(List<LineRegionClip> region) {
        mEffectLineView.setCaptionRegion(region);
    }

    /**
     * 更新字幕线区域
     * Set caption line region，添加或者删除
     *
     * @param regionClip the caption line region clip 字幕线区域片段
     * @param delete     true delete it ,false add 真则删除，假则添加
     */
    public void updateCaptionRegion(LineRegionClip regionClip, boolean delete) {
        mEffectLineView.updateCaptionRegion(regionClip, delete);
    }

    /**
     * 设置组合字幕线区域
     * Set compound caption line region
     *
     * @param region the compound caption line region 组合字幕线区域
     */
    public void setCompoundCaptionRegion(List<LineRegionClip> region) {
        mEffectLineView.setCompoundCaptionRegion(region);
    }

    /**
     * 更新组合字幕线区域，添加或者删除
     * Set compound caption line region
     *
     * @param regionClip the compound caption line region clip 字幕线区域片段
     * @param delete     true delete it ,false add 真则删除，假则添加
     */
    public void updateCompoundCaptionRegion(LineRegionClip regionClip, boolean delete) {
        mEffectLineView.updateCompoundCaptionRegion(regionClip, delete);
    }

    /**
     * 设置贴纸线区域
     * Set sticker line region
     *
     * @param region the sticker line region 贴纸线区域
     */
    public void setStickerRegion(List<LineRegionClip> region) {
        mEffectLineView.setStickerRegion(region);
    }

    /**
     * 更新贴纸线区域，添加或者删除
     * Set sticker line region
     *
     * @param regionClip the sticker line region clip 贴纸线片段
     * @param delete     true delete it ,false add 真则删除，假则添加
     */
    public void updateStickerRegion(LineRegionClip regionClip, boolean delete) {
        mEffectLineView.updateStickerRegion(regionClip, delete);
    }

    /**
     * 设置画中画图标以及线区域
     * Set PIP icon region
     *
     * @param region the PIP region 画中画区域
     */
    public void setPipRegion(List<LineRegionClip> region) {
        mEffectLineView.setPipRegion(region);
    }

    /**
     * 更新画中画区域。添加或者删除
     * Set sticker line region
     *
     * @param regionClip the pip line region clip 画中画片段
     * @param delete     true delete it ,false add 真则删除，假则添加
     */
    public void updatePipRegion(LineRegionClip regionClip, boolean delete) {
        mEffectLineView.updatePipRegion(regionClip, delete);
    }

    /**
     * 寻找线图区域片段
     * Find region clip
     *
     * @param regionType the region type 类型
     * @param trackIndex the track index 轨道索引
     * @param inPoint    the in point 入点
     */
    public LineRegionClip findRegionClip(int regionType, int trackIndex, long inPoint) {
        return mEffectLineView.findRegionClip(regionType, trackIndex, inPoint);
    }

    /**
     * 设置显示特效线区域
     * Show effect line region
     *
     * @param showPip       true show pip icon ,false not,真则展示画中画图标，假则不展示
     * @param showPipLine   true show pip line,false not,真则展示画中画线区域，假则不展示
     * @param showOtherLine true show other line,false not.真则展示除画中画线外的区域，假则不展示
     */
    public void showEffectRegion(boolean showPip, boolean showPipLine, boolean showOtherLine) {
        mEffectLineView.showPipIcon(showPip);
        mEffectLineView.showPip(showPipLine);
        mEffectLineView.showOther(showOtherLine);
        updateEffectRegion();
    }

    /**
     * 更新主轨道特效线区域
     * Update main track effect line region
     */
    public void updateEffectRegion() {
        mEffectLineView.update();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        removeCallbacks(mCheckScrollStopRunnable);
    }

    private Runnable mCheckScrollStopRunnable = new Runnable() {
        @Override
        public void run() {
            if (isFinishScroll()) {
                handleStop();
            } else {
                postDelayed(this, TIME_DELAY);
            }
        }
    };


    private void handleStop() {
        if (mScrollListener != null) {
            mScrollListener.onScrollStopped();
        }
    }


    private boolean isFinishScroll() {
        boolean isFinish = false;
        Class scrollView = MultiThumbnailSequenceView.class.getSuperclass();
        try {
            if (scrollView != null) {
                Field scrollField = scrollView.getDeclaredField("mScroller");
                scrollField.setAccessible(true);
                Object scroller = scrollField.get(mSvThumbnailView);
                Class<?> overScroller = scrollField.getType();
                Method isFinishedMethod = overScroller.getMethod("isFinished");
                isFinishedMethod.setAccessible(true);
                isFinish = (boolean) isFinishedMethod.invoke(scroller);
            }
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }
        return isFinish;
    }


    public int getThumbnailScrollX() {
        return mSvThumbnailView.getScrollX();
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (!isScrollFromUser()) {
            setScrollFromUser(true);
        }
        int action = ev.getAction();
        if (action == MotionEvent.ACTION_DOWN) {
            if (mScrollListener != null) {
                mScrollListener.onSeekingTimeline(true);
            }
        } else if (action == MotionEvent.ACTION_UP) {
            handleStop();
        }
        return false;
    }

    /**
     * 长度转化成时长，微秒
     * Length convert to duration
     *
     * @param length the length 长度，像素
     */
    public long lengthToDuration(int length) {
        return (long) Math.floor(length / mPixelPerMicrosecond + 0.5D);
    }

    /**
     * 时长转化成长度像素
     * Duration convert to length
     *
     * @param duration the duration 时长.微秒
     */
    public int durationToLength(long duration) {
        return (int) Math.floor(duration * mPixelPerMicrosecond + 0.5D);
    }

    public void toOtherMenu() {
        int newMargin = getResources().getDimensionPixelOffset(R.dimen.multi_thumbnail_sequence_other_margin);
        LayoutParams layoutParams = (LayoutParams) mFlMainTrackParent.getLayoutParams();
        layoutParams.topMargin = newMargin;
        mFlMainTrackParent.setLayoutParams(layoutParams);
    }

    public void toMainMenu() {
        int newMargin = getResources().getDimensionPixelOffset(R.dimen.multi_thumbnail_sequence_main_margin);
        LayoutParams layoutParams = (LayoutParams) mFlMainTrackParent.getLayoutParams();
        if (layoutParams.topMargin != newMargin) {
            layoutParams.topMargin = newMargin;
            mFlMainTrackParent.setLayoutParams(layoutParams);
            showEffectRegion(true, true, true);
            enableThumbnailAnimation(false);
            enableThumbnailCoverEditMode(false);
        }

    }

    @Override
    public void onPixelPerMicrosecondChange(double pixelPerMicrosecond, float scale) {
        if (isScrollFromUser()) {
            //缩放的时候会造成MultiThumbnailView滚动，设置为false
            // Scaling will cause MultiThumbnailView to scroll, set to false.
            setScrollFromUser(false);
        }
        mPixelPerMicrosecond = pixelPerMicrosecond;
        // 更新缩略图 Update the thumbnail.
        mSvThumbnailView.setPixelPerMicrosecond(pixelPerMicrosecond);
        mSpanView.scale(pixelPerMicrosecond);
        mTimeDotAdapter.updateScale(scale, durationToLength(TIME_BASE));
        relocationTimeDotPosition();
        ITrackClip mainSelectedClip = getMainSelectedClip();
        if (mainSelectedClip != null) {
            int difference = mSvThumbnailView.getScrollX() - durationToLength(mainSelectedClip.getInPoint());
            int leftMargin = 0;
            if (difference < mScreenWidth / 2) {
                leftMargin = mScreenWidth / 2 - difference;
                difference = 0;
            } else {
                difference = difference - mScreenWidth / 2 + mSpanView.getHandleWidth();
            }
            //LogUtils.d("difference=" + difference + ",leftMargin=" + leftMargin + ",getScrollX()=" + mSpanView.getScrollX());
            mSpanView.forceScrollTo(difference);
            mSpanView.setMargin(leftMargin);
        }
    }

    /**
     * 重新定位时间点的滚动位置
     * Reposition the scroll position of the time point
     */
    private void relocationTimeDotPosition() {
        mRvTimeDotList.post(new Runnable() {
            @Override
            public void run() {
                mRvTimeDotList.scrollBy(-(getTimeDotDistance() - mSvThumbnailView.getScrollX()), 0);
            }
        });
    }

    /**
     * 获取时间点的滚动距离
     * Gets the scrolling distance of a point in time
     */
    private int getTimeDotDistance() {
        RecyclerView.LayoutManager layoutManager = mRvTimeDotList.getLayoutManager();
        if (layoutManager != null) {
            LinearLayoutManager linearLayoutManager = (LinearLayoutManager) layoutManager;
            int position = linearLayoutManager.findFirstVisibleItemPosition();
            View firstVisibleChildView = layoutManager.findViewByPosition(position);
            if (firstVisibleChildView == null) {
                LogUtils.e("firstVisibleChildView==null");
                return 0;
            }
            int itemWidth = firstVisibleChildView.getWidth();
            if (position > 0) {
                return mTimeDotAdapter.getFirstWidth() + (position - 1) * itemWidth - firstVisibleChildView.getLeft();
            }
            return (position) * itemWidth - firstVisibleChildView.getLeft();
        }
        return 0;
    }

    private float mMoveDownX;
    private float mMainTrackDownX;

    /**
     * 打开主轨道移动编辑
     * Open main track move edit
     *
     * @param downX the down x 按下的x坐标
     */
    private void openMoveEdit(float downX) {
        if (!mEnableThumbnailCoverEdit) {
            mMoveDownX = downX;
            postDelayed(mLongPressRunnable, ViewConfiguration.getLongPressTimeout());
        }
    }

    /**
     * 退出主轨道移动编辑
     * close main track move edit
     */
    private void exitMoveEdit() {
        if (!mEnableThumbnailCoverEdit) {
            mSvThumbnailView.setVisibility(VISIBLE);
            mFlDragThumbnail.setVisibility(INVISIBLE);
            mFlDragThumbnail.selected(-1, -1);
            removeCallbacks(mLongPressRunnable);
        }
    }

    private Runnable mLongPressRunnable = new Runnable() {
        @Override
        public void run() {
            notShowSpanView(true);
            ITrackClip targetClip = mSvThumbnailView.findTargetClip(lengthToDuration(
                    (int) (mMainTrackDownX + mSvThumbnailView.getScrollX() - mScreenWidth / 2)));
            if (targetClip != null) {
                mSvThumbnailView.setVisibility(INVISIBLE);
                mFlDragThumbnail.setVisibility(VISIBLE);
                if (mVibrator != null) {
                    mVibrator.vibrate(50);
                }
                mFlDragThumbnail.selected(targetClip.getIndexInTrack(), mMoveDownX);
            }

        }
    };

    public void setOnScrollListener(OnScrollListener scrollListener) {
        this.mScrollListener = scrollListener;
    }


    public interface OnScrollListener {

        void onScrollStopped();

        void onSeekingTimeline(boolean isSeekTimeline);

    }

    /**
     * 设置操作监听者
     * Set operation listener
     *
     * @param listener the listener
     */
    public void setOperationListener(OperationListener listener) {
        mOperationListener = listener;
    }

    /**
     * 设置主轨道点击监听
     * Set main track click listener
     *
     * @param listener the listener
     */
    public void setMainTrackClickListener(OnTrackClickListener listener) {
        mTrackClickListener = listener;
    }


    /**
     * 设置主轨道缩略图剪裁监听者
     * Set main track thumbnail trim listener
     *
     * @param listener the listener
     */
    public void setThumbnailTrimListener(OnThumbnailTrimListener listener) {
        mThumbnailTrimListener = listener;
    }

    /**
     * Sets on cover click listener.
     * 设置点击封面监听
     * @param listener the listener
     */
    public void setCoverClickListener(OnCoverClickListener listener) {
        this.mOnCoverClickListener = listener;
    }

    private SpanView.OnHandleChangeListener mHandleListener = new SpanView.OnHandleChangeListener() {
        float moveX;
        float remainingX;
        long downDuration;
        boolean isFirstClip;
        boolean isLastClip;

        @Override
        public void onHandleDown(boolean leftHandle) {
            mSpanView.setScrollEnable(false);
            setCheckSpanOverrunEnable(false);
            remainingX = 0;
            moveX = 0;
            isFirstClip = mSpanView.getTrackClip().getInPoint() == 0;
            if (!isFirstClip) {
                isLastClip = mSvThumbnailView.isLastAvailableTrackClip(mSpanView.getTrackClip());
            } else {
                isLastClip = false;
            }
            if (leftHandle) {
                mSvThumbnailView.setIsTrimming(true);
            }
            downDuration = mSpanView.getDuration();
            setScrollFromUser(false);
            if (mThumbnailTrimListener != null) {
                mThumbnailTrimListener.onThumbnailTrimStart(mSpanView.getTrackClip(), leftHandle);
            }
            setTrimming(true);
        }

        @Override
        public void onHandleMove(float dx, boolean leftHandle, boolean moveToLeft, boolean isBorder) {
//            LogUtils.d("dx=" + dx + ",time=" + lengthToDuration((int) dx) + ",leftHandle=" + leftHandle + ",moveToLeft=" + moveToLeft);
            if (dx == 0) {
                return;
            }
            ITrackClip trackClip = mSpanView.getTrackClip();
            //把手拖动的时候更新缩略图 Update thumbnail when dragging the handle.
            if (leftHandle) {
                if (moveToLeft && BaseTrackClip.CLIP_IMAGE.equals(trackClip.getType())) {
                    /*图片禁止往左剪裁
                    * Picture cannot be cropped to the left.
                    * */
                    return;
                }
                if (!isBorder) {
                    moveX = moveX - dx;
                    if (isFirstClip) {
                        mSvThumbnailView.setStartPadding((int) (mScreenWidth / 2 - moveX), false);
                    } else {
                        int scrollBy = (int) (-dx + remainingX);
                        remainingX = -dx + remainingX - scrollBy;
                        scrollBy(scrollBy);
                    }
                    mSpanView.setTranslationX((int) -moveX);
                }
            } else {
                moveX = moveX + dx;
                if (moveToLeft && (isLastClip || isFirstClip)) {
                    /*最后一个片段左移动或者第一个片段移动的时候要增加右内边距，防止往左滑动的时候，滚动造成的视图显示错位
                    * When the last segment moves left or the first segment moves,
                    * increase the right inner margin to prevent view display misalignment caused by scrolling to the left.
                    * */
                    mSvThumbnailView.setEndPadding(mScreenWidth, false);
                }
            }
            mSvThumbnailView.updateThumbnail(trackClip, leftHandle, lengthToDuration((int) dx));
            mSvThumbnailView.enableThumbnailSpeed(trackClip, false);
            updateThumbnailAnimationInfo(trackClip);
            if (mThumbnailTrimListener != null) {
                mThumbnailTrimListener.onThumbnailTrim(trackClip, leftHandle);
            }
        }

        @Override
        public void onHandleUp(boolean leftHandle) {
            //抬起的时候，更新时间点的长度 When lifting, update the length of the time point.
            if (mThumbnailTrimListener != null) {
                mThumbnailTrimListener.onThumbnailTrimComplete(mSpanView.getTrackClip(), leftHandle);
            }
            changeOperationDuration(mSpanView.getDuration() - downDuration, true);
            downDuration = 0;
            setTrimming(false);
            if (moveX != 0) {
                final int scrollX;
                if (leftHandle) {
                    mSvThumbnailView.setIsTrimming(false);
                    if (isFirstClip) {
                        mSvThumbnailView.setStartPadding(mScreenWidth / 2, true);
                    }
                    mSpanView.setTranslationX(0);
                    scrollX = durationToLength(mSpanView.getTrackClip().getInPoint()) + 2;
                } else {
                    scrollX = durationToLength(mSpanView.getTrackClip().getOutPoint()) - 2;
                    if (isLastClip || isFirstClip) {
                        /*还原右内边距
                        * Restore Right Inner Margin.
                        * */
                        mSvThumbnailView.setEndPadding(mScreenWidth / 2, true);
                    }
                }
                mSvThumbnailView.post(new Runnable() {
                    @Override
                    public void run() {
                        scrollToFromUser(scrollX);
                        showSpanView(mSpanView.getTrackClip(), scrollX, false);
                        relocationTimeDotPosition();
                    }
                });
                moveX = 0;
            }
            setCheckSpanOverrunEnable(true);
            mSpanView.setScrollEnable(true);
        }

        @Override
        public void onNeedScroll(float dx, boolean leftHandle, boolean leftBorder) {
            // LogUtils.d("dx=" + dx + ",leftBorder=" + leftBorder);
            if (!leftHandle) {
                mSpanView.setScrollEnable(true);
                smoothScrollByFromUser((int) dx);
            }
        }

    };
    private OnTouchListener mOnTouch = new OnTouchListener() {
        @SuppressLint("ClickableViewAccessibility")
        @Override
        public boolean onTouch(View view, MotionEvent event) {
            if (!mEnableThumbnailMove) {
                return false;
            }
            int action = event.getAction();
            if (mFlDragThumbnail.getVisibility() == VISIBLE) {
                mFlDragThumbnail.onHandleTouchEvent(event);
            }
            if (action == MotionEvent.ACTION_DOWN) {
                mMainTrackDownX = event.getX();
                removeCallbacks(mCheckScrollStopRunnable);
                // LogUtils.d("width=" + getWidth() + ",mrtW=" + mMarkingLineRecyclerView.getWidth() + ",svw=" + mSvThumbnailView.getWidth());
                openMoveEdit(event.getRawX());
            } else if (action == MotionEvent.ACTION_UP) {
                if (mFlDragThumbnail.getVisibility() != VISIBLE && Math.abs(mMainTrackDownX - event.getX()) <= 5) {
//                    LogUtils.d("raxX=" + event.getRawX() + ",X=" + event.getX() + ",scrollX=" + mSvThumbnailView.getScrollX() + ",text=" + lengthToDuration(1080));
                    long time = lengthToDuration((int) (event.getX() + mSvThumbnailView.getScrollX() - mScreenWidth / 2));
                    //找到对应片段 Find the corresponding fragment.
                    final ITrackClip targetClip = mSvThumbnailView.findTargetClip(time);
                    if (targetClip != null && !BaseTrackClip.CLIP_HOLDER.equals(targetClip.getType())) {
                        if (mainSpanIsShow() && mSpanView.isSameClip(targetClip)) {
                            notShowSpanView(true);
                        } else {
                            //获取当前的时间戳 Get the current timestamp.
                            time = getCurrentTimestamp();
                            int scrollX;
                            //先禁用的把手的滚动 The scrolling of the disabled handle first.
                            mSpanView.setScrollEnable(false);
                            //符合条件就滚动到对应的位置,+-2是为了误差容错
                            // If the conditions are met, scroll to the corresponding position, and+-2 is for error tolerance.
                            if (time > targetClip.getOutPoint()) {
                                scrollX = durationToLength(targetClip.getOutPoint()) - 2;
                                scrollToFromUser(scrollX);
                            } else if (time < targetClip.getInPoint()) {
                                scrollX = durationToLength(targetClip.getInPoint()) + 2;
                                scrollToFromUser(scrollX);
                            } else {
                                scrollX = durationToLength(time);
                            }
                            if (!mEnableThumbnailCoverEdit) {
                                showSpanView(targetClip, scrollX, false);
                            }
                            //显示完毕后，再开启滚动 After the display is complete, turn on scrolling again.
                            mSpanView.setScrollEnable(true);
                        }
                        if (mTrackClickListener != null && !mEnableThumbnailCoverEdit) {
                            /*from user 是扩展字段，暂时无用
                            * From user is an extension field and is temporarily unavailable.
                            * */
                            mTrackClickListener.onThumbnailClick(targetClip, mainSpanIsShow(), true);
                        }
                    }
                }
                mMainTrackDownX = 0;
                exitMoveEdit();
                post(mCheckScrollStopRunnable);
            } else if (action == MotionEvent.ACTION_MOVE) {
                if (Math.abs(mMainTrackDownX - event.getX()) > ViewConfiguration.get(getContext()).getScaledTouchSlop()
                        && mFlDragThumbnail.getVisibility() != VISIBLE) {
                    exitMoveEdit();
                }
            } else if (event.getAction() == MotionEvent.ACTION_CANCEL) {
                if (mFlDragThumbnail.getVisibility() != VISIBLE) {
                    exitMoveEdit();
                }
            }
            return mFlDragThumbnail.onHandleTouchEvent(event);
        }
    };

    public interface OnCoverClickListener{
        void onCoverClicked();
    }
}
