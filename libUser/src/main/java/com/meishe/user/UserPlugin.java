package com.meishe.user;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import androidx.fragment.app.Fragment;

import com.meishe.base.utils.CommonUtils;
import com.meishe.draft.data.DraftData;
import com.meishe.engine.EngineNetApi;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.libplugin.user.UserAssetsInfo;
import com.meishe.libplugin.user.UserConstant;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;
import com.meishe.third.pop.core.BasePopupView;
import com.meishe.user.bean.ProjectBatchBean;
import com.meishe.user.view.AssetsUnAvailablePop;
import com.meishe.user.view.LoginPop;
import com.meishe.user.view.LogoutPop;
import com.meishe.user.view.ManageCloudDraftPop;
import com.meishe.user.view.UploadConfirmPop;
import com.meishe.user.view.fragment.CloudCompileFragment;
import com.meishe.user.view.fragment.CloudDraftFragment;
import com.meishe.user.view.fragment.UserEditingFragment;

import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/3/27 13:02
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class UserPlugin implements IUserPlugin {
    private BasePopupView mAssetsUnAvailablePop;

    @Override
    public void showUnAvailablePop(Context context, List<UserAssetsInfo> assetsData, OnEventListener listener) {
        if (mAssetsUnAvailablePop != null) {
            mAssetsUnAvailablePop.dismiss();
        }
        mAssetsUnAvailablePop = AssetsUnAvailablePop.create(context, assetsData, listener).show();
    }

    @Override
    public void showLoginPop(Context context, ILoginCallBack callBack) {
        new LoginPop(context, callBack).show();
    }

    @Override
    public void showLoginOut(Context context, View view, ILoginCallBack callBack) {
        LogoutPop.show(context, view, context.getResources().getString(R.string.user_logout), new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                UserUtil.logOut();
                if (callBack != null) {
                    callBack.onLoginFailed(UserConstant.ResultCode.LOGIN_OUT);
                }
            }
        });
    }

    @Override
    public void showLoginInConfirmPop(Context context, View view, ILoginCallBack callBack) {
        LogoutPop.show(context, view, context.getResources().getString(R.string.user_confirm_login), new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                UserUtil.logOut();
                if (callBack != null) {
                    callBack.onLoginFailed(UserConstant.ResultCode.LOGIN_IN_CONFIRM);
                }
            }
        });
    }

    @Override
    public void showCloudDraftManagePop(Context context, boolean needUpload, IDraftManagerCallBack callBack) {
        ManageCloudDraftPop.create(context, needUpload, new ManageCloudDraftPop.ManageListener() {
            @Override
            public void onDelete() {
                if (callBack != null) {
                    callBack.onDelete();
                }
            }

            @Override
            public void onUpload() {
                if (callBack != null) {
                    callBack.onUpload();
                }
            }
        }).show();
    }

    @Override
    public void login(ILoginCallBack callBack) {
        UserUtil.login(callBack);
    }


    @Override
    public boolean isLogin() {
        return UserUtil.isLogin();
    }

    @Override
    public String getToken() {
        return UserState.get().getToken();
    }

    @Override
    public String getUserId() {
        return UserState.get().getUserID();
    }

    @Override
    public void exitManagerState(Fragment fragment, int layoutId) {
        if (fragment instanceof CloudDraftFragment) {
            ((CloudDraftFragment) fragment).exitManagerState(layoutId);
        }
    }

    @Override
    public void deleteDraft(Fragment fragment) {
        if (fragment instanceof CloudDraftFragment) {
            ((CloudDraftFragment) fragment).deleteDraft();
        }
    }

    @Override
    public void showCloudUploadConfirmPop(Activity activity, OnEventListener listener) {
        UploadConfirmPop.create(activity, listener).show();
    }

    @Override
    public void goToCompilingPage(Fragment fragment) {
        if (fragment instanceof CloudCompileFragment) {
            ((CloudCompileFragment) fragment).goToCompilingPage();
        } else if (fragment instanceof UserEditingFragment) {
            ((UserEditingFragment) fragment).goToCompilingPage();
        }
    }

    @Override
    public void getProjectBatchInfo(List<String> projectList, RequestCallback<List<String>> callback) {
        EngineNetApi.getProjectBatchInfo(getToken(), projectList, new RequestCallback<ProjectBatchBean>() {
            @Override
            public void onSuccess(BaseResponse<ProjectBatchBean> response) {
                if (response != null && response.getData() != null) {
                    ProjectBatchBean data = response.getData();
                    List<ProjectBatchBean.BatchBean> projectBeanList = data.getProjectList();
                    BaseResponse<List<String>> result = new BaseResponse<>();
                    if (!CommonUtils.isEmpty(projectBeanList)) {
                        List<String> resultData = new ArrayList<>();
                        for (ProjectBatchBean.BatchBean batchBean : projectBeanList) {
                            if (batchBean != null) {
                                String id = batchBean.getId();
                                if (!TextUtils.isEmpty(id)) {
                                    resultData.add(id);
                                }
                            }
                        }
                        result.setData(resultData);
                    }
                    if (callback != null) {
                        callback.onSuccess(result);
                    }
                }
            }

            @Override
            public void onError(BaseResponse<ProjectBatchBean> response) {
                if (callback != null) {
                    callback.onError(new BaseResponse<>());
                }
            }
        });
    }

    @Override
    public void upload(Object item, boolean isUpdate) throws InvalidParameterException {
        if (!(item instanceof DraftData)) {
            throw new InvalidParameterException("The item mast be class of com.meishe.draft.data.DraftData !");
        }
        CloudDraftManager.getInstance().uploadDraft((DraftData) item, getToken(), isUpdate);
    }

    @Override
    public Fragment getEditingFragment(int containerLayoutId) {
        return UserEditingFragment.create(containerLayoutId);
    }

    @Override
    public String getMsg(Context context, int type) {
        String msg = "";
        if (!isLogin() && (type == 1 || type == 2)) {
            msg = context.getResources().getString(R.string.user_hint_need_login);
        } else {
            switch (type) {
                case MSG_NO_PURCHASED:
                    msg = context.getResources().getString(R.string.user_hint_no_purchased_assets);
                    break;
                case MSG_NO_CUSTOM:
                    msg = context.getResources().getString(R.string.user_hint_no_custom_assets);
                    break;
                case MSG_NET_ERROR_NEED_REFRESH:
                    msg = context.getResources().getString(R.string.user_hint_assets_net_error_refresh);
                    break;
                case MSG_NET_ERROR_NO_ASSETS:
                    msg = context.getResources().getString(R.string.user_hint_assets_net_error_no_assets);
                    break;
                case MSG_COMMIT_FAILED:
                    msg = context.getResources().getString(R.string.user_hint_commit_failed);
                    break;
                case MSG_COMMIT_SUCCESS:
                    msg = context.getResources().getString(R.string.user_hint_commit_success);
                    break;
                default:
                    break;
            }
        }
        return msg;
    }
}
