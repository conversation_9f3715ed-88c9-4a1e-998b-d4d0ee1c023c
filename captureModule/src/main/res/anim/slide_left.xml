<?xml version="1.0" encoding="utf-8"?><!-- Copyright (C) 2007 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at
  
          http://www.apache.org/licenses/LICENSE-2.0
  
     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
--><!-- android:duration="@android:integer/config_shortAnimTime" -->
<set xmlns:android="http://schemas.android.com/apk/res/android"
    android:interpolator="@android:anim/accelerate_decelerate_interpolator">

    <!--这个让动画中间加速，头尾都是缓慢-->
    <!--@android:anim/accelerate_decelerate_interpolator-->

    <!--从当前屏幕的右边进入  是第二个页面的左边-->
    <translate
        android:duration="300"
        android:fromXDelta="100%p"
        android:toXDelta="0" />
</set>
