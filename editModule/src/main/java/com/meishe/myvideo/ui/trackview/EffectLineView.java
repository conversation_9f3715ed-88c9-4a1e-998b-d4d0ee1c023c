package com.meishe.myvideo.ui.trackview;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.RelativeLayout;

import com.meishe.base.utils.ScreenUtils;
import com.meishe.myvideo.R;
import com.meishe.myvideo.ui.bean.LineRegionClip;
import com.meishe.myvideo.util.PixelPerMicrosecondUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/04/15 14:10
 * @Description :贴纸 字幕 组合字幕 画中画的横线的视图 sticker  caption  compound caption PIP line view
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class EffectLineView extends RelativeLayout {
    private int mStartPadding;
    private int mStickerColor;
    private int mCaptionColor;
    private int mCompoundCaptionColor;
    private int mPipColor;
    private Paint mLinePaint;
    private int mLineWidth = 1;
    private int mLineMargin = 5;
    private int totalHeight = 0;
    private List<LineRegionClip> mCaptionRegion;
    private List<LineRegionClip> mStickerRegion;
    private List<LineRegionClip> mCompoundCaptionRegion;
    private List<LineRegionClip> mPipRegion;
    private int mBitmapWidth;
    private int mBitmapHeight;
    private int mBitmapMargin;
    private int mCaptionLineY;
    private int mCompoundCaptionLineY;
    private int mStickerLineY;
    private int mPipLineY;
    private Bitmap mBackgroundBitmap;
    private int mViewMarginBottom;
    private boolean showPip = true;
    private boolean showPipLine = true;
    private boolean showOtherLine = true;
    private final Rect mIconRect = new Rect();

    private EventListener mListener;

    public EffectLineView(Context context) {
        super(context);
        init(context);
    }

    public EffectLineView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public EffectLineView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        mStartPadding = ScreenUtils.getScreenWidth() / 2;
        mStickerColor = getResources().getColor(R.color.track_background_color_sticker);
        mCaptionColor = getResources().getColor(R.color.track_background_color_caption);
        mCompoundCaptionColor = getResources().getColor(R.color.track_background_color_compound_caption);
        mPipColor = getResources().getColor(R.color.track_background_color_pip);
        mViewMarginBottom = getResources().getDimensionPixelOffset(R.dimen.dp2);
        mLineWidth = getResources().getDimensionPixelOffset(R.dimen.line_view_height);
        mLineMargin = getResources().getDimensionPixelOffset(R.dimen.line_view_margin);
        mBitmapWidth = getResources().getDimensionPixelOffset(R.dimen.line_view_bitmap_width);
        mBitmapHeight = getResources().getDimensionPixelOffset(R.dimen.line_view_bitmap_height);
        mBitmapMargin = getResources().getDimensionPixelOffset(R.dimen.line_view_bitmap_margin);
        mBackgroundBitmap = BitmapFactory.decodeResource(context.getResources(), R.mipmap.lineview_bitmap_cover);
        mLinePaint = new Paint();
        mLinePaint.setAntiAlias(false);
        mLinePaint.setStyle(Paint.Style.FILL);
        mLinePaint.setStrokeWidth(mLineWidth);
        mLinePaint.setStrokeCap(Paint.Cap.ROUND);
    }

    /**
     * 设置字幕线区域
     * Set caption line region
     *
     * @param region the caption line region 字幕线区域
     */
    public void setCaptionRegion(List<LineRegionClip> region) {
        mCaptionRegion = region;
    }

    /**
     * 更新字幕线区域
     * Set caption line region
     *
     * @param regionClip the caption line region clip 字幕线区域片段
     * @param delete     true delete it ,false add 真则删除，假则添加
     */
    public void updateCaptionRegion(LineRegionClip regionClip, boolean delete) {
        if (mCaptionRegion == null) {
            mCaptionRegion = new ArrayList<>();
        }
        if (delete) {
            removeTargetClip(mCaptionRegion, regionClip.getTrackIndex(), regionClip.getInPoint());
        } else {
            mCaptionRegion.add(regionClip);
        }

    }

    /**
     * 设置组合字幕线区域
     * Set compound caption line region
     *
     * @param region the compound caption line region 组合字幕线区域
     */
    public void setCompoundCaptionRegion(List<LineRegionClip> region) {
        mCompoundCaptionRegion = region;
    }

    /**
     * 更新组合字幕线区域
     * Set compound caption line region
     *
     * @param regionClip the compound caption line region clip 字幕线区域片段
     * @param delete     true delete it ,false add 真则删除，假则添加
     */
    public void updateCompoundCaptionRegion(LineRegionClip regionClip, boolean delete) {
        if (mCompoundCaptionRegion == null) {
            mCompoundCaptionRegion = new ArrayList<>();
        }
        if (delete) {
            removeTargetClip(mCompoundCaptionRegion, regionClip.getTrackIndex(), regionClip.getInPoint());
        } else {
            mCompoundCaptionRegion.add(regionClip);
        }
    }

    /**
     * 设置贴纸线区域
     * Set sticker line region
     *
     * @param region the sticker line region 贴纸线区域
     */
    public void setStickerRegion(List<LineRegionClip> region) {
        mStickerRegion = region;
    }

    /**
     * 更新贴纸线区域
     * Set sticker line region
     *
     * @param regionClip the sticker line region clip 贴纸线片段
     * @param delete     true delete it ,false add 真则删除，假则添加
     */
    public void updateStickerRegion(LineRegionClip regionClip, boolean delete) {
        if (mStickerRegion == null) {
            mStickerRegion = new ArrayList<>();
        }
        if (delete) {
            removeTargetClip(mStickerRegion, regionClip.getTrackIndex(), regionClip.getInPoint());
        } else {
            mStickerRegion.add(regionClip);
        }

    }

    /**
     * 设置画中画区域
     * Set PIP icon region
     *
     * @param region the PIP region 画中画区域
     */
    public void setPipRegion(List<LineRegionClip> region) {
        mPipRegion = region;
    }

    /**
     * 更新画中画区域
     * Set sticker line region
     *
     * @param regionClip the pip line region clip 画中画片段
     * @param delete     true delete it ,false add 真则删除，假则添加
     */
    public void updatePipRegion(LineRegionClip regionClip, boolean delete) {
        if (mPipRegion == null) {
            mPipRegion = new ArrayList<>();
        }
        if (delete) {
            removeTargetClip(mPipRegion, regionClip.getTrackIndex(), regionClip.getInPoint());
        } else {
            mPipRegion.add(regionClip);
        }
    }


    /**
     * 删除目标片段
     * Delete target clip
     *
     * @param list       the clip list 区域
     * @param trackIndex the track index 轨道索引
     * @param inPoint    the in point 入点
     */
    private void removeTargetClip(List<LineRegionClip> list, int trackIndex, long inPoint) {
        for (int i = 0; i < list.size(); i++) {
            LineRegionClip clip = list.get(i);
            if (clip.getTrackIndex() == trackIndex && clip.getInPoint() == inPoint) {
                list.remove(i);
                break;
            }
        }
    }

    /**
     * 寻找区域片段
     * Find region clip
     *
     * @param regionType the region type 类型
     * @param trackIndex the track index 轨道索引
     * @param inPoint    the in point 入点
     */
    public LineRegionClip findRegionClip(int regionType, int trackIndex, long inPoint) {
        List<LineRegionClip> list = null;
        switch (regionType) {
            case LineRegionClip.REGION_TYPE_CAPTION:
                list = mCaptionRegion;
                break;
            case LineRegionClip.REGION_TYPE_COMPOUND_CAPTION:
                list = mCompoundCaptionRegion;
                break;
            case LineRegionClip.REGION_TYPE_STICKER:
                list = mStickerRegion;
                break;
            case LineRegionClip.REGION_TYPE_PIP:
                list = mPipRegion;
                break;
            default:
                break;
        }
        if (list != null) {
            for (LineRegionClip clip : list) {
                if (clip.getTrackIndex() == trackIndex && clip.getInPoint() == inPoint) {
                    return clip;
                }
            }
        }
        return null;
    }

    /**
     * 更新，生效必须要调用该方法
     * Update
     */
    public void update() {
        int viewHeight = mLineWidth + mLineMargin;
        totalHeight = 0;
        if (mPipRegion != null && mPipRegion.size() > 0) {
            if (showPip) {
                totalHeight = totalHeight + viewHeight + mBitmapHeight + mBitmapMargin;
            } else {
                totalHeight += viewHeight;
            }
            mPipLineY = totalHeight;
        }
        if (mStickerRegion != null && mStickerRegion.size() > 0) {
            totalHeight += viewHeight;
            mStickerLineY = totalHeight;
        }

        if (mCaptionRegion != null && mCaptionRegion.size() > 0) {
            totalHeight += viewHeight;
            mCaptionLineY = totalHeight;
        }

        if (mCompoundCaptionRegion != null && mCompoundCaptionRegion.size() > 0) {
            totalHeight += viewHeight;
            mCompoundCaptionLineY = totalHeight;
        }
        totalHeight += mViewMarginBottom;
        if (getLayoutParams() == null) {
            post(new Runnable() {
                @Override
                public void run() {
                    setLayoutParam();
                }
            });
        } else {
            setLayoutParam();
        }
        requestLayout();
    }

    /**
     * 是否展示画中画图标
     * Whether to show an PIP icon
     *
     * @param show true show ,false not ,真则展示，假则不展示
     */
    public void showPipIcon(boolean show) {
        showPip = show;
    }

    /**
     * 是否展示画中画线
     * Whether to show an PIP line
     *
     * @param show true show ,false not ,真则展示，假则不展示
     */
    public void showPip(boolean show) {
        showPipLine = show;
    }

    /**
     * 是否展示除画中画外的线
     * Whether to show lines other than PIP
     *
     * @param show true show ,false not ,真则展示，假则不展示
     */
    public void showOther(boolean show) {
        showOtherLine = show;
    }

    private void setLayoutParam() {
        LayoutParams layoutParams = (LayoutParams) getLayoutParams();
        layoutParams.height = totalHeight;
        layoutParams.topMargin = -totalHeight - mViewMarginBottom;
        setLayoutParams(layoutParams);
    }

    public int timeToX(long in) {
        return PixelPerMicrosecondUtil.durationToLength(in) + mStartPadding;
    }

    /**
     * 设置事件监听
     * Set event listener
     *
     * @param listener the listener 监听
     */
    public void setEventListener(EventListener listener) {
        mListener = listener;
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            return true;
        } else if (event.getAction() == MotionEvent.ACTION_UP) {
            dealWithPipClick((int) event.getX() + getScrollX(), (int) event.getY());
        }
        return super.onTouchEvent(event);
    }

    /**
     * 处理画中画的点击
     * Deal with PIP click
     *
     * @param x the x  x坐标
     * @param y the y  y坐标
     */
    private void dealWithPipClick(int x, int y) {
        if (mListener != null && mPipRegion != null && mPipRegion.size() > 0) {
            /*从尾部查询的原因是，不同轨道，同一入点的画中画视图是后边的视图覆盖前边的，点击的时候要
            点击最后边的视图
            The reason for querying from the tail is that the picture-in-picture view of
            the same entry point on different tracks is the view of the rear side covering
            the front side. When clicking click the view on the last side.
            */
            for (int i = mPipRegion.size() - 1; i >= 0; i--) {
                LineRegionClip regionClip = mPipRegion.get(i);
                if (regionClip.getRect() != null && regionClip.getRect().contains(x, y)) {
                    mListener.onPipClick(regionClip.getTrackIndex(), regionClip.getInPoint());
                    break;
                }
            }
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        int leftVisibleBorder = getScrollX();
        int rightVisibleBorder = leftVisibleBorder + getWidth();
        if (mPipRegion != null && mPipRegion.size() > 0) {
            int x1;
            int x2;
            for (LineRegionClip regionClip : mPipRegion) {
                x1 = timeToX(regionClip.getInPoint());
                x2 = timeToX(regionClip.getOutPoint());
                if (regionClip.bitmapAvailable() && showPip) {
                    mIconRect.set(x1 - mBitmapWidth / 2, 0, x1 + mBitmapWidth / 2, mBitmapHeight);
                    regionClip.getRect().set(mIconRect);
                    if (mIconRect.left >= leftVisibleBorder - mBitmapWidth / 2 && mIconRect.right < rightVisibleBorder + mBitmapWidth / 2) {
                        canvas.drawBitmap(regionClip.getBitmap(), null, mIconRect, mLinePaint);
                        canvas.drawBitmap(mBackgroundBitmap, null, mIconRect, mLinePaint);
                    }
                }
                if (showPipLine) {
                    mLinePaint.setColor(mPipColor);
                    canvas.drawLine(x1, mPipLineY, x2, mPipLineY, mLinePaint);
                }
            }
        }
        if (showOtherLine) {
            if (mCompoundCaptionRegion != null && mCompoundCaptionRegion.size() > 0) {
                mLinePaint.setColor(mCompoundCaptionColor);
                drawLines(mCompoundCaptionRegion, mCompoundCaptionLineY, canvas, leftVisibleBorder, rightVisibleBorder);
            }
            if (mStickerRegion != null && mStickerRegion.size() > 0) {
                mLinePaint.setColor(mStickerColor);
                drawLines(mStickerRegion, mStickerLineY, canvas, leftVisibleBorder, rightVisibleBorder);
            }
            if (mCaptionRegion != null && mCaptionRegion.size() > 0) {
                mLinePaint.setColor(mCaptionColor);
                drawLines(mCaptionRegion, mCaptionLineY, canvas, leftVisibleBorder, rightVisibleBorder);
            }
        }
    }

    private void drawLines(List<LineRegionClip> clipList, int y, Canvas canvas, int leftBorder, int rightBorder) {
        int x1;
        int x2;
        for (LineRegionClip regionClip : clipList) {
            x1 = timeToX(regionClip.getInPoint());
            x2 = timeToX(regionClip.getOutPoint());
           /* if (x1 < leftBorder || x2 > rightBorder) {
                continue;
            }*/
            canvas.drawLine(x1, y, x2, y, mLinePaint);
        }
    }

    /**
     * 事件监听
     * Event listener
     */
    public interface EventListener {
        /**
         * 画中画被点击
         *
         * @param trackIndex the track index ,轨道索引
         * @param inPoint    the in point ,入点
         */
        void onPipClick(int trackIndex, long inPoint);
    }
}