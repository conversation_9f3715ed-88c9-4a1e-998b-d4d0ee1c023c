package com.meishe.myvideo.ui.bean;

import com.meishe.engine.bean.KeyFrameProcessor;
import com.meishe.engine.interf.IClip;
import com.meishe.engine.interf.IKeyFrameProcessor;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/6/16 13:54
 * 基础剪辑类
 * Intercepting image class
 */
public abstract class BaseUIClip implements Serializable, Cloneable, IClip, IKeyFrameProcessor {
    private int mTrackIndex;
    private String mType;
    private long mTrimOut;
    private long mTrimIn;
    private String mDisplayName;
    private long inPoint;
    private long outPoint;
    private long duration;
    private double speed = 1.0F;
    private String filePath;
    private String leftChannelDataPath;
    private boolean hasProp;
    private float volume;
    private KeyFrameInfo keyFrameInfo;
    private ThumbNailInfo thumbNailInfo;


    public BaseUIClip(String type, int trackIndex) {
        this.mTrackIndex = trackIndex;
        this.mType = type;
    }

    @Override
    public String getType() {
        return mType;
    }

    @Override
    public double getSpeed() {
        return speed;
    }

    public void setSpeed(double speed) {
        this.speed = speed;
    }

    @Override
    public void setTrimIn(long trimIn) {
        mTrimIn = trimIn;
    }

    @Override
    public void setTrimOut(long trimOut) {
        mTrimOut = trimOut;
    }

    @Override
    public long getTrimOut() {
        return mTrimOut;
    }

    @Override
    public long getTrimIn() {
        return mTrimIn;
    }

    public long getAnimationTrimIn() {
        return INVALID;
    }

    public long getAnimationTrimOut() {
        return INVALID;
    }

    public long getAnimationTrimIn2() {
        return INVALID;
    }

    public long getAnimationTrimOut2() {
        return INVALID;
    }

    @Override
    public int getTrackIndex() {
        return mTrackIndex;
    }

    @Override
    public void setTrackIndex(int trackIndex) {
        mTrackIndex = trackIndex;
    }

    public String getCurveSpeedName() {
        return "";
    }

    @Override
    public long getOriginalDuration() {
        return 0;
    }

    @Override
    public void setOriginalDuration(long duration) {

    }

    @Override
    public void setDisplayName(String name) {
        mDisplayName = name;
    }

    @Override
    public String getDisplayName() {
        return mDisplayName;
    }

    @Override
    public void setRecordArray(float[] data) {

    }

    public void setKeyFrameInfo(KeyFrameInfo keyFrameInfo) {
        this.keyFrameInfo = keyFrameInfo;
    }

    public KeyFrameInfo getKeyFrameInfo() {
        if(keyFrameInfo == null){
            keyFrameInfo = new KeyFrameInfo();
        }
        return keyFrameInfo;
    }

    @Override
    public void setInPoint(long inPoint) {
        this.inPoint = inPoint;
    }

    @Override
    public long getInPoint() {
        return inPoint;
    }

    public long getOutPoint() {
        return outPoint;
    }

    @Override
    public void setOutPoint(long outPoint) {
        this.outPoint = outPoint;
    }

    @Override
    public void setDuration(long duration) {
        this.duration = duration;
    }

    @Override
    public long getDuration() {
        return duration;
    }

    @Override
    public boolean isHasProp() {
        return hasProp;
    }

    public float getVolume() {
        return volume;
    }

    public void setVolume(float volume) {
        this.volume = volume;
    }

    @Override
    public Object clone() {
        try {
            return super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getLeftChannelDataPath() {
        return leftChannelDataPath;
    }

    public void setLeftChannelDataPath(String leftChannelDataPath) {
        this.leftChannelDataPath = leftChannelDataPath;
    }

    public ThumbNailInfo getThumbNailInfo() {
        return thumbNailInfo;
    }

    @Override
    public KeyFrameProcessor keyFrameProcessor() {
        return null;
    }

    public List<String> getTag(){
        return null;
    }
}
