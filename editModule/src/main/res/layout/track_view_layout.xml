<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.meishe.base.view.MYScrollView
        android:id="@+id/track_view_vertical_scroll"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none">

        <LinearLayout
            android:id="@+id/linear_scroll"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal">

            <com.meishe.base.view.MYHorizontalScrollView
                android:id="@+id/track_view_horizontal_scroll"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scrollbars="none">

                <RelativeLayout
                    android:id="@+id/track_view_horizontal_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:id="@+id/ll_add_voice"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/track_view_real_height"
                        android:layout_marginTop="@dimen/track_view_real_margin_top"
                        android:background="@color/add_voice_bg_color"
                        android:orientation="horizontal">
                        <RelativeLayout
                            android:id="@+id/real_audio"
                            android:layout_centerVertical="true"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent" />

                        <ImageView
                            android:id="@+id/image_audio"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@mipmap/bg_add_audio"
                            android:visibility="gone" />

                        <ImageView
                            android:id="@+id/tv_add"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="@dimen/dp10" />

                        <TextView
                            android:id="@+id/tv_music_desc"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="5dp"
                            android:layout_toRightOf="@+id/tv_add"
                            android:text="@string/add_voice"
                            android:textColor="@color/white_8"
                            android:textSize="@dimen/sp10" />
                    </RelativeLayout>

                    <FrameLayout
                        android:id="@+id/track_view_vertical_parent"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>

                </RelativeLayout>
            </com.meishe.base.view.MYHorizontalScrollView>
        </LinearLayout>
    </com.meishe.base.view.MYScrollView>
</merge>