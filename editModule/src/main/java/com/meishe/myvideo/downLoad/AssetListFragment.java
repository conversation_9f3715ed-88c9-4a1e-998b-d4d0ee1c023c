package com.meishe.myvideo.downLoad;

import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;

import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.model.BaseMvpFragment;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.activity.iview.AssetsDownloadView;
import com.meishe.myvideo.activity.presenter.AssetsDownloadPresenter;
import com.meishe.third.adpater.BaseQuickAdapter;

import java.util.List;

import static com.meishe.logic.constant.PagerConstants.ASSET_TYPE;
import static com.meishe.logic.constant.PagerConstants.RATIO;

/**
 * Created by czl on 2018/6/25.
 * 素材下载列表Fragment
 * Download List Fragment
 */
public class AssetListFragment extends BaseMvpFragment<AssetsDownloadPresenter> implements AssetsDownloadView {
    private LinearLayout mLlLoading;
    private SwipeRefreshLayout mSwipeRefreshLayout;
    private RecyclerView mRvAssetList;
    private LinearLayout mLlLoadFailed;
    private Button mBtReload;
    private AssetDownloadListAdapter mAssetListAdapter;
    private int mAssetType = 0;

    public AssetListFragment() {
    }

    @Override
    protected int bindLayout() {
        return R.layout.asset_download_list_fragment;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        mLlLoading = rootView.findViewById(R.id.ll_loading);
        mSwipeRefreshLayout = rootView.findViewById(R.id.swipe_refresh_layout);
        mRvAssetList = rootView.findViewById(R.id.rv_asset_list);
        mLlLoadFailed = rootView.findViewById(R.id.ll_load_failed);
        mBtReload = rootView.findViewById(R.id.bt_reload);
    }


    @Override
    protected void initData() {
        Bundle bundle = getArguments();
        int ratio = AssetInfo.AspectRatio_16v9;
        if (bundle != null) {
            mAssetType = bundle.getInt(ASSET_TYPE);
            ratio = bundle.getInt(RATIO, AssetInfo.AspectRatio_16v9);
        }
        /*
         * 设置刷新控件颜色
         * Set refresh control color
         * */
        mSwipeRefreshLayout.setColorSchemeColors(getResources().getColor(R.color.color_ff4db6ac));
        if (getContext() != null) {
            mAssetListAdapter = new AssetDownloadListAdapter(ratio);
            mRvAssetList.setLayoutManager(new LinearLayoutManagerWrapper(getContext()));
            mRvAssetList.addItemDecoration(new AssetListDecoration(getContext(), AssetListDecoration.VERTICAL_LIST));
            mRvAssetList.setAdapter(mAssetListAdapter);
        }
        mPresenter.refresh(mAssetType);
        initListener();
    }

    private void initListener() {
        /*
         * 下拉刷新
         * Pull down to refresh
         * */
        mSwipeRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                mPresenter.refresh(mAssetType);
            }
        });

        mAssetListAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                LogUtils.d("hasNext="+mPresenter.hasNext());
                if (mPresenter.hasNext()) {
                    mPresenter.loadMore(mAssetType);
                } else {
                    mAssetListAdapter.loadMoreEnd();
                }
            }
        }, mRvAssetList);
        mAssetListAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
            @Override
            public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                AssetInfo item = mAssetListAdapter.getItem(position);
                if (item != null && (!item.isHadDownloaded() || item.needUpdate())) {
                    mPresenter.downloadAsset(item, position);
                }
            }
        });

        mBtReload.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mLlLoadFailed.setVisibility(View.GONE);
                mLlLoading.setVisibility(View.VISIBLE);
                mPresenter.refresh(mAssetType);
            }
        });
    }

    private void closeRefresh() {
        if (mSwipeRefreshLayout != null && mSwipeRefreshLayout.isRefreshing()) {
            mSwipeRefreshLayout.setRefreshing(false);
        }
    }


    @Override
    public void onAssetListBack(List<AssetInfo> assetInfoList, boolean isLoadMore) {
        if (isLoadMore) {
            if (assetInfoList != null) {
                mAssetListAdapter.addData(assetInfoList);
            }
            if (mPresenter.hasNext()) {
                mAssetListAdapter.loadMoreComplete();
            } else {
                mAssetListAdapter.loadMoreEnd();
            }
        } else {
            mLlLoading.setVisibility(View.GONE);
            mLlLoadFailed.setVisibility(View.GONE);
            mAssetListAdapter.setNewData(assetInfoList);
        }
        closeRefresh();
    }

    @Override
    public void onLoadAssetFailed(boolean isLoadMore) {
        if (!isLoadMore) {
            mLlLoadFailed.setVisibility(View.VISIBLE);
            mLlLoading.setVisibility(View.GONE);
        }else{
            if (mPresenter.hasNext()) {
                mAssetListAdapter.loadMoreComplete();
            } else {
                mAssetListAdapter.loadMoreEnd();
            }
        }
    }

    @Override
    public void onDownloadUpdate(int position) {
        mAssetListAdapter.notifyItemChanged(position);
    }

}
