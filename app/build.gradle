// 定义一个变量控制是否为应用模式
def isApplication = project.hasProperty('isApp') ? project.isApp.toBoolean() : false

// 根据变量应用不同的插件
if (isApplication) {
    apply plugin: 'com.android.application'
} else {
    apply plugin: 'com.android.library'
}

android {
    compileSdkVersion rootProject.android.extCompileSdkVersion
    buildToolsVersion rootProject.android.extBuildToolsVersion

    defaultConfig {
        // 仅在应用模式下设置applicationId
        if (isApplication) {
            applicationId "com.meishe.myvideoapp"
        }

        minSdkVersion rootProject.android.extMinSdkVersion
        targetSdkVersion rootProject.android.extTargetSdkVersion
        versionCode rootProject.config.extVersionCode
        versionName rootProject.config.extVersionName
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        flavorDimensions "default"
        ndk {
            abiFilters "armeabi-v7a","arm64-v8a"  // 指定要ndk需要兼容的架构(这样其他依赖包里mips,x86,armeabi,arm-v8之类的so会被过滤掉)
        }
        multiDexEnabled true
        consumerProguardFiles 'proguard-rules.pro'
    }
    compileOptions {
        targetCompatibility JavaVersion.VERSION_1_8
        sourceCompatibility JavaVersion.VERSION_1_8
    }
    signingConfigs {
        debug {
        }
    }

    buildTypes {
        release {
            buildConfigField "String", "HAS_LOG", "\"true\""
            minifyEnabled true
            matchingFallbacks = ['release']
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        release_no_log {
            buildConfigField "String", "HAS_LOG", "\"false\""
            minifyEnabled true
            matchingFallbacks = ['release']
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        debug {
            buildConfigField "String", "HAS_LOG", "\"true\""
            minifyEnabled false
            signingConfig signingConfigs.debug
            matchingFallbacks = ['debug']
            debuggable true
        }
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    // 根据模式选择不同的源集
    sourceSets {
        main {
            if (!isApplication) {
                manifest.srcFile 'src/main/debug/AndroidManifest.xml'
            } else {
                manifest.srcFile 'src/main/AndroidManifest.xml'
            }
        }
    }
}


dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    testImplementation rootProject.ext.dependencies.extTestJunit
    androidTestImplementation rootProject.ext.dependencies.extAndroidTestRunner
    androidTestImplementation rootProject.ext.dependencies.extTestEspresso
    implementation rootProject.ext.dependencies.extConstraintLayout
    implementation rootProject.ext.dependencies.extSupportDesign
    implementation rootProject.ext.dependencies.extSwiperefreshlayout
    implementation project(path: ':libEngine')
    implementation project(path: ':cutsameModel')
    implementation project(path: ':editModule')
    implementation project(path: ':captureModule')
    implementation project(path: ':libUser')

    //下边的引用会逐步淘汰
    implementation 'org.greenrobot:eventbus:3.2.0'
    implementation 'com.facebook.fresco:fresco:1.12.0'
    implementation 'com.facebook.fresco:animated-gif:1.12.0'
    implementation 'com.facebook.fresco:animated-webp:1.12.0'
    implementation 'com.facebook.fresco:webpsupport:1.12.0'

}
