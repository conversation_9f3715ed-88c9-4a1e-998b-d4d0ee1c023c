<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="TranslucentFullScreenTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@color/black</item>
    </style>

    <style name="dialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <!--边框-->
        <item name="android:windowIsFloating">true</item>
        <!--是否浮现在activity之上-->
        <item name="android:windowIsTranslucent">true</item>
        <!--半透明-->
        <item name="android:windowNoTitle">true</item>
        <!--无标题-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--背景透明-->
        <item name="android:backgroundDimEnabled">true</item>
        <!--模糊-->

    </style>

    <style name="tab_text_12">
        <item name="android:textSize">@dimen/sp_px_36</item>
    </style>

    <!--设置tabLayout字体大小-->
    <style name="cut_tab_layout_style">
        <item name="android:textSize">11sp</item>
    </style>
    <style name="FullScreenTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@color/colorTranslucent</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <declare-styleable name="RectSelectView">
        <attr name="innerPadding" format="dimension" />
    </declare-styleable>

    <style name="TimePickerDialog" parent="android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowBackground">@color/white</item>
    </style>
</resources>
