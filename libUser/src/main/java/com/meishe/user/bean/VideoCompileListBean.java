package com.meishe.user.bean;

import java.io.Serializable;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/7/20 13:10
 * @Description :视频合成list bean The video compile list bean.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class VideoCompileListBean implements Serializable {
   /**
    * 成片数量
    * The video compile count
    */
   private int videoCompiledCount;

   private List<VideoCompileBean> videoCompiledList;

   public int getVideoCompiledCount() {
      return videoCompiledCount;
   }

   public void setVideoCompiledCount(int videoCompiledCount) {
      this.videoCompiledCount = videoCompiledCount;
   }

   public List<VideoCompileBean> getVideoCompiledList() {
      return videoCompiledList;
   }

   public void setVideoCompiledList(List<VideoCompileBean> videoCompiledList) {
      this.videoCompiledList = videoCompiledList;
   }
}
