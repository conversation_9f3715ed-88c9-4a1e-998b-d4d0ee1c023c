<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_px_784">



    <TextView
        android:id="@+id/tv_tips"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_132"
        android:layout_centerHorizontal="true"
        android:gravity="center_vertical"
        android:text="@string/change_caption_content"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_30"
        tools:ignore="RelativeOverlap" />

    <ImageView
        android:id="@+id/iv_confirm"
        android:layout_width="@dimen/dp_px_75"
        android:layout_height="@dimen/dp_px_75"
        android:layout_alignTop="@id/tv_tips"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="@dimen/dp_px_28"
        android:layout_marginEnd="@dimen/dp_px_45"
        android:background="@mipmap/ic_confirm"
        android:contentDescription="@null" />


    <View
        android:id="@+id/v_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_1"
        android:layout_below="@+id/tv_tips"
        android:background="@color/menu_divide_color" />

    <com.meishe.base.view.PullToRefreshAndPushToLoadView
        android:id="@+id/ptl_recyclerView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/ttv_tab_type"
        android:layout_below="@+id/v_line"
        android:layout_centerHorizontal="true">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/tv_hint"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:textColor="@color/color_ff808080"
                android:visibility="gone" />
        </FrameLayout>
    </com.meishe.base.view.PullToRefreshAndPushToLoadView>


    <com.meishe.myvideo.view.AssetsTypeTabView
        android:id="@+id/ttv_tab_type"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_102"
        android:layout_alignParentBottom="true" />


</RelativeLayout>