package com.czc.cutsame.fragment.presenter;

import static com.meicam.sdk.NvsCaption.BOUNDING_TYPE_TEXT;

import android.graphics.Bitmap;
import android.graphics.PointF;
import android.text.TextUtils;
import android.util.Log;

import com.czc.cutsame.bean.TemplateClip;
import com.czc.cutsame.fragment.iview.CutEditorVpView;
import com.czc.cutsame.util.NvTemplateDataAdjustTool;
import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimelineCompoundCaption;
import com.meicam.sdk.NvsVideoClip;
import com.meishe.base.bean.MediaData;
import com.meishe.base.model.Presenter;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ThreadUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.engine.bean.MeicamCompoundCaptionClip;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.template.MeicamNvsTemplateFootageCorrespondingClipInfo;
import com.meishe.engine.bean.template.TemplateCaptionDesc;
import com.meishe.engine.editor.EditorController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 通用模板中编辑页面viewpager的业务类
 * <p></p>
 * Edit page viewPager business class in the CutSameEditorActivity
 */
public class CutEditorVpPresenter extends Presenter<CutEditorVpView> {
    private static final String TAG = "CutEditorVpPresenter";
    /**
     * 获取模板中的视频信息的key
     * <p></p>
     * the key for the video information in the template
     */
    public static final String VIDEO = "VIDEO";
    /**
     * 获取模板中的字幕信息的key
     * <p></p>
     * the key for the caption information in the template
     */
    public static final String CAPTION = "caption";

    /**
     * 根据模板ID和需要的数据类型，从模板中获取数据
     * <p></p>
     * Get data from the template based on the template ID and the type of data you want
     *
     * @param type       the type 类型
     * @param templateId the template id 模板编号
     */
    public void onDataReady(String type, String templateId) {
        if (TextUtils.equals(type, VIDEO)) {
            if (getView() != null) {
                getView().getVideoData(EditorController.getInstance().getTemplateVideoClip(templateId));
            }
        } else if (TextUtils.equals(type, CAPTION)) {
            if (getView() != null) {
                List<TemplateCaptionDesc> templateCaptionDescList = getTemplateAllCaptionDescs(templateId);
                getView().getCaptionData(templateCaptionDescList);
            }
        }
    }

    private List<TemplateCaptionDesc> getTemplateAllCaptionDescs(String templateId) {
        /*
         *普通字幕数据
         * Common caption data.
         */
        List<TemplateCaptionDesc> templateCaptionDescList = EditorController.getInstance().getTemplateCaptions(templateId);

        /*
         *组合字幕数据
         * Compound caption data.
         */
        List<TemplateCaptionDesc> nvsTemplateCompoundCaptionDescs =
                EditorController.getInstance().getTemplateCompoundCaptions(templateId);
        if (nvsTemplateCompoundCaptionDescs != null && nvsTemplateCompoundCaptionDescs.size() != 0) {
            templateCaptionDescList.addAll(nvsTemplateCompoundCaptionDescs);
        }
        Collections.sort(templateCaptionDescList);
        return templateCaptionDescList;
    }

    /**
     * 获取模板中的字幕对应的位图信息
     * <p></p>
     * Gets the bitmap of the template caption.
     *
     * @param captionDescList the template caption list
     */
    public void getCaptionBitmap(final List<TemplateCaptionDesc> captionDescList) {
        if (CommonUtils.isEmpty(captionDescList)) {
            return;
        }
        final int[] currentIndex = {0};
        EditorController.getInstance().grabBitmapFromAuxiliaryTimelineAsync(captionDescList.get(currentIndex[0]).getInPoint(),
                new NvsStreamingContext.ImageGrabberCallback() {
                    NvsStreamingContext.ImageGrabberCallback callback = this;

                    @Override
                    public void onImageGrabbedArrived(final Bitmap bitmap, final long l) {
                        ThreadUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                if (CommonUtils.isIndexAvailable(currentIndex[0], captionDescList)) {
                                    captionDescList.get(currentIndex[0]).setBitmap(bitmap);
                                    if (getView() != null) {
                                        getView().getCaptionBitmap(currentIndex[0]);
                                    }
                                }
                                currentIndex[0]++;
                                if (currentIndex[0] < captionDescList.size()) {
                                    EditorController.getInstance().grabBitmapFromAuxiliaryTimelineAsync(captionDescList.get(currentIndex[0]).getInPoint(),
                                            callback);
                                }
                            }
                        });
                    }
                });
    }

    /**
     * 修改模板中对应footage下所有视频片段的新入点
     * <p></p>
     * Change the new trim point for all video clip in footage in the template
     *
     * @param newTrim          the new trim 新的裁剪位置
     * @param selectedClipInfo 选中的片段
     */
    public void changClipTrim(long newTrim, MeicamNvsTemplateFootageCorrespondingClipInfo selectedClipInfo) {
        if (selectedClipInfo == null) {
            return;
        }
        MeicamVideoClip clip = EditorController.getInstance().getVideoClipByTemplateFootageCorrespondingClipInfo(
                selectedClipInfo);
        if (clip == null) {
            return;
        }
        long oldTrimIn = clip.getTrimIn();
        clip.moveTrimPoint(newTrim - oldTrimIn);
        EditorController.getInstance().seekTimeline(0);
        if (getView() != null) {
            getView().needSeekPosition(0, null, 1);
        }
    }

    /**
     * seek到字幕的起点位置
     * <p></p>
     * Seek to caption start time.
     *
     * @param nvsTemplateCaptionDesc the nvs template caption desc 模板中的字幕信息
     */
    public void seekToCaptionStartTime(TemplateCaptionDesc nvsTemplateCaptionDesc) {
        if (nvsTemplateCaptionDesc.isCaption()) {
            MeicamCaptionClip nvsTimelineCaption = EditorController.getInstance().getCaptionByTemplateCaptionDesc(nvsTemplateCaptionDesc);
            if (nvsTimelineCaption == null) {
                Log.e(TAG, "seekToCaptionStartTime: nvsTimelineCaption is NULL! " + nvsTemplateCaptionDesc.replaceId);
                return;
            }
            EditorController.getInstance().seekTimeline(nvsTemplateCaptionDesc.getInPoint(), NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER);
            if (getView() != null) {
                List<PointF> captionBoundingVertices = nvsTimelineCaption.getCaptionBoundingVertices(BOUNDING_TYPE_TEXT);
                if (!CommonUtils.isEmpty(captionBoundingVertices)) {
                    float[] translationInOutTimeline = nvsTimelineCaption.getTranslationInOutTimeline();
                    if (translationInOutTimeline != null && translationInOutTimeline.length > 0) {
                        for (PointF captionBoundingVertex : captionBoundingVertices) {
                            captionBoundingVertex.x += translationInOutTimeline[0];
                            captionBoundingVertex.y += translationInOutTimeline[1];
                        }
                    }
                }
                getView().needSeekPosition(nvsTemplateCaptionDesc.getInPoint(), captionBoundingVertices, nvsTimelineCaption.getScaleInOutTimeline());
            }
        } else if (nvsTemplateCaptionDesc.isCompoundCaption()) {
            MeicamCompoundCaptionClip compoundCaptionClip = EditorController.getInstance().getCompCaptionByTemplateCaptionDesc
                    (nvsTemplateCaptionDesc);
            if (compoundCaptionClip == null) {
                Log.e(TAG, "seekToCaptionStartTime: nvsTimelineCaption is NULL! " + nvsTemplateCaptionDesc.replaceId);
                return;
            }
            EditorController.getInstance().seekTimeline(nvsTemplateCaptionDesc.getInPoint(), NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER);
            if (getView() != null) {
                List<List<PointF>> childPointList = new ArrayList<>();
                getView().needSeekCompoundPosition(nvsTemplateCaptionDesc.getInPoint(), getCaptionCoordinates(compoundCaptionClip, childPointList), childPointList, 1);
            }
        }

    }

    /**
     * 获取组合字幕坐标
     * Get compound caption coordinates
     *
     * @param caption        the caption
     * @param childPointList the caption child point list
     */
    private List<PointF> getCaptionCoordinates(MeicamCompoundCaptionClip
                                                       caption, List<List<PointF>> childPointList) {
        List<PointF> transformPointList = caption.getCompoundBoundingVertices(NvsTimelineCompoundCaption.BOUNDING_TYPE_FRAME);
        if (transformPointList == null || transformPointList.size() < 4) {
            return null;
        }
        for (int index = 0; index < caption.getCaptionItemCount(); index++) {
            List<PointF> subList = caption.getCaptionBoundingVertices(index, NvsTimelineCompoundCaption.BOUNDING_TYPE_TEXT);
            if (subList == null || subList.size() < 4) {
                continue;
            }
            childPointList.add(subList);
        }
        return transformPointList;
    }


    /**
     * 获取字幕的边框位置
     * Gets the border point of the caption
     *
     * @param nvsTemplateCaptionDesc the template caption desc 模板字幕描述信息
     * @return the caption point list 字幕的点位信息
     */
    public List<PointF> getCaptionPointList(TemplateCaptionDesc nvsTemplateCaptionDesc) {
        if (nvsTemplateCaptionDesc == null) {
            return null;
        }
        MeicamCaptionClip caption = EditorController.getInstance().getCaptionByTemplateCaptionDesc(nvsTemplateCaptionDesc);
        if (caption == null) {
            return null;
        }
        List<PointF> boundingVertices = caption.getCaptionBoundingVertices(BOUNDING_TYPE_TEXT);
        float[] translationInOutTimeline = caption.getTranslationInOutTimeline();
        if (!CommonUtils.isEmpty(boundingVertices)) {
            for (PointF point : boundingVertices) {
                point.x += translationInOutTimeline[0];
                point.y += translationInOutTimeline[1];
            }
        }
        return boundingVertices;
    }

    public float getCaptionScaleValue(TemplateCaptionDesc nvsTemplateCaptionDesc) {
        if (nvsTemplateCaptionDesc == null) {
            return 1;
        }
        MeicamCaptionClip caption = EditorController.getInstance().getCaptionByTemplateCaptionDesc(nvsTemplateCaptionDesc);
        if (caption == null) {
            return 1;
        }
        return caption.getScaleInOutTimeline();
    }


    /**
     * 获取组合字幕的边框坐标信息
     * Gets compound caption point list.
     *
     * @param nvsTemplateCaptionDesc the template caption desc 模板字幕描述信息
     * @param childPointList         the child point list 子字幕的点位容器
     * @return the compound caption point list 组合字幕的点位信息
     */
    public List<PointF> getCompoundCaptionPointList(TemplateCaptionDesc nvsTemplateCaptionDesc, List<List<PointF>> childPointList) {
        if (nvsTemplateCaptionDesc == null) {
            return null;
        }
        MeicamCompoundCaptionClip caption = EditorController.getInstance().getCompCaptionByTemplateCaptionDesc
                (nvsTemplateCaptionDesc);
        if (caption == null) {
            return null;
        }
        return getCaptionCoordinates(caption, childPointList);
    }

    /**
     * 处理视频片段的替换
     * Handle the replacement of video clips
     *
     * @param templateClip the templateClip
     */
    public void dealVideoReplace(TemplateClip templateClip) {
        if (templateClip == null) {
            return;
        }
        MeicamTimeline nvsTimeline = EditorController.getInstance().getNvsTimeline();
        if (nvsTimeline == null) {
            LogUtils.e("timeline is null!");
            return;
        }
        MeicamVideoClip nvsVideoClip = EditorController.getInstance().getVideoClipByTemplateFootageCorrespondingClipInfo(
                null, templateClip.getClipIndexInTimelineList(), templateClip.getClipTrackIndexInTimelineList(),
                templateClip.getTrackIndex(), templateClip.getClipIndex());
        NvTemplateDataAdjustTool.adjustVideoClipData(EditorEngine.getInstance().getStreamingContext(), nvsTimeline, nvsVideoClip, true);
        if (templateClip.getMediaType() == MediaData.TYPE_VIDEO && templateClip.getNeedReverse()
                && (!TextUtils.isEmpty(templateClip.getReversePath()))) {
            nvsVideoClip.changeFilePath(templateClip.getReversePath());
        } else {
            nvsVideoClip.changeFilePath(templateClip.getFilePath());
        }

        if (nvsVideoClip.getNvsVideoType() == NvsVideoClip.VIDEO_CLIP_TYPE_AV && nvsVideoClip.getTrimIn() > 0) {
            NvsStreamingContext streamingContext = EditorController.getInstance().getStreamingContext();
            if (streamingContext != null) {
                NvsAVFileInfo avFileInfo = streamingContext.getAVFileInfo(nvsVideoClip.getFilePath());
                if (avFileInfo != null) {
                    // 替换视频的时候，如果trim过大，会有静帧现象，这里调整trimIn和trimOut，每次归零
                    // When replacing a video, if the trim is too large, there will be still frames.
                    // Here, adjust trimIn and trimOut to zero each time.
                    nvsVideoClip.moveTrimPoint(-nvsVideoClip.getTrimIn());
                }
            }
        }
        NvTemplateDataAdjustTool.adjustVideoClipData(EditorEngine.getInstance().getStreamingContext(), nvsTimeline, nvsVideoClip, false);
        EditorController.getInstance().seekTimeline();
    }
}
