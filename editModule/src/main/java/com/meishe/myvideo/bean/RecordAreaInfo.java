package com.meishe.myvideo.bean;

import android.view.View;

/**
 * The type Record area info.
 * 此类为记录区域信息
 */
public class RecordAreaInfo {

    private long inPoint;
    private long outPoint;
    private int inPosition;
    private View areaView;
    private View leftHandle;
    private View rightHandle;

    @Override
    public RecordAreaInfo clone() {
        RecordAreaInfo cloneInfo = new RecordAreaInfo();
        cloneInfo.setInPoint(this.getInPoint());
        cloneInfo.setOutPoint(this.getOutPoint());
        cloneInfo.setInPosition(this.getInPosition());
        cloneInfo.setAreaView(this.getAreaView());
        cloneInfo.setLeftHandle(this.getLeftHandle());
        cloneInfo.setRightHandle(this.getRightHandle());
        return cloneInfo;
    }

    /**
     * Gets in point.
     * 获取点
     *
     * @return the in point
     */
    public long getInPoint() {
        return inPoint;
    }

    /**
     * Sets in point.
     * 设置点
     * @param inPoint the in point
     */
    public void setInPoint(long inPoint) {
        this.inPoint = inPoint;
    }

    /**
     * Gets out point.
     * 获取结束点
     * @return the out point
     */
    public long getOutPoint() {
        return outPoint;
    }

    /**
     * Sets out point.
     * 设置结束点
     * @param outPoint the out point
     */
    public void setOutPoint(long outPoint) {
        this.outPoint = outPoint;
    }

    /**
     * Gets in position.
     * 获取位置
     * @return the in position
     */
    public int getInPosition() {
        return inPosition;
    }

    /**
     * Sets in position.
     * 设置位置
     * @param inPosition the in position
     */
    public void setInPosition(int inPosition) {
        this.inPosition = inPosition;
    }

    /**
     * Gets area view.
     * 获取全景摄像
     * @return the area view
     */
    public View getAreaView() {
        return areaView;
    }

    /**
     * Sets area view.
     * 设置全景摄像
     * @param areaView the area view
     */
    public void setAreaView(View areaView) {
        this.areaView = areaView;
    }

    /**
     * Gets left handle.
     * 获取左边手柄
     * @return the left handle
     */
    public View getLeftHandle() {
        return leftHandle;
    }

    /**
     * Sets left handle.
     * 设置左边手柄
     * @param leftHandle the left handle
     */
    public void setLeftHandle(View leftHandle) {
        this.leftHandle = leftHandle;
    }

    /**
     * Gets right handle.
     * 获取右边手柄
     * @return the right handle
     */
    public View getRightHandle() {
        return rightHandle;
    }

    /**
     * Sets right handle.
     * 设置右边手柄
     * @param rightHandle the right handle
     */
    public void setRightHandle(View rightHandle) {
        this.rightHandle = rightHandle;
    }

}
