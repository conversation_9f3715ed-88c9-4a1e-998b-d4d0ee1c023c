<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_media_list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/tv_next"
        android:layout_marginBottom="@dimen/dp_px_20"/>

    <!-- 下一步按钮 -->
    <TextView
        android:id="@+id/tv_next"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_113"
        android:layout_alignParentBottom="true"
        android:layout_margin="@dimen/dp_px_45"
        android:background="@drawable/bg_round_corners_solid_red_fc"
        android:gravity="center"
        android:text="@string/start_marking"
        android:textColor="@color/start_edit_text"
        android:textSize="@dimen/sp_px_48"
        android:visibility="gone" />

</RelativeLayout>