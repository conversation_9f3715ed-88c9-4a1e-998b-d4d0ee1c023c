package com.meishe.myvideo.bean;

import com.meishe.engine.bean.BaseInfo;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate :2021/1/5 16:19
 * @Description : 字体实体类 The caption font info
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CaptionFontInfo extends BaseInfo {
    private String assetPath;

    private String fontFamily = "";

    private String enName;

    @Override
    public String getAssetPath() {
        return assetPath;
    }

    @Override
    public void setAssetPath(String assetPath) {
        this.assetPath = assetPath;
    }

    public String getFontFamily() {
        return fontFamily;
    }

    public void setFontFamily(String fontFamily) {
        this.fontFamily = fontFamily;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }
}
