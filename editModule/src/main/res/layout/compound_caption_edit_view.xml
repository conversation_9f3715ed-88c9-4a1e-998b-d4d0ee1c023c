<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_px_660">


    <EditText
        android:id="@+id/et_caption_input"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_105"
        android:layout_marginLeft="@dimen/dp_px_39"
        android:layout_marginTop="@dimen/dp_px_45"
        android:layout_marginRight="@dimen/dp_px_39"
        android:background="@drawable/editor_caption_input_corner"
        android:importantForAutofill="no"
        android:inputType="text"
        android:paddingLeft="@dimen/dp_px_15"
        android:paddingRight="@dimen/dp_px_15"
        android:textColor="@color/white_8"
        android:textCursorDrawable="@drawable/editor_caption_input_cursor"
        android:textSize="@dimen/sp_px_48"
        tools:ignore="LabelFor" />


    <ImageView
        android:id="@+id/iv_cancel"
        android:layout_width="@dimen/dp_px_132"
        android:layout_height="@dimen/dp_px_132"
        android:layout_below="@+id/et_caption_input"
        android:layout_marginStart="@dimen/dp_px_45"
        android:layout_marginLeft="@dimen/dp_px_45"
        android:contentDescription="@null"
        android:padding="@dimen/dp_px_42"
        android:src="@mipmap/ic_cancel" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_132"
        android:layout_below="@+id/et_caption_input"
        android:layout_marginStart="@dimen/dp_px_72"
        android:layout_marginLeft="@dimen/dp_px_72"
        android:layout_toEndOf="@+id/iv_cancel"
        android:layout_toRightOf="@+id/iv_cancel"
        android:gravity="center_vertical"
        android:text="@string/compound_caption_title_style"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_42" />

    <ImageView
        android:id="@+id/iv_confirm"
        android:layout_width="@dimen/dp_px_132"
        android:layout_height="@dimen/dp_px_132"
        android:layout_below="@+id/et_caption_input"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_marginEnd="@dimen/dp_px_45"
        android:layout_marginRight="@dimen/dp_px_45"
        android:contentDescription="@null"
        android:padding="@dimen/dp_px_28"
        android:src="@mipmap/ic_record_confirm" />

    <View
        android:id="@+id/menu_divide"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_1"
        android:layout_below="@+id/iv_cancel"
        android:background="@color/menu_divide_color" />

    <LinearLayout
        android:id="@+id/ll_font_and_color_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/menu_divide"
        android:background="@color/black"
        android:orientation="vertical">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_font_list"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_px_90"
            android:layout_marginStart="@dimen/dp_px_36"
            android:layout_marginLeft="@dimen/dp_px_36"
            android:layout_marginTop="@dimen/dp_px_66"
            android:layout_marginEnd="@dimen/dp_px_36"
            android:layout_marginRight="@dimen/dp_px_36" />

        <com.meishe.myvideo.view.MYMultiColorView
            android:id="@+id/multi_color_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_px_36"
            android:layout_marginLeft="@dimen/dp_px_36"
            android:layout_marginTop="@dimen/dp_px_60" />


    </LinearLayout>

</merge>