package com.czc.cutsame.fragment.adapter;

import android.net.Uri;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.czc.cutsame.R;
import com.czc.cutsame.bean.Template;
import com.czc.cutsame.util.ConfigUtil;
import com.meishe.base.utils.FormatUtils;
import com.meishe.base.utils.ImageLoader;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.Utils;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;


/**
 * author : lhz
 * date   : 2020/11/4
 * desc   :模板列表适配器,注意因为效果图仅仅是图片大小比例不同，所以用了BaseQuickAdapter，没有用
 * BaseMultiItemQuickAdapter
 * Template list adapter, note that since renderings are only image size and scale differences, using the BaseQuickAdapter does not work
 * BaseMultiItemQuickAdapter
 */
public class TemplateAdapter extends BaseQuickAdapter<Template, BaseViewHolder> {
    private int mItemWidth;
    private ImageLoader.Options mCircleOptions;
    private ImageLoader.Options mRoundCornerOptions;
    private boolean mNeedShowPurchased;

    public TemplateAdapter(int itemWidth) {
        super(R.layout.item_template);
        this.mItemWidth = itemWidth;
        mCircleOptions = new ImageLoader.Options().circleCrop();
        mRoundCornerOptions = new ImageLoader.Options().fitCenter();
    }


    public void setNeedShowPurchased(boolean needShowPurchased) {
        this.mNeedShowPurchased = needShowPurchased;
    }

    /**
     * Convert.
     * 转换
     *
     * @param helper the helper  helper
     * @param item   the item 条目
     */
    @Override
    protected void convert(@NonNull BaseViewHolder helper, final Template item) {
        if (item.getUseNum() == -1) {
            helper.setVisible(R.id.tv_used_num, false);
        } else {
            helper.setText(R.id.tv_used_num, String.format(mContext.getString(R.string.template_used_num), formatNumber(item.getUseNum())));
        }
        helper.setText(R.id.tv_template_name, item.getDisplayName());
        helper.setText(R.id.tv_description, Utils.isZh() ? item.getDescriptionZhCn() : item.getDescription());
        Template.Producer producer = item.getProducer();
        if (producer != null) {
            helper.setText(R.id.tv_user_name, producer.getNickName());
            String path = producer.getIconUrl();
            if (TextUtils.isEmpty(path)) {
                path = "https://qasset.meishesdk.com/my/default_icon.png";
            }
            ImageLoader.loadUrl(mContext, path, (ImageView) helper.getView(R.id.iv_portrait), mCircleOptions);
        }
        TextView tvAssetPurchased = helper.getView(R.id.tv_asset_purchased);
        tvAssetPurchased.setVisibility(assetPurchasedVisible(item.isAuthorized()) ? View.VISIBLE : View.GONE);

        final ImageView cover = helper.getView(R.id.iv_cover);
        setLayoutParams(cover, item);
        cover.post(new Runnable() {
            @Override
            public void run() {
                String coverUrl = item.getCoverUrl();
                if (!TextUtils.isEmpty(coverUrl)) {
                    ImageLoader.loadUrl(mContext, Uri.parse(coverUrl.startsWith("http") ? coverUrl : "file://" + coverUrl).toString(), cover, mRoundCornerOptions);
                } else {
                    LogUtils.e("coverUrl is null !!");
                }
            }
        });
    }

    /**
     * 更改布局参数
     * 注意：由于瀑布流各个item大小可能不同，所以在convert做处理，如果非瀑布流不要在convert做处理。
     * Change layout parameters
     * Note: Since waterfall streams may vary in item size, do this at Convert, but not at Convert if not waterfall streams.
     */
    private void setLayoutParams(View view, Template item) {
        ViewGroup.LayoutParams params = view.getLayoutParams();
        int height = (int) (mItemWidth / item.getRatio());
        if (params != null && params.height != height) {
            params.width = mItemWidth;
            params.height = height;
            view.setLayoutParams(params);
            RelativeLayout parent = (RelativeLayout) view.getParent();
            ViewGroup.LayoutParams parentParams = parent.getLayoutParams();
            parentParams.height = (int) (params.height + mContext.getResources().getDimension(R.dimen.dp_px_270));
            parent.setLayoutParams(parentParams);
        }
    }


    private String formatNumber(int number) {
        if (number < 10000) {
            return number + "";
        }
        return FormatUtils.objectFormat2String(number / 10000f) + mContext.getString(R.string.num_unit_w);
    }

    /**
     * 已购素材UI是否显示
     * Asset purchased visible boolean.
     *
     * @return the boolean
     */
    private boolean assetPurchasedVisible(boolean authed) {
        return (!ConfigUtil.isToC() && authed && mNeedShowPurchased);
    }
}
