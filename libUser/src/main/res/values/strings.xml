<resources>
    <string name="dialog_account_number">phone:</string>
    <string name="dialog_account_pwd">password:</string>
    <string name="dialog_hint_input_pwd">Input password</string>
    <string name="user_login_error_hint">Incorrect account or password</string>
    <string name="user_login_business_hint">The login process is only applicable to enterprise users.\n Please contact the business on the official website.</string>
    <string name="user_login_input_phone_email">Please input email address/mobile number</string>
    <string name="user_login">Login</string>
    <string name="user_account_main">MAIN</string>
    <string name="user_account_sub">SUB</string>
    <string name="user_logout">Logout</string>
    <string name="user_confirm_login">Login</string>
    <string name="user_hint_no_purchased_assets">You haven not bought any material yet. Go and buy it.</string>
    <string name="user_hint_no_custom_assets">You do not have any self-made materials. Go and make them.</string>
    <string name="user_login_success">Login success</string>
    <string name="user_login_net_error">Login failed, please check network</string>
    <string name="user_hint_assets_net_error_refresh">Network error, please refresh again</string>
    <string name="user_hint_assets_net_error_no_assets">No material available.</string>
    <string name="user_hint_need_login">You need to login to get material information</string>
    <string name="user_hint_commit_success">Commit success</string>
    <string name="user_hint_commit_failed">Failed to submit. Please try again later</string>

    <string name="user_dialog_assets_unavailable_title">The following materials have no right to use.</string>
    <string name="user_dialog_assets_unavailable_type_no_purchased">No purchased</string>
    <string name="user_dialog_assets_unavailable_type_not_current_account">Materials not belonging to this account</string>
    <string name="user_dialog_assets_unavailable_subtitle">If you need to export the template for using, please submit it to the meicam server.</string>
    <string name="user_dialog_assets_unavailable_submit">Submit materials not purchased to the main account.</string>
    <string name="user_dialog_assets_unavailable_i_see">I see</string>

    <string name="draft_manager_delete">delete</string>
    <string name="draft_manager_upload">upload</string>
    <string name="draft_manager_download">download</string>

    <string name="tab_draft_up_download_1">Uploaded</string>
    <string name="tab_draft_up_download_2">Uploading</string>
    <string name="tab_draft_up_download_3">Downloading</string>

    <string name="tab_draft_compile_1">All</string>
    <string name="tab_draft_compile_2">Compiling</string>
    <string name="tab_draft_compile_3">Downloading</string>

    <string name="hint_cloud_top">If there is no special application, the cloud draft will be cleared within 48 hours.</string>

    <string name="hint_cloud_upload_pop_top">The same saved manuscript already exists in the cloud. Do you want to overwrite it?</string>
    <string name="button_cloud_upload_pop_replace">confirm</string>
    <string name="button_cloud_upload_pop_create">create new</string>

    <string name="cancel">Cancel</string>

    <string name="confirm">Confirm</string>
    <string name="hint_cloud_download_pop_top">Draft already exists locally, do you want to overwrite it？</string>
    <string name="hint_cloud_cancel_download_pop_top">Terminate draft download?</string>
    <string name="hint_cloud_cancel_upoad_pop_top">Terminate draft upload?</string>

    <string name="upload_ali_failed">upload to ali could failed \n</string>

    <string name="upload_fail">Upload failed!</string>
    <string name="download_fail">Download failed!</string>

    <string name="download_replace">Replace</string>
    <string name="download_new">Create new</string>

    <string name="download_success">Download success</string>

    <string name="video_frame_rate">frame rate</string>
    <string name="video_resolution">resolution</string>
    <string name="job_name">Job name</string>
    <string name="video_frame_rate_info">higher the frame rate, higher the smoothness</string>
    <string name="video_resolution_info">higher the resolution, clearer the video</string>

    <string name="commit_cloud_compile">Commit cloud compile</string>
    <string name="commit_cloud_compile_hint">After successful submission, you will jump to the cloud generation page to view the progress and download files. Log in to the cloud clip, and you can also view the generated files</string>
    <string name="cloud_compile_error">Compile failed</string>

    <string name="int1080">1080p</string>
    <string name="int720">720p</string>
    <string name="int360">360p</string>
    <string name="int480">480p</string>
    <string name="int4K">4K</string>

    <string name="frame_rate_15">15</string>
    <string name="frame_rate_24">24</string>
    <string name="frame_rate_25">25</string>
    <string name="frame_rate_30">30</string>
    <string name="frame_rate_50">50</string>
    <string name="frame_rate_60">60</string>

    <string name="create_at">Create at</string>
    <string name="tv_login_desc">You haven\'t logged in yet. Go and log in</string>
    <string name="tv_login">go login</string>
    <string name="draft_box">draft box</string>
    <string name="draft_manage">manage</string>
    <string name="tab_draft_1">Local draft</string>
    <string name="tab_draft_2">Cloud draft</string>
    <string name="tab_draft_3">Cloud Compile</string>
    <string name="upload_no_exit">uploading do not exit...</string>
    <string name="upload_input_dialog_success">upload success to %s</string>
</resources>
