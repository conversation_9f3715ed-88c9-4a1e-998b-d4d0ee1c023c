package com.meishe.capturemodule.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.ViewGroup;

import com.meicam.sdk.NvsLiveWindowExt;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/4/20 15:02
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class NvsLiveWindowWrapper extends NvsLiveWindowExt {
   private int animationWidth;
   private int animationHeight;
   public NvsLiveWindowWrapper(Context context) {
      super(context);
   }

   public NvsLiveWindowWrapper(Context context, AttributeSet attributeSet) {
      super(context, attributeSet);
   }

   public NvsLiveWindowWrapper(Context context, AttributeSet attributeSet, int i) {
      super(context, attributeSet, i);
   }

   public void setAnimationHeight(int animationHeight) {
      this.animationHeight = animationHeight;
      ViewGroup.LayoutParams layoutParams = getLayoutParams();
      if (layoutParams != null) {
         layoutParams.height = animationHeight;
         setLayoutParams(layoutParams);
      }
   }

   public void setAnimationWidth(int animationWidth) {
      this.animationWidth = animationWidth;
      ViewGroup.LayoutParams layoutParams = getLayoutParams();
      if (layoutParams != null) {
         layoutParams.width = animationWidth;
         setLayoutParams(layoutParams);
      }
   }

   public int getAnimationHeight() {
      return animationHeight;
   }

   public int getAnimationWidth() {
      return animationWidth;
   }
}
