package com.meishe.user.bean;

import java.io.Serializable;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/12 18:40
 * @Description :云草稿bean The cloud draft bean
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CloudDraftListBean implements Serializable {
    private int projectCount;
    private List<Project> projectList;

    public int getProjectCount() {
        return projectCount;
    }

    public void setProjectCount(int projectCount) {
        this.projectCount = projectCount;
    }

    public List<Project> getProjectList() {
        return projectList;
    }

    public void setProjectList(List<Project> projectList) {
        this.projectList = projectList;
    }

    public static class Project implements Serializable{
        private String coverUrl;
        private String createdAt;
        private String path;
        private String title;
        private String projectId;
        private String modifyAt;
        private String uuid;
        private String userId;
        private String multiProjectUrl;
        private String infoUrl;
        /**
         *当且仅当为多端工程使时返回
         *如果更新时转码未完成 则有此字段 应当轮询job/info接口
         *如果转码已完成 则无此字段
         * Returns if and only if enabled for a multi terminal project
         * If the transcoding is not completed when updating, this field should poll the
         * job/Info interface
         * If transcoding has been completed, there is no such field
         */
        private String jobId;

        private long duration;

        public String getCoverUrl() {
            return coverUrl;
        }

        public void setCoverUrl(String coverUrl) {
            this.coverUrl = coverUrl;
        }

        public String getCreatedAt() {
            return createdAt;
        }

        public void setCreatedAt(String createdAt) {
            this.createdAt = createdAt;
        }

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getProjectId() {
            return projectId;
        }

        public void setProjectId(String projectId) {
            this.projectId = projectId;
        }

        public String getModifyAt() {
            return modifyAt;
        }

        public void setModifyAt(String modifyAt) {
            this.modifyAt = modifyAt;
        }

        public String getUuid() {
            return uuid;
        }

        public void setUuid(String uuid) {
            this.uuid = uuid;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getMultiProjectUrl() {
            return multiProjectUrl;
        }

        public void setMultiProjectUrl(String multiProjectUrl) {
            this.multiProjectUrl = multiProjectUrl;
        }

        public String getInfoUrl() {
            return infoUrl;
        }

        public void setInfoUrl(String infoUrl) {
            this.infoUrl = infoUrl;
        }

        public String getJobId() {
            return jobId;
        }

        public void setJobId(String jobId) {
            this.jobId = jobId;
        }

        public long getDuration() {
            return duration;
        }

        public void setDuration(long duration) {
            this.duration = duration;
        }
    }

    @Override
    public String toString() {
        return "CloudDraftListBean{" +
                "projectCount=" + projectCount +
                ", projectList=" + projectList +
                '}';
    }
}
