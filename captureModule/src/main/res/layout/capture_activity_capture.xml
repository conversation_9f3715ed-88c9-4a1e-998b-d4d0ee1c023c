<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <com.meishe.capturemodule.view.NvsLiveWindowWrapper
        android:id="@+id/lw_window"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.meishe.capturemodule.view.ExposureSeekBarView
        android:id="@+id/exposure_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.meishe.capturemodule.view.CaptureMusicControlView
        android:id="@+id/select_music_control_View"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/iv_exit"
        app:layout_constraintBottom_toBottomOf="@+id/iv_exit"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/tv_zoom"
        android:layout_width="@dimen/dp_px_90"
        android:layout_height="@dimen/dp_px_90"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="@dimen/dp_px_54"
        android:background="@drawable/bg_corners_90_4d000000"
        android:gravity="center"
        android:text="1x"
        android:textColor="@color/white"
        android:textSize="@dimen/sp12"
        app:layout_constraintBottom_toTopOf="@+id/fl_take_photo"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <FrameLayout
        android:id="@+id/fl_middle_parent"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_105"
        android:layout_marginBottom="@dimen/dp_px_54"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@+id/fl_take_photo">

        <ImageView
            android:id="@+id/iv_back_delete"
            android:layout_width="@dimen/dp_px_106"
            android:layout_height="@dimen/dp_px_106"
            android:layout_marginStart="@dimen/dp_px_255"
            android:contentDescription="@null"
            android:src="@mipmap/capture_back_delete"
            android:visibility="visible" />

        <ImageView
            android:id="@+id/iv_confirm"
            android:layout_width="@dimen/dp_px_93"
            android:layout_height="@dimen/dp_px_93"
            android:layout_gravity="end"
            android:layout_marginEnd="@dimen/dp_px_255"
            android:contentDescription="@null"
            android:src="@mipmap/capture_confirm"
            android:visibility="visible" />

        <TextView
            android:id="@+id/tv_timing_num"
            android:layout_width="@dimen/dp_px_219"
            android:layout_height="@dimen/dp_px_60"
            android:layout_gravity="center"
            android:background="@drawable/bg_66000000_10"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:visibility="visible"
            tools:ignore="RelativeOverlap" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/fl_take_photo"
        android:layout_width="65dp"
        android:layout_height="65dp"
        android:layout_centerHorizontal="true"
        app:layout_constraintBottom_toTopOf="@+id/ll_chang_pv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <ImageView
            android:id="@+id/iv_take_photo"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@null"
            android:src="@mipmap/capture_take_video" />

        <TextView
            android:id="@+id/tv_video_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:textColor="@color/color_ffff365e"
            android:textSize="25sp"
            android:visibility="invisible" />
    </FrameLayout>
    <!--美颜-->
    <ImageView
        android:id="@+id/iv_focus"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:contentDescription="@null"
        android:scaleType="fitCenter"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@mipmap/capture_focus" />

    <ImageView
        android:id="@+id/iv_exit"
        android:layout_width="@dimen/dp_px_90"
        android:layout_height="@dimen/dp_px_90"
        android:layout_marginStart="@dimen/sp_px_42"
        android:layout_marginTop="@dimen/dp_px_129"
        android:contentDescription="@null"
        android:src="@mipmap/capture_exit_white"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_more"
        android:layout_width="@dimen/dp_px_90"
        android:layout_height="@dimen/dp_px_90"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dp_px_129"
        android:layout_marginEnd="@dimen/dp_px_68"
        android:contentDescription="@null"
        android:src="@mipmap/capture_more_white"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <!--切换视频-->
    <ImageView
        android:id="@+id/iv_rollover"
        android:layout_width="@dimen/dp_px_90"
        android:layout_height="@dimen/dp_px_90"
        android:layout_marginTop="@dimen/top_tool_margin"
        android:layout_marginEnd="@dimen/dp_px_68"
        android:contentDescription="@null"
        android:src="@mipmap/capture_rollover_white"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_more" />

    <ImageView
        android:id="@+id/iv_ratio"
        android:layout_width="@dimen/dp_px_90"
        android:layout_height="@dimen/dp_px_90"
        android:layout_marginTop="@dimen/top_tool_margin"
        android:layout_marginEnd="@dimen/dp_px_68"
        android:contentDescription="@null"
        android:src="@mipmap/ic_capture_ration_9v16_normal"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_rollover" />

    <LinearLayout
        android:id="@+id/ll_beauty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="@+id/fl_take_photo"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintLeft_toRightOf="@+id/fl_take_photo"
        app:layout_constraintRight_toLeftOf="@+id/ll_filter"
        app:layout_constraintTop_toTopOf="@+id/fl_take_photo"
        tools:ignore="UseCompoundDrawables">

        <ImageView
            android:layout_width="37dp"
            android:layout_height="37dp"
            android:contentDescription="@null"
            android:src="@mipmap/capture_beauty_white" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="@string/beauty"
            android:textColor="@color/white"
            android:textSize="@dimen/bottom_tool_text_size" />
    </LinearLayout>

    <!--道具-->
    <View
        android:id="@+id/holder"
        android:layout_width="@dimen/dp_px_90"
        android:layout_height="@dimen/dp_px_90"
        android:layout_marginStart="@dimen/dp_px_60"
        app:layout_constraintBottom_toBottomOf="@+id/fl_take_photo"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@+id/fl_take_photo" />

    <LinearLayout
        android:id="@+id/ll_props"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="@+id/fl_take_photo"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintLeft_toRightOf="@+id/holder"
        app:layout_constraintRight_toLeftOf="@+id/fl_take_photo"
        app:layout_constraintTop_toTopOf="@+id/fl_take_photo"
        tools:ignore="UseCompoundDrawables">

        <ImageView
            android:layout_width="37dp"
            android:layout_height="37dp"
            android:contentDescription="@null"
            android:src="@mipmap/capture_props_white" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="@string/faceU"
            android:textColor="@color/white"
            android:textSize="@dimen/bottom_tool_text_size" />
    </LinearLayout>

    <!--滤镜-->
    <LinearLayout
        android:id="@+id/ll_filter"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_px_60"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="@+id/fl_take_photo"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/fl_take_photo"
        tools:ignore="UseCompoundDrawables">

        <ImageView
            android:layout_width="37dp"
            android:layout_height="37dp"
            android:contentDescription="@null"
            android:src="@mipmap/capture_filter_white" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="@string/filter"
            android:textColor="@color/white"
            android:textSize="@dimen/bottom_tool_text_size" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_chang_pv"
        android:layout_width="wrap_content"
        android:layout_height="42dp"
        android:layout_marginBottom="@dimen/dp_px_116"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/fl_take_photo"
        app:layout_constraintLeft_toRightOf="@+id/fl_take_photo">

        <TextView
            android:id="@+id/tv_take_photos"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="22dp"
            android:layout_marginTop="@dimen/dp20"
            android:gravity="center_horizontal"
            android:text="@string/photo"
            android:textColor="@color/white"
            android:textSize="@dimen/bottom_tool_text_size" />

        <TextView
            android:id="@+id/tv_take_video"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="@dimen/dp20"
            android:layout_marginEnd="@dimen/dp20"
            android:text="@string/video"
            android:textColor="@color/white"
            android:textSize="@dimen/bottom_tool_text_size" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/timer_layout"
        android:layout_width="@dimen/dp_px_360"
        android:layout_height="@dimen/dp_px_360"
        android:background="@drawable/bg_66000000_4"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.meishe.capturemodule.view.TimeDownView
            android:id="@+id/time_down_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="80sp" />
    </FrameLayout>

    <LinearLayout
        android:id="@+id/linear_filter_sb"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_150"
        android:background="#80000000"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/multiply_bottom_view">

        <com.meishe.capturemodule.view.MagicProgress
            android:id="@+id/filter_sb"
            android:layout_width="0dp"
            android:layout_height="45dp"
            android:layout_marginLeft="@dimen/dp_px_216"
            android:layout_marginRight="@dimen/dp_px_216"
            android:layout_weight="1"
            android:focusable="true"
            android:progressDrawable="@drawable/seekbar"
            android:thumb="@drawable/beauty_seek_thumb"
            app:textSizeMagic="12sp" />

    </LinearLayout>

    <com.meishe.business.assets.view.MYMultiBottomView
        android:id="@+id/multiply_bottom_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/color_ff252525"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
