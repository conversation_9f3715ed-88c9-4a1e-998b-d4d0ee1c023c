package com.meishe.base.view;

import android.content.Context;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.animation.RotateAnimation;
import android.widget.AbsListView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.Scroller;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.R;

/**
 * Created by -- on 2016/11/2.
 * 自定义下拉刷新上拉加载的基类，可以扩展多种可滑动view(ListView,GridView,RecyclerView...)
 * 第六版：第五版的基础上进行改进，修复item点击与滑动冲突问题
 * header和列表不能连续无缝平滑滑动，必须松手一次：因为该次事件已经被处理为拦截或者不拦截，所以后续move事件不能再次触发onInterceptTouchevent事件，只能松手重新触发
 Customize the base class for pull-down refresh and pull-up loading, which can extend multiple sliding views (ListView, GridView, RecyclerView...)
 *Sixth edition: Improvements made to the fifth edition to fix the issue of item clicking and sliding conflicts
 *The header and list cannot slide seamlessly and continuously, and must be released once: because this event has already been processed as intercepted or not intercepted, subsequent move events cannot trigger the onInterceptTouchevent event again, and can only be released and triggered again
 */

public class PullToRefreshAndPushToLoadView extends ViewGroup {
    private static final String TAG = "PullToRefreshAndPushToLoadView";
    private Context mContext;
    private Scroller mScroller;
    /**
     * 在被判定为滚动之前用户手指可以移动的最大值。
     * The maximum value that a user's finger can move before being judged as scrolling.
     */
    private int touchSlop;
    /**
     * 判断手指起始落点，如果距离属于滑动了，就屏蔽一切点击事件
     * Determine the starting landing point of the finger,
     * and if the distance belongs to sliding, block all click events.
     */
    private boolean isUserSwiped;

    /**
     * 记录开始按下的时间
     * Record the time when the button was pressed
     */
    private long startPress;

    /**
     * 下拉头的高度
     * The height of the pull-down head
     */
    private int hideHeaderHeight;
    /**
     * 上拉底部的高度
     * The height of the pull-up bottom
     */
    private int hideFooterHeight;

    /**
     * 用于存储上次更新时间
     * Used to store the last update time
     */
    private SharedPreferences preferences;

    /**
     * 下拉头的View
     * View of dropdown header
     */
    private View header;
    /**
     * 上拉底部的View
     * Pull up the View at the bottom
     */
    private View footer;

    /**
     * 需要去刷新和加载的View
     * View that needs to be refreshed and loaded
     */
    private View mView;
    /**
     * 本控件的宽高
     * The width and height of this view
     */
    private int maxWidth, maxHeight;

    /**
     * footer的进度条
     * Progress bar of footer
     */
    private ProgressBar pbFooter;
    /**
     * footer的文字描述
     * The textual description of the footer
     */
    private TextView tvLoadMore;
    /**
     * 刷新时显示的进度条
     * Progress bar displayed when refreshing
     */
    private ProgressBar progressBar;

    /**
     * 指示下拉和释放的箭头
     * Arrows indicating pull-down and release
     */
    private ImageView arrow;

    /**
     * 指示下拉和释放的文字描述
     * Text description indicating pull-down and release
     */
    private TextView description;

    /**
     * 上次更新时间的文字描述
     * Text description of the last update time
     */
    private TextView updateAt;

    /**
     * 上次更新时间的毫秒值
     */
    private long lastUpdateTime;

    /**
     * 一分钟的毫秒值，用于判断上次的更新时间
     */
    public static final long ONE_MINUTE = 60 * 1000;

    /**
     * 一小时的毫秒值，用于判断上次的更新时间
     */
    public static final long ONE_HOUR = 60 * ONE_MINUTE;

    /**
     * 一天的毫秒值，用于判断上次的更新时间
     */
    public static final long ONE_DAY = 24 * ONE_HOUR;

    /**
     * 一月的毫秒值，用于判断上次的更新时间
     */
    public static final long ONE_MONTH = 30 * ONE_DAY;

    /**
     * 一年的毫秒值，用于判断上次的更新时间
     */
    public static final long ONE_YEAR = 12 * ONE_MONTH;

    /**
     * 上次更新时间的字符串常量，用于作为SharedPreferences的键值
     */
    private static final String UPDATED_AT = "updated_at";
    /**
     * 为了防止不同界面的下拉刷新在上次更新时间上互相有冲突，使用id来做区分
     */
    private int mId = -1;

    /**
     * 当前是否在view的顶部，只有View滚动到头的时候才允许下拉
     */
    private boolean isTop;
    /**
     * 当前是否在view的底部，只有View滚动到底的时候才允许上拉
     */
    private boolean isBottom;

    /**
     * 当前是否需要强制刷新
     */
    private boolean isNeedForceRefresh;
    /**
     * 上次手指按下时的屏幕纵坐标
     */
    private float mLastY = -1;
    /**
     * 第一次手指按下时的屏幕纵坐标
     */
    private float mFirstY = -1;
    /**
     * 当前处理什么状态，可选值有STATUS_PULL_TO_REFRESH, STATUS_RELEASE_TO_REFRESH,
     * STATUS_REFRESHING 和 STATUS_REFRESH_FINISHED
     */
    private int currentStatus = STATUS_REFRESH_FINISHED;

    /**
     * 记录上一次的状态是什么，避免进行重复操作
     * Record the last status to avoid duplicate operations
     */
    private int lastStatus = currentStatus;
    /**
     * 下拉状态
     * Dropdown status
     */
    public static final int STATUS_PULL_TO_REFRESH = 0;

    /**
     * 释放立即刷新状态
     * Release immediate refresh status
     */
    public static final int STATUS_RELEASE_TO_REFRESH = 1;

    /**
     * 正在刷新状态
     * Refreshing status
     */
    public static final int STATUS_REFRESHING = 2;

    /**
     * 刷新完成或未刷新状态
     * Refresh completed or not refreshed status
     */
    public static final int STATUS_REFRESH_FINISHED = 3;

    /**
     * 当前处于什么状态，STATUS_LOAD_NORMAL, STATUS_LOADING
     * What is the current state, Status_ LOAD_ NORMAL, STATUS_ LOADING
     */
    private int currentFooterStatus = STATUS_LOAD_NORMAL;
    /**
     * 上拉状态
     * Pull-up state
     */
    public static final int STATUS_LOAD_NORMAL = 4;
    /**
     * 正在加载状态
     * Loading status
     */
    public static final int STATUS_LOADING = 5;
    /**
     * 下拉刷新上拉加载的回调接口
     * Pull down refresh pull up loaded callback interface
     */
    private PullToRefreshAndPushToLoadMoreListener mListener;

    /**
     * 是否正在加载
     * Loading or not
     */
    private boolean isLoading;
    /**
     * 是否正在刷新
     * Is it refreshing
     */
    private boolean isRefreshing;
    /**
     * 是否正在自动刷新
     * Is automatic refresh in progress
     */
    private boolean autoRefresh;
    /**
     * 是否正在完成刷新
     * Are you completing the refresh
     */
    private boolean isFinishingRefresh = false;

    private static final float DEFAULT_RATIO = 2f;
    /**
     * 判断是否可以拖动（防止多指触摸）
     * Determine if dragging is possible (to prevent multi finger touch)
     */
    private boolean canDrag = false;
    /**
     * 拖动阻力系数
     * Drag drag coefficient
     */
    private float ratio = DEFAULT_RATIO;

    private int screenHeight;
    /**
     * 是否支持下拉刷新
     * Does it support dropdown refresh
     */
    private boolean canRefresh = true;
    /**
     * 是否支持上拉加载
     * Does it support pull-up loading
     */
    private boolean canLoadMore = true;

    /**
     * 是否支持滑动到底部自动加载更多
     * Does it support sliding to the bottom to automatically load more
     */
    private boolean canAutoLoadMore = false;

    /**
     * 是否已经完成header,mView,footer的布局
     * Have you completed the layout of the header, mView, and footer
     */
    private boolean hasFinishedLayout = false;

    /**
     * 是否正在触摸
     * Are you touching
     */
    private boolean isTouching = false;

    public PullToRefreshAndPushToLoadView(Context context) {
        this(context, null);
    }

    public PullToRefreshAndPushToLoadView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public PullToRefreshAndPushToLoadView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context mContext) {
        this.mContext = mContext;
        mScroller = new Scroller(mContext);
        screenHeight = getResources().getDisplayMetrics().heightPixels;
        preferences = PreferenceManager.getDefaultSharedPreferences(mContext);

        header = LayoutInflater.from(mContext).inflate(R.layout.refresh_header, null, false);
        progressBar = (ProgressBar) header.findViewById(R.id.progress_bar);
        arrow = (ImageView) header.findViewById(R.id.arrow);
        description = (TextView) header.findViewById(R.id.description);
        updateAt = (TextView) header.findViewById(R.id.updated_at);

        footer = LayoutInflater.from(mContext).inflate(R.layout.load_more_footer, null, false);
        pbFooter = (ProgressBar) footer.findViewById(R.id.pb);
        tvLoadMore = (TextView) footer.findViewById(R.id.tv_load_more);

        touchSlop = ViewConfiguration.get(mContext).getScaledTouchSlop();
        refreshUpdatedAtValue();
        addView(header, 0);
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
//        Log.e(TAG, "onFinishInflate: ================" +getChildCount());
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        for (int i = 0; i < getChildCount(); i++) {
            View childView = getChildAt(i);
            if (childView.getVisibility() != View.GONE) {
                //获取每个子view的自己高度宽度，取最大的就是viewGroup的大小
                // Obtain the height and width of each subview, and the largest one is the size of the viewGroup
                measureChild(childView, widthMeasureSpec, heightMeasureSpec);
                maxWidth = Math.max(maxWidth, childView.getMeasuredWidth());
                maxHeight = Math.max(maxHeight, childView.getMeasuredHeight());
            }
        }
        //为ViewGroup设置宽高 Set width and height for ViewGroup.
        setMeasuredDimension(maxWidth + getPaddingLeft() + getPaddingRight(), maxHeight + getPaddingTop() + getPaddingBottom());

        //处理数据不满一屏的情况下禁止上拉
        // Do not pull up when processing data on less than one screen.
        if (mView != null) {
            LayoutParams vlp = mView.getLayoutParams();
            if (vlp.height == LayoutParams.WRAP_CONTENT) {
                vlp.height = LayoutParams.MATCH_PARENT;
            }
            if (vlp.width == LayoutParams.WRAP_CONTENT) {
                vlp.width = LayoutParams.MATCH_PARENT;
            }
            mView.setLayoutParams(vlp);
        }
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        if (!hasFinishedLayout) {
            mView = getChildAt(1);
            addView(footer);
            hasFinishedLayout = true;

            if (canLoadMore && canAutoLoadMore) {
                setAutoLoadMore();
            }
        }
        if (hideHeaderHeight == 0) {
            hideHeaderHeight = -header.getHeight();
        }
        if (hideFooterHeight == 0) {
            hideFooterHeight = footer.getHeight();
        }

        int top = hideHeaderHeight + getPaddingTop();
//        header.layout(0,top,maxWidth,top+header.getMeasuredHeight());
//        top+=header.getMeasuredHeight();
//        mView.layout(0,top,maxWidth,top+mView.getMeasuredHeight());
//        top+=mView.getMeasuredHeight();
//        footer.layout(0,top,maxWidth,top+footer.getMeasuredHeight());
        for (int i = 0; i < getChildCount(); i++) {
            View childView = getChildAt(i);
            if (childView.getVisibility() != GONE) {
                childView.layout(getPaddingLeft(), top, maxWidth + getPaddingLeft(), top + childView.getMeasuredHeight());
                top += childView.getMeasuredHeight();
            }
        }
    }

    @Override
    public boolean dispatchTouchEvent(final MotionEvent event) {
        //每次首先进行判断是否在列表顶部或者底部
        // Each time, first determine whether it is at the top or bottom of the list.
        judgeIsTop();
        judgeIsBottom();

        switch (event.getActionMasked()) {
            case MotionEvent.ACTION_DOWN:
            case MotionEvent.ACTION_POINTER_DOWN:
                isUserSwiped = false;
                startPress = System.currentTimeMillis();
                if (event.getPointerId(event.getActionIndex()) == 0) {
                    mLastY = event.getY(0);
                    mFirstY = event.getY();
                    isTouching = true;
                    canDrag = true;
                    if (isNeedForceRefresh) {
                        return true;
                    } else {
                        return super.dispatchTouchEvent(event);
                    }
                } else {
                    return false;
                }
            case MotionEvent.ACTION_MOVE:
                if (!canDrag) {
                    //false交给父控件处理 False Leave it to the parent control for processing
                    return false;
                }
                break;
            case MotionEvent.ACTION_POINTER_UP:
            default:
                if (Math.abs(event.getY() - mFirstY) > touchSlop) {
                    //判断是否滑动还是长按
                    // Determine whether to slide or press and hold
                    isUserSwiped = true;
                } else {
                }
                if (event.getPointerId(event.getActionIndex()) == 0) {
                    canDrag = false;
                }
                ratio = DEFAULT_RATIO;
                isTouching = false;
                break;
        }
        return super.dispatchTouchEvent(event);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        switch (ev.getAction()) {
            case MotionEvent.ACTION_MOVE:
                float deltaY = ev.getY() - mLastY;
                if (Math.abs(ev.getY() - mFirstY) > touchSlop) {
                    //只要有滑动，就进行处理，屏蔽一切点击长按事件
                    // As long as there is a slide, handle it and block all click and hold events
                    if (getScrollY() < 0 && currentStatus == STATUS_REFRESHING) {
                        //正在刷新并且header没有完全隐藏时，把事件交给自己处理
                        // When refreshing and the header is not completely hidden, leave the event to oneself for processing.
                        return true;
                    }
                    if (getScrollY() > 0 && currentFooterStatus == STATUS_LOADING) {
                        //正在刷新并且footer没有完全隐藏时，把事件交给自己处理
                        // When refreshing and the footer is not completely hidden, leave the event to oneself for processing.
                        return true;
                    }
                    if (getScrollY() == 0 && ((isTop && deltaY > 0) || (isBottom && deltaY < 0))) {
                        //header footer都隐藏时，顶部下拉或者底部上拉都把事件交给自己处理
                        // When the header footer is hidden, either the top drop-down or bottom pull-up will
                        // handle the event themselves.
                        return true;
                    }
                } else {
                    if (System.currentTimeMillis() - startPress >= ViewConfiguration.getLongPressTimeout()) {
                        //说明长按事件发生，禁止任何滑动操作
                        // Long press event occurs, prohibiting any sliding operation.
//                        Log.e(TAG, "onInterceptTouchEvent: "+"======longclick happened======" );
                        canDrag = false;
                    }
                }
                break;
            case MotionEvent.ACTION_UP:
                if (isUserSwiped) {
                    //点击事件发生在onTouchEvent的ACTION_UP中，所以此处进行处理：如果属于滑动则拦截一切事件，禁止传递给子view
                    // The click event occurs in the ACTION_UP of onTouchEvent, so handle it here:
                    // if it belongs to sliding, intercept all events and prohibit transmission to child views.
                    return true;
                }
                if (isRefreshing || isLoading) {
                    //正在刷新或者加载的时候，禁止点击事件
                    // Prohibit clicking on events while refreshing or loading.
                    return true;
                }
                break;
            default:
                break;
        }
        return super.onInterceptTouchEvent(ev);
    }

    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        switch (ev.getAction()) {
            case MotionEvent.ACTION_MOVE:
                float deltaY = ev.getY() - mLastY;
                mLastY = ev.getY();

                boolean showTop = deltaY >= 0 && isTop;
                boolean hideTop = deltaY <= 0 && getScrollY() < 0;
//                boolean noMove=deltaY==0;//当不动的时候屏蔽一切事件，防止列表滚动 Block all events when stationary to prevent list scrolling
                boolean showBottom = deltaY <= 0 && isBottom;
                boolean hideBottom = deltaY >= 0 && getScrollY() > 0;

//                Log.e(TAG, "dispatchTouchEvent: "+ratio+"+++"+isTop+"###"+getScrollY()+"$$$"+deltaY);
                if ((showBottom && canLoadMore) || hideBottom) {
                    if (deltaY < 0) {
                        if (getScrollY() >= hideFooterHeight) {
                            ratio += 0.05f;
                        }
                    } else {
                        ratio = 1;
                    }
                    int dy = (int) (deltaY / ratio);
                    if (deltaY > 0 && Math.abs(dy) > Math.abs(getScrollY())) {
                        //当滑动距离大于可滚动距离时，进行调整
                        // Adjust when the sliding distance is greater than the scrollable distance.
                        dy = Math.abs(getScrollY());
                    }
                    scrollBy(0, -dy);
                    return true;
                } else if ((showTop && canRefresh) || hideTop) {
                    //说明头部显示，自己处理滑动，无论上滑下滑均同步移动（==0代表滑动到顶部可以继续下拉）
                    //The head shows that you can handle sliding on your own,
                    // and whether you slide up or down, you will move synchronously
                    // (==0 means sliding to the top can continue to pull down).
                    if (deltaY < 0) {
                        //来回按住上下移动：下拉逐渐增加难度，上拉不变
                        // Press and hold back and move up and down: Pull down gradually increases difficulty, pull up remains unchanged
                        ratio = 1;
                        //此处如果系数不是1，则会出现列表跳动的现象
                        // If the coefficient is not 1 here, there will be a phenomenon of list jumping... Not resolved yet!!!
                    } else {
                        if (Math.abs(getScrollY()) >= -hideHeaderHeight) {
                            //当头部露出以后逐步增加下拉难度
                            // Gradually increase the difficulty of pulling down when the head is exposed
                            ratio += 0.05f;
                        }
                    }
                    int dy = (int) (deltaY / ratio);
                    if (deltaY < 0 && Math.abs(dy) > Math.abs(getScrollY())) {
                        //当滑动距离大于可滚动距离时，进行调整
                        // Adjust when the sliding distance is greater than the scrollable distance
                        dy = -Math.abs(getScrollY());
                    }
//                    Log.e(TAG, "dispatchTouchEvent: "+"###"+getScrollY()+"%%%"+dy);
                    scrollBy(0, -dy);
//                    Log.e(TAG, "dispatchTouchEvent: "+"###"+getScrollY()+"&&&"+dy);
                    if (currentStatus != STATUS_REFRESHING) {
                        if (getScrollY() <= hideHeaderHeight) {
                            currentStatus = STATUS_RELEASE_TO_REFRESH;
                        } else {
                            currentStatus = STATUS_PULL_TO_REFRESH;
                        }
                        // 时刻记得更新下拉头中的信息
                        // Always remember to update the information in the dropdown header
                        updateHeaderView();
                        lastStatus = currentStatus;
                    }
                    return true;
                } else {
                    return super.onTouchEvent(ev);
                }
            case MotionEvent.ACTION_UP:
                //处理顶部==========================================
                // Process Top==========================================
                if (currentStatus == STATUS_RELEASE_TO_REFRESH) {
                    // 松手时如果是释放立即刷新状态，就去调用正在刷新的任务
                    // If the immediate refresh state is released when releasing, call the task being refreshed
                    backToTop();
                } else if (currentStatus == STATUS_PULL_TO_REFRESH) {
                    // 松手时如果是下拉状态，就去调用隐藏下拉头的任务
                    // If it is in a dropdown state when releasing, call the task of hiding the dropdown header
                    hideHeader(false);
                } else if (currentStatus == STATUS_REFRESHING) {
                    if (getScrollY() <= hideHeaderHeight) {
                        //回弹 Back to top
                        backToTop();
                    }
                }
                //处理底部===========================================
                // Processing bottom===============================
                if (getScrollY() > 0 && getScrollY() < hideFooterHeight && !isLoading) {
                    //松手时隐藏底部 Hide bottom when releasing
                    hideFooter();
                } else if (getScrollY() >= hideFooterHeight) {
                    //显示底部，开始加载更多 Show bottom, start loading more
                    showFooter();
                }
                return true;
                default:
                    break;
        }
        return super.onTouchEvent(ev);
    }

    private void hideFooter() {
        currentFooterStatus = STATUS_LOAD_NORMAL;
        isLoading = false;
        mScroller.startScroll(0, getScrollY(), 0, -getScrollY());
        footer.setVisibility(GONE);
        invalidate();
    }

    private void showFooter() {
        currentFooterStatus = STATUS_LOADING;
        updateFooterView();
        mScroller.startScroll(0, getScrollY(), 0, hideFooterHeight - getScrollY());
        invalidate();
        if (mListener != null && !isLoading) {
            isLoading = true;
            mListener.onLoadMore();
        }
    }

    private void autoLoadMore() {
        if (mListener != null && !isLoading) {
            currentFooterStatus = STATUS_LOADING;
            updateFooterView();
            mScroller.startScroll(0, 0, 0, hideFooterHeight);
            invalidate();
            isLoading = true;
            mListener.onLoadMore();
        }
    }

    /**
     * 当所有的加载逻辑完成后，记录调用一下，否则你的View将一直处于正在加载状态。
     * After all the loading logic is completed, record and call it,
     * otherwise your View will remain in the loading state.
     */
    public void finishLoading() {
        post(mHideFooter);
    }

    private void backToTop() {
        currentStatus = STATUS_REFRESHING;
        updateHeaderView();
        mScroller.startScroll(0, getScrollY(), 0, hideHeaderHeight - getScrollY());
        invalidate();
        if (mListener != null && !isRefreshing) {
            isRefreshing = true;
            mListener.onRefresh();
        }
    }

    private void hideHeader(boolean isRefreshFinished) {
        currentStatus = STATUS_REFRESH_FINISHED;
        lastStatus = currentStatus;
        autoRefresh = false;
        isRefreshing = false;
        isFinishingRefresh = false;
        if (isRefreshFinished) {
            //只有刷新完成才更新时间，反弹不算
            // Only update the time after the refresh is completed, rebound does not count.
            preferences.edit().putLong(UPDATED_AT + mId, System.currentTimeMillis()).commit();
        }
        mScroller.startScroll(0, getScrollY(), 0, -getScrollY());
        invalidate();
    }

    /**
     * 自动刷新
     */
    public void autoRefresh() {
        if (mListener != null && !isRefreshing) {
            currentStatus = STATUS_REFRESHING;
            updateHeaderView();
            mScroller.startScroll(0, 0, 0, hideHeaderHeight);
            invalidate();
            isRefreshing = true;
            //放在updateHeaderView后面
            // Place after updateHeaderView
            autoRefresh = true;
            mListener.onRefresh();
        }
    }

    /**
     * 当所有的刷新逻辑完成后，记录调用一下，否则你的View将一直处于正在刷新状态。
     * After all the refresh logic is completed, call the record,
     * otherwise your View will remain in the refresh state.
     */
    public void finishRefreshing() {
        isFinishingRefresh = true;
        post(mRefreshSuccess);
        postDelayed(mHideHeader, 200);
    }

    @Override
    public void computeScroll() {
        if (mScroller.computeScrollOffset()) {
            scrollTo(mScroller.getCurrX(), mScroller.getCurrY());
            postInvalidate();
        } else {
            //刷新动画完成以后初始化头部并且没有正在显示刷新成功的状态
            // Initialize the header after refreshing the animation
            // and there is no status showing successful refresh.
            if (!isFinishingRefresh) {
                updateHeaderView();
            }
            //加载动画完成后更新footer
            // Update the footer after loading the animation.
            updateFooterView();
        }
    }

    /**
     * 根据是否滚动到最底部去进行自动加载更多的操作
     * Automatically load more operations based on whether to scroll to the bottom or not
     */
    private void setAutoLoadMore() {
        if (mView != null) {
            if (mView instanceof AbsListView) {
                AbsListView absListView = (AbsListView) mView;
                absListView.setOnScrollListener(new AbsListView.OnScrollListener() {
                    @Override
                    public void onScrollStateChanged(AbsListView view, int scrollState) {
                    }

                    @Override
                    public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
                        //此处有两种选择：1、绝对的滚动到最底部。2、滚动到最后一个元素就开始去加载，不必显示footer
                        // There are two options here: 1. Absolute scrolling to the bottom.
                        // 2. Scroll to the last element and start loading without displaying a footer.
                        if (isTouching) {
                            return;
                        }
                        judgeIsBottom();
                        if (isBottom) {
                            autoLoadMore();
                        }
//                        if(view.getLastVisiblePosition()==totalItemCount-1){
//                            autoLoadMore();
//                        }
                    }
                });
            } else if (mView instanceof RecyclerView) {
                RecyclerView recyclerView = (RecyclerView) mView;
                recyclerView.setOnScrollListener(new RecyclerView.OnScrollListener() {

                    @Override
                    public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                        super.onScrolled(recyclerView, dx, dy);
                        if (isTouching) {
                            return;
                        }
                        judgeIsBottom();
//                        View lastChild = recyclerView.getLayoutManager().findViewByPosition(recyclerView.getAdapter().getItemCount()-1);
                        if (isBottom) {
                            //绝对的底部 Absolute bottom
                            autoLoadMore();
                        }
//                        else if(lastChild!=null){
//                            //最后一个元素可见，但不一定完全可见 The last element is visible,
//                            but not necessarily completely visible
//                            autoLoadMore();
//                        }
                    }
                });
            }
        }
    }

    /**
     * 根据当前View的滚动状态来设定 {@link #isBottom}
     * 的值，每次都需要在触摸事件中第一个执行，这样可以判断出当前应该是滚动View，还是应该进行上拉。
     * Set the value of bottom based on the current scrolling state of the View.{@link #isBottom}
     * The value of needs to be executed first in the touch event each time, so as to determine
     * whether the current should be scrolling View or scrolling up.
     */
    private void judgeIsBottom() {
        if (mView instanceof AbsListView) {
            AbsListView absListView = (AbsListView) mView;
            //返回的是当前屏幕中的第最后一个子view，非整个列表
            // Returns the last child view in the current screen, not the entire list/
            View lastChild = absListView.getChildAt(absListView.getLastVisiblePosition() - absListView.getFirstVisiblePosition());
            if (lastChild != null) {
                //不必完全可见，当前屏幕中最后一个可见的子view在整个列表的位置
                // It is not necessary to be fully visible, the last visible sub view on the current
                // screen is located in the entire list.
                int lastVisiblePos = absListView.getLastVisiblePosition();
                if (lastVisiblePos == absListView.getAdapter().getCount() - 1 && lastChild.getBottom() == absListView.getMeasuredHeight() - mView.getPaddingBottom()) {
                    // 如果最后一个元素的下边缘，距离父布局值为view的高度，就说明View滚动到了最底部，此时应该允许上拉加载
                    // If the lower edge of the last element is at a height of view from the parent layout value,
                    // it indicates that the View has scrolled to the bottom, and pull-up loading should be allowed
                    // at this time.
                    isBottom = true;
                } else {
                    isBottom = false;
                }
            } else {
                // 如果View中没有元素，也应该允许下拉刷新，但不允许上拉
                // If there are no elements in the View, pull-down refresh should also be allowed,
                // but pull-up is not allowed.
                isBottom = false;
            }
        } else if (mView instanceof RecyclerView) {
            RecyclerView recyclerView = (RecyclerView) mView;
            //lastChild不必须完全可见 LastChild does not have to be fully visible
            View lastChild = recyclerView.getLayoutManager().findViewByPosition(recyclerView.getAdapter().getItemCount() - 1);
            //返回的是当前屏幕中的第一个子view，非整个列表
            // Returns the first child view in the current screen, not the entire list.
            View firstVisibleChild = recyclerView.getChildAt(0);
//            if(lastChild!=null){
//                Log.e("tianbin",lastChild.getBottom()+"==="+recyclerView.getChildAt(0).getTop()+"==="+recyclerView.getLayoutManager().getDecoratedBottom(lastChild));
//            }else{
//                Log.e("tianbin","+++++++++");
//            }
            if (firstVisibleChild != null) {
                if (lastChild != null &&
                        recyclerView.getLayoutManager().getDecoratedBottom(lastChild) == recyclerView.getMeasuredHeight() - mView.getPaddingBottom()) {
//                    Log.e(TAG, "judgeIsBottom: "+"==================" );
                    isBottom = true;
                } else {
                    isBottom = false;
                }
            } else {
                //没有元素也允许刷新，but不允许上拉
                // Allow refresh without elements, but do not allow pull up.
                isBottom = false;
            }
        } else if (mView instanceof ViewGroup) {
            View firstChild = ((ViewGroup) mView).getChildAt(0);
            if (firstChild instanceof AbsListView) {
                AbsListView absListView = (AbsListView) firstChild;
                //返回的是当前屏幕中的第最后一个子view，非整个列表
                // Returns the last child view in the current screen, not the entire list.
                View lastChild = absListView.getChildAt(absListView.getLastVisiblePosition() - absListView.getFirstVisiblePosition());
                if (lastChild != null) {
                    //不必完全可见，当前屏幕中最后一个可见的子view在整个列表的位置
                    // It is not necessary to be fully visible, the last visible sub view on the current screen is located in the entire list.
                    int lastVisiblePos = absListView.getLastVisiblePosition();
                    if (lastVisiblePos == absListView.getAdapter().getCount() - 1 && lastChild.getBottom() == absListView.getMeasuredHeight() - firstChild.getPaddingBottom()) {
                        // 如果最后一个元素的下边缘，距离父布局值为view的高度，就说明View滚动到了最底部，此时应该允许上拉加载
                        // If the lower edge of the last element is at a height of view from the parent layout value,
                        // it indicates that the View has scrolled to the bottom, and pull-up loading should be allowed
                        // at this time.
                        isBottom = true;
                    } else {
                        isBottom = false;
                    }
                } else {
                    // 如果View中没有元素，也应该允许下拉刷新，但不允许上拉
                    // If there are no elements in the View, pull-down refresh should also be allowed,
                    // but pull-up is not allowed.
                    isBottom = false;
                }
            } else if (firstChild instanceof RecyclerView) {
                RecyclerView recyclerView = (RecyclerView) firstChild;
                //lastChild不必须完全可见 LastChild does not have to be fully visible.
                View lastChild = recyclerView.getLayoutManager().findViewByPosition(recyclerView.getAdapter().getItemCount() - 1);
                //返回的是当前屏幕中的第一个子view，非整个列表
                // Returns the first child view in the current screen, not the entire list.
                View firstVisibleChild = recyclerView.getChildAt(0);
                if (firstVisibleChild != null) {
                    if (lastChild != null &&
                            recyclerView.getLayoutManager().getDecoratedBottom(lastChild) == recyclerView.getMeasuredHeight() - firstChild.getPaddingBottom()) {
                        isBottom = true;
                    } else {
                        isBottom = false;
                    }
                } else {
                    //没有元素也允许刷新，but不允许上拉
                    // Allow refresh without elements, but do not allow pull up.
                    isBottom = false;
                }
            }
        } else {
            isBottom = true;
        }
    }

    /**
     * 根据当前View的滚动状态来设定 {@link #isTop}
     * 的值，每次都需要在触摸事件中第一个执行，这样可以判断出当前应该是滚动View，还是应该进行下拉。
     * Set the value of top based on the current scrolling state of the View.{@link #isTop}
     * The value of needs to be executed first in the touch event each time, so as to determine
     * whether the current should be scrolling through the View or scrolling down.
     */
    private void judgeIsTop() {
        if (mView instanceof AbsListView) {
            AbsListView absListView = (AbsListView) mView;
            //返回的是当前屏幕中的第一个子view，非整个列表
            // Returns the first child view in the current screen, not the entire list
            View firstChild = absListView.getChildAt(0);
            if (firstChild != null) {
                //不必完全可见，当前屏幕中第一个可见的子view在整个列表的位置
                // It is not necessary to be fully visible. The first visible sub view on the current
                // screen is located in the entire list.
                int firstVisiblePos = absListView.getFirstVisiblePosition();
                if (firstVisiblePos == 0 && firstChild.getTop() - mView.getPaddingTop() == 0) {
                    // 如果首个元素的上边缘，距离父布局值为0，就说明ListView滚动到了最顶部，此时应该允许下拉刷新
                    // If the distance from the top edge of the first element to the parent layout value is 0, it indicates that the ListView has scrolled to the top, and drop-down refresh should be allowed at this time
                    isTop = true;
                } else {
                    isTop = false;
                }
            } else {
                // 如果ListView中没有元素，也应该允许下拉刷新
                // If there are no elements in the ListView, dropdown refresh should also be allowed.
                isTop = true;
            }
        } else if (mView instanceof RecyclerView) {
            RecyclerView recyclerView = (RecyclerView) mView;
            //firstChild不必须完全可见
            // FirstChild does not have to be fully visible。
            View firstChild = recyclerView.getLayoutManager().findViewByPosition(0);
            View firstVisibleChild = recyclerView.getChildAt(0);
            //返回的是当前屏幕中的第一个子view，非整个列表
            // Returns the first child view in the current screen, not the entire list
            if (firstVisibleChild != null) {
                if (firstChild != null && recyclerView.getLayoutManager().getDecoratedTop(firstChild) - mView.getPaddingTop() == 0) {
                    isTop = true;
                } else {
                    isTop = false;
                }
            } else {
                //没有元素也允许刷新 Allow refresh without elements
                isTop = true;
            }
        } else if (mView instanceof ViewGroup) {
            View first = ((ViewGroup) mView).getChildAt(0);
            isNeedForceRefresh = (first.getVisibility() != VISIBLE);
            if (first instanceof RecyclerView) {
                RecyclerView recyclerView = (RecyclerView) first;
                //firstChild不必须完全可见
                // FirstChild does not have to be fully visible。
                View firstChild = recyclerView.getLayoutManager().findViewByPosition(0);
                //返回的是当前屏幕中的第一个子view，非整个列表
                // Returns the first child view in the current screen, not the entire list
                View firstVisibleChild = recyclerView.getChildAt(0);
                if (firstVisibleChild != null) {
                    if (firstChild != null && recyclerView.getLayoutManager().getDecoratedTop(firstChild) - first.getPaddingTop() == 0) {
                        isTop = true;
                    } else {
                        isTop = false;
                    }
                } else {
                    //没有元素也允许刷新 Allow refresh without elements
                    isTop = true;
                }
            } else if (first instanceof AbsListView) {
                AbsListView absListView = (AbsListView) first;
                //返回的是当前屏幕中的第一个子view，非整个列表
                // Returns the first child view in the current screen, not the entire list
                View firstChild = absListView.getChildAt(0);
                if (firstChild != null) {
                    //不必完全可见，当前屏幕中第一个可见的子view在整个列表的位置
                    // It is not necessary to be fully visible. The first visible sub view on the current
                    // screen is located in the entire list.
                    int firstVisiblePos = absListView.getFirstVisiblePosition();
                    if (firstVisiblePos == 0 && firstChild.getTop() - first.getPaddingTop() == 0) {
                        // 如果首个元素的上边缘，距离父布局值为0，就说明ListView滚动到了最顶部，此时应该允许下拉刷新
                        // If the distance from the top edge of the first element to the parent layout value is 0,
                        // it indicates that the ListView has scrolled to the top, and drop-down refresh should be
                        // allowed at this time。
                        isTop = true;
                    } else {
                        isTop = false;
                    }
                } else {
                    // 如果ListView中没有元素，也应该允许下拉刷新
                    // If there are no elements in the ListView, dropdown refresh should also be allowed.
                    isTop = true;
                }
            }
        } else {
            isTop = true;
        }
    }

    /**
     * 刷新下拉头中上次更新时间的文字描述。
     * Refresh the text description of the last update time in the dropdown header.
     */
    private void refreshUpdatedAtValue() {
        lastUpdateTime = preferences.getLong(UPDATED_AT + mId, -1);
        long currentTime = System.currentTimeMillis();
        long timePassed = currentTime - lastUpdateTime;
        long timeIntoFormat;
        String updateAtValue;
        if (lastUpdateTime == -1) {
            updateAtValue = getResources().getString(R.string.not_updated_yet);
        } else if (timePassed < 0) {
            updateAtValue = getResources().getString(R.string.time_error);
        } else if (timePassed < ONE_MINUTE) {
            updateAtValue = getResources().getString(R.string.updated_just_now);
        } else if (timePassed < ONE_HOUR) {
            timeIntoFormat = timePassed / ONE_MINUTE;
            String value = timeIntoFormat + "分钟";
            updateAtValue = String.format(getResources().getString(R.string.updated_at), value);
        } else if (timePassed < ONE_DAY) {
            timeIntoFormat = timePassed / ONE_HOUR;
            String value = timeIntoFormat + "小时";
            updateAtValue = String.format(getResources().getString(R.string.updated_at), value);
        } else if (timePassed < ONE_MONTH) {
            timeIntoFormat = timePassed / ONE_DAY;
            String value = timeIntoFormat + "天";
            updateAtValue = String.format(getResources().getString(R.string.updated_at), value);
        } else if (timePassed < ONE_YEAR) {
            timeIntoFormat = timePassed / ONE_MONTH;
            String value = timeIntoFormat + "个月";
            updateAtValue = String.format(getResources().getString(R.string.updated_at), value);
        } else {
            timeIntoFormat = timePassed / ONE_YEAR;
            String value = timeIntoFormat + "年";
            updateAtValue = String.format(getResources().getString(R.string.updated_at), value);
        }
        updateAt.setVisibility(View.VISIBLE);
        updateAt.setText(updateAtValue);
    }

    /**
     * 更新底部信息
     * Update footer view
     */
    private void updateFooterView() {
        if (currentFooterStatus == STATUS_LOAD_NORMAL) {
            tvLoadMore.setText(getResources().getString(R.string.load_more_normal));
            pbFooter.setVisibility(View.GONE);
            footer.setVisibility(GONE);
        } else if (currentFooterStatus == STATUS_LOADING) {
            tvLoadMore.setText(getResources().getString(R.string.load_more_loading));
            pbFooter.setVisibility(View.VISIBLE);
            footer.setVisibility(VISIBLE);
        }
    }

    /**
     * 更新下拉头中的信息
     * Update header view
     */
    private void updateHeaderView() {
        ((View) arrow.getParent()).setVisibility(View.VISIBLE);
        if (lastStatus != currentStatus && lastStatus != STATUS_REFRESH_FINISHED) {
            if (currentStatus == STATUS_PULL_TO_REFRESH) {
                description.setText(getResources().getString(R.string.pull_to_refresh));
                arrow.setVisibility(View.VISIBLE);
                progressBar.setVisibility(View.GONE);
                rotateArrow();
            } else if (currentStatus == STATUS_RELEASE_TO_REFRESH) {
                description.setText(getResources().getString(R.string.release_to_refresh));
                arrow.setVisibility(View.VISIBLE);
                progressBar.setVisibility(View.GONE);
                rotateArrow();
            } else if (currentStatus == STATUS_REFRESHING) {
                description.setText(getResources().getString(R.string.refreshing));
                progressBar.setVisibility(View.VISIBLE);
                arrow.clearAnimation();
                arrow.setVisibility(View.GONE);
            }
        } else if (currentStatus == STATUS_REFRESH_FINISHED && lastStatus == STATUS_REFRESH_FINISHED) {
            //说明刷新完成或者第一次进入 Refresh completed or first time entered.
            description.setText(getResources().getString(R.string.pull_to_refresh));
            arrow.clearAnimation();
            arrow.setVisibility(View.VISIBLE);
            progressBar.setVisibility(View.GONE);
        } else if (autoRefresh)  {
            //自动刷新单独处理 Automatically refresh and handle separately.
            description.setText(getResources().getString(R.string.refreshing));
            progressBar.setVisibility(View.VISIBLE);
            arrow.clearAnimation();
            arrow.setVisibility(View.GONE);
        }
        refreshUpdatedAtValue();
    }

    /**
     * 根据当前的状态来旋转箭头。
     * Rotate arrow
     */
    private void rotateArrow() {
        float pivotX = arrow.getWidth() / 2f;
        float pivotY = arrow.getHeight() / 2f;
        float fromDegrees = 0f;
        float toDegrees = 0f;
        if (currentStatus == STATUS_PULL_TO_REFRESH) {
            fromDegrees = 180f;
            toDegrees = 360f;
        } else if (currentStatus == STATUS_RELEASE_TO_REFRESH) {
            fromDegrees = 0f;
            toDegrees = 180f;
        }
        RotateAnimation animation = new RotateAnimation(fromDegrees, toDegrees, pivotX, pivotY);
        animation.setDuration(300);
        animation.setFillAfter(true);
        arrow.startAnimation(animation);
    }

    public boolean isCanLoadMore() {
        return canLoadMore;
    }

    public void setCanLoadMore(boolean canLoadMore) {
        this.canLoadMore = canLoadMore;
    }

    public boolean isCanRefresh() {
        return canRefresh;
    }

    public void setCanRefresh(boolean canRefresh) {
        this.canRefresh = canRefresh;
    }

    public boolean isCanAutoLoadMore() {
        return canAutoLoadMore;
    }

    public void setCanAutoLoadMore(boolean canAutoLoadMore) {
        this.canAutoLoadMore = canAutoLoadMore;
    }

    public boolean isRefreshing() {
        return isRefreshing;
    }

    public boolean isLoading() {
        return isLoading;
    }


    /**
     * The interface Pull to refresh and push to load more listener.
     * 监听器，使用刷新和加载的地方应该注册此监听器来获取刷新回调。
     */
    public interface PullToRefreshAndPushToLoadMoreListener {

        /**
         * 刷新时会去回调此方法，在方法内编写具体的刷新逻辑。注意此方法是在主线程中调用的， 需要另开线程来进行耗时操作。
         * When refreshing, this method will be called back and specific refresh logic will be written within
         * the method. Note that this method is called in the main thread, and another thread needs to be opened
         * for time-consuming operations.
         */
        void onRefresh();

        /**
         * 加载更多时会去回调此方法，在方法内编写具体的加载更多逻辑。注意此方法是在主线程中调用的， 需要另开线程来进行耗时操作。
         * When loading more, this method will be called back and specific refresh logic will be written within
         * the method. Note that this method is called in the main thread, and another thread needs to be opened
         * for time-consuming operations.
         */
        void onLoadMore();

    }

    /**
     * 给控件注册一个监听器。
     * Set OnRefreshAndLoadMoreListener
     *
     * @param listener the listener 监听器的实现。
     * @param id       为了防止不同界面的下拉刷新在上次更新时间上互相有冲突， 请不同界面在注册下拉刷新监听器时一定要传入不同的id。
     *                 如果不用时间则可以不传递此参数
     *                 To prevent conflicts between the pull-down refresh times of different interfaces during the last update,
     *                 please make sure to pass in different IDs when registering pull-down refresh listeners for different
     *                 interfaces.If time is not required, this parameter can be left blank.
     */
    public void setOnRefreshAndLoadMoreListener(PullToRefreshAndPushToLoadMoreListener listener, int id) {
        mListener = listener;
        mId = id;
    }

    /**
     * 给控件注册一个监听器。
     * Set OnRefreshAndLoadMoreListener
     *
     * @param listener the listener 监听器的实现。
     */
    public void setOnRefreshAndLoadMoreListener(PullToRefreshAndPushToLoadMoreListener listener) {
        setOnRefreshAndLoadMoreListener(listener, mId);
    }

    private int dp2px(int dp) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, mContext.getResources().getDisplayMetrics());
    }


    /**
     * 取消长按事件的回调
     * Remove long click
     */
    private void removeLongClick() {
        if (mView != null) {
            mView.cancelLongPress();
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        removeCallbacks(mHideHeader);
        removeCallbacks(mRefreshSuccess);
        removeCallbacks(mHideFooter);
    }

    private Runnable mHideHeader = new Runnable() {
        @Override
        public void run() {
            hideHeader(true);
        }
    };

    private Runnable mRefreshSuccess = new Runnable() {
        @Override
        public void run() {
            description.setText(getResources().getString(R.string.refresh_success));
            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) description.getLayoutParams();
            layoutParams.gravity = Gravity.CENTER;
            description.setLayoutParams(layoutParams);
            ((View) arrow.getParent()).setVisibility(View.GONE);
            updateAt.setVisibility(View.GONE);
        }
    };

    private Runnable mHideFooter = new Runnable() {
        @Override
        public void run() {
            hideFooter();
        }
    };
}
