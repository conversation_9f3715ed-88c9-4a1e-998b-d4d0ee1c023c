package com.meishe.engine.util;

import android.graphics.Point;

import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsAudioResolution;
import com.meicam.sdk.NvsRational;
import com.meicam.sdk.NvsSize;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsVideoResolution;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoFx;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.logic.utils.UMengUtils;

import static com.meishe.base.constants.Constants.TRACK_INDEX_MAIN;
import static com.meishe.engine.bean.CommonData.TIMELINE_RESOLUTION_VALUE;

/**
 * The type Timeline util.
 * 、时间轴工具类
 */
public class TimelineUtil {

    private static String TAG = TimelineUtil.class.getSimpleName();

    /**
     * Create timeline nvs timeline.
     * 创建时间线nvs时间线
     *
     * @return the nvs timeline
     */
    /*public static NvsTimeline createTimeline() {
        NvsTimeline timeline = newTimeline(TimelineData.getInstance().getVideoResolution());
        if (timeline == null) {
            LogUtils.e("failed to create timeline");
            return null;
        }
        if (!buildVideoTrack(timeline)) {
            return null;
        }
        setTimelineData(timeline);


        return timeline;
    }*/

    /**
     * 铺设timeline上的信息，除视频轨道以外。
     * Sets timeline data.
     *
     * @param timeline the timeline
     */
   /* public static void setTimelineData(NvsTimeline timeline) {
        if (timeline == null) {
            return;
        }
        *//*
     * 时间轴数据
     * Timeline data
     * *//*
        applyTheme(timeline);
        *//*
     * 添加timeline特效
     * Add Timeline effects
     * *//*
        removeAllTimelineEffect(timeline);
        addTimeLineEffect(timeline);

        *//*
     * 添加音乐
     * Add Music
     * *//*
        buildTimelineMusic(timeline);

        *//*
     * 添加timeline滤镜
     * Add the Timeline filter
     * *//*
        MeicamVideoFx filterFx = TimelineData.getInstance().getFilterFx();
        if (filterFx != null) {
            buildTimelineFilter(timeline, filterFx);
        }


        setAdjustEffects(timeline);

        *//*
     * 添加clip滤镜
     * Add a clip filter
     * *//*
        buildClipFilter(timeline);


        removeAllSticker(timeline);
        removeAllCaption(timeline);
        removeAllCompoundCaption(timeline);
        setSitckerCaptionObject(timeline);

        *//**
     * 创建水印
     * Create a watermark
     *//*
        buildTimelineWaterMark(timeline);
        *//**
     * 创建马赛克、模糊特效
     * Create Mosaic and blur effects
     *//*
        buildTimelineMasicEffect(timeline);
    }
*/

    /**
     * 设置调节功能的参数
     * Sets adjust effects.
     *
     * @param timeline the timeline
     */
    /*public static void setAdjustEffects(NvsTimeline timeline) {
        if (timeline == null) {
            return;
        }
        MeicamAdjustData meicamAdjustData = TimelineData.getInstance().getMeicamAdjustData();
        if (meicamAdjustData == null) {
            return;
        }
        setTimelineAdjustEffect(timeline, meicamAdjustData.getAmount(), NvsConstants.ADJUST_AMOUNT, NvsConstants.FX_SHARPEN);
        setTimelineAdjustEffect(timeline, meicamAdjustData.getDegree(), NvsConstants.ADJUST_DEGREE, NvsConstants.FX_VIGNETTE);
        setTimelineAdjustEffect(timeline, meicamAdjustData.getBlackPoint(), NvsConstants.ADJUST_BLACKPOINT, NvsConstants.ADJUST_TYPE_BASIC_IMAGE_ADJUST);
        setTimelineAdjustEffect(timeline, meicamAdjustData.getTint(), NvsConstants.ADJUST_TINT, NvsConstants.ADJUST_TINT);
        setTimelineAdjustEffect(timeline, meicamAdjustData.getTemperature(), NvsConstants.ADJUST_TEMPERATURE, NvsConstants.ADJUST_TINT);
        setTimelineAdjustEffect(timeline, meicamAdjustData.getShadow(), NvsConstants.ADJUST_SHADOW, NvsConstants.ADJUST_TYPE_BASIC_IMAGE_ADJUST);
        setTimelineAdjustEffect(timeline, meicamAdjustData.getHighlight(), NvsConstants.ADJUST_HIGHTLIGHT, NvsConstants.ADJUST_TYPE_BASIC_IMAGE_ADJUST);
        setTimelineAdjustEffect(timeline, meicamAdjustData.getSaturation(), NvsConstants.ADJUST_SATURATION, NvsConstants.ADJUST_TYPE_BASIC_IMAGE_ADJUST);
        setTimelineAdjustEffect(timeline, meicamAdjustData.getContrast(), NvsConstants.ADJUST_CONTRAST, NvsConstants.ADJUST_TYPE_BASIC_IMAGE_ADJUST);
        setTimelineAdjustEffect(timeline, meicamAdjustData.getBrightness(), NvsConstants.ADJUST_BRIGHTNESS, NvsConstants.ADJUST_TYPE_BASIC_IMAGE_ADJUST);
    }


    private static void setTimelineAdjustEffect(NvsTimeline timeline, float adjustData, String adjustKey, String attachmentKey) {
        NvsTimelineVideoFx timenlineSharpenVideoFx = null;
        NvsTimelineVideoFx nvsTimelineVideoFx = timeline.getFirstTimelineVideoFx();

        while (nvsTimelineVideoFx != null) {
            Object attachment = nvsTimelineVideoFx.getAttachment(attachmentKey);
            if (attachment != null && attachmentKey.equals(attachment)) {
                timenlineSharpenVideoFx = nvsTimelineVideoFx;
                break;
            }
            nvsTimelineVideoFx = timeline.getNextTimelineVideoFx(nvsTimelineVideoFx);
        }
        if (timenlineSharpenVideoFx != null) {
            timenlineSharpenVideoFx.setFloatVal(adjustKey, adjustData);
            timenlineSharpenVideoFx.setBooleanVal(NvsConstants.KEY_VIDEO_MODE, true);
        } else {
            timenlineSharpenVideoFx = timeline.addBuiltinTimelineVideoFx(0, timeline.getDuration(), attachmentKey);
            if (timenlineSharpenVideoFx != null) {
                timenlineSharpenVideoFx.setAttachment(attachmentKey, attachmentKey);
                timenlineSharpenVideoFx.setFloatVal(adjustKey, adjustData);
                timenlineSharpenVideoFx.setBooleanVal(NvsConstants.KEY_VIDEO_MODE, true);
            }
        }
    }

*/
    /**
     * 内建特效马赛克模糊
     * buildIn fx mosaic
     */
    public static final String MOSAICNAME = "Mosaic";
    /**
     * 内建特效高斯模糊
     * buildIn fx Gaussian Blur
     */
    public static final String BLURNAME = "Gaussian Blur";

    /*private static void buildTimelineMasicEffect(NvsTimeline timeline) {
        List<MeicamTimelineVideoFxClip> meicamTimelineVideoFxClips = TimelineData.getInstance().getMeicamTimelineVideoFxClipList();
        if (meicamTimelineVideoFxClips.size() > 0) {
            MeicamTimelineVideoFxClip meicamTimelineVideoFxClip = meicamTimelineVideoFxClips.get(0);
            NvsTimelineVideoFx nvsTimelineVideoFx = meicamTimelineVideoFxClip.bindToTimeline(timeline);
            if (nvsTimelineVideoFx == null) {
                return;
            }
            nvsTimelineVideoFx.setRegional(true);
            nvsTimelineVideoFx.setRegionalFeatherWidth(0f);
            meicamTimelineVideoFxClip.setRegional(true);
            if (meicamTimelineVideoFxClip.getDesc().equals(MOSAICNAME)) {
                nvsTimelineVideoFx.setFilterIntensity(meicamTimelineVideoFxClip.getIntensity());
                List<MeicamFxParam> meicamFxParams = meicamTimelineVideoFxClip.getMeicamFxParamList();
                MeicamFxParam meicamFxParamFirst = null, meicamFxParamSecond = null;
                for (MeicamFxParam param : meicamFxParams) {
                    if ("float".equals(param.getType())) {
                        meicamFxParamFirst = param;
                    } else if (TYPE_OBJECT.equals(param.getType())) {
                        meicamFxParamSecond = param;
                    }
                }
                nvsTimelineVideoFx.setFloatVal(meicamFxParamFirst.getKey(), Double.parseDouble(meicamFxParamFirst.getValue().toString()));
                float[] point = new float[8];
                Object value = meicamFxParamSecond.getValue();
                if (value instanceof ArrayList) {
                    ArrayList<Double> valueList = (ArrayList<Double>) value;
                    for (int i = 0; i < valueList.size(); i++) {
                        point[i] = Float.parseFloat(valueList.get(i).toString());
                    }
                    nvsTimelineVideoFx.setRegion(point);
                } else {
                    nvsTimelineVideoFx.setRegion((float[]) value);
                }

            } else if (meicamTimelineVideoFxClip.getDesc().equals(BLURNAME)) {
                List<MeicamFxParam> meicamFxParams = meicamTimelineVideoFxClip.getMeicamFxParamList();
                if (meicamFxParams == null || meicamFxParams.size() == 0) {
                    return;
                }
                MeicamFxParam meicamFxParam = null, meicamFxParamSecond = null;
                for (MeicamFxParam param : meicamFxParams) {
                    if ("float".equals(param.getType())) {
                        meicamFxParamSecond = param;
                    } else if (TYPE_OBJECT.equals(param.getType())) {
                        meicamFxParam = param;
                    }
                }
                nvsTimelineVideoFx.setFilterIntensity(1);
                if (meicamFxParamSecond != null) {
                    nvsTimelineVideoFx.setFloatVal(meicamFxParamSecond.getKey(), Double.parseDouble(meicamFxParamSecond.getValue().toString()));
                }
                if (meicamFxParam != null) {
                    float[] point = new float[8];
                    Object value = meicamFxParam.getValue();
                    if (value instanceof ArrayList) {
                        ArrayList<Double> valueList = (ArrayList<Double>) value;
                        if (valueList == null || valueList.size() == 0) {
                            LogUtils.e("setRegion  valueList.size() == 0");
                            return;
                        }
                        for (int i = 0; i < valueList.size(); i++) {
                            point[i] = Float.parseFloat(valueList.get(i).toString());
                        }
                        nvsTimelineVideoFx.setRegion(point);
                    } else {
                        nvsTimelineVideoFx.setRegion((float[]) value);
                    }
                }

            }
        }
    }*/

    /**
     * 创建水印
     * Create a watermark
     *
     * @param timeline
     */
   /* private static void buildTimelineWaterMark(NvsTimeline timeline) {
        MeicamWaterMark waterMark = TimelineData.getInstance().getMeicamWaterMark();
        if (waterMark == null) {
            return;
        }
        timeline.deleteWatermark();
        if (TextUtils.isEmpty(waterMark.getWatermarkFilePath())) {
            return;
        }
        timeline.addWatermark(waterMark.getWatermarkFilePath(), waterMark.getDisplayWidth(), waterMark.getDisplayHeight(), 1
                , NvsTimeline.NvsTimelineWatermarkPosition_TopLeft, waterMark.getMarginX(), waterMark.getMarginY());
    }*/

   /* private static void addTimeLineEffect(NvsTimeline timeline) {
        List<MeicamTimelineVideoFxTrack> videoFxTrackList = TimelineData.getInstance().getMeicamTimelineVideoFxTrackList();
        if (CommonUtils.isEmpty(videoFxTrackList)) {
            return;
        }
        List<ClipInfo<?>> clipInfoList = videoFxTrackList.get(0).getClipInfoList();
        if (CommonUtils.isEmpty(clipInfoList)) {
            return;
        }
        for (ClipInfo clipInfo : clipInfoList) {
            ((MeicamTimelineVideoFxClip) clipInfo).bindToTimeline(timeline);
        }
    }*/

    /**
     * 添加时间线上的音频信息
     * Build timeline music boolean.
     *
     * @param timeline the timeline
     * @return the boolean。如果音频数据为空或者添加音频轨道失败则返回false
     */
    /*public static boolean buildTimelineMusic(NvsTimeline timeline) {

        int audioTrackCount = timeline.audioTrackCount();
        for (int index = audioTrackCount - 1; index >= 0; index--) {
            timeline.removeAudioTrack(index);
        }
        *//*
     * 去掉音乐之后，要把已经应用的主题中的音乐还原
     * After removing the music, you need to restore the music in the theme you have applied
     * *//*
        MeicamTheme meicamTheme = TimelineData.getInstance().getMeicamTheme();
        if (meicamTheme != null) {
            String themePackageId = meicamTheme.getThemePackageId();
            if (!TextUtils.isEmpty(themePackageId)) {
                timeline.setThemeMusicVolumeGain(1.0f, 1.0f);
            }
        }

        List<MeicamAudioTrack> meicamAudioTrackList = TimelineData.getInstance().getMeicamAudioTrackList();
        if (CommonUtils.isEmpty(meicamAudioTrackList)) {
            return false;
        }
        for (MeicamAudioTrack meicamAudioTrack : meicamAudioTrackList) {
            NvsAudioTrack audioTrack = meicamAudioTrack.bindToTimeline(timeline);
            if (audioTrack == null) {
                LogUtils.e("buildTimelineMusic: fail to create audio track");
                return false;
            }
            List<ClipInfo<?>> meicamAudioClipList = meicamAudioTrack.getClipInfoList();

            for (int j = 0; j < meicamAudioClipList.size(); j++) {
                MeicamAudioClip meicamAudioClip = (MeicamAudioClip) meicamAudioClipList.get(j);
                meicamAudioClip.bindToTimeline(audioTrack);
            }

        }

        return true;
    }*/

    /**
     * 设置时间线上的贴纸和字幕
     * Sets sitcker caption object.
     *
     * @param timeline the timeline
     */
  /*  public static void setSitckerCaptionObject(NvsTimeline timeline) {
        List<MeicamStickerCaptionTrack> meicamStickerCaptionTrackList = com.meishe.engine.bean.TimelineData.getInstance().getMeicamStickerCaptionTrackList();
        for (MeicamStickerCaptionTrack meicamStickerCaptionTrack : meicamStickerCaptionTrackList) {
            List<ClipInfo<?>> clipInfoList = meicamStickerCaptionTrack.getClipInfoList();
            for (ClipInfo clipInfo : clipInfoList) {
                if (clipInfo instanceof MeicamStickerClip) {
                    MeicamStickerClip meicamStickerClip = (MeicamStickerClip) clipInfo;
                    meicamStickerClip.bindToTimeline(timeline);
                } else if (clipInfo instanceof MeicamCaptionClip) {
                    MeicamCaptionClip meicamCaptionClip = (MeicamCaptionClip) clipInfo;
                    meicamCaptionClip.bindToTimeline(timeline);
                } else if (clipInfo instanceof MeicamCompoundCaptionClip) {
                    MeicamCompoundCaptionClip meicamCompoundCaptionClip = (MeicamCompoundCaptionClip) clipInfo;
                    meicamCompoundCaptionClip.bindToTimeline(timeline);
                }
            }
        }
    }*/

  /*  private static void removeAllSticker(NvsTimeline timeline) {
        if (timeline == null) {
            Log.e(TAG, "removeAllSticker: timeline is null");
            return;
        }
        NvsTimelineAnimatedSticker sticker = timeline.getFirstAnimatedSticker();
        while (sticker != null) {
            sticker = timeline.removeAnimatedSticker(sticker);
        }
    }*/

   /* private static void removeAllTimelineEffect(NvsTimeline timeline) {
        if (timeline == null) {
            Log.e(TAG, "removeAllSticker: timeline is null");
            return;
        }
        NvsTimelineVideoFx videoFx = timeline.getFirstTimelineVideoFx();
        while (videoFx != null) {
            videoFx = timeline.removeTimelineVideoFx(videoFx);
        }
    }*/

  /*  private static void removeAllCaption(NvsTimeline timeline) {
        if (timeline == null) {
            Log.e(TAG, "removeAllSticker: timeline is null");
            return;
        }
        NvsTimelineCaption deleteCaption = timeline.getFirstCaption();
        while (deleteCaption != null) {
            int capCategory = deleteCaption.getCategory();
            LogUtils.e("capCategory = " + capCategory);
            int roleTheme = deleteCaption.getRoleInTheme();
            if (capCategory == NvsTimelineCaption.THEME_CATEGORY
                    && roleTheme != NvsTimelineCaption.ROLE_IN_THEME_GENERAL) {//主题字幕不作删除
                deleteCaption = timeline.getNextCaption(deleteCaption);
            } else {
                deleteCaption = timeline.removeCaption(deleteCaption);
            }
        }
    }*/

    /*private static void removeAllCompoundCaption(NvsTimeline timeline) {
        if (timeline == null) {
            Log.e(TAG, "removeAllSticker: timeline is null");
            return;
        }
        NvsTimelineCompoundCaption compoundCaption = timeline.getFirstCompoundCaption();
        while (compoundCaption != null) {
            compoundCaption = timeline.removeCompoundCaption(compoundCaption);
        }
    }*/


    /**
     * 铺设视频轨道
     * build video track
     *
     * @param timeline
     * @return 如果时间线为空或者视频轨道数据为空则返回false.
     * if timeline is null or video track list is empty will return false.
     */
  /*  private static boolean buildVideoTrack(NvsTimeline timeline) {
        if (timeline == null) {
            return false;
        }
        List<MeicamVideoTrack> videoTrackList = TimelineData.getInstance().getMeicamVideoTrackList();
        if (CommonUtils.isEmpty(videoTrackList)) {
            LogUtils.e("no track data!!!");
            return false;
        }
        return fillTrack(timeline, videoTrackList);
    }*/

    /**
     * 创建时间线
     * New timeline nvs timeline.
     *
     * @param firstVideoPath the first video path
     * @return the nvs timeline
     */
   /* public static NvsTimeline newTimeline(String firstVideoPath) {
        return newTimeline(getVideoEditResolutionByClip(firstVideoPath));
    }*/

    /**
     * 根据设置的宽高信息创建时间线
     * New timeline nvs timeline.
     *
     * @param videoResolution 视频分辨率   audioResolution:音频解析度
     * @return nvs timeline
     */
    public static NvsTimeline newTimeline(NvsVideoResolution videoResolution) {
        NvsStreamingContext context = NvsStreamingContext.getInstance();
        if (context == null) {
            context = EditorEngine.getInstance().initStreamContext();
            if (context == null) {
                LogUtils.e("failed to get streamingContext");
                UMengUtils.generateCustomLog("failed to get streamingContext");
                throw new NullPointerException();
            }
        }

        videoResolution.imagePAR = new NvsRational(1, 1);       //像素比（仅支持1:1）
        NvsRational videoFps = new NvsRational(CommonData.DEFAULT_TIMELINE_FPS, 1);             //帧率  每秒帧数越多所显示的动作就会越流畅
        NvsAudioResolution nvsAudioResolution = new NvsAudioResolution();
        nvsAudioResolution.sampleRate = 44100;                            //采样率
        nvsAudioResolution.channelCount = 2;                              //声道数
        return context.createTimeline(videoResolution, videoFps, nvsAudioResolution);
    }

    /**
     * SDK要求时间线的宽高需要满足：宽为4的倍数，高为2的倍数。
     * SDK requires that the width and height of the timeline should be: a multiple of 4 and a multiple of 2.
     *
     * @param resolution the resolution
     */
    public static void alignedResolution(NvsVideoResolution resolution) {
        if (resolution == null) {
            LogUtils.e("alignedResolution==null");
            UMengUtils.generateCustomLog("alignedResolution==null");
            return;
        }
        resolution.imageWidth = alignedData(resolution.imageWidth, 4);
        resolution.imageHeight = alignedData(resolution.imageHeight, 2);
    }

    /**
     * 整数对齐
     * Integer alignment
     *
     * @param data，源数据
     * @param num      对齐的数据
     * @return
     */
    private static int alignedData(int data, int num) {
        return data - data % num;
    }


    /**
     * 重新构建时间线
     * rebuild timeline
     *
     * @param timeline the timeline
     */
    /*public static void rebuildTimeline(MeicamTimeline timeline) {
        if (timeline == null) {
            return;
        }
        NvsVideoResolution videoResolution = TimelineData.getInstance().getVideoResolution();
        if (videoResolution != null) {
            timeline.changeVideoSize(videoResolution.imageWidth, videoResolution.imageHeight);
            timeline.setVideoResolution(videoResolution);
        }

        List<MeicamVideoTrack> videoTrackList = TimelineData.getInstance().getMeicamVideoTrackList();
        if (CommonUtils.isEmpty(videoTrackList)) {
            LogUtils.e("no track data!!!");
            return;
        }
        //重建timeline的时候要清除轨道数据
        int count = timeline.videoTrackCount();
        for (int index = count - 1; index >= 0; index--) {
            timeline.removeVideoTrack(index);
        }

        if (!fillTrack(timeline, videoTrackList)) {
            return;
        }
        setTimelineData(timeline);
    }*/

    /**
     * 根据视频轨道数据填充到时间线
     * Fill track boolean.
     *
     * @param timeline       the timeline
     * @param videoTrackList the video track list
     * @return the boolean
     */
  /*  public static boolean fillTrack(NvsTimeline timeline, List<MeicamVideoTrack> videoTrackList) {
        for (MeicamVideoTrack meicamVideoTrack : videoTrackList) {
            NvsVideoTrack track = meicamVideoTrack.bindToTimeline(timeline);
            if (track == null) {
                LogUtils.e("failed to append video track");
                return false;
            }
            List<ClipInfo<?>> clipInfoList = meicamVideoTrack.getClipInfoList();
            if (CommonUtils.isEmpty(clipInfoList)) {
                LogUtils.e("no clip data!!!");
                continue;
            }
            for (int index = 0; index < clipInfoList.size(); index++) {
                MeicamVideoClip clipInfo = (MeicamVideoClip) clipInfoList.get(index);
                clipInfo.addToTimeline(track);
            }

            setTransition(timeline, meicamVideoTrack.getTransitionInfoList());
        }
        return true;
    }*/

    /**
     * 添加全部转场特效
     * <p>
     * Add all transition effects
     *
     * @param timeline          the timeline
     * @param meicamTransitions the meicam transitions
     * @return transition
     */
   /* public static boolean setTransition(NvsTimeline timeline, List<MeicamTransition> meicamTransitions) {
        if (timeline == null) {
            return false;
        }

        NvsVideoTrack videoTrack = timeline.getVideoTrackByIndex(TRACK_INDEX_MAIN);
        if (videoTrack == null) {
            return false;
        }

        if (meicamTransitions == null) {
            return false;
        }

        int videoClipCount = videoTrack.getClipCount();
        if (videoClipCount <= 1) {
            return false;
        }
        *//*
     * 添加全部转场特效
     * Add all transition effects
     * *//*
        for (MeicamTransition transitionInfo : meicamTransitions) {
            if (transitionInfo == null) {
                continue;
            }
            transitionInfo.bindToTimeline(videoTrack);
        }

        return true;
    }*/

    /**
     * 清理所有的内建特效
     * <p>
     * Clear all build in transition boolean.
     *
     * @param timeline the timeline
     * @return the boolean
     */
  /*  public static boolean clearAllBuildInTransition(NvsTimeline timeline) {
        if (timeline == null) {
            return false;
        }

        NvsVideoTrack videoTrack = timeline.getVideoTrackByIndex(TRACK_INDEX_MAIN);
        if (videoTrack == null) {
            return false;
        }

        int videoClipCount = videoTrack.getClipCount();
        if (videoClipCount <= 1) {
            return false;
        }

        for (int i = 0; i < videoClipCount - 1; i++) {
            videoTrack.setBuiltinTransition(i, "");
        }
        return true;

    }*/

    /**
     * 时间线添加主题
     * <p>
     * Apply theme boolean.
     *
     * @param timeline the timeline
     * @return the boolean
     */
    /*public static boolean applyTheme(NvsTimeline timeline) {
        if (timeline == null) {
            return false;
        }
        *//*
     * 添加主题
     * Add Theme
     * *//*
        MeicamTheme meicamTheme = TimelineData.getInstance().getMeicamTheme();
        if (meicamTheme == null) {
            return false;
        }
        String themeId = meicamTheme.getThemePackageId();
        timeline.removeCurrentTheme();
        if (TextUtils.isEmpty(themeId)) {
            return false;
        }

        if (!timeline.applyTheme(themeId)) {
            Log.e(TAG, "failed to apply theme");
            return false;
        }

        timeline.setThemeMusicVolumeGain(1.0f, 1.0f);

        *//*
     * 新需求 添加主题删除所有增加的音频轨道
     * New requirements add theme remove all added audio tracks
     * *//*
        List<MeicamAudioTrack> meicamAudioTrackList = TimelineData.getInstance().getMeicamAudioTrackList();
        Iterator<MeicamAudioTrack> iterator = meicamAudioTrackList.iterator();
        while (iterator.hasNext()) {
            MeicamAudioTrack meicamAudioTrack = iterator.next();
            if (meicamAudioTrack == null) {
                continue;
            }
            NvsAudioTrack nvsAudioTrack = meicamAudioTrack.getObject();
            if (nvsAudioTrack == null) {
                continue;
            }
            timeline.removeAudioTrack(nvsAudioTrack.getIndex());
            iterator.remove();
        }
        return true;

    }*/

    /**
     * 时间线添加滤镜
     * <p></>
     * Build timeline filter boolean.
     *
     * @param timeline        the timeline
     * @param videoClipFxData the video clip fx data
     * @return the boolean
     */
  /*  public static boolean buildTimelineFilter(MeicamTimeline timeline, MeicamVideoFx videoClipFxData) {
        if (timeline == null) {
            return false;
        }
        MeicamVideoTrack meicamVideoTrack = timeline.getVideoTrack(TRACK_INDEX_MAIN);
        if (meicamVideoTrack == null) {
            return false;
        }
        int clipCount = meicamVideoTrack.getClipCount();
        if (clipCount > 0) {
            for (int index = 0; index < clipCount; index++) {
                MeicamVideoClip videoClip = meicamVideoTrack.getVideoClip(index);
                MeicamVideoFx cloneVideoFx = videoClipFxData.clone();
                videoClip.appendFilterFromFx(cloneVideoFx, false);
            }
        }
        return true;
    }*/

    /**
     * 时间线添加滤镜
     * <p></p>
     * Build clip filter boolean.
     *
     * @param timeline the timeline
     * @return the boolean。时间线为空或没有滤镜数据则返回空
     * Returns false if timeline is empty or no filter data
     */
  /*  public static boolean buildClipFilter(NvsTimeline timeline) {
        if (timeline == null) {
            return false;
        }
        List<MeicamVideoTrack> meicamVideoTrackList = TimelineData.getInstance().getMeicamVideoTrackList();
        if (CommonUtils.isEmpty(meicamVideoTrackList)) {
            return false;
        }
        for (MeicamVideoTrack meicamVideoTrack : meicamVideoTrackList) {
            List<ClipInfo<?>> clipInfoList = meicamVideoTrack.getClipInfoList();
            for (ClipInfo clipInfo : clipInfoList) {
                appendFilterFx((MeicamVideoClip) clipInfo, ((MeicamVideoClip) clipInfo).getVideoFxByType(MeicamVideoFx.SUB_TYPE_CLIP_FILTER));
            }
        }
        return true;
    }*/

    /**
     * 修改滤镜强度
     * <p></p>
     * Change timeline filter intensity boolean.
     *
     * @param timeline  the timeline
     * @param intensity the intensity
     * @return the boolean.时间线或轨道为空则返回false
     * False if the timeline or track is empty
     */
    public static boolean changeTimelineFilterIntensity(MeicamTimeline timeline, float intensity) {
        if (timeline == null) {
            return false;
        }

        MeicamVideoTrack meicamVideoTrack = timeline.getVideoTrack(TRACK_INDEX_MAIN);
        if (meicamVideoTrack == null) {
            return false;
        }
        int clipCount = meicamVideoTrack.getClipCount();
        if (clipCount > 0) {
            for (int index = 0; index < clipCount; index++) {
                MeicamVideoClip videoClip = meicamVideoTrack.getVideoClip(index);
                MeicamVideoFx videoFx = videoClip.getVideoFxByType(MeicamVideoFx.SubType.SUB_TYPE_TIMELINE_FILTER);
                if (videoFx != null) {
                    videoFx.setIntensity(intensity);
                }
            }
        }
        return true;
    }

    /*private static boolean removeAllVideoFx(NvsVideoClip videoClip) {
        if (videoClip == null) {
            return false;
        }

        int fxCount = videoClip.getFxCount();
        for (int i = 0; i < fxCount; i++) {
            NvsVideoFx fx = videoClip.getFxByIndex(i);
            if (fx == null) {
                continue;
            }

            String name = fx.getBuiltinVideoFxName();
            Log.e("===>", "fx name: " + name);
            if (name.equals(NvsConstants.FX_COLOR_PROPERTY) || name.equals(NvsConstants.FX_VIGNETTE) ||
                    name.equals(NvsConstants.FX_SHARPEN) || name.equals(NvsConstants.FX_TRANSFORM_2D)) {
                continue;
            }
            videoClip.removeFx(i);
            i--;
        }
        return true;
    }*/


//    /**
//     * 片段编辑页面时间线扩展API
//     * Create single clip timeline ext nvs timeline.
//     *
//     * @param filePath the file path
//     * @return the nvs timeline
//     */
//    public static NvsTimeline createSingleClipTimelineExt(String filePath) {
//        NvsVideoResolution resolution = TimelineUtil.getVideoEditResolutionByClip(filePath);
//        NvsTimeline timeline = newTimeline(resolution);
//        if (timeline == null) {
//            Log.e(TAG, "failed to create timeline");
//            return null;
//        }
//        buildSingleClipVideoTrackExt(timeline, filePath);
//        return timeline;
//    }
//
//    private static boolean buildSingleClipVideoTrackExt(NvsTimeline timeline, String filePath) {
//        if (timeline == null || filePath == null) {
//            return false;
//        }
//
//        NvsVideoTrack videoTrack = timeline.appendVideoTrack();
//        if (videoTrack == null) {
//            Log.e(TAG, "failed to append video track");
//            return false;
//        }
//        NvsVideoClip videoClip = videoTrack.appendClip(filePath);
//        if (videoClip == null) {
//            Log.e(TAG, "failed to append video clip");
//            return false;
//        }
//        videoClip.changeTrimOutPoint(8000000, true);
//        return true;
//    }

    /**
     * 通过视频片段路径获取需要的时间线分辨率信息
     * <P></P>
     * Gets video edit resolution by clip.
     *
     * @param path the path
     * @return the video edit resolution by clip
     */
    public static NvsVideoResolution getVideoEditResolutionByClip(String path) {

//        int compileRes = 1080;
//        SettingParameter parameter = GsonUtils.fromJson(PreferencesManager.get().getSettingParams(), SettingParameter.class);
//        if (parameter.getCompileResolution() == NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_720) {
//            compileRes = 720;
//        } else if (parameter.getCompileResolution() == NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_2160) {
//            compileRes = 2160;
//        }
//
//        int imageWidth = 720, imageHeight = 1080;
//        if (!TextUtils.isEmpty(path)) {
//            NvsAVFileInfo avFileInfo = NvsStreamingContext.getInstance().getAVFileInfo(path);
//            if (avFileInfo != null) {
//                NvsSize dimension = avFileInfo.getVideoStreamDimension(0);
//                int streamRotation = avFileInfo.getVideoStreamRotation(0);
//                imageWidth = dimension.width;
//                imageHeight = dimension.height;
//                if (streamRotation == 1 || streamRotation == 3) {
//                    imageWidth = dimension.height;
//                    imageHeight = dimension.width;
//                }
//            }
//        }
//
//        NvsVideoResolution videoEditRes = new NvsVideoResolution();
//        Point size = new Point();
//        float iamgeToTimelineSatio = 1.0F;
//        if (imageWidth > imageHeight) {
//            iamgeToTimelineSatio = compileRes * 1.0F / imageWidth;
//            size.set(compileRes, (int) (imageHeight * iamgeToTimelineSatio));
//        } else {
//            iamgeToTimelineSatio = compileRes * 1.0F / imageHeight;
//            size.set((int) (imageWidth * iamgeToTimelineSatio), compileRes);
//        }
//        videoEditRes.imageWidth = alignedData(size.x, 4);
//        videoEditRes.imageHeight = alignedData(size.y, 2);
//        LogUtils.d("getVideoEditResolution   ", videoEditRes.imageWidth + "     " + videoEditRes.imageHeight);
//        return videoEditRes;

        int imageWidth = 720, imageHeight = 1080;
        NvsVideoResolution videoEditRes = new NvsVideoResolution();
        int resolution = TIMELINE_RESOLUTION_VALUE;
        NvsAVFileInfo avFileInfo = NvsStreamingContext.getInstance().getAVFileInfo(path);
        if (avFileInfo != null) {
            NvsSize dimension = avFileInfo.getVideoStreamDimension(0);
            int streamRotation = avFileInfo.getVideoStreamRotation(0);
            imageWidth = dimension.width;
            imageHeight = dimension.height;
            if (streamRotation == 1 || streamRotation == 3) {
                imageWidth = dimension.height;
                imageHeight = dimension.width;
            }
        } else {
            LogUtils.e("getVideoEditResolutionByClip avFileInfo == null" + "===path===" + path);
        }

        //按照视频的比例生成timeline的宽高 标准是720P （720*1280） 或者是1080P（1080*1920）
        // The width and height standard for generating a timeline based on the proportion of the
        // video is 720P (720 * 1280) or 1080P (1080 * 1920).
        float timelineRation = imageWidth * 1.0F / imageHeight;
        Point size = new Point();
        if (timelineRation > 1) {
            //宽视频 Wide video
            size.y = resolution;
            size.x = (int) (resolution * timelineRation);
        } else {
            //高视频 High video
            size.x = resolution;
            size.y = (int) (resolution * 1.0F / timelineRation);
        }

        videoEditRes.imageWidth = alignedData(size.x, 4);
        videoEditRes.imageHeight = alignedData(size.y, 2);

        LogUtils.d("getVideoEditResolution   ", videoEditRes.imageWidth + "     " + videoEditRes.imageHeight);
        return videoEditRes;

    }

    public static Point calculateTimelineSize(int type) {
        int size = TIMELINE_RESOLUTION_VALUE;
        int width = 0;
        int height = 0;
        if (type == NvsConstants.AspectRatio.AspectRatio_1v1) {
            width = size;
            height = size;
        } else if (type == NvsConstants.AspectRatio.AspectRatio_3v4) {
            width = size;
            height = size / 3 * 4;
        } else if (type == NvsConstants.AspectRatio.AspectRatio_4v3) {
            height = size;
            width = size / 3 * 4;
        } else if (type == NvsConstants.AspectRatio.AspectRatio_9v16) {
            width = size;
            height = size / 9 * 16;
        } else if (type == NvsConstants.AspectRatio.AspectRatio_16v9) {
            height = size;
            width = size / 9 * 16;
        } else if (type == NvsConstants.AspectRatio.AspectRatio_9v18) {
            width = size;
            height = size / 9 * 18;
        } else if (type == NvsConstants.AspectRatio.AspectRatio_18v9) {
            height = size;
            width = size / 9 * 18;
        } else if (type == NvsConstants.AspectRatio.AspectRatio_21v9) {
            height = size;
            width = size / 9 * 21;
        } else if (type == NvsConstants.AspectRatio.AspectRatio_9v21) {
            width = size;
            height = size / 9 * 21;
        } else if (type == NvsConstants.AspectRatio.AspectRatio_2D39v1) {
            height = size;
            width = (int) (size / 2.39F);
        } else if (type == NvsConstants.AspectRatio.AspectRatio_2D55v1) {
            height = size;
            width = (int) (size / 2.55F);
        } else if (type == NvsConstants.AspectRatio.AspectRatio_NoFitRatio) {
            Point ratio = getOriginalRatio();
            width = ratio.x;
            height = ratio.y;
        }
        return new Point(width, height);
    }

    private static Point getOriginalRatio() {
        //第一个轨道的第一个视频 The first video of the first track.
        MeicamVideoClip editVideoClip = EditorEngine.getInstance().getEditVideoClip(0, 0);
        Point size = new Point();
        if (editVideoClip != null) {
            String filePath = editVideoClip.getFilePath();
            NvsVideoResolution nvsVideoResolution = getVideoEditResolutionByClip(filePath);
            int imageWidth = nvsVideoResolution.imageWidth;
            int imageHeight = nvsVideoResolution.imageHeight;
            size.x = imageWidth;
            size.y = imageHeight;
        }
        return size;
    }

}
