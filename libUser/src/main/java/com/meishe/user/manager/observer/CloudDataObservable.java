package com.meishe.user.manager.observer;

import android.database.Observable;

import com.meishe.base.utils.ThreadUtils;
import com.meishe.draft.data.DraftData;
import com.meishe.user.bean.CloudDraftData;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/11 18:39
 * @Description :上传被监听对象 The observable for uploading.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CloudDataObservable extends Observable<DataObserver> {

    public void notifyUploadDataChanged(List<DraftData> data){
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (int i = mObservers.size() - 1; i >= 0; i--) {
                    mObservers.get(i).onUploadDataChanged(data);
                }
            }
        });
    }

    public void notifyUploadProgressChanged(String tag, int progress){
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (int i = mObservers.size() - 1; i >= 0; i--) {
                    mObservers.get(i).onUploadProgressChanged(tag, progress);
                }
            }
        });
    }

    public void notifyDownloadDataChanged(List<CloudDraftData> data){
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (int i = mObservers.size() - 1; i >= 0; i--) {
                    mObservers.get(i).onDownloadDataChanged(data);
                }
            }
        });
    }

}
