package com.meishe.myvideo.ui.trackview.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.IntRange;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FormatUtils;
import com.meishe.myvideo.R;
import com.meishe.myvideo.util.PixelPerMicrosecondUtil;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;


/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2020/10/28 14:54
 * @Description :操作面板，时间点适配器
 * Time dot adapter
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class OperationTimeDotAdapter extends RecyclerView.Adapter {
    private List<String> mDotList;
    private int mStartWidth;
    private int mEndWidth;
    private int mSecondWidth;
    private float mItemWidth;
    private long mDuration;
    private double mScale;
    private final int TYPE_EMPTY_START = 0;
    private final int TYPE_EMPTY_END = 1;
    private final int TYPE_DEFAULT = 2;

    public OperationTimeDotAdapter(int secondWidth, int startWidth, int endWidth) {
        mSecondWidth = secondWidth;
        mStartWidth = startWidth;
        mEndWidth = endWidth;
        mDotList = new ArrayList<>();
    }

    /**
     * 创建时间点
     * Create time dot
     *
     * @param scale    the scale 缩放因子
     * @param duration the duration 时长
     */
    private List<String> createTimeDot(double scale, long duration) {
        return createTimeDot(0, scale, duration);
    }

    /**
     * 创建时间点
     * Create time dot
     *
     * @param startIndex the start index 开始索引
     * @param scale      the scale 缩放因子
     * @param duration   the duration 时长
     */
    private List<String> createTimeDot(int startIndex, double scale, long duration) {
        List<String> data = new ArrayList<>();
        if (scale < 0.3) {
            mItemWidth = mSecondWidth * 8f;
            data = createZoomDot(startIndex, 8, duration);
        } else if (scale >= 0.3 && scale < 0.5) {
            mItemWidth = mSecondWidth * 4f;
            data = createZoomDot(startIndex, 4, duration);
        } else if (scale >= 0.5 && scale < 0.8) {
            mItemWidth = mSecondWidth * 2f;
            data = createZoomDot(startIndex, 2, duration);
        } else if (scale >= 0.8 && scale < 2) {
            mItemWidth = mSecondWidth;
            // 对后面半秒的时候会显示不全，所以加1
            // The display will be incomplete in the next half a second, so add 1
            data = createZoomDot(startIndex, 1, duration);
        } else if (scale >= 2 && scale < 4) {
            mItemWidth = mSecondWidth / 2f;
            data = createZoomDot(startIndex, 0, 0, duration);
        } else if (scale >= 4 && scale < 6) {
            mItemWidth = mSecondWidth / 4f;
            data = createZoomDot(startIndex, 15, 1, duration);
        } else if (scale >= 6 && scale < 8) {
            mItemWidth = mSecondWidth / 6f;
            data = createZoomDot(startIndex, 10, 2, duration);
        } else if (scale >= 8 && scale < 13) {
            mItemWidth = mSecondWidth / 12f;
            data = createZoomDot(startIndex, 5, 5, duration);
        } else if (scale >= 13) {
            mItemWidth = mSecondWidth / 20f;
            data = createZoomDot(startIndex, 3, 9, duration);
        }
        //LogUtils.d("mItemWidth="+mItemWidth+",duration="+duration+",mDuration="+mDuration+",length="+durationToLength(mDuration));
        return data;
    }

    /**
     * 创建缩放时间点
     * Create time dot
     *
     * @param startIndex the start index 开始索引
     * @param baseNum    the base num
     * @param duration   the duration 时长
     */
    private List<String> createZoomDot(int startIndex, int baseNum, long duration) {
        List<String> data = new ArrayList<>();
        int durationTime = (int) Math.floor(duration + 1);
        for (int i = startIndex; i < durationTime; i += baseNum) {
            if (i % (baseNum * 2) == 0) {
                data.add(FormatUtils.sec2Time(i));
            } else {
                data.add(" . ");
            }
        }
        return data;
    }

    /**
     * 创建缩放时间点
     * Create time dot
     *
     * @param startIndex the start index 开始索引
     * @param baseNum    the base num
     * @param factor     the factor
     * @param duration   the duration 时长
     */
    private List<String> createZoomDot(int startIndex, int baseNum, int factor, long duration) {
        List<String> temp = getCommonDot(baseNum, factor);
        List<String> data = new ArrayList<>();
        int durationTime = (int) Math.floor(duration + 1);
        for (int i = startIndex; i < durationTime; i++) {
            if (factor > 0 && i > 0) {
                data.add(" . ");
            }
            data.add(FormatUtils.sec2Time(i));
            if (i <= durationTime - 1) {
                data.addAll(temp);
            }
        }
        return data;
    }

    private List<String> getCommonDot(int baseNum, int factor) {
        List<String> data = new ArrayList<>();
        //15 10-20 5-25 3-27
        int size = baseNum * factor;
        if (size == 0) {
            data.add(" . ");
            return data;
        }
        for (int i = baseNum; i <= size; i += baseNum) {
            data.add(" . ");
            data.add(i + "f");
        }
        return data;
    }

    /**
     * 设置数据
     * Set data
     *
     * @param scale    the scale 缩放因子
     * @param duration the duration 时长
     */
    public void setData(double scale, long duration) {
        mDuration = duration;
        mScale = scale;
        mDotList.clear();
        mDotList.add("empty");
        mDotList.addAll(createTimeDot(scale, duration));
        mDotList.add("empty");
        //LogUtils.d("size="+mDotList.size());
        notifyDataSetChanged();
    }

    /**
     * 更新缩放
     * Update Scale
     *
     * @param scale the scale 缩放因子
     * @param width width per second 每秒的宽度
     */
    public void updateScale(double scale, int width) {
        if (Math.abs(mScale - scale) < 0.000001d || width == mSecondWidth) {
            return;
        }
        mSecondWidth = width;
        mScale = scale;
        mDotList.clear();
        mDotList.add("empty");
        mDotList.addAll(createTimeDot(scale, mDuration));
        mDotList.add("empty");
        //LogUtils.d("size="+mDotList.size());
        notifyDataSetChanged();
    }

    /**
     * 时长转化成长度
     * Duration convert to length
     *
     * @param duration the duration
     */
    public int durationToLength(long duration) {
        return PixelPerMicrosecondUtil.durationToLength(duration);
    }

    /**
     * 更新相对时长
     * Update relative duration
     *
     * @param duration the relative duration
     */
    public void updateDxDuration(long duration) {
        updateDuration(duration + mDuration);
    }

    /**
     * 更新时长，在缩放没变的时候，如果时长刻度变化，没有必要更新所有数据，只需更新部分数据
     * Update duration
     *
     * @param duration the duration 时长
     */
    public void updateDuration(long duration) {
        if (duration == mDuration) {
            return;
        }
        //LogUtils.d("duration="+duration+",mDuration="+mDuration+",size="+getData().size());
        List<String> timeDot;
        if (duration - mDuration < 0) {
            timeDot = createTimeDot((int) duration, mScale, mDuration);
            for (int i = getData().size() - timeDot.size() + 1; i < getData().size(); i++) {
                if (i >= 0) {
                    remove(i);
                    i--;
                }
            }
        } else {
            timeDot = createTimeDot((int) mDuration + 1, mScale, duration);
            addData(timeDot);
        }
        mDuration = duration;
    }

    public float getItemWidth() {
        return mItemWidth;
    }

    public long getDuration() {
        return mDuration;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        ViewGroup.LayoutParams layoutParams;
        if (viewType == TYPE_EMPTY_START) {
            View view = new View(parent.getContext());
            layoutParams = new ViewGroup.LayoutParams((int) (mStartWidth - mItemWidth / 2), ViewGroup.LayoutParams.MATCH_PARENT);
            view.setLayoutParams(layoutParams);
            return new EmptyHolder(view);
        } else if (viewType == TYPE_EMPTY_END) {
            View view = new View(parent.getContext());
            layoutParams = new ViewGroup.LayoutParams(mEndWidth, ViewGroup.LayoutParams.MATCH_PARENT);
            view.setLayoutParams(layoutParams);
            return new EmptyHolder(view);
        }
        return new DotHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_time_dot, parent, false));
    }

    public int getFirstWidth() {
        return (int) (mStartWidth - mItemWidth / 2);
    }

    @Override
    public int getItemViewType(int position) {
        if (position == 0) {
            return TYPE_EMPTY_START;
        } else if (position == getItemCount() - 1) {
            return TYPE_EMPTY_END;
        } else {
            return TYPE_DEFAULT;
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        if (holder instanceof DotHolder) {
            if (holder.itemView.getWidth() != mItemWidth) {
                //根布局
                //Root
                setLayoutParams(holder.itemView);
            }
            ((DotHolder) holder).mTvDot.setText(getItem(position));
        } else if (holder instanceof EmptyHolder) {
            ViewGroup.LayoutParams layoutParams = holder.itemView.getLayoutParams();
            if (position == 0) {
                layoutParams.width = (int) (mStartWidth - mItemWidth / 2);
            } else {
                layoutParams.width = (int) (mEndWidth + mItemWidth / 2);
            }
            holder.itemView.setLayoutParams(layoutParams);
        }

    }

    /**
     * 更改item的宽度
     * Set item width
     */
    private void setLayoutParams(View view) {
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        if (layoutParams == null) {
            layoutParams = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        }
        layoutParams.width = (int) mItemWidth;
    }

    public List<String> getData() {
        return mDotList;
    }

    public String getItem(int position) {
        if (position < 0 || position >= mDotList.size()) {
            return "";
        }
        return mDotList.get(position);
    }

    @Override
    public int getItemCount() {
        return mDotList.size();
    }

    private void addData(@NonNull Collection<String> newData) {
        int index = mDotList.size();
        if (mDotList.size() > 0) {
            index = mDotList.size() - 1;
        }
        mDotList.addAll(index, newData);
        index = mDotList.size() - newData.size();
        if (index < 0) {
            index = 0;
        }
        notifyItemRangeInserted(index, newData.size());
        compatibilityDataSizeChanged(newData.size());
    }

    private void compatibilityDataSizeChanged(int size) {
        final int dataSize = mDotList == null ? 0 : mDotList.size();
        if (dataSize == size) {
            notifyDataSetChanged();
        }
    }

    /**
     * remove the item associated with the specified position of adapter
     *
     * @param item T
     */
    public void remove(@NonNull String item) {
        int position = mDotList.indexOf(item);
        if (position >= 0) {
            remove(position);
        }
    }

    /**
     * remove the item associated with the specified position of adapter
     *
     * @param position the position
     */
    public void remove(@IntRange(from = 0) int position) {
        if (CommonUtils.isIndexAvailable(position, mDotList)) {
            mDotList.remove(position);
            notifyItemRemoved(position);
            compatibilityDataSizeChanged(0);
            notifyItemRangeChanged(position, mDotList.size() - position);
        }
    }

    static class DotHolder extends RecyclerView.ViewHolder {
        TextView mTvDot;

        DotHolder(@NonNull View itemView) {
            super(itemView);
            mTvDot = itemView.findViewById(R.id.tv_dot);
        }
    }

    static class EmptyHolder extends RecyclerView.ViewHolder {
        EmptyHolder(@NonNull View itemView) {
            super(itemView);
        }
    }
}

