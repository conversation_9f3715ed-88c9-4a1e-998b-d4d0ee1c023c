package com.meishe.myvideo.fragment;

import android.os.Bundle;
import android.view.View;
import android.widget.SeekBar;

import com.meishe.base.constants.Constants;
import com.meishe.base.model.BaseFragment;
import com.meishe.myvideo.R;
import com.meishe.myvideo.view.MYSeekBarTextView;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2022 /11/1 10:59
 * @Description: 音量Fragment The volume fragment
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class VolumeFragment extends BaseFragment {

    private MYSeekBarTextView mSeekBar;
    /**
     * The constant BUNDLE_PROGRESS.
     */
    public static final String BUNDLE_PROGRESS = "bundle_progress";
    private EventListener mEventListener;
    private final int maxVolumeProgress = Constants.maxVolumeProgress;

    /**
     * Instantiates a new Volume fragment.
     */
    public VolumeFragment() {
    }

    /**
     * Create volume fragment.
     *
     * @param progress      the progress
     * @param eventListener the event listener
     * @return the volume fragment
     */
    public static VolumeFragment create(int progress, EventListener eventListener) {
        VolumeFragment volumeFragment = new VolumeFragment();
        volumeFragment.setEventListener(eventListener);
        Bundle bundle = new Bundle();
        bundle.putInt(BUNDLE_PROGRESS, progress);
        volumeFragment.setArguments(bundle);
        return volumeFragment;
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_volume;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        mSeekBar = rootView.findViewById(R.id.seek_bar);
        Bundle bundle = getArguments();
        mSeekBar.setMax(maxVolumeProgress);
        if (bundle != null) {
            mSeekBar.setProgress(bundle.getInt(BUNDLE_PROGRESS));
        }
        initListener();
    }

    private void initListener() {
        mSeekBar.setOnSeekBarChangeListener(new MYSeekBarTextView.OnSeekBarListener() {
            @Override
            public void onStopTrackingTouch(int progress, String name) {
                if (mEventListener != null) {
                    mEventListener.onVolumeChange(progress);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
            }
        });
    }

    @Override
    protected void initData() {

    }

    /**
     * Sets event listener.
     *
     * @param mEventListener the m event listener
     */
    public void setEventListener(EventListener mEventListener) {
        this.mEventListener = mEventListener;
    }

    /**
     * The interface Event listener.
     */
    public interface EventListener {
        /**
         * 音量改变的回调
         * On volume change.
         *
         * @param progress the progress 进度
         */
        void onVolumeChange(int progress);

        /**
         * 音频淡入淡出回调
         * On audio fade change.
         *
         * @param fadeIn  the fade in 淡入时长
         * @param fadeOut the fade out 淡出时长
         */
        void onAudioFadeChange(long fadeIn, long fadeOut);
    }

}
