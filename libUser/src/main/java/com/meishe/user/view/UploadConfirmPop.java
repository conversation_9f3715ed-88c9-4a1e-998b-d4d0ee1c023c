package com.meishe.user.view;

import android.content.Context;
import android.view.View;

import androidx.annotation.NonNull;

import com.meishe.base.utils.KeyboardUtils;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.third.pop.XPopup;
import com.meishe.third.pop.core.CenterPopupView;
import com.meishe.user.R;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/16 19:57
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class UploadConfirmPop extends CenterPopupView {
    private IUserPlugin.OnEventListener<Object> mListener;

    private UploadConfirmPop(@NonNull Context context, IUserPlugin.OnEventListener<Object> listener) {
        super(context);
        mListener = listener;
    }

    public static UploadConfirmPop create(Context context, IUserPlugin.OnEventListener<Object> listener) {
        return ((UploadConfirmPop) new XPopup
                .Builder(context)
                .asCustom(new UploadConfirmPop(context, listener)));
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_upload_confirm;
    }

    @Override
    protected int getPopupWidth() {
        return (int) (ScreenUtils.getScreenWidth() * 19 / 20f);
    }


    @Override
    protected void onDismiss() {
        super.onDismiss();
        KeyboardUtils.hideSoftInput(this);
    }


    @Override
    protected void onCreate() {
        super.onCreate();
        findViewById(R.id.tv_update).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mListener != null) {
                    mListener.onConfirm(null);
                }
                dismiss();

            }
        });
        findViewById(R.id.tv_create).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mListener != null) {
                    mListener.onCancel();
                }
                dismiss();
            }
        });
    }
}
