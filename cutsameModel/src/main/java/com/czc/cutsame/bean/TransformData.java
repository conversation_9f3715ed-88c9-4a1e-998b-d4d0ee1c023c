package com.czc.cutsame.bean;

import android.graphics.RectF;
import android.os.Parcel;
import android.os.Parcelable;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/11/17 18:25
 * @Description :位移变换信息 the transform data
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class TransformData implements Parcelable {
    private float transX;
    private float transY;
    private float scale = 1F;
    private float rotation;
    private RectF region = new RectF(-1, 1, 1, -1);
    private float[] rectSize = new float[]{1, 1};
    private float rectRatio = 1;

    public TransformData() {
    }


    protected TransformData(Parcel in) {
        transX = in.readFloat();
        transY = in.readFloat();
        scale = in.readFloat();
        rotation = in.readFloat();
        region = in.readParcelable(RectF.class.getClassLoader());
        rectSize = in.createFloatArray();
        rectRatio = in.readFloat();
    }

    public static final Creator<TransformData> CREATOR = new Creator<TransformData>() {
        @Override
        public TransformData createFromParcel(Parcel in) {
            return new TransformData(in);
        }

        @Override
        public TransformData[] newArray(int size) {
            return new TransformData[size];
        }
    };

    public float getTransX() {
        return transX;
    }

    public void setTransX(float transX) {
        this.transX = transX;
    }

    public float getTransY() {
        return transY;
    }

    public void setTransY(float transY) {
        this.transY = transY;
    }

    public RectF getRegion() {
        return region;
    }

    public void setRegion(RectF region) {
        this.region = region;
    }

    public float getScale() {
        return scale;
    }

    public void setScale(float scale) {
        this.scale = scale;
    }

    public float getRotation() {
        return rotation;
    }

    public void setRotation(float rotation) {
        this.rotation = rotation;
    }

    public float[] getRectViewSize() {
        return rectSize;
    }

    public void setRectSize(float[] rectSize) {
        this.rectSize = rectSize;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {

        dest.writeFloat(transX);
        dest.writeFloat(transY);
        dest.writeFloat(scale);
        dest.writeFloat(rotation);
        dest.writeParcelable(region, flags);
        dest.writeFloatArray(rectSize);
        dest.writeFloat(rectRatio);
    }

    public float getRectRatio() {
        return rectRatio;
    }

    public void setRectRatio(float rectRatio) {
        this.rectRatio = rectRatio;
    }
}
