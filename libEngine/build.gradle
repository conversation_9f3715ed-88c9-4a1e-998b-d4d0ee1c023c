apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.android.extCompileSdkVersion
    buildToolsVersion rootProject.android.extBuildToolsVersion


    defaultConfig {
        minSdkVersion rootProject.android.extMinSdkVersion
        targetSdkVersion rootProject.android.extTargetSdkVersion
        versionCode rootProject.config.extVersionCode
        versionName rootProject.config.extVersionName

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        //解决多包依赖显示使用注解
        //javaCompileOptions { annotationProcessorOptions { includeCompileClasspath = true } }
    }

    buildTypes {
        release {
            consumerProguardFiles 'proguard-rules.pro'
        }
    }
}

dependencies {
    api fileTree(dir: 'libs', include: ['*.jar'])
   //implementation files('libs\\NvAndroidStreamingSdk.jar')
    testImplementation rootProject.ext.dependencies.extTestJunit
    implementation project(path: ':libLogic')
    implementation project(path: ':libPlugin')
    implementation project(path: ':annotation')
    annotationProcessor project(path: ':libAnnotateProcessor')
    implementation rootProject.ext.dependencies.room
    annotationProcessor  rootProject.ext.dependencies.roomComplier

}
