package com.meishe.myvideo.util;


import android.content.res.AssetFileDescriptor;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Handler;
import android.os.Message;
import android.util.Log;

import com.meishe.base.manager.AppManager;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;
import com.meishe.myvideo.R;
import com.meishe.myvideo.bean.MusicInfo;

import java.io.FileDescriptor;
import java.io.IOException;
import java.lang.ref.WeakReference;
import java.util.Timer;
import java.util.TimerTask;

/**
 * Created by ms on 2018/7/15
 * note: MediaPlayer功能封装，使用meidiaPlayer时时间单位是毫秒
 * MediaPlayer features encapsulation so that using MeidiaPlayer is done in milliseconds
 */
public class AudioPlayer {
    private final String TAG = "AudioPlayer";
    private final PlayHandler m_handler = new PlayHandler(this);
    private static final int UPDATE_TIME = 0;
    private MediaPlayer mMediaPlayer;
    private Timer mTimer;
    private TimerTask mTimerTask;
    private MusicInfo mCurrentMusic;
    private OnPlayListener mListener;
    private static AudioPlayer mMusicPlayer;


    /**
     * Sets play listener.
     * 设置播放监听器
     *
     * @param listener the listener 监听
     */
    public void setPlayListener(OnPlayListener listener) {
        mListener = listener;
    }

    private AudioPlayer() {
        mCurrentMusic = null;
    }

    public static AudioPlayer getInstance() {
        if (mMusicPlayer == null) {
            synchronized (AudioPlayer.class) {
                if (mMusicPlayer == null) {
                    mMusicPlayer = new AudioPlayer();
                }
            }
        }
        return mMusicPlayer;
    }

    /**
     * Destroy player.
     * 销毁播放器
     */
    public void destroyPlayer() {
        if (mMediaPlayer == null)
            return;
        stopMusicTimer();
        if (mMediaPlayer != null) {
            mMediaPlayer.stop();
            mMediaPlayer.release();
            mMediaPlayer = null;
        }
        m_handler.removeCallbacksAndMessages(null);
    }

    /**
     * Start play.
     * 开始播放
     */
    public void startPlay() {
        stopMusicTimer();
        if (mCurrentMusic == null || mMediaPlayer == null)
            return;
        if (mCurrentMusic.isPrepare()) {
            try {
                mMediaPlayer.start();
                startMusicTimer();
            } catch (Exception e) {
                e.printStackTrace();
                Log.e(TAG, "start Exception");
            }
        }
        if (mListener != null)
            mListener.onMusicPlay();
    }

    /**
     * Stop play.
     * 停止播放
     */
    public void stopPlay() {
        if (mMediaPlayer != null) {
            mMediaPlayer.pause();
            stopMusicTimer();
            if (mListener != null)
                mListener.onMusicStop();
        }
    }

    public void startPlay(long statTime){
        seekPosition(statTime);
        startPlay();
    }

    /**
     * Sets current music.
     * 设置当前的音乐
     *
     * @param audioInfo the audio info 音频信息
     * @param autoPlay  the auto play 自动播放
     */
    public void setCurrentMusic(MusicInfo audioInfo, boolean autoPlay) {
        if (audioInfo == null)
            return;
        mCurrentMusic = audioInfo;
        mCurrentMusic.setPrepare(false);
        resetMediaPlayer(autoPlay);
    }

    /**
     * Seek position.
     * 寻找位置
     *
     * @param time the time 时间
     */
    public void seekPosition(long time) {
        if (mMediaPlayer != null) {
            time = time / 1000;
            if (time < mMediaPlayer.getDuration() && time >= 0) {
                mMediaPlayer.seekTo((int) time);
            }
        }
    }

    /**
     * Gets cur music pos.
     * 获得当前音乐的位置
     *
     * @return the cur music pos 当前音乐的位置
     */
    public long getCurMusicPos() {
        return mMediaPlayer.getCurrentPosition();
    }

    /**
     * 重置MediaPlayer Reset the MediaPlayer】
     * Reset media player
     *
     * @param autoPlay 是否自动播放
     */
    private void resetMediaPlayer(final boolean autoPlay) {
        stopMusicTimer();
        if (mCurrentMusic == null) {
            if (mMediaPlayer == null)
                return;
            try {
                mMediaPlayer.stop();
                mMediaPlayer.release();
            } catch (Exception e) {
                e.printStackTrace();
                Log.e(TAG, "stop & release: null");
            }
            mMediaPlayer = null;
            return;
        }

        if (mMediaPlayer == null) {
            mMediaPlayer = new MediaPlayer();
            mMediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
                @Override
                public void onCompletion(MediaPlayer mediaPlayer) {
                    if (mCurrentMusic != null) {
                        int trimIn = (int) mCurrentMusic.getTrimIn() / 1000;
                        if (trimIn > 0)
                            mMediaPlayer.seekTo(trimIn);

                        if (mCurrentMusic.isPrepare()) {
                            if (!AppManager.getInstance().isBackground()) {
                                startPlay();
                            }
                        }
                    }
                }
            });

            mMediaPlayer.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
                @Override
                public void onPrepared(MediaPlayer mediaPlayer) {
                    if (mCurrentMusic != null) {
                        mCurrentMusic.setPrepare(true);
                        mMediaPlayer.seekTo((int) mCurrentMusic.getTrimIn() / 1000);
                    }
                    if (!AppManager.getInstance().isBackground() && autoPlay) {
                        startPlay();
                    }
                }
            });

            mMediaPlayer.setOnErrorListener(new MediaPlayer.OnErrorListener() {
                @Override
                public boolean onError(MediaPlayer mediaPlayer, int i, int i1) {
                    ToastUtils.showShort(R.string.music_play_error);
                    return true;
                }
            });
        }
        try {
            mMediaPlayer.stop();
            mMediaPlayer.reset();
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "stop & release: null");
        }
        try {
            String url;
            if (mCurrentMusic.isHttpMusic())
                url = mCurrentMusic.getFileUrl();
            else
                url = mCurrentMusic.getFilePath();
            if (url != null) {
                if (mCurrentMusic.isAsset()) {
                    AssetFileDescriptor musicfd = Utils.getApp().getAssets().openFd(mCurrentMusic.getAssetPath());
                    mMediaPlayer.setDataSource(musicfd.getFileDescriptor(), musicfd.getStartOffset(), musicfd.getLength());
                } else {
                    if (FileUtils.isAndroidQUriPath(url)) {
                        AssetFileDescriptor musicfd = Utils.getApp().getContentResolver().openAssetFileDescriptor(Uri.parse(url), "r");
                        if (musicfd != null) {
                            FileDescriptor fileDescriptor = musicfd.getFileDescriptor();
                            if (fileDescriptor != null) {
                                mMediaPlayer.setDataSource(fileDescriptor, musicfd.getStartOffset(), musicfd.getLength());
                            }
                        }
                    } else {
                        mMediaPlayer.setDataSource(url);
                    }
                }
                mMediaPlayer.setAudioStreamType(AudioManager.STREAM_MUSIC);
                mMediaPlayer.prepareAsync();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 开启刷新进度定时器
     * Turn on the refresh progress timer
     */
    private void startMusicTimer() {
        mTimer = new Timer();
        mTimerTask = new TimerTask() {
            @Override
            public void run() {
                if (mMediaPlayer != null && mMediaPlayer.isPlaying()) {
                    m_handler.sendEmptyMessage(UPDATE_TIME);
                }
            }
        };
        mTimer.schedule(mTimerTask, 0, 100);
    }

    /**
     * 停止刷新进度定时器
     * Stop refreshing the progress timer
     *
     */
    private void stopMusicTimer() {
        if (mTimerTask != null) {
            mTimerTask.cancel();
            mTimerTask = null;
        }
        if (mTimer != null) {
            mTimer.cancel();
            mTimer.purge();
            mTimer = null;
        }
    }

    /**
     * 回调进度值
     * Calling back the progress value
     * @param pos the position
     */
    private void sendCurrentPos(long pos) {
        if (mListener != null)
            mListener.onGetCurrentPos(pos * 1000L);
    }

    static class PlayHandler extends Handler {
        WeakReference<AudioPlayer> mWeakReference;

        public PlayHandler(AudioPlayer player) {
            mWeakReference = new WeakReference<>(player);
        }

        @Override
        public void handleMessage(Message msg) {
            final AudioPlayer player = mWeakReference.get();
            if (player != null) {
                if (msg.what == UPDATE_TIME) {
                    if (player.mMediaPlayer == null)
                        return;
                    int curPos = player.mMediaPlayer.getCurrentPosition();

                    if (curPos >= player.mCurrentMusic.getTrimOut() / 1000) {
                        player.mMediaPlayer.seekTo((int) (player.mCurrentMusic.getTrimIn() / 1000));
                        player.startPlay();
                    }

                    player.sendCurrentPos(curPos);
                }
            }
        }
    }

    /**
     * The interface On play listener.
     * 播放监听的接口
     */
    public interface OnPlayListener {
        /**
         * On music play.
         * 播放音乐
         */
        void onMusicPlay();

        /**
         * On music stop.
         * 停止音乐
         */
        void onMusicStop();

        /**
         * On get current pos.
         * 获得当前的位置
         *
         * @param curPos the cur pos
         */
        void onGetCurrentPos(long curPos);
    }
}
