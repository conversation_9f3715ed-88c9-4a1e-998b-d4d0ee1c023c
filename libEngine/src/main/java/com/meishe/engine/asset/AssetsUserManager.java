package com.meishe.engine.asset;

import android.content.Context;
import android.text.TextUtils;

import com.meishe.engine.asset.bean.AssetList;
import com.meishe.engine.asset.bean.RequestParam;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/3/22 16:01
 * @Description :和用户相关的资源管理类 The assets manager about users
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class AssetsUserManager extends IAssetsManager {
    private static final float RATIO_16_9 = 16f / 9f;
    private static final float RATIO_4_3 = 4f / 3f;
    private static final float RATIO_1_1 = 1f;
    private static final float RATIO_3_4 = 3f / 4f;
    private static final float RATIO_9_16 = 9f / 16f;
    private static final float RATIO_18_9 = 18f / 9f;
    private static final float RATIO_9_18 = 9f / 18f;
    private static final float RATIO_21_9 = 21f / 9f;
    private static final float RATIO_9_21 = 9f / 21f;

    private static final int RATIO_COMMON = 1;
    private static final int RATIO_UNCOMMON = 0;
    private static final int RATIO_NO_FIT = -1;

    public AssetsUserManager() {
    }


    @Override
    public int[] getAspectRatio(float ratio) {
        int[] data = new int[2];
        if (isFloatEqual(ratio, RATIO_16_9)) {
            data[0] = BaseInfo.AspectRatio_16v9;
            data[1] = RATIO_NO_FIT;
        } else if (isFloatEqual(ratio, RATIO_4_3)) {
            data[0] = BaseInfo.AspectRatio_4v3;
            data[1] = RATIO_NO_FIT;
        } else if (isFloatEqual(ratio, RATIO_1_1)) {
            data[0] = BaseInfo.AspectRatio_1v1;
            data[1] = RATIO_NO_FIT;
        } else if (isFloatEqual(ratio, RATIO_3_4)) {
            data[0] = BaseInfo.AspectRatio_3v4;
            data[1] = RATIO_NO_FIT;
        } else if (isFloatEqual(ratio, RATIO_9_16)) {
            data[0] = BaseInfo.AspectRatio_9v16;
            data[1] = RATIO_NO_FIT;
        } else if (isFloatEqual(ratio, RATIO_18_9)) {
            data[0] = BaseInfo.AspectRatio_18v9;
            data[1] = RATIO_NO_FIT;
        } else if (isFloatEqual(ratio, RATIO_9_18)) {
            data[0] = BaseInfo.AspectRatio_9v18;
            data[1] = RATIO_NO_FIT;
        } else if (isFloatEqual(ratio, RATIO_9_21)) {
            data[0] = BaseInfo.AspectRatio_9v21;
            data[1] = RATIO_NO_FIT;
        } else if (isFloatEqual(ratio, RATIO_21_9)) {
            data[0] = BaseInfo.AspectRatio_21v9;
            data[1] = RATIO_NO_FIT;
        } else {
            data[0] = BaseInfo.AspectRatio_All;
            data[1] = RATIO_COMMON;
        }
        return data;
    }

    @Override
    public String getErrorMsg(Context context, int type) {
        IUserPlugin userPlugin = getUserPlugin();
        if (userPlugin != null) {
            return userPlugin.getMsg(context, type);
        }
        return "";
    }

    @Override
    public void getAssetsList(final String token, final RequestParam param, final int aspectRatio, final int ratioFlag,
                              final int page, final int pageSize, final RequestCallback<AssetList> callback) {
        if (TextUtils.isEmpty(token) && getUserPlugin() != null) {
            loginAndRequest(token, param, aspectRatio, ratioFlag, page, pageSize, callback);
            return;
        }
        AssetsManager.get().getAssetListNew(token, param,
                aspectRatio, ratioFlag, page, pageSize, new RequestCallback<AssetList>() {
                    @Override
                    public void onSuccess(final BaseResponse<AssetList> response) {
                        if (response != null && response.getCode() == -2) {
                            loginAndRequest(token, param, aspectRatio, ratioFlag, page, pageSize, callback);
                            return;
                        }
                        if (response == null) {
                            if (callback != null) {
                                callback.onError((BaseResponse<AssetList>) null);
                            }
                            return;
                        }
                        AssetList data = response.getData();
                        if (data == null) {
                            if (callback != null) {
                                callback.onError(response);
                            }
                            return;
                        }
                        if (data.elements != null) {
                            if (callback != null) {
                                callback.onSuccess(response);
                            }
                        }
                    }

                    @Override
                    public void onError(final BaseResponse<AssetList> response) {
                        if (response != null && response.getCode() == -2) {
                            loginAndRequest(token, param, aspectRatio, ratioFlag, page, pageSize, callback);
                        } else {
                            if (callback != null) {
                                callback.onError(response);
                            }
                        }
                    }
                });
    }

    @Override
    void getLocalAssetList(RequestParam param, final AssetCallback assetCallback) {
        String token = "";
        IUserPlugin userPlugin = getUserPlugin();
        if (userPlugin != null) {
            token = userPlugin.getToken();
        }
        AssetsManager.get().getAssetsListFromDb(token, param, assetCallback);

    }

    private void loginAndRequest(final String oldToken, final RequestParam param, final int aspectRatio, final int ratioFlag, final int page, final int pageSize, final RequestCallback<AssetList> callback) {
        IUserPlugin userPlugin = getUserPlugin();
        if (userPlugin != null) {
            userPlugin.login(new IUserPlugin.ILoginCallBack() {
                @Override
                public void onLoginSuccess(String token) {
                    if (!TextUtils.isEmpty(oldToken)) {
                        AssetsManager.get().updateUserAssetsInfo(oldToken, token);
                    }
                    AssetsManager.get().getAssetListNew(token, param,
                            aspectRatio, ratioFlag, page, pageSize, callback);
                }

                @Override
                public void onLoginFailed(int code) {
                    if (param.subType == 0) {
                        AssetsManager.get().getAssetListNew("", param,
                                aspectRatio, ratioFlag, page, pageSize, callback);
                    } else {
                        if (callback != null) {
                            BaseResponse<AssetList> response = new BaseResponse<>();
                            response.setCode(code);
                            callback.onError(response);
                        }
                    }
                }
            });
        } else {
            if (callback != null) {
                BaseResponse<AssetList> response = new BaseResponse<>();
                callback.onError(response);
            }
        }
    }

    private boolean isFloatEqual(float a, float b) {
        return Math.abs(a - b) <= 1e-6;
    }
}
