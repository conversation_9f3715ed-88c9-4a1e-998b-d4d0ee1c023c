package com.meishe.engine.interf;

import android.util.Pair;

import com.meishe.engine.bean.MeicamKeyFrame;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/6/30 15:51
 * @Description :关键帧持有者接口 The interface of key frame holder
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface IKeyFrameHolder {
    /**
     * 获取关键帧对
     * Gets frame pair.
     *
     * @param key       the key 关键帧的关键字
     * @param timestamp the timestamp 时刻
     * @return the frame pair 关键帧对
     */
    Pair<MeicamKeyFrame, MeicamKeyFrame> getFramePair(String key, long timestamp);
}
