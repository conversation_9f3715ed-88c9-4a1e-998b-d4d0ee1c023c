package com.meishe.myvideo.fragment.adapter;

import android.graphics.Typeface;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.bean.CaptionFontInfo;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/1/12 17:37
 * @Description :字幕字体适配器 The caption font adapter
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CaptionFontAdapter extends BaseQuickAdapter<IBaseInfo, BaseViewHolder> {
    private int mSelectedPosition = -1;
    private final float mFontSize;
    private final int mItemWidth;

    public CaptionFontAdapter(float fontSize, int itemWidth) {
        super(R.layout.item_caption_font);
        this.mFontSize = fontSize;
        this.mItemWidth = itemWidth;
    }

    /**
     * 选中某一项
     * Selected item .
     *
     * @param position The index of list
     */
    public void selected(int position) {
        if (mSelectedPosition >= 0) {
            notifyItemChanged(mSelectedPosition);
        }
        mSelectedPosition = position;
        if (position >= 0 && position < getData().size()) {
            notifyItemChanged(position);
        }
    }

    /**
     * 选中某一项
     * Selected item .
     *
     * @param fontName font Name
     * @param fontPath font path
     */
    public void selected(String fontName, String fontPath) {
        boolean fontNameIsEmpty = TextUtils.isEmpty(fontName);
        if (fontNameIsEmpty && TextUtils.isEmpty(fontPath)) {
            selected(0);
            return;
        }
        for (int i = 0; i < getData().size(); i++) {
            CaptionFontInfo info = (CaptionFontInfo) getData().get(i);
            String fontFamily = info.getFontFamily();
            boolean fontFamilyIsSame = !fontNameIsEmpty && !TextUtils.isEmpty(fontFamily)
                    && fontName.contains(fontFamily);
            if (fontFamilyIsSame || fontPath.equals(info.getAssetPath())) {
                selected(i);
                break;
            }
        }
    }

    @NonNull
    @Override
    public BaseViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        BaseViewHolder holder = super.onCreateViewHolder(parent, viewType);
        TextView textView = (TextView) holder.itemView;
        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, mFontSize);
        ViewGroup.LayoutParams layoutParams = textView.getLayoutParams();
        layoutParams.width = mItemWidth;
        textView.setLayoutParams(layoutParams);
        return holder;
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, IBaseInfo item) {
        TextView textView = (TextView) helper.itemView;
        textView.setText(item.getName());
        textView.setTypeface(getTypefaceFromTTF(item.getAssetPath()));
        textView.setBackground(mSelectedPosition == helper.getAdapterPosition() ?
                CommonUtils.getRadiusDrawable(3, mContext.getResources().getColor(R.color.color_fffc2b55),
                        4, mContext.getResources().getColor(R.color.color_ff101010)) :
                CommonUtils.getRadiusDrawable(4, mContext.getResources().getColor(R.color.color_ff242424)));
    }

    private Typeface getTypefaceFromTTF(String ttfPath) {
        if (TextUtils.isEmpty(ttfPath)) {
            return Typeface.DEFAULT;
        } else {
            try {
                return Typeface.createFromAsset(mContext.getAssets(), ttfPath);
            } catch (Exception e) {
                LogUtils.e(e);
            }
        }
        return Typeface.DEFAULT;
    }
}