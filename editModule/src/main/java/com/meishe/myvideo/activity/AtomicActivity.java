package com.meishe.myvideo.activity;

import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_PLUG_CLICK;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;
import com.meishe.base.adapter.CommonFragmentAdapter;
import com.meishe.base.model.BaseMvpActivity;
import com.meishe.base.view.CustomViewPager;
import com.meishe.engine.bean.PlugKind;
import com.meishe.engine.bean.bridges.AtomicFxBridge;
import com.meishe.myvideo.R;
import com.meishe.myvideo.activity.iview.PlugsView;
import com.meishe.myvideo.activity.presenter.PlugsPresenter;
import com.meishe.myvideo.event.MessageEvent;
import com.meishe.myvideo.fragment.PlugsFragment;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: ChuChenGuang
 * @CreateDate: 2022/6/20 14:18
 * @Description: 原子特效Activity The atomic activity
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class AtomicActivity extends BaseMvpActivity<PlugsPresenter> implements PlugsView {

    private CustomViewPager mViewPager;
    private TabLayout mTabLayout;

    @Override
    protected int bindLayout() {
        return R.layout.activity_plugs;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        EventBus.getDefault().register(this);
    }

    @Override
    protected void initView() {
        mTabLayout = findViewById(R.id.tabLayout);
        mViewPager = findViewById(R.id.viewPager);
        ImageView back = findViewById(R.id.iv_back);
        back.setOnClickListener(view -> onBackPressed());
        List<PlugsFragment> mPlugsFragments = new ArrayList<>();
        List<PlugKind> plugKinds = AtomicFxBridge.getPlugKindList();
        View tabView;
        for (int i = 0; i < plugKinds.size(); i++) {
            PlugKind kind = plugKinds.get(i);
            tabView = View.inflate(this, R.layout.custon_title_plug_activity, null);
            TextView tvTitle = tabView.findViewById(R.id.tv_title);
            if (i == 0) {
                tvTitle.setBackgroundResource(R.drawable.bg_title_plug_selected);
            }
            tvTitle.setText(plugKinds.get(i).getName());
            TabLayout.Tab tab = mTabLayout.newTab();
            tab.setCustomView(tabView);
            mTabLayout.addTab(tab);

            mPlugsFragments.add(PlugsFragment.create(AtomicFxBridge.getPlugList(kind.effectListPath), plug -> {
                MessageEvent.sendEvent(plug, MESSAGE_TYPE_PLUG_CLICK);
                finish();
            }));
        }

        FragmentPagerAdapter adapter = new CommonFragmentAdapter(getSupportFragmentManager(), mPlugsFragments);
        mViewPager.setAdapter(adapter);
        initListener();

    }

    private void initListener() {
        mTabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                mViewPager.setCurrentItem(tab.getPosition());
                View tabView = tab.getCustomView();
                if (tabView != null) {
                    TextView tvTitle = tabView.findViewById(R.id.tv_title);
                    if (tvTitle != null) {
                        tvTitle.setBackgroundResource(R.drawable.bg_title_plug_selected);
                    }
                }
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                View tabView = tab.getCustomView();
                if (tabView != null) {
                    TextView tvTitle = tabView.findViewById(R.id.tv_title);
                    if (tvTitle != null) {
                        tvTitle.setBackgroundResource(R.drawable.bg_title_plug);
                    }
                }
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });

        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int i, float v, int i1) {

            }

            @Override
            public void onPageSelected(int i) {
                mTabLayout.selectTab(mTabLayout.getTabAt(i));
            }

            @Override
            public void onPageScrollStateChanged(int i) {

            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

    /**
     * On message event.
     * 消息事件
     *
     * @param event the event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(MessageEvent event) {

    }
}