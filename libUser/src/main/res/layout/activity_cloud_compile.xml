<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/color_ff101010">

    <ImageView
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:id="@+id/iv_compile_back"
        android:layout_width="@dimen/dp_px_54"
        android:layout_height="@dimen/dp_px_54"
        android:layout_marginStart="@dimen/dp_px_39"
        android:layout_marginLeft="@dimen/dp_px_39"
        android:layout_marginTop="@dimen/dp_px_96"
        android:contentDescription="@null"
        android:src="@mipmap/ic_cloud_compile_close" />

    <ImageView
        app:layout_constraintTop_toBottomOf="@+id/iv_compile_back"
        android:id="@+id/iv_cover"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_660"
        android:layout_gravity="center_horizontal"
        android:contentDescription="@null" />

    <TextView
        android:layout_marginTop="@dimen/dp_px_168"
        app:layout_constraintTop_toBottomOf="@+id/iv_cover"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginStart="@dimen/dp_px_165"
        android:id="@+id/tv_job_name"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="@dimen/dp_px_70"
        android:text="@string/job_name"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_42" />
    <EditText
        android:autofillHints="@null"
        android:textColor="@color/white"
        android:textCursorDrawable="@drawable/input_cursor"
        android:hint="@string/tab_draft_compile_1"
        android:id="@+id/et_job_name"
        android:textSize="@dimen/sp_px_36"
        app:layout_constraintLeft_toLeftOf="@+id/tv_job_name"
        app:layout_constraintTop_toBottomOf="@+id/tv_job_name"
        android:layout_width="match_parent"
        android:layout_marginStart="@dimen/dp_px_165"
        android:layout_marginEnd="@dimen/dp_px_165"
        android:theme="@style/DialogEditText"
        android:inputType="text"
        android:layout_height="@dimen/dp_px_105"/>
    <!--分辨率-->
    <TextView
        app:layout_constraintTop_toBottomOf="@+id/et_job_name"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="@dimen/dp_px_30"
        android:layout_marginLeft="@dimen/dp_px_165"
        android:id="@+id/tv_resolution_title"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="@dimen/dp_px_70"
        android:text="@string/video_resolution"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_42" />

    <TextView
        app:layout_constraintTop_toTopOf="@+id/tv_resolution_title"
        app:layout_constraintBottom_toBottomOf="@+id/tv_resolution_title"
        app:layout_constraintRight_toRightOf="parent"
        android:id="@+id/tv_resolution_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_px_165"
        android:text="@string/video_resolution_info"
        android:textColor="@color/color_ffffa4a4"
        android:textSize="@dimen/sp_px_30" />

    <com.meishe.base.view.CustomCompileParamView
        app:layout_constraintTop_toBottomOf="@+id/tv_resolution_hint"
        android:layout_marginStart="@dimen/dp_px_165"
        android:layout_marginEnd="@dimen/dp_px_165"
        android:id="@+id/custom_resolution"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_px_30" />

    <!--帧率-->
    <TextView
        android:id="@+id/tv_frame_rate_title"
        app:layout_constraintTop_toBottomOf="@+id/custom_resolution"
        app:layout_constraintLeft_toLeftOf="@+id/tv_job_name"
        android:layout_marginTop="@dimen/dp_px_30"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="@string/video_frame_rate"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_42" />

    <TextView
        android:id="@+id/tv_frame_rate_hint"
        app:layout_constraintTop_toTopOf="@+id/tv_frame_rate_title"
        app:layout_constraintBottom_toBottomOf="@+id/tv_frame_rate_title"
        app:layout_constraintRight_toRightOf="@+id/tv_resolution_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/video_frame_rate_info"
        android:textColor="@color/color_ffffa4a4"
        android:textSize="@dimen/sp_px_30" />

    <com.meishe.base.view.CustomCompileParamView
        app:layout_constraintTop_toBottomOf="@+id/tv_frame_rate_hint"
        android:id="@+id/custom_frame_rate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_px_165"
        android:layout_marginEnd="@dimen/dp_px_165"
        android:layout_marginTop="@dimen/dp_px_30" />

    <Button
        app:layout_constraintBottom_toTopOf="@+id/tv_result"
        android:id="@+id/tv_compile"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_100"
        android:layout_marginTop="@dimen/dp_px_60"
        android:layout_marginStart="@dimen/dp_px_165"
        android:layout_marginEnd="@dimen/dp_px_165"
        android:text="@string/commit_cloud_compile"
        android:layout_marginBottom="@dimen/dp_px_42"
        android:background="@mipmap/ic_cloud_compile_button_bg"
        android:textColor="@color/white" />
    <TextView
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginStart="@dimen/dp_px_252"
        android:layout_marginEnd="@dimen/dp_px_252"
        android:layout_marginBottom="@dimen/dp_px_138"
        android:id="@+id/tv_result"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_px_810"
        android:gravity="center"
        android:text="@string/commit_cloud_compile_hint"
        android:textColor="@color/color_ff6e6e6e"
        android:textSize="@dimen/sp_px_27"/>

</androidx.constraintlayout.widget.ConstraintLayout>