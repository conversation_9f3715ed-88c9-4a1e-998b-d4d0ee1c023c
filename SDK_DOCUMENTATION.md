# XbotVideoEdit SDK 使用文档

## 目录

- [简介](#简介)
- [快速开始](#快速开始)
- [核心概念](#核心概念)
- [基础功能](#基础功能)
- [高级功能](#高级功能)
- [API参考](#api参考)
- [常见问题](#常见问题)

## 简介

XbotVideoEdit SDK是一个功能强大的视频编辑工具包，为Android应用开发者提供全面的视频编辑能力。SDK支持视频剪辑、特效添加、转场、字幕、贴纸等多种功能，帮助开发者快速构建专业级视频编辑应用。

### 架构概述

XbotVideoEdit SDK采用分层架构，包含两个主要模块：

1. **Core Module** - 视频编辑核心逻辑层，负责底层视频编辑功能的实现
2. **SDK Module** - 用户接口层，包含业务逻辑和UI组件，作为中间层解耦业务逻辑与核心编辑功能

这种分层设计使得：
- 开发者可以通过SDK模块快速集成视频编辑功能
- 核心功能与业务逻辑分离，便于维护和升级
- 在迁移或替换底层编辑引擎时，上层应用受影响最小

### 主要特性

- **全面的视频编辑功能**：剪切、拼接、转场、滤镜、特效等
- **丰富的音频处理**：音频混合、变速、变调等
- **强大的文字和贴纸**：支持多种字体、动画贴纸等
- **高性能渲染引擎**：流畅的预览和快速的导出
- **易于集成**：简洁的API设计，易于集成到现有应用中

## 快速开始

### 系统要求

- Android 5.0 (API级别21)或更高版本
- 支持OpenGL ES 3.0的设备

### 安装

1. 在项目的`build.gradle`文件中添加依赖：

```gradle
dependencies {
    // 使用SDK模块（包含业务逻辑和UI组件）
    implementation 'com.xbot.videoedit:sdk:1.0.0'

    // 如果只需要核心功能，可以只依赖Core模块
    // implementation 'com.xbot.videoedit:core:1.0.0'
}
```

2. 在应用的`AndroidManifest.xml`中添加必要的权限：

```xml
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.RECORD_AUDIO" />
```

### 初始化SDK

在应用的`Application`类或主`Activity`中初始化SDK：

```java
// 导入SDK模块的主类
import com.xbot.videoedit.sdk.XbotVideoEditSDK;

public class MyApplication extends Application {
    @Override
    public void onCreate() {
        super.onCreate();

        // 初始化SDK
        XbotVideoEditSDK.init(this, "/storage/emulated/0/XbotVideoEdit");
    }
}
```

### 使用核心模块（高级用法）

如果需要直接使用核心模块的功能，可以这样初始化：

```java
import com.xbot.videoedit.core.XbotStreamingContext;

public class MyApplication extends Application {
    @Override
    public void onCreate() {
        super.onCreate();

        // 初始化核心模块
        XbotStreamingContext.init(this, "/storage/emulated/0/XbotVideoEdit");
    }
}
```

### 创建简单的视频编辑项目

#### 使用SDK模块（推荐）

```java
// 使用SDK模块的编辑器创建视频项目
import com.xbot.videoedit.sdk.api.XbotVideoEditor;
import com.xbot.videoedit.sdk.model.VideoEditProject;
import com.xbot.videoedit.sdk.ui.editor.EditorActivity;

// 创建新项目
VideoEditProject project = XbotVideoEditor.createProject("My Video Project", 1920, 1080);

// 添加媒体资源
ArrayList<String> mediaPaths = new ArrayList<>();
mediaPaths.add("/storage/emulated/0/DCIM/Camera/video1.mp4");
mediaPaths.add("/storage/emulated/0/DCIM/Camera/video2.mp4");
XbotVideoEditor.addMediaToProject(project, mediaPaths);

// 打开编辑器
Intent intent = new Intent(this, EditorActivity.class);
intent.putExtra("project_id", project.getId());
startActivityForResult(intent, REQUEST_VIDEO_EDIT);

// 在onActivityResult中处理编辑结果
@Override
protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    if (requestCode == REQUEST_VIDEO_EDIT && resultCode == RESULT_OK) {
        // 获取导出的视频路径
        String outputPath = data.getStringExtra("output_path");
        // 处理导出的视频
    }
}
```

#### 使用核心模块（高级用法）

```java
// 直接使用核心模块创建视频项目
import com.xbot.videoedit.core.XbotStreamingContext;
import com.xbot.videoedit.core.video.XbotVideoResolution;
import com.xbot.videoedit.core.timeline.XbotTimeline;
import com.xbot.videoedit.core.track.XbotVideoTrack;
import com.xbot.videoedit.core.clip.XbotVideoClip;
import com.xbot.videoedit.core.effect.XbotVideoFx;
import com.xbot.videoedit.core.ui.XbotLiveWindow;

// 创建时间线
XbotVideoResolution resolution = new XbotVideoResolution(1920, 1080);
XbotTimeline timeline = XbotStreamingContext.getInstance().createTimeline(resolution);

// 添加视频轨道
XbotVideoTrack videoTrack = timeline.appendVideoTrack();

// 添加视频片段
String videoPath = "/storage/emulated/0/DCIM/Camera/video.mp4";
XbotVideoClip videoClip = videoTrack.appendClip(videoPath);

// 添加滤镜特效
XbotVideoFx videoFx = videoClip.appendFx("Filter", "warm");
videoFx.setFloatVal("intensity", 0.8f);

// 预览视频
XbotLiveWindow liveWindow = findViewById(R.id.live_window);
XbotStreamingContext.getInstance().connectTimelineWithLiveWindow(timeline, liveWindow);
XbotStreamingContext.getInstance().playbackTimeline(timeline, 0, timeline.getDuration());

// 导出视频
String outputPath = "/storage/emulated/0/Movies/output.mp4";
XbotStreamingContext.getInstance().compileTimeline(timeline, outputPath, new CompileCallback() {
    @Override
    public void onCompileCompleted() {
        // 导出完成
    }

    @Override
    public void onCompileFailed(int errorCode) {
        // 导出失败
    }

    @Override
    public void onCompileProgress(int progress) {
        // 导出进度更新
    }
});
```

## 核心概念

### 时间线 (Timeline)

时间线是视频编辑的核心概念，代表整个视频项目。所有的编辑操作都是在时间线上进行的。

### 轨道 (Track)

轨道是时间线的组成部分，用于容纳片段。一个时间线可以包含多个视频轨道和音频轨道。

### 片段 (Clip)

片段是轨道上的基本单位，可以是视频片段、音频片段等。

### 特效 (Effect)

特效用于为视频添加各种视觉效果，如滤镜、转场等。

### 贴纸 (Sticker)

贴纸是添加到视频上的图像或动画元素。

### 字幕 (Caption)

字幕用于在视频中添加文字内容。

## 基础功能

### 视频剪辑

```java
// 设置片段入点和出点
videoClip.setInPoint(1000000); // 1秒，单位为微秒
videoClip.setOutPoint(5000000); // 5秒

// 调整片段速度
videoClip.setSpeed(2.0f); // 2倍速
```

### 添加转场

```java
// 在两个片段之间添加转场
XbotVideoTransition transition = videoTrack.appendTransition(0, "Dissolve", 1000000); // 1秒转场
```

### 添加滤镜

```java
// 添加滤镜特效
XbotVideoFx filterFx = videoClip.appendFx("Filter", "sepia");
```

### 添加字幕

```java
// 添加字幕
XbotTimelineCaption caption = timeline.addCaption("Hello World", 1000000, 3000000); // 1-4秒显示
caption.setFontSize(48);
caption.setTextColor(new XbotColor(1.0f, 1.0f, 1.0f, 1.0f)); // 白色
```

### 添加贴纸

```java
// 添加贴纸
XbotTimelineAnimatedSticker sticker = timeline.addAnimatedSticker("sticker_package", "heart", 2000000, 5000000);
sticker.setScale(0.8f);
sticker.setRotationZ(45.0f); // 旋转45度
```

## 高级功能

### 画中画效果

```java
// 创建多个视频轨道实现画中画
XbotVideoTrack pipTrack = timeline.appendVideoTrack();
XbotVideoClip pipClip = pipTrack.appendClip("/storage/emulated/0/DCIM/Camera/pip_video.mp4");

// 设置画中画视频的位置和大小
XbotPosition2D position = new XbotPosition2D(0.7f, 0.7f); // 右下角
pipClip.setPosition(position);
pipClip.setScale(0.3f); // 缩小到30%
```

### 关键帧动画

```java
// 添加位置关键帧
long startTime = 0;
long endTime = 5000000; // 5秒
XbotPosition2D startPos = new XbotPosition2D(0.2f, 0.2f);
XbotPosition2D endPos = new XbotPosition2D(0.8f, 0.8f);

videoClip.addPositionKeyframe(startTime, startPos);
videoClip.addPositionKeyframe(endTime, endPos);
```

### 音频处理

```java
// 添加音频轨道
XbotAudioTrack audioTrack = timeline.appendAudioTrack();
XbotAudioClip audioClip = audioTrack.appendClip("/storage/emulated/0/Music/background.mp3");

// 调整音量
audioClip.setVolumeGain(0.5f); // 设置为50%音量

// 添加音频特效
XbotAudioFx audioFx = audioClip.appendFx("Reverb");
audioFx.setFloatVal("intensity", 0.6f);
```

### 自定义特效

```java
// 实现自定义视频特效
public class MyCustomVideoFx implements XbotCustomVideoFx.Renderer {
    @Override
    public void initialize(XbotCustomVideoFx.RenderContext context) {
        // 初始化特效
    }

    @Override
    public boolean render(XbotCustomVideoFx.VideoFrame srcFrame, XbotCustomVideoFx.VideoFrame dstFrame) {
        // 实现特效渲染逻辑
        return true;
    }
}

// 使用自定义特效
XbotCustomVideoFx customFx = videoClip.appendCustomFx(new MyCustomVideoFx());
```

## API参考

### SDK模块类（推荐使用）

#### 核心API

- `XbotVideoEditSDK` - SDK初始化和全局配置
- `XbotVideoEditor` - 视频编辑器主要API
- `VideoEditProject` - 视频编辑项目模型

#### UI组件

- `EditorActivity` - 编辑器主界面
- `MediaPickerActivity` - 媒体选择器
- `VideoPreviewActivity` - 视频预览界面
- `VideoPlayerView` - 视频播放器组件
- `TimelineView` - 时间线编辑组件
- `EffectPanelView` - 特效面板组件

#### 管理器

- `ProjectManager` - 项目管理
- `ResourceManager` - 资源管理
- `ExportManager` - 导出管理

### 核心模块类（高级用法）

#### 核心类

- `XbotStreamingContext` - 核心上下文
- `XbotTimeline` - 时间线
- `XbotVideoTrack` - 视频轨道
- `XbotAudioTrack` - 音频轨道
- `XbotVideoClip` - 视频片段
- `XbotAudioClip` - 音频片段
- `XbotVideoFx` - 视频特效
- `XbotAudioFx` - 音频特效
- `XbotVideoTransition` - 视频转场
- `XbotTimelineCaption` - 时间线字幕
- `XbotTimelineAnimatedSticker` - 时间线贴纸

#### 核心UI组件

- `XbotLiveWindow` - 视频预览窗口
- `XbotThumbnailSequenceView` - 缩略图序列
- `XbotWaveformView` - 音频波形显示

## 常见问题

### 内存管理

SDK内部使用了大量的原生资源，请确保在不再使用时释放资源：

```java
// 释放时间线资源
timeline.release();

// 关闭SDK
XbotStreamingContext.getInstance().shutdown();
```

### 性能优化

- 使用适当的视频分辨率，过高的分辨率会影响性能
- 减少同时应用的特效数量
- 在后台线程中执行导出操作

### 常见错误

- **错误码1001**：文件不存在或无法访问
  - 解决方案：检查文件路径和权限

- **错误码1002**：不支持的文件格式
  - 解决方案：使用支持的视频/音频格式

- **错误码1003**：内存不足
  - 解决方案：减少项目复杂度或释放不必要的资源

## 联系与支持

如有任何问题或建议，请联系我们：

- 技术支持邮箱：<EMAIL>
- 官方网站：https://www.xbottech.com
- GitHub仓库：https://github.com/XbotTech/VideoEditing-Android
