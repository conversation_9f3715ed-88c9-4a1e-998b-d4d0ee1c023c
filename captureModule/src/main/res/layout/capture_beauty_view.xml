<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_252525"
    android:orientation="vertical">

    <!--美型seekbar-->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_150"
        android:layout_marginLeft="@dimen/dp_px_216"
        android:layout_marginRight="@dimen/dp_px_216"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.meishe.capturemodule.view.MagicProgress
            android:id="@+id/shape_sb"
            android:layout_width="0dp"
            android:layout_height="45dp"
            android:layout_weight="1"
            android:focusable="true"
            android:progressDrawable="@drawable/seekbar"
            android:thumb="@drawable/beauty_seek_thumb"
            android:visibility="invisible"
            app:textSizeMagic="12sp" />

    </LinearLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/beauty_shape_item_list"
        android:layout_marginTop="@dimen/dp_px_15"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="13dp"
        android:layout_marginRight="13dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/dp_px_45"
        android:background="#343434" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/dp_px_90">

        <TextView
            android:id="@+id/beauty_shape_reset_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="@dimen/dp_px_120"
            android:paddingTop="@dimen/dp_px_30"
            android:text="@string/reset"
            android:textColor="@color/beauty_reset_color_selector"
            android:textSize="11dp" />


        <TextView
            android:id="@+id/beauty_shape_switch_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_gravity="center_vertical"
            android:paddingLeft="@dimen/dp_px_30"
            android:paddingTop="@dimen/dp_px_30"
            android:paddingRight="@dimen/dp_px_120"
            android:text="@string/beauty_close"
            android:textColor="@color/color_f2f2f2"
            android:textSize="11dp" />
    </RelativeLayout>


</LinearLayout>