package com.meishe.myvideo.view.interf;

import android.content.Context;

import com.meishe.engine.interf.IBaseInfo;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 基础契约类
 * Base contract class
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/28 15:00
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class BaseContract {
    /**
     * The interface Model.
     * model接口
     */
    public interface Model {
    }

    /**
     * The interface Mvp view.
     * mvp视图接口
     */
    public interface MvpView {
        void initView();

        void initData();

        /**
         * Sets progress.
         * 设置进度
         *
         * @param progress the progress 进度
         */
        void setProgress(float progress);

        /**
         * Update select position.
         * 更新选择位置
         *
         * @param selection the selection 选择
         */
        void updateSelectPosition(int selection);

        void onDataBack(List<IBaseInfo> list, int index);

        void onDataChanged(IBaseInfo baseInfo);
    }

    /**
     * The interface Presenter.
     * Presenter接口
     *
     * @param <V> the type parameter 类型参数
     */
    public interface Presenter<V> {
        /**
         * Attached to View
         * 绑定到View上
         * @param view the view
         */
        void attachView(V view);

        /**
         * Detach.
         * 分离
         */
        void detach();

        /**
         * Get view
         * 获取View的引用
         * @return
         */
        V getView();

        /**
         * Gets data.
         * 获取数据
         *
         * @param context the context 上下文
         * @return the data 数据
         */
        void getData(Context context);

        /**
         * Update data
         * 更新数据
         *
         * @param subType         数据的次类型subtype of data
         * @param needForceUpdate 是否需要强制更新数据 Whether need force update.
         * @return 是否正在更新 Updating or not. true：yes； false：no
         */
        boolean updateData(int subType, boolean needForceUpdate);
    }
}
