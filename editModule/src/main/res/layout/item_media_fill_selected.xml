<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/real_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/iv_cover"
            android:layout_width="@dimen/dp_px_180"
            android:layout_height="@dimen/dp_px_180"
            android:layout_marginTop="@dimen/dp_px_24"
            android:contentDescription="@null"
            android:scaleType="centerCrop" />

        <TextView
            android:id="@+id/tv_index"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/iv_cover"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dp_px_10"
            android:textColor="@color/color_ff707070"
            android:textSize="@dimen/sp_px_33" />
    </RelativeLayout>

    <ImageView
        android:id="@+id/iv_delete"
        android:layout_width="@dimen/dp_px_51"
        android:layout_height="@dimen/dp_px_51"
        android:layout_marginStart="@dimen/dp_px_154"
        android:layout_marginLeft="@dimen/dp_px_154"
        android:contentDescription="@null"
        android:scaleType="fitXY"
        android:src="@mipmap/close_white_round" />

    <TextView
        android:id="@+id/tv_duration"
        android:layout_width="@dimen/dp_px_100"
        android:layout_height="@dimen/dp_px_50"
        android:layout_alignTop="@+id/real_image"
        android:layout_marginStart="@dimen/dp_px_40"
        android:layout_marginLeft="@dimen/dp_px_40"
        android:layout_marginTop="@dimen/dp_px_87"
        android:gravity="center"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_px_33" />


</RelativeLayout>
