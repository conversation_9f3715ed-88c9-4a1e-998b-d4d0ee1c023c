package com.meishe.engine;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Point;
import android.graphics.PointF;
import android.text.TextUtils;
import android.util.Pair;
import android.view.Gravity;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsAssetPackageManager;
import com.meicam.sdk.NvsCaption;
import com.meicam.sdk.NvsCompoundCaption;
import com.meicam.sdk.NvsIconGenerator;
import com.meicam.sdk.NvsLiveWindowExt;
import com.meicam.sdk.NvsMaskRegionInfo;
import com.meicam.sdk.NvsPosition2D;
import com.meicam.sdk.NvsRational;
import com.meicam.sdk.NvsSize;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsTimelineAnimatedSticker;
import com.meicam.sdk.NvsTimelineCaption;
import com.meicam.sdk.NvsVideoFx;
import com.meicam.sdk.NvsVideoResolution;
import com.meishe.base.bean.FloatPoint;
import com.meishe.base.bean.MediaData;
import com.meishe.base.constants.Constants;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.GsonUtils;
import com.meishe.base.utils.ImageUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;
import com.meishe.engine.adapter.LGsonContext;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.bean.AnimationData;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.engine.bean.ClipInfo;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.KeyFrameProcessor;
import com.meishe.engine.bean.MaskRegionInfoData;
import com.meishe.engine.bean.MeicamAudioClip;
import com.meishe.engine.bean.MeicamAudioTrack;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.engine.bean.MeicamCompoundCaptionClip;
import com.meishe.engine.bean.MeicamFxParam;
import com.meishe.engine.bean.MeicamKeyFrame;
import com.meishe.engine.bean.MeicamMaskRegionInfo;
import com.meishe.engine.bean.MeicamPosition2D;
import com.meishe.engine.bean.MeicamStickerCaptionTrack;
import com.meishe.engine.bean.MeicamStickerClip;
import com.meishe.engine.bean.MeicamTheme;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamTimelineVideoFilterAndAdjustClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxTrack;
import com.meishe.engine.bean.MeicamTransition;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoFx;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.engine.bean.MeicamWaterMark;
import com.meishe.engine.bean.NvMaskModel;
import com.meishe.engine.bean.PlugDetail;
import com.meishe.engine.bean.StickerAnimation;
import com.meishe.engine.bean.Transform;
import com.meishe.engine.bean.template.TemplateClip;
import com.meishe.engine.command.AudioCommand;
import com.meishe.engine.command.AudioTrackCommand;
import com.meishe.engine.command.CaptionCommand;
import com.meishe.engine.command.CaptionStickerTrackCommand;
import com.meishe.engine.command.ClipCommand;
import com.meishe.engine.command.FilterAndAdjustClipCommand;
import com.meishe.engine.command.KeyFrameCommand;
import com.meishe.engine.command.KeyFrameHolderCommand;
import com.meishe.engine.command.StickerCommand;
import com.meishe.engine.command.TimelineCommand;
import com.meishe.engine.command.TimelineFxCommand;
import com.meishe.engine.command.TimelineVideoFxTrackCommand;
import com.meishe.engine.command.TransitionCommand;
import com.meishe.engine.command.VideoClipCommand;
import com.meishe.engine.command.VideoFxCommand;
import com.meishe.engine.command.VideoTrackCommand;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.editor.EditorController;
import com.meishe.engine.interf.EditOperater;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.engine.interf.IClip;
import com.meishe.engine.interf.IKeyFrameProcessor;
import com.meishe.engine.local.LTimelineData;
import com.meishe.engine.observer.EngineCallbackObserver;
import com.meishe.engine.util.ColorUtil;
import com.meishe.engine.util.CoordinateUtil;
import com.meishe.engine.util.NvLineTool;
import com.meishe.engine.util.PathUtils;
import com.meishe.engine.util.TimelineFxBinder;
import com.meishe.engine.util.TimelineUtil;
import com.meishe.engine.util.gson.GsonContext;
import com.meishe.logic.bean.SettingParameter;
import com.meishe.logic.manager.PreferencesManager;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;

import static com.meicam.sdk.NvsStreamingContext.STREAMING_CONTEXT_VIDEO_DECODER_WITHOUT_SURFACE_TEXTURE;
import static com.meicam.sdk.NvsStreamingContext.STREAMING_ENGINE_STATE_PLAYBACK;
import static com.meishe.base.constants.Constants.BASEITEM_MIN_DURATION;
import static com.meishe.base.constants.Constants.TIME_BASE;
import static com.meishe.base.constants.Constants.TRACK_INDEX_MAIN;
import static com.meishe.engine.asset.bean.AssetInfo.ASSET_ANIMATION_GROUP;
import static com.meishe.engine.asset.bean.AssetInfo.ASSET_ANIMATION_IN;
import static com.meishe.engine.asset.bean.AssetInfo.ASSET_ANIMATION_OUT;
import static com.meishe.engine.bean.AnimationData.POST_PACKAGE;
import static com.meishe.engine.bean.BaseInfo.AspectRatio_NoFitRatio;
import static com.meishe.engine.bean.CommonData.CLIP_HOLDER;
import static com.meishe.engine.bean.CommonData.DEFAULT_LENGTH_FX;
import static com.meishe.engine.bean.CommonData.MAIN_TRACK_INDEX;
import static com.meishe.engine.bean.CommonData.TIMELINE_RESOLUTION_VALUE;
import static com.meishe.engine.bean.CommonData.TYPE_AI_CAPTION;
import static com.meishe.engine.bean.CommonData.TYPE_RAW_BUILTIN;
import static com.meishe.engine.bean.MeicamCaptionClip.CAPTION_ALIGN_BOTTOM;
import static com.meishe.engine.bean.MeicamCaptionClip.CAPTION_ALIGN_HORIZ_CENTER;
import static com.meishe.engine.bean.MeicamCaptionClip.CAPTION_ALIGN_LEFT;
import static com.meishe.engine.bean.MeicamCaptionClip.CAPTION_ALIGN_RIGHT;
import static com.meishe.engine.bean.MeicamCaptionClip.CAPTION_ALIGN_TOP;
import static com.meishe.engine.bean.MeicamCaptionClip.CAPTION_ALIGN_VERT_CENTER;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_BOOLEAN;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_FLOAT;
import static com.meishe.engine.bean.MeicamKeyFrame.CAPTION_ROTATION_Z;
import static com.meishe.engine.bean.MeicamKeyFrame.CAPTION_SCALE_X;
import static com.meishe.engine.bean.MeicamKeyFrame.CAPTION_SCALE_Y;
import static com.meishe.engine.bean.MeicamKeyFrame.CAPTION_TRANS_X;
import static com.meishe.engine.bean.MeicamKeyFrame.CAPTION_TRANS_Y;
import static com.meishe.engine.bean.MeicamKeyFrame.SCALE_X;
import static com.meishe.engine.bean.MeicamKeyFrame.STICKER_ROTATION_Z;
import static com.meishe.engine.bean.MeicamKeyFrame.STICKER_SCALE;
import static com.meishe.engine.bean.MeicamKeyFrame.STICKER_TRANS_X;
import static com.meishe.engine.bean.MeicamKeyFrame.STICKER_TRANS_Y;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_ADJUST;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_ALPHA;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_CROPPER;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_CROPPER_EXT;
import static com.meishe.engine.command.VideoClipCommand.PARAM_VIDEO_IS_REVERSE;
import static com.meishe.engine.command.VideoClipCommand.PARAM_VIDEO_THUMBNAIL_INFO;
import static com.meishe.engine.constant.NvsConstants.ALPHA_CLIP_TRIM_USED;
import static com.meishe.engine.constant.NvsConstants.ALPHA_FILE;
import static com.meishe.engine.constant.NvsConstants.KEY_BACKGROUND_IMAGE_PATH;
import static com.meishe.engine.constant.NvsConstants.SET_ALPHA;
import static com.meishe.engine.constant.NvsConstants.TYPE_BUILD_IN;
import static com.meishe.engine.constant.NvsConstants.TYPE_PACKAGE;
import static com.meishe.engine.editor.EditorController.ASSET_PACKAGE_MANAGER_ERROR_SDK_VERSION;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 版权所有:www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/18 10:20
 * @Description : 封装sdk api. Encapsulation sdk api.详细方法请参考：https://www.meishesdk.com/android/doc_ch/html/content/index.html
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class EditorEngine implements EditOperater {

    /**
     * 动画的默认时长
     * The constant ANIMATION_DEFAULT_DURATION.
     */
    public static int ANIMATION_DEFAULT_DURATION = 600;
    /**
     * 入动画和出动画的默认时长
     * The constant IN_OUT_ANIMATION_DEFAULT_DURATION.
     */
    public static int IN_OUT_ANIMATION_DEFAULT_DURATION = 500;
    private int mMakeRatio = AspectRatio_NoFitRatio;
    private NvsIconGenerator mIconGenerator;

    public void setMakeRatio(int makeRatio) {
        if (meicamTimeline != null) {
            meicamTimeline.setMakeRatio(makeRatio);
        }
        mMakeRatio = makeRatio;
    }

    /**
     * Remove empty sticker caption track in the end
     * 删除尾部的贴纸字幕空轨道
     */
    public void removeEmptyStickerCaptionTrackInTheEnd() {
        MeicamTimeline timeline = getCurrentTimeline();
        int trackCount = timeline.getStickerCaptionTrackCount();
        if (trackCount > 0) {
            for (int index = trackCount - 1; index >= 0; index--) {
                MeicamStickerCaptionTrack stickCaptionTrack = timeline.findStickCaptionTrack(index);
                if (stickCaptionTrack != null && stickCaptionTrack.getClipCount() > 0) {
                    if (stickCaptionTrack.getClipCount() > 0) {
                        break;
                    }
                    TimelineCommand.removeStickCaptionTrack(timeline, index);
                }
            }
        }
    }

    /**
     * Remove empty timeline fx track in the end.
     * 删除尾部的时间线特技的空轨道
     */
    public void removeEmptyTimelineFxTrackInTheEnd() {
        MeicamTimeline meicamTimeline = getCurrentTimeline();
        int timelineFxTrackCount = meicamTimeline.getTimelineFxTrackCount();
        if (timelineFxTrackCount > 0) {
            for (int index = timelineFxTrackCount - 1; index >= 0; index--) {
                MeicamTimelineVideoFxTrack timelineFxTrack = meicamTimeline.getTimelineFxTrack(index);
                if (timelineFxTrack != null && timelineFxTrack.getClipCount() > 0) {
                    break;
                }
                TimelineCommand.removeTimelineFxTrack(meicamTimeline, index);
            }
        }
    }

    /**
     * Remove empty filter and adjust track in the end.
     * 删除尾部的滤镜和调节空轨道
     */
    public void removeEmptyFilterAndAdjustTrackInTheEnd() {
        MeicamTimeline meicamTimeline = getCurrentTimeline();
        int trackCount = meicamTimeline.getFilterAndAdjustTimelineTracksCount();
        if (trackCount > 0) {
            for (int index = trackCount - 1; index >= 0; index--) {
                MeicamTimelineVideoFxTrack track = meicamTimeline.getFilterAndAdjustTimelineTrack(index);
                if (track != null && track.getFilterAndAdjustCount() > 0) {
                    break;
                }
                TimelineCommand.removeFilterAndAdjustTrack(meicamTimeline, index);
            }
        }
    }

    /**
     * Remove empty audio track in the end.
     * 删除尾部的音乐空轨道
     */
    public void removeEmptyAudioTrackInTheEnd() {
        MeicamTimeline meicamTimeline = getCurrentTimeline();
        int trackCount = meicamTimeline.getAudioTrackCount();
        if (trackCount > 0) {
            for (int index = trackCount - 1; index >= 0; index--) {
                MeicamAudioTrack track = meicamTimeline.getAudioTrack(index);
                if (track != null && track.getClipCount() > 0) {
                    break;
                }
                TimelineCommand.removeAudioTrack(meicamTimeline, index);
            }
        }
    }

    /**
     * Is use segmentation boolean.
     * 是否有segmentation 特技
     *
     * @return the boolean
     */
    public boolean isUseSegmentation() {
        MeicamTimeline currentTimeline = getCurrentTimeline();
        if (currentTimeline == null) {
            return false;
        }
        int count = currentTimeline.videoTrackCount();
        if (count > 0) {
            for (int index = 0; index < count; index++) {
                MeicamVideoTrack videoTrack = currentTimeline.getVideoTrack(index);
                if (videoTrack != null) {
                    int clipCount = videoTrack.getClipCount();
                    if (clipCount > 0) {
                        for (int clipIndex = 0; clipIndex < clipCount; clipIndex++) {
                            MeicamVideoClip videoClip = videoTrack.getVideoClip(clipIndex);
                            if (videoClip != null) {
                                MeicamVideoFx videoFxById = videoClip.getVideoFxById(NvsConstants.SEGMENTATION);
                                if (videoFxById != null) {
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    public NvsIconGenerator getIconGenerator() {
        if (mIconGenerator == null) {
            mIconGenerator = new NvsIconGenerator();
        }
        return mIconGenerator;
    }

    /**
     * 编辑视频的音频淡入淡出
     * Video edit audio transition.
     *
     * @param videoClip the video clip
     * @param fadeIn    the fade in
     * @param fadeOut   the fade out
     */
    public void videoEditAudioTransition(MeicamVideoClip videoClip, long fadeIn, long fadeOut) {
        VideoClipCommand.setParam(videoClip, VideoClipCommand.PARAM_FADE_IN_DURATION, fadeIn);
        VideoClipCommand.setParam(videoClip, VideoClipCommand.PARAM_FADE_OUT_DURATION, fadeOut);
    }


    /**
     * 返回值枚举
     * Enum of return code enumeration of return values
     */
    public static class ReturnCode {

        /**
         * 正确
         * Result is ok
         */
        public static final int CODE_OK = 1;
        /**
         * 参数错误
         * Param is error
         */
        public static final int CODE_PARAM_ERROR = 2;

        /**
         * 此操作不允许执行
         * Can not do this operation,
         */
        public static final int CODE_CAN_NOT_OPERATE = 4;
        /**
         * 其它错误
         * Other error
         */
        public static final int CODE_OTHER = 8;
    }

    private MeicamTimeline meicamTimeline;
    private NvsStreamingContext mStreamingContext;
    private OnTimelineChangeListener mOnTimelineChangeListener;
    private static EditorEngine INSTANCE = null;

    /**
     * 选中的音乐片段
     * Selected piece of music
     */
    private MeicamAudioTrack mMeicamAudioTrack = null;
    private MeicamAudioClip mMeicamAudioClip = null;
    private OnTrackChangeListener mOnTrackChangeListener;
    private boolean hasAudio = false;

    /**
     * use Face 是否使用到了人脸美型功能
     * Have you used the facial beauty function
     */
    private boolean useFaceShape = false;
    private PointF mLiveWindowSize = new PointF();
    private static String mLicPath;

    private EditorEngine() {
        mStreamingContext = NvsStreamingContext.getInstance();
        if (mStreamingContext == null) {
            initStreamContext();
        }
    }

    public static void init(String licPath) {
        mLicPath = licPath;
        getInstance();
    }

    public static EditorEngine getInstance() {
        if (INSTANCE == null) {
            INSTANCE = new EditorEngine();
        }
        return INSTANCE;
    }

    /**
     * Init stream context nvsstreamingcontext.
     * 初始化NvsStreamingContext
     *
     * @return the nvs streaming context
     */
    public NvsStreamingContext initStreamContext() {
        mStreamingContext = NvsStreamingContext.init(Utils.getApp(), mLicPath,
                NvsStreamingContext.STREAMING_CONTEXT_FLAG_SUPPORT_4K_EDIT
                        | NvsStreamingContext.STREAMING_CONTEXT_FLAG_ENABLE_HDR_DISPLAY_WHEN_SUPPORTED
                        | NvsStreamingContext.STREAMING_CONTEXT_FLAG_INTERRUPT_STOP_FOR_INTERNAL_STOP
                        | NvsStreamingContext.STREAMING_CONTEXT_FLAG_NEED_GIF_MOTION);
        int engineHDRCaps = mStreamingContext.getEngineHDRCaps();
        CommonData.SUPPORT_HDR = engineHDRCaps ==
                (NvsStreamingContext.HDR_CAPABILITY_FLAG_SUPPORTED_BY_IMPORTER
                        | NvsStreamingContext.HDR_CAPABILITY_FLAG_SUPPORTED_BY_EDITING
                        | NvsStreamingContext.HDR_CAPABILITY_FLAG_SUPPORTED_BY_EXPORTER
                        | NvsStreamingContext.HDR_CAPABILITY_FLAG_SUPPORTED_BY_LIVEWINDOW);

        SettingParameter parameter = GsonUtils.fromJson(PreferencesManager.get().getSettingParams(), SettingParameter.class);
        float colorGain = -1;
        if (parameter != null) {
            NvsConstants.sHdrColorGain = parameter.getHdrColorGain();
            NvsConstants.sHdrBitDepth = parameter.getHdrBitDepth();
            NvsConstants.sHdrPreviewMode = parameter.getHdrPreViewMode();
        }
        if (NvsConstants.sHdrColorGain > 0) {
            mStreamingContext.setColorGainForSDRToHDR(colorGain);
        }
        return mStreamingContext;
    }

    /**
     * 创建辅助流媒体上下文对象
     * Create auxiliary streaming context
     *
     * @return NvsStreamingContext;
     */
    public NvsStreamingContext createAuxiliaryStreamingContext() {
        if (mStreamingContext != null) {
            return mStreamingContext.createAuxiliaryStreamingContext(NvsStreamingContext.STREAMING_CONTEXT_FLAG_SUPPORT_4K_EDIT | STREAMING_CONTEXT_VIDEO_DECODER_WITHOUT_SURFACE_TEXTURE);
        }
        return null;
    }

    /**
     * 销毁辅助流媒体上下文对象
     * Destroy auxiliary streaming context
     */
    public void destroyAuxiliaryStreamingContext(NvsStreamingContext streamingContext) {
        if (mStreamingContext != null) {
            mStreamingContext.destoryAuxiliaryStreamingContext(streamingContext);
        }
    }

    /**
     * 连接时间线和实时预览图像窗口
     * Connect timeline with live window
     *
     * @param timeline   the timeline
     * @param liveWindow the live window
     * @return Success or not. true:yes; false :no.
     */
    public boolean connectTimeline(MeicamTimeline timeline, NvsLiveWindowExt liveWindow) {
        if (mStreamingContext != null && timeline != null && liveWindow != null) {
            return timeline.connectToLiveWindow(mStreamingContext, liveWindow);
        }
        return false;
    }

    /**
     * 连接时间线和实时预览图像窗口
     * Connect timeline with live window
     *
     * @param liveWindow the live window
     * @return Success or not. true:yes; false :no.
     */
    public boolean connectTimeline(NvsLiveWindowExt liveWindow) {
        return connectTimeline(meicamTimeline, liveWindow);
    }

    /**
     * 预览时间线的某个时间点
     * Seek to the position of timeline
     *
     * @param timeline     the timeline
     * @param timestamp    the timestamp
     * @param seekShowMode the seek show mode
     * @return Is success or not. true:yes:false:no.
     */
    public boolean seekTimeline(MeicamTimeline timeline, long timestamp, int seekShowMode) {
        if (timeline == null) {
            return false;
        }
        return timeline.seekTimeline(getStreamingContext(), timestamp, seekShowMode);
    }


    /**
     * Grab image from timeline bitmap.
     * 从时间线中获取图片
     *
     * @param timeline    the timeline
     * @param position    the position 当前位置
     * @param nvsRational the nvs rational
     * @return the bitmap
     */
    public Bitmap grabImageFromTimeline(MeicamTimeline timeline, long position, NvsRational nvsRational) {
        if (timeline == null) {
            LogUtils.e("timeline is null");
            return null;
        }
        return timeline.grabImageFromTimeline(getStreamingContext(), position, nvsRational);
    }

    /**
     * Grab image from timeline bitmap async.
     * 从时间线中获取图片 异步方法
     *
     * @param timeline    the timeline
     * @param position    the position 当前位置
     * @param nvsRational the nvs rational
     */
    public void grabImageFromTimelineAsync(MeicamTimeline timeline, long position, NvsRational nvsRational) {
        if (timeline == null) {
            LogUtils.e("timeline is null");
            return;
        }
        timeline.grabImageFromTimelineAsync(getStreamingContext(), position, nvsRational, 0);
    }


    /**
     * Export template info boolean.
     * 导出模板信息
     *
     * @param timeline the timeline
     * @param uuid     the uuid
     * @param ratio    the ratio
     * @return the boolean
     */
    public boolean exportTemplateInfo(MeicamTimeline timeline, String uuid, int ratio) {
        if (timeline == null) {
            LogUtils.e("timeline is null");
            return false;
        }

        return timeline.exportTemplateInfo(getStreamingContext(), uuid, ratio);
    }

    /**
     * Export project info boolean.
     * 导出工程信息
     *
     * @param timeline the timeline
     * @param uuid     the uuid
     * @param ratio    the ratio
     * @return the boolean
     */
    public boolean exportProjectInfo(MeicamTimeline timeline, String uuid, int ratio) {
        if (timeline == null) {
            LogUtils.e("timeline is null");
            return false;
        }

        return timeline.exportProjectInfo(getStreamingContext(), uuid, ratio);
    }

    /**
     * 预览时间线的某个时间点
     * Seek to the position of timeline
     *
     * @param timestamp    the timestamp
     * @param seekShowMode the seek show mode
     */
    public boolean seekTimeline(long timestamp, int seekShowMode) {
        return seekTimeline(meicamTimeline, timestamp, seekShowMode);
    }


    /**
     * 播放
     * Play
     *
     * @param timeline  the timeline
     * @param startTime the start position
     * @param endTime   the end position
     */
    public boolean playBackTimeline(MeicamTimeline timeline, long startTime, long endTime) {
        return playBackTimeline(timeline, startTime, endTime, 0);
    }

    /**
     * 播放
     * Play
     *
     * @param timeline  the timeline
     * @param startTime the start position
     * @param endTime   the end position
     */
    public boolean playBackTimeline(MeicamTimeline timeline, long startTime, long endTime, int flag) {
        if (timeline == null || mStreamingContext == null) {
            return false;
        }
        return timeline.playBack(getStreamingContext(), startTime, endTime, flag);
    }

    /**
     * 播放
     * Play
     *
     * @param startTime the start position
     * @param endTime   the end position
     */
    public boolean playBackTimeline(long startTime, long endTime) {
        return playBackTimeline(meicamTimeline, startTime, endTime);
    }

    /**
     * Compile timeline
     * <p>
     * <p>
     * 导出视频
     *
     * @param timeline          the timeline 时间线
     * @param startTime         start time
     * @param endTime           end time
     * @param outputFilePath    path for save video 视频保存路径
     * @param resolutionGrade   resolution grade 分辨率等级
     * @param videoBitrateGrade video bitrate grade 码率级别
     * @param customHeight      custom height，
     *                          It will go into effect only when resolutionGrade is
     *                          {NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_CUSTOM}
     *                          自定义高度，只有在resolutionGrade
     *                          是NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_CUSTOM的时候才生效
     * @param flags             compile flags
     * @param compileConfig     config of compile 导出配置参数
     * @return Is compiling start success or not. true:yes;false no.
     */
    public boolean compileTimeline(MeicamTimeline timeline, long startTime,
                                   long endTime,
                                   String outputFilePath,
                                   int resolutionGrade,
                                   int customHeight,
                                   int videoBitrateGrade,
                                   int flags, Hashtable<String, Object> compileConfig) {
        if (timeline == null) {
            LogUtils.e("timeline is null");
            return false;
        }
        return timeline.compileTimeline(getStreamingContext(), startTime, endTime, outputFilePath,
                resolutionGrade, customHeight, videoBitrateGrade, flags, compileConfig);
    }

    /**
     * Sets on timeline change listener.
     *
     * @param onTimelineChangeListener the on timeline change listener
     */
    public void setOnTimelineChangeListener(OnTimelineChangeListener onTimelineChangeListener) {
        this.mOnTimelineChangeListener = onTimelineChangeListener;
    }

    /**
     * Sets on track change listener.
     *
     * @param mOnTrackChangeListener the m on track change listener
     */
    public void setOnTrackChangeListener(OnTrackChangeListener mOnTrackChangeListener) {
        this.mOnTrackChangeListener = mOnTrackChangeListener;
    }


    /**
     * Restore timeline.
     *
     * @param jsonData the json data from draft 草稿数据
     * @return the  timeline
     */
    public MeicamTimeline recoverTimeline(String jsonData) {
        if (TextUtils.isEmpty(jsonData)) {
            LogUtils.e("param error: json data is null");
            return null;
        }
        LTimelineData lTimelineData = LGsonContext.getInstance().fromJson(jsonData, LTimelineData.class);
        return new MeicamTimeline.TimelineBuilder(getStreamingContext(), MeicamTimeline.TimelineBuilder.BUILD_FORM_DRAFT)
                .setDraftData(lTimelineData)
                .build();
    }


    /**
     * Recover from cloud draft boolean.
     * 从
     *
     * @param templatePath the template path
     * @param assetsPath   the assets path
     * @param projectId    the project id
     * @param modifiedAt   the modified at time
     * @param callback     the callback
     */
    public void recoverFromCloudDraft(final String templatePath, final List<String> assetsPath, final String projectId, final String modifiedAt, final TimelineCreateCallback callback) {
        final NvsAssetPackageManager assetPackageManager = mStreamingContext.getAssetPackageManager();
        final String packageIdString = assetPackageManager.getAssetPackageIdFromAssetPackageFilePath(templatePath);
        if (TextUtils.isEmpty(packageIdString)) {
            if (callback != null) {
                callback.onTimelineCreate(null);
            }
            return;
        }
        try {
            //由于升级的存在，这里取巧了，先卸载后安装。 Because of the existence of upgrade, here take advantage of, install after uninstall first
            assetPackageManager.uninstallAssetPackage(packageIdString, NvsAssetPackageManager.ASSET_PACKAGE_TYPE_PROJECT);
        } catch (Exception e) {
            LogUtils.e("Exception=" + e);
            if (callback != null) {
                callback.onTimelineCreate(null);
            }
        }
        final StringBuilder packageId = new StringBuilder();
        EngineCallbackObserver mCallBackObserver = new EngineCallbackObserver() {
            @Override
            public boolean isActive() {
                return true;
            }

            @Override
            public void onFinishAssetPackageInstallation(final String packageId, String filePath, int packageType, int error) {
                EngineCallbackManager.get().unregisterCallbackObserver(this);
                LogUtils.d("error = " + error + ", packageId = " + packageId + ", filePath = " + packageIdString);
                String packageIdString = assetPackageManager.getAssetPackageIdFromAssetPackageFilePath(filePath);
                if (!TextUtils.equals(packageId, packageIdString)) {
                    if (callback != null) {
                        callback.onTimelineCreate(null);
                    }
                    return;
                }
                if ((error == EditorController.ASSET_PACKAGE_MANAGER_ERROR_NO_ERROR)
                        || (error == EditorController.ASSET_PACKAGE_MANAGER_ERROR_ALREADY_INSTALLED)) {
                    int groupIndex = -1;
                    List<NvsAssetPackageManager.NvsTemplateFootageDesc> templateList = new ArrayList<>();
                    List<NvsAssetPackageManager.NvsTemplateFootageDesc> nvsTemplateFootageDescList = assetPackageManager.getTemplateFootages(packageId);
                    if (!CommonUtils.isEmpty(nvsTemplateFootageDescList)) {
                        templateList.addAll(nvsTemplateFootageDescList);
                    }

                    List<TemplateClip> clipList = new ArrayList<>();
                    for (NvsAssetPackageManager.NvsTemplateFootageDesc footage : templateList) {
                        ArrayList<NvsAssetPackageManager.NvsTemplateFootageCorrespondingClipInfo> correspondingClipInfos = footage.correspondingClipInfos;
                        boolean hasGroup = false;
                        if (!CommonUtils.isEmpty(correspondingClipInfos) && correspondingClipInfos.size() >= 2) {
                            hasGroup = true;
                            groupIndex += 1;
                        }
                        for (NvsAssetPackageManager.NvsTemplateFootageCorrespondingClipInfo clipInfo : correspondingClipInfos) {
                            clipList.add(new TemplateClip()
                                    .setDuration(clipInfo.outpoint - clipInfo.inpoint)
                                    .setTrimDuration(clipInfo.trimOut - clipInfo.trimIn)
                                    .setInPoint(clipInfo.inpoint)
                                    .setNeedReverse(clipInfo.needReverse)
                                    .setTrackIndex(clipInfo.trackIndex)
                                    .setType(footage.type)
                                    .setHasGroup(hasGroup)
                                    .setGroupIndex(groupIndex)
                                    .setFootageId(footage.id));
                        }
                    }
                    if (CommonUtils.isEmpty(clipList)) {
                        if (callback != null) {
                            callback.onTimelineCreate(null);
                        }
                        return;
                    }
                    installAllInnerAssets(assetsPath);
                    if (callback != null) {
                        //mStreamingContext.setTemplateCustomResourceDir(packageId, PathUtils.getCloudDraftFootageFileFolder());*/
                        MeicamTimeline timeline = recoverFromCloudDraft(mStreamingContext.loadProject(packageId, PathUtils.getCloudDraftFootageFileFolder(), 0));
                        if (timeline != null) {
                            adjustTimeline(timeline);
                            EditorEngine.getInstance().setCurrentTimeline(timeline);
                            EditorEngine.getInstance().checkTrackDuration();
                            timeline.setLastModifiedTime(modifiedAt);
                            timeline.setProjectId(projectId);
                        }
                        callback.onTimelineCreate(timeline);
                    }
                } else {
                    if (error == ASSET_PACKAGE_MANAGER_ERROR_SDK_VERSION) {
                        ToastUtils.make().setGravity(Gravity.CENTER, 0, 0).show("版本错误");
                    }
                    if (callback != null) {
                        callback.onTimelineCreate(null);
                    }
                }
            }

            @Override
            public void onFinishAssetPackageUpgrading(String s, String s1, int i, int i1) {
                EngineCallbackManager.get().unregisterCallbackObserver(this);
            }

        };
        EngineCallbackManager.get().registerCallbackObserver(mCallBackObserver);
        assetPackageManager.installAssetPackage(templatePath, null, NvsAssetPackageManager.ASSET_PACKAGE_TYPE_PROJECT, false, packageId);
    }

    private void adjustTimeline(MeicamTimeline timeline) {
        int count = timeline.videoTrackCount();
        String fileFolder = PathUtils.getCloudDraftFootageFileFolder();
        if (count > 0) {
            for (int index = 0; index < count; index++) {
                MeicamVideoTrack videoTrack = timeline.getVideoTrack(index);
                int clipCount = videoTrack.getClipCount();
                if (clipCount > 0) {
                    for (int clipIndex = 0; clipIndex < clipCount; clipIndex++) {
                        MeicamVideoClip videoClip = videoTrack.getVideoClip(clipIndex);
                        MeicamVideoFx videoFx = videoClip.findPropertyVideoFx();
                        if (videoFx != null) {
                            String stringVal = videoFx.getStringVal(KEY_BACKGROUND_IMAGE_PATH);
                            if (!TextUtils.isEmpty(stringVal)) {
                                stringVal = fileFolder + File.separator + FileUtils.getFileName(stringVal);
                            }
                            videoFx.setStringVal(KEY_BACKGROUND_IMAGE_PATH, stringVal);
                        }
                        videoFx = videoClip.getVideoFx(SUB_TYPE_ALPHA, SET_ALPHA);
                        if (videoFx != null) {
                            String alphaPath = videoFx.getStringVal(ALPHA_FILE);
                            if (!TextUtils.isEmpty(alphaPath)) {
                                alphaPath = fileFolder
                                        + File.separator + new File(alphaPath).getName();
                                videoFx.setStringVal(ALPHA_FILE, alphaPath);
                            }
                        }
                    }
                }
            }
        }
        MeicamWaterMark waterMark = timeline.getMeicamWaterMark();
        if (waterMark != null) {
            String stringVal = waterMark.getWatermarkFilePath();
            if (!TextUtils.isEmpty(stringVal)) {
                stringVal = fileFolder
                        + File.separator + new File(stringVal).getName();
                timeline.addWatermark(stringVal, waterMark.getDisplayWidth(), waterMark.getDisplayHeight(),
                        waterMark.getMarginX(), waterMark.getMarginY());
            }
        }

        int trackCount = timeline.getStickerCaptionTrackCount();
        if (trackCount > 0) {
            for (int index = 0; index < trackCount; index++) {
                MeicamStickerCaptionTrack track = timeline.findStickCaptionTrack(index);
                if (track != null) {
                    int clipCount = track.getClipCount();
                    if (clipCount > 0) {
                        for (int clipIndex = 0; clipIndex < clipCount; clipIndex++) {
                            ClipInfo<?> captionStickerClip = track.getCaptionStickerClip(index);
                            if (captionStickerClip instanceof MeicamStickerClip) {
                                MeicamStickerClip clip = (MeicamStickerClip) captionStickerClip;
                                String path = clip.getCustomAnimatedStickerImagePath();
                                path = fileFolder
                                        + File.separator + new File(path).getName();
                                clip.setCustomAnimatedStickerImagePath(path);
                                clip.setCoverImagePath(path);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 检查轨道时长，用于主轨道补黑片段的删除、添加、更改。
     * Check the track duration, which is used to delete, add and change the main track black patch.
     */
    public void checkTrackDuration() {
        long otherTrackMaxDuration = getOtherTrackMaxDuration();
        long offsetDuration = otherTrackMaxDuration - getVideoTrackDuration(TRACK_INDEX_MAIN);
        LogUtils.d("offsetDuration=" + offsetDuration);
        MeicamVideoClip lastVideoClip = getLastVideoClip(MAIN_TRACK_INDEX);
        if (offsetDuration > 0) {
            if (lastVideoClip != null) {
                if (CommonData.CLIP_HOLDER.equals(lastVideoClip.getVideoType())) {
                    lastVideoClip.setTrimOut(lastVideoClip.getTrimOut() + offsetDuration, true);
                    lastVideoClip.updateInAndOutPoint();
                } else {
                    MeicamVideoTrack videoTrack = getVideoTrack(MAIN_TRACK_INDEX);
                    MeicamVideoClip videoClip = videoTrack.addVideoClip(CommonData.IMAGE_BLACK_HOLDER,
                            lastVideoClip.getIndex() + 1, 0, offsetDuration);
                    if (videoClip == null) {
                        LogUtils.e("补黑视频片段添加失败");
                        return;
                    }
                    videoClip.setVideoType(CommonData.CLIP_HOLDER);
                    videoClip.setOrgDuration(Long.MAX_VALUE);
                    videoClip.setOpacity(0);
                    LogUtils.d("add,inPoint=" + videoClip.getInPoint() + ",outPoint=" + videoClip.getOutPoint());
                }
            }
        } else {
            if (lastVideoClip != null) {
                if (CommonData.CLIP_HOLDER.equals(lastVideoClip.getVideoType())) {
                    //如果子轨的长度小于补黑的入点直接删除
                    if (otherTrackMaxDuration <= lastVideoClip.getInPoint()) {
                        MeicamVideoTrack videoTrack = getVideoTrack(MAIN_TRACK_INDEX);
                        videoTrack.removeVideoClip(videoTrack.getClipCount() - 1, false);
                    } else {
                        lastVideoClip.setTrimOut(lastVideoClip.getTrimOut() + offsetDuration, true);
                        lastVideoClip.updateInAndOutPoint();
                    }
                }
            }
        }
    }

    private void installAllInnerAssets(final List<String> assetsPath) {
        if (CommonUtils.isEmpty(assetsPath)) {
            return;
        }
        for (String path : assetsPath) {
            mStreamingContext.getAssetPackageManager().installAssetPackage(path, null, AssetsManager.getAssetType(path), true, new StringBuilder());
        }
    }

    public interface TimelineCreateCallback {
        void onTimelineCreate(MeicamTimeline timeline);
    }


    /**
     * 从底层恢复到上层数据
     * Recover from nvs timeline meicam timeline.
     *
     * @param nvsTimeline the nvs timeline
     * @return the meicam timeline
     */
    public MeicamTimeline recoverFromCloudDraft(NvsTimeline nvsTimeline) {
        return new MeicamTimeline.TimelineBuilder(getStreamingContext(), MeicamTimeline.TimelineBuilder.BUILD_FORM_NVSTIMELINE)
                .setNvsTimeline(nvsTimeline)
                .build();
    }

    /**
     * Create timeline nvs timeline.
     * <p>
     * 创建timeline
     *
     * @param mediaList the media list 选择的素材列表
     * @return the timeline
     */
    public MeicamTimeline createTimeline(ArrayList<MediaData> mediaList) {
        if (!CommonUtils.isEmpty(mediaList)) {
            String path = mediaList.get(0).getPath();
            NvsVideoResolution videoResolution = getVideoEditResolution(path);
            MeicamTimeline timeline = new MeicamTimeline.TimelineBuilder(getStreamingContext(),
                    MeicamTimeline.TimelineBuilder.BUILD_NORMAL)
                    .setVideoResolution(videoResolution)
                    .build();
            if (timeline == null) {
                return null;
            }
            timeline.setMakeRatio(AspectRatio_NoFitRatio);
            MeicamVideoTrack meicamVideoTrack = timeline.appendVideoTrack();
            if (meicamVideoTrack == null) {
                return null;
            }
            for (MediaData mediaData : mediaList) {
                MeicamVideoClip videoClip = meicamVideoTrack.appendVideoClip(mediaData.getPath());
                updateVideoClipInfo(videoClip);
            }
            return timeline;
        }
        return null;
    }

    /**
     * 更新视频片段的一些信息
     * Update some info of the view clip
     *
     * @param videoClip the meicam video clip
     */
    private void updateVideoClipInfo(MeicamVideoClip videoClip) {
        if (videoClip == null) {
            LogUtils.e("videoClip is null");
            return;
        }
        NvsAVFileInfo avFileInfo = mStreamingContext.getAVFileInfo(videoClip.getFilePath());
        long duration;
        if (avFileInfo != null) {
            duration = avFileInfo.getDuration();
            NvsSize nvsSize = avFileInfo.getVideoStreamDimension(0);
            int streamRotation = avFileInfo.getVideoStreamRotation(0);
            if (streamRotation == 1 || streamRotation == 3) {
                videoClip.setOriginalWidth(nvsSize.height);
                videoClip.setOriginalHeight(nvsSize.width);
            } else {
                videoClip.setOriginalWidth(nvsSize.width);
                videoClip.setOriginalHeight(nvsSize.height);
            }
        } else {
            duration = videoClip.getOutPoint() - videoClip.getInPoint();
        }
        if (CommonData.CLIP_VIDEO.equals(videoClip.getVideoType())) {
            videoClip.setOrgDuration(duration);
        } else {
            videoClip.setOrgDuration(Long.MAX_VALUE);
        }
    }

    /**
     * Create timeline nvs timeline.
     * <p>
     * 创建timeline 单独创建和视频轨道无关，用于预览自定义贴纸
     *
     * @param path the path 选择的素材
     * @return the timeline
     */
    public MeicamTimeline createSingleClipTimelineExt(String path) {
        return createSingleClipTimelineExt(path, 0, 8000000, 1D);
    }

    /**
     * Create timeline nvs timeline.
     * <p>
     * 创建timeline 单独创建和视频轨道无关，用于预览自定义贴纸
     *
     * @param path the path 选择的素材
     * @param path the trimIn 入点
     * @param path the trimOut 出点
     * @return the timeline
     */
    public MeicamTimeline createSingleClipTimelineExt(String path, long trimIn, long trimOut, double speed) {
        if (!TextUtils.isEmpty(path)) {
            NvsVideoResolution videoResolution = getVideoEditResolution(path);
            MeicamTimeline timeline = new MeicamTimeline.TimelineBuilder(getStreamingContext(),
                    MeicamTimeline.TimelineBuilder.BUILD_NORMAL)
                    .setVideoResolution(videoResolution)
                    .build();
            if (timeline == null) {
                return null;
            }
            timeline.setVideoResolution(videoResolution);
            timeline.setMakeRatio(AspectRatio_NoFitRatio);
            MeicamVideoTrack meicamVideoTrack = timeline.appendVideoTrack();
            if (meicamVideoTrack == null) {
                return null;
            }
            MeicamVideoClip videoClip = meicamVideoTrack.addVideoClip(path, 0, trimIn, trimOut);
            if (videoClip != null) {
                videoClip.setSpeed(speed);
            }
            return timeline;
        }
        return null;
    }

    /**
     * Create timeline nvs timeline.
     * <p>
     * 创建timeline 创建timeline
     *
     * @param videoResolution the videoResolution timeline分辨率
     * @return the timeline
     */
    public MeicamTimeline createSingleClipTimelineExt(NvsVideoResolution videoResolution) {
        if (videoResolution != null) {
            return new MeicamTimeline.TimelineBuilder(getStreamingContext(),
                    MeicamTimeline.TimelineBuilder.BUILD_NORMAL)
                    .setVideoResolution(videoResolution)
                    .build();
        }
        return null;
    }

    /**
     * Sets base ui clip.
     *
     * @param baseUIClip the base ui clip
     */
    public void setBaseUIClip(MeicamAudioClip baseUIClip) {
        if (baseUIClip == null || meicamTimeline == null) {
            return;
        }
        mMeicamAudioTrack = meicamTimeline.getAudioTrack(baseUIClip.getTrackIndex());
        if (mMeicamAudioTrack == null) {
            LogUtils.e("NvsAudioTrack is null");
            return;
        }
        mMeicamAudioClip = baseUIClip;
    }

    @Override
    public void setCurrentTimeline(MeicamTimeline timeline) {
        meicamTimeline = timeline;
    }

    @Override
    public MeicamTimeline getCurrentTimeline() {
        if (meicamTimeline == null) {
            NvsVideoResolution videoResolution = new NvsVideoResolution();
            videoResolution.imagePAR = new NvsRational(1, 1);
            videoResolution.imageWidth = 720;
            videoResolution.imageHeight = 1080;
            meicamTimeline = new MeicamTimeline.TimelineBuilder(getStreamingContext(),
                    MeicamTimeline.TimelineBuilder.BUILD_NORMAL)
                    .setVideoResolution(videoResolution)
                    .build();
        }
        return meicamTimeline;
    }

    @Override
    public MeicamVideoClip getEditVideoClip(long timeStamp, int trackIndex) {
        if (meicamTimeline == null) {
            return null;
        }
        MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(trackIndex);
        if (videoTrack != null) {
            return videoTrack.getClipByTimelinePosition(timeStamp);
        }
        return null;
    }

    @Override
    public long getCurrentTimelinePosition() {
        if (meicamTimeline != null) {
            return meicamTimeline.getCurrentPosition();
        }
        return 0;
    }

    @Override
    public void changeTimelineSpeed(float speed) {

    }

    @Override
    public void changeClipSpeed(float speed) {
        MeicamVideoClip clip = getEditVideoClip(getCurrentTimelinePosition(), TRACK_INDEX_MAIN);
        if (clip == null) {
            return;
        }
        clip.setSpeed(speed);
        clip.updateInAndOutPoint();
        seekTimeline(getCurrentTimelinePosition(), 0);
    }

    @Override
    public int cutClip(MeicamVideoClip videoClip, int trackIndex) {
        if (meicamTimeline == null || videoClip == null) {
            return ReturnCode.CODE_PARAM_ERROR;
        }

        long timeStamp = getCurrentTimelinePosition();
        long minDuration = getDurationByFrame(3);
        if (timeStamp <= videoClip.getInPoint() + minDuration
                || timeStamp >= videoClip.getOutPoint() - minDuration) {
            return ReturnCode.CODE_CAN_NOT_OPERATE;
        }
        MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(trackIndex);
        if (videoTrack != null) {
            MeicamVideoClip lastVideoClip = VideoTrackCommand.split(videoTrack, videoClip.getIndex(), timeStamp);
            if (lastVideoClip == null) {
                return ReturnCode.CODE_OTHER;
            }
        }
        if (trackIndex == MAIN_TRACK_INDEX) {
            TimelineCommand.spiltAICaption(getCurrentTimeline(), timeStamp);
            //spiltAICaption(timeStamp);
        }
        seekTimeline(timeStamp, 0);
        return ReturnCode.CODE_OK;
    }

    /**
     * 分割AI 字幕
     * split ai caption
     *
     * @param timeStamp the timestamp 时间戳
     */
    public void spiltAICaption(long timeStamp) {
        List<ClipInfo<?>> allAICaption = getAllAICaption();
        if (CommonUtils.isEmpty(allAICaption)) {
            return;
        }
        for (int i = 0; i < allAICaption.size(); i++) {
            ClipInfo<?> clipInfo = allAICaption.get(i);
            if (clipInfo == null) {
                continue;
            }
            if (isAICaption(clipInfo)) {
                long inPoint = clipInfo.getInPoint();
                long outPoint = clipInfo.getOutPoint();
                if (timeStamp >= inPoint && timeStamp <= outPoint) {
                    if (clipInfo instanceof MeicamCaptionClip) {
                        MeicamCaptionClip clip = (MeicamCaptionClip) clipInfo;
                        clip.setOutPoint(timeStamp);
                        addCaption(clip.getText(), timeStamp, outPoint, true, Constants.TYPE_AI_CAPTION, false);
                    }
                }
            }
        }
    }

    /**
     * 获取制作比例
     * Get ratio
     *
     * @return ratio 比例
     */
    public int getMakeRatio() {
        if (meicamTimeline != null) {
            return meicamTimeline.getMakeRatio();
        }
        return mMakeRatio;
    }

    /**
     * 获取视频分辨率
     * Get video resolution
     *
     * @return resolution 分辨率
     */
    public NvsVideoResolution getVideoResolution() {
        if (meicamTimeline != null) {
            return meicamTimeline.getVideoResolution();
        }
        return null;
    }

    /**
     * 获取视频比例
     * Get video resolution
     *
     * @return rational 比例
     */
    public NvsRational getVideoRational() {
        if (meicamTimeline != null) {
            return meicamTimeline.getNvsRational();
        }
        return null;
    }

    /**
     * 获取转场
     * Get transition
     *
     * @param trackIndex the track index 轨道索引
     * @param index      the transition index 转场索引
     * @return MeicamTransition 转场
     **/
    public MeicamTransition getTransition(int trackIndex, int index) {
        if (meicamTimeline != null) {
            MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(trackIndex);
            if (videoTrack != null) {
                return videoTrack.getTransition(index);
            }
        }
        return null;
    }

    /**
     * 构建转场
     * Build transition
     *
     * @param trackIndex   the track index 轨道索引
     * @param index        the transition index 转场索引
     * @param transitionId the transition id 转场标识id
     * @param buildType    the build type 转场构建类型
     * @return MeicamTransition 转场
     **/
    public MeicamTransition buildTransition(int trackIndex, int index, String transitionId, String buildType) {
        if (meicamTimeline != null) {
            MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(trackIndex);
            if (videoTrack != null) {
                return videoTrack.buildTransition(index, buildType, transitionId);
            }
        }
        return null;
    }

    /**
     * 把目标转场应用到视频轨道所有的转场
     * Apply the target transitions to all transitions in the video track
     *
     * @param trackIndex the track index 轨道索引
     * @param transition the transition 转场
     * @return Is apply successfully or not. true:yes; false:no
     **/
    public boolean applyTransitionToVideoTrack(MeicamTransition transition, int trackIndex) {
        if (meicamTimeline != null) {
            MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(trackIndex);
            if (videoTrack != null) {
                return VideoTrackCommand.applyTransitionToAll(videoTrack, transition);
            }
        }
        return false;
    }

    /**
     * 应用转场
     * Apply transition
     *
     * @param baseInfo        the base info 资源信息
     * @param transitionIndex the target transition index 转场索引
     */
    public void applyTransition(IBaseInfo baseInfo, int transitionIndex, long duration) {
        if (meicamTimeline == null) {
            return;
        }
        MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(MAIN_TRACK_INDEX);
        if (videoTrack != null) {
            String desc;
            MeicamTransition transition;
            if (baseInfo.getEffectMode() == BaseInfo.EFFECT_MODE_BUILTIN) {
                desc = baseInfo.getEffectId();
                if (TextUtils.isEmpty(desc)) {
                    VideoTrackCommand.removeTransition(videoTrack, transitionIndex);
                    return;
                }
                transition = VideoTrackCommand.buildTransition(videoTrack, transitionIndex, TYPE_BUILD_IN, desc);
                if (transition != null) {
                    TransitionCommand.setParam(transition, TransitionCommand.PARAM_ICON_RESOURCE_ID, baseInfo.getCoverId());
                }
            } else {
                desc = baseInfo.getPackageId();
                if (TextUtils.isEmpty(desc)) {
                    VideoTrackCommand.removeTransition(videoTrack, transitionIndex);
                }
                transition = VideoTrackCommand.buildTransition(videoTrack, transitionIndex, TYPE_PACKAGE, desc);
                if (transition != null) {
                    TransitionCommand.setParam(transition, TransitionCommand.PARAM_ICON_PATH, baseInfo.getCoverPath());
                }
            }
            if (transition != null) {
                TransitionCommand.setParam(transition, TransitionCommand.PARAM_DURATION, duration);
            }
            playTransition(videoTrack, transitionIndex);
        }
    }

    /**
     * 播放转场效果
     * Play transition
     *
     * @param trackIndex the track index 轨道索引
     * @param clipIndex  the transition index 转场索引
     */
    public void playTransition(int trackIndex, int clipIndex) {
        if (meicamTimeline == null) {
            return;
        }
        MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(trackIndex);
        if (videoTrack != null) {
            int videoCount = videoTrack.getClipCount();
            if (videoCount < clipIndex + 1) {
                return;
            }
            playTransition(videoTrack, clipIndex);
        }
    }

    /**
     * 播放转场效果
     * Play transition
     *
     * @param videoTrack the video track 轨道
     * @param clipIndex  the transition index 转场索引
     */
    private void playTransition(MeicamVideoTrack videoTrack, int clipIndex) {
        MeicamVideoClip clip = videoTrack.getVideoClip(clipIndex);
        if (clip == null) {
            return;
        }
        MeicamTransition transition = videoTrack.getTransition(clipIndex);
        if (transition == null) {
            LogUtils.e("transition is null!");
            return;
        }

        long transitionDuration = transition.getDuration();
        long startPoint = (long) (clip.getOutPoint() - transitionDuration / 2F);
        if (startPoint < 0) {
            return;
        }
        long endPoint = startPoint + transitionDuration;
        long timelineDuration = meicamTimeline.getDuration();
        if (endPoint > timelineDuration) {
            endPoint = timelineDuration;
        }
        playVideo(startPoint, endPoint);
    }

    /**
     * 获取音频轨道的数量
     * Get meicam video track count
     *
     * @return video track count
     */
    public int getAudioTrackCount() {
        return meicamTimeline == null ? 0 : meicamTimeline.getAudioTrackCount();
    }

    /**
     * 获取音频轨道
     * Get meicam audio track
     *
     * @return audio track
     */
    public MeicamAudioTrack getAudioTrack(int trackIndex) {
        if (meicamTimeline != null) {
            return meicamTimeline.getAudioTrack(trackIndex);
        }
        return null;
    }

    /**
     * 获取音频片段
     * Get meicam audio clip
     *
     * @param trackIndex the track index 轨道索引
     * @param clipIndex  the clip index 片段索引
     * @return audio clip
     */
    public MeicamAudioClip getAudioClip(int trackIndex, int clipIndex) {
        if (meicamTimeline != null) {
            MeicamAudioTrack audioTrack = getAudioTrack(trackIndex);
            if (audioTrack != null) {
                return audioTrack.getAudioClip(clipIndex);
            }
        }
        return null;
    }

    /**
     * 获取音频片段
     * Get video clip
     *
     * @param trackIndex the track index 轨道索引
     * @param inPoint    the clip in point 片段入点
     * @return MeicamVideoClip 视频片段
     */
    public MeicamAudioClip getAudioClip(int trackIndex, long inPoint) {
        if (meicamTimeline != null) {
            MeicamAudioTrack audioTrack = meicamTimeline.getAudioTrack(trackIndex);
            if (audioTrack != null) {
                return audioTrack.getAudioClip(inPoint);
            }
        }
        return null;
    }

    /**
     * 获取视频轨道的数量
     * Get meicam video track count
     *
     * @return video track count
     */
    public int getVideoTrackCount() {
        return meicamTimeline == null ? 0 : meicamTimeline.videoTrackCount();
    }

    /**
     * 添加视频片段
     * Add video clip.
     *
     * @param trackIndex the track index 轨道索引
     * @param index      the add index 添加的索引
     * @param mediaList  the media list 媒体列表
     */
    public List<MeicamVideoClip> addVideoClip(int trackIndex, int index, List<MediaData> mediaList) {
        if (index < 0 || mediaList == null || mediaList.size() <= 0) {
            return null;
        }
        MeicamVideoTrack videoTrack = getVideoTrack(trackIndex);
        if (videoTrack == null) {
            LogUtils.e("add video clip failed!!!");
            return null;
        }
        List<MeicamVideoClip> addList = new ArrayList<>(mediaList.size());
        long beforeDuration = meicamTimeline.getDuration();
        MeicamVideoClip videoClip = videoTrack.getVideoClip(index);
        long startTime = 0;
        if (trackIndex == MAIN_TRACK_INDEX) {
            if (videoClip != null) {
                startTime = videoClip.getInPoint();
            } else {
                startTime = beforeDuration;
            }
        }
        int tempIndex = 0;
        for (MediaData item : mediaList) {
            videoClip = addVideoClip(videoTrack, index + tempIndex, item);
            if (videoClip != null) {
                ++tempIndex;
                addList.add(videoClip);
            }
        }
        if (trackIndex == MAIN_TRACK_INDEX) {
            timelineAddOrSubtract(startTime, meicamTimeline.getDuration() - beforeDuration);
        }
        return addList;
    }

    /**
     * 添加视频片段
     * Add video clip.
     *
     * @param trackIndex the track index 轨道索引
     * @param index      the add index 添加的索引
     * @param mediaData  the media data 媒体数据
     */
    public MeicamVideoClip addVideoClip(int trackIndex, int index, MediaData mediaData) {
        if (index < 0 || mediaData == null) {
            return null;
        }
        MeicamVideoTrack videoTrack = getVideoTrack(trackIndex);
        return addVideoClip(videoTrack, index, mediaData);
    }

    /**
     * 添加视频片段
     * Add video clip.
     *
     * @param videoTrack the video track  视频轨道
     * @param index      the add index 添加的索引
     * @param mediaData  the media data 媒体数据
     */
    public MeicamVideoClip addVideoClip(MeicamVideoTrack videoTrack, int index, MediaData mediaData) {
        if (index < 0 || mediaData == null || videoTrack == null) {
            return null;
        }
        long duration = mediaData.getDuration() * 1000;
        NvsAVFileInfo avFileInfo = mStreamingContext.getAVFileInfo(mediaData.getPath());
        long trimIn = 0;
        if (mediaData.getType() == MediaData.TYPE_VIDEO) {
            if (avFileInfo != null) {
                duration = avFileInfo.getDuration();
            }
        } else {
            duration = CommonData.DEFAULT_LENGTH;
        }
        long trimOut = trimIn + duration;
        MeicamVideoClip videoClip = VideoTrackCommand.addVideoClip(videoTrack, mediaData.getPath(), index, trimIn, trimOut);
        //MeicamVideoClip videoClip = videoTrack.addVideoClip(mediaData.getPath(), index, trimIn, trimOut);
        if (videoClip != null) {
            if (CommonData.CLIP_VIDEO.equals(videoClip.getVideoType())) {
                videoClip.setOrgDuration(videoClip.getOutPoint() - videoClip.getInPoint());
            } else {
                /*图片的原始时长为最大值，可以“无限”拖长*/
                videoClip.setOrgDuration(Long.MAX_VALUE);
            }
            if (avFileInfo != null) {
                NvsSize nvsSize = avFileInfo.getVideoStreamDimension(0);
                int streamRotation = avFileInfo.getVideoStreamRotation(0);
                if (streamRotation == 1 || streamRotation == 3) {
                    videoClip.setOriginalWidth(nvsSize.height);
                    videoClip.setOriginalHeight(nvsSize.width);
                } else {
                    videoClip.setOriginalWidth(nvsSize.width);
                    videoClip.setOriginalHeight(nvsSize.height);
                }
            }
        }
        return videoClip;
    }

    /**
     * 添加视频片段,注意本方法会检查嵌入索引，如果视频轨道没有足够的空白区域放入该片段则会添加失败
     * Add video clip.
     *
     * @param videoTrack the video track 视频轨道
     * @param inPoint    the in point 入点
     * @param mediaData  the media  媒体
     */
    public MeicamVideoClip addVideoClip(MeicamVideoTrack videoTrack, long inPoint, MediaData mediaData) {
        if (mediaData == null || videoTrack == null || TextUtils.isEmpty(mediaData.getPath())) {
            return null;
        }
        long duration = mediaData.getDuration() * 1000;
        NvsAVFileInfo avFileInfo = mStreamingContext.getAVFileInfo(mediaData.getPath());
        long trimIn = 0;
        if (mediaData.getType() == MediaData.TYPE_VIDEO) {
            if (avFileInfo != null) {
                duration = avFileInfo.getDuration();
            }
        } else {
            duration = CommonData.DEFAULT_LENGTH;
        }
        int embedIndex = findSuitableEmbedIndex(videoTrack, inPoint, inPoint + duration);
        if (embedIndex >= 0) {
            long trimOut = trimIn + duration;
            MeicamVideoClip videoClip = VideoTrackCommand.addVideoClip(videoTrack, mediaData.getPath(), inPoint, trimIn, trimOut);
            if (videoClip != null) {
                if (CommonData.CLIP_VIDEO.equals(videoClip.getVideoType())) {
                    VideoClipCommand.setParam(videoClip, VideoClipCommand.PARAM_ORG_DURATION,
                            videoClip.getOutPoint() - videoClip.getInPoint());
                } else {
                    /*图片的原始时长为最大值，可以“无限”拖长*/
                    VideoClipCommand.setParam(videoClip, VideoClipCommand.PARAM_ORG_DURATION,
                            Long.MAX_VALUE);
                }
                if (avFileInfo != null) {
                    NvsSize nvsSize = avFileInfo.getVideoStreamDimension(0);
                    int streamRotation = avFileInfo.getVideoStreamRotation(0);
                    if (streamRotation == 1 || streamRotation == 3) {
                        VideoClipCommand.setParam(videoClip, VideoClipCommand.PARAM_ORG_WIDTH, nvsSize.height);
                        VideoClipCommand.setParam(videoClip, VideoClipCommand.PARAM_ORG_HEIGHT, nvsSize.width);
                    } else {
                        VideoClipCommand.setParam(videoClip, VideoClipCommand.PARAM_ORG_WIDTH, nvsSize.width);
                        VideoClipCommand.setParam(videoClip, VideoClipCommand.PARAM_ORG_HEIGHT, nvsSize.height);
                    }
                }
            }
            return videoClip;
        }
        LogUtils.e("add video clip failed,not find embed index!!!");
        return null;
    }


    /**
     * 获取视频片段
     * Get video clip
     *
     * @param trackIndex the track index 轨道索引
     * @param clipIndex  the clip index 片段索引
     * @return MeicamVideoClip 视频片段
     */
    public MeicamVideoClip getVideoClip(int trackIndex, int clipIndex) {
        if (meicamTimeline != null) {
            MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(trackIndex);
            if (videoTrack != null) {
                return videoTrack.getVideoClip(clipIndex);
            }
        }
        return null;
    }

    /**
     * 获取视频片段
     * Get video clip
     *
     * @param trackIndex the track index 轨道索引
     * @param inPoint    the clip in point 片段入点
     * @return MeicamVideoClip 视频片段
     */
    public MeicamVideoClip getVideoClip(int trackIndex, long inPoint) {
        if (meicamTimeline != null) {
            MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(trackIndex);
            if (videoTrack != null) {
                return videoTrack.getVideoClip(inPoint);
            }
        }
        return null;
    }

    /**
     * 获取指定轨道的最后一个片段
     * Get the last video clip of the target track
     *
     * @param trackIndex the track index 轨道索引
     * @return MeicamVideoClip 视频片段
     */
    public MeicamVideoClip getLastVideoClip(int trackIndex) {
        if (meicamTimeline != null) {
            MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(trackIndex);
            if (videoTrack != null) {
                return videoTrack.getVideoClip(videoTrack.getClipCount() - 1);
            }
        }
        return null;
    }

    /**
     * 获取包含区间的视频片段
     * get container area video clip
     *
     * @param inPoint  the in point 入点
     * @param outPoint the out point 出点
     * @return MeicamVideoClip视频片段
     */
    public MeicamVideoClip getVideoClipInRange(int trackIndex, long inPoint, long outPoint) {
        MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(trackIndex);
        if (videoTrack != null) {
            for (int i = 0; i < videoTrack.getClipCount(); i++) {
                MeicamVideoClip videoClip = videoTrack.getVideoClip(i);
                if (videoClip != null) {
                    if ((inPoint >= videoClip.getInPoint()) && (outPoint <= videoClip.getOutPoint())) {
                        return videoClip;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 添加视频轨道
     * Add video track
     *
     * @return MeicamVideoTrack 视频轨道
     */
    public MeicamVideoTrack appendVideoTrack() {
        if (meicamTimeline != null) {
            return TimelineCommand.appendVideoTrack(meicamTimeline);
        }
        return null;
    }

    /**
     * 获取视频轨道
     * Get video track
     *
     * @param trackIndex the track index 轨道索引
     * @return MeicamVideoTrack 视频轨道
     */
    public MeicamVideoTrack getVideoTrack(int trackIndex) {
        if (meicamTimeline != null) {
            return meicamTimeline.getVideoTrack(trackIndex);
        }
        return null;
    }

    /**
     * 定格
     * Freeze frame clip.
     *
     * @param meicamVideoClip the  video clip 视频片段
     * @param trackIndex      the track index 轨道索引
     * @param isNeedSplit     need split or not 是否需要分割
     */
    public int freezeFrameClip(MeicamVideoClip meicamVideoClip, int trackIndex, boolean isNeedSplit) {
        if (meicamVideoClip == null) {
            return ReturnCode.CODE_OTHER;
        }
        long timeStamp = getCurrentTimelinePosition();
        /*获取当前一帧图片Gets the current frame image*/
        String path = meicamVideoClip.getVideoReverse() ? meicamVideoClip.getReverseFilePath() : meicamVideoClip.getFilePath();
        long point = (long) ((timeStamp - meicamVideoClip.getInPoint() + meicamVideoClip.getTrimIn() / meicamVideoClip.getSpeed()) * meicamVideoClip.getSpeed());
        Bitmap frameAtTime = getFrameAtTime(path, meicamVideoClip.getOriginalWidth(), meicamVideoClip.getOriginalHeight(), point);
        if (frameAtTime == null) {
            return ReturnCode.CODE_OTHER;
        }
        String fileName = System.currentTimeMillis() + ".png";
        String freezeFilePath = PathUtils.getVideoFreezeConvertDir() + File.separator + fileName;
        ImageUtils.save(frameAtTime, freezeFilePath, Bitmap.CompressFormat.PNG);
        MeicamVideoFx videoFx = meicamVideoClip.getVideoFx(SUB_TYPE_ALPHA, SET_ALPHA);
        String smartKeyerFilePath = PathUtils.getSmartKeyerFilePath(freezeFilePath, false, false);
        if (videoFx != null) {
            /*存在智能抠像，则以同样的方式获取一张alpha文件的帧图*/
            frameAtTime = getFrameAtTime(videoFx.getStringVal(ALPHA_FILE), meicamVideoClip.getOriginalWidth(), meicamVideoClip.getOriginalHeight(), point);
            if (!ImageUtils.save(frameAtTime, smartKeyerFilePath, Bitmap.CompressFormat.PNG)) {
                smartKeyerFilePath = "";
            }
        }
        long duration = CommonData.DEFAULT_LENGTH;
        MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(trackIndex);
        if (videoTrack == null) {
            return ReturnCode.CODE_OTHER;
        }
        //裁剪前，获取关键帧参数
        //Obtain key frame parameters before clipping
        Map<String, List<MeicamFxParam<?>>> keyParamParamAtSplitPoint = getAllKeyParamParamAtSplitPoint(meicamVideoClip, timeStamp - meicamVideoClip.getInPoint());
        MeicamVideoClip insertVideoClip;
        MeicamVideoClip lastVideoClip;
        long beforeDuration = meicamTimeline.getDuration();
        MeicamVideoClip insertClip = (MeicamVideoClip) meicamVideoClip.clone();
        insertClip.setFilePath(freezeFilePath);
        insertClip.setThumbNailInfo(null);
        insertClip.setVideoReverse(false);
        insertClip.setTrimIn(0, true);
        insertClip.setTrimOut(duration, true);
        insertClip.setSpeed(1);
        insertClip.setCurveSpeed("");
        insertClip.setCurveSpeedName("");
        insertClip.setVideoType(CommonData.CLIP_IMAGE);
        if (isNeedSplit) {
            lastVideoClip = VideoTrackCommand.split(videoTrack, meicamVideoClip.getIndex(), timeStamp);
            if (lastVideoClip == null) {
                LogUtils.e("split error");
                return ReturnCode.CODE_OTHER;
            }
            insertVideoClip = VideoTrackCommand.insertVideoClip(videoTrack, insertClip,
                    lastVideoClip.getIndex(), insertClip.getTrimIn(), insertClip.getTrimOut());
        } else {
            insertVideoClip = VideoTrackCommand.insertVideoClip(videoTrack, insertClip,
                    meicamVideoClip.getIndex() + 1, insertClip.getTrimIn(), insertClip.getTrimOut());
        }
        if (insertVideoClip == null) {
            LogUtils.e("freezeFrameClip newNvsVideoClip==null");
            return ReturnCode.CODE_OTHER;
        }
        if (trackIndex == MAIN_TRACK_INDEX) {
            timelineAddOrSubtract(meicamTimeline.getCurrentPosition(), meicamTimeline.getDuration() - beforeDuration);
        }
        if (!TextUtils.isEmpty(smartKeyerFilePath) && new File(smartKeyerFilePath).exists()) {
            /*如果有智能抠像，则插入的片段也需要添加*/
            insertVideoClip.removeVideoFx(NvsConstants.TYPE_RAW_BUILTIN, SUB_TYPE_ALPHA);
            MeicamVideoFx alphaFx = insertVideoClip.appendVideoFx(NvsConstants.TYPE_RAW_BUILTIN, SUB_TYPE_ALPHA, SET_ALPHA);
            if (alphaFx != null) {
                alphaFx.setStringVal(ALPHA_FILE, smartKeyerFilePath);
                alphaFx.setBooleanVal(ALPHA_CLIP_TRIM_USED, true);
            }
        }
        //处理所有关键帧参数
        //Process all keyframe parameters.
        handleAllKeyFrameForFreezeFrame(insertVideoClip, keyParamParamAtSplitPoint);
        updateVideoClipInfo(insertVideoClip);
        seekTimeline(timeStamp + 1, 0);
        return ReturnCode.CODE_OK;
    }

    private void handleAllKeyFrameForFreezeFrame(MeicamVideoClip videoClip, Map<String, List<MeicamFxParam<?>>> keyFrameParams) {
        if (CommonUtils.isEmpty(keyFrameParams)) {
            return;
        }
        int videoFxCount = videoClip.getVideoFxCount();
        for (int index = 0; index < videoFxCount; index++) {
            MeicamVideoFx videoFx = videoClip.getVideoFx(index);
            if (TYPE_PACKAGE.equals(videoFx.getType())) {
                continue;
            }
            String desc = videoFx.getDesc();
            List<MeicamFxParam<?>> meicamFxParams = keyFrameParams.get(desc);
            if (meicamFxParams == null) {
                continue;
            }
            handleKeyFrame(videoFx, meicamFxParams);
        }
    }

    private void handleKeyFrame(MeicamVideoFx videoFx, List<MeicamFxParam<?>> keyFrameParams) {
        KeyFrameProcessor<NvsVideoFx> keyFrameProcessor = videoFx.keyFrameProcessor();
        //删除所有关键帧，这个定帧是没有关键帧的
        keyFrameProcessor.removeAllKeyFrame();

        //给定帧片段设置参数
        for (MeicamFxParam<?> item : keyFrameParams) {
            String type = item.getType();
            String itemKey = item.getKey();
            Object itemValue = item.getValue();
            if (MeicamFxParam.TYPE_OBJECT.equals(type)) {
                if (itemValue instanceof MeicamMaskRegionInfo) {
                    videoFx.setObjectVal(itemKey, (MeicamMaskRegionInfo) itemValue);
                }
            } else if (TYPE_BOOLEAN.equals(type)) {
                if (itemValue instanceof Boolean) {
                    videoFx.setBooleanVal(itemKey, (Boolean) itemValue);
                }
            } else if (TYPE_FLOAT.equals(type)) {
                if (itemValue instanceof Float) {
                    videoFx.setFloatVal(itemKey, (Float) itemValue);
                }
            }
        }
    }

    /**
     * Gets all key param param at split point.
     * 获取所有特效的分割点处的参数，如果没有关键帧，则返回null
     *
     * @param videoClip the video clip the video clip
     * @param atTime    the at time
     * @return the all key param param at split point
     */
    public Map<String, List<MeicamFxParam<?>>> getAllKeyParamParamAtSplitPoint(MeicamVideoClip videoClip, long atTime) {
        int videoFxCount = videoClip.getVideoFxCount();
        Map<String, List<MeicamFxParam<?>>> result = new HashMap<>();
        for (int index = 0; index < videoFxCount; index++) {
            MeicamVideoFx videoFx = videoClip.getVideoFx(index);
            if (TYPE_PACKAGE.equals(videoFx.getType())) {
                continue;
            }
            List<MeicamFxParam<?>> middleParamBetweenKeyFrames = getKeyParamParamAtSplitPoint(videoFx, null, atTime);
            if (middleParamBetweenKeyFrames != null) {
                result.put(videoFx.getDesc(), middleParamBetweenKeyFrames);
            }
        }
        return result;
    }

    /**
     * Gets key param param at split point.
     * 获取分割点处的参数，如果没有关键帧，则返回null
     *
     * @param videoFx the video fx 特效
     * @param key     the key 关键帧的关键字key
     * @param atTime  the at time 关键帧点
     * @return the key param param at split point 关键帧参数
     */
    private List<MeicamFxParam<?>> getKeyParamParamAtSplitPoint(MeicamVideoFx videoFx, String key, long atTime) {
        KeyFrameProcessor<NvsVideoFx> keyFrameHolder = videoFx.keyFrameProcessor();
        MeicamKeyFrame keyFrame = keyFrameHolder.getKeyFrame(atTime);
        MeicamKeyFrame before = keyFrameHolder.findKeyFrame(key, atTime, true);
        if (keyFrame != null || before != null) {
            return keyFrameHolder.getKeyFrameParams(key, atTime);
        }
        return null;
    }

    /**
     * 从所给文件中获取所给时间点的bitmap
     * Get frame at time
     *
     * @param filePath the file path 文件路径
     * @param trimIn   the trim in point 裁入点
     */
    public Bitmap getFrameAtTime(String filePath, int width, int height, long trimIn) {
        NvsVideoResolution videoResolution = new NvsVideoResolution();
        videoResolution.imageWidth = width;
        videoResolution.imageHeight = height;
        videoResolution.imagePAR = new NvsRational(1, 1);
        MeicamTimeline timeline = new MeicamTimeline.TimelineBuilder(getStreamingContext(),
                MeicamTimeline.TimelineBuilder.BUILD_NORMAL)
                .setVideoResolution(videoResolution)
                .build();
        if (timeline == null) {
            return null;
        }
        MeicamVideoTrack meicamVideoTrack = timeline.appendVideoTrack();
        if (meicamVideoTrack == null) {
            return null;
        }
        MeicamVideoClip videoClip = meicamVideoTrack.appendVideoClip(filePath);
        if (videoClip == null) {
            return null;
        }
        return timeline.grabImageFromTimeline(mStreamingContext, trimIn, new NvsRational(1, 1));
    }

    @Override
    public void changeVolume(float volume) {
    }


    /**
     * 复制视频片段
     * Copy video clip.
     *
     * @param clipInfo   video clip that have been copied 被复制的视频片段
     * @param trackIndex the track index 轨道索引
     */
    public MeicamVideoClip copyVideoClip(MeicamVideoClip clipInfo, int trackIndex) {
        if (meicamTimeline == null || clipInfo == null || trackIndex < 0) {
            return null;
        }
        MeicamVideoClip copyVideoClip = (MeicamVideoClip) clipInfo.clone();
        MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(trackIndex);
        if (trackIndex > 0) {
            copyVideoClip = (MeicamVideoClip) clipInfo.clone();
            if (videoTrack == null) {
                videoTrack = TimelineCommand.appendVideoTrack(meicamTimeline);
            }
            if (videoTrack != null) {
                copyVideoClip = VideoTrackCommand.addVideoClip(videoTrack, copyVideoClip, clipInfo.getOutPoint(),
                        copyVideoClip.getTrimIn(), copyVideoClip.getTrimOut());
                TimelineFxBinder.unbindTimelineVideoFx(copyVideoClip);
                seekTimeline(0);
            }
            return copyVideoClip;
        } else {
            if (videoTrack != null) {
                copyVideoClip = VideoTrackCommand.insertVideoClip(videoTrack, copyVideoClip, clipInfo.getIndex() + 1,
                        copyVideoClip.getTrimIn(), copyVideoClip.getTrimOut());
                if (copyVideoClip != null) {
                    timelineAddOrSubtract(clipInfo.getOutPoint(),
                            (copyVideoClip.getOutPoint() - copyVideoClip.getInPoint()));
                    TimelineFxBinder.unbindTimelineVideoFx(copyVideoClip);
                }
                return copyVideoClip;
            }
        }
        return null;
    }

    /**
     * 获取复制的index
     * get Copy Pip Track Index
     * 1.选中clip后面有其他clip。空间足够：直接复制，入点为选中的出点。
     * 空间不够：判断下面是否有轨道，如果有，判断空间是否足够，足够放在下面轨道，不够，新建轨道.
     * 轨道达到最大则返回最大轨道数。入点为选中的出点
     * <p>
     * 2.选中clip后面无其他clip。优先复制该轨道及其上面轨道最短的轨道，依次补齐复制，入点为选中的出点，一直可以无限延伸
     * <p>
     * 1. Select the clip followed by other clips.Adequate space: direct copy, entry point is selected exit point.Not enough space:
     * judge whether there are tracks below, if there is, judge whether there is enough space, enough to put the track below,
     * not enough, create a new track, the track reaches the maximum, return the maximum number of tracks.The entry point is the selected exit point
     * <p>
     * 2. There are no other clips after the selected clip.The orbit and the shortest orbit above it are copied first,
     * and then copied in turn. The entry point is the selected exit point, and it can be extended indefinitely
     *
     * @param selectedClip 选中的videoclip
     * @return trackIndex 复制的trackIndex
     */
    public int getCopyPipTrackIndex(MeicamVideoClip selectedClip) {
        if (meicamTimeline == null || selectedClip == null) {
            return -1;
        }
        long insertPoint = selectedClip.getOutPoint();
        long duration = selectedClip.getOutPoint() - selectedClip.getInPoint();

        MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(selectedClip.getTrackIndex());
        if (videoTrack == null) {
            return -1;
        }
        MeicamVideoClip afterVideoClip = videoTrack.getVideoClip(selectedClip.getIndex() + 1);
        /*
         * 选中clip后面有其他clip
         * Check the clip followed by other clips
         */
        if (afterVideoClip != null) {
            /*
              空间足够：直接复制，入点为选中的出点
             Adequate space: direct copy, entry point is selected exit point
             */
            if (afterVideoClip.getInPoint() - selectedClip.getOutPoint() >= duration) {
                return selectedClip.getTrackIndex();
            } else {
                /*
                 * 空间不够
                 * Space is not enough
                 */
                for (int i = selectedClip.getTrackIndex() + 1; i < meicamTimeline.videoTrackCount(); i++) {
                    MeicamVideoTrack afterVideoTrack = meicamTimeline.getVideoTrack(i);
                    /*
                     * 下面有轨道
                     * You have track down here
                     */
                    if (afterVideoTrack != null) {
                        /*
                         * 当前选中的片段后面，下一个轨道已经没有片段了、
                         * After the currently selected clip, the next orbital has no clip left
                         */
                        MeicamVideoClip lastNvsClip = afterVideoTrack.getVideoClip(afterVideoTrack.getClipCount() - 1);
                        if (lastNvsClip == null || lastNvsClip.getOutPoint() <= insertPoint) {
                            return afterVideoTrack.getIndex();
                        }
                        MeicamVideoClip nvsVideoClipPosition = afterVideoTrack.getClipByTimelinePosition(insertPoint);
                        /*
                         * 下面轨道选中的入点没有其他片段
                         * The entry point selected for the lower orbitals has no other pieces
                         */
                        if (nvsVideoClipPosition == null) {
                            for (int j = 0; j < afterVideoTrack.getClipCount(); j++) {
                                MeicamVideoClip meicamVideoClip = afterVideoTrack.getVideoClip(j);
                                if (insertPoint < meicamVideoClip.getInPoint()) {
                                    /*
                                     * 当前时间节点和下一个节点之间空间是否足够
                                     * Is there enough space between the current time node and the next node
                                     */
                                    if (insertPoint + duration <= meicamVideoClip.getInPoint()) {
                                        return afterVideoTrack.getIndex();
                                    }
                                }
                            }
                        }
                    } else {
                        /*
                         * 下面无轨道
                         * There are no track below
                         */
                        return meicamTimeline.videoTrackCount();
                    }
                }
            }
        } else {
            /*
             * 选中clip后面无其他clip,优先复制该轨道及其上面轨道最短的轨道，依次补齐复制，入点为选中的出点，一直可以无限延伸
             * If there is no other clip after the clip is selected, the track and the track with the shortest track above it are copied first,
             * and the entry point is the selected exit point, which can be extended indefinitely
             */

            MeicamVideoTrack minTrack = meicamTimeline.getVideoTrack(1);
            if (minTrack != null) {
                for (int i = 1; i <= selectedClip.getTrackIndex(); i++) {
                    MeicamVideoTrack meicamVideoTrack = meicamTimeline.getVideoTrack(i);
                    if (meicamVideoTrack == null) {
                        return minTrack.getIndex();
                    }
                    if (meicamVideoTrack.getDuration() < minTrack.getDuration()) {
                        minTrack = meicamVideoTrack;
                    }
                }
                return minTrack.getIndex();
            }
        }
        return meicamTimeline.videoTrackCount();
    }


    /**
     * 获取特效copy的轨道Index
     * Gets track index by timeline effect copy.
     *
     * @param selectedClip the selected clip
     * @return the track index by timeline effect copy
     */
    private int getTrackIndexByTimelineEffectCopy(MeicamTimelineVideoFxClip selectedClip) {
        if (meicamTimeline == null || selectedClip == null) {
            return -1;
        }
        long insertPoint = selectedClip.getOutPoint();
        long duration = selectedClip.getOutPoint() - selectedClip.getInPoint();
        MeicamTimelineVideoFxTrack videoTrack = meicamTimeline.getTimelineFxTrack(selectedClip.getTrackIndex());
        if (videoTrack == null) {
            return -1;
        }
        MeicamTimelineVideoFxClip afterVideoClip = videoTrack.getClip(selectedClip.getIndex() + 1);
        /*
         * 选中clip后面有其他clip
         * Check the clip followed by other clips
         */
        if (afterVideoClip != null) {
            /*
              空间足够：直接复制，入点为选中的出点
             Adequate space: direct copy, entry point is selected exit point
             */
            if (afterVideoClip.getInPoint() - selectedClip.getOutPoint() >= duration) {
                return selectedClip.getTrackIndex();
            } else {
                /*
                 * 空间不够
                 * Space is not enough
                 */
                for (int i = selectedClip.getTrackIndex() + 1; i < meicamTimeline.getTimelineFxTrackCount(); i++) {
                    MeicamTimelineVideoFxTrack afterVideoTrack = meicamTimeline.getTimelineFxTrack(i);
                    /*
                     * 下面有轨道
                     * You have track down here
                     */
                    if (afterVideoTrack != null) {
                        /*
                         * 当前选中的片段后面，下一个轨道已经没有片段了、
                         * After the currently selected clip, the next orbital has no clip left
                         */
                        MeicamTimelineVideoFxClip lastNvsClip = afterVideoTrack.getClip(afterVideoTrack.getClipCount() - 1);
                        if (lastNvsClip == null || lastNvsClip.getOutPoint() <= insertPoint) {
                            return afterVideoTrack.getIndex();
                        }
                        MeicamTimelineVideoFxClip nvsVideoClipPosition = afterVideoTrack.getClipByTimelinePosition(insertPoint);
                        /*
                         * 下面轨道选中的入点没有其他片段
                         * The entry point selected for the lower orbitals has no other pieces
                         */
                        if (nvsVideoClipPosition == null) {
                            for (int j = 0; j < afterVideoTrack.getFilterAndAdjustCount(); j++) {
                                MeicamTimelineVideoFxClip meicamVideoClip = afterVideoTrack.getClip(j);
                                if (insertPoint < meicamVideoClip.getInPoint()) {
                                    /*
                                     * 当前时间节点和下一个节点之间空间是否足够
                                     * Is there enough space between the current time node and the next node
                                     */
                                    if (insertPoint + duration <= meicamVideoClip.getInPoint()) {
                                        return afterVideoTrack.getIndex();
                                    }
                                }
                            }
                        }
                    } else {
                        /*
                         * 下面无轨道
                         * There are no track below
                         */
                        return meicamTimeline.getTimelineFxTrackCount();
                    }
                }
            }
        } else {
            /*
             * 选中clip后面无其他clip,优先复制该轨道及其上面轨道最短的轨道，依次补齐复制，入点为选中的出点，一直可以无限延伸
             * If there is no other clip after the clip is selected, the track and the track with the shortest track above it are copied first,
             * and the entry point is the selected exit point, which can be extended indefinitely
             */

            MeicamTimelineVideoFxTrack minTrack = meicamTimeline.getTimelineFxTrack(0);
            if (minTrack != null) {
                for (int i = 1; i <= selectedClip.getTrackIndex(); i++) {
                    MeicamTimelineVideoFxTrack meicamVideoTrack = meicamTimeline.getTimelineFxTrack(i);
                    if (meicamVideoTrack == null) {
                        return minTrack.getIndex();
                    }
                    if (meicamVideoTrack.getClipDuration() < minTrack.getClipDuration()) {
                        minTrack = meicamVideoTrack;
                    }
                }
                return minTrack.getIndex();
            }
        }
        return meicamTimeline.getTimelineFxTrackCount();
    }

    /**
     * 更改镜像
     * Change mirror.
     *
     * @param videoClip the meicam video clip 视频片段
     */
    public void changeMirror(MeicamVideoClip videoClip) {
        if (meicamTimeline == null || videoClip == null) {
            return;
        }
        MeicamVideoFx propertyVideoFx = videoClip.findPropertyVideoFx();
        if (propertyVideoFx == null) {
            return;
        }
        KeyFrameProcessor<NvsVideoFx> keyFrameProcessor = propertyVideoFx.keyFrameProcessor();
        if (keyFrameProcessor.getKeyFrameCount(SCALE_X) > 0) {
            /*存在关键帧，全部都要调整*/
            Map<Long, MeicamKeyFrame> keyFrameMap = keyFrameProcessor.getKeyFrameMap(SCALE_X);
            for (Map.Entry<Long, MeicamKeyFrame> item : keyFrameMap.entrySet()) {
                MeicamKeyFrame keyFrame = item.getValue();
                if (keyFrame != null) {
                    MeicamFxParam<?> fxParam = keyFrame.getFxParam(SCALE_X);
                    if (fxParam != null) {
                        float value = fxParam.getFloatValue();
                        if (value != Float.MAX_VALUE) {
                            KeyFrameCommand.setFloatVal(keyFrame, SCALE_X, -1 * value);
                        }
                    }
                }
            }
        } else {
            float floatVal = propertyVideoFx.getFloatVal(NvsConstants.FX_TRANSFORM_2D_SCALE_X, 1);
            VideoFxCommand.setFloatVal(propertyVideoFx, NvsConstants.FX_TRANSFORM_2D_SCALE_X, -1 * floatVal);
        }
        seekTimeline();
        if (mOnTimelineChangeListener != null) {
            mOnTimelineChangeListener.onSaveOperation();
        }
    }


    /**
     * 更改旋转角度
     * Change rotation.
     *
     * @param videoClip      the video clip 视频片段
     * @param keyFrameAtTime the time point of ke frame 关键帧的时间点
     */
    public long changeRotation(MeicamVideoClip videoClip, long keyFrameAtTime) {
        long atTime = keyFrameAtTime;
        if (videoClip == null || meicamTimeline == null) {
            return atTime;
        }
        MeicamVideoFx propertyVideoFx = videoClip.findPropertyVideoFx();
        if (propertyVideoFx == null) {
            return atTime;
        }
        KeyFrameProcessor<NvsVideoFx> keyFrameHolder = propertyVideoFx.keyFrameProcessor();
        if (keyFrameHolder.getKeyFrameCount(NvsConstants.KEY_CROPPER_TRANS_X) > 0) {
            /*有关键帧*/
            MeicamKeyFrame keyFrame = keyFrameHolder.getKeyFrame(keyFrameAtTime);
            if (keyFrame == null) {
                /*如果所给点没有关键帧，查找当前时间点是否有关键帧*/
                atTime = meicamTimeline.getCurrentPosition() - videoClip.getInPoint();
                keyFrame = keyFrameHolder.getKeyFrame(atTime);
                if (keyFrame == null) {
                    /*如果当前时间点没有有关键帧则添加*/
                    keyFrame = KeyFrameHolderCommand.addKeyFrame(propertyVideoFx,
                            MeicamKeyFrame.getDefaultKeyFrameValue(propertyVideoFx, atTime), atTime, 0);
                }
            }
            if (keyFrame != null) {
                Transform transform = rotationTransform(videoClip);
                if (transform != null) {
                    KeyFrameCommand.setFloatVal(keyFrame, MeicamKeyFrame.SCALE_X, transform.scaleX);
                    KeyFrameCommand.setFloatVal(keyFrame, MeicamKeyFrame.SCALE_Y, transform.scaleY);
                    KeyFrameCommand.setFloatVal(keyFrame, MeicamKeyFrame.ROTATION, transform.rotation);
                }

            } else {
                atTime = -1;
            }
            keyFrameHolder.updateKeyFrameControlPoints();
        } else {
            Transform transform = rotationTransform(videoClip);
            if (transform != null) {
                VideoFxCommand.setFloatVal(propertyVideoFx, NvsConstants.FX_TRANSFORM_2D_SCALE_X, transform.scaleX);
                VideoFxCommand.setFloatVal(propertyVideoFx, NvsConstants.FX_TRANSFORM_2D_SCALE_Y, transform.scaleY);
                VideoFxCommand.setFloatVal(propertyVideoFx, NvsConstants.FX_TRANSFORM_2D_ROTATION, transform.rotation);
            }
        }
        seekTimeline(meicamTimeline.getCurrentPosition(), 0);
        return atTime;
    }

    private Transform rotationTransform(MeicamVideoClip videoClip) {
        Transform transformFxMode = EditorEngine.getInstance().getNvsTransform(videoClip);
        if (transformFxMode == null) {
            return null;
        }
        if (transformFxMode.transformX == 0f && transformFxMode.transformY == 0f) {//用户没有手动平移
            int intRotation = (int) transformFxMode.rotation;
            int remainder = intRotation % 90;
            if (remainder == 0) {//用户没有手动旋转
                float scaleBackup = transformFxMode.scaleX;
                float scaleValue = fetchCropperScale(videoClip, intRotation);
                if (scaleBackup == scaleValue) {//用户没有手动缩放
                    transformFxMode.scaleX = fetchCropperScale(videoClip, intRotation + 90);
                    transformFxMode.scaleY = transformFxMode.scaleX;
                }
            }
        }
        transformFxMode.rotation += 90;
        if (transformFxMode.rotation >= 360) {
            int intRotation = (int) transformFxMode.rotation;
            transformFxMode.rotation = intRotation % 90;
        }
        return transformFxMode;
    }

    private float fetchCropperScale(MeicamVideoClip videoClip, int intRotation) {
        MeicamVideoFx cropperVideoFx = videoClip.getVideoFxByType(TYPE_RAW_BUILTIN, SUB_TYPE_CROPPER);
        if (cropperVideoFx == null) {
            cropperVideoFx = videoClip.getVideoFxByType(TYPE_RAW_BUILTIN, SUB_TYPE_CROPPER_EXT);
        }
        NvsVideoResolution resolution = meicamTimeline.getVideoResolution();
        float timelineRate = resolution.imageWidth * 1.0f / resolution.imageHeight;
        float assetAspectRatio = videoClip.getOriginalWidth() * 1.0f / videoClip.getOriginalHeight();
        float cropperRatio = -1.0f;
        float scaleValue = 1;
        boolean isOdd = (intRotation / 90) % 2 != 0;
        if (cropperVideoFx != null) {
            cropperRatio = cropperVideoFx.getFloatVal(NvsConstants.KEY_CROPPER_ASSET_ASPECT_RATIO);
        }
        if (assetAspectRatio > timelineRate) {
            if (assetAspectRatio > resolution.imageHeight * 1.0f / resolution.imageWidth) {
                if (Math.abs(cropperRatio + 1.0f) >= 0.00001) {
                    scaleValue = isOdd ? cropperRatio : scaleValue;
                } else {
                    scaleValue = isOdd ? assetAspectRatio : scaleValue;
                }
            } else {
                if (Math.abs(cropperRatio + 1.0f) >= 0.00001) {
                    scaleValue = isOdd ? 1.0f / cropperRatio : scaleValue;
                } else {
                    scaleValue = isOdd ? 1.0f / timelineRate : scaleValue;
                }
            }
        } else {
            if (assetAspectRatio > resolution.imageHeight * 1.0f / resolution.imageWidth) {
                if (Math.abs(cropperRatio + 1.0f) >= 0.00001) {
                    scaleValue = isOdd ? 1.0f / cropperRatio : scaleValue;
                } else {
                    scaleValue = isOdd ? 1.0f / assetAspectRatio : scaleValue;
                }
            } else {
                if (Math.abs(cropperRatio + 1.0f) >= 0.00001) {
                    scaleValue = isOdd ? cropperRatio : scaleValue;
                } else {
                    scaleValue = isOdd ? timelineRate : scaleValue;
                }
            }
        }
        return scaleValue;
    }

    @Override
    public void changeOpacity(MeicamVideoClip videoClip, float opacity) {
        if (meicamTimeline == null || videoClip == null) {
            return;
        }
        MeicamVideoFx propertyVideoFx = videoClip.findPropertyVideoFx();
        MeicamKeyFrame keyFrame = null;
        long atTime = meicamTimeline.getCurrentPosition() - videoClip.getInPoint();
        if (propertyVideoFx != null && propertyVideoFx.keyFrameProcessor().haveKeyFrame(NvsConstants.PROPERTY_OPACITY)) {
            keyFrame = propertyVideoFx.keyFrameProcessor().getKeyFrame(NvsConstants.PROPERTY_OPACITY, atTime);
        }
        if (keyFrame != null) {
            KeyFrameCommand.setFloatVal(keyFrame, NvsConstants.PROPERTY_OPACITY, opacity);
        } else {
            VideoClipCommand.setOpacity(videoClip, opacity);
            videoClip.setOpacity(opacity);
        }
        seekTimeline(getCurrentTimelinePosition(), 0);
    }

    /**
     * 改变混合模式
     * <p>
     * set Blending Mode
     *
     * @param videoClip the video clip
     * @param mode      the mode
     */
    public void setBlendingMode(MeicamVideoClip videoClip, int mode) {
        if (meicamTimeline == null || videoClip == null) {
            return;
        }
        VideoClipCommand.setParam(videoClip, VideoClipCommand.PARAM_BLENDING_MODE, mode);
        seekTimeline(0);
    }

    @Override
    public int deleteClip(MeicamVideoClip clipInfo, int trackIndex, boolean isPip) {
        if (meicamTimeline == null || clipInfo == null) {
            return ReturnCode.CODE_PARAM_ERROR;
        }
        long timelinePosition = getCurrentTimelinePosition();
        MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(trackIndex);
        if (videoTrack == null) {
            return ReturnCode.CODE_OTHER;
        }
        if (isPip) {
            MeicamVideoClip removeClip = VideoTrackCommand.removeVideoClipWidthSpace(videoTrack, clipInfo.getIndex());
            if (removeClip == null) {
                return ReturnCode.CODE_OTHER;
            }
            if (videoTrack.getClipCount() == 0) {
                meicamTimeline.removeVideoTrack(videoTrack.getIndex());
            }
        } else {
            /*主轨道至少要保留一个有效片段（非补黑片段）*/
            if (videoTrack.getClipCount() == 1) {
                return ReturnCode.CODE_CAN_NOT_OPERATE;
            } else if (videoTrack.getClipCount() == 2) {
                MeicamVideoClip videoClip = videoTrack.getVideoClip(1);
                if (CLIP_HOLDER.equals(videoClip.getVideoType())) {
                    return ReturnCode.CODE_CAN_NOT_OPERATE;
                }
            }
            long startTime = clipInfo.getOutPoint();
            long changeDuration = clipInfo.getOutPoint() - clipInfo.getInPoint();
            VideoTrackCommand.removeVideoClip(videoTrack, clipInfo.getIndex(), false);
            timelineAddOrSubtract(startTime, -changeDuration);
        }
        seekTimeline(timelinePosition, 0);
        if (mOnTimelineChangeListener != null) {
            mOnTimelineChangeListener.onTimelineChanged(meicamTimeline, true);
            if (!isPip) {
                mOnTimelineChangeListener.refreshEditorTimelineView(OnTimelineChangeListener.TYPE_DELETE_CLIP);
            }
            // mOnTimelineChangeListener.onChangeBottomView();
        }
        return ReturnCode.CODE_OK;
    }


    /**
     * 移除添加字幕贴纸等
     * Remove and add caption sticker clip boolean.
     *
     * @param oldClipInfo   the old clip info
     * @param newTrackIndex the new track index
     * @return the ClipInfo
     */
    public ClipInfo<?> removeAndAddCaptionStickerClip(ClipInfo<?> oldClipInfo, int newTrackIndex) {
        if (oldClipInfo == null) {
            return null;
        }
        MeicamStickerCaptionTrack meicamStickerCaptionTrack = meicamTimeline.findStickCaptionTrack(oldClipInfo.getTrackIndex());
        if (meicamStickerCaptionTrack == null) {
            return null;
        }
        boolean isSuccess = CaptionStickerTrackCommand.removeClip(meicamStickerCaptionTrack, false, oldClipInfo.getInPoint());
        if (!isSuccess) {
            return null;
        }
        MeicamStickerCaptionTrack meicamStickerCaptionTrackNew = meicamStickerCaptionTrack;
        if (oldClipInfo.getTrackIndex() != newTrackIndex){
            meicamStickerCaptionTrackNew = meicamTimeline.findStickCaptionTrack(newTrackIndex);
            if (meicamStickerCaptionTrackNew == null) {
                meicamStickerCaptionTrackNew = TimelineCommand.addStickCaptionTrack(meicamTimeline, newTrackIndex);
            }
        }
        if (meicamStickerCaptionTrackNew == null) {
            return null;
        }
        return CaptionStickerTrackCommand.addClip(meicamStickerCaptionTrackNew, oldClipInfo, false, true);
    }

    /**
     * 排序字幕和贴纸
     * Sort caption sticker clip.
     *
     * @param trackIndex the  track index
     */
    public void sortCaptionStickerClip(int trackIndex) {
        MeicamStickerCaptionTrack meicamStickerCaptionTrack = meicamTimeline.findStickCaptionTrack(trackIndex);
        if (meicamStickerCaptionTrack != null) {
            CaptionStickerTrackCommand.reSort(meicamStickerCaptionTrack);
        }
    }


    /**
     * 移除添加timeline特效等
     * Remove and add timeline  fx boolean.
     *
     * @param oldClipInfo   the old clip info
     * @param newTrackIndex the new track index
     * @return the ClipInfo
     */
    public MeicamTimelineVideoFxClip removeAndAddTimelineClip(MeicamTimelineVideoFxClip oldClipInfo, int newTrackIndex) {
        if (oldClipInfo == null) {
            return null;
        }
        MeicamTimelineVideoFxTrack meicamTimelineVideoFxTrack = meicamTimeline.getTimelineFxTrack(oldClipInfo.getTrackIndex());
        if (meicamTimelineVideoFxTrack == null) {
            return null;
        }
        boolean isSuccess = TimelineVideoFxTrackCommand.removeClip(meicamTimelineVideoFxTrack, oldClipInfo);
        if (!isSuccess) {
            return null;
        }
        meicamTimelineVideoFxTrack = meicamTimeline.getTimelineFxTrack(newTrackIndex);
        if (meicamTimelineVideoFxTrack == null) {
            meicamTimelineVideoFxTrack = TimelineCommand.addTimelineFxTrack(meicamTimeline, newTrackIndex);
        }
        if (meicamTimelineVideoFxTrack == null) {
            return null;
        }
        return TimelineVideoFxTrackCommand.addFxClip(meicamTimelineVideoFxTrack, oldClipInfo);
    }

    /**
     * 移除添加滤镜调节等
     * Remove and add fliter adjust clip boolean.
     *
     * @param oldClipInfo   the old clip info
     * @param newTrackIndex the new track index
     * @return the ClipInfo
     */
    public MeicamTimelineVideoFilterAndAdjustClip removeAndAddFilterAndAdjustClip(MeicamTimelineVideoFilterAndAdjustClip oldClipInfo, int newTrackIndex) {
        if (oldClipInfo == null) {
            return null;
        }
        MeicamTimelineVideoFxTrack meicamTimelineVideoFxTrack = meicamTimeline.getFilterAndAdjustTimelineTrack(oldClipInfo.getTrackIndex());
        if (meicamTimelineVideoFxTrack == null) {
            return null;
        }
        boolean isSuccess = TimelineVideoFxTrackCommand.removeFilterAndAdjustClip(meicamTimelineVideoFxTrack, oldClipInfo);
        if (!isSuccess) {
            return null;
        }
        meicamTimelineVideoFxTrack = meicamTimeline.getFilterAndAdjustTimelineTrack(newTrackIndex);
        if (meicamTimelineVideoFxTrack == null) {
            meicamTimelineVideoFxTrack = TimelineCommand.addFilterAndAdjustTrack(meicamTimeline, newTrackIndex);
        }
        if (meicamTimelineVideoFxTrack == null) {
            return null;
        }
        return TimelineVideoFxTrackCommand.addFilterAndAdjustClip(meicamTimelineVideoFxTrack, oldClipInfo);
    }


    /**
     * Gets sticker caption data.
     * 根据 trackIndex 和 inPoint 获取字幕贴纸组合字母片段
     *
     * @param trackIndex the track index
     * @param inPoint    the in point
     * @return the sticker caption data
     */
    public ClipInfo<?> getCaptionStickerData(int trackIndex, long inPoint) {
        MeicamStickerCaptionTrack stickerCaptionTrack = meicamTimeline.findStickCaptionTrack(trackIndex);
        if (stickerCaptionTrack == null) {
            return null;
        }
        return stickerCaptionTrack.getCaptionStickerClip(inPoint);
    }


    /**
     * Find all caption stick in position clip info.
     * <p>
     * 获取当前时间的所有的clip
     *
     * @param position the position
     * @return the clip info
     */
    public List<ClipInfo<?>> findAllCaptionStickByTimelinePosition(long position) {
        if (meicamTimeline == null) {
            LogUtils.e("timeline is null");
            return null;
        }
        List<ClipInfo<?>> clipInfos = new ArrayList<>();
        for (int i = meicamTimeline.getStickerCaptionTrackCount() - 1; i >= 0; i--) {
            MeicamStickerCaptionTrack meicamStickerCaptionTrack = meicamTimeline.findStickCaptionTrack(i);
            if (meicamStickerCaptionTrack == null) {
                continue;
            }
            for (int j = meicamStickerCaptionTrack.getClipCount() - 1; j >= 0; j--) {
                ClipInfo<?> clipInfo = meicamStickerCaptionTrack.getCaptionStickerClip(j);
                if (clipInfo == null) {
                    continue;
                }
                if (position >= clipInfo.getInPoint() && position < clipInfo.getOutPoint()) {
                    clipInfos.add(clipInfo);
                }
            }
        }
        return clipInfos;
    }

    /*
     * Find all caption  in position clip info.
     *
     * 获取当前时间的所有的clip
     * @param position the position
     * @return the clip info
     */
    public List<ClipInfo<?>> getCaptionsByTimelinePosition(long position) {
        List<ClipInfo<?>> clipInfos = new ArrayList<>();
        if (meicamTimeline == null) {
            return clipInfos;
        }
        for (int i = 0; i < meicamTimeline.getStickerCaptionTrackCount(); i++) {
            MeicamStickerCaptionTrack meicamStickerCaptionTrack = meicamTimeline.findStickCaptionTrack(i);
            if (meicamStickerCaptionTrack == null) {
                continue;
            }
            for (int j = meicamStickerCaptionTrack.getClipCount() - 1; j >= 0; j--) {
                ClipInfo<?> clipInfo = meicamStickerCaptionTrack.getCaptionStickerClip(j);
                if (clipInfo instanceof MeicamCaptionClip && position >= clipInfo.getInPoint() && position <= clipInfo.getOutPoint()) {
                    clipInfos.add(clipInfo);
                }
            }
        }
        return clipInfos;
    }

    /**
     * 删除 字幕等clip，如果该轨道没有数据，删除轨道
     * Remove caption stick.
     *
     * @param clipInfo the clip info
     */
    public boolean removeCaptionStickClip(ClipInfo<?> clipInfo, boolean saveOperation) {
        if (clipInfo == null) {
            return false;
        }
        int trackIndex = clipInfo.getTrackIndex();
        MeicamStickerCaptionTrack meicamStickerCaptionTrack = meicamTimeline.findStickCaptionTrack(trackIndex);
        if (meicamStickerCaptionTrack == null) {
            return false;
        }
        return CaptionStickerTrackCommand.removeClip(meicamStickerCaptionTrack, clipInfo.getInPoint(), saveOperation);
    }

    /**
     * 删除 字幕等clip，如果该轨道没有数据，删除轨道
     * Remove caption stick.
     *
     * @param clipInfo the clip info
     */
    public boolean removeCaptionStickClip(ClipInfo<?> clipInfo) {
        return removeCaptionStickClip(clipInfo, true);
    }


    @Override
    public void changeVoice(String fxId) {
    }

    /**
     * 切换Timeline画幅比例
     * <p>
     * Change the ratio of timeline.
     *
     * @param type 比例类型 type of ratio
     */
    @Override
    public void changeRatio(int type) {
        if (meicamTimeline == null) {
            LogUtils.e("timeline is null");
            return;
        }
        Point timeLineSize = TimelineUtil.calculateTimelineSize(type);
        LogUtils.d("changeRatio ->width = " + timeLineSize.x + ", height = " + timeLineSize.y);
        TimelineCommand.changeVideoSize(meicamTimeline, timeLineSize.x, timeLineSize.y);
        meicamTimeline.setMakeRatio(type);
    }

    /**
     * 检测视频分辨率是否存在
     * Check if the video resolution is present
     */
    public void checkVideoResolution() {
        if (meicamTimeline == null) {
            return;
        }
        if (meicamTimeline.getVideoResolution() == null) {
            MeicamVideoTrack meicamVideoTrack = meicamTimeline.getVideoTrack(TRACK_INDEX_MAIN);
            if (meicamVideoTrack == null) {
                return;
            }
            MeicamVideoClip meicamVideoClip = meicamVideoTrack.getVideoClip(0);
            if (meicamVideoClip == null) {
                return;
            }
            meicamTimeline.setVideoResolution(getVideoEditResolution(meicamVideoClip.getFilePath()));
        }
    }

    /**
     * 通过视频片段路径获取需要的时间线分辨率信息
     * <P></P>
     * Gets video edit resolution by clip.
     *
     * @param path the path
     * @return the video edit resolution by clip
     */
    public static NvsVideoResolution getVideoEditResolution(String path) {
        int imageWidth = 720, imageHeight = 1080;
        NvsVideoResolution videoEditRes = new NvsVideoResolution();
        int resolution = TIMELINE_RESOLUTION_VALUE;
        NvsAVFileInfo avFileInfo = NvsStreamingContext.getInstance().getAVFileInfo(path);
        if (avFileInfo != null) {
            NvsSize dimension = avFileInfo.getVideoStreamDimension(0);
            int streamRotation = avFileInfo.getVideoStreamRotation(0);
            imageWidth = dimension.width;
            imageHeight = dimension.height;
            if (streamRotation == 1 || streamRotation == 3) {
                imageWidth = dimension.height;
                imageHeight = dimension.width;
            }
        } else {
            LogUtils.e("getVideoEditResolutionByClip avFileInfo == null" + "===path===" + path);
        }
        LogUtils.d(" imageWidth = ", imageWidth + " , imageHeight = " + imageHeight);
        //按照视频的比例生成timeline的宽高 标准是720P （720*1280） 或者是1080P（1080*1920）
        float timelineRation = imageWidth * 1.0F / imageHeight;
        Point size = new Point();
        if (timelineRation > 1) {//宽视频 Wide video
            size.y = resolution;
            size.x = (int) (resolution * timelineRation);
        } else {//高视频 High video
            size.x = resolution;
            size.y = (int) (resolution * 1.0F / timelineRation);
        }
        videoEditRes.imageWidth = alignedData(size.x, 4);
        videoEditRes.imageHeight = alignedData(size.y, 2);
        videoEditRes.imagePAR = new NvsRational(1, 1);
        LogUtils.d("width = ", videoEditRes.imageWidth + ", height = " + videoEditRes.imageHeight);
        return videoEditRes;

    }

    /**
     * 整数对齐
     * Integer alignment
     *
     * @param data，源数据
     * @param num      对齐的数据
     */
    public static int alignedData(int data, int num) {
        return data - data % num;
    }

    /**
     * Set clip adjust data
     * <p>
     * 设置clip的调节数据
     *
     * @param clipInfo the videoClip
     * @param progress progress 调节进度
     * @param name     name 调节特效名称
     */
    public void setClipAdjustData(MeicamVideoClip clipInfo, int progress, String name) {
        if (clipInfo == null) {
            return;
        }
        if (TextUtils.isEmpty(name)) {
            return;
        }
        if (meicamTimeline == null) {
            return;
        }
        String fxId = getFxNameByEffectName(name);
        MeicamVideoFx adjustVideoFx = clipInfo.getVideoFxById(fxId);
        if (adjustVideoFx == null) {
            adjustVideoFx = VideoClipCommand.appendFx(clipInfo, NvsConstants.TYPE_RAW_BUILTIN, SUB_TYPE_ADJUST, fxId);
        }
        if (adjustVideoFx == null) {
            return;
        }
        VideoFxCommand.setFloatVal(adjustVideoFx, name, getProgressByEffectName(name, progress));
        seekTimeline(getCurrentTimelinePosition(), 0);
    }

    /**
     * Sets timeline adjust data.
     * 设置timeline的调节数据
     *
     * @param adjustVideoFx the adjust video fx
     * @param progress      the progress  调节进度
     * @param name          the name  调节特效名称
     */
    public void setTimelineAdjustData(MeicamTimelineVideoFxClip adjustVideoFx, int progress, String name) {
        if (TextUtils.isEmpty(name)) {
            return;
        }
        if (adjustVideoFx == null) {
            return;
        }
        TimelineFxCommand.setFloatVal(adjustVideoFx, name, getProgressByEffectName(name, progress));
        seekTimeline(0);
    }

    /**
     * 添加滤镜或者调节
     * Add filter and adjust timeline fx meicam timeline video filter and adjust clip.
     *
     * @param type   the type
     * @param fxType the fx type
     * @param desc   the desc
     * @return the meicam timeline video filter and adjust clip
     */
    public MeicamTimelineVideoFilterAndAdjustClip addFilterAndAdjustTimelineFx(String type, String fxType, String desc, String displayName) {
        long inPoint = getCurrentTimelinePosition();
        long defaultDuration = DEFAULT_LENGTH_FX;
        if (inPoint + defaultDuration >= meicamTimeline.getDuration()) {
            defaultDuration = meicamTimeline.getDuration() - inPoint;
        }
        if (defaultDuration < 0.5 * CommonData.TIMEBASE) {
            if (mOnTimelineChangeListener != null) {
                mOnTimelineChangeListener.onTips(ReturnCode.CODE_OTHER);
            }
            return null;
        }
        int trackIndex = getFilterAndAdjustTimelineFxTrackIndex(meicamTimeline, inPoint, inPoint + defaultDuration);
        MeicamTimelineVideoFxTrack timelineVideoFxTrack = meicamTimeline.getFilterAndAdjustTimelineTrack(trackIndex);
        if (timelineVideoFxTrack == null) {
            timelineVideoFxTrack = TimelineCommand.addFilterAndAdjustTrack(meicamTimeline, trackIndex);
        }
        MeicamTimelineVideoFilterAndAdjustClip clip = TimelineVideoFxTrackCommand.addFilterAndAdjustClip(timelineVideoFxTrack, fxType, inPoint, defaultDuration);
        if (MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_ADJUST.equals(fxType)) {
            FilterAndAdjustClipCommand.addAdjustTimelineFx(clip, type, fxType, getFxNameByEffectName(desc));
        } else {
            FilterAndAdjustClipCommand.addAdjustTimelineFx(clip, type, fxType, desc);
            FilterAndAdjustClipCommand.setDisplayName(clip, displayName);

        }
        return clip;
    }

    /**
     * 获取特效需要添加的轨道
     * Gets effect track index.
     *
     * @param inPoint  the in point
     * @param outPoint the out point
     * @return the effect track index
     */
    public static int getEffectTrackIndex(MeicamTimeline meicamTimeline, long inPoint, long outPoint) {
        if (meicamTimeline == null) {
            return -1;
        }
        for (int i = 0; i < meicamTimeline.getTimelineFxTrackCount(); i++) {
            MeicamTimelineVideoFxTrack videoFxTrack = meicamTimeline.getTimelineFxTrack(i);
            if (videoFxTrack == null) {
                continue;
            }
            int nowTrackIndex = videoFxTrack.getIndex();
            if (videoFxTrack.getClipCount() == 0) {
                return nowTrackIndex;
            }
            for (int j = 0; j < videoFxTrack.getClipCount(); j++) {
                ClipInfo<?> nowClipInfo = videoFxTrack.getClip(j);
                if (nowClipInfo == null) {
                    continue;
                }
                long nowInpoint = nowClipInfo.getInPoint();
                long nowOutpoint = nowClipInfo.getOutPoint();
                long beforeOutpoint = 0;
                if (j > 0) {
                    ClipInfo<?> beforeClipInfo = videoFxTrack.getClip(j - 1);
                    if (beforeClipInfo == null) {
                        continue;
                    }
                    beforeOutpoint = beforeClipInfo.getOutPoint();
                }
                if (inPoint >= beforeOutpoint && outPoint <= nowInpoint) {
                    return nowTrackIndex;
                }
                long afterInPoint;
                if (j < videoFxTrack.getClipCount() - 1) {
                    ClipInfo<?> afterClipInfo = videoFxTrack.getClip(j + 1);
                    if (afterClipInfo == null) {
                        continue;
                    }
                    afterInPoint = afterClipInfo.getInPoint();
                    if (inPoint >= nowOutpoint && outPoint <= afterInPoint) {
                        return nowTrackIndex;
                    }
                } else {
                    if (inPoint >= nowOutpoint) {
                        return nowTrackIndex;
                    }
                }
            }
        }
        return meicamTimeline.getTimelineFxTrackCount();
    }


    /**
     * 获取滤镜或者调节需要添加的轨道
     * Gets filter and adjust timeline fx track index.
     *
     * @param inPoint  the in point
     * @param outPoint the out point
     * @return the filter and adjust timeline fx track index
     */
    public static int getFilterAndAdjustTimelineFxTrackIndex(MeicamTimeline meicamTimeline, long inPoint, long outPoint) {
        if (meicamTimeline == null) {
            return -1;
        }
        for (int i = 0; i < meicamTimeline.getFilterAndAdjustTimelineTracksCount(); i++) {
            MeicamTimelineVideoFxTrack videoFxTrack = meicamTimeline.getFilterAndAdjustTimelineTrack(i);
            if (videoFxTrack == null) {
                continue;
            }
            int nowTrackIndex = videoFxTrack.getIndex();
            if (videoFxTrack.getFilterAndAdjustCount() == 0) {
                return nowTrackIndex;
            }
            for (int j = 0; j < videoFxTrack.getFilterAndAdjustCount(); j++) {
                ClipInfo<?> nowClipInfo = videoFxTrack.getFilterAndAdjustClip(j);
                if (nowClipInfo == null) {
                    continue;
                }
                long nowInpoint = nowClipInfo.getInPoint();
                long nowOutpoint = nowClipInfo.getOutPoint();
                long beforeOutpoint = 0;
                if (j > 0) {
                    ClipInfo<?> beforeClipInfo = videoFxTrack.getFilterAndAdjustClip(j - 1);
                    if (beforeClipInfo == null) {
                        continue;
                    }
                    beforeOutpoint = beforeClipInfo.getOutPoint();
                }
                if (inPoint >= beforeOutpoint && outPoint <= nowInpoint) {
                    return nowTrackIndex;
                }
                long afterInPoint;
                if (j < videoFxTrack.getFilterAndAdjustCount() - 1) {
                    ClipInfo<?> afterClipInfo = videoFxTrack.getFilterAndAdjustClip(j + 1);
                    if (afterClipInfo == null) {
                        continue;
                    }
                    afterInPoint = afterClipInfo.getInPoint();
                    if (inPoint >= nowOutpoint && outPoint <= afterInPoint) {
                        return nowTrackIndex;
                    }
                } else {
                    if (inPoint >= nowOutpoint) {
                        return nowTrackIndex;
                    }
                }
            }
        }
        return meicamTimeline.getFilterAndAdjustTimelineTracksCount();
    }

    /**
     * 替换增加滤镜或者增加调节的其他选项
     * Replace filter and adjust timeline fx meicam timeline video filter and adjust clip.
     *
     * @param adjustClip  the adjust clip
     * @param type        the type
     * @param fxType      the fx type
     * @param desc        the desc
     * @param displayName the displayName
     * @return the meicam timeline video filter and adjust clip
     */
    public MeicamTimelineVideoFilterAndAdjustClip replaceFilterAndAdjustTimelineFx(MeicamTimelineVideoFilterAndAdjustClip adjustClip, String type, String fxType, String desc, String displayName) {
        if (MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_ADJUST.equals(fxType)) {
            FilterAndAdjustClipCommand.addAdjustTimelineFx(adjustClip, type, fxType, getFxNameByEffectName(desc));
            return adjustClip;
        } else {
            //滤镜要先删掉，然后再添加
            MeicamTimelineVideoFxTrack track = meicamTimeline.getFilterAndAdjustTimelineTrack(adjustClip.getTrackIndex());
            if (track == null) {
                return null;
            }
            TimelineVideoFxTrackCommand.removeFilterAndAdjustClip(track, adjustClip);
            MeicamTimelineVideoFilterAndAdjustClip clip = TimelineVideoFxTrackCommand.addFilterAndAdjustClip(track, fxType, adjustClip.getInPoint(), adjustClip.getOutPoint() - adjustClip.getInPoint());

            FilterAndAdjustClipCommand.addAdjustTimelineFx(clip, type, fxType, desc);

            FilterAndAdjustClipCommand.setDisplayName(clip, displayName);
            return clip;
        }

    }

    /**
     * 根据 trackIndex 和Index找调节滤镜片段
     * Gets meicam timeline video filter and adjust clip.
     *
     * @param trackIndex the track index 轨道索引
     * @param index      the index clip在轨道中的索引
     * @return the meicam timeline video filter and adjust clip
     */
    public MeicamTimelineVideoFilterAndAdjustClip getMeicamTimelineVideoFilterAndAdjustClip(int trackIndex, int index) {
        if (meicamTimeline == null) {
            return null;
        }
        MeicamTimelineVideoFxTrack track = meicamTimeline.getFilterAndAdjustTimelineTrack(trackIndex);
        if (track == null) {
            return null;
        }
        return track.getFilterAndAdjustClip(index);
    }

    /**
     * 移除滤镜或者调节clip
     * Remove filter and adjust timeline fx boolean.
     *
     * @param adjustClip the adjust clip
     * @return the boolean
     */
    public boolean removeFilterAndAdjustTimelineFx(MeicamTimelineVideoFilterAndAdjustClip adjustClip) {
        MeicamTimelineVideoFxTrack track = meicamTimeline.getFilterAndAdjustTimelineTrack(adjustClip.getTrackIndex());
        if (track != null) {
            boolean andAdjustClip = TimelineVideoFxTrackCommand.removeFilterAndAdjustClip(track,adjustClip);
            //删除尾部的空轨道
            //Delete the empty track at the end
            removeEmptyFilterAndAdjustTrackInTheEnd();
            return andAdjustClip;
        }
        return false;
    }

    /**
     * Apply filter to timeline.
     * <p>
     * 应用滤镜到timeline
     *
     * @param baseInfo the base info
     * @return the meicam timeline video fx
     */
    public MeicamTimelineVideoFilterAndAdjustClip applyTimelineFilter(IBaseInfo baseInfo) {
        if (baseInfo == null) {
            LogUtils.e("baseInfo is null");
            return null;
        }
        boolean isBuildIn = baseInfo.getEffectMode() == BaseInfo.EFFECT_MODE_BUILTIN;
        String type = isBuildIn ? TYPE_BUILD_IN : TYPE_PACKAGE;
        String desc = isBuildIn ? baseInfo.getEffectId() : baseInfo.getPackageId();

        return addFilterAndAdjustTimelineFx(type, MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FILTER, desc, baseInfo.getName());
    }

    /**
     * Apply filter to clip.
     * <p>
     * 应用滤镜到Clip
     *
     * @param baseInfo the base info
     * @param clipInfo the clip info
     * @return the  video fx
     */
    public MeicamVideoFx applyClipFilter(IBaseInfo baseInfo, MeicamVideoClip clipInfo) {
        if (baseInfo == null || clipInfo == null) {
            LogUtils.e("baseInfo or clipInfo is null");
            return null;
        }
        boolean isBuildIn = baseInfo.getEffectMode() == BaseInfo.EFFECT_MODE_BUILTIN;
        String type = isBuildIn ? TYPE_BUILD_IN : TYPE_PACKAGE;
        String desc = isBuildIn ? baseInfo.getEffectId() : baseInfo.getPackageId();
        MeicamVideoFx meicamVideoFx = VideoClipCommand.appendFilter(clipInfo, type, desc, false);
        if (meicamVideoFx != null) {
            seekTimeline(0);
            if (mOnTimelineChangeListener != null) {
                mOnTimelineChangeListener.onTimelineChanged(meicamTimeline, true);
            }
        }
        return meicamVideoFx;
    }


    /**
     * Apply fx to clip.
     * <p>
     * 应用特效到Clip
     *
     * @param baseInfo the base info
     * @param clipInfo the clip info
     * @return the  video fx
     */
    public MeicamVideoFx applyClipFx(IBaseInfo baseInfo, MeicamVideoClip clipInfo) {
        if (baseInfo == null || clipInfo == null) {
            LogUtils.e("baseInfo or clipInfo is null");
            return null;
        }
        boolean isBuildIn = baseInfo.getEffectMode() == BaseInfo.EFFECT_MODE_BUILTIN;
        String desc = isBuildIn ? baseInfo.getEffectId() : baseInfo.getPackageId();
        MeicamVideoFx meicamVideoFx = VideoClipCommand.appendFx(clipInfo, TYPE_RAW_BUILTIN, MeicamVideoFx.SubType.SUB_TYPE_PLUG_FX, desc, true);
        if (meicamVideoFx != null) {
            //raw filter有个特性，当他会渲染到自己图像之外的话会重新在扩边之后的图像大小去渲染
            //不需要这个的话就把这个参数设为true
            VideoFxCommand.setBooleanVal(meicamVideoFx, "Force Identical Position", true, true);
            seekTimeline(0);
            if (mOnTimelineChangeListener != null) {
                mOnTimelineChangeListener.onTimelineChanged(meicamTimeline, false);
            }
        }
        return meicamVideoFx;
    }

    /**
     * 添加特效  当前有特效替换，无特效则添加
     * Add timeline effect.
     *
     * @param baseInfo       the base info
     * @param preVideoFxClip the pre video fx clip
     * @param saveOperation  true save operation false not
     */
    public MeicamTimelineVideoFxClip addTimelineEffect(IBaseInfo baseInfo, MeicamTimelineVideoFxClip preVideoFxClip, boolean saveOperation) {
        if (meicamTimeline == null) {
            LogUtils.e("timeline is null");
            return null;
        }
        String type = baseInfo.getEffectMode() == BaseInfo.EFFECT_MODE_BUILTIN ? TYPE_BUILD_IN : TYPE_PACKAGE;
        String desc = baseInfo.getEffectMode() == BaseInfo.EFFECT_MODE_BUILTIN ? baseInfo.getEffectId() : baseInfo.getPackageId();
        long inPoint, defaultDuration;
        long currentTimelinePosition = getCurrentTimelinePosition();
        MeicamTimelineVideoFxTrack timelineFxTrack;
        int trackIndex = 0;
        if (preVideoFxClip != null) {
            inPoint = preVideoFxClip.getInPoint();
            defaultDuration = preVideoFxClip.getOutPoint() - preVideoFxClip.getInPoint();
            timelineFxTrack = meicamTimeline.getTimelineFxTrack(preVideoFxClip.getTrackIndex());
            if (timelineFxTrack != null) {
                TimelineVideoFxTrackCommand.removeClip(timelineFxTrack, preVideoFxClip.getInPoint());
            }
        } else {
            defaultDuration = DEFAULT_LENGTH_FX;
            inPoint = currentTimelinePosition;
            if (inPoint + defaultDuration >= meicamTimeline.getDuration()) {
                defaultDuration = meicamTimeline.getDuration() - inPoint;
            }
            if (defaultDuration < 0.5 * CommonData.TIMEBASE) {
                if (mOnTimelineChangeListener != null) {
                    mOnTimelineChangeListener.onTips(ReturnCode.CODE_OTHER);
                }
                return null;
            }
            trackIndex = getEffectTrackIndex(meicamTimeline, inPoint, inPoint + defaultDuration);
        }
        timelineFxTrack = meicamTimeline.getTimelineFxTrack(trackIndex);
        if (timelineFxTrack == null) {
            timelineFxTrack = TimelineCommand.addTimelineFxTrack(meicamTimeline, trackIndex);
        }
        if (timelineFxTrack == null) {
            return null;
        }
        long effectDuration = getEffectDuration(timelineFxTrack, inPoint, defaultDuration);
        MeicamTimelineVideoFxClip effect = TimelineVideoFxTrackCommand.addFxClip(timelineFxTrack, type, inPoint, effectDuration, desc);
        if (effect != null) {
            TimelineFxCommand.setDisplayName(effect, baseInfo.getName());
            if (mOnTimelineChangeListener != null) {
                mOnTimelineChangeListener.onAddStickerCaptionPicFx(effect, CommonData.TYPE_EFFECT);
                mOnTimelineChangeListener.onTimelineChanged(meicamTimeline, saveOperation);
                mOnTimelineChangeListener.onNeedTrackSelectChanged(effect.getTrackIndex(), effect.getInPoint());
            }
        }
        return effect;
    }

    /**
     * 重置timeline的调节数据
     * Reset timeline adjust data.
     *
     * @param meicamTimelineVideoFilterAndAdjustClip the meicam timeline video filter and adjust clip
     */
    public void resetTimelineAdjustData(MeicamTimelineVideoFilterAndAdjustClip meicamTimelineVideoFilterAndAdjustClip) {
        if (meicamTimeline == null || meicamTimelineVideoFilterAndAdjustClip == null) {
            return;
        }
        Map<String, List<String>> adjustFxAndKeyMap = NvsConstants.getAdjustFxAndKeyMap();
        Set<Map.Entry<String, List<String>>> entries = adjustFxAndKeyMap.entrySet();
        for (Map.Entry<String, List<String>> entry : entries) {
            MeicamTimelineVideoFxClip meicamTimelineVideoFxClip = meicamTimelineVideoFilterAndAdjustClip.getAdjustTimelineFx(entry.getKey());
            if (meicamTimelineVideoFxClip != null) {
                List<String> value = entry.getValue();
                for (String key : value) {
                    meicamTimelineVideoFxClip.setFloatVal(key, 0);
                }
            }
        }
        seekTimeline();
    }

    /**
     * Reset clip adjust data
     * <p>
     * 重置clip的调节数据
     *
     * @param videoClip videoClip the video clip
     */
    public void resetClipAdjustData(MeicamVideoClip videoClip) {
        if (videoClip == null) {
            return;
        }
        Map<String, List<String>> adjustFxAndKeyMap = NvsConstants.getAdjustFxAndKeyMap();
        Set<Map.Entry<String, List<String>>> entries = adjustFxAndKeyMap.entrySet();
        for (Map.Entry<String, List<String>> entry : entries) {
            List<String> value = entry.getValue();
            for (String key : value) {
                int progress;
                if (NvsConstants.ADJUST_BLACKPOINT.equals(key) || NvsConstants.ADJUST_DEGREE.equals(key)
                        || NvsConstants.ADJUST_AMOUNT.equals(key)) {
                    progress = 0;
                } else {
                    progress = 50;
                }
                videoClip.setAdjustItemValue(entry.getKey(), key, getProgressByEffectName(key, progress));
            }
        }
        seekTimeline();
    }

    /**
     * Apply clip adjust to all
     * <p>
     * 调节数据应用到全部
     *
     * @param videoClip videoClip the video clip
     * @return Apply success or not.true：yes；false no.
     */
    public boolean applyClipAdjustToAll(MeicamVideoClip videoClip) {
        if (videoClip == null || meicamTimeline == null) {
            return false;
        }
        Map<String, List<String>> adjustFxAndKeyMap = NvsConstants.getAdjustFxAndKeyMap();
        Set<Map.Entry<String, List<String>>> entries = adjustFxAndKeyMap.entrySet();
        Map<String, Float> keyValueMap = new HashMap<>();
        for (Map.Entry<String, List<String>> entry : entries) {
            List<String> value = entry.getValue();
            for (String key : value) {
                keyValueMap.put(key, videoClip.getAdjustItemValue(entry.getKey(), key));
            }
        }
        int count = meicamTimeline.videoTrackCount();
        for (int index = 0; index < count; index++) {
            MeicamVideoTrack meicamVideoTrack = meicamTimeline.getVideoTrack(index);
            int clipCount = meicamVideoTrack.getClipCount();
            for (int i = 0; i < clipCount; i++) {
                MeicamVideoClip clip = meicamVideoTrack.getVideoClip(i);
                if (clip == videoClip) {
                    continue;
                }
                for (Map.Entry<String, List<String>> entry : entries) {
                    MeicamVideoFx adjustVideoFx = clip.getAdjustVideoFx(entry.getKey());
                    if (adjustVideoFx != null) {
                        List<String> value = entry.getValue();
                        for (String key : value) {
                            Float aFloat = keyValueMap.get(key);
                            if (aFloat == null || aFloat == MeicamVideoFx.INVALID_VALUE) {
                                aFloat = 0f;
                            }
                            adjustVideoFx.setFloatVal(key, aFloat);
                        }

                    }
                }
            }
        }
        seekTimeline();
        return true;
    }


    private float getProgressByEffectName(String name, int progress) {
        float adjustData = progress / 50f - 1;
        if (NvsConstants.ADJUST_AMOUNT.equals(name)
                || NvsConstants.ADJUST_DEGREE.equals(name)) {
            adjustData = progress / 100f;
        } else if (NvsConstants.ADJUST_BLACKPOINT.equals(name)) {
            //最小值是0，最大值是-10
            //The min is 0, the max is -10
            adjustData = (10F / 100 * -progress);
        }
        return adjustData;
    }

    public String getFxNameByEffectName(String name) {
        if (NvsConstants.ADJUST_AMOUNT.equals(name)) {
            return NvsConstants.FX_SHARPEN;
        } else if (NvsConstants.ADJUST_DEGREE.equals(name)) {
            return NvsConstants.FX_VIGNETTE;
        } else if (NvsConstants.ADJUST_BLACKPOINT.equals(name)) {
            return NvsConstants.ADJUST_TYPE_BASIC_IMAGE_ADJUST;
        } else if (NvsConstants.ADJUST_TINT.equals(name)) {
            return NvsConstants.ADJUST_TINT;
        } else if (NvsConstants.ADJUST_TEMPERATURE.equals(name)) {
            return NvsConstants.ADJUST_TINT;
        } else if (NvsConstants.ADJUST_SHADOW.equals(name)
                || NvsConstants.ADJUST_HIGHTLIGHT.equals(name)
                || NvsConstants.ADJUST_SATURATION.equals(name)
                || NvsConstants.ADJUST_CONTRAST.equals(name)
                || NvsConstants.ADJUST_BRIGHTNESS.equals(name)) {
            return NvsConstants.ADJUST_TYPE_BASIC_IMAGE_ADJUST;
        }
        return null;
    }


    /**
     * Add animator sticker.
     *
     * @param uuid       the uuid
     * @param coverPath  the cover path
     * @param viewWidth  the view width
     * @param viewHeight the view height
     */
    public void addAnimatorSticker(String uuid, String coverPath, int viewWidth, int viewHeight) {
        if (meicamTimeline == null) {
            LogUtils.e("timeline is null");
            return;
        }
        long inPoint = meicamTimeline.getCurrentPosition();
        /*
         * 对片头主题 进行特殊处理，贴纸不能加在片头主题片段上
         * Special treatment for the opening theme, stickers can not be added to the opening theme segment
         * */
        long titleThemeDuration = meicamTimeline.getTitleThemeDuration();
        if (titleThemeDuration > 0) {
            if (inPoint < titleThemeDuration) {
                inPoint = titleThemeDuration;
            }
        }
        long outPoint = inPoint + DEFAULT_LENGTH_FX;
        int trackIndex = getTrackIndex(inPoint, outPoint);
        MeicamStickerCaptionTrack meicamStickerCaptionTrack = TimelineCommand.addStickCaptionTrack(meicamTimeline, trackIndex);
        if (meicamStickerCaptionTrack == null) {
            return;
        }
        MeicamStickerClip meicamStickerClip = CaptionStickerTrackCommand.addSticker(meicamStickerCaptionTrack, inPoint, outPoint, uuid, false, "");
        if (meicamStickerClip == null) {
            return;
        }

        StickerCommand.setParam(meicamStickerClip, StickerCommand.PARAM_COVER_IMAGE_PATH, coverPath);
        translateStickerRandom(meicamStickerClip, viewWidth, viewHeight);
        //seekTimeline(inPoint, NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_ANIMATED_STICKER_POSTER);
        /*
         * 贴纸添加动画后，需要去掉NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_ANIMATED_STICKER_POSTER标记
         */
        seekTimeline(inPoint, 0);
        if (mOnTimelineChangeListener != null) {
            mOnTimelineChangeListener.onAddStickerCaptionPicFx(meicamStickerClip, CommonData.TYPE_STICKER);
        }
        if (mOnTimelineChangeListener != null) {
            mOnTimelineChangeListener.onSaveOperation();
        }

    }

    /**
     * Add custom animator sticker.
     *
     * @param filePath   the file path
     * @param uuid       the uuid
     * @param coverPath  the cover path
     * @param viewWidth  the view width
     * @param viewHeight the view height
     */
    public void addCustomAnimatorSticker(String filePath, String uuid, String coverPath, int viewWidth, int viewHeight) {
        if (meicamTimeline == null) {
            LogUtils.e("timeline is null");
            return;
        }
        long inPoint = meicamTimeline.getCurrentPosition();
        /*
         * 对片头主题 进行特殊处理，贴纸不能加在片头主题片段上
         * Special treatment for the opening theme, stickers can not be added to the opening theme segment
         * */
        long titleThemeDuration = meicamTimeline.getTitleThemeDuration();
        if (titleThemeDuration > 0) {
            if (inPoint < titleThemeDuration) {
                inPoint = titleThemeDuration;
            }
        }
        long outPoint = inPoint + DEFAULT_LENGTH_FX;

        int trackIndex = getTrackIndex(inPoint, outPoint);
        MeicamStickerCaptionTrack meicamStickerCaptionTrack = TimelineCommand.addStickCaptionTrack(meicamTimeline, trackIndex);
        if (meicamStickerCaptionTrack == null) {
            return;
        }
        MeicamStickerClip meicamStickerClip = CaptionStickerTrackCommand.addSticker(meicamStickerCaptionTrack, inPoint, outPoint, uuid, true, filePath);
        if (meicamStickerClip == null) {
            return;
        }
        StickerCommand.setParam(meicamStickerClip, StickerCommand.PARAM_COVER_IMAGE_PATH, coverPath);
        translateStickerRandom(meicamStickerClip, viewWidth, viewHeight);
        //seekTimeline(inPoint, NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_ANIMATED_STICKER_POSTER);
        /*
         * 贴纸添加动画后，需要去掉NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_ANIMATED_STICKER_POSTER标记
         */
        seekTimeline(inPoint, 0);
        if (mOnTimelineChangeListener != null) {
            mOnTimelineChangeListener.onAddStickerCaptionPicFx(meicamStickerClip, CommonData.TYPE_STICKER);
        }
        if (mOnTimelineChangeListener != null) {
            mOnTimelineChangeListener.onSaveOperation();
        }
    }

    private void translateStickerRandom(MeicamStickerClip stickerClip, int viewWidth, int viewHeight) {
        if (stickerClip == null) {
            return;
        }
        //判断当前时间下是否存在其他字幕，贴纸等，如果不存在那么贴纸就不进行随机缩放了。
        List<ClipInfo<?>> clipInfos = findAllCaptionStickByTimelinePosition(getCurrentTimelinePosition());
        if (clipInfos.size() == 1) {
            return;
        }
        /*
         * 获取贴纸的原始包围矩形框变换后的顶点位置
         * Gets the vertex position after the transformation of the original bounding rectangle of the sticker
         * */
        List<PointF> list = stickerClip.getBoundingRectangleVertices();
        if (list == null || list.size() < 4) {
            return;
        }
        boolean isHorizonFlip = stickerClip.isHorizontalFlip();
        if (isHorizonFlip) {
            /*
             * 如果已水平翻转，需要对顶点数据进行处理
             * If it has been flipped horizontally, the vertex data needs to be processed
             * */
            Collections.swap(list, 0, 3);
            Collections.swap(list, 1, 2);
        }

        List<PointF> newList = CoordinateUtil.parseCanonicalToView(list, viewWidth, viewHeight);
        PointF leftTop = newList.get(0);
        float x = leftTop.x;
        float y = leftTop.y;
        Random rand = new Random();
        int randX = rand.nextInt(10);
        int randY = rand.nextInt(10);
        boolean isX = rand.nextBoolean();
        boolean isY = rand.nextBoolean();
        float transtionX = randX / 10.0f * x;
        float transtionY = randY / 10.0f * y;
        transtionX = isX ? transtionX : -transtionX;
        transtionY = isY ? transtionY : -transtionY;
        StickerCommand.setParam(stickerClip, StickerCommand.PARAM_TRANS_X, transtionX);
        StickerCommand.setParam(stickerClip, StickerCommand.PARAM_TRANS_Y, transtionY);
    }

    /**
     * 更改视频片段的裁出裁入点
     *
     * @param clipIndex the clip index 片段索引
     * @param trim      trim point 裁点
     * @param isTrimIn  true trim in point裁入点，false trim out point裁出点
     */
    public void changeVideoClipTrim(int clipIndex, long trim, boolean isTrimIn) {
        if (meicamTimeline == null) {
            return;
        }
        MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(TRACK_INDEX_MAIN);
        if (videoTrack != null) {
            MeicamVideoClip videoClip = videoTrack.getVideoClip(clipIndex);
            if (videoClip != null) {
                if (isTrimIn && CommonData.CLIP_VIDEO.equals(videoClip.getVideoType())) {
                    VideoClipCommand.setTrimIn(videoClip, trim, true);
                } else {
                    VideoClipCommand.setTrimOut(videoClip, trim, true);
                }
            }
        }
    }

    /**
     * 给片段添加关键帧
     * Add key frame for  clip
     *
     * @param clipInfo  the clip片段
     * @param timestamp the timestamp时间戳
     */
    public MeicamKeyFrame addClipKeyFrame(IKeyFrameProcessor<?> clipInfo, long timestamp) {
        if (clipInfo == null) {
            return null;
        }
        KeyFrameProcessor<?> keyFrameProcessor = clipInfo.keyFrameProcessor();
        if (timestamp >= keyFrameProcessor.getInPoint() && timestamp <= keyFrameProcessor.getOutPoint()) {
            long atTime = timestamp - keyFrameProcessor.getInPoint();
            List<MeicamFxParam<?>> clipParam = getClipParam(keyFrameProcessor, atTime);
            MeicamKeyFrame keyFrame = KeyFrameHolderCommand.addKeyFrame(clipInfo, clipParam, atTime, 0);
            if (keyFrame != null) {
                KeyFrameHolderCommand.cutKeyFrameCurve(clipInfo, atTime, NvsConstants.KEY_CROPPER_TRANS_X);
            }
            return keyFrame;
        }
        return null;
    }

    /**
     * 给片段添加关键帧
     * Add key frame for  clip
     *
     * @param clipInfo  the clip片段
     * @param timestamp the timestamp时间戳
     */
    public MeicamKeyFrame addClipKeyFrame(IKeyFrameProcessor<?> clipInfo, PlugDetail.Param param, long timestamp) {
        if (clipInfo == null) {
            return null;
        }
        KeyFrameProcessor<?> keyFrameProcessor = clipInfo.keyFrameProcessor();
        if (timestamp >= keyFrameProcessor.getInPoint() && timestamp <= keyFrameProcessor.getOutPoint()) {
            long atTime = timestamp - keyFrameProcessor.getInPoint();
            List<MeicamFxParam<?>> params = getParam(param);
            MeicamKeyFrame keyFrame = KeyFrameHolderCommand.addKeyFrame(clipInfo, params, atTime, 0);
            if (keyFrame != null) {
                KeyFrameHolderCommand.cutKeyFrameCurve(clipInfo, keyFrame.getAtTime(), param.paramName);
            }
            return keyFrame;
        }
        return null;
    }

    public static List<MeicamFxParam<?>> getClipParam(KeyFrameProcessor<?> processor, long atTime) {
        if (processor == null) {
            return null;
        }
        Object object = processor.getObject();
        List<MeicamFxParam<?>> data = new ArrayList<>();
        if (object instanceof NvsTimelineCaption) {
            NvsTimelineCaption caption = (NvsTimelineCaption) object;
            caption.setCurrentKeyFrameTime(atTime);
            data.add(new MeicamFxParam<>(TYPE_FLOAT, CAPTION_SCALE_X, caption.getFloatValAtTime(CAPTION_SCALE_X, atTime)));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, CAPTION_SCALE_Y, caption.getFloatValAtTime(CAPTION_SCALE_Y, atTime)));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, CAPTION_TRANS_X, caption.getFloatValAtTime(CAPTION_TRANS_X, atTime)));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, CAPTION_TRANS_Y, caption.getFloatValAtTime(CAPTION_TRANS_Y, atTime)));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, CAPTION_ROTATION_Z, caption.getFloatValAtTime(CAPTION_ROTATION_Z, atTime)));
        } else if (object instanceof NvsCompoundCaption) {
            /*组合字幕暂无*/
        } else if (object instanceof NvsTimelineAnimatedSticker) {
            /*贴纸关键帧*/
            NvsTimelineAnimatedSticker sticker = (NvsTimelineAnimatedSticker) object;
            sticker.setCurrentKeyFrameTime(atTime);
            data.add(new MeicamFxParam<>(TYPE_FLOAT, STICKER_SCALE, sticker.getFloatValAtTime(STICKER_SCALE, atTime)));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, STICKER_TRANS_X, sticker.getFloatValAtTime(STICKER_TRANS_X, atTime)));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, STICKER_TRANS_Y, sticker.getFloatValAtTime(STICKER_TRANS_Y, atTime)));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, STICKER_ROTATION_Z, sticker.getFloatValAtTime(STICKER_ROTATION_Z, atTime)));
        } else if (object instanceof NvsVideoFx) {
            /*视频关键帧*/
            NvsVideoFx videoFx = (NvsVideoFx) object;
            data.add(new MeicamFxParam<>(TYPE_FLOAT, MeicamKeyFrame.SCALE_X, videoFx.getFloatValAtTime(MeicamKeyFrame.SCALE_X, atTime)));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, MeicamKeyFrame.SCALE_Y, videoFx.getFloatValAtTime(MeicamKeyFrame.SCALE_Y, atTime)));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, MeicamKeyFrame.TRANS_X, videoFx.getFloatValAtTime(MeicamKeyFrame.TRANS_X, atTime)));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, MeicamKeyFrame.TRANS_Y, videoFx.getFloatValAtTime(MeicamKeyFrame.TRANS_Y, atTime)));
            data.add(new MeicamFxParam<>(TYPE_FLOAT, MeicamKeyFrame.ROTATION, videoFx.getFloatValAtTime(MeicamKeyFrame.ROTATION, atTime)));

        }
        return data;
    }

    private List<MeicamFxParam<?>> getParam(PlugDetail.Param param) {
        List<MeicamFxParam<?>> result = new ArrayList<>();
        String valueType = param.valueType;
        if (Constants.PlugType.FLOAT.equals(valueType)) {
            float defaultValue = Float.parseFloat(param.valueDefault);
            result.add(new MeicamFxParam<>(TYPE_FLOAT, param.paramName, defaultValue));
        } else if (Constants.PlugType.POSITION2D_X.equals(valueType)) {
            float defaultValue = Float.parseFloat(param.valueDefault);
            MeicamPosition2D position2D = new MeicamPosition2D(defaultValue, 0);
            result.add(new MeicamFxParam<>(MeicamFxParam.TYPE_POSITION_2D, param.paramName, position2D));
        } else if (Constants.PlugType.POSITION2D_Y.equals(valueType)) {
            float defaultValue = Float.parseFloat(param.valueDefault);
            MeicamPosition2D position2D = new MeicamPosition2D(0, defaultValue);
            result.add(new MeicamFxParam<>(MeicamFxParam.TYPE_POSITION_2D, param.paramName, position2D));
        } else if (Constants.PlugType.INT_CHOOSE.equals(valueType)) {
            int defaultValue = Integer.parseInt(param.valueDefault);
            result.add(new MeicamFxParam<>(MeicamFxParam.TYPE_INT, param.paramName, defaultValue));
        } else if (Constants.PlugType.COLOR.equals(valueType)) {
            result.add(new MeicamFxParam<>(MeicamFxParam.TYPE_COLOR, param.paramName, param.valueDefault));
        }
        return result;
    }


    /**
     * 给片段添加蒙版关键帧
     * Add key frame for Mask of clip
     *
     * @param clipInfo  the clip片段
     * @param timestamp the timestamp时间戳
     */
    public MeicamKeyFrame addMaskKeyFrame(MeicamVideoClip clipInfo, long timestamp) {
        if (clipInfo != null && timestamp >= clipInfo.getInPoint() && timestamp <= clipInfo.getOutPoint()) {
            long atTime = timestamp - clipInfo.getInPoint();
            MeicamVideoFx maskTargetFx = getMaskTargetFx(clipInfo);
            if (maskTargetFx == null) {
                return null;
            }

            List<MeicamFxParam<?>> params = new ArrayList<>();

            KeyFrameProcessor<NvsVideoFx> keyFrameHolder = maskTargetFx.keyFrameProcessor();
            List<MeicamFxParam<?>> paramsAtTime = keyFrameHolder.getNearbyKeyFrameParamsAtTime(NvsConstants.KEY_PROPERTY_MASK_REGION_INFO, atTime);
            if (!CommonUtils.isEmpty(paramsAtTime)) {
                for (MeicamFxParam<?> meicamFxParam : paramsAtTime) {
                    String key = meicamFxParam.getKey();
                    Object value = meicamFxParam.getValue();
                    MeicamFxParam<?> fxParam = null;
                    if (NvsConstants.KEY_PROPERTY_MASK_REGION_INFO.equals(key)) {
                        fxParam = new MeicamFxParam<>(MeicamFxParam.TYPE_OBJECT, NvsConstants.KEY_PROPERTY_MASK_REGION_INFO, (MeicamMaskRegionInfo) value);
                    } else if (NvsConstants.KEY_PROPERTY_MASK_FEATHER_WIDTH.equals(key)) {
                        fxParam = new MeicamFxParam<>(TYPE_FLOAT, NvsConstants.KEY_PROPERTY_MASK_FEATHER_WIDTH, (Float) value);
                    } else if (NvsConstants.KEY_PROPERTY_MASK_INVERSE_REGION.equals(key)) {
                        fxParam = new MeicamFxParam<>(TYPE_BOOLEAN, NvsConstants.KEY_PROPERTY_MASK_INVERSE_REGION, (Boolean) value);
                    }
                    if (fxParam != null) {
                        params.add(fxParam);
                    }
                }
            } else {
                Object objectVal = maskTargetFx.getObjectVal(NvsConstants.KEY_PROPERTY_MASK_REGION_INFO);
                if (objectVal instanceof MeicamMaskRegionInfo) {
                    params.add(new MeicamFxParam<>(MeicamFxParam.TYPE_OBJECT, NvsConstants.KEY_PROPERTY_MASK_REGION_INFO, (MeicamMaskRegionInfo) objectVal));
                }
                params.add(new MeicamFxParam<>(TYPE_FLOAT, NvsConstants.KEY_PROPERTY_MASK_FEATHER_WIDTH,
                        maskTargetFx.getFloatVal(NvsConstants.KEY_PROPERTY_MASK_FEATHER_WIDTH, 0)));
                params.add(new MeicamFxParam<>(TYPE_BOOLEAN, NvsConstants.KEY_PROPERTY_MASK_INVERSE_REGION,
                        maskTargetFx.getBooleanVal(NvsConstants.KEY_PROPERTY_MASK_INVERSE_REGION)));
            }
            //params.add(new MeicamFxParam<>(TYPE_BOOLEAN, NvsConstants.KEY_MASK_KEEP_RGB, true));
            MeicamKeyFrame keyFrame = KeyFrameHolderCommand.addKeyFrame(maskTargetFx, params, atTime, 0);
            KeyFrameHolderCommand.cutKeyFrameCurve(maskTargetFx, atTime, NvsConstants.KEY_PROPERTY_MASK_REGION_INFO);
            return keyFrame;
        }
        return null;
    }

    /**
     * 给片段添加不透明度关键帧
     * Add key frame for opacity of clip
     *
     * @param clipInfo  the clip片段
     * @param timestamp the timestamp时间戳
     */
    public MeicamKeyFrame addOpacityFrame(MeicamVideoClip clipInfo, long timestamp) {
        if (clipInfo != null && timestamp >= clipInfo.getInPoint() && timestamp <= clipInfo.getOutPoint()) {
            long atTime = timestamp - clipInfo.getInPoint();
            MeicamVideoFx propertyVideoFx = clipInfo.findPropertyVideoFx();
            if (propertyVideoFx == null) {
                return null;
            }
            KeyFrameProcessor<NvsVideoFx> keyFrameHolder = propertyVideoFx.keyFrameProcessor();
            float opacity = 1F;
            List<MeicamFxParam<?>> paramsAtTime = keyFrameHolder.getNearbyKeyFrameParamsAtTime(NvsConstants.PROPERTY_OPACITY, atTime);
            if (!CommonUtils.isEmpty(paramsAtTime)) {
                for (MeicamFxParam<?> meicamFxParam : paramsAtTime) {
                    if (NvsConstants.PROPERTY_OPACITY.equals(meicamFxParam.getKey())) {
                        opacity = meicamFxParam.getFloatValue();
                        break;
                    }
                }
            }
            if (opacity == 1F) {
                opacity = propertyVideoFx.getFloatVal(NvsConstants.PROPERTY_OPACITY, 1F);
            }
            List<MeicamFxParam<?>> params = new ArrayList<>();
            params.add(new MeicamFxParam<>(TYPE_FLOAT, NvsConstants.PROPERTY_OPACITY, opacity));
            MeicamKeyFrame keyFrame = KeyFrameHolderCommand.addKeyFrame(propertyVideoFx, params, atTime, 0);
            KeyFrameHolderCommand.cutKeyFrameCurve(propertyVideoFx, atTime, NvsConstants.PROPERTY_OPACITY);
            return keyFrame;
        }
        return null;
    }

    /**
     * 检查关键帧
     * check key frame
     *
     * @param clipInfo    the video clip 片段
     * @param offset      the offset 间隔
     * @param fromInPoint true from the video clip in point从入点算，false from the out point从出点算
     */
    public void checkKeyFrame(ClipInfo<?> clipInfo, long offset, boolean fromInPoint) {
        if (clipInfo instanceof MeicamVideoClip) {
            MeicamVideoClip videoClip = (MeicamVideoClip) clipInfo;
            int videoFxCount = videoClip.getVideoFxCount();
            for (int index = 0; index < videoFxCount; index++) {
                MeicamVideoFx videoFx = videoClip.getVideoFx(index);
                if (TYPE_PACKAGE.equals(videoFx.getType())) {
                    continue;
                }
                checkKeyFrame(videoFx, null, offset, fromInPoint);
            }
        } else {
            if (clipInfo instanceof IKeyFrameProcessor<?>) {
                checkKeyFrame((IKeyFrameProcessor<?>) clipInfo, null, offset, fromInPoint);
            }
        }

    }

    /**
     * 检查关键帧
     * check key frame
     *
     * @param clipInfo    the video clip 片段
     * @param offset      the offset 间隔
     * @param fromInPoint true from the video clip in point从入点算，false from the out point从出点算
     */
    public void checkKeyFrame(IKeyFrameProcessor<?> clipInfo, String key, long offset, boolean fromInPoint) {
        if (clipInfo != null) {
            /*业务上长度减少，需要移除被剪裁掉的关键帧，但是因为更改trim底层不会删除关键帧且
            更改trimIn关键帧可能会自动后移，所以这样处理*/
            KeyFrameProcessor<?> keyFrameHolder = clipInfo.keyFrameProcessor();
            Map<Long, MeicamKeyFrame> keyFrameMap = keyFrameHolder.getKeyFrameMap(key);
            for (Map.Entry<Long, MeicamKeyFrame> keyFrameSet : keyFrameMap.entrySet()) {
                MeicamKeyFrame keyFrame = keyFrameSet.getValue();
                if (offset < 0) {
                    if (fromInPoint) {
                        if (keyFrame.getAtTime() < Math.abs(offset)) {
                            /*删除无效关键帧*/
                            keyFrameHolder.removeKeyFrame(keyFrame.getAtTime());
                        }
                    } else {
                        if (keyFrame.getAtTime() > (keyFrameHolder.getOutPoint() - keyFrameHolder.getInPoint())) {
                            /*删除无效关键帧*/
                            keyFrameHolder.removeKeyFrame(keyFrame.getAtTime());
                        }
                    }
                }
            }
            if (!fromInPoint) {
                /*从出点计算的不再移动关键帧*/
                return;
            }
            /*整体移动关键帧*/
            keyFrameHolder.moveAllKeyFrame(offset);
        }
    }

    /**
     * 设置组合字幕的字体
     * Sets compound caption font.
     *
     * @param caption      the caption
     * @param captionIndex the caption index
     * @param fontFamily   fontFamily
     */
    public void setCompoundCaptionFont(MeicamCompoundCaptionClip caption, int captionIndex, String fontFamily) {
        if (caption == null) {
            return;
        }
        caption.setFontFamily(captionIndex, fontFamily);
        seekTimeline(0);
    }


    /**
     * Seek timeline.
     */
    public void seekTimeline() {
        seekTimeline(0);
    }

    /**
     * Seek timeline.
     *
     * @param seekShowMode the seek show mode
     */
    public void seekTimeline(int seekShowMode) {
        if (meicamTimeline != null) {
            seekTimeline(meicamTimeline.getCurrentPosition(), seekShowMode);
        }
    }


    public void playVideo(long startTime, long endTime) {
        if (meicamTimeline != null) {
            meicamTimeline.playBack(getStreamingContext(), startTime, endTime);
        }
    }

    /**
     * 播放以后回滚到播放入点
     * Play roll back video.
     *
     * @param startTime the start time
     * @param endTime   the end time
     */
    public void playVideoRollBack(long startTime, long endTime) {
        if (meicamTimeline != null) {
            meicamTimeline.setAttachment(NvsConstants.KEY_ATTACHMENT_RESET_PLAY_POINT, startTime);
            meicamTimeline.playBack(getStreamingContext(), startTime, endTime);
        }
    }

    /**
     * 循环播放
     * Loop Playback video.
     *
     * @param startTime the start time
     * @param endTime   the end time
     */
    public void playVideoLoop(long startTime, long endTime) {
        if (meicamTimeline != null) {
            meicamTimeline.setAttachment(NvsConstants.KEY_ATTACHMENT_LOOP_PLAY_POINT_START, startTime);
            meicamTimeline.setAttachment(NvsConstants.KEY_ATTACHMENT_LOOP_PLAY_POINT_END, endTime);
            meicamTimeline.playBack(getStreamingContext(), startTime, endTime);
        }
    }

    /**
     * 开关原声
     * Toggle original voice
     *
     * @param trackIndex       the track index 轨道索引
     * @param open             true open打开 ,false close关闭
     * @param changeClipVolume true 影响clip音量，false not 不影响clip音量
     * @return true success 成功，false failed 失败
     */
    public boolean toggleOriginalVoice(int trackIndex, boolean open, boolean changeClipVolume) {
        if (meicamTimeline == null) {
            return false;
        }
        MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(trackIndex);
        if (videoTrack != null) {
            return videoTrack.setIsMute(!open, changeClipVolume);
        }
        return false;
    }


    /**
     * Apply theme.
     *
     * @param packageId the package id
     */
    public void applyTheme(String packageId) {
        if (meicamTimeline == null) {
            return;
        }
        //主题 theme
        boolean isSuccess = meicamTimeline.applyTheme(packageId);
        if (isSuccess) {
            /*
             * 新需求 添加主题删除所有增加的音频轨道
             * New requirements add theme remove all added audio tracks
             * */
            int audioTrackCount = meicamTimeline.getAudioTrackCount();
            if (audioTrackCount > 0) {
                for (int index = audioTrackCount - 1; index >= 0; index--) {
                    meicamTimeline.removeAudioTrack(index);
                }
            }
        }
        playVideo(0, meicamTimeline.getDuration());
        if (mOnTimelineChangeListener != null) {
            mOnTimelineChangeListener.onTimelineChanged(meicamTimeline, false);
            mOnTimelineChangeListener.onSaveOperation();
        }
    }


    /**
     * Change caption text.
     *
     * @param captionClip the caption clip
     * @param content     the content
     */
    public void changeCaptionText(MeicamCaptionClip captionClip, String content) {
        if (captionClip == null) {
            addCaption(content);
        } else {
            updateCaption(captionClip, content);
        }
    }

    private MeicamCaptionClip addCaption(String text) {
        long inPoint = getCurrentTimelinePosition();

        /*
         * 对片头主题 进行特殊处理，字幕不能加在片头主题片段上
         * Special treatment to the opening theme, subtitle can not be added to the opening theme segment
         * */
        long titleThemeDuration = meicamTimeline.getTitleThemeDuration();
        if (titleThemeDuration > 0) {
            if (inPoint < titleThemeDuration) {
                inPoint = titleThemeDuration;
            }
        }
        long captionDuration = 4 * Constants.NS_TIME_BASE;
        long outPoint = inPoint + captionDuration;
        return addCaption(text, inPoint, outPoint, true, 0, false);
    }

    public int getAITrackIndex() {
        if (meicamTimeline == null) {
            return -1;
        }
        for (int i = 0; i < meicamTimeline.getStickerCaptionTrackCount(); i++) {
            MeicamStickerCaptionTrack meicamStickerCaptionTrack = meicamTimeline.findStickCaptionTrack(i);
            if (meicamStickerCaptionTrack == null) {
                continue;
            }
            int nowTrackIndex = meicamStickerCaptionTrack.getIndex();
            if (meicamStickerCaptionTrack.getClipCount() == 0) {
                return nowTrackIndex;
            }
        }
        return meicamTimeline.getStickerCaptionTrackCount();
    }

    public MeicamCaptionClip addCaption(String text, long inPoint, long outPoint, boolean showNow, int operateType, boolean isNormal) {
        return addCaption(text, inPoint, outPoint, getTrackIndex(inPoint, outPoint), showNow, operateType, isNormal);
    }

    public MeicamCaptionClip addCaption(String text, long inPoint, long outPoint, int trackIndex, boolean showNow, int operateType, boolean isNormal) {
        if (meicamTimeline == null) {
            LogUtils.e("The timeline is null!");
            return null;
        }
        MeicamStickerCaptionTrack meicamStickerCaptionTrack = TimelineCommand.addStickCaptionTrack(meicamTimeline, trackIndex);
        if (meicamStickerCaptionTrack == null) {
            return null;
        }
        MeicamCaptionClip meicamCaptionClip;
        if (isNormal) {
            meicamCaptionClip = CaptionStickerTrackCommand.addNormalCaption(meicamStickerCaptionTrack, text, inPoint, outPoint);
        } else {
            meicamCaptionClip = CaptionStickerTrackCommand.addModularCaption(meicamStickerCaptionTrack, text, inPoint, outPoint);
        }
        if (meicamCaptionClip == null) {
            return null;
        }
        CaptionCommand.setParam(meicamCaptionClip, CaptionCommand.PARAM_OPERATION_TYPE, operateType);
        if (showNow) {
            if (mOnTimelineChangeListener != null) {
                mOnTimelineChangeListener.onAddStickerCaptionPicFx(meicamCaptionClip, CommonData.TYPE_CAPTION);
            }
            seekTimeline(inPoint, 0);
        }
        return meicamCaptionClip;
    }

    public MeicamCaptionClip addCaption(MeicamCaptionClip captionClip, long inPoint, long outPoint, boolean addNvsObject, boolean showNow) {
        int trackIndex = getTrackIndex(inPoint, outPoint);
        MeicamStickerCaptionTrack meicamStickerCaptionTrack = meicamTimeline.addStickCaptionTrack(trackIndex);
        if (meicamStickerCaptionTrack == null) {
            return null;
        }
        MeicamCaptionClip meicamCaptionClip = (MeicamCaptionClip) CaptionStickerTrackCommand.addClip(meicamStickerCaptionTrack, captionClip, addNvsObject, true);;
        if (meicamCaptionClip == null) {
            return null;
        }
        if (meicamCaptionClip.getOperationType() == TYPE_AI_CAPTION) {
            changeAiCaptionPositionAndSize(meicamCaptionClip, 1);
        }
        if (showNow) {
            if (mOnTimelineChangeListener != null) {
                mOnTimelineChangeListener.onAddStickerCaptionPicFx(meicamCaptionClip, CommonData.TYPE_CAPTION);
            }
            seekTimeline(inPoint, 0);
        }
        return meicamCaptionClip;
    }


    private void updateCaption(MeicamCaptionClip caption, String content) {
        if (caption == null || meicamTimeline == null) {
            return;
        }
        if (meicamTimeline.isAddTitleTheme() && caption.getThemeType() == NvsTimelineCaption.ROLE_IN_THEME_TITLE) {
            MeicamTheme meicamTheme = meicamTimeline.getMeicamTheme();
            if (meicamTheme != null) {
                meicamTheme.setThemeTitleText(content);
            }
        }
        CaptionCommand.setParam(caption, CaptionCommand.PARAM_TEXT, content);
        if (mOnTimelineChangeListener != null) {
            mOnTimelineChangeListener.onAddStickerCaptionPicFx(caption, CommonData.TYPE_CAPTION);
        }
        seekTimeline();
    }


    /**
     * 删除字幕
     * Remove caption
     *
     * @param caption 字幕 the caption
     */
    public void removeCaption(MeicamCaptionClip caption) {
        if (caption == null || meicamTimeline == null) {
            return;
        }
        int trackIndex = caption.getTrackIndex();
        MeicamStickerCaptionTrack meicamStickerCaptionTrack = meicamTimeline.findStickCaptionTrack(trackIndex);
        if (meicamStickerCaptionTrack == null) {
            return;
        }
        boolean isRemoveSuccess = CaptionStickerTrackCommand.removeClip(meicamStickerCaptionTrack, caption.getInPoint());
        if (isRemoveSuccess && meicamStickerCaptionTrack.getClipCount() == 0) {
            TimelineCommand.removeStickCaptionTrack(meicamTimeline, trackIndex);
        }
        seekTimeline();
    }

    /**
     * 添加组合字幕，如果有旧的，则替换
     * Add or replace compound caption
     *
     * @param clipInfo Clip data 素材数据
     * @param uuid     Uuid of compound caption 复合标题的Uuid
     */
    @Override
    public void addCompoundCaption(ClipInfo<?> clipInfo, String uuid) {
        if (meicamTimeline == null) {
            LogUtils.e("timeline is null");
            return;
        }

        long inPoint = getCurrentTimelinePosition();
        /*
         * 对片头主题 进行特殊处理，组合字幕不能加在片头主题片段上
         * Special treatment is given to the opening theme. Combined subtitles cannot be added to the opening theme fragments
         * */
        long titleThemeDuration = meicamTimeline.getTitleThemeDuration();
        if (titleThemeDuration > 0) {
            if (inPoint < titleThemeDuration) {
                inPoint = titleThemeDuration;
            }
        }
        long captionDuration = 4 * Constants.NS_TIME_BASE;
        long outPoint = inPoint + captionDuration;
        /*
         * 删除当前使用的组合字幕
         * Deletes the combined subtitle currently in use
         * */
        if (clipInfo instanceof MeicamCompoundCaptionClip) {
            removeCaptionStickClip(clipInfo, false);
        }
        int trackIndex = getTrackIndex(inPoint, outPoint);
        MeicamStickerCaptionTrack meicamStickerCaptionTrack = meicamTimeline.addStickCaptionTrack(trackIndex);
        if (meicamStickerCaptionTrack == null) {
            return;
        }
        MeicamCompoundCaptionClip compoundCaptionClip = CaptionStickerTrackCommand.addCompoundCaption(meicamStickerCaptionTrack, inPoint, outPoint, uuid);
        if (compoundCaptionClip == null) {
            return;
        }
        seekTimeline(inPoint, 0);
        if (mOnTimelineChangeListener != null) {
            mOnTimelineChangeListener.onAddStickerCaptionPicFx(compoundCaptionClip, CommonData.TYPE_COMPOUND_CAPTION);
        }
    }

    @Override
    public void restoreTimeline(String operateData, MeicamTimeline timeline) {
        if (timeline == null) {
            LogUtils.e("timeline is null!");
            return;
        }
        MeicamTimeline meicamTimeline = GsonContext.getInstance().fromJson(operateData, MeicamTimeline.class);
        this.meicamTimeline = new MeicamTimeline.TimelineBuilder(getStreamingContext(),
                MeicamTimeline.TimelineBuilder.BUILD_FORM_TIMELINE)
                .setTimelineData(meicamTimeline)
                .setEditTimeline(timeline)
                .build();
        seekTimeline(0);
    }


    @Override
    public void updateBackground(int type) {
    }


    @Override
    public void deleteBackground(MeicamVideoClip editVideoClip, boolean isDelete) {
        if (editVideoClip == null) {
            return;
        }
        if (isDelete) {
            VideoFxCommand.deleteBackground(editVideoClip.findPropertyVideoFx());
        }
        editVideoClip.removeOldBackground();
        seekTimeline(0);
        if (mOnTimelineChangeListener != null) {
            mOnTimelineChangeListener.onTimelineChanged(meicamTimeline, false);
        }
    }

    @Override
    public void updateBlurAndColorBackground(MeicamVideoClip editVideoClip, String value, int type) {
        if (editVideoClip == null) {
            return;
        }
        MeicamVideoFx videoFx = editVideoClip.findPropertyVideoFx();
        if (videoFx == null) {
            return;
        }
        VideoFxCommand.setBooleanVal(videoFx, NvsConstants.KEY_BACKGROUND_ROTATION, false);
//        nvsVideoFx.setBooleanVal(NvsConstants.KEY_BACKGROUND_MUTLISAMPLE, true);
        deleteBackground(editVideoClip, false);
        if (type == CommonData.STORYBOARD_BACKGROUND_TYPE_COLOR) {
            VideoFxCommand.setMenuVal(videoFx, NvsConstants.KEY_BACKGROUND_MODE, NvsConstants.VALUE_COLOR_BACKGROUND_MODE);
            VideoFxCommand.setColorVal(videoFx, NvsConstants.KEY_BACKGROUND_COLOR, value);
        } else if (type == CommonData.STORYBOARD_BACKGROUND_TYPE_IMAGE) {
            String filePath;
            if (FileUtils.isFileExists(value)) {
                filePath = value;
            } else {
                filePath = "assets:/background/image" + File.separator + value;
            }
            // videoFx.setMenuVal(NvsConstants.KEY_BACKGROUND_MODE, NvsConstants.VALUE_IMAGE_BACKGROUND_MODE);
            VideoFxCommand.setMenuVal(videoFx, NvsConstants.KEY_BACKGROUND_MODE, NvsConstants.VALUE_IMAGE_BACKGROUND_MODE);
            VideoFxCommand.setStringVal(videoFx, NvsConstants.KEY_BACKGROUND_IMAGE_PATH, filePath);
        } else {
            float strength = Float.parseFloat(value);
            if (strength == 0) {
                deleteBackground(editVideoClip, true);
            } else {
                VideoFxCommand.setMenuVal(videoFx, NvsConstants.KEY_BACKGROUND_MODE, NvsConstants.VALUE_BLUR_BACKGROUND_MODE);
                VideoFxCommand.setFloatVal(videoFx, NvsConstants.KEY_BACKGROUND_BLUR_RADIUS, strength);
            }
        }
        seekTimeline(0);
    }

    @Override
    public void applyAllBlurBackground(String value, int type) {
        if (meicamTimeline == null) {
            return;
        }
        MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(TRACK_INDEX_MAIN);
        for (int index = 0; index < videoTrack.getClipCount(); index++) {
            updateBlurAndColorBackground(videoTrack.getVideoClip(index), value, type);
        }
        seekTimeline(0);
    }

    @Override
    public void applyAllImageBackground(String imagePath, int type) {
        if (meicamTimeline == null) {
            return;
        }
        MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(TRACK_INDEX_MAIN);
        for (int index = 0; index < videoTrack.getClipCount(); index++) {
            updateBlurAndColorBackground(videoTrack.getVideoClip(index), imagePath, type);
        }
        seekTimeline(0);
    }

    @Override
    public void refreshData(int trackIndex, String type) {
        if (meicamTimeline != null) {
            meicamTimeline.updateTrackData(trackIndex, type);
        }
    }

    @Override
    public int audioEditCutClip(Context context) {
        if (mMeicamAudioTrack == null || mMeicamAudioClip == null || meicamTimeline == null) {
            return ReturnCode.CODE_PARAM_ERROR;
        }
        long currentPosition = meicamTimeline.getCurrentPosition();
        boolean success = AudioTrackCommand.splitClip(mMeicamAudioTrack, mMeicamAudioClip.getIndex(), currentPosition);
        if (success) {
            mOnTrackChangeListener.audioEditCutClip(mMeicamAudioTrack, currentPosition);
            return ReturnCode.CODE_OK;
        } else {
            return ReturnCode.CODE_CAN_NOT_OPERATE;
        }
    }

    @Override
    public void audioEditChangeClipSpeed(float speed, boolean isChangeVoice) {
        if (mMeicamAudioClip == null) {
            return;
        }
        AudioCommand.setSpeed(mMeicamAudioClip, speed, isChangeVoice);
        MeicamAudioTrack audioTrack = meicamTimeline.getAudioTrack(mMeicamAudioClip.getTrackIndex());
        if (audioTrack != null) {
            for (int i = audioTrack.getIndex(); i < audioTrack.getClipCount(); i++) {
                MeicamAudioClip audioClip = audioTrack.getAudioClip(i);
                AudioCommand.updateInAndOutPoint(audioClip);
            }
        }
    }

    @Override
    public void audioEditChangeVolume(float volume) {
        if (mMeicamAudioClip == null) {
            return;
        }
        AudioCommand.setVolume(mMeicamAudioClip, volume);
    }

    @Override
    public void audioEditDeleteClip(MeicamAudioClip audioClip) {
        if (audioClip == null) {
            LogUtils.e("audio clip is null");
            return;
        }
        boolean success = EditorEngine.getInstance().deleteAudioClip(audioClip);
        if (mOnTrackChangeListener != null) {
            mOnTrackChangeListener.audioEditDeleteClip(success);
        }
    }

    @Override
    public void audioEditCopyClip(Context context) {
        if (mMeicamAudioTrack == null || mMeicamAudioClip == null || meicamTimeline == null) {
            return;
        }
        long insertPoint = mMeicamAudioClip.getOutPoint();
        long audioLength = insertPoint - mMeicamAudioClip.getInPoint();
        MeicamAudioTrack insertTrack = mMeicamAudioTrack;

        int audioTrackCount = meicamTimeline.getAudioTrackCount();
        if (audioTrackCount == 1) {//已经有一个轨道

            int index = mMeicamAudioClip.getIndex();
            if (index < insertTrack.getClipCount()) {
                MeicamAudioClip currNvsAudioClip = mMeicamAudioClip;
                MeicamAudioClip nextNvsAudioClip = insertTrack.getAudioClip(index + 1);
                MeicamAudioClip lastNvsAudioClip = insertTrack.getAudioClip(insertTrack.getClipCount() - 1);
                if (nextNvsAudioClip == null) {
                    insertPoint = lastNvsAudioClip.getOutPoint();
                } else {
                    long during = nextNvsAudioClip.getInPoint() - currNvsAudioClip.getOutPoint();
                    insertPoint = currNvsAudioClip.getOutPoint();
                    if (during < audioLength) {
                        insertTrack = TimelineCommand.appendAudioTrack(meicamTimeline);
                    }
                }
                if (insertTrack != null) {
                    MeicamAudioClip newAudioClip = AudioTrackCommand.copyClip(insertTrack, currNvsAudioClip, currNvsAudioClip.getOutPoint());
                    mOnTrackChangeListener.audioEditCopyClip(insertPoint, newAudioClip, insertTrack);
                }
            }
        } else {
            if (mMeicamAudioClip.getIndex() == mMeicamAudioTrack.getClipCount() - 1) {
                //最后一个clip
                //选择插入轨道 Select track for inserting.
                int index = selectAddTrackIndex();
                insertTrack = meicamTimeline.getAudioTrack(index);
                if (insertTrack.getClipCount() > 1) {
                    MeicamAudioClip lastClip = insertTrack.getAudioClip(insertTrack.getClipCount() - 1);
                    insertPoint = lastClip.getOutPoint();
                }
                MeicamAudioClip copyClip = AudioTrackCommand.copyClip(insertTrack, mMeicamAudioClip, insertPoint);
                mOnTrackChangeListener.audioEditCopyClip(insertPoint, copyClip, insertTrack);
            } else {
                /*
                * 不是最后一个音乐片段,遍历所有的音乐轨道，
                 以当前选中的outPoint为下一个的inPoint，
                 放的下就放在该轨道上，放不下就新建轨道
                 并且只会比较下面的轨道，不往上复制
                * Not the last piece of music, going through all the music tracks,
                 With the currently selected outPoint as the next inPoint,
                 If you put it down, you put it on that track. If you can't put it down, you build a new track
                 And it will only compare the orbitals below, not copy them up
                * */
                if (audioTrackCount - 1 == mMeicamAudioTrack.getIndex()) {
                    /*
                    * 选中的是最后一个音乐轨道
                       首先考虑当前音乐轨道
                    * The final track is selected
                       First consider the current music track
                    * */
                    if (insertPoint + audioLength
                            <= mMeicamAudioTrack.getAudioClip(mMeicamAudioClip.getIndex() + 1).getInPoint()) {
                        insertTrack = mMeicamAudioTrack;
                    } else {
                        insertTrack = TimelineCommand.appendAudioTrack(meicamTimeline);
                    }
                    MeicamAudioClip copyClip = AudioTrackCommand.copyClip(mMeicamAudioTrack, mMeicamAudioClip, insertPoint);
                    mOnTrackChangeListener.audioEditCopyClip(insertPoint, copyClip, insertTrack);
                } else {
                    /*
                     * 选中的不是最后一个音乐轨道
                     * The selected track is not the last one
                     * */
                    for (int i = mMeicamAudioTrack.getIndex(); i < meicamTimeline.getAudioTrackCount(); i++) {
                        /*
                         * 首先考虑当前音乐轨道
                         * First consider the current music track
                         * */
                        if (insertPoint + audioLength <=
                                mMeicamAudioTrack.getAudioClip(mMeicamAudioClip.getIndex() + 1).getInPoint()) {
                            MeicamAudioClip copyClip = AudioTrackCommand.copyClip(mMeicamAudioTrack, mMeicamAudioClip, insertPoint);
                            mOnTrackChangeListener.audioEditCopyClip(insertPoint, copyClip, mMeicamAudioTrack);
                            return;
                        }

                        /*
                         * 下一个音乐轨道
                         * Next music track
                         * */
                        MeicamAudioTrack nvsAudioTrack = meicamTimeline.getAudioTrack(i + 1);

                        /*
                         * 下面已经没有音乐轨道了
                         * There are no music tracks down there
                         * */
                        if (nvsAudioTrack == null) {
                            MeicamAudioTrack newNvsAudioTrack = TimelineCommand.appendAudioTrack(meicamTimeline);
                            if (newNvsAudioTrack != null) {
                                MeicamAudioClip copyClip = AudioTrackCommand.copyClip(newNvsAudioTrack, mMeicamAudioClip, insertPoint);
                                mOnTrackChangeListener.audioEditCopyClip(insertPoint, copyClip, newNvsAudioTrack);
                            }
                            return;
                        }

                        if (nvsAudioTrack.getClipCount() == 0) {
                            MeicamAudioClip copyClip = AudioTrackCommand.copyClip(nvsAudioTrack, mMeicamAudioClip, insertPoint);
                            mOnTrackChangeListener.audioEditCopyClip(insertPoint, copyClip, nvsAudioTrack);
                            return;
                        }
                        /*
                         * 当前选中的片段后面，下一个轨道已经没有片段了、
                         * After the currently selected fragment, the next orbital has no fragment left
                         * */
                        MeicamAudioClip lastNvsClip = nvsAudioTrack.getAudioClip(nvsAudioTrack.getClipCount() - 1);
                        if (lastNvsClip.getOutPoint() <= insertPoint) {
                            MeicamAudioClip copyClip = AudioTrackCommand.copyClip(nvsAudioTrack, mMeicamAudioClip, insertPoint);
                            mOnTrackChangeListener.audioEditCopyClip(insertPoint, copyClip, nvsAudioTrack);
                            return;
                        }

                        MeicamAudioClip nvsAudioClipPosition = nvsAudioTrack.getAudioClip(insertPoint);
                        /*
                         * 选中的音乐片段outPoint时间节点没有音乐片段
                         * The selected music fragment outPoint time node has no music fragment
                         * */
                        if (nvsAudioClipPosition == null) {
                            for (int j = 0; j < nvsAudioTrack.getClipCount(); j++) {

                                MeicamAudioClip nvsAudioClip = nvsAudioTrack.getAudioClip(j);
                                if (insertPoint < nvsAudioClip.getInPoint()) {
                                    /*
                                     * 当前时间节点和下一个节点之间空间是否足够
                                     * Is there enough space between the current time node and the next node
                                     * */
                                    if (insertPoint + audioLength <= nvsAudioClip.getInPoint()) {
                                        MeicamAudioClip copyClip = AudioTrackCommand.copyClip(nvsAudioTrack, mMeicamAudioClip, insertPoint);
                                        mOnTrackChangeListener.audioEditCopyClip(insertPoint, copyClip, nvsAudioTrack);
                                        return;
                                    }
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private int selectAddTrackIndex() {
        int index = 0;
        long tempOutPoint = 0L;
        if (mMeicamAudioTrack != null) {
            for (int i = 0; i <= mMeicamAudioTrack.getIndex(); i++) {
                MeicamAudioTrack audioTrack = meicamTimeline.getAudioTrack(i);
                if (audioTrack == null) {
                    continue;
                }
                if (audioTrack.getClipCount() == 0) {
                    return audioTrack.getIndex();
                }
                if (audioTrack.getIndex() == 0) {
                    tempOutPoint = audioTrack.getAudioClip(audioTrack.getClipCount() - 1).getOutPoint();
                }
                long lastNvsAudioClipOutPoint = audioTrack.getAudioClip(audioTrack.getClipCount() - 1).getOutPoint();
                if (lastNvsAudioClipOutPoint < tempOutPoint) {
                    index = i;
                }
                tempOutPoint = lastNvsAudioClipOutPoint;
            }
        }
        return index;
    }

    /**
     * 获取时间线特效片段
     * Get timeline video fx clip
     *
     * @param trackIndex the track index轨道索引
     * @param inPoint    the int point 入点
     */
    public MeicamTimelineVideoFxClip getTimelineVideoFxClip(int trackIndex, long inPoint) {
        if (meicamTimeline != null) {
            MeicamTimelineVideoFxTrack videoFxTrack = meicamTimeline.getTimelineFxTrack(trackIndex);
            if (videoFxTrack != null) {
                for (int i = 0; i < videoFxTrack.getClipCount(); i++) {
                    MeicamTimelineVideoFxClip videoFxClip = videoFxTrack.getClip(i);
                    if (videoFxClip.getInPoint() == inPoint) {
                        return videoFxClip;
                    }
                }
            }

        }
        return null;
    }


    /**
     * 获取时间线滤镜调节片段
     * Get timeline filter adjust clip
     *
     * @param trackIndex the track index轨道索引
     * @param inPoint    the int point 入点
     */
    public MeicamTimelineVideoFilterAndAdjustClip getTimelineFilterAndAdjustClip(int trackIndex, long inPoint) {
        if (meicamTimeline != null) {
            MeicamTimelineVideoFxTrack videoFxTrack = meicamTimeline.getFilterAndAdjustTimelineTrack(trackIndex);
            if (videoFxTrack != null) {
                for (int i = 0; i < videoFxTrack.getFilterAndAdjustCount(); i++) {
                    MeicamTimelineVideoFilterAndAdjustClip videoFxClip = videoFxTrack.getFilterAndAdjustClip(i);
                    if (videoFxClip.getInPoint() == inPoint) {
                        return videoFxClip;
                    }
                }
            }

        }
        return null;
    }


    @Override
    public void videoEditChangeVoice(MeicamVideoClip videoClip, String fxId) {
        if (videoClip == null) {
            return;
        }
        if (TextUtils.isEmpty(fxId)) {
            int audioFxCount = videoClip.getAudioFxCount();
            if (audioFxCount > 0) {
                for (int index = audioFxCount - 1; index >= 0; index--) {
                    VideoClipCommand.removeAudioFx(videoClip, index);
                }
            }
            return;
        }
        VideoClipCommand.appendAudioFx(videoClip, fxId);
    }

    @Override
    public void audioEditChangeVoice(String fxId) {
        if (mMeicamAudioClip == null) {
            return;
        }
        if (TextUtils.isEmpty(fxId)) {
            mMeicamAudioClip.removeAllAudioFx();
            return;
        }
        AudioCommand.appendAudioFx(mMeicamAudioClip, fxId);
    }

    @Override
    public void audioEditTransition(long fadeIn, long fadeOut) {
        if (mMeicamAudioClip != null) {
            AudioCommand.setFadeInDuration(mMeicamAudioClip, fadeIn);
            AudioCommand.setFadeOutDuration(mMeicamAudioClip, fadeOut);
        }
    }

    @Override
    public boolean deleteEffect(IClip trackData) {
        if (meicamTimeline == null) {
            LogUtils.e("timeline is null!");
            return false;
        }
        if (trackData == null) {
            LogUtils.e("trackData is null!");
            return false;
        }
        MeicamTimelineVideoFxTrack effectTrack = findEffectTrack(trackData);
        if (effectTrack != null) {
            MeicamTimelineVideoFxClip videoFxClip = TimelineVideoFxTrackCommand.removeClip(effectTrack, trackData.getInPoint());
            TimelineFxBinder.removeTargetVideoFx(videoFxClip);
            //删除尾部的空轨道
            //Delete the empty track at the end.
            removeEmptyTimelineFxTrackInTheEnd();
            seekTimeline(0);
            if (mOnTimelineChangeListener != null) {
                mOnTimelineChangeListener.onTimelineChanged(meicamTimeline, true);
            }
            return true;
        } else {
            LogUtils.e("deleteEffect: effectInTrack is null！");
            return false;
        }
    }

    public boolean deleteClipEffect(MeicamVideoClip meicamVideoClip, MeicamVideoFx meicamVideoFx) {
        VideoClipCommand.removeFx(meicamVideoClip, meicamVideoFx);
        if (mOnTimelineChangeListener != null) {
            mOnTimelineChangeListener.onTimelineChanged(meicamTimeline, true);
        }
        return true;
    }

    public boolean copyClipEffect(MeicamVideoClip meicamVideoClip, MeicamVideoFx meicamVideoFx) {
        MeicamVideoFx result = VideoClipCommand.insertVideoFxFromFx(meicamVideoClip, meicamVideoFx.clone(), meicamVideoFx.getIndex() + 1, false);
        if (result != null) {
            if (mOnTimelineChangeListener != null) {
                mOnTimelineChangeListener.onTimelineChanged(meicamTimeline, true);
            }
            return true;
        }
        return false;
    }

    @Override
    public int copyEffect(IClip trackData, Context context) {
        if (trackData == null) {
            return ReturnCode.CODE_PARAM_ERROR;
        }
        MeicamTimelineVideoFxTrack effectTrack = findEffectTrack(trackData);
        MeicamTimelineVideoFxClip videoFxClip = findEffectInTrack(effectTrack, trackData.getInPoint());
        if (effectTrack == null || videoFxClip == null) {
            return ReturnCode.CODE_OTHER;
        }

        long inPoint = videoFxClip.getOutPoint();
        int trackIndex = getTrackIndexByTimelineEffectCopy(videoFxClip);
        long duration = videoFxClip.getOutPoint() - videoFxClip.getInPoint();
        long effectDuration = getEffectDuration(effectTrack, inPoint, duration);
        if (effectDuration < duration) {
            return ReturnCode.CODE_CAN_NOT_OPERATE;
        }
        if (trackIndex != effectTrack.getIndex()) {
            effectTrack = TimelineCommand.addTimelineFxTrack(meicamTimeline, trackIndex);
        }
        if (effectTrack == null) {
            return ReturnCode.CODE_OTHER;
        }
        String oldCreateTag = videoFxClip.getExtraTag();
        videoFxClip = TimelineVideoFxTrackCommand.addFxClip(effectTrack, videoFxClip, true, inPoint, effectDuration, videoFxClip.getDesc(), true);
        if (videoFxClip != null) {
            TimelineFxBinder.bindToTarget(videoFxClip, oldCreateTag);
            seekTimeline(0);
            if (mOnTimelineChangeListener != null) {
                mOnTimelineChangeListener.onTimelineChanged(meicamTimeline, true);
                mOnTimelineChangeListener.onNeedTrackSelectChanged(videoFxClip.getTrackIndex(), videoFxClip.getInPoint());
            }
            return ReturnCode.CODE_OK;
        }
        return ReturnCode.CODE_OTHER;
    }

    @Override
    public void changeClipFilter(MeicamVideoClip videoClip, float intensity) {
        if (videoClip == null) {
            return;
        }
        MeicamVideoFx videoFx = videoClip.getVideoFxByType(MeicamVideoFx.SubType.SUB_TYPE_CLIP_FILTER);
        if (videoFx != null) {
            VideoFxCommand.setIntensity(videoFx, intensity);
            seekTimeline(0);
        }
    }

    public void changeTimelineFilter(MeicamTimelineVideoFxClip filterFx, float intensity) {
        if (filterFx != null) {
            TimelineFxCommand.setIntensity(filterFx,intensity);
            seekTimeline(0);
        }
    }

    @Override
    public void removeClipFilter(MeicamVideoClip editVideoClip) {
        if (editVideoClip == null) {
            return;
        }
        MeicamVideoFx videoFx = editVideoClip.getVideoFxByType(MeicamVideoFx.SubType.SUB_TYPE_CLIP_FILTER);
        VideoClipCommand.removeFx(editVideoClip, videoFx);
        seekTimeline(0);
        if (mOnTimelineChangeListener != null) {
            mOnTimelineChangeListener.onTimelineChanged(meicamTimeline, true);
        }
    }


    @Override
    public void applyAllFilter(MeicamVideoClip oldClip) {
        if (meicamTimeline == null) {
            return;
        }
        MeicamVideoFx filterFx = oldClip.getVideoFxByType(MeicamVideoFx.SubType.SUB_TYPE_CLIP_FILTER);
        int count = meicamTimeline.videoTrackCount();
        if (count > 0) {
            for (int index = 0; index < count; index++) {
                MeicamVideoTrack meicamVideoTrack = meicamTimeline.getVideoTrack(index);
                int clipCount = meicamVideoTrack.getClipCount();
                if (clipCount <= 0) {
                    continue;
                }
                for (int i = 0; i < clipCount; i++) {
                    MeicamVideoClip appliedClip = meicamVideoTrack.getVideoClip(i);
                    if (appliedClip == oldClip) {
                        continue;
                    }
                    if (appliedClip != null) {
                        if (filterFx != null) {
                            VideoClipCommand.appendFilter(appliedClip, filterFx.clone(), false);
                        } else {
                            MeicamVideoFx videoFx = appliedClip.getVideoFxByType(MeicamVideoFx.SubType.SUB_TYPE_CLIP_FILTER);
                            if (videoFx != null) {
                                VideoClipCommand.removeFx(appliedClip, videoFx);
                            }
                        }
                    }
                }
            }
        }
        seekTimeline(0);
        if (mOnTimelineChangeListener != null) {
            mOnTimelineChangeListener.onTimelineChanged(meicamTimeline, true);
        }
    }


    /**
     * 获取clip的动画
     * Gets clip animation.
     *
     * @param videoClip the video clip
     * @return the clip animation
     */
    public AnimationData getVideoClipAnimation(MeicamVideoClip videoClip) {
        AnimationData animationData = new AnimationData();
        MeicamVideoFx videoFx = videoClip.findPropertyVideoFx();
        if (videoFx == null) {
            return animationData;
        }
        String packageID, postPackageID;
        packageID = videoFx.getStringVal(NvsConstants.PACKAGE_ID);
        if (TextUtils.isEmpty(packageID)) {
            postPackageID = videoFx.getStringVal(NvsConstants.POST_PACKAGE_ID);
            if (!TextUtils.isEmpty(postPackageID)) {
                animationData.setPackageID(postPackageID);
                animationData.setInPoint((long) videoFx.getFloatVal(NvsConstants.PACKAGE_EFFECT_IN));
                animationData.setOutPoint((long) videoFx.getFloatVal(NvsConstants.PACKAGE_EFFECT_OUT));
                animationData.setIsAnimationIn(videoFx.getBooleanVal(NvsConstants.PACKAGE_TYPE_ANIMATION_IN));
            }
        } else {
            animationData.setPackageID(packageID);
            animationData.setInPoint((long) videoFx.getFloatVal(NvsConstants.PACKAGE_EFFECT_IN));
            animationData.setOutPoint((long) videoFx.getFloatVal(NvsConstants.PACKAGE_EFFECT_OUT));
            animationData.setIsAnimationIn(videoFx.getBooleanVal(NvsConstants.PACKAGE_TYPE_ANIMATION_IN));
        }
        String package2ID, postPackage2ID;
        package2ID = videoFx.getStringVal(NvsConstants.PACKAGE2_ID);
        if (TextUtils.isEmpty(package2ID)) {
            postPackage2ID = videoFx.getStringVal(NvsConstants.POST_PACKAGE2_ID);
            if (!TextUtils.isEmpty(postPackage2ID)) {
                animationData.setPackageID2(postPackage2ID);
                animationData.setInPoint2((long) videoFx.getFloatVal(NvsConstants.PACKAGE2_EFFECT_IN));
                animationData.setOutPoint2((long) videoFx.getFloatVal(NvsConstants.PACKAGE2_EFFECT_OUT));
            }
        } else {
            animationData.setPackageID2(package2ID);
            animationData.setInPoint2((long) videoFx.getFloatVal(NvsConstants.PACKAGE2_EFFECT_IN));
            animationData.setOutPoint2((long) videoFx.getFloatVal(NvsConstants.PACKAGE2_EFFECT_OUT));
        }
        return animationData;
    }

    /**
     * 添加视频动画
     * Add video animation.
     *
     * @param videoClip the video clip
     * @param assetInfo the asset info
     */
    public void addVideoAnimation(MeicamVideoClip videoClip, AssetInfo assetInfo, boolean needPlay) {

        MeicamVideoFx videoFx = VideoClipCommand.findPropertyVideoFx(videoClip);
        if (videoFx == null) {
            return;
        }
        AnimationData animationData = EditorEngine.getInstance().getVideoClipAnimation(videoClip);
        if (animationData == null) {
            return;
        }
        int type = assetInfo.getType();
        VideoFxCommand.setBooleanVal(videoFx, NvsConstants.IS_POST_STORY_BOARD_3D, false, true);
        if (type == ASSET_ANIMATION_IN) {
            long duration = CommonData.VIDEO_ANIMATION_IN_DEFAULT_DURATION;
            if (animationData.getIsAnimationIn() && (!TextUtils.isEmpty(animationData.getPackageID()))) {
                duration = animationData.getOutPoint() - animationData.getInPoint();
            }
            if (duration == 0) {
                duration = CommonData.VIDEO_ANIMATION_IN_DEFAULT_DURATION;
            }
            VideoFxCommand.setStringVal(videoFx, NvsConstants.PACKAGE_ID, "", true);
            VideoFxCommand.setStringVal(videoFx, NvsConstants.POST_PACKAGE_ID, "", true);

            //3d动画使用package id，不是3d使用 post package id
            if (assetInfo.isPostPackage() == POST_PACKAGE) {
                VideoFxCommand.setStringVal(videoFx, NvsConstants.POST_PACKAGE_ID, assetInfo.getPackageId(), true);
            } else {
                VideoFxCommand.setStringVal(videoFx, NvsConstants.PACKAGE_ID, assetInfo.getPackageId(), true);
            }
            VideoFxCommand.setFloatVal(videoFx, NvsConstants.PACKAGE_EFFECT_IN, 0, true);
            VideoFxCommand.setFloatVal(videoFx, NvsConstants.PACKAGE_EFFECT_OUT, duration, true);
            VideoFxCommand.setBooleanVal(videoFx, NvsConstants.PACKAGE_TYPE_ANIMATION_IN, true, true);
            double amplitude = duration * 1.0f / TIME_BASE;
            VideoFxCommand.setExprVar(videoFx, NvsConstants.AMPLITUDE, amplitude, true);
            if (needPlay) {
                playVideo(videoClip.getInPoint(), videoClip.getInPoint() + duration);
            }
        } else if (type == ASSET_ANIMATION_OUT) {
            long clipDuration = videoClip.getOutPoint() - videoClip.getInPoint();
            long duration = CommonData.VIDEO_ANIMATION_OUT_DEFAULT_DURATION;
            if (!TextUtils.isEmpty(animationData.getPackageID2())) {
                duration = animationData.getOutPoint2() - animationData.getInPoint2();
            }
            if (duration <= 0) {
                duration = CommonData.VIDEO_ANIMATION_OUT_DEFAULT_DURATION;
            }
            //如果存在组合动画，把组合动画置空
            if ((!animationData.getIsAnimationIn() && (!TextUtils.isEmpty(animationData.getPackageID())))) {
                VideoFxCommand.setStringVal(videoFx, NvsConstants.PACKAGE_ID, "", true);
                VideoFxCommand.setStringVal(videoFx, NvsConstants.POST_PACKAGE_ID, "", true);
            }

            //3d动画使用package id，不是3d使用 post package id
            if (assetInfo.isPostPackage() == POST_PACKAGE) {
                VideoFxCommand.setStringVal(videoFx, NvsConstants.PACKAGE2_ID, "", true);
                VideoFxCommand.setStringVal(videoFx, NvsConstants.POST_PACKAGE2_ID, assetInfo.getPackageId(), true);
            } else {
                VideoFxCommand.setStringVal(videoFx, NvsConstants.POST_PACKAGE2_ID, "", true);
                VideoFxCommand.setStringVal(videoFx, NvsConstants.PACKAGE2_ID, assetInfo.getPackageId(), true);
            }

            VideoFxCommand.setFloatVal(videoFx, NvsConstants.PACKAGE2_EFFECT_IN, clipDuration - duration, true);
            VideoFxCommand.setFloatVal(videoFx, NvsConstants.PACKAGE2_EFFECT_OUT, clipDuration, true);
            double amplitude = duration * 1.0f / TIME_BASE;
            VideoFxCommand.setExprVar(videoFx, NvsConstants.AMPLITUDE, amplitude, true);
            if (needPlay) {
                playVideoRollBack(videoClip.getOutPoint() - duration, videoClip.getOutPoint());
            }
        } else if (type == ASSET_ANIMATION_GROUP) {
            long duration = videoClip.getOutPoint() - videoClip.getInPoint();
            if ((!animationData.getIsAnimationIn() && (!TextUtils.isEmpty(animationData.getPackageID())))) {
                duration = animationData.getOutPoint() - animationData.getInPoint();
            }
            //循环动画最小时长0.1秒
            if (duration <= CommonData.VIDEO_ANIMATION_COMP_MIN_DURATION) {
                duration = videoClip.getOutPoint() - videoClip.getInPoint();
            }

            //入出动画置空
            VideoFxCommand.setStringVal(videoFx, NvsConstants.PACKAGE2_ID, "", true);
            VideoFxCommand.setStringVal(videoFx, NvsConstants.POST_PACKAGE2_ID, "", true);
            VideoFxCommand.setStringVal(videoFx, NvsConstants.PACKAGE_ID, "", true);
            VideoFxCommand.setStringVal(videoFx, NvsConstants.POST_PACKAGE_ID, "", true);

            //3d动画使用package id，不是3d使用 post package id
            if (assetInfo.isPostPackage() == POST_PACKAGE) {
                VideoFxCommand.setStringVal(videoFx, NvsConstants.POST_PACKAGE_ID, assetInfo.getPackageId(), true);
            } else {
                VideoFxCommand.setStringVal(videoFx, NvsConstants.PACKAGE_ID, assetInfo.getPackageId(), true);
            }
            VideoFxCommand.setFloatVal(videoFx, NvsConstants.PACKAGE_EFFECT_IN, 0, true);
            VideoFxCommand.setFloatVal(videoFx, NvsConstants.PACKAGE_EFFECT_OUT, duration, true);
            VideoFxCommand.setBooleanVal(videoFx, NvsConstants.PACKAGE_TYPE_ANIMATION_IN, false, true);

            double amplitude = duration * 1.0f / TIME_BASE;
            VideoFxCommand.setExprVar(videoFx, NvsConstants.AMPLITUDE, amplitude, true);
            if (needPlay) {
                playVideoLoop(videoClip.getInPoint(), videoClip.getInPoint() + duration);
            }
        }
    }

    /**
     * 删除clip 动画
     * Remove animation.
     *
     * @param videoClip     the video clip
     * @param animationType the animation type
     */
    public void removeAnimation(MeicamVideoClip videoClip, int animationType) {
        if (videoClip == null) {
            return;
        }
        MeicamVideoFx videoFx = videoClip.findPropertyVideoFx();
        if (videoFx == null) {
            return;
        }
        if (animationType == ASSET_ANIMATION_IN || animationType == ASSET_ANIMATION_GROUP) {
            VideoFxCommand.setStringVal(videoFx, NvsConstants.POST_PACKAGE_ID, "", true);
            VideoFxCommand.setStringVal(videoFx, NvsConstants.PACKAGE_ID, "", true);
            VideoFxCommand.setFloatVal(videoFx, NvsConstants.PACKAGE_EFFECT_IN, 0, true);
            VideoFxCommand.setFloatVal(videoFx, NvsConstants.PACKAGE_EFFECT_OUT, 0, true);
            /**
             * 组合动画删除的时候，入出动画都要删除
             * When the composite animation is deleted, both the inbound and outbound animations should be deleted
             */
            if (animationType == ASSET_ANIMATION_GROUP) {
                VideoFxCommand.setStringVal(videoFx, NvsConstants.POST_PACKAGE2_ID, "", true);
                VideoFxCommand.setStringVal(videoFx, NvsConstants.PACKAGE2_ID, "", true);
                VideoFxCommand.setFloatVal(videoFx, NvsConstants.PACKAGE2_EFFECT_IN, 0, true);
                VideoFxCommand.setFloatVal(videoFx, NvsConstants.PACKAGE2_EFFECT_OUT, 0, true);
            }
        } else {
            VideoFxCommand.setStringVal(videoFx, NvsConstants.POST_PACKAGE2_ID, "", true);
            VideoFxCommand.setStringVal(videoFx, NvsConstants.PACKAGE2_ID, "", true);
            VideoFxCommand.setFloatVal(videoFx, NvsConstants.PACKAGE2_EFFECT_IN, 0, true);
            VideoFxCommand.setFloatVal(videoFx, NvsConstants.PACKAGE2_EFFECT_OUT, 0, true);
        }
        seekTimeline(0);
    }

    /**
     * 改变入和组合动画的时长
     * Chang animation in.
     *
     * @param animationData the animation data
     * @param videoClip     the video clip
     */
    public void changAnimationInAndComp(AnimationData animationData, MeicamVideoClip videoClip) {
        if (animationData == null) {
            return;
        }
        if (videoClip == null) {
            return;
        }
        MeicamVideoFx videoFx = videoClip.findPropertyVideoFx();
        if (videoFx == null) {
            return;
        }
        //如先添加了入、出动画，并且调节后所剩时长小于默认0.5秒，则后添加的动画时长为最大时长减去已添加的动画时长
        if (animationData.getIsAnimationIn()) {
            long videoDuration = videoClip.getOutPoint() - videoClip.getInPoint();
            long animationDuration = (long) (videoFx.getFloatVal(NvsConstants.PACKAGE_EFFECT_OUT) - videoFx.getFloatVal(NvsConstants.PACKAGE_EFFECT_IN)
                    + videoFx.getFloatVal(NvsConstants.PACKAGE2_EFFECT_OUT) - videoFx.getFloatVal(NvsConstants.PACKAGE2_EFFECT_IN));
            if (animationDuration >= videoDuration) {
                animationDuration = videoDuration;
            }
            if (videoDuration - animationDuration < 0.5 * CommonData.TIMEBASE) {
                long residueDuration = videoDuration - animationDuration;
                animationData.setOutPoint(animationData.getOutPoint() + residueDuration);
            }
        }

        long duration = animationData.getOutPoint() - animationData.getInPoint();
        /**
         * 如果入场动画的总时长为0，则SDK会将动画总时长设置为videoClip的总时长，这里不应该是0，需要修正
         * If the total duration of admission animation is 0,
         * the SDK will set the total duration of animation to the total duration of videoclip.
         * It should not be 0 here and needs to be corrected.
         */
        if (duration == 0) {
            animationData.setOutPoint(1);
            duration = 1;
        }
        VideoFxCommand.setFloatVal(videoFx, NvsConstants.PACKAGE_EFFECT_IN, animationData.getInPoint(), true);
        VideoFxCommand.setFloatVal(videoFx, NvsConstants.PACKAGE_EFFECT_OUT, animationData.getOutPoint(), true);
        double amplitude = (animationData.getOutPoint() - animationData.getInPoint()) * 1.0f / TIME_BASE;
        VideoFxCommand.setExprVar(videoFx, NvsConstants.AMPLITUDE, amplitude, true);
        if (animationData.getIsAnimationIn()) {
            playVideo(videoClip.getInPoint(), videoClip.getInPoint() + duration);
        } else {
            playVideoLoop(videoClip.getInPoint(), videoClip.getInPoint() + duration);
        }
    }

    /**
     * 改变出动画的时长
     * Chang animation in.
     *
     * @param animationData the animation data
     * @param videoClip     the video clip
     */
    public void changAnimationOut(AnimationData animationData, MeicamVideoClip videoClip) {
        if (animationData == null) {
            return;
        }
        if (videoClip == null) {
            return;
        }
        MeicamVideoFx videoFx = videoClip.findPropertyVideoFx();
        if (videoFx == null) {
            return;
        }

        //如先添加了入、出动画，并且调节后所剩时长小于默认0.5秒，则后添加的动画时长为最大时长减去已添加的动画时长
        long videoDuration = videoClip.getOutPoint() - videoClip.getInPoint();
        long animationDuration = (long) (videoFx.getFloatVal(NvsConstants.PACKAGE_EFFECT_OUT) - videoFx.getFloatVal(NvsConstants.PACKAGE_EFFECT_IN)
                + videoFx.getFloatVal(NvsConstants.PACKAGE2_EFFECT_OUT) - videoFx.getFloatVal(NvsConstants.PACKAGE2_EFFECT_IN));
        if (animationDuration >= videoDuration) {
            animationDuration = videoDuration;
        }
        if (videoDuration - animationDuration < 0.5 * CommonData.TIMEBASE) {
            long residueDuration = videoDuration - animationDuration;
            animationData.setInPoint2(animationData.getInPoint2() - residueDuration);
        }

        VideoFxCommand.setFloatVal(videoFx, NvsConstants.PACKAGE2_EFFECT_IN, animationData.getInPoint2(), true);
        VideoFxCommand.setFloatVal(videoFx, NvsConstants.PACKAGE2_EFFECT_OUT, animationData.getOutPoint2(), true);
        long duration = animationData.getOutPoint2() - animationData.getInPoint2();
        double amplitude = duration * 1.0f / TIME_BASE;
        VideoFxCommand.setExprVar(videoFx, NvsConstants.AMPLITUDE, amplitude, true);
        playVideoRollBack(videoClip.getOutPoint() - duration, videoClip.getOutPoint());
    }

    private MeicamTimelineVideoFxClip findEffectInTrack(MeicamTimelineVideoFxTrack videoFxTrack, long inPoint) {
        if (videoFxTrack == null) {
            return null;
        }
        for (int index = 0; index < videoFxTrack.getClipCount(); index++) {
            MeicamTimelineVideoFxClip clipInfo = videoFxTrack.getClip(index);
            if ((clipInfo.getInPoint() == inPoint)) {
                return clipInfo;
            }
        }
        return null;
    }

    private MeicamTimelineVideoFxTrack findEffectTrack(IClip trackData) {
        if (meicamTimeline == null) {
            LogUtils.e("timeline is null");
            return null;
        }
        return meicamTimeline.getTimelineFxTrack(trackData.getTrackIndex());
    }

    public int getTrackIndex(long inPoint, long outPoint) {
        return getTrackIndex(meicamTimeline, inPoint, outPoint);
    }

    public static int getTrackIndex(MeicamTimeline meicamTimeline, long inPoint, long outPoint) {
        if (meicamTimeline == null) {
            return -1;
        }
        for (int i = 0; i < meicamTimeline.getStickerCaptionTrackCount(); i++) {
            MeicamStickerCaptionTrack meicamStickerCaptionTrack = meicamTimeline.findStickCaptionTrack(i);
            if (meicamStickerCaptionTrack == null) {
                continue;
            }
            int nowTrackIndex = meicamStickerCaptionTrack.getIndex();
            if (meicamStickerCaptionTrack.getClipCount() == 0) {
                return nowTrackIndex;
            }
            for (int j = 0; j < meicamStickerCaptionTrack.getClipCount(); j++) {
                ClipInfo<?> nowClipInfo = meicamStickerCaptionTrack.getCaptionStickerClip(j);
                if (nowClipInfo == null) {
                    continue;
                }
                long nowInpoint = nowClipInfo.getInPoint();
                long nowOutpoint = nowClipInfo.getOutPoint();
                long beforeOutpoint = 0;
                if (j > 0) {
                    ClipInfo<?> beforeClipInfo = meicamStickerCaptionTrack.getCaptionStickerClip(j - 1);
                    if (beforeClipInfo == null) {
                        continue;
                    }
                    beforeOutpoint = beforeClipInfo.getOutPoint();
                }
                if (inPoint >= beforeOutpoint && outPoint <= nowInpoint) {
                    return nowTrackIndex;
                }
                long afterInPoint;
                if (j < meicamStickerCaptionTrack.getClipCount() - 1) {
                    ClipInfo<?> afterClipInfo = meicamStickerCaptionTrack.getCaptionStickerClip(j + 1);
                    if (afterClipInfo == null) {
                        continue;
                    }
                    afterInPoint = afterClipInfo.getInPoint();
                    if (inPoint >= nowOutpoint && outPoint <= afterInPoint) {
                        return nowTrackIndex;
                    }
                } else {
                    if (inPoint >= nowOutpoint) {
                        return nowTrackIndex;
                    }
                }
            }
        }
        return meicamTimeline.getStickerCaptionTrackCount();
    }


    /**
     * 复制获取Index
     * Copy to get Index
     *
     * @return 轨道的索引 Index of track
     */
    private int getTrackIndexByCopy(long inPoint, long outPoint, int index) {
        if (meicamTimeline == null) {
            return -1;
        }
        long during = outPoint - inPoint;
        for (int i = index + 1; i < meicamTimeline.getStickerCaptionTrackCount(); i++) {
            MeicamStickerCaptionTrack track = meicamTimeline.findStickCaptionTrack(i);
            if (track == null) {
                continue;
            }
            if (track.getClipCount() == 0) {
                return track.getIndex();
            }
            for (int j = 0; j < track.getClipCount(); j++) {
                ClipInfo<?> clipInfo = track.getCaptionStickerClip(j);
                if (clipInfo == null) {
                    continue;
                }
                if (clipInfo.getInPoint() > inPoint) {
                    /*
                     * 找到了后面的一个片段了
                     * We found the last clip
                     * */
                    if (j > 0) {
                        /*
                         * 前面一个片段
                         * The previous segment
                         * */
                        ClipInfo<?> clipInfoBefore = track.getCaptionStickerClip(j - 1);
                        if (clipInfoBefore == null) {
                            continue;
                        }
                        if (clipInfo.getInPoint() - clipInfoBefore.getOutPoint() > during) {
                            return i;
                        }
                    } else {
                        if (clipInfo.getInPoint() - inPoint > during) {
                            return i;
                        }
                    }
                } else {
                    ClipInfo<?> last = track.getCaptionStickerClip(track.getClipCount() - 1);
                    if (last == null) {
                        continue;
                    }
                    if (inPoint > last.getOutPoint()) {
                        return i;
                    }
                }
            }
        }
        return meicamTimeline.getStickerCaptionTrackCount();
    }


    /**
     * 获取导出自定义高度
     * Get custom height
     *
     * @param resolution 分辨率
     * @param ratio      比例
     */
    public int getCustomHeight(int resolution, int ratio) {
        int height;
        if (ratio == 0) {
            NvsVideoResolution videoResolution = getVideoResolution();
            int widthImage = 720;
            int heightImage = 1080;
            if (videoResolution != null) {
                widthImage = videoResolution.imageWidth;
                heightImage = videoResolution.imageHeight;
            }
            height = widthImage > heightImage ? resolution : Math.round(resolution * 1.0F / widthImage * heightImage);
            return height;
        }
        if (ratio == NvsConstants.AspectRatio.AspectRatio_16v9) { // 16:9
            height = resolution;
        } else if (ratio == NvsConstants.AspectRatio.AspectRatio_1v1) { //1:1
            height = resolution;
        } else if (ratio == NvsConstants.AspectRatio.AspectRatio_9v16) { //9:16
            height = (int) (resolution / 9.0 * 16);
        } else if (ratio == NvsConstants.AspectRatio.AspectRatio_3v4) { // 3:4
            height = (int) (resolution / 3.0 * 4.0);
        } else if (ratio == NvsConstants.AspectRatio.AspectRatio_4v3) { //4:3
            height = resolution;
        } else if (ratio == NvsConstants.AspectRatio.AspectRatio_18v9) { //18:9
            height = resolution;
        } else if (ratio == NvsConstants.AspectRatio.AspectRatio_9v18) { //9:18
            height = (int) (resolution / 9.0 * 18);
        } else if (ratio == NvsConstants.AspectRatio.AspectRatio_21v9) { //21:9
            height = resolution;
        } else if (ratio == NvsConstants.AspectRatio.AspectRatio_9v21) { //9:21
            height = (int) (resolution / 9.0 * 21);
        } else {
            NvsVideoResolution videoResolution = getVideoResolution();
            if (videoResolution != null) {
                height = videoResolution.imageWidth > videoResolution.imageHeight ? 720 : 1280;
            } else {
                height = 720;
            }
        }
        return height;
    }

    /***
     *更改普通变速
     * Change normal speed
     * @param trackIndex the track index 轨道索引
     * @param changeSpeedVideoClip the change speed video clip 要变速的片段
     * @param speed the speed 速度
     * @param changeVoice true change voice 变调，false not 不变调
     * */
    public void changeNormalSpeed(int trackIndex, MeicamVideoClip changeSpeedVideoClip, float speed, boolean changeVoice) {
        if (meicamTimeline != null) {
            MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(trackIndex);
            if (videoTrack != null) {
                long linkageOutPoint = changeSpeedVideoClip.getOutPoint();
                double oldSpeed = changeSpeedVideoClip.getSpeed();
                VideoClipCommand.setCurveSpeed(changeSpeedVideoClip, "", "");
                VideoClipCommand.setSpeed(changeSpeedVideoClip, speed, changeVoice);
                VideoClipCommand.zoomKeyFrame(changeSpeedVideoClip, speed / oldSpeed);
                long changeTime = linkageOutPoint - changeSpeedVideoClip.getOutPoint();
                for (int i = changeSpeedVideoClip.getIndex() + 1; i < videoTrack.getClipCount(); i++) {
                    MeicamVideoClip videoClip = videoTrack.getVideoClip(i);
                    if (videoClip != null) {
                        VideoClipCommand.updateInAndOutPoint(videoClip);
                    }
                }
                if (trackIndex == MAIN_TRACK_INDEX) {
                    timelineAddOrSubtract(linkageOutPoint, -changeTime);
                }
                updateAnimationDurationBySpeed(changeSpeedVideoClip, speed / oldSpeed);
            }
        }
    }

    /**
     * Update animation duration by speed
     * 更新动画时长
     *
     * @param videoClip the video clip
     * @param speedFactor the speed factor 速度变化量
     */
    private void updateAnimationDurationBySpeed(@NonNull MeicamVideoClip videoClip, double speedFactor) {
        MeicamVideoFx videoFx = videoClip.findPropertyVideoFx();
        if (videoFx == null) {
            return;
        }

        String package2Id = videoFx.getStringVal(NvsConstants.PACKAGE2_ID);
        String package2PostId = videoFx.getStringVal(NvsConstants.POST_PACKAGE2_ID);
        if ((!TextUtils.isEmpty(package2Id)) || (!TextUtils.isEmpty(package2PostId))) {
            float effectOut = videoFx.getFloatVal(NvsConstants.PACKAGE2_EFFECT_OUT, -1);
            float effectIn = videoFx.getFloatVal(NvsConstants.PACKAGE2_EFFECT_IN, -1);
            if (effectOut > 0 && effectIn >= 0) {
                long duration = videoClip.getOutPoint() - videoClip.getInPoint();
                VideoFxCommand.setFloatVal(videoFx, NvsConstants.PACKAGE2_EFFECT_OUT, duration);
                effectIn = (float) (duration - (effectOut - effectIn) / speedFactor);
                VideoFxCommand.setFloatVal(videoFx, NvsConstants.PACKAGE2_EFFECT_IN, effectIn);
                double amplitude = duration * 1.0f / TIME_BASE;
                VideoFxCommand.setExprVar(videoFx, NvsConstants.AMPLITUDE, amplitude);
            }
        }

        String packageId = videoFx.getStringVal(NvsConstants.PACKAGE_ID);
        String packagePostId = videoFx.getStringVal(NvsConstants.POST_PACKAGE_ID);
        if ((!TextUtils.isEmpty(packageId)) || (!TextUtils.isEmpty(packagePostId))) {
            float effectOut = videoFx.getFloatVal(NvsConstants.PACKAGE_EFFECT_OUT, -1);
            if (effectOut > 0) {
                VideoFxCommand.setFloatVal(videoFx, NvsConstants.PACKAGE_EFFECT_OUT, (float) (effectOut / speedFactor));
            }
        }
    }

    /**
     * 更改曲线变速
     * Change the video clip curve speed.
     *
     * @param trackIndex           the track index 轨道索引
     * @param changeSpeedVideoClip the change speed video clip 要变速的片段
     * @param speedId              the speed id曲线变速标识
     * @param speedName            the speed name曲线变速名称
     */
    public void changeCurveSpeed(int trackIndex, MeicamVideoClip changeSpeedVideoClip, String speedId, String speedName) {
        if (meicamTimeline != null && changeSpeedVideoClip != null) {
            MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(trackIndex);
            if (videoTrack != null) {
                long linkageOutPoint = changeSpeedVideoClip.getOutPoint();
                double oldSpeed = changeSpeedVideoClip.getSpeed();
                if (TextUtils.isEmpty(speedId)) {
                    VideoClipCommand.setCurveSpeed(changeSpeedVideoClip, "", "");
                } else {
                    VideoClipCommand.setCurveSpeed(changeSpeedVideoClip, speedId, speedName);
                }
                VideoClipCommand.updateInAndOutPoint(changeSpeedVideoClip);
                long changeTime = linkageOutPoint - changeSpeedVideoClip.getOutPoint();
                double speedFactor = changeSpeedVideoClip.getSpeed() / oldSpeed;
                VideoClipCommand.zoomKeyFrame(changeSpeedVideoClip, speedFactor);
                for (int i = changeSpeedVideoClip.getIndex() + 1; i < videoTrack.getClipCount(); i++) {
                    MeicamVideoClip videoClip = videoTrack.getVideoClip(i);
                    if (videoClip != null) {
                        VideoClipCommand.updateInAndOutPoint(videoClip);
                    }
                }
                if (trackIndex == MAIN_TRACK_INDEX) {
                    timelineAddOrSubtract(linkageOutPoint, changeTime);
                }
                updateAnimationDurationBySpeed(changeSpeedVideoClip, speedFactor);
            }
        }
    }

    /**
     * Gets pip video clip index.
     *
     * @param inPoint  the in point
     * @param outPoint the out point
     * @return the pip video clip index
     */
    public int getPipTrackIndex(long inPoint, long outPoint) {
        for (int i = 1; i < meicamTimeline.videoTrackCount(); i++) {
            MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(i);
            if (videoTrack == null) {
                continue;
            }
            if (videoTrack.getClipCount() == 0) {
                return videoTrack.getIndex();
            }
            for (int j = 0; j < videoTrack.getClipCount(); j++) {
                MeicamVideoClip nowClipInfo = videoTrack.getVideoClip(j);
                long nowInPoint = nowClipInfo.getInPoint();
                long nowOutpoint = nowClipInfo.getOutPoint();
                long beforeOutpoint = 0;
                if (j > 0) {
                    ClipInfo<?> beforeClipInfo = videoTrack.getVideoClip(j - 1);
                    beforeOutpoint = beforeClipInfo.getOutPoint();
                }
                if (inPoint >= beforeOutpoint && outPoint <= nowInPoint) {
                    return videoTrack.getIndex();
                }
                long afterInPoint;
                if (j < videoTrack.getClipCount() - 1) {
                    ClipInfo<?> afterClipInfo = videoTrack.getVideoClip(j + 1);
                    afterInPoint = afterClipInfo.getInPoint();
                    if (inPoint >= nowOutpoint && outPoint <= afterInPoint) {
                        return videoTrack.getIndex();
                    }
                } else {
                    if (inPoint >= nowOutpoint) {
                        return videoTrack.getIndex();
                    }
                }
            }
        }
        return meicamTimeline.videoTrackCount();
    }

    /**
     * 查找一个合适的嵌入索引
     * Find an appropriate embedded index
     *
     * @param videoTrack the video track  视频轨道
     * @param inPoint    the in point  入点
     * @param outPoint   the out point  出点
     * @return index 索引
     */
    public int findSuitableEmbedIndex(MeicamVideoTrack videoTrack, long inPoint, long outPoint) {
        if (videoTrack != null) {
            if (inPoint >= videoTrack.getDuration()) {
                return videoTrack.getClipCount();
            }
            int count = videoTrack.getClipCount();
            for (int i = 0; i < count; i++) {
                MeicamVideoClip videoClip = videoTrack.getVideoClip(i);
                if (inPoint >= videoClip.getOutPoint()) {
                    if (i + 1 < count) {
                        videoClip = videoTrack.getVideoClip(i + 1);
                        if (outPoint <= videoClip.getInPoint()) {
                            return i + 1;
                        }
                    }
                } else {
                    if (outPoint <= videoClip.getInPoint()) {
                        return i;
                    }
                    return -1;
                }
            }
        }
        return -1;
    }

    /**
     * 是否可以嵌入一个视频片段，注意：集合有序才可以这么用
     * Can you embed a video clip
     *
     * @param videoTrack the video track  视频轨道
     * @param inPoint    the in point  入点
     * @param outPoint   the out point  出点
     * @return true can embed 可以嵌入,false not 不可以嵌入
     */
    public boolean canEmbedVideoClip(MeicamVideoTrack videoTrack, long inPoint, long outPoint) {
        if (videoTrack != null) {
            if (inPoint >= videoTrack.getDuration()) {
                return true;
            }
            int count = videoTrack.getClipCount();
            for (int i = 0; i < count; i++) {
                MeicamVideoClip videoClip = videoTrack.getVideoClip(i);
                if (inPoint >= videoClip.getOutPoint()) {
                    if (i + 1 < count) {
                        videoClip = videoTrack.getVideoClip(i + 1);
                        if (outPoint <= videoClip.getInPoint()) {
                            return true;
                        }
                    }
                } else {
                    return outPoint <= videoClip.getInPoint();
                }
            }
        }
        return false;
    }

    /**
     * 删除所有的AI 字幕
     * delete all AI caption
     */
    public void removeAllAICaption() {
        if (meicamTimeline != null) {
            for (int i = 0; i < meicamTimeline.getStickerCaptionTrackCount(); i++) {
                MeicamStickerCaptionTrack meicamStickerCaptionTrack = meicamTimeline.findStickCaptionTrack(i);
                if (meicamStickerCaptionTrack == null) {
                    continue;
                }
                int clipCount = meicamStickerCaptionTrack.getClipCount();
                for (int j = 0; j < clipCount; j++) {
                    ClipInfo<?> clipInfo = meicamStickerCaptionTrack.getCaptionStickerClip(j);
                    if (isAICaption(clipInfo)) {
                        CaptionStickerTrackCommand.removeClip(meicamStickerCaptionTrack, clipInfo, true);
                        j--;
                    }
                }

            }
        }
    }


    /**
     * 判断贴纸是否有声音
     * Determine if the sticker makes a sound
     */
    public boolean currStickerHasVoice() {
        return hasAudio;
    }

    /**
     * 设置贴纸是否有声音
     * <p>
     * Set sticker has audio or not.
     *
     * @param hasAudio 是否有声音，true: 是; false: 否. Whether has audio or not. true: yes; false: no.
     */
    public void setStickerHasAudio(boolean hasAudio) {
        this.hasAudio = hasAudio;
    }

    /**
     * 更改字幕参数
     * Change caption param
     *
     * @param meicamCaptionClip the caption clip
     * @param paramType         int the param type
     * @param oldParam          the old param
     * @param oldParam          the old param
     */
    public boolean changeCaptionParam(MeicamCaptionClip meicamCaptionClip, int paramType, Object oldParam, Object newParam) {
        boolean success = CaptionCommand.setParamWidthOld(meicamCaptionClip, paramType, oldParam, newParam);
        seekTimeline();
        return success;
    }


    /**
     * Handle caption position.
     *
     * @param meicamCaptionClip the meicam caption clip
     * @param newPosition       the int value
     * @param keyFrameAtTime    the key frame at time 关键帧的时间点
     */
    public void handleCaptionPosition(MeicamCaptionClip meicamCaptionClip, MeicamCaptionClip oldCaption, int newPosition, long keyFrameAtTime) {
        if (meicamCaptionClip == null || meicamTimeline == null) {
            return;
        }
        NvsVideoResolution nvsVideoResolution = meicamTimeline.getVideoResolution();
        if (nvsVideoResolution == null) {
            return;
        }
        MeicamKeyFrame keyFrame = null;
        KeyFrameProcessor<NvsCaption> keyFrameHolder = meicamCaptionClip.keyFrameProcessor();
        if (keyFrameHolder.getKeyFrameCount() > 0) {
            keyFrame = keyFrameHolder.getKeyFrame(keyFrameAtTime);
            if (keyFrame == null) {
                long atTime = meicamTimeline.getCurrentPosition() - meicamCaptionClip.getInPoint();
                if (keyFrameHolder.getKeyFrameCount() > 0 && atTime >= 0 &&
                        atTime <= (meicamCaptionClip.getOutPoint() - meicamCaptionClip.getInPoint())) {
                    keyFrame = keyFrameHolder.getKeyFrame(atTime);
                    if (keyFrame == null) {
                        /*当前位置没有关键帧，获取需要的关键帧参数,并添加关键帧*/
                        addClipKeyFrame(meicamCaptionClip, atTime);
                    }
                }
            }
        }
        MeicamKeyFrame oldKeyFrame = null;
        keyFrameHolder = oldCaption.keyFrameProcessor();
        if (keyFrameHolder.getKeyFrameCount() > 0) {
            oldKeyFrame = keyFrameHolder.getKeyFrame(keyFrameAtTime);
            if (oldKeyFrame == null) {
                long atTime = meicamTimeline.getCurrentPosition() - oldCaption.getInPoint();
                if (keyFrameHolder.getKeyFrameCount() > 0 && atTime >= 0 &&
                        atTime <= (oldCaption.getOutPoint() - oldCaption.getInPoint())) {
                    oldKeyFrame = keyFrameHolder.getKeyFrame(atTime);
                }
            }
        }
        List<PointF> list = meicamCaptionClip.getBoundingRectangleVertices();
        if (list == null || list.size() < 4) {
            return;
        }
        Collections.sort(list, new PointXComparator());
        float xOffset = Float.MAX_VALUE;
        float yOffset = Float.MAX_VALUE;
        NvsVideoResolution videoResolution = meicamTimeline.getVideoResolution();
        int imageWidth = videoResolution.imageWidth;
        int imageHeight = videoResolution.imageHeight;
        if (newPosition == CAPTION_ALIGN_LEFT) {
            xOffset = -(imageWidth / 2f + list.get(0).x);
        } else if (newPosition == CAPTION_ALIGN_HORIZ_CENTER) {
            xOffset = -((list.get(3).x - list.get(0).x) / 2 + list.get(0).x);
        } else if (newPosition == CAPTION_ALIGN_RIGHT) {
            xOffset = imageWidth / 2f - list.get(3).x;
        } else if (newPosition == CAPTION_ALIGN_TOP) {
            yOffset = imageHeight / 2f - list.get(0).y;
        } else if (newPosition == CAPTION_ALIGN_VERT_CENTER) {
            yOffset = -((list.get(3).y - list.get(0).y) / 2 + list.get(0).y);
        } else if (newPosition == CAPTION_ALIGN_BOTTOM) {
            float y_dis = list.get(1).y - list.get(0).y;
            yOffset = -(imageHeight / 2f + list.get(3).y + y_dis);
        }
        boolean translationX = xOffset != Float.MAX_VALUE;
        setCaptionTranslation(translationX ? xOffset : yOffset, translationX, meicamCaptionClip, keyFrame, oldCaption, oldKeyFrame);
        seekTimeline();
    }

    /**
     * 设置字幕的平移值
     * Set caption translation
     *
     * @param deltaValue   the delta value 中间值
     * @param translationX true change translation x 更改x坐标，false not 更改y坐标
     * @param captionClip  the caption clip 字幕
     * @param keyFrame     the key frame 关键帧
     **/
    private void setCaptionTranslation(float deltaValue, boolean translationX, MeicamCaptionClip captionClip, MeicamKeyFrame keyFrame, MeicamCaptionClip oldCaption, MeicamKeyFrame oldKeyFrame) {
        if (keyFrame == null) {
            if (translationX) {
                Float odlTranslationX = oldCaption == null ? null : oldCaption.getTranslationX();
                CaptionCommand.setParamWidthOld(captionClip, CaptionCommand.PARAM_TRANS_X, odlTranslationX, captionClip.getTranslationX() + deltaValue);
            } else {
                Float odlTranslationY = oldCaption == null ? null : oldCaption.getTranslationY();
                CaptionCommand.setParamWidthOld(captionClip, CaptionCommand.PARAM_TRANS_Y, odlTranslationY, captionClip.getTranslationY() + deltaValue);
            }
        } else {
            String key = translationX ? CAPTION_TRANS_X : CAPTION_TRANS_Y;
            MeicamFxParam<?> fxParam = keyFrame.getFxParam(key);
            Float oldValue = null;
            if (oldKeyFrame != null) {
                MeicamFxParam<?> oldFxParam = oldKeyFrame.getFxParam(key);
                if (oldFxParam != null) {
                    float floatValue = fxParam.getFloatValue();
                    if (floatValue != Float.MAX_VALUE) {
                        oldValue = floatValue;
                    }
                }
            }
            if (fxParam != null) {
                float floatValue = fxParam.getFloatValue();
                if (floatValue != Float.MAX_VALUE) {
                    KeyFrameCommand.setFloatValWidthOld(keyFrame, key, oldValue, floatValue + deltaValue);
                }
            }
        }
    }


    private long getEffectDuration(@NonNull MeicamTimelineVideoFxTrack videoFxTrack, long inPoint, long defaultDuration) {
        int clipCount = videoFxTrack.getClipCount();
        if (clipCount <= 0) {
            return defaultDuration;
        }
        long outPoint = inPoint + defaultDuration;
        if (outPoint > meicamTimeline.getDuration()) {
            outPoint = meicamTimeline.getDuration();
        }
        return outPoint - inPoint;
    }

    private MeicamTimelineVideoFxClip removePreviewEffect(long position) {
        MeicamTimelineVideoFxTrack timelineFxTrack = meicamTimeline.getTimelineFxTrack(0);
        if (timelineFxTrack == null) {
            return null;
        }
        int clipCount = timelineFxTrack.getClipCount();
        if (clipCount > 0) {
            for (int index = clipCount - 1; index >= 0; index--) {
                MeicamTimelineVideoFxClip clipInfo = timelineFxTrack.getClip(index);
                if (clipInfo.getInPoint() <= position && clipInfo.getOutPoint() > position) {
                    timelineFxTrack.removeClip(clipInfo);
                    return clipInfo;
                }
            }
        }
        return null;
    }


    /**
     * Copy caption stick clip info.
     * 复制字幕贴纸组合字幕
     *
     * @param clipInfo the clip info
     * @return the clip info
     */
    public ClipInfo<?> copyCaptionStick(ClipInfo<?> clipInfo) {
        if (meicamTimeline == null || clipInfo == null) {
            LogUtils.e("copyCaptionStick clipInfo is null !");
            return null;
        }
        long inPoint = clipInfo.getInPoint();
        long duration = clipInfo.getOutPoint() - clipInfo.getInPoint();
        long outPoint = inPoint + duration;
        int trackIndex = getTrackIndexByCopy(clipInfo.getInPoint(), clipInfo.getOutPoint(), clipInfo.getTrackIndex());
        if (clipInfo instanceof MeicamCaptionClip) {
            LogUtils.d("copy caption");
            Object object = ((MeicamCaptionClip) clipInfo).clone(true);

            MeicamCaptionClip meicamCaptionClip = (MeicamCaptionClip) object;
            MeicamStickerCaptionTrack meicamStickerCaptionTrack = meicamTimeline.findStickCaptionTrack(trackIndex);
            if (meicamStickerCaptionTrack == null) {
                meicamStickerCaptionTrack = TimelineCommand.addStickCaptionTrack(meicamTimeline, trackIndex);
            }
            if (meicamStickerCaptionTrack == null) {
                return null;
            }
            meicamCaptionClip.setInPoint(inPoint);
            meicamCaptionClip.setOutPoint(outPoint);
            MeicamCaptionClip copyCaption = addCaption(meicamCaptionClip, inPoint, outPoint, true, true);
            if (copyCaption == null) {
                return null;
            }
            return meicamCaptionClip;
        } else if (clipInfo instanceof MeicamStickerClip) {
            LogUtils.d("copySticker");
            Object object = ((MeicamStickerClip) clipInfo).clone(true);
            MeicamStickerClip meicamStickerClip = (MeicamStickerClip) object;
            MeicamStickerCaptionTrack meicamStickerCaptionTrack = meicamTimeline.findStickCaptionTrack(trackIndex);
            if (meicamStickerCaptionTrack == null) {
                meicamStickerCaptionTrack = TimelineCommand.addStickCaptionTrack(meicamTimeline, trackIndex);
            }
            if (meicamStickerCaptionTrack == null) {
                return null;
            }
            meicamStickerClip.setInPoint(inPoint);
            meicamStickerClip.setOutPoint(outPoint);
            MeicamStickerClip copySticker = (MeicamStickerClip) CaptionStickerTrackCommand.addClip(meicamStickerCaptionTrack, meicamStickerClip, true, true);
            if (copySticker == null) {
                return null;
            }
            return meicamStickerClip;
        } else if (clipInfo instanceof MeicamCompoundCaptionClip) {
            LogUtils.e("copyComCaption");
            Object object = ((MeicamCompoundCaptionClip) clipInfo).clone(true);

            MeicamCompoundCaptionClip meicamCompoundCaptionClip = (MeicamCompoundCaptionClip) object;

            MeicamStickerCaptionTrack meicamStickerCaptionTrack = meicamTimeline.findStickCaptionTrack(trackIndex);
            if (meicamStickerCaptionTrack == null) {
                meicamStickerCaptionTrack = TimelineCommand.addStickCaptionTrack(meicamTimeline, trackIndex);
            }
            if (meicamStickerCaptionTrack == null) {
                return null;
            }
            meicamCompoundCaptionClip.setInPoint(inPoint);
            meicamCompoundCaptionClip.setOutPoint(outPoint);
            MeicamCompoundCaptionClip copyCompoundCaptionClip = (MeicamCompoundCaptionClip) CaptionStickerTrackCommand.addClip(meicamStickerCaptionTrack, meicamCompoundCaptionClip, true, true);
            if (copyCompoundCaptionClip == null) {
                return null;
            }
            return meicamCompoundCaptionClip;
        }
        return null;
    }


    /**
     * videoClip 倒放
     * 画中画remove的时候保存视频空间，主轨道不保存。添加的时候画中画使用add的方式，主轨道使用insert的方式。
     * 因为画中画位置不连续，而且画中画删除不应该影响其他画中画。
     * <p>
     * I'm going to save video space when I draw remove, but I'm not going to save the main track. When you add it, add it in the drawing, insert it in the main track.
     * Because picture-in-picture positions are discontinuous, and picture-in-picture deletions should not affect other picture-in-picture.
     * Sets video convert.
     *
     * @param videoClip the meicam video clip
     */
    public void setVideoConvert(MeicamVideoClip videoClip) {
        if (meicamTimeline == null || videoClip == null) {
            return;
        }
        MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(videoClip.getTrackIndex());
        if (videoTrack == null) {
            return;
        }
        NvsAVFileInfo avFileInfo = mStreamingContext.getAVFileInfo(videoClip.getFilePath());
        if (avFileInfo == null) {
            return;
        }
        long duration = avFileInfo.getDuration();
        long trimOut = videoClip.getTrimOut();
        long clipDuration = videoClip.getTrimOut() - videoClip.getTrimIn();
        //videoClip.setVideoReverse(!videoClip.getVideoReverse());
        boolean reverse = !videoClip.getVideoReverse();
        //VideoClipCommand.setParam(videoClip, PARAM_VIDEO_IS_REVERSE, reverse);
        if (videoClip.getTrackIndex() == 0) {
            videoClip = VideoTrackCommand.removeVideoClip(videoTrack, videoClip.getIndex(), false);
        } else {
            videoClip = VideoTrackCommand.removeVideoClipWidthSpace(videoTrack, videoClip.getIndex());
        }
        //videoClip = videoTrack.removeVideoClip(videoClip.getIndex(), videoClip.getTrackIndex() != 0);
        if (videoClip != null) {
            MeicamVideoClip addVideoClip;
            VideoClipCommand.setParam(videoClip, PARAM_VIDEO_IS_REVERSE, reverse);
            if (videoClip.getTrackIndex() == 0) {
                addVideoClip = VideoTrackCommand.insertVideoClip(videoTrack, videoClip, videoClip.getIndex(), duration - trimOut,
                        duration - trimOut + clipDuration);
                /*addVideoClip = videoTrack.insertVideoClip(videoClip, videoClip.getIndex(), duration - trimOut,
                        duration - trimOut + clipDuration);*/
            } else {
                addVideoClip = VideoTrackCommand.addVideoClip(videoTrack, videoClip, videoClip.getInPoint(), duration - trimOut,
                        duration - trimOut + clipDuration);
                /*addVideoClip = videoTrack.addVideoClip(videoClip, videoClip.getInPoint(), duration - trimOut,
                        duration - trimOut + clipDuration);*/
            }

            if (addVideoClip == null) {
                //添加失败，还原
                //Add failed, restore.
                if (videoClip.getTrackIndex() == 0) {
                    VideoTrackCommand.insertVideoClip(videoTrack, videoClip, videoClip.getIndex(), videoClip.getTrimIn(), videoClip.getTrimOut());
                    //videoTrack.insertVideoClip(videoClip, videoClip.getIndex());
                } else {
                    VideoTrackCommand.addVideoClip(videoTrack, videoClip, videoClip.getInPoint(), videoClip.getTrimIn(), videoClip.getTrimOut());
                    //videoTrack.addVideoClip(videoClip, videoClip.getInPoint(), videoClip.getTrimIn(), videoClip.getTrimOut());
                }
                VideoClipCommand.setParam(videoClip, PARAM_VIDEO_IS_REVERSE, !reverse);
            } else {
                VideoClipCommand.setParam(videoClip, PARAM_VIDEO_IS_REVERSE, reverse);
                //添加成功，倒转关键帧时间点
                VideoClipCommand.setParam(addVideoClip, PARAM_VIDEO_THUMBNAIL_INFO, null);
                //addVideoClip.setThumbNailInfo(null);
                int videoFxCount = addVideoClip.getVideoFxCount();
                for (int index = 0; index < videoFxCount; index++) {
                    MeicamVideoFx videoFx = addVideoClip.getVideoFx(index);
                    KeyFrameProcessor<NvsVideoFx> frameProcessor = videoFx.keyFrameProcessor();
                    if (frameProcessor.getKeyFrameCount() > 0) {
                        //frameProcessor.reverseKeyFrame();
                        KeyFrameHolderCommand.reverseKeyFrame(videoFx);
                    }
                }
            }
            seekTimeline(0);
            if (mOnTimelineChangeListener != null) {
                mOnTimelineChangeListener.refreshEditorTimelineView(OnTimelineChangeListener.TYPE_REVERT_CLIP);
            }
        }
    }

    /**
     * Change sticker mirror.
     * <p>
     * 贴纸镜像
     *
     * @param clipInfo the meicam sticker clip
     */
    public void changeStickerMirror(ClipInfo<?> clipInfo) {
        if (!(clipInfo instanceof MeicamStickerClip)) {
            return;
        }
        MeicamStickerClip meicamStickerClip = (MeicamStickerClip) clipInfo;
        /*
         * 贴纸水平翻转
         * Flip the sticker horizontally
         * */
        boolean isHorizFlip = !meicamStickerClip.isHorizontalFlip();
        StickerCommand.setParam(meicamStickerClip, StickerCommand.PARAM_HORIZONTAL_FLIP, isHorizFlip);
        if (mOnTimelineChangeListener != null) {
            mOnTimelineChangeListener.onSaveOperation();
        }
        //seekTimeline(NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_ANIMATED_STICKER_POSTER);
        /*
         * 贴纸添加动画后，需要去掉NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_ANIMATED_STICKER_POSTER标记
         */
        seekTimeline();
    }

    /**
     * 切换比例改变字幕位置
     * Change caption position.
     *
     * @param widthRatio  the width ratio
     * @param heightRatio the height ratio
     */
    public void changeCaptionPosition(float widthRatio, float heightRatio) {
        if (meicamTimeline == null) {
            return;
        }
        MeicamVideoTrack meicamVideoTrack = meicamTimeline.getVideoTrack(MAIN_TRACK_INDEX);
        if (meicamVideoTrack == null) {
            return;
        }
        MeicamVideoClip meicamVideoClip = meicamVideoTrack.getVideoClip(0);
        if (meicamVideoClip == null) {
            return;
        }

        float vScale = meicamVideoClip.getOriginalWidth() * 1.0f / meicamVideoClip.getOriginalHeight() <= 1 ? heightRatio : widthRatio;
        int trackCount = meicamTimeline.getStickerCaptionTrackCount();
        List<MeicamStickerCaptionTrack> aiTrack = new ArrayList<>(trackCount);
        for (int i = 0; i < trackCount; i++) {
            MeicamStickerCaptionTrack meicamStickerCaptionTrack = meicamTimeline.findStickCaptionTrack(i);
            if (meicamStickerCaptionTrack == null) {
                continue;
            }
            ClipInfo<?> captionStickerClip = meicamStickerCaptionTrack.getCaptionStickerClip(0);
            if (captionStickerClip instanceof MeicamCaptionClip) {
                MeicamCaptionClip meicamCaptionClip = (MeicamCaptionClip) captionStickerClip;
                if (meicamCaptionClip.getOperationType() == Constants.TYPE_AI_CAPTION) {
                    aiTrack.add(meicamStickerCaptionTrack);
                }
            }
        }

        for (int i = 0; i < trackCount; i++) {
            MeicamStickerCaptionTrack meicamStickerCaptionTrack = meicamTimeline.findStickCaptionTrack(i);
            if (meicamStickerCaptionTrack == null) {
                continue;
            }
            for (int j = 0; j < meicamStickerCaptionTrack.getClipCount(); j++) {
                ClipInfo<?> clipInfo = meicamStickerCaptionTrack.getCaptionStickerClip(j);
                if (clipInfo instanceof MeicamCaptionClip) {
                    MeicamCaptionClip meicamCaptionClip = (MeicamCaptionClip) clipInfo;
                    if (meicamCaptionClip.getOperationType() != Constants.TYPE_AI_CAPTION) {
                        CaptionCommand.setParam(meicamCaptionClip, CaptionCommand.PARAM_TRANS_X,
                                meicamCaptionClip.getTranslationX() * vScale);
                        CaptionCommand.setParam(meicamCaptionClip, CaptionCommand.PARAM_TRANS_Y,
                                meicamCaptionClip.getTranslationY() * vScale);
                    }
                }
            }
        }
        if (!aiTrack.isEmpty()) {
            for (int index = 0; index < aiTrack.size(); index++) {
                MeicamStickerCaptionTrack track = aiTrack.get(index);
                int clipCount = track.getClipCount();
                for (int clipIndex = 0; clipIndex < clipCount; clipIndex++) {
                    ClipInfo<?> clipInfo = track.getCaptionStickerClip(clipIndex);
                    if (clipInfo instanceof MeicamCaptionClip) {
                        MeicamCaptionClip meicamCaptionClip = (MeicamCaptionClip) clipInfo;
                        if (meicamCaptionClip.getOperationType() == Constants.TYPE_AI_CAPTION) {
                            //识别字幕的大小和位置 Size and position of Ai caption.
                            changeAiCaptionPositionAndSize(meicamCaptionClip, index);
                        }
                    }
                }
            }
        }
        seekTimeline();
    }

    /**
     * 更改识别字幕的位置的大小
     * Change the position and size of ai caption
     *
     * @param captionClip the ai caption识别的字幕
     */
    public void changeAiCaptionPositionAndSize(MeicamCaptionClip captionClip, int layerCount) {
        if (meicamTimeline == null) {
            return;
        }
        MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(MAIN_TRACK_INDEX);
        if (videoTrack == null) {
            return;
        }
        MeicamVideoClip firstVideoClip = videoTrack.getVideoClip(0);
        if (firstVideoClip == null) {
            return;
        }
        NvsVideoResolution videoResolution = meicamTimeline.getVideoResolution();
        /*clip在live window中的高度*/
        // float clipHeight = getViewSizeInLiveWindow(firstVideoClip.getOriginalWidth(), firstVideoClip.getOriginalHeight()).y;
        /*识别字幕在下边居上20px*/
        float tempY = getViewSizeInLiveWindow(firstVideoClip.getOriginalWidth(), 20 + (60 * layerCount)).y;
        /*clip在timeline中的高度*/
        // clipHeight = clipHeight * videoResolution.imageHeight / mLiveWindowSize.y;
        tempY = tempY * videoResolution.imageHeight / mLiveWindowSize.y;
        if (videoResolution.imageHeight > videoResolution.imageWidth) {
            CaptionCommand.setParam(captionClip, CaptionCommand.PARAM_SCALE_X, 0.4f);
            CaptionCommand.setParam(captionClip, CaptionCommand.PARAM_SCALE_Y, 0.4f);
        } else {
            CaptionCommand.setParam(captionClip, CaptionCommand.PARAM_SCALE_X, 0.6f);
            CaptionCommand.setParam(captionClip, CaptionCommand.PARAM_SCALE_Y, 0.6f);
        }
        List<PointF> list = captionClip.getBoundingRectangleVertices();
        if (list != null && list.size() >= 4) {
            float y = Math.abs(list.get(1).y - list.get(0).y) / 2 - videoResolution.imageHeight / 2f + tempY;
            CaptionCommand.setParam(captionClip, CaptionCommand.PARAM_TRANS_Y, y);
        }
    }

    /**
     * Gets all ai caption.
     * 获取所有的AI字幕
     *
     * @return the all ai caption
     */
    public List<ClipInfo<?>> getAllAICaption() {
        List<ClipInfo<?>> clipInfoList = new ArrayList<>();
        if (meicamTimeline != null) {
            for (int i = 0; i < meicamTimeline.getStickerCaptionTrackCount(); i++) {
                MeicamStickerCaptionTrack meicamStickerCaptionTrack = meicamTimeline.findStickCaptionTrack(i);
                if (meicamStickerCaptionTrack == null) {
                    continue;
                }
                for (int j = 0; j < meicamStickerCaptionTrack.getClipCount(); j++) {
                    ClipInfo<?> clipInfo = meicamStickerCaptionTrack.getCaptionStickerClip(j);
                    if (isAICaption(clipInfo)) {
                        clipInfoList.add(clipInfo);
                    }
                }

            }
        }
        return clipInfoList;
    }

    /**
     * is AI caption
     *
     * @param clipInfo the clip info
     * @return Is ai caption or not. true:yes; false:no.
     */
    public boolean isAICaption(ClipInfo<?> clipInfo) {
        return (clipInfo instanceof MeicamCaptionClip && ((MeicamCaptionClip) clipInfo).getOperationType() == Constants.TYPE_AI_CAPTION);
    }

    /**
     * Gets area all ai caption.
     * 获取某一区域内的所有的Ai字幕
     *
     * @param inPoint  the in point
     * @param outPoint the out point
     * @return the area all ai caption
     */
    public List<ClipInfo<?>> getAreaAllAICaption(long inPoint, long outPoint) {
        List<ClipInfo<?>> clipInfoList = new ArrayList<>();
        for (int i = 0; i < meicamTimeline.getStickerCaptionTrackCount(); i++) {
            MeicamStickerCaptionTrack meicamStickerCaptionTrack = meicamTimeline.findStickCaptionTrack(i);
            if (meicamStickerCaptionTrack == null) {
                continue;
            }
            for (int j = 0; j < meicamStickerCaptionTrack.getClipCount(); j++) {
                ClipInfo<?> clipInfo = meicamStickerCaptionTrack.getCaptionStickerClip(j);
                if (clipInfo instanceof MeicamCaptionClip &&
                        ((MeicamCaptionClip) clipInfo).getOperationType() == Constants.TYPE_AI_CAPTION &&
                        clipInfo.getInPoint() >= inPoint &&
                        clipInfo.getOutPoint() <= outPoint) {
                    clipInfoList.add(clipInfo);
                }
            }

        }
        return clipInfoList;
    }

    public void updateBaseItemRange(ClipInfo<?> clipInfo, long inPoint, long outPoint) {
        ClipCommand.changeInAndOutPoint(clipInfo, inPoint, outPoint);
        if (clipInfo instanceof MeicamCaptionClip) {
            MeicamCaptionClip captionClip = (MeicamCaptionClip) clipInfo;
            int clipDuration = (int) ((outPoint - inPoint) / 1000);
            if (!TextUtils.isEmpty(captionClip.getCombinationAnimationUuid())
                    && (captionClip.getCombinationAnimationDuration() > clipDuration)) {
                CaptionCommand.setParam(captionClip, CaptionCommand.PARAM_COMP_ANIMATION_DURATION, clipDuration);
            } else {
                if (captionClip.getMarchInAnimationDuration() + captionClip.getMarchOutAnimationDuration() > clipDuration) {
                    if (!TextUtils.isEmpty(captionClip.getMarchInAnimationUuid()) && (!TextUtils.isEmpty(captionClip.getMarchOutAnimationUuid()))) {
                        CaptionCommand.setParam(captionClip, CaptionCommand.PARAM_IN_ANIMATION_DURATION, clipDuration - captionClip.getMarchOutAnimationDuration());
                    } else if (!TextUtils.isEmpty(captionClip.getMarchInAnimationUuid())) {
                        CaptionCommand.setParam(captionClip, CaptionCommand.PARAM_IN_ANIMATION_DURATION, clipDuration);
                    } else if (!TextUtils.isEmpty(captionClip.getMarchOutAnimationUuid())) {
                        CaptionCommand.setParam(captionClip, CaptionCommand.PARAM_OUT_ANIMATION_DURATION, clipDuration);
                    }
                }
            }
        } else if (clipInfo instanceof MeicamStickerClip) {
            MeicamStickerClip stickerClip = (MeicamStickerClip) clipInfo;
            int clipDuration = (int) ((outPoint - inPoint) / 1000);
            StickerAnimation compAnimation = stickerClip.getAnimation(StickerAnimation.TYPE_ANIMATION_COMP);
            if (compAnimation != null
                    && (compAnimation.getDuration()) > clipDuration) {
                StickerCommand.changeAnimationDuration(stickerClip, StickerAnimation.TYPE_ANIMATION_COMP, clipDuration);
            } else {
                StickerAnimation inAnimation = stickerClip.getAnimation(StickerAnimation.TYPE_ANIMATION_IN);
                StickerAnimation outAnimation = stickerClip.getAnimation(StickerAnimation.TYPE_ANIMATION_OUT);
                if (inAnimation != null && outAnimation != null) {
                    StickerCommand.changeAnimationDuration(stickerClip, StickerAnimation.TYPE_ANIMATION_IN, clipDuration - outAnimation.getDuration());
                } else if (inAnimation != null) {
                    StickerCommand.changeAnimationDuration(stickerClip, StickerAnimation.TYPE_ANIMATION_IN, clipDuration);
                } else if (outAnimation != null) {
                    StickerCommand.changeAnimationDuration(stickerClip, StickerAnimation.TYPE_ANIMATION_OUT, clipDuration);
                }
            }

        }
    }

    public void timelineAddOrSubtract(long startTime, long changeTime) {
        for (int i = 0; i < meicamTimeline.getStickerCaptionTrackCount(); i++) {
            MeicamStickerCaptionTrack meicamStickerCaptionTrack = meicamTimeline.findStickCaptionTrack(i);
            if (meicamStickerCaptionTrack == null) {
                continue;
            }
            for (int j = 0; j < meicamStickerCaptionTrack.getClipCount(); j++) {
                ClipInfo<?> clipInfo = meicamStickerCaptionTrack.getCaptionStickerClip(j);
                operateTimelineRangChange(clipInfo, startTime, changeTime);
            }
        }
    }

    private void operateTimelineRangChange(ClipInfo<?> clipInfo, long thingTime, long timeChanged) {
        long clipInPoint = clipInfo.getInPoint();
        long clipOutPoint = clipInfo.getOutPoint();
        if (timeChanged > 0) {
            //时间线变长
            //The timeline gets longer
            if (clipOutPoint > thingTime) {
                if (clipInPoint >= thingTime) {
//                    Log.e(TAG, "operateTimelineRangChange: 在变长后面，平移");
                    updateBaseItemRange(clipInfo, clipInfo.getInPoint() + timeChanged,
                            clipInfo.getOutPoint() + timeChanged);
                } else {
//                    Log.e(TAG, "operateTimelineRangChange: 横跨了变长");
                    updateBaseItemRange(clipInfo, clipInfo.getInPoint(),
                            clipInfo.getOutPoint() + timeChanged);
                }
            }
        } else if (timeChanged < 0) {
            //时间线变短
            //The timeline gets shorter
            if (clipOutPoint > thingTime) {
                if (clipInPoint > thingTime) {
//                    Log.e(TAG, "operateTimelineRangChange: 在变短后面，平移");
                    updateBaseItemRange(clipInfo, clipInfo.getInPoint() + timeChanged,
                            clipInfo.getOutPoint() + timeChanged);
                } else if (clipInPoint < thingTime) {
//                    Log.e(TAG, "operateTimelineRangChange: 横跨了变短");
                    long canChangTime = thingTime - clipInPoint;
                    long translateTime = 0;
                    if (canChangTime + timeChanged >= 0) {
//                        Log.e(TAG, "operateTimelineRangChange: 就少这么多");
                        canChangTime = -timeChanged;
                    } else {
//                        Log.e(TAG, "operateTimelineRangChange: 少多了，平移一些");
                        translateTime = timeChanged + canChangTime;
                    }
//                    Log.e(TAG, "operateTimelineRangChange: " + canChangTime + "  " + translateTime);
                    updateBaseItemRange(clipInfo, clipInfo.getInPoint() + translateTime,
                            clipInfo.getOutPoint() - canChangTime + translateTime);
                }
            } else {
                //变短的范围有相交，计算相交区域
                //The shorter range has intersection, and the intersection area is calculated
                if (clipInPoint > (thingTime + timeChanged) + BASEITEM_MIN_DURATION) {
//                    Log.e(TAG, "operateTimelineRangChange: 短的有点多222，删了");
                    removeCaptionStickClip(clipInfo);
                } else if (clipOutPoint > (thingTime + timeChanged)) {
//                    Log.e(TAG, "operateTimelineRangChange: 正常变短222");
                    updateBaseItemRange(clipInfo, clipInfo.getInPoint(),
                            (thingTime + timeChanged));
                }
            }
        }
    }

    /**
     * 发生timeline时间区间：p1-p2
     * 变化后timeline时间区间：p3-p4
     *
     * 交换的另一个区间为： p2-p4
     * 变化后timeline时间区间：p1-p3
     *
     * 字幕时间如果包含p1，字幕在p1 截断
     * 字幕时间如果包含p2，字幕在p2 截断
     * 字幕时间如果包含p4，字幕在p4 截断
     * 截取：以视频的前端为主，后面的删除，保持头部相对主轨画面一致
     * 然后
     * 在区间p1-p2的字幕移动到p3-p4
     * 在区间p2-p4的字幕移动到p1-p3
     *
     * Timeline time interval: p1-p2
     * Timeline time interval after change: p3-p4
     *
     * Another interval for swapping is: p2-p4
     * Timeline time interval after change: p1-p3
     *
     * If the subtitle time contains p1, the subtitle is truncated at p1
     * If the subtitle time contains p2, the subtitle is truncated at p2
     * If the subtitle time contains p4, the subtitle is truncated at p4
     * And then
     * Subtitles in intervals P1-P2 are moved to P3-P4
     * Subtitles in intervals p2-p4 are moved to p1-p3
     */
    public void timeRangeExchange(long p1, long p2, long p4) {
        long minClipDuration = BASEITEM_MIN_DURATION;
        long p1p2Offset = p4 - p2;
        long p2p4Offset = p1 - p2;
        for (int i = 0; i < meicamTimeline.getStickerCaptionTrackCount(); i++) {
            MeicamStickerCaptionTrack meicamStickerCaptionTrack = meicamTimeline.findStickCaptionTrack(i);
            if (meicamStickerCaptionTrack == null) {
                LogUtils.ex("timeRangeExchange findStickCaptionTrack index is NULL!: " + i);
                continue;
            }
            for (int j = 0; j < meicamStickerCaptionTrack.getClipCount(); j++) {
                ClipInfo<?> clipInfo = meicamStickerCaptionTrack.getCaptionStickerClip(j);
                long itemInPoint = clipInfo.getInPoint();
                long itemOutPoint = clipInfo.getOutPoint();
                if (itemInPoint > p4 || itemOutPoint < p1 || (itemInPoint < p1 && itemOutPoint > p4)) {
                    // 不在变化区间，无影响
                    // Not in the change interval, no effect
                    continue;
                } else if (itemInPoint >= p1 && itemOutPoint <= p2) {
                    //p1-p2区间，跟随整体后移
                    //The p1-p2 interval, moving backward with the whole
                    updateBaseItemRange(clipInfo, clipInfo.getInPoint() + p1p2Offset,
                            clipInfo.getOutPoint() + p1p2Offset);
                } else if (p2 <= itemInPoint && p4 >= itemOutPoint) {
                    //p2-p4区间，跟随整体前移
                    //The p2-p4 interval, which moves forward with the whole
                    updateBaseItemRange(clipInfo, clipInfo.getInPoint() + p2p4Offset,
                            clipInfo.getOutPoint() + p2p4Offset);
                } else {
                    if (itemInPoint < p1 && itemOutPoint > p2) {
                        //和前面片段交叉，截取加移动
                        //Cross with the previous clip, intercept and move
                        long curDuration = p1 - itemInPoint;
                        if (curDuration < minClipDuration) {
                            removeCaptionStickClip(clipInfo);
                        } else {
                            updateBaseItemRange(clipInfo, clipInfo.getInPoint(), p1);
                        }
                    } else if (itemInPoint < p2 && itemOutPoint > p2) {
                        //中交叉，截取加移动
                        //Medium crossing, interception plus movement
                        long curDuration = p2 - itemInPoint;
                        if (curDuration < minClipDuration) {
                            removeCaptionStickClip(clipInfo);
                        } else {
                            updateBaseItemRange(clipInfo, itemInPoint + p1p2Offset, p2 + p1p2Offset);
                        }
                    } else if (itemInPoint < p4 && itemOutPoint > p4) {
                        //后交叉，截取加移动
                        //After crossing, intercept plus move
                        long curDuration = p4 - itemInPoint;
                        if (curDuration < minClipDuration) {
                            removeCaptionStickClip(clipInfo);
                        } else {
                            updateBaseItemRange(clipInfo, itemInPoint + p2p4Offset, p4 + p2p4Offset);
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取视频轨道的时长
     * Get video track duration
     *
     * @return the other track max duration
     */
    public long getVideoTrackDuration(int trackIndex) {
        if (meicamTimeline == null) {
            return 0;
        }
        MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(trackIndex);
        if (videoTrack != null) {
            return videoTrack.getDuration();
        }
        return 0;
    }

    /**
     * Gets other track max duration.
     * 获取其他轨道最大时长
     *
     * @return the other track max duration
     */
    public long getOtherTrackMaxDuration() {
        long max = 0;
        long maxPip = getPipTrackMaxDuration();
        max = Math.max(max, maxPip);
        long maxAudio = getAudioTrackMaxDuration();
        max = Math.max(max, maxAudio);
        long maxStickerAndCaption = getStickerAndCaptionTrackMaxDuration();
        max = Math.max(max, maxStickerAndCaption);
        long maxTimelineEffect = getTimelineEffectDuration();
        max = Math.max(max, maxTimelineEffect);
        long maxTimelineFilter = getTimelineFilterAndAdjustDuration();
        max = Math.max(max, maxTimelineFilter);
        return max;
    }

    private long getTimelineFilterAndAdjustDuration() {
        long max = 0;
        if (meicamTimeline != null) {
            for (int i = meicamTimeline.getFilterAndAdjustTimelineTracksCount() - 1; i >= 0; i--) {
                MeicamTimelineVideoFxTrack meicamTimelineVideoFxTrack = meicamTimeline.getFilterAndAdjustTimelineTrack(i);
                if (meicamTimelineVideoFxTrack == null) {
                    continue;
                }
                MeicamTimelineVideoFilterAndAdjustClip lastVideoClip = meicamTimelineVideoFxTrack.getFilterAndAdjustClip(meicamTimelineVideoFxTrack.getFilterAndAdjustCount() - 1);
                if (lastVideoClip == null) {
                    continue;
                }
                max = Math.max(lastVideoClip.getOutPoint(), max);
            }
        }
        return max;
    }

    private long getTimelineEffectDuration() {
        long max = 0;
        if (meicamTimeline != null) {
            for (int i = meicamTimeline.getTimelineFxTrackCount() - 1; i >= 0; i--) {
                MeicamTimelineVideoFxTrack meicamTimelineVideoFxTrack = meicamTimeline.getTimelineFxTrack(i);
                if (meicamTimelineVideoFxTrack == null) {
                    continue;
                }
                MeicamTimelineVideoFxClip lastVideoClip = meicamTimelineVideoFxTrack.getClip(meicamTimelineVideoFxTrack.getClipCount() - 1);
                if (lastVideoClip == null) {
                    continue;
                }
                max = Math.max(lastVideoClip.getOutPoint(), max);
            }
        }
        return max;
    }

    /**
     * Gets pip track max duration.
     * 获取画中画轨道最大时长
     *
     * @return the pip track max duration
     */
    private long getPipTrackMaxDuration() {
        long max = 0;
        if (meicamTimeline != null) {
            for (int i = meicamTimeline.videoTrackCount() - 1; i >= 1; i--) {
                MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(i);
                if (videoTrack == null) {
                    continue;
                }
                MeicamVideoClip lastVideoClip = videoTrack.getVideoClip(videoTrack.getClipCount() - 1);
                if (lastVideoClip == null) {
                    continue;
                }
                max = Math.max(lastVideoClip.getOutPoint(), max);
            }
        }
        return max;
    }

    /**
     * Gets audio track max duration.
     * 获取音频轨道最大时长
     *
     * @return the audio track max duration
     */
    private long getAudioTrackMaxDuration() {
        long max = 0;
        if (meicamTimeline != null) {
            for (int i = meicamTimeline.getAudioTrackCount() - 1; i >= 0; i--) {
                MeicamAudioTrack audioTrack = meicamTimeline.getAudioTrack(i);
                if (audioTrack == null) {
                    continue;
                }
                MeicamAudioClip lastClip = audioTrack.getAudioClip(audioTrack.getClipCount() - 1);
                if (lastClip == null) {
                    continue;
                }
                max = Math.max(lastClip.getOutPoint(), max);
            }
        }
        return max;
    }

    /**
     * Gets sticker and caption track max duration.
     * 获取 贴纸，组合字幕轨道最大时长
     *
     * @return the sticker and caption track max duration
     */
    private long getStickerAndCaptionTrackMaxDuration() {
        long max = 0;
        if (meicamTimeline != null) {
            for (int i = 0; i < meicamTimeline.getStickerCaptionTrackCount(); i++) {
                MeicamStickerCaptionTrack meicamStickerCaptionTrack = meicamTimeline.findStickCaptionTrack(i);
                if (meicamStickerCaptionTrack == null) {
                    continue;
                }
                for (int j = 0; j < meicamStickerCaptionTrack.getClipCount(); j++) {
                    ClipInfo<?> clipInfo = meicamStickerCaptionTrack.getCaptionStickerClip(j);
                    if (isAICaption(clipInfo)) {
                        continue;
                    }
                    max = Math.max(clipInfo.getOutPoint(), max);
                }

            }
        }
        return max;
    }


    /**
     * 时间线变化时的回调
     * The interface On timeline change listener.
     */
    public interface OnTimelineChangeListener {

        //删除clip Delete the clip
        int TYPE_DELETE_CLIP = 1;
        //复制clip Copy the clip
        int TYPE_COPY_CLIP = 2;
        //分割clip Split clip
        int TYPE_CUT_CLIP = 3;
        //定格clip Stop motion clip
        int TYPE_FREEZE_CLIP = 4;
        //倒放clip Backwards clip
        int TYPE_REVERT_CLIP = 5;

        /**
         * 当时间线内容变化的回调
         * On timeline changed.
         *
         * @param timeline the timeline
         * @param needSave 是否保存草稿。the need save
         */
        void onTimelineChanged(MeicamTimeline timeline, boolean needSave);

        /**
         * 轨道上的片段选中变化时触发
         * On need track select changed.
         *
         * @param trackIndex 对应的轨道index。the track index
         * @param inPoint    片段在轨道上的入点，通过入点获取clip。the in point
         */
        void onNeedTrackSelectChanged(int trackIndex, long inPoint);

        void onTips(int code);

        /**
         * 刷新轨道控件
         * Refresh editor timeline view.
         *
         * @param type 触发轨道变化的操作。the type
         */
        void refreshEditorTimelineView(int type);

        /**
         * 保存草稿的操作
         * On save operation.
         */
        void onSaveOperation();

        /**
         * 添加贴纸、字幕、fx(特效、滤镜等)的操作
         * On add sticker caption pic fx.
         *
         * @param object 添加的对象。the object
         * @param type   添加的类型。the type
         */
        void onAddStickerCaptionPicFx(Object object, int type);

    }

    /**
     * 轨道变化的回调
     * The interface On track change listener.
     */
    public interface OnTrackChangeListener {
        /**
         * 音频的切割操作
         * Audio edit cut clip.
         *
         * @param nvsAudioTrack 音频轨道。the nvs audio track
         * @param inPoint       需要切割的时间点。the in point
         */
        void audioEditCutClip(MeicamAudioTrack nvsAudioTrack, long inPoint);

        /**
         * 音频的删除操作，删除的是上层报错的当前选中的片段
         * Audio edit delete clip.
         *
         * @param success Whether the delete is success or not. true:yes; false: no
         */
        void audioEditDeleteClip(boolean success);

        /**
         * 复制上层保存的当前的片段
         * Audio edit copy clip.
         *
         * @param inPoint      复制后新片段的入点。the in point
         * @param newAudioClip the  new audio clip
         * @param audioTrack   the  audio track
         */
        void audioEditCopyClip(long inPoint, MeicamAudioClip newAudioClip, MeicamAudioTrack audioTrack);

    }

    public NvsStreamingContext getStreamingContext() {
        if (mStreamingContext == null) {
            mStreamingContext = initStreamContext();
        }
        return mStreamingContext;
    }


    /**
     * 引擎的停止操作。可以停止播放，生成等。
     * Stop.
     */
    public void stop() {
        mStreamingContext.stop();
    }

    /**
     * 判断当前是否是在播放中。
     * Is playing boolean.
     *
     * @return the boolean
     */
    public boolean isPlaying() {
        return mStreamingContext.getStreamingEngineState() == STREAMING_ENGINE_STATE_PLAYBACK;
    }


    public Transform getNvsTransform(MeicamVideoClip meicamVideoClip, long keyFrameAtTime) {
        Transform transform;
        Transform transformModel = meicamVideoClip.maskModel.transform;
        if (transformModel != null) {
            transform = transformModel.clone();
        } else {
            transform = new Transform();
        }
        float fxTransformX = 0.0f, fxTransformY = 0.0f, fxScaleX = 1.0f, fxScaleY = 1.0f, fxRotation = 0.0f;
        MeicamVideoFx propertyVideoFx = meicamVideoClip.findPropertyVideoFx();
        KeyFrameProcessor<NvsVideoFx> frameProcessor = propertyVideoFx.keyFrameProcessor();
        if (frameProcessor.getKeyFrameCount(NvsConstants.KEY_CROPPER_TRANS_X) > 0 && keyFrameAtTime >= 0 &&
                keyFrameAtTime <= (meicamVideoClip.getOutPoint() - meicamVideoClip.getInPoint())) {
            MeicamKeyFrame keyFrame = frameProcessor.getKeyFrame(keyFrameAtTime);
            List<MeicamFxParam<?>> keyFrameParams;
            if (keyFrame == null) {
                /*当前位置没有关键帧，获取需要的关键帧参数*/
                keyFrameParams = propertyVideoFx.keyFrameProcessor().getNearbyKeyFrameParamsAtTime(NvsConstants.KEY_CROPPER_TRANS_X, keyFrameAtTime);
            } else {
                /*当前位置有关键帧，获取需要的关键帧参数*/
                keyFrameParams = keyFrame.getParams();
            }
            for (MeicamFxParam<?> meicamFxParam : keyFrameParams) {
                if (meicamFxParam == null) {
                    continue;
                }
                String key = meicamFxParam.getKey();
                if (MeicamKeyFrame.SCALE_X.equals(key)) {
                    fxScaleX = meicamFxParam.getFloatValue();
                } else if (MeicamKeyFrame.SCALE_Y.equals(key)) {
                    fxScaleY = meicamFxParam.getFloatValue();
                } else if (MeicamKeyFrame.TRANS_X.equals(key)) {
                    fxTransformX = meicamFxParam.getFloatValue();
                } else if (MeicamKeyFrame.TRANS_Y.equals(key)) {
                    fxTransformY = meicamFxParam.getFloatValue();
                } else if (MeicamKeyFrame.ROTATION.equals(key)) {
                    fxRotation = meicamFxParam.getFloatValue();
                }
            }
        } else {
            fxTransformX = propertyVideoFx.getFloatVal(NvsConstants.FX_TRANSFORM_2D_TRANS_X, 0);
            fxTransformY = propertyVideoFx.getFloatVal(NvsConstants.FX_TRANSFORM_2D_TRANS_Y, 0);
            fxRotation = propertyVideoFx.getFloatVal(NvsConstants.FX_TRANSFORM_2D_ROTATION, 0);
            fxScaleX = propertyVideoFx.getFloatVal(NvsConstants.FX_TRANSFORM_2D_SCALE_X, 1);
            fxScaleY = propertyVideoFx.getFloatVal(NvsConstants.FX_TRANSFORM_2D_SCALE_Y, 1);
        }
        transform.transformX = fxTransformX;
        transform.transformY = fxTransformY;
        transform.scaleX = fxScaleX;
        transform.scaleY = fxScaleY;
        transform.rotation = fxRotation;
        setMaskModel(meicamVideoClip, keyFrameAtTime);
        return transform;
    }

    public void setMaskModel(MeicamVideoClip meicamVideoClip, long keyFrameAtTime) {
        MeicamVideoFx keyFrameHolder = getMaskTargetFx(meicamVideoClip);
        if (keyFrameHolder == null) {
            return;
        }
        KeyFrameProcessor<NvsVideoFx> frameProcessor = keyFrameHolder.keyFrameProcessor();
        if (frameProcessor.getKeyFrameCount(NvsConstants.KEY_PROPERTY_MASK_REGION_INFO) > 0 && keyFrameAtTime >= 0 &&
                keyFrameAtTime <= (meicamVideoClip.getOutPoint() - meicamVideoClip.getInPoint())) {
            MeicamKeyFrame keyFrame = frameProcessor.getKeyFrame(keyFrameAtTime);
            List<MeicamFxParam<?>> keyFrameParams;
            if (keyFrame == null) {
                /*当前位置没有关键帧，获取需要的关键帧参数*/
                keyFrameParams = frameProcessor.getNearbyKeyFrameParamsAtTime(NvsConstants.KEY_PROPERTY_MASK_REGION_INFO, keyFrameAtTime);
            } else {
                /*当前位置有关键帧，获取需要的关键帧参数*/
                keyFrameParams = keyFrame.getParams();
            }
            for (MeicamFxParam<?> meicamFxParam : keyFrameParams) {
                if (meicamFxParam == null) {
                    continue;
                }
                String key = meicamFxParam.getKey();
                if (NvsConstants.KEY_PROPERTY_MASK_REGION_INFO.equals(key)) {
                    Object value = meicamFxParam.getValue();
                    if (value instanceof MeicamMaskRegionInfo) {
                        PointF viewSizeInLiveWindow = getViewSizeInLiveWindow((int)meicamVideoClip.getRectWidth(), (int)meicamVideoClip.getRectHeight());
                        getMaskModel((MeicamMaskRegionInfo) value, meicamVideoClip.maskModel, viewSizeInLiveWindow);
                    }
                }
            }
        }
    }

    private void getMaskModel(MeicamMaskRegionInfo meicamMaskRegionInfo, NvMaskModel maskModel, PointF clipSize) {
        NvsMaskRegionInfo info = meicamMaskRegionInfo.getMaskRegionInfo();
        List<NvsMaskRegionInfo.RegionInfo> regionInfoArray = info.getRegionInfoArray();
        NvsMaskRegionInfo.RegionInfo subRegionInfo = regionInfoArray.get(0);
        List<NvsPosition2D> nvsPosition2DS = subRegionInfo.getPoints();
        int type = MaskRegionInfoData.MaskType.NONE;
        int pointSize = subRegionInfo.getPoints().size();
        maskModel.transform.viewScale = 1F;
        if (subRegionInfo.getType() == 0) {
            //线性、镜像、五角星
            if (pointSize == 5) {
                type = MaskRegionInfoData.MaskType.LINE;
                maskModel.transform.scaleX = 1;
                maskModel.transform.scaleY = 1;
                NvsPosition2D leftTop = nvsPosition2DS.get(0);
                leftTop = mapNormalizedToTimeline(leftTop, clipSize);
                NvsPosition2D rightTop = nvsPosition2DS.get(1);
                rightTop = mapNormalizedToTimeline(rightTop, clipSize);
                //求线角度
                maskModel.transform.rotation = (float) Math.toDegrees(Math.atan2(rightTop.y - leftTop.y, rightTop.x - leftTop.x));
                NvsPosition2D topCenter = nvsPosition2DS.get(3);
                maskModel.transform.transformX = topCenter.x * clipSize.x * 0.5f;
                maskModel.transform.transformY = topCenter.y * clipSize.y * 0.5f;
            } else if (pointSize == 6) {
                type = MaskRegionInfoData.MaskType.MIRROR;
                NvsPosition2D leftTop = nvsPosition2DS.get(0);
                leftTop = mapNormalizedToTimeline(leftTop, clipSize);
                NvsPosition2D topCenter = nvsPosition2DS.get(1);
                NvsPosition2D bottomCenter = nvsPosition2DS.get(4);
                PointF maskCenter = new PointF((bottomCenter.x + topCenter.x) * 0.5f, (bottomCenter.y + topCenter.y) * 0.5f);
                topCenter = mapNormalizedToTimeline(topCenter, clipSize);
                maskModel.transform.rotation = (float) Math.toDegrees(Math.atan2(topCenter.y - leftTop.y, topCenter.x - leftTop.x));
                NvsPosition2D leftBottom = nvsPosition2DS.get(5);
                leftBottom = mapNormalizedToTimeline(leftBottom, clipSize);
                float distance = NvLineTool.twoPointDistance(leftBottom, leftTop);
                maskModel.transform.scaleX = distance / (clipSize.y * 0.5f);
                maskModel.transform.scaleY = maskModel.transform.scaleX;
                maskModel.transform.transformX = maskCenter.x * clipSize.x * 0.5f;
                maskModel.transform.transformY = maskCenter.y * clipSize.y * 0.5f;
            } else if (pointSize == 10) {
                type = MaskRegionInfoData.MaskType.STAR;
                NvsPosition2D point0 = nvsPosition2DS.get(0);
                point0 = mapNormalizedToTimeline(point0, clipSize);
                NvsPosition2D point4 = nvsPosition2DS.get(4);
                point4 = mapNormalizedToTimeline(point4, clipSize);
                NvsPosition2D point6 = nvsPosition2DS.get(6);
                point6 = mapNormalizedToTimeline(point6, clipSize);
                PointF centPoint = NvLineTool.calculateExcircle(point0, point4, point6);
                PointF tCenterPoint = centPoint;
                centPoint = mapTimelineToNormalized(centPoint, clipSize);
                maskModel.transform.transformX = centPoint.x * clipSize.x * 0.5f;
                maskModel.transform.transformY = centPoint.y * clipSize.y * 0.5f;
                //求线角度
                maskModel.transform.rotation = (float) Math.toDegrees(Math.atan2(point6.y - point0.y, point6.x - point0.x));
                //计算scale和中心点
                float currentRadius = NvLineTool.twoPointDistance(new NvsPosition2D(tCenterPoint.x, tCenterPoint.y), point0);
                float assetAspectRatio = clipSize.x / clipSize.y;
                float maskSquareWidth = assetAspectRatio >= 1 ? clipSize.y : clipSize.x;
                maskModel.transform.scaleX = currentRadius / (maskSquareWidth * 0.5f);
                maskModel.transform.scaleY = maskModel.transform.scaleX;
            }
        } else if (subRegionInfo.getType() == 1) {
            if (pointSize == 24) {
                type = MaskRegionInfoData.MaskType.RECT;
                NvsPosition2D point3 = nvsPosition2DS.get(3);
                point3 = mapNormalizedToTimeline(point3, clipSize);
                NvsPosition2D point6 = nvsPosition2DS.get(6);
                point6 = mapNormalizedToTimeline(point6, clipSize);
                NvsPosition2D point9 = nvsPosition2DS.get(9);
                point9 = mapNormalizedToTimeline(point9, clipSize);
                NvsPosition2D point12 = nvsPosition2DS.get(12);
                point12 = mapNormalizedToTimeline(point12, clipSize);
                NvsPosition2D point18 = nvsPosition2DS.get(18);
                point18 = mapNormalizedToTimeline(point18, clipSize);
                //求线角度
                //maskModel.transform.rotation = (float) Math.toDegrees(Math.atan2(point9.y - point6.y, point9.x - point6.x));
                NvsMaskRegionInfo.Transform2D transform2D = subRegionInfo.getTransform2D();
                maskModel.transform.rotation = -transform2D.getRotation();
                NvsPosition2D scaleX = transform2D.getScale();
                NvsPosition2D translation = transform2D.getTranslation();
                maskModel.transform.scaleX = scaleX.x;
                maskModel.transform.scaleY = scaleX.y;
                //PointF maskCenter = new PointF((point18.x + point6.x) * 0.5f, (point18.y + point6.y) * 0.5f);
                maskModel.transform.transformX = translation.x * clipSize.x * 0.5f;
                maskModel.transform.transformY = translation.y * clipSize.y * 0.5f;
                PointF pedalPoint = NvLineTool.pedalPoint(point9, point6, point3);
                float radius = NvLineTool.twoPointDistance(new NvsPosition2D(pedalPoint.x, pedalPoint.y), point3) * maskModel.transform.scaleX;
                float width = NvLineTool.twoPointDistance(point12, point3) * maskModel.transform.scaleX;
                float height = NvLineTool.twoPointDistance(point18, point9) * maskModel.transform.scaleX;
                if (width > height) {
                    maskModel.cornerRadiusRate = radius / height * 2.0f;
                } else {
                    maskModel.cornerRadiusRate = radius / width * 2.0f;
                }
                float rectRate = maskModel.rectRate;
                //float assetAspectRatio = clipSize.x / clipSize.y;
                //float maskSquareWidth = assetAspectRatio >= 1 ? clipSize.y : clipSize.x;
                //float maskSquareHeight = maskSquareWidth / rectRate;

                float rate = width / height;
                if (rate >= rectRate) {
                    //以width计算scale
                    //maskModel.transform.scaleX = width / maskSquareWidth;
                    maskModel.horizontalScale = 1;
                    maskModel.verticalScale = height / (width / rectRate);
                } else {
                    //maskModel.transform.scaleX = height / maskSquareHeight;
                    maskModel.horizontalScale = width / (height * rectRate);
                    maskModel.verticalScale = 1;
                }
            } else if (pointSize == 6) {
                type = MaskRegionInfoData.MaskType.HEART;
                float assetAspectRatio = clipSize.x / clipSize.y;
                float maskSquareWidth = assetAspectRatio >= 1 ? clipSize.y : clipSize.x;
                float radius = maskSquareWidth / 2;
                NvsPosition2D topIntersectionPoint = nvsPosition2DS.get(0);
                topIntersectionPoint = mapNormalizedToTimeline(topIntersectionPoint, clipSize);
                NvsPosition2D bottomIntersectionPoint = nvsPosition2DS.get(3);
                bottomIntersectionPoint = mapNormalizedToTimeline(bottomIntersectionPoint, clipSize);
                float distance = NvLineTool.twoPointDistance(bottomIntersectionPoint, topIntersectionPoint);
                float currentRadius = distance / 4 * 3f;
                maskModel.transform.scaleX = currentRadius / radius;
                maskModel.transform.scaleY = maskModel.transform.scaleX;
                //求线角度
                float lineRotation = (float) Math.atan2(bottomIntersectionPoint.y - topIntersectionPoint.y, bottomIntersectionPoint.x - topIntersectionPoint.x);
                maskModel.transform.rotation = (float) Math.toDegrees(lineRotation - Math.PI * 0.5f);
                NvsPosition2D maskCenter = NvLineTool.calculateIntersection(bottomIntersectionPoint, topIntersectionPoint, distance / 4.0f);
                PointF maskNorCenter = mapTimelineToNormalized(new PointF(maskCenter.x, maskCenter.y), clipSize);
                maskModel.transform.transformX = maskNorCenter.x * clipSize.x * 0.5f;
                maskModel.transform.transformY = maskNorCenter.y * clipSize.y * 0.5f;
            }
        } else if (subRegionInfo.getType() == 2) {
            type = MaskRegionInfoData.MaskType.CIRCLE;
            NvsMaskRegionInfo.Transform2D transform2d = subRegionInfo.getTransform2D();
            maskModel.transform.rotation = -transform2d.getRotation();
            NvsPosition2D normalizedCenter = subRegionInfo.getEllipse2D().getCenter();
            maskModel.transform.transformX = normalizedCenter.x * clipSize.x * 0.5f;
            maskModel.transform.transformY = normalizedCenter.y * clipSize.y * 0.5f;
            float assetAspectRatio = clipSize.x / clipSize.y;
            float maskSquareWidth = assetAspectRatio >= 1 ? clipSize.y : clipSize.x;
            maskSquareWidth *= maskModel.circleRate;
            maskSquareWidth *= 0.5f;
            float width = (subRegionInfo.getEllipse2D().getA()) * (clipSize.x * 0.5f);
            float height = (subRegionInfo.getEllipse2D().getB()) * (clipSize.y * 0.5f);
            if (width > height) {
                maskModel.horizontalScale = 1;
                maskModel.verticalScale = height / width;
            } else {
                maskModel.horizontalScale = width / height;
                maskModel.verticalScale = 1;
            }
            float maxLength = Math.max(width, height);
            maskModel.transform.scaleX = maxLength / maskSquareWidth;
            maskModel.transform.scaleY = maskModel.transform.scaleX;
        }
        maskModel.transform.transformY *= -1;
        maskModel.maskType = type;
        MeicamMaskRegionInfo regionInfo = new MeicamMaskRegionInfo();
        regionInfo.setRegionInfo(info);
        maskModel.regionInfo = regionInfo;
    }

    private PointF mapTimelineToNormalized(PointF point, PointF size) {
        float halfWidth = size.x * 0.5f;
        float halfHeight = size.y * 0.5f;
        return new PointF((point.x - halfWidth) / halfWidth, (halfHeight - point.y) / halfHeight);
    }

    public static NvsPosition2D mapNormalizedToTimeline(NvsPosition2D point, PointF size) {
        float halfWidth = size.x * 0.5f;
        float halfHeight = size.y * 0.5f;
        return new NvsPosition2D(point.x * halfWidth + halfWidth, halfHeight - point.y * halfHeight);
    }


    public Transform getNvsTransform(MeicamVideoClip meicamVideoClip) {
        return getNvsTransform(meicamVideoClip, getCurrentTimelinePosition() - meicamVideoClip.getInPoint());
    }

    @Override
    public boolean deleteAudioClip(MeicamAudioClip audioClip) {
        if (meicamTimeline == null) {
            LogUtils.e("timeline is null");
            return false;
        }
        if (audioClip == null) {
            LogUtils.e("selected audio clip is null");
            return false;
        }
        int trackIndex = audioClip.getTrackIndex();
        MeicamAudioTrack meicamAudioTrack = meicamTimeline.getAudioTrack(trackIndex);
        MeicamAudioClip meicamAudioClip = null;
        if (meicamAudioTrack != null) {
            meicamAudioClip = AudioTrackCommand.removeAudioClip(meicamAudioTrack, audioClip.getIndex(), true);
        }
        //删除尾部的空轨道
        // Delete the empty track at the end
        removeEmptyAudioTrackInTheEnd();
        return meicamAudioClip != null;
    }

    /**
     * 移除蒙版效果
     * <p>
     * Remove mask effect.
     *
     * @param meicamVideoClip 视频Clip. video clip data.
     */
    public void removeMask(MeicamVideoClip meicamVideoClip) {
        MeicamVideoFx targetVideoFx = getMaskTargetFx(meicamVideoClip);
        if (null != targetVideoFx) {
            meicamVideoClip.maskModel.maskType = 0;
            MeicamMaskRegionInfo nvsMaskRegionInfo = new MeicamMaskRegionInfo();
            MeicamMaskRegionInfo.RegionInfo regionInfo = new MeicamMaskRegionInfo.RegionInfo(NvsMaskRegionInfo.MASK_REGION_TYPE_POLYGON);
            List<MeicamPosition2D> positions = new ArrayList<>();
            positions.add(new MeicamPosition2D(-1, 1));
            positions.add(new MeicamPosition2D(-1, -1));
            positions.add(new MeicamPosition2D(1, -1));
            positions.add(new MeicamPosition2D(1, 1));
            regionInfo.setPoints(positions);
            nvsMaskRegionInfo.addRegionInfo(regionInfo);
            targetVideoFx.setObjectVal(NvsConstants.KEY_PROPERTY_MASK_REGION_INFO, nvsMaskRegionInfo);
            //meicamVideoClip.removeVideoFx(TYPE_RAW_BUILTIN, SUB_TYPE_MASK);
        }
    }

    /**
     * 获取蒙版对应的特效对象
     * <p>
     * Get mask object.
     *
     * @param meicamVideoClip 视频Clip. video clip data.
     * @return 蒙版特效对象 mask object
     */
    public MeicamVideoFx getMaskTargetFx(MeicamVideoClip meicamVideoClip) {
        if (meicamVideoClip == null) {
            LogUtils.e("getMaskTargetFx meicamVideoClip==null");
            return null;
        }
        return meicamVideoClip.findPropertyVideoFx();
        //return meicamVideoClip.getVideoFx(SUB_TYPE_MASK, NvsConstants.KEY_MASK_GENERATOR);
    }

    /**
     * 检查是否加过美型
     * Check to see if the beauty type has been added
     */
    public void checkBeautyShape() {
        if (meicamTimeline == null) {
            return;
        }
        int count = meicamTimeline.videoTrackCount();
        if (count > 0) {
            for (int index = 0; index < count; index++) {
                MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(index);
                if (videoTrack != null) {
                    for (int i = 0; i < videoTrack.getClipCount(); i++) {
                        MeicamVideoClip videoClip = videoTrack.getVideoClip(i);
                        if (videoClip != null && videoClip.hasBeautyShape()) {
                            setUseBeautyFaceShape(true);
                            return;
                        }
                    }
                }
            }
        }
        setUseBeautyFaceShape(false);
    }

    public void setUseBeautyFaceShape(boolean useBeautyFaceShape) {
        this.useFaceShape = useBeautyFaceShape;
    }

    public boolean isUseFaceShape() {
        return useFaceShape;
    }

    public static class PointXComparator implements Comparator<PointF> {

        @Override
        public int compare(PointF bean1, PointF bean2) {
            return (int) (bean1.x - bean2.x);
        }

    }

    /**
     * set live window size.
     * 设置liveWindrow大小，切换比例的时候要调用
     *
     * @param liveWindow the live window
     */
    public void setLiveWindrowSize(NvsLiveWindowExt liveWindow) {
        mLiveWindowSize = frameForTimeline(getCurrentTimeline(), liveWindow.getWidth(), liveWindow.getHeight());
    }

    /**
     * Gets live window size.
     * 获取liveWindrow大小，虽然现在不再改变liveWindrow的大小，但是依然沿用以前的liveWindrow的计算方法
     *
     * @param liveWindow the live window
     * @return the live windrow size
     */
    public PointF getLiveWindrowSize(NvsLiveWindowExt liveWindow) {
        if (mLiveWindowSize == null) {
            mLiveWindowSize = frameForTimeline(getCurrentTimeline(), liveWindow.getWidth(), liveWindow.getHeight());
        }
        return mLiveWindowSize;
    }


    /**
     * 根据比例，获取某一范围内的大小
     * Gets view size in size.
     *
     * @param size  the size
     * @param ratio the ratio
     * @return the view size in size
     */
    public PointF getViewSizeInSize(PointF size, float ratio) {
        PointF pointF = new PointF();
        float liveWindowRatio = size.x / size.y;
        if (liveWindowRatio > ratio) {
            pointF.y = size.y;
            pointF.x = pointF.y * ratio;
        } else {
            pointF.x = size.x;
            pointF.y = pointF.x / ratio;
        }
        return pointF;
    }

    /**
     * 由View视图坐标系下的宽高转化成在live window中的宽高
     * From the width and height of the View coordinate system to the width and height of the Live Window
     *
     * @param ratio 宽高比例
     */
    public PointF getViewSizeInLiveWindow(float ratio) {
        return getViewSizeInSize(mLiveWindowSize, ratio);
    }


    /**
     * 由View视图坐标系下的宽高转化成在live window中的宽高
     * From the width and height of the View coordinate system to the width and height of the Live Window
     *
     * @param width  the view width视图宽
     * @param height the view height 视图高
     */
    public PointF getViewSizeInLiveWindow(int width, int height) {
        float ratio = width * 1.0f / height;
        return getViewSizeInSize(mLiveWindowSize, ratio);
    }

    /**
     * Frame for timeline point f.
     * 根据timeline比例计算timeline 展示size
     *
     * @return the point f
     */
    private static PointF frameForTimeline(MeicamTimeline timeline, int width, int height) {
        if (timeline == null) {
            LogUtils.e("timeline is null");
            return null;
        }
        NvsVideoResolution videoResolution = timeline.getVideoResolution();
        float timelineRatio = videoResolution.imageWidth * 1.0F / videoResolution.imageHeight;
        float viewRatio = width * 1.0F / height;
        PointF rect = new PointF();
        if (timelineRatio > viewRatio) {//宽对齐
            rect.x = width;
            rect.y = width / timelineRatio;
        } else {
            rect.y = height;
            rect.x = height * timelineRatio;
        }
        return rect;
    }

    /**
     * 获取当前的时间线数据
     * Get current timeline data
     */
    public String getCurrentTimelineData() {
        if (meicamTimeline != null) {
            return meicamTimeline.toJson();
        }
        return "";
    }


    /**
     * 获取最大转场时长
     * Gets max duration by video clip.
     *
     * @param clipIndex the clip index
     * @return the max transition duration by video clip
     */
    public long getMaxTransitionDurationByVideoClip(int clipIndex) {
        if (meicamTimeline == null) {
            return 0;
        }
        MeicamVideoTrack meicamVideoTrack = meicamTimeline.getVideoTrack(MAIN_TRACK_INDEX);
        if (meicamVideoTrack == null) {
            return 0;
        }
        MeicamVideoClip meicamVideoClip = meicamVideoTrack.getVideoClip(clipIndex);
        if (meicamVideoClip == null) {
            return 0;
        }
        MeicamVideoClip meicamVideoClipAfter = meicamVideoTrack.getVideoClip(clipIndex + 1);
        if (meicamVideoClipAfter == null) {
            return 0;
        }
        return Math.min(meicamVideoClip.getOutPoint() - meicamVideoClip.getInPoint(), meicamVideoClipAfter.getOutPoint() - meicamVideoClipAfter.getInPoint()) / 2;

    }


    /**
     * 获取最大转场时长
     * Gets max duration by video clip.
     *
     * @param clipIndex the clip index
     * @return the max transition duration by video clip
     */
    public boolean canAddTransition(int clipIndex) {
        if (meicamTimeline == null) {
            return false;
        }
        MeicamVideoTrack meicamVideoTrack = meicamTimeline.getVideoTrack(MAIN_TRACK_INDEX);
        if (meicamVideoTrack == null) {
            return false;
        }
        MeicamVideoClip meicamVideoClip = meicamVideoTrack.getVideoClip(clipIndex);
        if (meicamVideoClip == null) {
            return false;
        }
        MeicamVideoClip meicamVideoClipAfter = meicamVideoTrack.getVideoClip(clipIndex + 1);
        if (meicamVideoClipAfter == null) {
            return false;
        }
        long duration = meicamVideoClip.getOutPoint() - meicamVideoClip.getInPoint();
        long durationAfter = meicamVideoClipAfter.getOutPoint() - meicamVideoClipAfter.getInPoint();
        long minDuration = getDurationByFrame(6);
        return duration >= minDuration && durationAfter >= minDuration;
    }

    /**
     * 根据帧数获取时长
     * Gets duration by frame.
     *
     * @param frame the frame
     * @return the duration by frame
     */
    public long getDurationByFrame(int frame) {
        NvsRational nvsRational = getVideoRational();
        if (nvsRational != null) {
            return CommonData.TIMEBASE / nvsRational.num * frame;
        }
        return 0;
    }

    /**
     * 清除Engine里的保存的数据，监听
     * clear.
     */
    public void clear() {
        meicamTimeline = null;
        mMeicamAudioTrack = null;
        mMeicamAudioClip = null;
        useFaceShape = false;
        if (mIconGenerator != null) {
            mIconGenerator.cancelTask(0);
            mIconGenerator.setIconCallback(null);
            mIconGenerator.release();
            mIconGenerator = null;
        }
    }

    /**
     * Remove OnTimelineChangeListener
     * <p>
     * 删除OnTimelineChangeListener回调
     *
     * <p>
     *
     * @param listener the listener
     */
    public void removeOnTimelineChangeListener(OnTimelineChangeListener listener) {
        if (mOnTimelineChangeListener == listener) {
            mOnTimelineChangeListener = null;
        }

    }

    /**
     * Remove OnTrackChangeListener
     * <p>
     * 删除OnTrackChangeListener回调
     *
     * <p>
     *
     * @param listener the listener
     */
    public void removeOnTrackChangeListener(OnTrackChangeListener listener) {
        if (mOnTrackChangeListener == listener) {
            mOnTrackChangeListener = null;
        }
    }

    /**
     * Gets frame pair.
     * 获取关键帧对
     *
     * @param keyFrameHolder the key frame holder
     * @param key            关键字 the key
     * @param time           时间 the time
     * @return 关键帧对the frame pair
     */
    public Pair<MeicamKeyFrame, MeicamKeyFrame> getFramePair(IKeyFrameProcessor<?> keyFrameHolder, String key, long time) {
        if (keyFrameHolder == null) {
            return null;
        }
        return keyFrameHolder.keyFrameProcessor().getFramePair(key, time);
    }

    /**
     * Add key frame curve
     * 添加关键帧曲线
     *
     * @param key         关键帧关键字 the key of key frame
     * @param atTime      时刻 the at time
     * @param startControlPoint 开始控制点 the start control point
     * @param endControlPoint   结束控制点  the end control point
     * @param id                曲线id  the curve id.
     * @return 操作是否成功 true:yes;false:no. Is the operation successful？true:yes;false:no
     */
    public boolean addKeyFrameCurve(IKeyFrameProcessor<?> keyFrameProcessor, String key, long atTime,
                                    PointF startControlPoint, PointF endControlPoint, int id) {
        return keyFrameProcessor != null && KeyFrameHolderCommand.addKeyFrameCurve(keyFrameProcessor, key, atTime,
                new FloatPoint(startControlPoint.x, startControlPoint.y),
                new FloatPoint(endControlPoint.x, endControlPoint.y), id);
    }

    /**
     * Alignment timeline fx duration.
     * 对齐timeline特效的时长
     */
    public void alignmentTimelineFxDuration() {
        if (meicamTimeline == null) {
            return;
        }
        MeicamTimelineVideoFxClip fromClipList = meicamTimeline.getTimelineFxFromClipList(0);
        if (fromClipList != null) {
            fromClipList.setOutPoint(meicamTimeline.getDuration());
        }
    }


    /**
     * Add prop.
     * 添加道具
     *
     * @param videoClip  the video clip
     * @param assetsPath the package path 道具包路径
     * @param packageId  the package id 道具包id
     */
    public void addProp(MeicamVideoClip videoClip, String assetsPath, String packageId) {
        if (videoClip == null) {
            return;
        }
        VideoClipCommand.setProp(videoClip, assetsPath, packageId);
    }

    /**
     * Delete prop.
     * 删除道具
     *
     * @param videoClip the video clip
     */
    public void deleteProp(MeicamVideoClip videoClip) {
        if (videoClip == null) {
            return;
        }
        VideoClipCommand.setProp(videoClip, "", null);
    }

    /**
     * Sets plug param.
     * 设置插件特效参数值
     *
     * @param meicamVideoFx         the meicam video fx
     * @param meicamTimelineVideoFx the meicam timeline video fx
     * @param param                 the param
     * @param inPoint               the inPoint
     */
    public void setPlugParam(MeicamVideoFx meicamVideoFx, MeicamTimelineVideoFxClip meicamTimelineVideoFx, PlugDetail.Param param
            , long inPoint) {
        String paramName = param.paramName;
        MeicamKeyFrame keyFrame = null;
        long atTime = meicamTimeline.getCurrentPosition() - inPoint;
        if (meicamVideoFx != null && meicamVideoFx.keyFrameProcessor().getKeyFrameCount(paramName) > 0) {
            keyFrame = meicamVideoFx.keyFrameProcessor().getKeyFrame(paramName, atTime);
        } else if (meicamTimelineVideoFx != null && meicamTimelineVideoFx.keyFrameProcessor().getKeyFrameCount(paramName) > 0) {
            keyFrame = meicamTimelineVideoFx.keyFrameProcessor().getKeyFrame(paramName, atTime);
        }

        String valueType = param.valueType;
        String valueDefault = param.valueDefault;

        if (Constants.PlugType.BOOL.equals(valueType)) {
            //bool形式的，需要UI checkBox
            boolean defaultValue = Boolean.parseBoolean(valueDefault);
            if (meicamTimelineVideoFx != null) {
                TimelineFxCommand.setBooleanVal(meicamTimelineVideoFx, paramName, defaultValue);
            } else if (meicamVideoFx != null) {
                VideoFxCommand.setBooleanVal(meicamVideoFx, paramName, defaultValue);
            }
        } else if (Constants.PlugType.FLOAT.equals(valueType)) {
            //float形式的，需要UI 轮盘
            float defaultValue = Float.parseFloat(valueDefault);
            if (keyFrame != null) {
                KeyFrameCommand.setFloatVal(keyFrame, paramName, defaultValue);
            } else if (meicamTimelineVideoFx != null) {
                TimelineFxCommand.setFloatVal(meicamTimelineVideoFx, paramName, defaultValue);
            } else if (meicamVideoFx != null) {
                VideoFxCommand.setFloatVal(meicamVideoFx, paramName, defaultValue);
            }

        } else if (Constants.PlugType.POSITION2D_X.equals(valueType)) {

            //float形式的，需要UI 轮盘
            float defaultValue = Float.parseFloat(valueDefault);
            if (keyFrame != null) {
                MeicamFxParam<?> meicamFxParam = keyFrame.getFxParam(paramName);
                if (meicamFxParam != null) {
                    MeicamPosition2D position2D;
                    if (meicamFxParam.getValue() != null) {
                        position2D = (MeicamPosition2D) meicamFxParam.getValue();
                        position2D.x = defaultValue;
                    } else {
                        position2D = new MeicamPosition2D(defaultValue, 0);
                    }
                    KeyFrameCommand.setPosition2DVal(keyFrame, paramName, position2D);
                }

            } else if (meicamTimelineVideoFx != null) {
                MeicamPosition2D position2D = meicamTimelineVideoFx.getPosition2DVal(paramName);
                if (position2D != null) {
                    position2D.x = defaultValue;
                } else {
                    position2D = new MeicamPosition2D(defaultValue, 0);
                }
                TimelineFxCommand.setPosition2DVal(meicamTimelineVideoFx, paramName, position2D);
            } else if (meicamVideoFx != null) {
                MeicamPosition2D position2D = meicamVideoFx.getPosition2DVal(paramName);
                if (position2D != null) {
                    position2D.x = defaultValue;
                } else {
                    position2D = new MeicamPosition2D(defaultValue, 0);
                }
                VideoFxCommand.setPosition2DVal(meicamVideoFx, paramName, position2D);
            }

        } else if (Constants.PlugType.POSITION2D_Y.equals(valueType)) {

            //float形式的，需要UI 轮盘
            float defaultValue = Float.parseFloat(valueDefault);
            if (keyFrame != null) {
                MeicamFxParam<?> meicamFxParam = keyFrame.getFxParam(paramName);
                if (meicamFxParam != null) {
                    MeicamPosition2D position2D;
                    if (meicamFxParam.getValue() != null) {
                        position2D = (MeicamPosition2D) meicamFxParam.getValue();
                        position2D.y = defaultValue;
                    } else {
                        position2D = new MeicamPosition2D(0, defaultValue);
                    }
                    KeyFrameCommand.setPosition2DVal(keyFrame, paramName, position2D);
                }

            } else if (meicamTimelineVideoFx != null) {
                MeicamPosition2D position2D = meicamTimelineVideoFx.getPosition2DVal(paramName);
                if (position2D != null) {
                    position2D.y = defaultValue;
                } else {
                    position2D = new MeicamPosition2D(0, defaultValue);
                }
                TimelineFxCommand.setPosition2DVal(meicamTimelineVideoFx, paramName, position2D);
            } else if (meicamVideoFx != null) {
                MeicamPosition2D position2D = meicamVideoFx.getPosition2DVal(paramName);
                if (position2D != null) {
                    position2D.y = defaultValue;
                } else {
                    position2D = new MeicamPosition2D(0, defaultValue);
                }
                VideoFxCommand.setPosition2DVal(meicamVideoFx, paramName, position2D);
            }
        } else if (Constants.PlugType.INT.equals(valueType)) {
            int defaultValue = Integer.parseInt(valueDefault);
            if (keyFrame != null) {
                KeyFrameCommand.setIntVal(keyFrame, paramName, defaultValue);
            } else if (meicamTimelineVideoFx != null) {
                TimelineFxCommand.setIntVal(meicamTimelineVideoFx, paramName, defaultValue);
            } else if (meicamVideoFx != null) {
                VideoFxCommand.setIntVal(meicamVideoFx, paramName, defaultValue);
            }
        } else if (Constants.PlugType.INT_CHOOSE.equals(valueType)) {
            int defaultValue = Integer.parseInt(valueDefault);
            if (keyFrame != null) {
                KeyFrameCommand.setIntVal(keyFrame, paramName, defaultValue);
            } else if (meicamTimelineVideoFx != null) {
                TimelineFxCommand.setIntVal(meicamTimelineVideoFx, paramName, defaultValue);
            } else if (meicamVideoFx != null) {
                VideoFxCommand.setIntVal(meicamVideoFx, paramName, defaultValue);
            }
        } else if (Constants.PlugType.COLOR.equals(valueType)) {
            //CO 颜色 形式的 边缘增强
            String color = valueDefault;
            if (!valueDefault.contains("#")) {
                //默认值 {1，1，1，1}这个形式的颜色
                valueDefault = valueDefault.substring(1, valueDefault.length() - 1);
                String[] strings = valueDefault.split(",");
                float[] defaultFloat = new float[strings.length];
                for (int i = 0; i < strings.length; i++) {
                    defaultFloat[i] = Integer.parseInt(strings[i]);
                }
                color = ColorUtil.nvsColorToHexString(ColorUtil.colorFloatToNvsColor(defaultFloat));
            }
            if (keyFrame != null) {
                KeyFrameCommand.setColorVal(keyFrame, paramName, color);
            } else if (meicamTimelineVideoFx != null) {
                TimelineFxCommand.setColorVal(meicamTimelineVideoFx, paramName, color);
            } else if (meicamVideoFx != null) {
                VideoFxCommand.setColorVal(meicamVideoFx, paramName, color);
            }
        } else if (Constants.PlugType.STRING.equals(valueType)
                || Constants.PlugType.CURVE.equals(valueType)) {
            //文件路径 UI上需要添加路径入口
            //q曲线调节 UI上需要调节曲线入口
            if (meicamTimelineVideoFx != null) {
                TimelineFxCommand.setStringVal(meicamTimelineVideoFx, paramName, valueDefault);
            } else if (meicamVideoFx != null) {
                VideoFxCommand.setStringVal(meicamVideoFx, paramName, valueDefault);
            }
        } else if (Constants.PlugType.MENU.equals(valueType)) {
            if (meicamTimelineVideoFx != null) {
                TimelineFxCommand.setMenuVal(meicamTimelineVideoFx, paramName, valueDefault);
            } else if (meicamVideoFx != null) {
                VideoFxCommand.setMenuVal(meicamVideoFx, paramName, valueDefault);
            }
        }
    }


    public static void adjustMaskGeneratorData(MeicamVideoFx maskFx, String key, float rateX, float rateY) {
        VideoFxCommand.adjustMaskGeneratorData(maskFx, key, rateX, rateY);
    }

    public static void adjustMaskModel(MeicamVideoClip videoClip, float rate) {
        VideoClipCommand.updateMaskModel(videoClip, VideoClipCommand.MASK_KEY_LAST_VIEW_SCALE, rate);
    }

}
