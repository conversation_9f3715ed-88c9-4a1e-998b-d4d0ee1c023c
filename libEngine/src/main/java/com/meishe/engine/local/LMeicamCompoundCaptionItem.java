package com.meishe.engine.local;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON>hiChao on 2020/7/4 13:37
 */
public class LMeicamCompoundCaptionItem implements Cloneable, Serializable{
    private int index;
    private String text;
    private float[] textColor = {1f, 1f, 1f, 1f};
    private String font = "";

    public LMeicamCompoundCaptionItem(int index, String text) {
        this.index = index;
        this.text = text;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getFont() {
        return font;
    }

    public void setFont(String font) {
        this.font = font;
    }

    public float[] getTextColor() {
        return textColor;
    }

    public void setTextColor(float[] textColor) {
        this.textColor = textColor;
    }

}
