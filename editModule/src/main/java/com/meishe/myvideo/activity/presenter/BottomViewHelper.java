package com.meishe.myvideo.activity.presenter;

import android.app.Activity;
import android.util.Pair;
import android.view.View;

import androidx.fragment.app.Fragment;

import com.meishe.base.model.BasePresenter;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.StringUtils;
import com.meishe.base.utils.Utils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.MeicamAudioClip;
import com.meishe.engine.bean.MeicamAudioFx;
import com.meishe.engine.bean.MeicamCompoundCaptionClip;
import com.meishe.engine.bean.MeicamCompoundCaptionItem;
import com.meishe.engine.bean.MeicamKeyFrame;
import com.meishe.engine.bean.MeicamStickerClip;
import com.meishe.engine.bean.MeicamTimelineVideoFilterAndAdjustClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxClip;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoFx;
import com.meishe.engine.command.CompCaptionCommand;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.engine.util.ColorUtil;
import com.meishe.myvideo.R;
import com.meishe.myvideo.adapter.BaseSelectAdapter;
import com.meishe.myvideo.bean.CaptionFontInfo;
import com.meishe.myvideo.bean.ChangeSpeedCurveInfo;
import com.meishe.myvideo.event.MessageEvent;
import com.meishe.myvideo.fragment.CaptionMouldFragment;
import com.meishe.myvideo.fragment.CompoundCaptionFragment;
import com.meishe.myvideo.fragment.StickerAnimationFragment;
import com.meishe.myvideo.fragment.VideoClipAnimationFragment;
import com.meishe.myvideo.fragment.WaterEffectFragment;
import com.meishe.myvideo.fragment.WaterFragment;
import com.meishe.myvideo.interfaces.BottomEventListener;
import com.meishe.myvideo.interfaces.PlugsEventListener;
import com.meishe.myvideo.view.AtomicEditMenuView;
import com.meishe.myvideo.view.BottomContainer;
import com.meishe.myvideo.view.MYAdjustMenuView;
import com.meishe.myvideo.view.MYCanvasBlur;
import com.meishe.myvideo.view.MYCanvasColor;
import com.meishe.myvideo.view.MYCanvasStyle;
import com.meishe.myvideo.view.MYColorPickMenuView;
import com.meishe.myvideo.view.MYCompoundCaptionEditView;
import com.meishe.myvideo.view.MYEffectTargetMenuView;
import com.meishe.myvideo.view.MYFilterMenuView;
import com.meishe.myvideo.view.MYMixedModeMenuView;
import com.meishe.myvideo.view.MYRecordMenuView;
import com.meishe.myvideo.view.MYSpeedCurveMenu;
import com.meishe.myvideo.view.MYThemeMenu;
import com.meishe.myvideo.view.editview.AdjustSeekBarView;
import com.meishe.myvideo.view.editview.EditAtomicCurveView;
import com.meishe.myvideo.view.editview.EditChangeSpeedCurveView;
import com.meishe.myvideo.view.editview.EditChangeSpeedView;
import com.meishe.myvideo.view.editview.EditChangeTransitionView;
import com.meishe.myvideo.view.editview.EditChangeVoiceView;
import com.meishe.myvideo.view.editview.EditKeyFrameCurveView;
import com.meishe.myvideo.view.editview.EditMaskView;

import java.util.List;

import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_CLIP_FILTER;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_UPDATE_FILTER_PROGRESS;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_UPDATE_SELECT_POSITION;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2020/12/29 9:45
 * @Description :编辑页面底部视图帮助者
 * The bottom view helper of the edit page
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class BottomViewHelper extends BasePresenter<BottomContainer, BottomViewModel> {
    private BottomContainer mBottomContainer;

    public BottomViewHelper(BottomViewModel model) {
        super(model);
    }

    @Override
    public void attachView(BottomContainer bottomContainer) {
        mBottomContainer = bottomContainer;
    }

    @Override
    public BottomContainer getView() {
        return mBottomContainer;
    }

    @Override
    public void detachView() {
        mBottomContainer = null;
    }

    /**
     * 展示画布颜色视图
     * Show the canvas color view
     *
     * @param videoClip the MeicamVideoClip
     */
    public void showCanvasColor(MeicamVideoClip videoClip, BottomEventListener listener) {
        MYCanvasColor canvasColor = new MYCanvasColor(getView().getContext());
        canvasColor.setListener(listener);
        canvasColor.selectedColor(mModel.getCanvasColor(videoClip));
        getView().showView(canvasColor);
    }

    /**
     * 更新画布颜色视图
     * Update the canvas color view
     */
    public void updateCanvasColor(MeicamVideoClip videoClip) {
        ((MYCanvasColor) getView().getShowView()).selectedColor(mModel.getCanvasColor(videoClip));
    }

    /**
     * 展示画布样式视图
     * Show the canvas style view
     *
     * @param videoClip the MeicamVideoClip
     */
    public void showCanvasStyle(MeicamVideoClip videoClip, BottomEventListener listener) {
        MYCanvasStyle style = new MYCanvasStyle(getView().getContext());
        style.setListener(listener);
        style.selected(mModel.getCanvasStyle(videoClip));
        getView().showView(style);
    }

    /**
     * 更新画布样式视图
     * Update the canvas style view
     */
    public void updateCanvasStyle(MeicamVideoClip videoClip) {
        ((MYCanvasStyle) getView().getShowView()).selected(mModel.getCanvasStyle(videoClip));
    }

    /**
     * 展示画布模糊视图
     * Show the canvas blur view
     *
     * @param videoClip the MeicamVideoClip
     */
    public void showCanvasBlur(MeicamVideoClip videoClip, BottomEventListener listener) {
        MYCanvasBlur blur = new MYCanvasBlur(getView().getContext());
        blur.setListener(listener);
        blur.selected(mModel.getCanvasBlur(videoClip));
        getView().showView(blur);
    }

    /**
     * 更新画布模糊视图
     * Update the canvas blur view
     */
    public void updateCanvasBlur(MeicamVideoClip videoClip) {
        ((MYCanvasBlur) getView().getShowView()).selected(mModel.getCanvasBlur(videoClip));
    }

    /**
     * 展示调节进度视图
     * Show the change volume view
     *
     * @param maxProgress the max progress
     * @param progress    the current progress
     * @param resourceId  the content text
     */
    public void showAdjustSeekBar(int maxProgress, int progress, int resourceId, String type, BottomEventListener listener) {
        AdjustSeekBarView adjustView = new AdjustSeekBarView(getView().getContext());
        adjustView.setSeekBarMax(maxProgress);
        adjustView.setType(type);
        adjustView.setProgress(progress);
        adjustView.setContentText(resourceId);
        adjustView.setListener(listener);
        getView().showView(adjustView);
    }

    /**
     * 更新调节进度视图
     * Show the change volume view
     *
     * @param progress the current progress
     */
    public void updateAdjustSeekBar(int progress) {
        ((AdjustSeekBarView) getView().getShowView()).setProgress(progress);
    }

    /**
     * 展示曲线变速的视图
     * Show the speed view of the curve
     *
     * @param videoClip the MeicamVideoClip
     * @param info      the baseInfo
     */
    public void showCurveSpeed(MeicamVideoClip videoClip, IBaseInfo info, final EditChangeSpeedCurveView.SpeedCurveListener listener) {
        if (videoClip != null) {
            EditChangeSpeedCurveView curveView = new EditChangeSpeedCurveView(getView().getContext());
            ChangeSpeedCurveInfo baseInfo = (ChangeSpeedCurveInfo) info;
            curveView.updateView(videoClip.getOutPoint() - videoClip.getInPoint(), videoClip.getOrgDuration(),
                    videoClip.getTrimOut() - videoClip.getTrimIn(), videoClip.getCurveSpeedName(),
                    videoClip.getCurveSpeed(), baseInfo);
            curveView.setListener(new EditChangeSpeedCurveView.SpeedCurveListener() {
                @Override
                public void onStartPlay(String originalSpeed, String speed) {

                    if (listener != null) {
                        listener.onStartPlay(originalSpeed, speed);
                    }
                }

                @Override
                public void onSeekPosition(long position) {
                    if (listener != null) {
                        listener.onSeekPosition(position);
                    }
                }

                @Override
                public void onDismiss(boolean confirm) {
                    if (listener != null) {
                        listener.onDismiss(confirm);
                    }
                    getView().dismissPopView();
                }
            });
            getView().showPushView(curveView);
        }
    }

    /**
     * 展示原子特效曲线的视图
     * Show the atomic  of the curve
     *
     * @param curve the curve
     */
    public void showAtomicCurve(String curve, final EditAtomicCurveView.AtomicCurveListener listener) {
        EditAtomicCurveView curveView = new EditAtomicCurveView(getView().getContext());
        curveView.updateView(curve);
        curveView.setListener(new EditAtomicCurveView.AtomicCurveListener() {
            @Override
            public void onCurveChanged(String curve) {
                if (listener != null) {
                    listener.onCurveChanged(curve);
                }
            }


            @Override
            public void onDismiss(boolean confirm) {
                if (listener != null) {
                    listener.onDismiss(confirm);
                }
                getView().dismissPopView();
            }
        });
        getView().showPushView(curveView);
    }

    /**
     * 展示常规变速视图
     * Show the normal speed view
     *
     * @param speed            the speed value
     * @param isKeepAudioPitch true keep,false not
     */
    public void showNormalSpeed(float speed, boolean isKeepAudioPitch, EditChangeSpeedView.ChangeSpeedListener listener) {
        EditChangeSpeedView speedView = new EditChangeSpeedView(getView().getContext());
        speedView.setSpeed(speed, isKeepAudioPitch);
        speedView.setListener(listener);
        getView().showView(speedView);

    }

    /**
     * 展示蒙版视图
     * Show the masking view
     *
     * @param type int the type
     */
    public void showMasking(int type, BottomEventListener listener) {
        EditMaskView maskView = new EditMaskView(getView().getContext());
        maskView.setData(mModel.getMaskData(), type);
        maskView.setListener(listener);
        getView().showView(maskView);
    }

    /**
     * 展示动画视图
     * Show the animation view
     *
     * @param name      the name
     * @param videoClip the MeicamVideoClip
     */
    public void showAnimationView(String name, MeicamVideoClip videoClip, final VideoClipAnimationFragment.OnEventListener listener) {
        if (videoClip == null) {
            LogUtils.e("videoClip clip is null ");
            return;
        }
        VideoClipAnimationFragment animationFragment = VideoClipAnimationFragment.create(videoClip);
        animationFragment.setOnEventListener(new VideoClipAnimationFragment.OnEventListener() {
            @Override
            public void onConfirm() {
                getView().dismissView();
                if (listener != null) {
                    listener.onConfirm();
                }
            }
        });
        getView().showPushFragment(animationFragment);
        getView().post(new Runnable() {
            @Override
            public void run() {
                if (Utils.getApp().getString(R.string.sub_menu_animation_in).equals(name)) {
                    animationFragment.setCurrPage(0);
                } else if (Utils.getApp().getString(R.string.sub_menu_animation_out).equals(name)) {
                    animationFragment.setCurrPage(1);
                } else {
                    animationFragment.setCurrPage(2);
                }
            }
        });

    }

    /**
     * 更新动画视图
     * Update the animation view
     *
     * @param videoClip the MeicamVideoClip
     */
    public void updateAnimationView(MeicamVideoClip videoClip) {
        if (videoClip == null) {
            return;
        }
        if (getView().getShowFragment() instanceof VideoClipAnimationFragment) {
            ((VideoClipAnimationFragment) getView().getShowFragment()).updateClip(videoClip);
        }
    }

    /**
     * 展示录音视图
     * Show the record view
     */
    public void showRecordView(MYRecordMenuView.OnRecordListener recordListener) {
        MYRecordMenuView recordView = new MYRecordMenuView(getView().getContext());
        recordView.setListener(recordListener);
        getView().showView(recordView);
    }

    /**
     * 展示转场视图
     * Show the record view
     */
    public void showTransitionView(MeicamAudioClip audioClip, EditChangeTransitionView.TransitionChangeListener listener) {
        if (audioClip == null) {
            return;
        }
        EditChangeTransitionView transitionView = new EditChangeTransitionView(getView().getContext());
        transitionView.setSeekBarMax((int) ((audioClip.getTrimOut() - audioClip.getTrimIn()) / 1000));
        transitionView.setProgress(audioClip.getFadeInDuration(), audioClip.getFadeOutDuration());
        transitionView.setListener(listener);
        getView().showView(transitionView);
    }

    /**
     * 展示变声视图
     * Show the change voice view
     */
    public void showChangeVoiceView(MeicamAudioClip audioClip, BottomEventListener listener) {
        EditChangeVoiceView changeVoiceView = new EditChangeVoiceView(getView().getContext());
        changeVoiceView.setData(mModel.getAudioChangeVoiceList());
        if (audioClip != null) {
            if (audioClip.getAudioFxCount() == 0) {
                changeVoiceView.setSelectedPosition("");
            } else {
                MeicamAudioFx fx = audioClip.getAudioFx(0);
                if (fx != null) {
                    String desc = fx.getDesc();
                    changeVoiceView.setSelectedPosition(desc);
                }
            }
        }
        changeVoiceView.setListener(listener);
        getView().showView(changeVoiceView);
    }


    /**
     * 展示视频变声视图
     * Show the video change voice view
     */
    public void showVideoChangeVoiceView(MeicamVideoClip videoClip, BottomEventListener listener) {
        EditChangeVoiceView changeVoiceView = new EditChangeVoiceView(getView().getContext());
        changeVoiceView.setData(mModel.getAudioChangeVoiceList());
        if (videoClip != null) {
            if (videoClip.getAudioFxCount() == 0) {
                changeVoiceView.setSelectedPosition("");
            } else {
                MeicamAudioFx fx = videoClip.getAudioFx(0);
                if (fx != null) {
                    String desc = fx.getDesc();
                    changeVoiceView.setSelectedPosition(desc);
                }
            }
        }
        changeVoiceView.setListener(listener);
        getView().showView(changeVoiceView);
    }

    /**
     * 展示字幕模板
     * Show the  caption mould
     */
    public void showCaptionMouldView(BottomEventListener listener) {
        final CaptionMouldFragment captionMenuView = CaptionMouldFragment.getInstance();
        captionMenuView.setEventListener(listener);
        getView().showFragment(captionMenuView);
    }

    /**
     * 展示组合字幕编辑视图
     * Show the combined caption edit menu
     */
    public void showCompoundCaptionEdit(final MeicamCompoundCaptionClip captionClip, final int keyboardHeight, final MYCompoundCaptionEditView.CompoundCaptionListener listener) {
        final CompoundCaptionFragment captionMenuView = getView().getShowFragment() instanceof CompoundCaptionFragment ?
                (CompoundCaptionFragment) getView().getShowFragment() : null;
        getView().dismissFragment();
        final MeicamCompoundCaptionItem captionItem = captionClip.getCaptionItem(captionClip.getItemSelectedIndex());
        final MeicamCompoundCaptionItem copyCaptionItem = captionItem.copy();
        MYCompoundCaptionEditView captionEditView = new MYCompoundCaptionEditView(getView().getContext());
        captionEditView.setData(captionClip, mModel.getCaptionFontList(getView().getContext()), keyboardHeight);
        captionEditView.setListener(new MYCompoundCaptionEditView.CompoundCaptionListener() {
            @Override
            public void onColorChange(String colorValue) {
                CompCaptionCommand.setTextColor(captionClip, captionClip.getItemSelectedIndex(), colorValue);
                EditorEngine.getInstance().seekTimeline();
                if (listener != null) {
                    listener.onColorChange(colorValue);
                }
            }

            @Override
            public void onCaptionTextChange(String text) {
                CompCaptionCommand.setText(captionClip, captionClip.getItemSelectedIndex(), text);
                EditorEngine.getInstance().seekTimeline();
                if (listener != null) {
                    listener.onCaptionTextChange(text);
                }
            }

            @Override
            public void onDismiss(boolean confirm) {
                if (!confirm) {
                    int selectedIndex = captionClip.getItemSelectedIndex();
                    CompCaptionCommand.setText(captionClip, selectedIndex, copyCaptionItem.getText());
                    CompCaptionCommand.setTextColor(captionClip, selectedIndex, ColorUtil.nvsColorToHexString(ColorUtil.colorFloatToNvsColor(copyCaptionItem.getTextColor())));
                    captionClip.setFontFamily(selectedIndex, copyCaptionItem.getFont());
                    EditorEngine.getInstance().seekTimeline();
                }
                if (listener != null) {
                    listener.onDismiss(confirm);
                }
                if (captionMenuView != null) {
                    //如果组合字幕选择菜单之前正在显示，则还原。
                    //If the combined caption selection menu is displayed before, restore it.
                    getView().showFragment(captionMenuView);
                }
            }

            @Override
            public void onItemClick(IBaseInfo baseInfo, boolean applyAll) {
                if (baseInfo instanceof CaptionFontInfo) {
                    EditorEngine.getInstance().setCompoundCaptionFont(captionClip, captionClip.getItemSelectedIndex(), ((CaptionFontInfo) baseInfo).getFontFamily());
                }
                if (listener != null) {
                    listener.onItemClick(baseInfo, applyAll);
                }

            }
        });
        getView().showView(captionEditView);
    }

    /**
     * 展示调节菜单
     * Show the adjust menu.
     */
    public void showAdjustMenu(MeicamVideoClip videoClip, MeicamTimelineVideoFilterAndAdjustClip meicamTimelineVideoFilterAndAdjustClip
            , final BottomEventListener listener) {
        List<IBaseInfo> adjustData = mModel.getAdjustData(getView().getContext());
        mModel.handleAdjustData(adjustData, videoClip, meicamTimelineVideoFilterAndAdjustClip);
        MYAdjustMenuView adjustMenuView = new MYAdjustMenuView(getView().getContext());
        adjustMenuView.setNeedShowApply(videoClip != null);
        adjustMenuView.updateView(adjustData);
        adjustMenuView.setEventListener(new BottomEventListener() {
            @Override
            public void onDismiss(boolean confirm) {
                getView().dismissView();
                if (listener != null) {
                    listener.onDismiss(confirm);
                }
            }
        });
        getView().showView(adjustMenuView);
    }


    /**
     * 展示混合模式菜单
     * Show the mixed mode menu.
     */
    public void showMixedModeMenu(MeicamVideoClip videoClip, final BottomEventListener listener) {
        if (videoClip == null) {
            return;
        }
        List<IBaseInfo> mixedModeData = mModel.getMixedModeData(getView().getContext());
        MYMixedModeMenuView mixedModeMenuView = new MYMixedModeMenuView(getView().getContext());
        mixedModeMenuView.updateView(mixedModeData);
        mixedModeMenuView.setSelectedAndSeekBarProgress(videoClip.getBlendingMode(), videoClip.getOpacity());
        mixedModeMenuView.setEventListener(new BottomEventListener() {
            @Override
            public void onDismiss(boolean confirm) {
                getView().dismissView();
                if (listener != null) {
                    listener.onDismiss(confirm);
                }
            }
        });
        getView().showView(mixedModeMenuView);
    }


    /**
     * 展示滤镜菜单
     * Show the filter menu.
     */
    public void showFilterMenu(MeicamVideoClip videoClip, MeicamTimelineVideoFilterAndAdjustClip meicamTimelineVideoFilterAndAdjustClip, final BottomEventListener listener) {
        MYFilterMenuView adjustMenuView = new MYFilterMenuView(getView().getContext());
        adjustMenuView.setNeedShowApply(videoClip != null);
        adjustMenuView.setEventListener(new BottomEventListener() {
            @Override
            public void onDismiss(boolean confirm) {
                getView().dismissView();
                if (listener != null) {
                    listener.onDismiss(confirm);
                }
            }
        });
        String desc = "", type = "";
        if (videoClip == null) {
            if (meicamTimelineVideoFilterAndAdjustClip != null) {
                MeicamTimelineVideoFxClip clip = meicamTimelineVideoFilterAndAdjustClip.getAdjustTimelineFx(MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FILTER);
                if (clip != null) {
                    type = clip.getClipType();
                    desc = clip.getDesc();
                }
            }
        } else {
            MeicamVideoFx filterFx = videoClip.getVideoFxByType(SUB_TYPE_CLIP_FILTER);
            if (filterFx != null) {
                desc = filterFx.getDesc();
                type = filterFx.getType();

            }
        }
        adjustMenuView.selected(desc, CommonData.TYPE_BUILD_IN.equals(type)
                ? BaseInfo.EFFECT_MODE_BUILTIN : BaseInfo.EFFECT_MODE_PACKAGE);
        adjustMenuView.setProgress(mModel.getFilterIntensity(videoClip, meicamTimelineVideoFilterAndAdjustClip));
        getView().showView(adjustMenuView);
    }

    /**
     * 设置Timeline滤镜选中状态
     * set Timeline Filter selected.
     *
     * @param adjustMenuView the adjust menu view
     */
    public void setTimelineFilterSelected(MYFilterMenuView adjustMenuView, MeicamTimelineVideoFilterAndAdjustClip meicamTimelineVideoFilterAndAdjustClip) {
        if (meicamTimelineVideoFilterAndAdjustClip != null) {
            MeicamTimelineVideoFxClip clip = meicamTimelineVideoFilterAndAdjustClip.getAdjustTimelineFx(MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FILTER);
            if (clip != null) {
                adjustMenuView.selected(clip.getDesc(), CommonData.TYPE_BUILD_IN.equals(clip.getClipType())
                        ? BaseInfo.EFFECT_MODE_BUILTIN : BaseInfo.EFFECT_MODE_PACKAGE);
                float intensity = mModel.getFilterIntensity(null, meicamTimelineVideoFilterAndAdjustClip);
                adjustMenuView.setProgress(intensity);
            } else {
                adjustMenuView.selected(null, BaseInfo.EFFECT_MODE_BUILTIN);
            }
        } else {
            adjustMenuView.selected(null, BaseInfo.EFFECT_MODE_BUILTIN);
        }
    }

    /**
     * 显示曲线变速菜单 Show the speed curve menu.
     */
    public void showSpeedCurveMenu(MeicamVideoClip videoClip, BottomEventListener listener) {
        List<IBaseInfo> curveList = mModel.getSpeedCurveData(getView().getContext());
        int position = mModel.getCurveSpeedSelectPosition(curveList, videoClip);
        MYSpeedCurveMenu speedMenuView = new MYSpeedCurveMenu(getView().getContext());
        speedMenuView.setNeedShowSeekBar(false);
        speedMenuView.updateView(curveList);
        speedMenuView.setEventListener(listener);
        getView().showPushView(speedMenuView);
        MessageEvent.sendEvent(position, MESSAGE_TYPE_UPDATE_SELECT_POSITION);
    }

    /**
     * 显示曲线变速菜单 Show the speed curve menu.
     */
    public void showPlugsMenu(Activity activity, MeicamVideoClip meicamVideoClip, MeicamTimelineVideoFxClip meicamTimelineVideoFxClip, PlugsEventListener listener) {
        if (getView().getShowView() instanceof AtomicEditMenuView) {
            AtomicEditMenuView view = (AtomicEditMenuView) getView().getShowView();
            view.updateView(activity, meicamVideoClip, meicamTimelineVideoFxClip);
        } else {
            AtomicEditMenuView speedMenuView = new AtomicEditMenuView(getView().getContext());
            speedMenuView.updateView(activity, meicamVideoClip, meicamTimelineVideoFxClip);
            speedMenuView.setListener(listener);
            getView().showView(speedMenuView);
        }

    }

    /**
     * 显示主题菜单
     * <p>
     * show theme menu
     */
    public void showThemeMenu() {
        MYThemeMenu menuView = new MYThemeMenu(getView().getContext());
        menuView.setNeedShowApply(false);
        menuView.setEventListener(new BottomEventListener() {
            @Override
            public void onDismiss(boolean confirm) {
                getView().dismissView();
            }
        });
        getView().showView(menuView);
    }

    /**
     * 更新seekBar的进度
     * <p>
     * update the progress of seekBar
     *
     * @param videoClip the video clip
     */
    public void updateSeekBar(MeicamVideoClip videoClip, MeicamTimelineVideoFilterAndAdjustClip clip) {
        View showView = getView().getShowView();
        if (showView instanceof MYFilterMenuView) {
            float progress = mModel.getFilterIntensity(videoClip, clip);
            MessageEvent.sendEvent(progress, MESSAGE_TYPE_UPDATE_FILTER_PROGRESS);
        } else if (showView instanceof MYAdjustMenuView) {
            IBaseInfo baseInfo = ((MYAdjustMenuView) showView).getSelectedItem();
            if (baseInfo != null) {
                MessageEvent.sendEvent(mModel.getAdjustStrength(baseInfo, videoClip, clip), MESSAGE_TYPE_UPDATE_FILTER_PROGRESS);
            }
        } else if (videoClip != null && showView instanceof AdjustSeekBarView) {
            AdjustSeekBarView adjustSeekBarView = (AdjustSeekBarView) showView;
            if (StringUtils.getString(R.string.sub_menu_name_edit_opacity).equals(adjustSeekBarView.getType())) {
                //透明度
                //opacity
                adjustSeekBarView.setProgress((int) (videoClip.getOpacity() * 100));
            }
//            else if (adjustSeekBarView.getResourceId() == R.string.sub_menu_audio_edit_volume) {
//                //音量 volume
//                if (adjustSeekBarView.getType().equals(CommonData.CLIP_IMAGE) || adjustSeekBarView.getType().equals(CommonData.CLIP_VIDEO)) {
//                    int progress = (int) (videoClip.getVolume() * Constants.maxVolumeProgress / Constants.maxNvVolume);
//                    adjustSeekBarView.setProgress(progress);
//                }
//            }
        }

    }

    /**
     * 如果需要，隐藏
     * <p>
     * hide view if need
     */
    public void hideIfNeed() {
        BottomContainer view = getView();
        if (view != null) {
            View showView = view.getShowView();
            if (showView instanceof MYFilterMenuView
                    || showView instanceof MYAdjustMenuView
                    || showView instanceof MYThemeMenu
                    || showView instanceof MYSpeedCurveMenu) {
                view.dismissView();
            }
        }
    }

    /**
     * 更新选中态
     * <p>
     * update selection
     *
     * @param videoClip 视频数据 video clip data
     */
    public void updateSelection(MeicamVideoClip videoClip) {
        if (videoClip == null) {
            return;
        }
        View showView = getView().getShowView();
        if (showView instanceof MYFilterMenuView) {
            BaseSelectAdapter<IBaseInfo> adapter = ((MYFilterMenuView) showView).getAdapter();
            int selection = mModel.getFilterSelection(adapter.getData(), videoClip);
            MessageEvent.sendEvent(selection, MESSAGE_TYPE_UPDATE_SELECT_POSITION);
        } else if (showView instanceof MYThemeMenu) {
            BaseSelectAdapter<IBaseInfo> adapter = ((MYThemeMenu) showView).getAdapter();
            int selection = mModel.getThemeSelection(adapter.getData());
            MessageEvent.sendEvent(selection, MESSAGE_TYPE_UPDATE_SELECT_POSITION);
        } else if (showView instanceof MYSpeedCurveMenu) {
            BaseSelectAdapter<IBaseInfo> adapter = ((MYSpeedCurveMenu) showView).getAdapter();
            int position = mModel.getCurveSpeedSelectPosition(adapter.getData(), videoClip);
            MessageEvent.sendEvent(position, MESSAGE_TYPE_UPDATE_SELECT_POSITION);
        } else if (showView instanceof EditMaskView) {
            int selection = videoClip.maskModel.maskType;
            ((EditMaskView) showView).setSelection(selection);
        }
    }

    public void showKeyFrameCurveView(Pair<MeicamKeyFrame, MeicamKeyFrame> paraFrame, final EditKeyFrameCurveView.OnEventListener listener) {
        if (paraFrame != null) {
            EditKeyFrameCurveView view = new EditKeyFrameCurveView(getView().getContext());
            view.setSelection(mModel.getKeyFrameCurveIndex(paraFrame));
            view.setPointsInView(mModel.getKeyFrameControlPoints(paraFrame));
            view.setListener(listener);
            getView().showPushView(view);
        } else {
            LogUtils.e(" there is no key frame in this time!");
        }
    }

    public void showStickerAnimationView(MeicamStickerClip stickerClip, final StickerAnimationFragment.OnEventListener listener) {
        if (stickerClip == null) {
            LogUtils.e("sticker clip is null ");
            return;
        }
        StickerAnimationFragment animationFragment = StickerAnimationFragment.create(stickerClip);
        animationFragment.setOnEventListener(new StickerAnimationFragment.OnEventListener() {
            @Override
            public void onConfirm() {
                getView().dismissView();
                if (listener != null) {
                    listener.onConfirm();
                }
            }
        });
        getView().showPushFragment(animationFragment);
    }

    public void showWaterView(BottomEventListener listener) {
        getView().showReplaceFragment(WaterFragment.getInstance(listener));
    }

    public void showWaterEffectView(BottomEventListener listener) {
        getView().showReplaceFragment(WaterEffectFragment.getInstance(listener));
    }

    /**
     * 更新水印选中
     * Update watermark selected
     */
    public boolean updateWatermarkSelected() {
        Fragment showFragment = getView().getShowFragment();
        if (showFragment instanceof WaterFragment) {
            ((WaterFragment) showFragment).updateSelected();
            return true;
        }
        return false;
    }

    /**
     * 添加水印
     * Update watermark
     */
    public void addWatermark(String waterPath) {
        Fragment showFragment = getView().getShowFragment();
        if (showFragment instanceof WaterFragment) {
            ((WaterFragment) showFragment).addWaterMark(waterPath);
        }
    }

    /**
     * 更新水印效果选中
     * Update watermark effect selected
     */
    public boolean updateWatermarkEffectSelected() {
        Fragment showFragment = getView().getShowFragment();
        if (showFragment instanceof WaterEffectFragment) {
            ((WaterEffectFragment) showFragment).updateSelected();
            return true;
        }
        return false;
    }

    /**
     * 取消选中水印相关
     * Unselected about watermark
     */
    public void unselectedAboutWatermark() {
        Fragment selectedFragment = getView().getShowFragment();
        if (selectedFragment instanceof WaterFragment) {
            ((WaterFragment) selectedFragment).unselected();
        } else if (selectedFragment instanceof WaterEffectFragment) {
            ((WaterEffectFragment) selectedFragment).unSelected();
        }
    }

    public void showColorPickMenu(float[] param, MYColorPickMenuView.OnEventChangedListener listener) {
        MYColorPickMenuView adjustMenuView = new MYColorPickMenuView(getView().getContext(), param);
        adjustMenuView.setOnEventChangedListener(new MYColorPickMenuView.OnEventChangedListener() {
            @Override
            public void onMenuClicked(int position) {
                if (listener != null) {
                    listener.onMenuClicked(position);
                }
            }

            @Override
            public void onDataChanged(String key, int progress) {
                if (listener != null) {
                    listener.onDataChanged(key, progress);
                }
            }

            @Override
            public void onConfirm() {
                getView().dismissView();
                if (listener != null) {
                    listener.onConfirm();
                }
            }

            @Override
            public void onReset() {
                if (listener != null) {
                    listener.onReset();
                }
            }
        });
        getView().showView(adjustMenuView);
    }


    public void showEffectTargetMenu(MeicamTimelineVideoFxClip timelineVideoFxClip
            ,  MYEffectTargetMenuView.OnEventChangedListener listener) {
        long timelinePosition = EditorEngine.getInstance().getCurrentTimelinePosition();
        boolean needSelected = timelinePosition >= timelineVideoFxClip.getInPoint() && timelinePosition <= timelineVideoFxClip.getOutPoint();
        List<MYEffectTargetMenuView.TargetInfo> effectTargetInfo = mModel.getEffectTargetInfo(timelineVideoFxClip, needSelected);
        int selection = mModel.getEffectTargetSelection(effectTargetInfo, timelineVideoFxClip);
        MYEffectTargetMenuView adjustMenuView = new MYEffectTargetMenuView(getView().getContext(), effectTargetInfo, selection, needSelected);
        adjustMenuView.setOnEventChangedListener(listener);
        getView().showView(adjustMenuView);
    }
}
