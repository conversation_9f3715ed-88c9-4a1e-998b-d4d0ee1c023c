package com.meishe.myvideo.ui.bean;

import android.graphics.Bitmap;
import android.graphics.Rect;

import java.util.Objects;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/4/16 15:29
 * @Description :线图区域片段 Line view region
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class LineRegionClip extends BaseTrackClip {
    /**
     * 字幕类型
     * Caption type
     */
    public static final int REGION_TYPE_CAPTION = 1;
    /**
     * 组合字幕类型
     * Compound Caption type
     */
    public static final int REGION_TYPE_COMPOUND_CAPTION = 2;
    /**
     * 贴纸类型
     * Sticker type
     */
    public static final int REGION_TYPE_STICKER = 3;
    /**
     * 画中画类型
     * PIP type
     */
    public static final int REGION_TYPE_PIP = 4;
    /**
     * 图标区域
     * Icon area
     */
    private Rect rect;
    /**
     * 图标
     * Icon
     */
    private Bitmap bitmap;

    private BitmapDesc bitmapDesc;

    private long bitmapTaskId;

    public Rect getRect() {
        if(rect == null){
            rect = new Rect();
        }
        return rect;
    }

    public void setRect(Rect rect) {
        this.rect = rect;
    }

    public Bitmap getBitmap() {
        return bitmap;
    }

    public void setBitmap(Bitmap bitmap) {
        this.bitmap = bitmap;
    }

    public boolean bitmapAvailable() {
        return bitmap != null && !bitmap.isRecycled();
    }

    public void setBitmapDesc(BitmapDesc bitmapDesc) {
        this.bitmapDesc = bitmapDesc;
    }

    public BitmapDesc getBitmapDesc() {
        return bitmapDesc;
    }

    public void setBitmapTaskId(long task) {
        bitmapTaskId = task;
    }

    public long getBitmapTaskId() {
        return bitmapTaskId;
    }

    public static class BitmapDesc{

        public BitmapDesc(String filePath, long trimIn) {
            this.filePath = filePath;
            this.trimIn = trimIn;
        }

        public String filePath;
        public long trimIn;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof BitmapDesc)) return false;
            BitmapDesc that = (BitmapDesc) o;
            return trimIn == that.trimIn && Objects.equals(filePath, that.filePath);
        }

        @Override
        public int hashCode() {
            return Objects.hash(filePath, trimIn);
        }
    }
}
