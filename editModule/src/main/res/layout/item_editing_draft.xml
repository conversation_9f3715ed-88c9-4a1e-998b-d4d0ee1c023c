<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:paddingStart="@dimen/dp_px_45"
    android:paddingTop="@dimen/dp_px_30"
    android:paddingEnd="@dimen/dp_px_45"
    android:background="@color/black"
    android:paddingBottom="@dimen/dp_px_30">

    <ImageView
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:id="@+id/iv_cover"
        android:layout_width="@dimen/dp_px_246"
        android:layout_height="@dimen/dp_px_246"
        android:contentDescription="@null"
        android:scaleType="centerCrop" />

    <TextView
        app:layout_constraintLeft_toRightOf="@+id/iv_cover"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_update_time"
        android:id="@+id/tv_draft_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_px_45"
        android:layout_marginTop="@dimen/dp_px_9"
        android:layout_toEndOf="@+id/iv_cover"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_39"
        tools:ignore="RelativeOverlap" />

    <TextView
        app:layout_constraintTop_toBottomOf="@+id/tv_draft_name"
        app:layout_constraintLeft_toRightOf="@+id/iv_cover"
        app:layout_constraintBottom_toTopOf="@+id/tv_draft_size"
        android:id="@+id/tv_update_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_px_45"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:textColor="@color/white_5"
        android:textSize="@dimen/sp_px_33" />

    <TextView
        app:layout_constraintTop_toBottomOf="@+id/tv_update_time"
        app:layout_constraintLeft_toRightOf="@+id/iv_cover"
        app:layout_constraintBottom_toTopOf="@+id/tv_draft_duration"
        android:id="@+id/tv_draft_size"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_px_45"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/white_5"
        android:textSize="@dimen/sp_px_33" />

    <TextView
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/iv_cover"
        app:layout_constraintTop_toBottomOf="@+id/tv_draft_size"
        android:id="@+id/tv_draft_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@+id/iv_cover"
        android:layout_marginStart="@dimen/dp_px_45"
        android:layout_marginBottom="@dimen/dp_px_9"
        android:layout_toEndOf="@+id/iv_cover"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_33"
        tools:ignore="RelativeOverlap" />

    <TextView
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:id="@+id/tv_upload"
        android:layout_width="@dimen/dp_px_105"
        android:layout_height="@dimen/dp_px_246"
        android:gravity="center"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/dp_px_140"
        android:visibility="gone"
        android:text="@string/draft_upload"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_36"
        tools:visibility="gone"
        tools:ignore="RelativeOverlap" />

    <ImageView
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:id="@+id/iv_draft_manager"
        android:layout_width="@dimen/dp_px_105"
        android:layout_height="@dimen/dp_px_240"
        android:contentDescription="@null"
        android:paddingStart="@dimen/dp5"
        android:paddingTop="@dimen/dp_px_90"
        android:paddingEnd="@dimen/dp_px_5"
        android:paddingBottom="@dimen/dp_px_90"
        android:src="@mipmap/manager_item" />

    <ImageView
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:id="@+id/iv_select_state"
        android:layout_width="@dimen/dp_px_54"
        android:layout_height="@dimen/dp_px_54"
        android:contentDescription="@null"
        android:scaleType="fitXY"
        android:visibility="gone"
        tools:visibility="visible" />

    <FrameLayout
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:id="@+id/fl_cloud"
        android:layout_width="@dimen/dp_px_246"
        android:layout_height="@dimen/dp_px_246"
        android:visibility="gone">
        <ImageView
            android:layout_width="@dimen/dp_px_54"
            android:layout_height="@dimen/dp_px_54"
            android:layout_marginTop="@dimen/dp_px_9"
            android:layout_marginEnd="@dimen/dp_px_9"
            android:src="@mipmap/ic_draft_is_cloud"
            android:layout_gravity="end|top"/>
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
