package com.meishe.engine.bean;

import com.meicam.sdk.NvsAudioFx;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.local.LMeicamAudioFx;


/**
 * All rights reserved,Designed by www.meishesdk.com
 * 版权所有:www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/05/20 10:07
 * @Description : 音频特效 The audio clip fx
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MeicamAudioFx extends NvsObject<NvsAudioFx> implements Cloneable, TimelineDataParserAdapter<LMeicamAudioFx> {
    /**
     * 索引
     * index
     */
   private int index;
    /**
     * 类型
     * type
     */
    private String type;
    /**
     * 特效标识
     * type
     */
    private String desc;

    public MeicamAudioFx(NvsAudioFx audioFx, int index, String type, String desc) {
        super(audioFx);
        this.index = index;
        this.type = type;
        this.desc = desc;
    }

    public int getIndex() {
        return index;
    }

     void setIndex(int index) {
        this.index = index;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

     void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public LMeicamAudioFx parseToLocalData() {
        LMeicamAudioFx local = new LMeicamAudioFx();
        local.setType(getType());
        local.setIndex(getIndex());
        local.setDesc(getDesc());
        return local;
    }

    @Override
    public void recoverFromLocalData(LMeicamAudioFx lMeicamAudioFx) {

    }

    @Override
    public void recoverFromTimelineData(com.meicam.sdk.NvsObject nvsObject) {
        if (nvsObject instanceof NvsAudioFx) {
            setObject((NvsAudioFx) nvsObject);
        }
    }
}
