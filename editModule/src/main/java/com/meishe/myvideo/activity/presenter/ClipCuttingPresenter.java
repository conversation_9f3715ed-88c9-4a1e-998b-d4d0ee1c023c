package com.meishe.myvideo.activity.presenter;

import android.graphics.Point;
import android.graphics.PointF;
import android.text.TextUtils;

import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsAudioResolution;
import com.meicam.sdk.NvsRational;
import com.meicam.sdk.NvsSize;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsVideoClip;
import com.meicam.sdk.NvsVideoResolution;
import com.meicam.sdk.NvsVideoTrack;
import com.meishe.base.model.Presenter;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.CutData;
import com.meishe.engine.bean.MeicamMaskRegionInfo;
import com.meishe.engine.bean.MeicamStoryboardInfo;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoFx;
import com.meishe.engine.bean.NvMaskModel;
import com.meishe.engine.command.VideoClipCommand;
import com.meishe.engine.command.VideoFxCommand;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.util.StoryboardUtil;
import com.meishe.myvideo.activity.iview.ClipCuttingView;
import com.meishe.player.view.cutregion.ICutRegionFragment;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.meishe.engine.bean.CommonData.TYPE_RAW_BUILTIN;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_CROPPER;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_CROPPER_EXT;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_CROPPER_TRANSFORM;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_POST_CROPPER_TRANSFORM;
import static com.meishe.engine.util.StoryboardUtil.STORYBOARD_KEY_ROTATION_Z;
import static com.meishe.engine.util.StoryboardUtil.STORYBOARD_KEY_SCALE_X;
import static com.meishe.engine.util.StoryboardUtil.STORYBOARD_KEY_SCALE_Y;
import static com.meishe.engine.util.StoryboardUtil.STORYBOARD_KEY_TRANS_X;
import static com.meishe.engine.util.StoryboardUtil.STORYBOARD_KEY_TRANS_Y;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/22 11:26
 * @Description :区域裁剪的的逻辑处理类，The presenter of clip rect cutting.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class ClipCuttingPresenter extends Presenter<ClipCuttingView> {
    private int mOriginalTimelineHeight;
    private int mOriginalTimelineWidth;
    private NvsStreamingContext mStreamingContext;
    private NvsTimeline mTimeline;
    private CutData mCutData;
    private MeicamVideoClip mVideoClip;

    public void intData(int originalTimelineWidth, int originalTimelineHeight, int trackIndex, int clipIndex) {
        mOriginalTimelineHeight = originalTimelineHeight;
        mOriginalTimelineWidth = originalTimelineWidth;
        mStreamingContext = EditorEngine.getInstance().getStreamingContext();
        mVideoClip = EditorEngine.getInstance().getVideoClip(trackIndex, clipIndex);
        if (mVideoClip == null) {
            LogUtils.e("error check it!!!");
            getView().exit();
            return;
        }
        addVideoClip();
        mCutData = getInitCutData();
    }

    public CutData getCutData() {
        return mCutData;
    }

    public NvsTimeline getTimeline() {
        return mTimeline;
    }

    private void addVideoClip() {
        mTimeline = newTimeline(getOriginalResolutionByClip(mVideoClip.getFilePath()));
        if (mTimeline == null) {
            return;
        }
        NvsVideoTrack videoTrack = mTimeline.appendVideoTrack();
        if (videoTrack == null) {
            return;
        }
        NvsVideoClip videoClip = videoTrack.appendClip(mVideoClip.getFilePath());
        if (videoClip == null) {
            return;
        }
        videoClip.disableAmbiguousCrop(true);
        int videoType = videoClip.getVideoType();
        if (videoType == NvsVideoClip.VIDEO_CLIP_TYPE_IMAGE) {
            videoClip.setImageMotionMode(NvsVideoClip.CLIP_MOTIONMODE_LETTERBOX_ZOOMIN);
        } else {
            videoClip.setPanAndScan(0, 0);
        }
    }

    /**
     * 根据设置的宽高信息创建时间线
     * New timeline nvs timeline.
     *
     * @param videoResolution 视频分辨率   audioResolution:音频解析度
     * @return nvs timeline
     */
    public NvsTimeline newTimeline(NvsVideoResolution videoResolution) {
        NvsStreamingContext context = NvsStreamingContext.getInstance();
        videoResolution.imagePAR = new NvsRational(1, 1);
        NvsAudioResolution nvsAudioResolution = new NvsAudioResolution();
        nvsAudioResolution.sampleRate = 44100;
        nvsAudioResolution.channelCount = 2;
        return context.createTimeline(videoResolution, EditorEngine.getInstance().getVideoRational(), nvsAudioResolution);
    }

    private CutData getInitCutData() {
        CutData cutData = new CutData();
        MeicamVideoFx transformVideoFx = mVideoClip.getVideoFxByType(TYPE_RAW_BUILTIN, SUB_TYPE_CROPPER_TRANSFORM);
        MeicamVideoFx cropperVideoFx = mVideoClip.getVideoFxByType(TYPE_RAW_BUILTIN, SUB_TYPE_CROPPER);
        if (cropperVideoFx == null) {
            cropperVideoFx = mVideoClip.getVideoFxByType(TYPE_RAW_BUILTIN, SUB_TYPE_CROPPER_EXT);
        }
        if (transformVideoFx != null && cropperVideoFx != null) {
            cutData.putTransformData(STORYBOARD_KEY_TRANS_X, transformVideoFx.getFloatVal(NvsConstants.KEY_CROPPER_TRANS_X));
            cutData.putTransformData(STORYBOARD_KEY_TRANS_Y, -transformVideoFx.getFloatVal(NvsConstants.KEY_CROPPER_TRANS_Y));
            cutData.putTransformData(STORYBOARD_KEY_SCALE_X, transformVideoFx.getFloatVal(NvsConstants.KEY_CROPPER_SCALE_X));
            cutData.putTransformData(STORYBOARD_KEY_SCALE_Y, transformVideoFx.getFloatVal(NvsConstants.KEY_CROPPER_SCALE_Y));
            cutData.putTransformData(STORYBOARD_KEY_ROTATION_Z, -transformVideoFx.getFloatVal(NvsConstants.KEY_CROPPER_ROTATION));
            cutData.setIsOldData(false);
            float cropperAssetAspectRatio = cropperVideoFx.getFloatVal(NvsConstants.KEY_CROPPER_ASSET_ASPECT_RATIO);
            String cropperValue = cropperVideoFx.getStringVal(NvsConstants.KEY_CROPPER_RATIO);
            if ("free".equals(cropperValue)) {
                cutData.setRatio(NvsConstants.AspectRatio.AspectRatio_NoFitRatio);
            } else {
                cutData.setRatio(CommonData.AspectRatio.getAspect(cropperAssetAspectRatio));
            }
            cutData.setRatioValue(cropperAssetAspectRatio);
            return cutData;
        }

        String transformDescription = null;
        String croperDescription = null;
        MeicamVideoFx videoFx = mVideoClip.getOldStoryboardFx(MeicamStoryboardInfo.SUB_TYPE_CROPPER_TRANSFROM);
        if (videoFx != null) {
            transformDescription = videoFx.getStringVal("Description String");
        }
        MeicamVideoFx cropperFx = mVideoClip.getOldStoryboardFx(MeicamStoryboardInfo.SUB_TYPE_CROPPER);
        if (cropperFx != null) {
            croperDescription = cropperFx.getStringVal("Description String");
        }
        NvsVideoResolution videoRes = mTimeline.getVideoRes();
        float[] size = getRelativeSize(videoRes.imageWidth, videoRes.imageHeight, mOriginalTimelineWidth, mOriginalTimelineHeight);
        return StoryboardUtil.parseStoryToCatData(croperDescription, transformDescription, size);
    }

    private float[] getRelativeSize(int imageWidth, int imageHeight, int timelineWidth, int timelineHeight) {
        float[] size = new float[2];
        float timelineRatio = timelineWidth * 1.0F / timelineHeight;
        float imageRatio = imageWidth * 1.0F / imageHeight;
        if (imageRatio > timelineRatio) {
            //宽对齐 Wide alignment
            size[0] = 1.0F;
            float ratio = timelineWidth * 1.0F / imageWidth;
            size[1] = (imageHeight * ratio) / timelineHeight;
        } else {
            size[1] = 1.0F;
            float ratio = timelineHeight * 1.0F / imageHeight;
            size[0] = imageWidth * ratio / timelineWidth;
        }
        return size;
    }

    /**
     * 转换transform 数据为timeline范围内的transfrom
     * Convert the transform data to transfrom within the timeline range
     *
     * @param timelineWidth  时间线的宽 the timeline width
     * @param timelineHeight 时间线的高 the timeline height
     * @param filePath       文件路径  the file path
     * @param rectSize       裁剪区域的宽高  the rect size
     * @param transFormData  转换数据 the transform data
     * @return timeline坐标系下的转换数据
     */
    private Map<String, Float> parseTransToTimeline(int timelineWidth, int timelineHeight,
                                                    String filePath, int[] rectSize, Map<String, Float> transFormData) {
        NvsAVFileInfo avFileInfo = mStreamingContext.getAVFileInfo(filePath);
        if (avFileInfo == null) {
            return transFormData;
        }
        float transXInView = transFormData.get(STORYBOARD_KEY_TRANS_X);
        float transYInView = transFormData.get(STORYBOARD_KEY_TRANS_Y);
        int videoStreamRotation = avFileInfo.getVideoStreamRotation(0);
        NvsSize dimension = avFileInfo.getVideoStreamDimension(0);
        int height;
        int width;
        if (videoStreamRotation % 2 == 0) {
            height = dimension.height;
            width = dimension.width;
        } else {
            width = dimension.height;
            height = dimension.width;
        }

        float fileRatio = width * 1F / height;
        float timelineRatio = timelineWidth * 1F / timelineHeight;

        float fileWidthInTimeline;
        float fileHeightInTimeline;
        if (fileRatio > timelineRatio) {
            //文件宽对齐 File width alignment
            fileWidthInTimeline = timelineWidth;
            fileHeightInTimeline = fileWidthInTimeline / fileRatio;
        } else {//高对齐 High alignment
            fileHeightInTimeline = timelineHeight;
            fileWidthInTimeline = fileHeightInTimeline * fileRatio;
        }
        float rectWidthInTimeline;
        float rectHeightInTimeline;
        float rectRatio = rectSize[0] * 1F / rectSize[1];
        if (rectRatio > fileRatio) {
            //裁剪区域宽对齐 Cropped area width alignment
            rectWidthInTimeline = fileWidthInTimeline;
            rectHeightInTimeline = rectWidthInTimeline / rectRatio;
        } else {
            rectHeightInTimeline = fileHeightInTimeline;
            rectWidthInTimeline = rectHeightInTimeline * rectRatio;
        }

        float transXInTimeline = transXInView / rectSize[0] * rectWidthInTimeline;
        float transYInTimeline = transYInView / rectSize[1] * rectHeightInTimeline;
        //Mask Generator方案需要归一化
        //Mask generator scheme needs normalization
        transFormData.put(STORYBOARD_KEY_TRANS_X, transXInTimeline / fileWidthInTimeline * 2);
        //Timeline Y坐标轴反向 Timeline Y axis reverse
        transFormData.put(STORYBOARD_KEY_TRANS_Y, -transYInTimeline / fileHeightInTimeline * 2);
        return transFormData;
    }

    /**
     * 转换transform 数据为view范围内的transfrom
     * Convert transform data to transfrom within view scope
     *
     * @param timelineWidth  时间线的宽 the timeline width
     * @param timelineHeight 时间线的高 the timeline height
     * @param filePath       文件路径 the file path
     * @param rectSize       裁剪区域的宽高 the rect size
     * @param transFormData  转换数据 the transform data
     * @return view坐标系下的转换数据
     */
    private Map<String, Float> parseTransToView(int timelineWidth, int timelineHeight, String filePath,
                                                int[] rectSize, Map<String, Float> transFormData) {
        NvsAVFileInfo avFileInfo = mStreamingContext.getAVFileInfo(filePath);
        if (avFileInfo == null) {
            return transFormData;
        }
        float transXInTimeline = transFormData.get(STORYBOARD_KEY_TRANS_X);
        float transYInTimeline = transFormData.get(STORYBOARD_KEY_TRANS_Y);
        int videoStreamRotation = avFileInfo.getVideoStreamRotation(0);
        NvsSize dimension = avFileInfo.getVideoStreamDimension(0);
        int height;
        int width;
        if (videoStreamRotation % 2 == 0) {
            height = dimension.height;
            width = dimension.width;
        } else {
            width = dimension.height;
            height = dimension.width;
        }

        float fileRatio = width * 1F / height;
        float timelineRatio = timelineWidth * 1F / timelineHeight;

        float fileWidthInTimeline;
        float fileHeightInTimeline;
        //文件宽对齐 File width alignment
        if (fileRatio > timelineRatio) {
            fileWidthInTimeline = timelineWidth;
            fileHeightInTimeline = fileWidthInTimeline / fileRatio;
        } else {
            //高对齐 High alignment
            fileHeightInTimeline = timelineHeight;
            fileWidthInTimeline = fileHeightInTimeline * fileRatio;
        }
        float rectWidthInTimeline;
        float rectHeightInTimeline;
        float rectRatio = rectSize[0] * 1F / rectSize[1];
        if (rectRatio > fileRatio) {
            //裁剪区域宽对齐 Cropped area width alignment
            rectWidthInTimeline = fileWidthInTimeline;
            rectHeightInTimeline = rectWidthInTimeline / rectRatio;
        } else {
            rectHeightInTimeline = fileHeightInTimeline;
            rectWidthInTimeline = rectHeightInTimeline * rectRatio;
        }

        float transXInView = transXInTimeline / rectWidthInTimeline * rectSize[0];
        float transYInView = transYInTimeline / rectHeightInTimeline * rectSize[1];
        transFormData.put(STORYBOARD_KEY_TRANS_X, transXInView * fileWidthInTimeline / 2F);
        transFormData.put(STORYBOARD_KEY_TRANS_Y, transYInView * fileHeightInTimeline / 2F);
        return transFormData;
    }

    /**
     * 页面变化时更新记录的数据
     * on page size change, record the data
     *
     * @param size 页面数据
     */
    public void handleCutData(Point size) {
        //需要考虑老的草稿数据 Old draft data needs to be considered
        if ((mCutData != null) && (!mCutData.isOldData())) {
            Map<String, Float> transFromData = parseTransToView(mOriginalTimelineWidth, mOriginalTimelineHeight, mVideoClip.getFilePath(),
                    new int[]{size.x, size.y}, mCutData.getTransformData());
            mCutData.setTransformData(transFromData);
        }
    }


    /**
     * 添加裁剪需要的特效
     * Add the effects needed for clipping
     */
    public void addCutEffect() {

        ICutRegionFragment videoFragment = getView().getCutUiView();
        if (videoFragment == null) {
            return;
        }

        //删除旧方案的裁剪特技
        mVideoClip.removeVideoFx(TYPE_RAW_BUILTIN, SUB_TYPE_CROPPER);

        //删除非裁剪用特效，在裁剪添加完后，再恢复这些特技
        //Delete special effects for non clipping, and restore these special effects after clipping and adding.
        int rawFxCount = mVideoClip.getRawFxCount();
        List<MeicamVideoFx> removedRawFxList = new ArrayList<>();
        if (rawFxCount > 0) {
            for (int index = 0; index < rawFxCount; index++) {
                MeicamVideoFx videoFx = mVideoClip.getRawFxByIndex(index);
                if (videoFx != null) {
                    String subType = videoFx.getSubType();
                    if ((!SUB_TYPE_CROPPER_TRANSFORM.equals(subType))
                            && (!SUB_TYPE_POST_CROPPER_TRANSFORM.equals(subType))
                            && (!SUB_TYPE_CROPPER_EXT.equals(subType))
                            && (!MeicamVideoFx.SubType.SUB_TYPE_ALPHA.equals(subType))
                            && (!MeicamVideoFx.SubType.SUB_TYPE_AR_SCENE.equals(subType))) {
                        MeicamVideoFx removeVideoFx = VideoClipCommand.removeFx(mVideoClip, videoFx);
                        if (removeVideoFx != null) {
                            removedRawFxList.add(removeVideoFx);
                        }
                    }
                }
            }
        }

        VideoClipCommand.setParam(mVideoClip, VideoClipCommand.PARAM_ENABLE_RAW_SOURCE_MODE, true);
        mVideoClip.enableRawSourceMode(true);

        float oldRectWidth = mVideoClip.getRectWidth();
        float oldRectHeight = mVideoClip.getRectHeight();

        MeicamVideoFx transformVideoFx = mVideoClip.getVideoFxByType(TYPE_RAW_BUILTIN, SUB_TYPE_CROPPER_TRANSFORM);
        if (transformVideoFx == null) {
            transformVideoFx = VideoClipCommand.appendFx(mVideoClip, TYPE_RAW_BUILTIN, SUB_TYPE_CROPPER_TRANSFORM, NvsConstants.FX_TRANSFORM_2D);
            VideoFxCommand.setBooleanVal(transformVideoFx, NvsConstants.KEY_CROPPER_IS_NORMALIZED_COORD, true);
            VideoFxCommand.setBooleanVal(transformVideoFx, NvsConstants.KEY_CROPPER_FORCE_IDENTICAL_POSITION, true);
        }

        Map<String, Float> transFromData = videoFragment.getTransFromData(mOriginalTimelineWidth, mOriginalTimelineHeight);
        parseTransToTimeline(mOriginalTimelineWidth, mOriginalTimelineHeight, mVideoClip.getFilePath(),
                videoFragment.getRectViewSize(), transFromData);
        VideoFxCommand.setFloatVal(transformVideoFx, NvsConstants.KEY_CROPPER_TRANS_X, transFromData.get(STORYBOARD_KEY_TRANS_X));
        VideoFxCommand.setFloatVal(transformVideoFx, NvsConstants.KEY_CROPPER_TRANS_Y, transFromData.get(STORYBOARD_KEY_TRANS_Y));
        VideoFxCommand.setFloatVal(transformVideoFx, NvsConstants.KEY_CROPPER_SCALE_X, transFromData.get(STORYBOARD_KEY_SCALE_X));
        VideoFxCommand.setFloatVal(transformVideoFx, NvsConstants.KEY_CROPPER_SCALE_Y, transFromData.get(STORYBOARD_KEY_SCALE_Y));
        VideoFxCommand.setFloatVal(transformVideoFx,  NvsConstants.KEY_CROPPER_ROTATION, -transFromData.get(STORYBOARD_KEY_ROTATION_Z));

        float fullScale = 1F;
        float[] regionData = videoFragment.getRegionData(new float[]{1F, 1F});
        if (regionData != null && regionData.length >= 8) {

            int ratio = videoFragment.getRatio();
            float ratioValue = videoFragment.getRatioValue();
            CommonData.AspectRatio aspectRatio = CommonData.AspectRatio.getAspectRatio(ratio);
            String cropperRatio;
            if (aspectRatio == null) {
                cropperRatio = NvsConstants.VALUE_CROPPER_FREE;
            } else {
                cropperRatio = aspectRatio.getStringValue();
            }

            MeicamVideoFx cropperVideoFx = mVideoClip.getVideoFxByType(TYPE_RAW_BUILTIN, SUB_TYPE_CROPPER_EXT);
            if (cropperVideoFx == null) {
                cropperVideoFx = VideoClipCommand.appendFx(mVideoClip, TYPE_RAW_BUILTIN, SUB_TYPE_CROPPER_EXT, NvsConstants.Crop.NAME);
            }
            if (cropperVideoFx == null) {
                LogUtils.e("PropertyVideoFx is null");
                return;
            }

            VideoFxCommand.setStringVal(cropperVideoFx, NvsConstants.KEY_CROPPER_RATIO, cropperRatio);
            VideoFxCommand.setFloatVal(cropperVideoFx, NvsConstants.KEY_CROPPER_ASSET_ASPECT_RATIO, ratioValue);

            float halfWidth = mVideoClip.getOriginalWidth() / 2F;
            float halfHeight = mVideoClip.getOriginalHeight() / 2F;

            float bLeft = regionData[0] * halfWidth;
            float bTop = regionData[1] * halfHeight;
            float bRight = regionData[4] * halfWidth;
            float bBottom = regionData[5] * halfHeight;

            VideoFxCommand.setFloatVal(cropperVideoFx, NvsConstants.Crop.BOUNDING_LEFT, bLeft);
            VideoFxCommand.setFloatVal(cropperVideoFx, NvsConstants.Crop.BOUNDING_TOP, bTop);
            VideoFxCommand.setFloatVal(cropperVideoFx, NvsConstants.Crop.BOUNDING_RIGHT, bRight);
            VideoFxCommand.setFloatVal(cropperVideoFx, NvsConstants.Crop.BOUNDING_BOTTOM, bBottom);

            MeicamVideoFx fullTransformFx = mVideoClip.getVideoFxByType(TYPE_RAW_BUILTIN, SUB_TYPE_POST_CROPPER_TRANSFORM);
            if (fullTransformFx == null) {
                fullTransformFx = VideoClipCommand.appendFx(mVideoClip, TYPE_RAW_BUILTIN, SUB_TYPE_POST_CROPPER_TRANSFORM, NvsConstants.FX_TRANSFORM_2D);
            }
            float cropFxWidth = Math.abs(bRight - bLeft);
            float cropFxHeight = Math.abs(bBottom - bTop);
            mVideoClip.setRectWidth(cropFxWidth);
            mVideoClip.setRectHeight(cropFxHeight);

            PointF timelineSize = new PointF(mOriginalTimelineWidth, mOriginalTimelineHeight);
            PointF fileSizeInTimeline = assetSizeInBox(timelineSize, halfWidth / halfHeight);
            float scaleFile = fileSizeInTimeline.x / halfWidth / 2F;
            float rectSizeInTimelineWidthBeforeScale = cropFxWidth /(2F * halfWidth) * fileSizeInTimeline.x;
            float rectSizeInTimelineHeightBeforeScale = cropFxHeight /(2F * halfHeight) * fileSizeInTimeline.y;
            PointF rectSizeInTimelineAfterScale = assetSizeInBox(timelineSize, cropFxWidth / cropFxHeight);
            double ratioW = rectSizeInTimelineAfterScale.x / rectSizeInTimelineWidthBeforeScale;
            double ratioH = rectSizeInTimelineAfterScale.y / rectSizeInTimelineHeightBeforeScale;
            double scale = ratioW;
            if (ratioW - 1F < 0.001) {
                scale = ratioH;
            }
            fullScale = (float) scale;
            VideoFxCommand.setFloatVal(fullTransformFx, NvsConstants.KEY_CROPPER_SCALE_X, (float) (scale * scaleFile));
            VideoFxCommand.setFloatVal(fullTransformFx, NvsConstants.KEY_CROPPER_SCALE_Y, (float) (scale * scaleFile));
        }


        //倒序恢复之前删除的特技
        //Reverse order restores the previously deleted trick
        if (!removedRawFxList.isEmpty()) {
            for (int index = removedRawFxList.size() - 1; index >= 0; index--) {
                VideoClipCommand.appendFx(mVideoClip, removedRawFxList.get(index),true);
            }
        }
        MeicamVideoFx propertyVideoFx = mVideoClip.findPropertyVideoFx();
        if (propertyVideoFx != null && propertyVideoFx.getObjectVal(NvsConstants.KEY_PROPERTY_MASK_REGION_INFO) != null) {
            float rectWidth = mVideoClip.getRectWidth();
            float rectHeight = mVideoClip.getRectHeight();
            PointF timelineSize = new PointF(mOriginalTimelineWidth, mOriginalTimelineHeight);
            NvMaskModel maskModel = mVideoClip.maskModel;
            float assetsWidth = maskModel.assetsWidth;
            float assetsHeight = maskModel.assetsHeight;
            PointF rectSizeInTimelineAfterScale = assetSizeInBox(timelineSize, assetsWidth / assetsHeight);
            PointF newRectSizeInTimelineAfterScale = assetSizeInBox(timelineSize, rectWidth / rectHeight);
            float ratioW = newRectSizeInTimelineAfterScale.x / rectSizeInTimelineAfterScale.x;
            float ratioH = newRectSizeInTimelineAfterScale.y / rectSizeInTimelineAfterScale.y;
            float newScale = ratioW;
            if (ratioW - 1F < 0.001) {
                newScale = ratioH;
            }
            Object objectVal = propertyVideoFx.getObjectVal(NvsConstants.KEY_PROPERTY_MASK_REGION_INFO);
            if (objectVal instanceof MeicamMaskRegionInfo) {
                float deltaScale = 1f / newScale;
                if (Math.abs(fullScale - 1) < 0.001F) {
                    deltaScale = 1F;
                }
                EditorEngine.adjustMaskGeneratorData(propertyVideoFx, NvsConstants.KEY_PROPERTY_MASK_REGION_INFO,oldRectWidth / rectWidth * deltaScale / maskModel.transform.lastScale,  oldRectHeight / rectHeight * deltaScale / maskModel.transform.lastScale);
                maskModel.regionInfo = (MeicamMaskRegionInfo) objectVal;
                maskModel.transform.lastScale = deltaScale;
                VideoClipCommand.updateMaskModel(mVideoClip, VideoClipCommand.MASK_KEY_LAST_SCALE, deltaScale);
                VideoClipCommand.updateMaskModel(mVideoClip, VideoClipCommand.MASK_KEY_REGION, (MeicamMaskRegionInfo)objectVal);
            }

            PointF fileSizeInTimeline = assetSizeInBox(timelineSize, assetsWidth / assetsHeight);
            float oldValue = Math.min(fileSizeInTimeline.y, fileSizeInTimeline.x);
            rectSizeInTimelineAfterScale = assetSizeInBox(timelineSize, rectWidth / rectHeight);
            float newValue = Math.min(rectSizeInTimelineAfterScale.y, rectSizeInTimelineAfterScale.x);
            float modelScale = oldValue / newValue;
            EditorEngine.adjustMaskModel(mVideoClip,  modelScale);
        }
        getView().exit();
    }


    private static PointF assetSizeInBox(PointF boxSize, float assetAspectRatio) {
        PointF pointF = new PointF();
        float boxSizeRate = boxSize.x / boxSize.y;
        if (boxSizeRate > assetAspectRatio) {
            //高对齐
            pointF.y = boxSize.y;
            pointF.x = pointF.y * assetAspectRatio;
        } else {//宽对齐
            pointF.x = boxSize.x;
            pointF.y = pointF.x / assetAspectRatio;
        }
        return pointF;
    }

    /**
     * 通过视频片段路径获取原始的时间线分辨率信息
     * <P></P>
     * Gets video edit original resolution by clip.
     *
     * @param path the path
     * @return the video edit resolution by clip
     */
    public NvsVideoResolution getOriginalResolutionByClip(String path) {

        int compileRes = 720;
        int imageWidth = 720, imageHeight = 1080;
        if (!TextUtils.isEmpty(path)) {
            NvsAVFileInfo avFileInfo = NvsStreamingContext.getInstance().getAVFileInfo(path);
            if (avFileInfo != null) {
                NvsSize dimension = avFileInfo.getVideoStreamDimension(0);
                int streamRotation = avFileInfo.getVideoStreamRotation(0);
                imageWidth = dimension.width;
                imageHeight = dimension.height;
                if (streamRotation == 1 || streamRotation == 3) {
                    imageWidth = dimension.height;
                    imageHeight = dimension.width;
                }
            }
        }

        NvsVideoResolution videoEditRes = new NvsVideoResolution();
        Point size = new Point();
        float imageToTimelineRatio;
        if (imageWidth > imageHeight) {
            imageToTimelineRatio = compileRes * 1.0F / imageWidth;
            size.set(compileRes, (int) (imageHeight * imageToTimelineRatio));
        } else {
            imageToTimelineRatio = compileRes * 1.0F / imageHeight;
            size.set((int) (imageWidth * imageToTimelineRatio), compileRes);
        }
        videoEditRes.imageWidth = alignedData(size.x, 4);
        videoEditRes.imageHeight = alignedData(size.y, 2);
        LogUtils.d("getOriginalResolutionByClip   ", videoEditRes.imageWidth + "     " + videoEditRes.imageHeight);
        return videoEditRes;
    }

    /**
     * 整数对齐
     * Integer alignment
     *
     * @param data，源数据
     * @param num      对齐的数据
     * @return the value
     */
    private int alignedData(int data, int num) {
        return data - data % num;
    }
}
