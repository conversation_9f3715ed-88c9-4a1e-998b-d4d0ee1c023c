package com.meishe.myvideo.fragment;

import android.view.View;
import android.widget.SeekBar;

import com.meishe.base.model.BaseMvpFragment;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.myvideo.R;
import com.meishe.myvideo.fragment.iview.CaptionStyleView;
import com.meishe.myvideo.fragment.presenter.CaptionStylePresenter;
import com.meishe.myvideo.view.MYSeekBarTextView;


/**
 * The type Caption letter spacing fragment.
 * 文字说明字母间距的fragment
 * <AUTHOR>
 * @desc 字幕间距
 */
public class CaptionLetterSpacingFragment extends BaseMvpFragment<CaptionStylePresenter> implements CaptionStyleView {

    private CaptionStyleFragment.CaptionStyleEventListener mEventListener;
    private MYSeekBarTextView mWordSpaceSeekBar;
    private MYSeekBarTextView mLineSpaceSeekBar;

    public CaptionLetterSpacingFragment() {
        mPresenter = new CaptionStylePresenter(null);
    }

    public static CaptionLetterSpacingFragment create(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>lip captionClip, CaptionStyleFragment.CaptionStyleEventListener listener) {
        CaptionLetterSpacingFragment fragment = new CaptionLetterSpacingFragment();
        fragment.updateCaptionClip(captionClip);
        fragment.setEventListener(listener);
        return fragment;
    }

    public void setEventListener(CaptionStyleFragment.CaptionStyleEventListener listener) {
        this.mEventListener = listener;
    }

    @Override
    protected CaptionStylePresenter createPresenter() {
        return mPresenter;
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_caption_letter_space;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        mWordSpaceSeekBar = rootView.findViewById(R.id.sb_word_space);
        mLineSpaceSeekBar = rootView.findViewById(R.id.sb_line_space);
        mWordSpaceSeekBar.setMax(200);
        mLineSpaceSeekBar.setMax(40);
        initListener();
    }

    @Override
    protected void initData() {
        mWordSpaceSeekBar.setProgress((int) mPresenter.getCaptionWordSpace());
        mLineSpaceSeekBar.setProgress((int) mPresenter.getCaptionLineSpace());
    }

    private void initListener() {
        mWordSpaceSeekBar.setOnSeekBarChangeListener(new MYSeekBarTextView.OnSeekBarListener() {
            @Override
            public void onStopTrackingTouch(int progress, String name) {

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (!fromUser) {
                    return;
                }
                mPresenter.setCaptionWordSpace(progress);
                if (mEventListener != null) {
                    mEventListener.onCaptionLocalChanged();
                }
            }
        });

        mLineSpaceSeekBar.setOnSeekBarChangeListener(new MYSeekBarTextView.OnSeekBarListener() {
            @Override
            public void onStopTrackingTouch(int progress, String name) {

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (!fromUser) {
                    return;
                }
                mPresenter.setCaptionLineSpace(progress);
                if (mEventListener != null) {
                    mEventListener.onCaptionLocalChanged();
                }
            }
        });
    }

    /**
     * 更新字幕片段
     * Update caption clip
     *
     * @param captionClip the new caption clip
     */
    public void updateCaptionClip(MeicamCaptionClip captionClip) {
        mPresenter.updateCaptionClip(captionClip);
        if (mWordSpaceSeekBar != null) {
            mWordSpaceSeekBar.setProgress((int) mPresenter.getCaptionWordSpace());
        }
        if (mLineSpaceSeekBar != null) {
            mLineSpaceSeekBar.setProgress((int) mPresenter.getCaptionLineSpace());
        }
    }
}
