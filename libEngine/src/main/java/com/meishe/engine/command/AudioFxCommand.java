package com.meishe.engine.command;

import com.meishe.base.utils.LogUtils;
import com.meishe.engine.bean.MeicamAudioClip;
import com.meishe.engine.bean.MeicamAudioFx;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/9/6 11:06
 * @Description :音频特效命令 The audio effect command.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class AudioFxCommand {
    private static String TAG = "audioFx";

    private static String getTag(MeicamAudioFx audioFx){
        return TAG + audioFx.getExtraTag() + "[" +audioFx.getDesc();
    }

    public static MeicamAudioFx getItByTag(String tag){
        try {
            tag = tag.replaceAll(TAG, "");
            int index = tag.lastIndexOf("[");
            String extraTag = "";
            if (index > 0) {
                extraTag = tag.substring(0, index);
            }
            String fxDesc = tag.substring(index + 1);
            MeicamAudioClip audioClip = AudioCommand.getItByTag(extraTag);
            if (audioClip != null) {
                return audioClip.getAudioFxById(fxDesc);
            }
        } catch (Exception e) {
            LogUtils.e(e);
            LogUtils.e(tag);
        }
        return null;
    }
}
