package com.meishe.myvideo.audio;


/**
 * 录音各种状态的回调类
 * Recording various states of the callback class
 */
public class AudioStatusListener {


    /**
     * 开始录音的回调
     * Start recording the callback
     * @param audioPath the audio path
     */
    public void onStartRecording(String audioPath) {
    }

    /**
     * 录音时的buffer
     * The recording  of buffer
     * @param data   PCM Data
     * @param length 长度
     */
    public void onRecordData(short[] data, int length) {
    }

    /**
     * 波形数据回调
     * Waveform data callback
     * @param wave   float[] 一组波形数据,波形上边和下边数据依次交替进行
     * @param during the during
     * @param path   the path
     */
    public void onWaveDate(float[] wave, int during,String path) {

    }

    /**
     * 录音时的音量
     * The volume of the recording
     * @param volume 音量
     */
    public void onVoiceVolume(int volume) {
    }


    /**
     * 录音失败
     * The recording failure
     * @param code     错误码
     * @param errorMsg 错误信息描述
     */
    public void onRecordError(int code, String errorMsg) {

    }


    /**
     * 保存文件失败
     * Failed to save file
     * @param error the error
     */
    public void onFileSaveFailed(String error) {

    }

    /**
     * 保存录音文件成功
     * The recording file was saved successfully
     * @param fileUri  保存文件的路径
     * @param duration 时长
     */
    public void onFileSaveSuccess(String fileUri, long duration) {

    }


    /**
     * 停止录音的回调
     * Stop the recording callback
     */
    public void onStopRecording() {
    }


}
