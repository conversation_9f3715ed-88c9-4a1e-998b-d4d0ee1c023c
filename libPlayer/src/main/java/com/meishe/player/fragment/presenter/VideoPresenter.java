package com.meishe.player.fragment.presenter;

import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Point;
import android.graphics.PointF;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.meicam.sdk.NvsColor;
import com.meicam.sdk.NvsLiveWindowExt;
import com.meicam.sdk.NvsRational;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimelineCaption;
import com.meicam.sdk.NvsTimelineCompoundCaption;
import com.meicam.sdk.NvsVideoFx;
import com.meicam.sdk.NvsVideoResolution;
import com.meishe.base.bean.FloatPoint;
import com.meishe.base.constants.Constants;
import com.meishe.base.model.Presenter;
import com.meishe.base.utils.GsonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.engine.bean.ClipInfo;
import com.meishe.engine.bean.KeyFrameProcessor;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.engine.bean.MeicamCompoundCaptionClip;
import com.meishe.engine.bean.MeicamFxParam;
import com.meishe.engine.bean.MeicamKeyFrame;
import com.meishe.engine.bean.MeicamMaskRegionInfo;
import com.meishe.engine.bean.MeicamStickerClip;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamTimelineVideoFxClip;
import com.meishe.engine.bean.MeicamTransition;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoFx;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.engine.bean.MeicamWaterMark;
import com.meishe.engine.bean.NvMaskModel;
import com.meishe.engine.bean.NvsObject;
import com.meishe.engine.command.CaptionCommand;
import com.meishe.engine.command.CompCaptionCommand;
import com.meishe.engine.command.KeyFrameCommand;
import com.meishe.engine.command.KeyFrameHolderCommand;
import com.meishe.engine.command.StickerCommand;
import com.meishe.engine.command.TimelineCommand;
import com.meishe.engine.command.TimelineFxCommand;
import com.meishe.engine.command.VideoClipCommand;
import com.meishe.engine.command.VideoFxCommand;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.engine.interf.IKeyFrameProcessor;
import com.meishe.engine.util.ColorUtil;
import com.meishe.player.R;
import com.meishe.player.common.utils.ImageConverter;
import com.meishe.player.fragment.VideoFragment;
import com.meishe.player.fragment.iview.VideoFragmentView;
import com.meishe.player.view.bean.OperationBoxInfo;
import com.meishe.player.view.bean.PipTransformInfo;
import com.meishe.player.view.mask.NvMaskHelper;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.meishe.base.constants.Constants.BLUR;
import static com.meishe.base.constants.Constants.EDIT_MODE_CAPTION;
import static com.meishe.base.constants.Constants.EDIT_MODE_COMPOUND_CAPTION;
import static com.meishe.base.constants.Constants.EDIT_MODE_NONE;
import static com.meishe.base.constants.Constants.EDIT_MODE_STICKER;
import static com.meishe.base.constants.Constants.EDIT_MODE_WATERMARK;
import static com.meishe.base.constants.Constants.EDIT_MODE_WATERMARK_EFFECT;
import static com.meishe.base.constants.Constants.MOSAIC;
import static com.meishe.base.constants.Constants.MOSAIC_DEGREE;
import static com.meishe.base.constants.Constants.MOSAIC_NUM;
import static com.meishe.base.constants.Constants.NONE;
import static com.meishe.engine.bean.CommonData.TYPE_BUILD_IN;
import static com.meishe.engine.bean.CommonData.TYPE_RAW_BUILTIN;
import static com.meishe.engine.bean.MeicamVideoFx.INVALID_VALUE;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_CROPPER;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_CROPPER_EXT;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_MASK;
import static com.meishe.engine.constant.NvsConstants.BLURNAME;
import static com.meishe.engine.constant.NvsConstants.FX_TRANSFORM_2D_ROTATION;
import static com.meishe.engine.constant.NvsConstants.FX_TRANSFORM_2D_SCALE_X;
import static com.meishe.engine.constant.NvsConstants.FX_TRANSFORM_2D_SCALE_Y;
import static com.meishe.engine.constant.NvsConstants.FX_TRANSFORM_2D_TRANS_X;
import static com.meishe.engine.constant.NvsConstants.FX_TRANSFORM_2D_TRANS_Y;
import static com.meishe.engine.constant.NvsConstants.KEY_REGION;
import static com.meishe.engine.constant.NvsConstants.KEY_UNIT_RADIUS;
import static com.meishe.engine.constant.NvsConstants.KEY_UNIT_SIZE;
import static com.meishe.engine.constant.NvsConstants.MOSAICNAME;
import static com.meishe.player.view.OperationBox.FOURTH_QUADRANT;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/1/22 10:36
 * @Description :video fragment 逻辑处理类 Video fragment logic processing class
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class VideoPresenter extends Presenter<VideoFragmentView> {
    private final static float PIP_MIM_SCALE = 0.1F;
    private final static float PIP_MAX_SCALE = 10F;

    public static final int EDIT_MODE_EDIT_TIMELINE = 1;
    public static final int EDIT_MODE_EDIT_COVER = 2;

    private MeicamTimeline mTimeline;
    private NvsStreamingContext mStreamingContext;
    private MeicamVideoFx mPropertyVideoFx;
    private @VideoFragment.FxEditMode
    int mFxEditMode;
    private int mEditMode;
    private NvsObject<?> mEditFx;
    private IBaseInfo mBaseInfo;
    private boolean hasChangeWaterList = false;
    private PointF mRealLiveWindrowSize;
    private MeicamVideoClip meicamVideoClip;
    private boolean mHaveKeyFrame;

    public VideoPresenter(MeicamTimeline timeline, NvsStreamingContext streamingContext) {
        mTimeline = timeline;
        mStreamingContext = streamingContext;
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mRealLiveWindrowSize != null) {
            mRealLiveWindrowSize = null;
        }
    }

    /**
     * 连接时间线和实时预览图像窗口
     * Connect timeline with live window
     *
     * @param liveWindow the live window
     */
    public void connectTimeline(NvsLiveWindowExt liveWindow) {
        EditorEngine.getInstance().connectTimeline(mTimeline, liveWindow);
    }

    /**
     * 播放
     * Play
     *
     * @param startTime the start position开始时间
     * @param endTime   the end position结束时间
     */
    public void playVideo(long startTime, long endTime) {
        playVideo(startTime, endTime, 0);
    }

    /**
     * 播放
     * Play
     *
     * @param startTime the start position开始时间
     * @param endTime   the end position结束时间
     * @param flag      the flag播放标记
     */
    public void playVideo(long startTime, long endTime, int flag) {
        EditorEngine.getInstance().playBackTimeline(mTimeline, startTime, endTime, flag);
    }

    /**
     * 预览时间线的某个时间点
     * Seek to the position of timeline
     *
     * @param timestamp    the timestamp
     * @param seekShowMode the seek show mode
     */
    public void seekTimeline(long timestamp, int seekShowMode) {
        if (mEditMode == EDIT_MODE_EDIT_COVER) {
            seekShowMode |= NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER;
        }
        EditorEngine.getInstance().seekTimeline(mTimeline, timestamp, seekShowMode);
    }

    /**
     * Sets timeline.
     * 设置时间线
     * @param timeline the timeline
     */
    public void setTimeline(MeicamTimeline timeline) {
        this.mTimeline = timeline;
    }

    /**
     * 预览时间线的当前的时间点
     * Seek to the current position of timeline
     *
     * @param seekShowMode the seek show mode
     */
    public void seekTimeline(int seekShowMode) {
        if (mStreamingContext.getStreamingEngineState() != NvsStreamingContext.STREAMING_ENGINE_STATE_PLAYBACK) {
            if (mEditMode == EDIT_MODE_EDIT_COVER) {
                seekShowMode |= NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER;
            }
            EditorEngine.getInstance().seekTimeline(mTimeline, mTimeline.getCurrentPosition(), seekShowMode);
        }
    }

    /**
     * 获取视频分辨率
     * Get video resolution
     */
    public NvsVideoResolution getVideoResolution() {
        NvsVideoResolution videoResolution = mTimeline.getVideoResolution();
        if (videoResolution == null) {
            EditorEngine.getInstance().checkVideoResolution();
        }
        videoResolution = mTimeline.getVideoResolution();
        return videoResolution;
    }

    /**
     * 是否正在播放
     * Is it playing
     */
    public boolean isPlaying() {
        return mStreamingContext != null && mStreamingContext.getStreamingEngineState() == NvsStreamingContext.STREAMING_ENGINE_STATE_PLAYBACK;
    }

    /**
     * 停止播放
     * Stop play
     */
    public void stopVideo() {
        if (isPlaying()) {
            mStreamingContext.stop();
        }
    }

    /**
     * 更改变形特效的缩放和旋转
     * Change the scaling and rotation of transform fx
     *
     * @param scale the scale value
     * @param angle the angle value
     */
    public void changeTransformFxScaleAndRotate(NvsLiveWindowExt liveWindow, float scale, float angle) {
        //计算缩放后的边框的点 Calculate the point of the scaled border.
        if (angle == Float.MAX_VALUE) {
            angle = 0;
        }
        float oldScale, oldAngle, newAngle;
        if (mPropertyVideoFx != null) {
            long atTime = mTimeline.getCurrentPosition() - meicamVideoClip.getInPoint();
            MeicamKeyFrame keyFrame = null;
            KeyFrameProcessor<NvsVideoFx> frameProcessor = mPropertyVideoFx.keyFrameProcessor();
            if (frameProcessor.getKeyFrameCount(NvsConstants.KEY_CROPPER_TRANS_X) > 0) {
                keyFrame = frameProcessor.getKeyFrame(mTimeline.getCurrentPosition() - meicamVideoClip.getInPoint());
                if (keyFrame != null) {
                    atTime = keyFrame.getAtTime();
                    if (transition != null) {
                        KeyFrameCommand.setOffsetTime(keyFrame, (long) (transition.getDuration() / 2F));
                        atTime += (long) (transition.getDuration() / 2F);
                    }
                    oldScale = (float) keyFrame.getFloatValue(MeicamKeyFrame.SCALE_X, atTime);
                    oldAngle = (float) keyFrame.getFloatValue(MeicamKeyFrame.ROTATION, atTime);

                } else {
                    LogUtils.e("error check it!!!");
                    return;
                }
            } else {
                oldScale = getPropertyFxParamValue(FX_TRANSFORM_2D_SCALE_X, 1);
                oldAngle = getPropertyFxParamValue(FX_TRANSFORM_2D_ROTATION, 0);
            }

            float newScale = oldScale * scale;
            if (newScale > PIP_MAX_SCALE) {
                newScale = PIP_MAX_SCALE;
            }
            if (Math.abs(newScale) < PIP_MIM_SCALE) {
                newScale = newScale < 0 ? -PIP_MIM_SCALE : PIP_MIM_SCALE;
            }
            newAngle = calculateFinalAngle(oldAngle, angle);
            if (keyFrame != null) {
                KeyFrameCommand.setFloatValAt(keyFrame, MeicamKeyFrame.SCALE_X, newScale, atTime);
                KeyFrameCommand.setFloatValAt(keyFrame, MeicamKeyFrame.SCALE_Y, Math.abs(newScale), atTime);
                KeyFrameCommand.setFloatValAt(keyFrame, MeicamKeyFrame.ROTATION, newAngle, atTime);
            } else {
                VideoFxCommand.setFloatVal(mPropertyVideoFx, FX_TRANSFORM_2D_SCALE_X, newScale);
                VideoFxCommand.setFloatVal(mPropertyVideoFx, FX_TRANSFORM_2D_SCALE_Y, Math.abs(newScale));
                VideoFxCommand.setFloatVal(mPropertyVideoFx, FX_TRANSFORM_2D_ROTATION, newAngle);
            }
            seekTimeline(0);
            refreshVideoBounding(liveWindow, mTimeline.getCurrentPosition());
        }
    }


    /**
     * 更改变形特效的位置
     * Change the scaling and rotation of transform fx
     *
     * @param lastPointF the the last point
     * @param newPointF  the new point
     */
    public void changeTransformFxPosition(NvsLiveWindowExt liveWindow, PointF lastPointF, PointF newPointF) {
        NvsVideoResolution nvsVideoResolution = mTimeline.getVideoResolution();
        if (nvsVideoResolution == null) {
            LogUtils.e("nvsVideoResolution==null");
            return;
        }
        int liveWindowHeight = (int) EditorEngine.getInstance().getLiveWindrowSize(liveWindow).y;
        float translationX = (newPointF.x - lastPointF.x);
        float translationY = (newPointF.y - lastPointF.y);
        if ((translationX == 0) && (translationY == 0)) {
            return;
        }
        float ratio = nvsVideoResolution.imageHeight * 1f / liveWindowHeight;
        translationX = translationX * ratio;
        translationY = translationY * ratio;
        float oldTranslationX, oldTranslationY;

        float[] finalTranslation;
        MeicamVideoFx propertyVideoFx = meicamVideoClip.findPropertyVideoFx();
        KeyFrameProcessor<NvsVideoFx> keyFrameProcessor = propertyVideoFx.keyFrameProcessor();
        if (keyFrameProcessor.haveKeyFrame(NvsConstants.KEY_CROPPER_TRANS_X)) {
            MeicamKeyFrame keyFrame = keyFrameProcessor.getKeyFrame(mTimeline.getCurrentPosition() - meicamVideoClip.getInPoint());
            if (keyFrame != null) {
                long atTime = keyFrame.getAtTime();
                if (transition != null) {
                    long deltaTime = (long) (transition.getDuration() / 2F);
                    KeyFrameCommand.setOffsetTime(keyFrame, deltaTime);
                    atTime += deltaTime;
                } else {
                    keyFrame.setOffsetTime(0);
                }
                oldTranslationX = (float) keyFrame.getFloatValue(MeicamKeyFrame.TRANS_X, atTime);
                oldTranslationY = (float) keyFrame.getFloatValue(MeicamKeyFrame.TRANS_Y, atTime);
                if (oldTranslationX != -1 && oldTranslationY != -1) {
                    finalTranslation = calculateFinalPosition(oldTranslationX, oldTranslationY, translationX, translationY);
                    KeyFrameCommand.setFloatValAt(keyFrame, MeicamKeyFrame.TRANS_X, finalTranslation[0], atTime);
                    KeyFrameCommand.setFloatValAt(keyFrame, MeicamKeyFrame.TRANS_Y, finalTranslation[1], atTime);
                }
            }
        } else if (mPropertyVideoFx != null) {
            oldTranslationX = getPropertyFxParamValue(FX_TRANSFORM_2D_TRANS_X, 0);
            oldTranslationY = getPropertyFxParamValue(FX_TRANSFORM_2D_TRANS_Y, 0);
            finalTranslation = calculateFinalPosition(oldTranslationX, oldTranslationY, translationX, translationY);
            VideoFxCommand.setFloatVal(mPropertyVideoFx, FX_TRANSFORM_2D_TRANS_X, finalTranslation[0]);
            VideoFxCommand.setFloatVal(mPropertyVideoFx, FX_TRANSFORM_2D_TRANS_Y, finalTranslation[1]);
        }

        seekTimeline(0);
        refreshVideoBounding(liveWindow, mTimeline.getCurrentPosition());
    }


    /**
     * 获取属性特效的某些特效的值
     * Gets the value of some effects of an property effect
     *
     * @param propertyFxKey the property key
     */
    private float getPropertyFxParamValue(String propertyFxKey, float defaultValue) {
        float floatVal = mPropertyVideoFx.getFloatVal(propertyFxKey);
        return floatVal == INVALID_VALUE ? defaultValue : floatVal;
    }

    /**
     * 计算视频片段的最终角度（处理镜像情况）
     * Calculate the final angle
     *
     * @param oldAngle the old angle
     * @param newAngle the new angle
     * @return the final angle
     */
    private float calculateFinalAngle(float oldAngle, float newAngle) {
        //return (oldAngle + newAngle) % Constants.PIP_MAX_ROTATION;
        return (oldAngle + newAngle);
    }

    /**
     * 计算视频片段的最终位置（处理镜像情况）
     * Calculate the final position
     *
     * @param oldTranslationX the old translation x
     * @param oldTranslationY the old translation y
     * @param newTranslationX the new translation x
     * @param newTranslationY the new translation y
     * @return the final position
     */
    private float[] calculateFinalPosition(float oldTranslationX, float oldTranslationY,
                                           float newTranslationX, float newTranslationY) {
        float[] finalTranslation = new float[2];
        finalTranslation[0] = oldTranslationX + newTranslationX;
        finalTranslation[1] = oldTranslationY - newTranslationY;
        return finalTranslation;
    }


    /**
     * 打开特效编辑模式
     * Open fx edit mode
     *
     * @param editMode the edit mode
     * @param nvsFx    the nvsFx
     */
    public void openFxEditMode(@VideoFragment.FxEditMode int editMode, NvsObject<?> nvsFx) {
        openFxEditModeWithShow(editMode, nvsFx, null);
    }

    /**
     * 关闭特效编辑模式
     * Close fx edit mode.
     */
    public void closeFxEditMode() {
        mFxEditMode = EDIT_MODE_NONE;
        mEditFx = null;
        mBaseInfo = null;
    }

    private void clearFxInfo() {
        mEditFx = null;
        mBaseInfo = null;
        mBoxInfo = null;
    }

    /**
     * 打开特效编辑模式并展示当前特效
     * Open fx edit mode
     *
     * @param editMode   the edit mode
     * @param nvsFx      the nvsFx
     * @param liveWindow the liveWindow
     */
    private MeicamTransition transition;

    public void openFxEditModeWithShow(@VideoFragment.FxEditMode int editMode, NvsObject<?> nvsFx, NvsLiveWindowExt liveWindow) {
        mFxEditMode = editMode;
        mEditFx = nvsFx;
        if (nvsFx instanceof MeicamVideoClip) {
            meicamVideoClip = (MeicamVideoClip) nvsFx;
            mPropertyVideoFx = meicamVideoClip.findPropertyVideoFx();
            MeicamVideoTrack meicamVideoTrack = mTimeline.getVideoTrack(meicamVideoClip.getTrackIndex());
            if (meicamVideoTrack != null) {
                transition = meicamVideoTrack.getTransition(meicamVideoClip.getIndex() - 1);
            }
            refreshVideoBounding(liveWindow, mTimeline.getCurrentPosition());
        } else {
            openEditFx(liveWindow);
        }
    }


    /**
     * 打开特效编辑
     * Open fx edit
     *
     * @param liveWindow the liveWindow
     */
    private void openEditFx(NvsLiveWindowExt liveWindow) {
        if (liveWindow != null) {
            mBoxInfo = new OperationBoxInfo();
            List<PointF> transformPointList = null;
            List<List<PointF>> childPointList = null;
            KeyFrameProcessor<?> keyFrameProcessor = null;
            long tempAtTime = -1;
            if (mEditFx instanceof MeicamCaptionClip || mEditFx instanceof MeicamStickerClip) {
                keyFrameProcessor = ((IKeyFrameProcessor<?>) mEditFx).keyFrameProcessor();
                if (keyFrameProcessor.getKeyFrameCount() > 0) {
                    long atTime = mTimeline.getCurrentPosition() - keyFrameProcessor.getInPoint();
                    if (atTime >= 0) {
                        MeicamKeyFrame keyFrame = keyFrameProcessor.getKeyFrame(atTime);
                        if (keyFrame == null) {
                            /*只要设置过关键帧，需要先设置关键帧才能正确获取边框位置，获取后再把没有必要的关键帧删除
                            * As long as a keyframe has been set, it is necessary to first set the keyframe
                            * to correctly obtain the border position, and then delete unnecessary keyframes
                            * after obtaining them.
                            * */
                            keyFrame = keyFrameProcessor.addKeyFrame(atTime);
                            tempAtTime = atTime;
                        }
                        if (keyFrame != null) {
                            keyFrame.bindOnlyKeyFrame();
                        }
                    }
                }
            }
            if (mFxEditMode == EDIT_MODE_CAPTION) {
                // 字幕 Caption
                if (mEditFx instanceof MeicamCaptionClip) {
                    transformPointList = getTransformPointList(liveWindow, mTimeline.getCurrentPosition(), true);
                }
                mBoxInfo.setSecondQuadrantIcon(R.mipmap.left_align);
            } else if (mFxEditMode == EDIT_MODE_STICKER) {
                // 贴纸 Sticker
                if (mEditFx instanceof MeicamStickerClip) {
                    MeicamStickerClip sticker = (MeicamStickerClip) mEditFx;
                    transformPointList = getTransformPointList(liveWindow, mTimeline.getCurrentPosition(), true);
                    EditorEngine.getInstance().setStickerHasAudio(sticker.isHasAudio());
                    if (sticker.isHasAudio()) {
                        mBoxInfo.setThirdQuadrantIcon(sticker.getLeftVolume() > 0 ? R.mipmap.non_silence : R.mipmap.silence);
                    }
                }
                mBoxInfo.setSecondQuadrantIcon(R.mipmap.horizontal_flip);
            } else if (mFxEditMode == EDIT_MODE_COMPOUND_CAPTION) {
                //组合字幕 Compound caption
                if (mEditFx instanceof MeicamCompoundCaptionClip) {
                    MeicamCompoundCaptionClip caption = (MeicamCompoundCaptionClip) mEditFx;
                    childPointList = new ArrayList<>(caption.getCaptionItemCount());
                    transformPointList = updateCaptionCoordinates(caption, childPointList, liveWindow, true);
                }
            } else if (mFxEditMode == EDIT_MODE_WATERMARK) {
                //水印 Water mark
                if (mTimeline == null) {
                    return;
                }
                MeicamWaterMark watermark = mTimeline.getMeicamWaterMark();
                if (!TextUtils.isEmpty(watermark.getWatermarkFilePath())) {
                    if (!hasChangeWaterList) {
                        hasChangeWaterList = true;
                    }
                    NvsVideoResolution videoResolution = mTimeline.getVideoResolution();
                    if (videoResolution != null) {
                        watermark.setPointList(liveWindow, videoResolution.imageWidth, videoResolution.imageHeight);
                    }
                    transformPointList = watermark.getList();
                    seekTimeline(0);
                    mBoxInfo.setInsideCover(watermark.getWatermarkFilePath());

                }
            } else if (mFxEditMode == EDIT_MODE_WATERMARK_EFFECT) {
                addWatermark(liveWindow);
                //水印特效 The water mark effect
                if (mTimeline != null) {
                    MeicamTimelineVideoFxClip videoFxClip = mTimeline.getTimelineFxFromClipList(0);
                    if (videoFxClip != null) {
                        mEditFx = videoFxClip;
                        if (!TextUtils.isEmpty(videoFxClip.getDesc())) {
                            if (mBaseInfo == null) {
                                mBaseInfo = new BaseInfo();
                                if (BLURNAME.equals(videoFxClip.getDesc())) {
                                    mBaseInfo.setType(BLUR);
                                } else if (MOSAICNAME.equals(videoFxClip.getDesc())) {
                                    mBaseInfo.setType(MOSAIC);
                                }
                            }
                            seekTimeline(0);
                        }
                        transformPointList = videoFxClip.getPointList();
                        if (transformPointList == null) {
                            videoFxClip.updatePointList(liveWindow);
                            transformPointList = videoFxClip.getPointList();
                        }
                    }
                }
            }
            mBoxInfo.setFirstQuadrantIcon(R.mipmap.delete)
                    .setFourthQuadrantIcon(R.mipmap.scale)
                    .setCornerPointList(transformPointList)
                    .setInsidePointList(childPointList)
                    .setScaleQuadrant(FOURTH_QUADRANT)
                    .setType(mFxEditMode);
            if (keyFrameProcessor != null && tempAtTime >= 0) {
                /*移除临时添加的关键帧
                * Remove temporarily added keyframes
                * */
                keyFrameProcessor.removeKeyFrame(tempAtTime);
            }
            getView().onOperationBoxUpdate(mBoxInfo);
        }
    }


    /**
     * 当存在关键帧的时候去更新操作框
     * Update the operation box when a keyframe is present
     *
     * @param liveWindow the live window
     * @param timestamp  the timestamp 时间戳
     */
    public void updateOperationBoxWhenHadKeyFrame(NvsLiveWindowExt liveWindow, long timestamp, boolean needSeek) {
        if (mBoxInfo == null) {
            return;
        }
        mBoxInfo.setCornerPointList(getTransformPointList(liveWindow, timestamp, needSeek));
        getView().onOperationBoxUpdate(mBoxInfo);
    }

    public List<PointF> getTransformPointList(NvsLiveWindowExt liveWindow, long timestamp, boolean needSeek) {
        List<PointF> transformPointList = null;
        KeyFrameProcessor<?> keyFrameProcessor = null;
        long tempAtTime = -1;
        if (mEditFx instanceof MeicamCaptionClip || mEditFx instanceof MeicamStickerClip) {
            keyFrameProcessor = ((IKeyFrameProcessor<?>) mEditFx).keyFrameProcessor();
            if (keyFrameProcessor.getKeyFrameCount() > 0 && timestamp <= keyFrameProcessor.getOutPoint()) {
                long atTime = timestamp - keyFrameProcessor.getInPoint();
                if (atTime >= 0) {
                    MeicamKeyFrame keyFrame = keyFrameProcessor.getKeyFrame(atTime);
                    if (keyFrame == null) {
                        /*只要设置过关键帧，需要先设置关键帧才能正确获取边框位置，获取后再把没有必要的关键帧删除
                        * As long as a keyframe has been set, it is necessary to first set the keyframe
                        * to correctly obtain the border position, and then delete unnecessary keyframes
                        * after obtaining them.
                        * */
                        keyFrame = keyFrameProcessor.addKeyFrame(atTime);
                        tempAtTime = atTime;
                    }
                    if (keyFrame != null) {
                        keyFrame.bindOnlyKeyFrame();
                    }
                }
            }
        }
        if (mEditFx instanceof MeicamCaptionClip) {
            MeicamCaptionClip caption = (MeicamCaptionClip) mEditFx;
            transformPointList = updateCaptionCoordinates(caption, liveWindow, needSeek);
        } else if (mEditFx instanceof MeicamStickerClip) {
            MeicamStickerClip sticker = (MeicamStickerClip) mEditFx;
            transformPointList = updateStickerCoordinates(sticker, liveWindow, needSeek);
        } else if (mEditFx instanceof MeicamCompoundCaptionClip) {
            MeicamCompoundCaptionClip compoundCaptionClip = (MeicamCompoundCaptionClip) mEditFx;
            List<List<PointF>> childPointList = new ArrayList<>(compoundCaptionClip.getCaptionItemCount());
            transformPointList = updateCaptionCoordinates(compoundCaptionClip, childPointList, liveWindow, true);
        }
        if (keyFrameProcessor != null && tempAtTime >= 0) {
            /*移除临时添加的关键帧
            * Remove temporarily added keyframes
            * */
            keyFrameProcessor.removeKeyFrame(tempAtTime);
        }
        return transformPointList;
    }

    /**
     * 刷新视频边界
     * Refresh video bounding.
     *
     * @param liveWindow the live window
     */
    public void refreshVideoBounding(NvsLiveWindowExt liveWindow, long timestamp) {
        PipTransformInfo info = getPipTransformInfo(liveWindow, timestamp);
        if (info == null) {
            return;
        }
        getView().onPipOperationBoxUpdate(info);
    }

    @Nullable
    private PipTransformInfo getPipTransformInfo(NvsLiveWindowExt liveWindow, long timestamp) {
        if (meicamVideoClip == null || mPropertyVideoFx == null || liveWindow == null) {
            return null;
        }

        meicamVideoClip.disableAmbiguousCrop(true);
        if(meicamVideoClip.isPropertyVideoFxEnabled()){
            MeicamVideoFx propertyVideoFx = meicamVideoClip.findPropertyVideoFx();
            propertyVideoFx.setBooleanVal("Disable Ambiguous Crop",true);
        }

        if (transition != null) {
            long duration = (long) (transition.getDuration() / 2.0f);
            timestamp = timestamp + duration;
            if (timestamp > meicamVideoClip.getOutPoint()) {
                timestamp = meicamVideoClip.getOutPoint();
            }
        }
        float scaleX = 1.0f, scaleY = 1.0f, transX = 0.0f, transY = 0.0f, rotation = 0.0f;
        long atTime = timestamp - meicamVideoClip.getInPoint();
        KeyFrameProcessor<NvsVideoFx> frameProcessor = mPropertyVideoFx.keyFrameProcessor();
        if (frameProcessor.haveKeyFrame(NvsConstants.KEY_CROPPER_TRANS_X)) {
            if (atTime >= 0 && atTime <= (meicamVideoClip.getOutPoint() - meicamVideoClip.getInPoint())) {
                List<MeicamFxParam<?>> keyFrameParams = frameProcessor.getKeyFrameParams(NvsConstants.KEY_CROPPER_TRANS_X, atTime);
                if (keyFrameParams != null) {
                    for (MeicamFxParam<?> meicamFxParam : keyFrameParams) {
                        if (meicamFxParam == null) {
                            continue;
                        }
                        String key = meicamFxParam.getKey();
                        if (MeicamKeyFrame.SCALE_X.equals(key)) {
                            scaleX = meicamFxParam.getFloatValue();
                        } else if (MeicamKeyFrame.SCALE_Y.equals(key)) {
                            scaleY = meicamFxParam.getFloatValue();
                        } else if (MeicamKeyFrame.TRANS_X.equals(key)) {
                            transX = meicamFxParam.getFloatValue();
                        } else if (MeicamKeyFrame.TRANS_Y.equals(key)) {
                            transY = meicamFxParam.getFloatValue();
                        } else if (MeicamKeyFrame.ROTATION.equals(key)) {
                            rotation = meicamFxParam.getFloatValue();
                        }
                    }
                }
            } else {
                return null;
            }
        } else {
            scaleX = mPropertyVideoFx.getFloatVal(FX_TRANSFORM_2D_SCALE_X, 1F);
            scaleY = mPropertyVideoFx.getFloatVal(FX_TRANSFORM_2D_SCALE_Y, 1F);
            transX = mPropertyVideoFx.getFloatVal(FX_TRANSFORM_2D_TRANS_X, 0);
            transY = mPropertyVideoFx.getFloatVal(FX_TRANSFORM_2D_TRANS_Y, 0);
            rotation = mPropertyVideoFx.getFloatVal(FX_TRANSFORM_2D_ROTATION, 0);
        }

        float assetAspectRatio = meicamVideoClip.getOriginalWidth() * 1.0f / meicamVideoClip.getOriginalHeight();
        List<PointF> list = new ArrayList<>();
        EditorEngine editorEngine = EditorEngine.getInstance();
        NvsVideoResolution nvsVideoResolution = editorEngine.getVideoResolution();
        //timeline在liveWindow上的大小 The size of the timeline on liveWindow,
        PointF liveWindowSize = editorEngine.getViewSizeInLiveWindow(nvsVideoResolution.imageWidth, nvsVideoResolution.imageHeight);
        /// 视频 在视图上展示的宽高 The width and height of the video displayed in the view。
        PointF assetSize = editorEngine.getViewSizeInLiveWindow(assetAspectRatio);

        MeicamVideoFx cropperVideoFx = meicamVideoClip.getVideoFxByType(TYPE_RAW_BUILTIN, SUB_TYPE_CROPPER);
        if (cropperVideoFx == null) {
            cropperVideoFx = meicamVideoClip.getVideoFxByType(TYPE_RAW_BUILTIN, SUB_TYPE_CROPPER_EXT);
        }
        if (cropperVideoFx != null) {
            /// 剪切后的宽高 Width and height after cutting.
            float cropperAssetAspectRatio = cropperVideoFx.getFloatVal(NvsConstants.KEY_CROPPER_ASSET_ASPECT_RATIO);
            if (INVALID_VALUE != cropperAssetAspectRatio) {
                assetSize = editorEngine.getViewSizeInSize(liveWindowSize, cropperAssetAspectRatio);
            }
        }
        /// 视频 原中心点 Video original center point.
        PointF liveWindowCenter = new PointF(liveWindow.getWidth() / 2.0f, liveWindow.getHeight() / 2.0f);

        float transformRate = liveWindowSize.x * 1.0f / nvsVideoResolution.imageWidth;

        //视图移动向量 View Move Vector.
        PointF trans = new PointF(transX * transformRate, transY * transformRate);
        //移动后中心点 Center point after moving.
        PointF videoCenterAfterMoving = new PointF(liveWindowCenter.x + trans.x, liveWindowCenter.y - trans.y);
        // 缩放后视频 在视图上的宽高 The width and height of the video in the view after zooming.
        PointF scalingSize = new PointF(assetSize.x * scaleX, assetSize.y * scaleY);
        PointF leftTop = new PointF(videoCenterAfterMoving.x - scalingSize.x * 0.5f, videoCenterAfterMoving.y - scalingSize.y * 0.5f);
        PointF rightTop = new PointF(videoCenterAfterMoving.x + scalingSize.x * 0.5f, videoCenterAfterMoving.y - scalingSize.y * 0.5f);
        PointF rightBottom = new PointF(videoCenterAfterMoving.x + scalingSize.x * 0.5f, videoCenterAfterMoving.y + scalingSize.y * 0.5f);
        PointF leftBottom = new PointF(videoCenterAfterMoving.x - scalingSize.x * 0.5f, videoCenterAfterMoving.y + scalingSize.y * 0.5f);
        // 以移动后的中心,计算旋转后的四个点 Calculate the four points after rotation based on the center after movement.
        list.add(NvMaskHelper.transformData(leftTop, videoCenterAfterMoving, 1.0f, -rotation));
        list.add(NvMaskHelper.transformData(leftBottom, videoCenterAfterMoving, 1.0f, -rotation));
        list.add(NvMaskHelper.transformData(rightBottom, videoCenterAfterMoving, 1.0f, -rotation));
        list.add(NvMaskHelper.transformData(rightTop, videoCenterAfterMoving, 1.0f, -rotation));
        PipTransformInfo info = new PipTransformInfo();
        info.setCornerPointList(list);
        return info;
    }

    public int getEditMode() {
        return mFxEditMode;
    }

    public NvsObject<?> getEditFx() {
        return mEditFx;
    }

    /**
     * 检测操作框的可见性
     * Check the visibility of the action box
     *
     * @return true is visible ,false not
     */
    public boolean checkOperationBoxVisible() {
        boolean show = false;
        long currentTime = 0;
        if (mTimeline != null) {
            currentTime = mTimeline.getCurrentPosition();
        }
        if (mFxEditMode == EDIT_MODE_CAPTION) {
            if (mEditFx instanceof MeicamCaptionClip) {
                MeicamCaptionClip caption = (MeicamCaptionClip) mEditFx;
                show = (currentTime >= caption.getInPoint() && currentTime <= caption.getOutPoint());
            }
        } else if (mFxEditMode == EDIT_MODE_STICKER) {
            if (mEditFx instanceof MeicamStickerClip) {
                MeicamStickerClip sticker = (MeicamStickerClip) mEditFx;
                show = (currentTime >= sticker.getInPoint() && currentTime <= sticker.getOutPoint());
            }
        } else if (mFxEditMode == EDIT_MODE_COMPOUND_CAPTION) {
            if (mEditFx instanceof MeicamCompoundCaptionClip) {
                MeicamCompoundCaptionClip caption = (MeicamCompoundCaptionClip) mEditFx;
                show = (currentTime >= caption.getInPoint() && currentTime <= caption.getOutPoint());
            }
        } else if (mFxEditMode == EDIT_MODE_WATERMARK) {
            if (mTimeline == null) {
                return false;
            }
            MeicamWaterMark watermark = mTimeline.getMeicamWaterMark();
            show = !TextUtils.isEmpty(watermark.getWatermarkFilePath());
        } else if (mFxEditMode == EDIT_MODE_WATERMARK_EFFECT) {
            show = mBaseInfo != null;
        }
        return show;
    }


    /**
     * 检测画中画操作框的可见性
     * Check the visibility of the action box
     *
     * @return true is visible ,false not
     */
    public boolean checkVideoClipOperationBoxVisible() {
        boolean show = false;
        long currentTime = 0;
        if (mTimeline != null) {
            currentTime = mTimeline.getCurrentPosition();
        }
        if (meicamVideoClip != null && meicamVideoClip.getTrackIndex() != 0) {
            show = (currentTime >= meicamVideoClip.getInPoint() && currentTime <= meicamVideoClip.getOutPoint());
        }
        return show;
    }

    /**
     * 贴纸水平翻转
     * Horizontal flip sticker
     */
    public void horizontalFlipSticker() {
        if (mEditFx instanceof MeicamStickerClip) {
            MeicamStickerClip sticker = (MeicamStickerClip) this.mEditFx;
            sticker.setHorizontalFlip(!sticker.isHorizontalFlip());
            /*
             * 贴纸添加动画后，需要去掉NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_ANIMATED_STICKER_POSTER标记
             * After adding animation to the sticker,
             * you need to remove NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_ ANIMATED_STICKER_ POSTER tag.
             */
            //seekTimeline(NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_ANIMATED_STICKER_POSTER);
            seekTimeline(0);
            //return sticker.getHorizontalFlip();
        }
        // return false;
    }

    /**
     * 更改贴纸的音量
     * Change the sticker volume
     */
    public void changeStickerVolume() {
        if (mEditFx instanceof MeicamStickerClip) {
            MeicamStickerClip sticker = (MeicamStickerClip) mEditFx;
            if (sticker.getLeftVolume() != 0) {
                sticker.setLeftVolume(0);
                mBoxInfo.setThirdQuadrantIcon(R.mipmap.silence);
            } else {
                sticker.setLeftVolume(1);
                mBoxInfo.setThirdQuadrantIcon(R.mipmap.non_silence);
            }
            getView().onOperationBoxUpdate(false);
        }
    }


    private OperationBoxInfo mBoxInfo;

    /**
     * 处理编辑的特效的更新
     * Deal with edit fx update
     *
     * @param baseInfo   the baseInfo
     * @param liveWindow the liveWindow
     */
    public void updateEditFx(IBaseInfo baseInfo, NvsLiveWindowExt liveWindow) {
        List<PointF> transformPointList = null;
        String watermarkPath = null;
        mBaseInfo = baseInfo;
        if (mBoxInfo == null) {
            mBoxInfo = new OperationBoxInfo();
        }
        if (mFxEditMode == EDIT_MODE_WATERMARK) {
            if (mTimeline == null) {
                return;
            }
            MeicamWaterMark waterMark = mTimeline.getMeicamWaterMark();
            if (waterMark == null || TextUtils.isEmpty(waterMark.getWatermarkFilePath())) {
                return;
            }
            TimelineCommand.deleteWatermark(mTimeline, false);
            transformPointList = getWatermarkPointList(liveWindow, waterMark.getWatermarkFilePath());
            waterMark.setList(transformPointList);
            watermarkPath = waterMark.getWatermarkFilePath();
            addWatermark(liveWindow);
        } else if (mFxEditMode == EDIT_MODE_WATERMARK_EFFECT) {
            MeicamTimelineVideoFxClip videoFxClip = null;
            if (baseInfo.getType() == MOSAIC) {
                videoFxClip = applyMosaicFx(liveWindow);
            } else if (baseInfo.getType() == BLUR) {
                videoFxClip = applyBlurFx(liveWindow);
            } else if (baseInfo.getType() == NONE) {
                removeBlurOrMosaicFx();
            }
            if (videoFxClip != null) {
                transformPointList = videoFxClip.getPointList();
            }
            mEditFx = videoFxClip;
        }
        seekTimeline(0);
        mBoxInfo.setFirstQuadrantIcon(R.mipmap.delete)
                .setFourthQuadrantIcon(R.mipmap.scale)
                .setCornerPointList(transformPointList)
                .setScaleQuadrant(FOURTH_QUADRANT)
                .setType(mFxEditMode)
                .setInsideCover(watermarkPath);
        getView().onOperationBoxUpdate(mBoxInfo);
    }

    /**
     * Apply mosaic effect
     * <p>
     * 应用马赛克特效
     *
     * @param liveWindow the liveWindow
     */
    private MeicamTimelineVideoFxClip applyMosaicFx(NvsLiveWindowExt liveWindow) {
        if (mTimeline == null) {
            LogUtils.e("timeline is null");
            return null;
        }
        MeicamTimelineVideoFxClip videoFxClip = mTimeline.getTimelineFxFromClipList(0);
        boolean needUpdatePoints = false;
        if (videoFxClip != null) {
            if (!MOSAICNAME.equals(videoFxClip.getDesc())) {
                //模糊和马萨克只能有一个,起效果之前要先清除
                // There can only be one blur and one Massac, which must be cleared before it takes effect.
                TimelineCommand.removeTimelineFxFromClipList(mTimeline, videoFxClip);
                videoFxClip = TimelineCommand.addTimelineVideoFxInClipList(mTimeline, TYPE_BUILD_IN,
                        0, mTimeline.getDuration(), MOSAICNAME);
                needUpdatePoints = true;
            } else {
                videoFxClip.updatePointList(liveWindow);
            }
        } else {
            videoFxClip = TimelineCommand.addTimelineVideoFxInClipList(mTimeline, TYPE_BUILD_IN,
                    0, mTimeline.getDuration(), MOSAICNAME);
            needUpdatePoints = true;
        }
        if (videoFxClip != null) {
            TimelineFxCommand.setRegionalFeatherWidth(videoFxClip, 0F);
            float floatVal = videoFxClip.getFloatVal(KEY_UNIT_SIZE, 0.1f);
            TimelineFxCommand.setFloatVal(videoFxClip, KEY_UNIT_SIZE, floatVal);
            if (needUpdatePoints) {
                videoFxClip.updatePointList(getWatermarkPointList(liveWindow, null));
            }
            List<PointF> pointList = videoFxClip.getPointList();
            float[] fxRegion = getFxRegion(liveWindow, pointList);
            TimelineFxCommand.setObjectVal(videoFxClip, KEY_REGION, fxRegion);
            TimelineFxCommand.setRegionData(videoFxClip, fxRegion);
            TimelineFxCommand.setRegional(videoFxClip, true);
        }
        return videoFxClip;
    }

    /**
     * Apply blur effect
     * <p>
     * 应用模糊特效
     *
     * @param liveWindow the liveWindow
     */
    private MeicamTimelineVideoFxClip applyBlurFx(NvsLiveWindowExt liveWindow) {
        if (mTimeline == null) {
            LogUtils.e("timeline is null");
            return null;
        }
        //模糊和马萨克只能有一个,起效果之前要先清除
        // There can only be one blur and one Massac, which must be cleared before it takes effect.
        MeicamTimelineVideoFxClip videoFxClip = mTimeline.getTimelineFxFromClipList(0);
        boolean needUpdatePoints = false;
        if (videoFxClip != null) {
            if (!BLURNAME.equals(videoFxClip.getDesc())) {
                //模糊和马萨克只能有一个,起效果之前要先清除
                // There can only be one blur and one Massac, which must be cleared before it takes effect.
                TimelineCommand.removeTimelineFxFromClipList(mTimeline, videoFxClip);
                videoFxClip = TimelineCommand.addTimelineVideoFxInClipList(mTimeline, TYPE_BUILD_IN,
                        0, mTimeline.getDuration(), BLURNAME);
                needUpdatePoints = true;
            } else {
                videoFxClip.updatePointList(liveWindow);
            }
        } else {
            videoFxClip = TimelineCommand.addTimelineVideoFxInClipList(mTimeline, TYPE_BUILD_IN,
                    0, mTimeline.getDuration(), BLURNAME);
            needUpdatePoints = true;
        }
        if (videoFxClip != null) {
            TimelineFxCommand.setIntensity(videoFxClip, 1F);
            TimelineFxCommand.setRegionalFeatherWidth(videoFxClip, 0F);
            float radius = videoFxClip.getFloatVal(KEY_UNIT_RADIUS, 64.0f);
            TimelineFxCommand.setFloatVal(videoFxClip, KEY_UNIT_RADIUS, radius);
            if (needUpdatePoints) {
                videoFxClip.updatePointList(getWatermarkPointList(liveWindow, null));
            }
            float[] fxRegion = getFxRegion(liveWindow, videoFxClip.getPointList());
            TimelineFxCommand.setObjectVal(videoFxClip, KEY_REGION, fxRegion);
            TimelineFxCommand.setRegionData(videoFxClip, fxRegion);
            TimelineFxCommand.setRegional(videoFxClip, true);

        }
        return videoFxClip;
    }

    /**
     * Remove blur or mosaic effect
     * <p></>
     * 刪除模糊或马赛克效果
     */
    private void removeBlurOrMosaicFx() {
        if (mTimeline == null) {
            return;
        }
        MeicamTimelineVideoFxClip fxClip = mTimeline.getTimelineFxFromClipList(0);
        if (fxClip != null) {
            TimelineCommand.removeTimelineFxFromClipList(mTimeline, fxClip);
        }
    }

    /**
     * 获取特效的范围
     * Get fx region
     *
     * @param liveWindow    the liveWindow
     * @param viewPointList the view point list
     */
    private float[] getFxRegion(NvsLiveWindowExt liveWindow, List<PointF> viewPointList) {
        float[] floats = new float[8];
        //视图坐标转换成归一化坐标,View coordinates are converted to normalized coordinates
        if (viewPointList != null && viewPointList.size() == 4) {
            floats[0] = liveWindow.mapViewToNormalized(viewPointList.get(0)).x;
            floats[1] = liveWindow.mapViewToNormalized(viewPointList.get(0)).y;
            floats[2] = liveWindow.mapViewToNormalized(viewPointList.get(1)).x;
            floats[3] = liveWindow.mapViewToNormalized(viewPointList.get(1)).y;
            floats[4] = liveWindow.mapViewToNormalized(viewPointList.get(2)).x;
            floats[5] = liveWindow.mapViewToNormalized(viewPointList.get(2)).y;
            floats[6] = liveWindow.mapViewToNormalized(viewPointList.get(3)).x;
            floats[7] = liveWindow.mapViewToNormalized(viewPointList.get(3)).y;
        }
        return floats;
    }

    /**
     * 获取水印相关的点集合
     * Get the point list about watermark
     *
     * @param liveWindow    the liveWindow
     * @param watermarkPath the watermark path
     */
    private List<PointF> getWatermarkPointList(NvsLiveWindowExt liveWindow, String
            watermarkPath) {
        Point point = ImageConverter.getPicturePoint(watermarkPath, liveWindow.getContext());
        int defaultWidth = (int) liveWindow.getContext().getResources().getDimension(R.dimen.edit_waterMark_width);
        int defaultHeight = (int) liveWindow.getContext().getResources().getDimension(R.dimen.edit_waterMark_height);
        if (point != null) {
            defaultHeight = defaultWidth * point.y / point.x;
        }
        int left = liveWindow.getWidth() / 2 - defaultWidth / 2;
        int top = liveWindow.getHeight() / 2 - defaultHeight / 2;
        List<PointF> pointList = new ArrayList<>(4);
        pointList.add(new PointF(left, top));
        pointList.add(new PointF(left, top + defaultHeight));
        pointList.add(new PointF(left + defaultWidth, top + defaultHeight));
        pointList.add(new PointF(left + defaultWidth, top));
        return pointList;
    }

    /**
     * 更新编辑的特效的属性
     * Update the property of the edit fx
     */
    public void updateEditFxProperty(MeicamFxParam<?> fxParam) {
        if (mTimeline == null) {
            LogUtils.e("timeline == null");
            return;
        }
        if (mEditFx == null || !(mEditFx instanceof MeicamTimelineVideoFxClip)) {
            LogUtils.e("mEditFx == null");
            return;
        }
        if (fxParam == null) {
            LogUtils.e("fxParam == null");
            return;
        }
        Object paramValue = fxParam.getValue();
        if (!(paramValue instanceof Float)) {
            LogUtils.e("paramValue type is wrong!");
            return;
        }
        MeicamTimelineVideoFxClip videoFxClip = (MeicamTimelineVideoFxClip) mEditFx;
        if (String.valueOf(MOSAIC_DEGREE).equals(fxParam.getType())) {
            TimelineFxCommand.setIntensity(videoFxClip, (Float) paramValue);
        } else if (String.valueOf(MOSAIC_NUM).equals(fxParam.getType())) {
            TimelineFxCommand.setFloatVal(videoFxClip, KEY_UNIT_SIZE, (Float) paramValue);
        } else if (String.valueOf(BLUR).equals(fxParam.getType())) {
            TimelineFxCommand.setFloatVal(videoFxClip, KEY_UNIT_RADIUS, (Float) paramValue);
        }
        seekTimeline(0);
    }

    /**
     * 添加水印
     * Add watermark
     *
     * @param liveWindow the liveWindow
     */
    public void addWatermark(NvsLiveWindowExt liveWindow) {
        if (mTimeline == null) {
            LogUtils.e("timeline is null");
            return;
        }
        MeicamWaterMark meicamWaterMark = mTimeline.getMeicamWaterMark();
        List<PointF> pointFList = null;
        if (meicamWaterMark != null) {
            pointFList = meicamWaterMark.getList();
            TimelineCommand.deleteWatermark(mTimeline, false);
        }
        if (pointFList == null || pointFList.size() == 0) {
            return;
        }
        NvsVideoResolution resolution = mTimeline.getVideoResolution();
        PointF rightBottom = liveWindow.mapViewToCanonical(pointFList.get(2));
        PointF leftTop = liveWindow.mapViewToCanonical(pointFList.get(0));
        if (rightBottom != null && leftTop != null) {
            meicamWaterMark.setDisplayWidth((int) (Math.abs(rightBottom.x - leftTop.x)));
            meicamWaterMark.setDisplayHeight((int) (Math.abs(rightBottom.y - leftTop.y)));
            meicamWaterMark.setMarginX((int) (resolution.imageWidth / 2 + leftTop.x));
            meicamWaterMark.setMarginY((int) (resolution.imageHeight / 2 - leftTop.y));
        }
        TimelineCommand.addWatermark(mTimeline, meicamWaterMark.getWatermarkFilePath(),
                meicamWaterMark.getDisplayWidth(), meicamWaterMark.getDisplayHeight()
                , meicamWaterMark.getMarginX(), meicamWaterMark.getMarginY());
        seekTimeline(0);
    }

    private long mCheckKeyFrameAtTime = -1;
    private String mCheckKeyFrameKey;
    private IKeyFrameProcessor<?> mCheckKeyFrameClip;

    /**
     * 试图添加临时关键帧,如果在两个关键帧之间移动操作框，必须要设置关键帧才行，要不然无法移动。
     * Start check key frame
     *
     * @param isVideo true is video 视频片段,false not
     */
    public void tryToAddTempKeyFrame(boolean isVideo) {
        if (mTimeline == null) {
            return;
        }
        ClipInfo<?> clipInfo = null;
        if (isVideo) {
            if (meicamVideoClip != null) {
                clipInfo = meicamVideoClip;
            }
        } else {
            if (mEditFx instanceof ClipInfo) {
                clipInfo = (ClipInfo<?>) mEditFx;
            }
        }
        if (clipInfo == null) {
            return;
        }


        IKeyFrameProcessor<?> keyFrameHolder = null;
        String key = null;
        if (clipInfo instanceof MeicamVideoClip) {
            MeicamVideoFx propertyVideoFx = ((MeicamVideoClip) clipInfo).findPropertyVideoFx();
            if (propertyVideoFx != null) {
                keyFrameHolder = propertyVideoFx;
                key = NvsConstants.KEY_CROPPER_TRANS_X;
            }
        } else if (clipInfo instanceof MeicamStickerClip || clipInfo instanceof MeicamCaptionClip || clipInfo instanceof MeicamCompoundCaptionClip) {
            keyFrameHolder = ((IKeyFrameProcessor<?>) clipInfo);
        }
        if (keyFrameHolder == null) {
            return;
        }
        long atTime = mTimeline.getCurrentPosition() - clipInfo.getInPoint();
        KeyFrameProcessor<?> keyFrameProcessor = keyFrameHolder.keyFrameProcessor();
        mHaveKeyFrame = keyFrameProcessor.haveKeyFrame(key);
        if (mHaveKeyFrame) {
            if (keyFrameProcessor.getKeyFrame(atTime) == null) {
                if (mCheckKeyFrameClip != null) {
                    removeTempKeyFrame(true);
                }
                /*有关键帧，且当前时间点没有关键帧
                * There are keyframes and there are no keyframes at the current time point.
                * */
                List<MeicamFxParam<?>> clipParam = EditorEngine.getClipParam(keyFrameProcessor, atTime);
                MeicamKeyFrame keyFrame = KeyFrameHolderCommand.addKeyFrame(keyFrameHolder, clipParam, atTime, 0);
                if (keyFrame != null) {
                    if (transition != null) {
                        keyFrame.setOffsetTime((long) (transition.getDuration() / 2F));
                    } else {
                        keyFrame.setOffsetTime(0);
                    }
                    mCheckKeyFrameKey = key;
                    mCheckKeyFrameClip = keyFrameHolder;
                    mCheckKeyFrameAtTime = atTime;
                }
            }
            KeyFrameHolderCommand.preChangeKeyFrame(keyFrameHolder);
        }
    }

    /**
     * 试图添加临时蒙版关键帧,如果在两个关键帧之间移动操作框，必须要设置关键帧才行，要不然无法移动。
     * Start check key frame
     *
     * @param videoClip the video clip 视频片段
     */
    public void tryToAddTempMaskKeyFrame(MeicamVideoClip videoClip) {
        ClipInfo clipInfo = videoClip;
        if (clipInfo == null) {
            return;
        }
        long atTime = mTimeline.getCurrentPosition() - clipInfo.getInPoint();
        MeicamVideoFx maskTargetFx = EditorEngine.getInstance().getMaskTargetFx(videoClip);
        if (maskTargetFx == null) {
            return;
        }
        KeyFrameProcessor<NvsVideoFx> frameProcessor = maskTargetFx.keyFrameProcessor();
        if (frameProcessor.getKeyFrameCount(NvsConstants.KEY_PROPERTY_MASK_REGION_INFO) > 0 && frameProcessor.getKeyFrame(NvsConstants.KEY_PROPERTY_MASK_REGION_INFO, atTime) == null) {
            if (mCheckKeyFrameClip != null) {
                removeTempKeyFrame(true);
            }
            /*有关键帧，且当前时间点没有关键帧
            * There are keyframes and there are no keyframes at the current time point.
            * */
            List<MeicamFxParam<?>> clipParam = new ArrayList<>();
            Object objectVal = maskTargetFx.getObjectVal(NvsConstants.KEY_PROPERTY_MASK_REGION_INFO);
            String json = GsonUtils.toJson(objectVal);
            objectVal = GsonUtils.fromJson(json, MeicamMaskRegionInfo.class);
            clipParam.add(new MeicamFxParam<>(MeicamFxParam.TYPE_OBJECT, NvsConstants.KEY_PROPERTY_MASK_REGION_INFO, (MeicamMaskRegionInfo) objectVal));
            clipParam.add(new MeicamFxParam<>(MeicamFxParam.TYPE_FLOAT, NvsConstants.KEY_PROPERTY_MASK_FEATHER_WIDTH, maskTargetFx.getFloatVal(NvsConstants.KEY_PROPERTY_MASK_FEATHER_WIDTH, 0)));
            clipParam.add(new MeicamFxParam<>(MeicamFxParam.TYPE_BOOLEAN, NvsConstants.KEY_PROPERTY_MASK_INVERSE_REGION, maskTargetFx.getBooleanVal(NvsConstants.KEY_PROPERTY_MASK_INVERSE_REGION)));
            //clipParam.add(new MeicamFxParam<>(MeicamFxParam.TYPE_BOOLEAN, NvsConstants.KEY_MASK_KEEP_RGB, true));
            long offsetTime = 0;
                if (transition != null) {
                offsetTime = (long) (transition.getDuration() / 2F);
                }
            MeicamKeyFrame keyFrame = KeyFrameHolderCommand.addKeyFrame(maskTargetFx, clipParam, atTime, offsetTime);
            if (keyFrame != null) {
                mCheckKeyFrameClip = maskTargetFx;
                mCheckKeyFrameAtTime = atTime;
                mCheckKeyFrameKey = NvsConstants.KEY_PROPERTY_MASK_REGION_INFO;
            }
        }
    }

    /**
     * 获取临时关键帧
     * Get temp key frame
     */
    public MeicamKeyFrame getTempKeyFrame() {
        if (mCheckKeyFrameClip != null) {
            return mCheckKeyFrameClip.keyFrameProcessor().getKeyFrame(mCheckKeyFrameAtTime);
        }
        return null;
    }

    /**
     * 移除临时关键帧
     * Remove temp key frame
     */
    public void removeTempKeyFrame(boolean removeKeyFrame) {
        if (mCheckKeyFrameClip != null) {
            if (removeKeyFrame) {
                KeyFrameHolderCommand.removeKeyFrame(mCheckKeyFrameClip, KeyFrameProcessor.getRemoveParamKeys(mCheckKeyFrameKey), mCheckKeyFrameAtTime);
            } else {
                KeyFrameHolderCommand.cutKeyFrameCurve(mCheckKeyFrameClip, mCheckKeyFrameAtTime, mCheckKeyFrameKey);
            }
            mCheckKeyFrameKey = null;
            mCheckKeyFrameClip = null;
            mCheckKeyFrameAtTime = -1;
        }
        IKeyFrameProcessor<?> keyFrameHolder = null;
        if (mEditFx instanceof MeicamStickerClip || mEditFx instanceof MeicamCaptionClip || mEditFx instanceof MeicamCompoundCaptionClip) {
            keyFrameHolder = ((IKeyFrameProcessor<?>) mEditFx);
        }
        if (keyFrameHolder != null) {
            KeyFrameHolderCommand.postChangeKeyFrame(keyFrameHolder, null);
        }
    }

    /**
     * Change ratio add water to timeline.
     * 改变比例时添加水印
     *
     * @param liveWindow   the live window
     * @param beforeWidth  the before width
     * @param beforeHeight the before height
     * @param nowWidth     the now width
     * @param nowHeight    the now height
     */
    public void changeRatioAddWaterToTimeline(NvsLiveWindowExt liveWindow, int beforeWidth,
                                              int beforeHeight, int nowWidth, int nowHeight) {
        if (mTimeline == null) {
            return;
        }
        MeicamWaterMark meicamWaterMark = mTimeline.getMeicamWaterMark();
        TimelineCommand.deleteWatermark(mTimeline, false);
        List<PointF> pointFList = meicamWaterMark.getList();
        if (pointFList == null || pointFList.size() == 0) {
            return;
        }
        NvsVideoResolution resolution = mTimeline.getVideoResolution();
        PointF leftTopPoint = pointFList.get(0);
        PointF leftBottomPoint = pointFList.get(1);
        PointF rightBottomPoint = pointFList.get(2);
        PointF rightTopPoint = pointFList.get(3);
        float width = (rightTopPoint.x - leftTopPoint.x);
        float height = (leftBottomPoint.y - leftTopPoint.y);
        float ratioX = leftTopPoint.x / beforeWidth;
        float ratioY = leftTopPoint.y / beforeHeight;
        leftTopPoint.x = ratioX * nowWidth;
        leftTopPoint.y = ratioY * nowHeight;
        leftBottomPoint.x = leftTopPoint.x;
        leftBottomPoint.y = leftTopPoint.y + height;
        rightBottomPoint.x = leftTopPoint.x + width;
        rightBottomPoint.y = leftBottomPoint.y;
        rightTopPoint.x = rightBottomPoint.x;
        rightTopPoint.y = leftTopPoint.y;
        PointF rightBottom = liveWindow.mapViewToCanonical(pointFList.get(2));
        PointF leftTop = liveWindow.mapViewToCanonical(pointFList.get(0));
        if (rightBottom != null && leftTop != null) {
            meicamWaterMark.setDisplayWidth((int) (Math.abs(rightBottom.x - leftTop.x)));
            meicamWaterMark.setDisplayHeight((int) (Math.abs(rightBottom.y - leftTop.y)));
            meicamWaterMark.setMarginX((int) (resolution.imageWidth / 2 + leftTop.x));
            meicamWaterMark.setMarginY((int) (resolution.imageHeight / 2 - leftTop.y));
        }
        TimelineCommand.addWatermark(mTimeline, meicamWaterMark.getWatermarkFilePath(),
                meicamWaterMark.getDisplayWidth(), meicamWaterMark.getDisplayHeight()
                , meicamWaterMark.getMarginX(), meicamWaterMark.getMarginY());
        seekTimeline(0);
    }

    /**
     * 处理特效的拖动
     * Handle drag of effects
     *
     * @param prePoint   the previous point
     * @param nowPoint   the now point
     * @param oldList    the old point list
     * @param liveWindow the NvsLiveWindowExt
     */
    public void dealFxDrag(PointF prePoint, PointF
            nowPoint, List<PointF> oldList, NvsLiveWindowExt liveWindow, PointF centerPointF) {
        List<PointF> transformPointList = null;
        List<List<PointF>> childPointList = null;
        PointF assetAnchor = liveWindow.mapViewToCanonical(centerPointF);
        PointF pre = liveWindow.mapViewToCanonical(prePoint);
        PointF now = liveWindow.mapViewToCanonical(nowPoint);
        PointF timelinePoint = new PointF(now.x - pre.x, now.y - pre.y);
        if (mFxEditMode == EDIT_MODE_CAPTION) {
            // 移动字幕 Caption
            if (mEditFx instanceof MeicamCaptionClip) {
                MeicamCaptionClip caption = (MeicamCaptionClip) mEditFx;
                CaptionCommand.translateCaption(caption, timelinePoint.x, timelinePoint.y, !mHaveKeyFrame);
                transformPointList = getTransformPointList(liveWindow, mTimeline.getCurrentPosition(), true);
            }
        } else if (mFxEditMode == EDIT_MODE_STICKER) {
            // 移动贴纸 Sticker
            if (mEditFx instanceof MeicamStickerClip) {
                MeicamStickerClip sticker = (MeicamStickerClip) mEditFx;
                StickerCommand.translateAnimatedSticker(sticker, timelinePoint.x, timelinePoint.y, !mHaveKeyFrame);
                transformPointList = getTransformPointList(liveWindow, mTimeline.getCurrentPosition(), true);
            }
        } else if (mFxEditMode == EDIT_MODE_COMPOUND_CAPTION) {
            //移动组合字幕 Compound caption
            if (mEditFx instanceof MeicamCompoundCaptionClip) {
                MeicamCompoundCaptionClip caption = (MeicamCompoundCaptionClip) mEditFx;
                CompCaptionCommand.translateCaption(caption, new FloatPoint(timelinePoint.x, timelinePoint.y), new FloatPoint(assetAnchor.x, assetAnchor.y));
                childPointList = new ArrayList<>(caption.getCaptionItemCount());
                transformPointList = updateCaptionCoordinates(caption, childPointList, liveWindow, true);
            }
        } else if (mFxEditMode == EDIT_MODE_WATERMARK || mFxEditMode == EDIT_MODE_WATERMARK_EFFECT) {
            if (!checkInLiveWindow(oldList, liveWindow)) {
                return;
            }
            timelinePoint = new PointF(nowPoint.x - prePoint.x, nowPoint.y - prePoint.y);
            transformPointList = new ArrayList<>(oldList.size());
            for (PointF pointF : oldList) {
                transformPointList.add(new PointF(pointF.x + timelinePoint.x, pointF.y + timelinePoint.y));
            }
            if (checkInLiveWindow(transformPointList, liveWindow)) {
                //更新 Update
                if (mFxEditMode == EDIT_MODE_WATERMARK) {
                    if (mTimeline != null) {
                        MeicamWaterMark meicamWaterMark = mTimeline.getMeicamWaterMark();
                        if (meicamWaterMark == null || TextUtils.isEmpty(meicamWaterMark.getWatermarkFilePath())) {
                            return;
                        }
                        mTimeline.deleteWatermark(false);
                        meicamWaterMark.setList(transformPointList);
                    }
                } else if (mFxEditMode == EDIT_MODE_WATERMARK_EFFECT) {
                    updateWatermarkFxRegion(getFxRegion(liveWindow, transformPointList), transformPointList);
                }
                seekTimeline(0);
            } else {
                return;
            }
        }
        mBoxInfo.setCornerPointList(transformPointList)
                .setInsidePointList(childPointList);
        getView().onOperationBoxUpdate(mBoxInfo);
    }

    /**
     * 更新水印特效的范围
     * Update watermark effect region
     */
    private void updateWatermarkFxRegion(float[] fxRegion, List<PointF> pointList) {
        if (mTimeline == null) {
            LogUtils.e(" timeline is null");
            return;
        }
        if (mEditFx == null) {
            LogUtils.e(" mEditFx == null");
            return;
        }
        if (mEditFx instanceof MeicamTimelineVideoFxClip) {
            TimelineFxCommand.setRegionData((MeicamTimelineVideoFxClip) mEditFx,fxRegion);
        }

        MeicamTimelineVideoFxClip videoFxClip = mTimeline.getTimelineFxFromClipList(0);
        if (videoFxClip != null) {
            videoFxClip.updatePointList(pointList);
            TimelineFxCommand.setObjectVal(videoFxClip,KEY_REGION,fxRegion);
        }
    }

    /**
     * 更新字幕坐标
     * Update caption coordinates
     *
     * @param caption    the caption
     * @param liveWindow the liveWindow
     */
    private List<PointF> updateCaptionCoordinates(MeicamCaptionClip caption, NvsLiveWindowExt
            liveWindow, boolean needSeek) {
        List<PointF> transformPointList = caption.getBoundingRectangleVertices();
        if (transformPointList == null || transformPointList.size() < 4) {
            return null;
        }
        transformPointList = transformCoordinates(transformPointList, liveWindow);
        if (needSeek) {
            seekTimeline(0);
        }
        return transformPointList;
    }

    /**
     * 更新贴纸坐标
     * Update sticker coordinates
     *
     * @param sticker    the sticker
     * @param liveWindow the liveWindow
     */
    private List<PointF> updateStickerCoordinates(MeicamStickerClip sticker, NvsLiveWindowExt
            liveWindow, boolean needSeek) {
        List<PointF> transformPointList = sticker.getBoundingRectangleVertices();
        if (transformPointList == null || transformPointList.size() < 4) {
            return null;
        }

        if (sticker.isHorizontalFlip()) {
            //如果已水平翻转，需要对顶点数据进行处理
            // If it has been flipped horizontally, it is necessary to process the vertex data.
            Collections.swap(transformPointList, 0, 3);
            Collections.swap(transformPointList, 1, 2);
        }
        transformPointList = transformCoordinates(transformPointList, liveWindow);
        if (needSeek) {
            /*
             * 贴纸添加动画后，需要去掉NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_ANIMATED_STICKER_POSTER标记
             * After adding animation to the sticker,
             * you need to remove NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_ ANIMATED_STICKER_ POSTER tag.
             */
            //seekTimeline(NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_ANIMATED_STICKER_POSTER);
            seekTimeline(0);
        }
        return transformPointList;
    }

    /**
     * 更新组合字幕坐标
     * Update compound caption coordinates
     *
     * @param caption        the caption
     * @param childPointList the caption child point list
     * @param liveWindow     the liveWindow
     */
    private List<PointF> updateCaptionCoordinates(MeicamCompoundCaptionClip
                                                          caption, List<List<PointF>> childPointList,
                                                  NvsLiveWindowExt liveWindow, boolean needSeek) {
        List<PointF> transformPointList = caption.getCompoundBoundingVertices(NvsTimelineCompoundCaption.BOUNDING_TYPE_FRAME);
        if (transformPointList == null || transformPointList.size() < 4) {
            return null;
        }
        transformPointList = transformCoordinates(transformPointList, liveWindow);
        for (int index = 0; index < caption.getCaptionItemCount(); index++) {
            List<PointF> subList = caption.getCaptionBoundingVertices(index, NvsTimelineCompoundCaption.BOUNDING_TYPE_TEXT);
            if (subList == null || subList.size() < 4) {
                continue;
            }
            childPointList.add(transformCoordinates(subList, liveWindow));
        }
        if (needSeek) {
            seekTimeline(0);
        }
        return transformPointList;
    }

    /**
     * 转换坐标(时间线坐标转换成视图坐标)
     * Transform coordinates,Timeline coordinates are converted to view coordinates
     *
     * @param originalPointList the timeline point list
     * @param liveWindow        the liveWindow
     */
    public List<PointF> transformCoordinates
    (List<PointF> originalPointList, NvsLiveWindowExt liveWindow) {
        List<PointF> newList = new ArrayList<>(originalPointList.size());
        for (int i = 0; i < originalPointList.size(); i++) {
            PointF pointF = liveWindow.mapCanonicalToView(originalPointList.get(i));
            newList.add(pointF);
        }
        return newList;
    }

    /**
     * 检查坐标点是否在LiveWindow范围内
     * Check if the coordinate point is within liveWindow range
     *
     * @param newList    the point list
     * @param liveWindow the liveWindow
     * @return true is ,false not
     */
    private boolean checkInLiveWindow(List<PointF> newList, NvsLiveWindowExt liveWindow) {
        if (newList == null) {
            return false;
        }
        if (mRealLiveWindrowSize == null) {
            mRealLiveWindrowSize = EditorEngine.getInstance().getLiveWindrowSize(liveWindow);
        }
        int width = liveWindow.getWidth();
        int height = liveWindow.getHeight();
        float rectLeft = (width - mRealLiveWindrowSize.x) / 2F;
        float rectTop = (height - mRealLiveWindrowSize.y) / 2F;
        float rectRight = rectLeft + mRealLiveWindrowSize.x;
        float rectBottom = rectTop + mRealLiveWindrowSize.y;
        for (PointF pointF : newList) {
            if (pointF.x < rectLeft || pointF.x > rectRight || pointF.y < rectTop || pointF.y > rectBottom) {
                return false;
            }
        }
        return true;
    }

    /**
     * 处理特效的拖动
     * Handle drag of effects
     *
     * @param scaleFactor the scale factor
     * @param anchor      the now point
     * @param angle       the now angle ,Note that an Angle of Float.MAX_VALUE is invalid.注意角度为Float.MAX_VALUE的时候是无效值。
     * @param oldList     the old point list
     * @param liveWindow  the NvsLiveWindowExt
     */
    public void dealFxScaleAndRotate(float scaleFactor, PointF anchor, float angle,
                                     List<PointF> oldList, NvsLiveWindowExt liveWindow) {
        List<PointF> transformPointList = null;
        List<List<PointF>> childPointList = null;
        PointF assetAnchor = liveWindow.mapViewToCanonical(anchor);
        if (mFxEditMode == Constants.EDIT_MODE_CAPTION) {
            if (mEditFx instanceof MeicamCaptionClip) {
                MeicamCaptionClip caption = (MeicamCaptionClip) mEditFx;
                // 放缩字幕 Scale caption
                CaptionCommand.scaleCaption(caption, scaleFactor, assetAnchor.x, assetAnchor.y, !mHaveKeyFrame);
                // 旋转字幕 Rotate caption
                if (angle != Float.MAX_VALUE) {
                    CaptionCommand.rotateCaption(caption, angle, !mHaveKeyFrame);
                }
                transformPointList = getTransformPointList(liveWindow, mTimeline.getCurrentPosition(), true);
            }
        } else if (mFxEditMode == EDIT_MODE_STICKER) {
            // 放缩贴纸 Scale sticker
            if (mEditFx instanceof MeicamStickerClip) {
                MeicamStickerClip sticker = (MeicamStickerClip) mEditFx;
                //缩放贴纸  Scale sticker
                StickerCommand.scaleAnimatedSticker(sticker, scaleFactor, assetAnchor.x, assetAnchor.y, !mHaveKeyFrame);
                //旋转贴纸 Rotate sticker
                if (angle != Float.MAX_VALUE) {
                    StickerCommand.rotateAnimatedSticker(sticker, angle, !mHaveKeyFrame);
                }
                transformPointList = getTransformPointList(liveWindow, mTimeline.getCurrentPosition(), true);
                //EditorEngine.getInstance().seekTimeline();
            }
        } else if (mFxEditMode == EDIT_MODE_COMPOUND_CAPTION) {
            if (mEditFx instanceof MeicamCompoundCaptionClip) {
                MeicamCompoundCaptionClip caption = (MeicamCompoundCaptionClip) mEditFx;
                // 旋转字幕  Rotate caption
                FloatPoint floatAnchor = new FloatPoint(assetAnchor.x, assetAnchor.y);
                CompCaptionCommand.scaleCaption(caption, scaleFactor, floatAnchor);
                if (angle != Float.MAX_VALUE) {
                    CompCaptionCommand.rotateCaption(caption, angle, floatAnchor);
                }
    /*            float scaleX = caption.getScaleX();
                float scaleY = caption.getScaleY();
                if (scaleX <= DEFAULT_SCALE_VALUE && scaleY <= DEFAULT_SCALE_VALUE) {
                    caption.setScaleX(DEFAULT_SCALE_VALUE);
                    caption.setScaleY(DEFAULT_SCALE_VALUE);
                }*/
                childPointList = new ArrayList<>(caption.getCaptionItemCount());
                transformPointList = updateCaptionCoordinates(caption, childPointList, liveWindow, true);
            }
        } else if (mFxEditMode == EDIT_MODE_WATERMARK) {
            //水印 Water mark
            if (mTimeline == null || oldList == null) {
                return;
            }
            MeicamWaterMark meicamWaterMark = mTimeline.getMeicamWaterMark();
            if (TextUtils.isEmpty(meicamWaterMark.getWatermarkFilePath())) {
                return;
            }
            transformPointList = new ArrayList<>(oldList.size());
            float width = Math.abs(oldList.get(0).x - oldList.get(3).x);
            float height = Math.abs(oldList.get(0).y - oldList.get(1).y);
            float x0 = anchor.x - width / 2 * scaleFactor;
            float x1 = anchor.x + width / 2 * scaleFactor;
            float y0 = anchor.y - height / 2 * scaleFactor;
            float y1 = anchor.y + height / 2 * scaleFactor;
            transformPointList.add(new PointF(x0, y0));
            transformPointList.add(new PointF(x0, y1));
            transformPointList.add(new PointF(x1, y1));
            transformPointList.add(new PointF(x1, y0));
            if (checkInLiveWindow(transformPointList, liveWindow)) {
                TimelineCommand.deleteWatermark(mTimeline, false);
                seekTimeline(0);
                //mapWaterMarkPointToWH(liveWindow);
                meicamWaterMark.setList(transformPointList);
            } else {
                return;
            }
        } else {
            return;
        }
        mBoxInfo.setCornerPointList(transformPointList)
                .setInsidePointList(childPointList);
        getView().onOperationBoxUpdate(false);
    }

    /**
     * 处理特效的缩放
     * Handle drag of effects
     *
     * @param xScaleFactor the x scale factor
     * @param yScaleFactor the y scale factor
     * @param pointF       the now pointF
     * @param oldList      the old point list
     * @param liveWindow   the NvsLiveWindowExt
     */
    public void dealFxScale(float xScaleFactor, float yScaleFactor, PointF
            pointF, List<PointF> oldList, NvsLiveWindowExt liveWindow) {
        if (oldList != null && mFxEditMode == EDIT_MODE_WATERMARK_EFFECT) {
            float width = Math.abs(oldList.get(0).x - oldList.get(3).x);
            float height = Math.abs(oldList.get(0).y - oldList.get(1).y);
            float x0 = pointF.x - width / 2 * xScaleFactor;
            float x1 = pointF.x + width / 2 * xScaleFactor;
            float y0 = pointF.y - height / 2 * yScaleFactor;
            float y1 = pointF.y + height / 2 * yScaleFactor;
            List<PointF> newList = new ArrayList<>();
            newList.add(new PointF(x0, y0));
            newList.add(new PointF(x0, y1));
            newList.add(new PointF(x1, y1));
            newList.add(new PointF(x1, y0));
            if (checkInLiveWindow(newList, liveWindow)) {
                updateWatermarkFxRegion(getFxRegion(liveWindow, newList), newList);
                seekTimeline(0);
                mBoxInfo.setCornerPointList(newList);
                getView().onOperationBoxUpdate(false);
            }
        }
    }

    /**
     * 设置字幕文本对齐
     * Set caption text alignment
     *
     * @param liveWindow the liveWindow
     */
    public void setTextAlignment(NvsLiveWindowExt liveWindow) {
        if (mFxEditMode == Constants.EDIT_MODE_CAPTION && mEditFx instanceof MeicamCaptionClip) {
            MeicamCaptionClip caption = (MeicamCaptionClip) mEditFx;
            int textAlignment = caption.getTextAlignment();
            if (textAlignment == NvsTimelineCaption.TEXT_ALIGNMENT_LEFT) {
                //居中对齐 center aligned
                CaptionCommand.setParam(caption, CaptionCommand.PARAM_TEXT_ALIGNMENT, NvsTimelineCaption.TEXT_ALIGNMENT_CENTER);
                mBoxInfo.setSecondQuadrantIcon(R.mipmap.center_align);
            } else if (textAlignment == NvsTimelineCaption.TEXT_ALIGNMENT_CENTER) {
                //居右对齐 right aligned
                CaptionCommand.setParam(caption, CaptionCommand.PARAM_TEXT_ALIGNMENT, NvsTimelineCaption.TEXT_ALIGNMENT_RIGHT);
                mBoxInfo.setSecondQuadrantIcon(R.mipmap.right_align);
            } else if (textAlignment == NvsTimelineCaption.TEXT_ALIGNMENT_RIGHT) {
                //左对齐 left aligned
                CaptionCommand.setParam(caption, CaptionCommand.PARAM_TEXT_ALIGNMENT, NvsTimelineCaption.TEXT_ALIGNMENT_LEFT);
                mBoxInfo.setSecondQuadrantIcon(R.mipmap.left_align);
            }
            seekTimeline(0);
            mBoxInfo.setCornerPointList(getTransformPointList(liveWindow, mTimeline.getCurrentPosition(), true));
            getView().onOperationBoxUpdate(false);
        }
    }

    /**
     * 处理特效的删除
     * Handle drag of effects
     */
    public void dealFxDelete() {
        int seekShowMode = 0;
        if (mFxEditMode == EDIT_MODE_CAPTION || mFxEditMode == EDIT_MODE_COMPOUND_CAPTION) {
            //seekShowMode = NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER;
            closeFxEditMode();
        } else if (mFxEditMode == EDIT_MODE_STICKER) {
            /*
             * 贴纸添加动画后，需要去掉NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_ANIMATED_STICKER_POSTER标记
             * After adding animation to the sticker,
             * you need to remove NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_ ANIMATED_STICKER_ POSTER tag.
             */
            //seekShowMode = NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_ANIMATED_STICKER_POSTER;
            seekShowMode = 0;
            closeFxEditMode();
        } else if (mFxEditMode == EDIT_MODE_WATERMARK) {
            mTimeline.deleteWatermark(true);
            clearFxInfo();
        } else if (mFxEditMode == EDIT_MODE_WATERMARK_EFFECT) {
            mTimeline.removeTimelineFxFromClipList(0);
            clearFxInfo();
        }
        seekTimeline(seekShowMode);
        getView().onOperationBoxUpdate(true);
    }

    /**
     * 恢复水印点列表
     * Restore water list.
     */
    public void restoreWaterList(NvsLiveWindowExt liveWindowExt) {
        if (mTimeline == null) {
            LogUtils.e("timeline is null");
        }
        MeicamWaterMark meicamWaterMark = mTimeline.getMeicamWaterMark();
        if (!hasChangeWaterList) {
            hasChangeWaterList = true;
            NvsVideoResolution videoResolution = mTimeline.getVideoResolution();
            if (videoResolution != null) {
                meicamWaterMark.setPointList(liveWindowExt, videoResolution.imageWidth, videoResolution.imageHeight);
            }
        }
    }

    /**
     * refresh Mask
     * 刷新蒙版数据
     *
     * @param meicamVideoClip
     */
    public void refreshMask(MeicamVideoClip meicamVideoClip) {
        if (meicamVideoClip != null) {
            MeicamVideoFx videoFx = meicamVideoClip.getVideoFx(SUB_TYPE_MASK, NvsConstants.KEY_MASK_GENERATOR);
            if (videoFx != null) {
                meicamVideoClip.removeVideoFx(videoFx);
            }
            MeicamVideoFx targetVideoFx = VideoClipCommand.findPropertyVideoFx(meicamVideoClip);

            KeyFrameProcessor<NvsVideoFx> keyFrameProcessor = targetVideoFx.keyFrameProcessor();
            MeicamKeyFrame keyFrame = null;
            long atTime = mTimeline.getCurrentPosition() - meicamVideoClip.getInPoint();
            if (keyFrameProcessor.haveKeyFrame(NvsConstants.KEY_PROPERTY_MASK_FEATHER_WIDTH)) {
                keyFrame = keyFrameProcessor.getKeyFrame(NvsConstants.KEY_PROPERTY_MASK_FEATHER_WIDTH, atTime);
            }
            if (keyFrame != null) {
                if (transition != null) {
                    KeyFrameCommand.setOffsetTime(keyFrame, (long) (transition.getDuration() / 2F));
                }
                KeyFrameCommand.setFloatVal(keyFrame, NvsConstants.KEY_PROPERTY_MASK_FEATHER_WIDTH, meicamVideoClip.maskModel.feather);
                KeyFrameCommand.setArbDataVal(keyFrame, NvsConstants.KEY_PROPERTY_MASK_REGION_INFO, meicamVideoClip.maskModel.regionInfo);
                KeyFrameCommand.setBooleanVal(keyFrame, NvsConstants.KEY_PROPERTY_MASK_INVERSE_REGION, meicamVideoClip.maskModel.inverseRegion);
                targetVideoFx.setStringVal(NvsConstants.KEY_PROPERTY_MASK_COORDINATE_SYSTEM, "ndc");
            } else {
                VideoFxCommand.setObjectVal(targetVideoFx, NvsConstants.KEY_PROPERTY_MASK_REGION_INFO, meicamVideoClip.maskModel.regionInfo);
                VideoFxCommand.setBooleanVal(targetVideoFx, NvsConstants.KEY_PROPERTY_MASK_INVERSE_REGION, meicamVideoClip.maskModel.inverseRegion);
                VideoFxCommand.setFloatVal(targetVideoFx, NvsConstants.KEY_PROPERTY_MASK_FEATHER_WIDTH, meicamVideoClip.maskModel.feather);
                VideoFxCommand.setStringVal(targetVideoFx, NvsConstants.KEY_PROPERTY_MASK_COORDINATE_SYSTEM, "ndc");
            }
            EditorEngine.getInstance().seekTimeline();

            NvMaskModel maskModel = meicamVideoClip.maskModel;
            if (maskModel.assetsHeight == 0) {
                maskModel.assetsHeight = meicamVideoClip.getRectHeight();
                maskModel.assetsWidth = meicamVideoClip.getRectWidth();
            }
        }
    }

    public List<PointF> getVideoRect(NvsLiveWindowExt liveWindowExt) {
        PipTransformInfo pipTransformInfo = getPipTransformInfo(liveWindowExt, mTimeline.getCurrentPosition());
        if (pipTransformInfo != null) {
            return pipTransformInfo.getCornerPointList();
        }
        return null;
    }

    /**
     * Get bitmap without master keyer bitmap.
     * 获取当前预览的bitmap，不含Master keyer特技
     * @return the bitmap
     */
    public Bitmap getBitmapWithoutMasterKeyer(){
        if (meicamVideoClip == null) {
            return null;
        }

        EditorEngine editorEngine = EditorEngine.getInstance();
        MeicamTimeline timelineExt = editorEngine.createSingleClipTimelineExt(meicamVideoClip.getFilePath(), meicamVideoClip.getTrimIn(), meicamVideoClip.getTrimOut(), meicamVideoClip.getSpeed());
        if (timelineExt != null) {
            Bitmap bitmap = timelineExt.grabImageFromTimeline(NvsStreamingContext.getInstance(), editorEngine.getCurrentTimelinePosition() - meicamVideoClip.getInPoint(), new NvsRational(1, 2));
            timelineExt.release();
            return bitmap;
        }
        return null;
    }

    public void setEditMode(int editMode) {
        mEditMode = editMode;
    }

    public FloatPoint getColorPosition() {
        if (meicamVideoClip == null) {
            return null;
        }
        MeicamVideoFx videoFx = meicamVideoClip.getVideoFxById(NvsConstants.MasterKeyer.NAME);
        if (videoFx != null) {
            Object objectVal = videoFx.getObjectVal(NvsConstants.MasterKeyer.KEY_COLOR_POSITION);
            if (objectVal instanceof FloatPoint) {
                return (FloatPoint) objectVal;
            }
        }
        return null;
    }


    public int getColor() {
        if (meicamVideoClip == null) {
            return -1;
        }
        MeicamVideoFx videoFx = meicamVideoClip.getVideoFxById(NvsConstants.MasterKeyer.NAME);
        if (videoFx != null) {
            String color = videoFx.getColor(NvsConstants.MasterKeyer.KEY_COLOR);
            NvsColor nvsColor = ColorUtil.colorToNvsColor(color);
            if (nvsColor != null) {
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                    return Color.argb(nvsColor.a, nvsColor.r, nvsColor.g, nvsColor.b);
                } else {
                    return Color.argb((int)nvsColor.a, (int)nvsColor.r, (int)nvsColor.g, (int)nvsColor.b);
                }
            }
        }
        return -1;
    }
}
