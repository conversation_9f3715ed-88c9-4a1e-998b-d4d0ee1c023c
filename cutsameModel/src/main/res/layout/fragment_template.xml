<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_no_date"
        android:drawableTop="@mipmap/fragment_mine_no_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawablePadding="@dimen/dp_px_51"
        android:layout_gravity="center"
        android:gravity="center"
        android:text="@string/activity_cut_export_no_template_data"
        android:textColor="@color/white_d1d"
        android:visibility="gone"
        android:textSize="@dimen/sp_px_33" />

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/srl_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!--多加一层布局是因为要动态调整margin，SwipeRefreshLayout无法动态调整-->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_horizontal" />
        </FrameLayout>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <include layout="@layout/layout_no_net" />
</FrameLayout>
