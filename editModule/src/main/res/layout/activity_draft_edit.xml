<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_ff242424"
    tools:context=".activity.DraftEditActivity">

    <com.meishe.myvideo.view.MYEditorParentLayout
        android:id="@+id/editor_parent_view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_666"
        android:background="@color/timeline_bg_color"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@id/edit_navigation_bar"
        tools:ignore="MissingConstraints">

        <com.meishe.myvideo.view.MYEditorTimeLine
            android:id="@+id/edit_timeline"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <com.meishe.myvideo.view.MYEditorTimelineTrackView
            android:id="@+id/editor_track_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/edit_timeline"
            android:minHeight="@dimen/dp_px_580" />
        <View
            android:id="@+id/warning_line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_px_6"
            android:layout_alignParentBottom="true"
            android:layout_marginTop="@dimen/dp_px_60"
            android:background="@color/color_ffff365E"
            android:visibility="gone"/>
        <View
            android:layout_width="@dimen/dp_px_3"
            android:layout_height="match_parent"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dp_px_60"
            android:background="@color/white" />
    </com.meishe.myvideo.view.MYEditorParentLayout>


    <com.meishe.base.view.NavigationBar
        android:id="@+id/edit_navigation_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_210"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/edit_operation_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <RelativeLayout
            android:id="@+id/edit_preview_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
        <!--智能抠像-->
        <FrameLayout
            android:id="@+id/fl_smart_keyer"
            android:layout_width="@dimen/dp_px_270"
            android:layout_height="@dimen/dp_px_78"
            android:layout_gravity="center_horizontal|bottom"
            android:background="@color/black"
            android:visibility="gone">

            <TextView
                android:id="@+id/tv_smart_keyer_hint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/dp_px_30"
                android:gravity="center"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_px_30" />

            <ImageView
                android:id="@+id/iv_cancel_smart_keyer"
                android:layout_width="@dimen/dp_px_45"
                android:layout_height="@dimen/dp_px_45"
                android:layout_gravity="center_vertical|end"
                android:layout_marginEnd="@dimen/dp_px_20"
                android:contentDescription="@null"
                android:src="@mipmap/ic_draft_back" />
        </FrameLayout>
    </FrameLayout>

    <LinearLayout
        android:id="@+id/top_operation_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_100"
        android:layout_marginTop="@dimen/dp_px_96"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_back_pressed"
            android:layout_width="@dimen/dp_px_95"
            android:layout_height="@dimen/dp_px_95"
            android:layout_marginStart="@dimen/dp_px_60"
            android:contentDescription="@null"
            android:padding="@dimen/dp_px_10"
            android:src="@mipmap/ic_draft_back" />

        <ImageView
            android:id="@+id/iv_login"
            android:layout_width="@dimen/dp_px_99"
            android:layout_height="@dimen/dp_px_99"
            android:layout_marginStart="@dimen/dp_px_30"
            android:contentDescription="@null"
            android:src="@drawable/selector_icon_user_login" />

        <View
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tv_export_template"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_px_66"
            android:background="@drawable/bg_rectangle_round_gray_4a90e2_d33"
            android:gravity="center"
            android:minWidth="@dimen/dp_px_165"
            android:paddingLeft="@dimen/dp_px_15"
            android:paddingRight="@dimen/dp_px_15"
            android:text="@string/export_template"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_px_33" />

        <TextView
            android:id="@+id/tv_export_video"
            android:layout_width="@dimen/dp_px_130"
            android:layout_height="@dimen/dp_px_66"
            android:layout_marginLeft="@dimen/dp_px_39"
            android:layout_marginRight="@dimen/dp_px_39"
            android:background="@drawable/bg_rectangle_round_gray_fc2b55_d33"
            android:gravity="center"
            android:padding="@dimen/dp_px_2"
            android:text="@string/export_content"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_px_33" />
    </LinearLayout>

    <com.meishe.myvideo.view.MYMiddleOperationView
        android:id="@+id/edit_operation_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dp_px_22"
        android:paddingBottom="@dimen/dp_px_22"
        app:layout_constraintBottom_toTopOf="@+id/editor_parent_view" />

    <com.meishe.business.assets.view.MYMultiBottomView
        android:id="@+id/edit_add_sticker"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/color_ff181818"
        android:gravity="bottom"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent" />

    <com.meishe.myvideo.view.BottomContainer
        android:id="@+id/fl_bottom_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/color_ff181818"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <com.meishe.myvideo.view.TopContainer
        android:id="@+id/fl_top_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <RelativeLayout
        android:id="@+id/rl_compile_progress"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_cc000000"
        android:visibility="gone">

        <com.meishe.myvideo.view.editview.CompileProgress
            android:id="@+id/edit_compile_progress"
            android:layout_width="@dimen/dp_px_390"
            android:layout_height="@dimen/dp_px_390"
            android:layout_above="@+id/tv_compile_info"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/dp_px_45"
            app:progressBackgroundColor="@color/color_ff4a4a4a"
            app:progressColor="@color/color_fffc2b55" />

        <TextView
            android:id="@+id/tv_compile_progress"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_px_190"
            android:layout_alignTop="@+id/edit_compile_progress"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dp_px_100"
            android:gravity="center"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_48" />

        <TextView
            android:id="@+id/tv_compile_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginTop="@dimen/dp_px_45"
            android:gravity="center"
            android:maxWidth="@dimen/dp_px_900"
            android:text="@string/activity_cut_export_template_please_not_lock_screen_info"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_36" />

        <Button
            android:id="@+id/bt_compile_cancel"
            android:layout_width="@dimen/dp_px_246"
            android:layout_height="@dimen/dp_px_105"
            android:layout_below="@+id/tv_compile_info"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dp_px_90"
            android:background="@color/color_ff2a2a2a"
            android:gravity="center"
            android:text="@string/activity_cut_export_template_cancel"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_42" />
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>