package com.meishe.player.fragment;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.meicam.sdk.NvsLiveWindow;
import com.meicam.sdk.NvsLiveWindowExt;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsVideoResolution;
import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseFragment;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.EngineCallbackManager;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.observer.EngineCallbackObserver;
import com.meishe.player.R;

import static com.meicam.sdk.NvsStreamingContext.STREAMING_ENGINE_STATE_PLAYBACK;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/2/7 10:02
 * @Description :共用的可更改屏幕尺寸的预览播放页面 Common preview playback page that can change screen size
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class PlayerFragment extends BaseFragment {
    private NvsLiveWindowExt mLiveWindow;
    private PlayEventListener mPlayerListener;
    private MeicamTimeline mTimeline;
    private NvsStreamingContext mStreamingContext;
    private EngineCallbackObserver mCallbackObserver;

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
    }

    public PlayerFragment() {
    }

    public static PlayerFragment create() {
        return new PlayerFragment();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EngineCallbackManager.get().registerCallbackObserver(mCallbackObserver = new EngineCallbackObserver() {
            @Override
            public boolean isActive() {
                Activity activity = AppManager.getInstance().currentActivity();
                return isVisible() && getActivity() != null && getActivity().equals(activity);
            }

            @Override
            public void onPlaybackStopped(NvsTimeline nvsTimeline) {
                if (mPlayerListener != null) {
                    mPlayerListener.playStopped(nvsTimeline);
                }
            }

            @Override
            public void onPlaybackEOF(NvsTimeline nvsTimeline) {
                if (mPlayerListener != null) {
                    mPlayerListener.playBackEOF(nvsTimeline);
                }
            }

            @Override
            public void onPlaybackTimelinePosition(NvsTimeline nvsTimeline, long currentPosition) {
                if (mPlayerListener != null) {
                    mPlayerListener.playbackTimelinePosition(nvsTimeline, currentPosition);
                }
            }

            @Override
            public void onStreamingEngineStateChanged(int state) {
                if (mPlayerListener != null) {
                    mPlayerListener.streamingEngineStateChanged(state);
                }
            }

        });
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        mTimeline = NvsBackupData.get().getTimeline();
        mStreamingContext = NvsBackupData.get().getStreamingContext();
        super.onViewCreated(view, savedInstanceState);
    }

    /**
     * 设置播放事件监听
     * Set play event listening
     *
     * @param playListener the play listener
     */
    public void setPlayListener(PlayEventListener playListener) {
        this.mPlayerListener = playListener;
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_palyer;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        mLiveWindow = rootView.findViewById(R.id.live_window);
        mLiveWindow.setFillMode(NvsLiveWindow.FILLMODE_PRESERVEASPECTFIT);
        if (mTimeline != null) {
            setLiveWindowRatio(mTimeline.getVideoResolution());
        }
    }

    @Override
    protected void initData() {
        connectTimeline();
        if (mPlayerListener != null) {
            mPlayerListener.onPlayBackPrepare();
        }
    }

    /**
     * 设置播放窗口的比例
     * Set the live window ratio
     */
    public void setLiveWindowRatio(final NvsVideoResolution videoResolution) {
        if (isInitView) {
            if (mLiveWindow.getWidth() == 0 && mLiveWindow.getHeight() == 0) {
                mLiveWindow.post(new Runnable() {
                    @Override
                    public void run() {
                        setLiveWindowSize(videoResolution);
                    }
                });
            } else {
                setLiveWindowSize(videoResolution);
            }
        }
    }

    /**
     * 设置播放窗口的大小
     * Set the live window size
     */
    private void setLiveWindowSize(NvsVideoResolution videoResolution) {
        if (videoResolution == null) {
            return;
        }
        int viewWidth = mLiveWindow.getWidth();
        int viewHeight = mLiveWindow.getHeight();
        float timelineRatio = 1f * videoResolution.imageWidth / videoResolution.imageHeight;
        float viewRatio = viewWidth * 1F / viewHeight;
        ViewGroup.LayoutParams layoutParams = mLiveWindow.getLayoutParams();
        if (timelineRatio > viewRatio) {
            //宽对齐Wide alignment
            layoutParams.width = viewWidth;
            layoutParams.height = (int) (viewWidth / timelineRatio);
        } else {
            layoutParams.height = viewHeight;
            layoutParams.width = (int) (viewHeight * timelineRatio);
        }
        mLiveWindow.setLayoutParams(layoutParams);
    }

    /**
     * 连接时间线和实时预览图像窗口
     * Connect timeline with live window
     */
    public void connectTimeline() {
        EditorEngine.getInstance().connectTimeline(mTimeline, mLiveWindow);
    }

    /**
     * 播放
     * Play
     *
     * @param flag the flag
     */
    public void playVideo(int flag) {
        if (mStreamingContext != null && mTimeline != null) {
            playVideo(mTimeline.getCurrentPosition(), mTimeline.getDuration(), NvsStreamingContext.VIDEO_PREVIEW_SIZEMODE_LIVEWINDOW_SIZE, flag);
        }
    }

    /**
     * 播放
     * Play
     *
     * @param startTime the start position
     * @param endTime   the end position
     * @param flag      the flag
     */
    public void playVideo(long startTime, long endTime, int flag) {
        playVideo(startTime, endTime, NvsStreamingContext.VIDEO_PREVIEW_SIZEMODE_LIVEWINDOW_SIZE, flag);
    }

    /**
     * 播放
     * Play
     *
     * @param startTime     the start position
     * @param endTime       the end position
     * @param videoSizeMode video preview Mode
     * @param flag          the flag
     */
    public void playVideo(long startTime, long endTime, int videoSizeMode, int flag) {
        // 播放视频 play video
        if (mTimeline != null && mStreamingContext != null) {
            mTimeline.playBack(mStreamingContext, startTime, endTime);
        }
    }


    /**
     * Whether is playing or not？
     * <P></>
     * 判断是否播放
     *
     * @return true：yes； false：no
     */
    public boolean isPlaying() {
        return mStreamingContext != null && mStreamingContext.getStreamingEngineState() == STREAMING_ENGINE_STATE_PLAYBACK;
    }

    /**
     * 预览时间线的当前的时间点
     * Seek to the current position of timeline
     *
     * @param flag the flag
     */
    public void seekTimeline(int flag) {
        if (mStreamingContext != null) {
            seekTimeline(mTimeline.getCurrentPosition(), flag);
        }
    }

    /**
     * 预览时间线的某个时间点
     * Seek to the position of timeline
     *
     * @param timestamp the timestamp
     * @param flag      the flag
     */
    public void seekTimeline(long timestamp, int flag) {
        seekTimeline(timestamp, NvsStreamingContext.VIDEO_PREVIEW_SIZEMODE_LIVEWINDOW_SIZE, flag);
    }

    /**
     * 预览时间线的某个时间点
     * Seek to the position of timeline
     *
     * @param timestamp     the timestamp
     * @param videoSizeMode video preview Mode
     * @param flag          the flag
     */
    public void seekTimeline(long timestamp, int videoSizeMode, int flag) {
        if (mStreamingContext != null && mTimeline != null) {
            mTimeline.seekTimeline(mStreamingContext, timestamp, flag);
        }
    }

    /**
     * 停止播放
     * Stop play
     */
    public void stopVideo() {
        if (mStreamingContext != null) {
            mStreamingContext.stop();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        NvsBackupData.release();
        EngineCallbackManager.get().unregisterCallbackObserver(mCallbackObserver);
    }

    public void setTimelineAndContext(MeicamTimeline timeline, NvsStreamingContext streamingContext) {
        NvsBackupData.get().setTimeline(timeline);
        mTimeline = timeline;
        NvsBackupData.get().setStreamingContext(streamingContext);
        mStreamingContext = streamingContext;
    }

    public interface PlayEventListener {

        void onPlayBackPrepare();

        void playBackEOF(NvsTimeline timeline);

        void playStopped(NvsTimeline timeline);

        void playbackTimelinePosition(NvsTimeline timeline, long stamp);

        void streamingEngineStateChanged(int state);
    }

    public static class NvsBackupData {

        private MeicamTimeline timeline;
        private NvsStreamingContext mStreamingContext;
        public static NvsBackupData sBackupData;

        private NvsBackupData() {
        }

        public static NvsBackupData get() {
            if (sBackupData == null) {
                synchronized (NvsBackupData.class) {
                    if (sBackupData == null) {
                        sBackupData = new NvsBackupData();
                    }
                }
            }
            return sBackupData;
        }

        public MeicamTimeline getTimeline() {
            return timeline;
        }

        public void setTimeline(MeicamTimeline timeline) {
            this.timeline = timeline;
        }

        public NvsStreamingContext getStreamingContext() {
            return mStreamingContext;
        }

        public void setStreamingContext(NvsStreamingContext context) {
            this.mStreamingContext = context;
        }

        public static void release() {
            sBackupData = null;
        }
    }

}
