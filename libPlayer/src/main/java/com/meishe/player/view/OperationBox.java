package com.meishe.player.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.DashPathEffect;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PointF;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Region;
import android.os.Vibrator;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.IntDef;
import androidx.annotation.Nullable;

import com.meishe.player.common.utils.ImageConverter;
import com.meishe.player.view.bean.OperationBoxInfo;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/1/29 14:06
 * @Description :特效操作框 the effect operation box
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class OperationBox extends View {
    public static final int NONE_QUADRANT = 0;
    public static final int FIRST_QUADRANT = 1;
    public static final int SECOND_QUADRANT = 2;
    public static final int THIRD_QUADRANT = 3;
    public static final int FOURTH_QUADRANT = 4;
    /**
     * 直角
     * Right angle
     */
    private final int RIGHT_ANGLE = 90;
    private final float TRANSLATE_ADSORB_DISTANCE = 30f;
    private final float ROTATION_ADSORB_DISTANCE = 10f;
    private PointF mLastPointF = new PointF(0, 0);

    /**
     * 操作框边框的路径
     * Path to the border of the action box
     */
    private Path mRectPath = new Path();
    private boolean mIsInsideBox = false;
    private Paint mRectPaint = new Paint();
    private Paint mInsideRectPaint = new Paint();
    private boolean mMoveOutScreen = false;
    private boolean mIsVisible = true;
    private int mQuadrantIndex;
    private float mNeedTranslateX;
    private float mNeedTranslateY;
    private double mTotalRotationAngle;
    private OperationListener mOperationListener;
    /**
     * 第一象限角的图标区域
     * the first quadrant icon rect
     */
    private RectF mFirstQuadrant = new RectF();

    /**
     * 第二象限角的图标区域
     * the second quadrant icon rect
     */
    private RectF mSecondQuadrant = new RectF();

    /**
     * 第三象限角的图标区域
     * the third quadrant icon rect
     */
    private RectF mThirdQuadrant = new RectF();

    /**
     * 第四象限角的图标区域
     * the fourth quadrant icon rect
     */
    private RectF mFourthQuadrant = new RectF();

    /**
     * 内部填充图最终区域
     * Inside fill the original rect of the icon
     */
    RectF mInsideBitmapDesRect = new RectF();

    /**
     * 内部填充图原始区域
     * Inside fill the final rect of the icon
     */
    Rect mInsideBitmapRect = new Rect();

    /**
     * 震动器
     * Vibrator
     */
    private Vibrator mVibrator;

    private OperationBoxInfo mBoxInfo;

    private final static int ONE_FINGER = 1;
    private final static int TWO_FINGER = 2;

    private boolean mIsTwoFingerEvent = false;
    private double mTwoFingerStartLength;

    public OperationBox(Context context) {
        this(context, null);
    }

    public OperationBox(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);

    }

    public OperationBox(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initRectPaint();
        initInsideRectPaint();
        mVibrator = (Vibrator) context.getSystemService(Context.VIBRATOR_SERVICE);
    }

    private void initRectPaint() {
        // 设置颜色 Set color
        mRectPaint.setColor(Color.parseColor("#4A90E2"));
        // 设置抗锯齿 Set antialias
        mRectPaint.setAntiAlias(true);
        // 设置线宽 Set stroke width
        mRectPaint.setStrokeWidth(8);
        // 设置非填充 Set style
        mRectPaint.setStyle(Paint.Style.STROKE);
    }

    private void initInsideRectPaint() {
        int dashWidth = 4;
        int dashGap = 2;
        // 设置颜色  Set color
        mInsideRectPaint.setColor(Color.parseColor("#9B9B9B"));
        // 设置抗锯齿 Set antialias
        mInsideRectPaint.setAntiAlias(true);
        // 设置线宽 Set stroke width
        mInsideRectPaint.setStrokeWidth(dashWidth);
        // 设置非填充 Set style
        mInsideRectPaint.setStyle(Paint.Style.STROKE);
        //设置虚线效果 Set path effect
        mInsideRectPaint.setPathEffect(new DashPathEffect(new float[]{dashWidth, dashGap}, 0));
    }

    /**
     * 更新操作框
     * update the operation box
     *
     * @param info the operation box info
     */
    public void update(OperationBoxInfo info) {
        mBoxInfo = info;
        notifyBoxChanged();
    }

    /**
     * 更新操作框
     * update the operation box
     */
    public void notifyBoxChanged() {
        changeBoxVisibility(true);
        setVisibility(VISIBLE);
        invalidate();
    }

    /**
     * 重置
     * Reset
     */
    public void reset() {
        if (mBoxInfo != null) {
            mBoxInfo.clear();
        }
    }

    /**
     * 更改操作框的可见性,注意是操作框，不是整个视图
     * Change the visibility of the operation box
     */
    public void changeBoxVisibility(boolean visible) {
        if (mIsVisible == visible) {
            return;
        }
        mIsVisible = visible;
        invalidate();
    }

    public boolean isVisible() {
        return mIsVisible;
    }

    /**
     * 设置操作框的事件监听
     * Set the operation listener
     */
    public void setOperationListener(OperationListener listener) {
        mOperationListener = listener;
    }

    public OperationBoxInfo getBoxInfo() {
        return mBoxInfo;
    }

    /**
     * 获取内部点的集合的索引
     * Gets the index of the collection of the inside point
     */
    public int getInsideIndex(PointF pointF) {
        if (pointF == null) {
            return -1;
        }
        return getInsideIndex((int) pointF.x, (int) pointF.y);
    }

    /**
     * 获取内部点的集合的索引
     * Gets the index of the collection of the inside point
     */
    private int getInsideIndex(int x, int y) {
        List<List<PointF>> insidePointList = mBoxInfo.getInsidePointList();
        if (insidePointList != null) {
            int subCount = insidePointList.size();
            for (int idx = 0; idx < subCount; idx++) {
                if (insideOperationBox(insidePointList.get(idx), x, y)) {
                    return idx;
                }
            }
        }
        return -1;
    }

    /**
     * 获取边框路径
     * Gets the path of border
     */
    private Path getRectPath(List<PointF> listPointF) {
        Path path = new Path();
        path.moveTo(listPointF.get(0).x, listPointF.get(0).y);
        path.lineTo(listPointF.get(1).x, listPointF.get(1).y);
        path.lineTo(listPointF.get(2).x, listPointF.get(2).y);
        path.lineTo(listPointF.get(3).x, listPointF.get(3).y);
        path.close();
        return path;
    }

    /**
     * 获取第一象限角的图片
     * Get the first quadrant icon
     */
    private Bitmap getFirstQuadrantBitmap() {
        PointF point = mBoxInfo.getCornerPointF(3);
        Bitmap bitmap = null;
        if (mBoxInfo.getFirstQuadrantIcon() > 0) {
            bitmap = BitmapFactory.decodeResource(getContext().getResources(), mBoxInfo.getFirstQuadrantIcon());
        }
        mFirstQuadrant.setEmpty();
        if (bitmap != null) {
            mFirstQuadrant.set(point.x - bitmap.getWidth() / 2f, point.y - bitmap.getHeight() / 2f,
                    point.x + bitmap.getWidth() / 2f, point.y + bitmap.getHeight() / 2f);
        }
        return bitmap;
    }

    /**
     * 获取第二象限角的图片
     * Get the second quadrant icon
     */
    private Bitmap getSecondQuadrantBitmap() {
        PointF point = mBoxInfo.getCornerPointF(0);
        Bitmap bitmap = null;
        if (mBoxInfo.getSecondQuadrantIcon() > 0) {
            bitmap = BitmapFactory.decodeResource(getContext().getResources(), mBoxInfo.getSecondQuadrantIcon());
        }
        mSecondQuadrant.setEmpty();
        if (bitmap != null) {
            mSecondQuadrant.set(point.x - bitmap.getWidth() / 2f, point.y - bitmap.getHeight() / 2f,
                    point.x + bitmap.getWidth() / 2f, point.y + bitmap.getHeight() / 2f);
        }
        return bitmap;
    }

    /**
     * 获取第三象限角的图片
     * Get the third quadrant icon
     */
    private Bitmap getThirdQuadrantBitmap() {
        PointF point = mBoxInfo.getCornerPointF(1);
        Bitmap bitmap = null;
        if (mBoxInfo.getThirdQuadrantIcon() > 0) {
            bitmap = BitmapFactory.decodeResource(getContext().getResources(), mBoxInfo.getThirdQuadrantIcon());
        }
        mThirdQuadrant.setEmpty();
        if (bitmap != null) {
            mThirdQuadrant.set(point.x - bitmap.getWidth() / 2f, point.y - bitmap.getHeight() / 2f,
                    point.x + bitmap.getWidth() / 2f, point.y + bitmap.getHeight() / 2f);
        }
        return bitmap;
    }

    /**
     * 获取第四象限角的图片
     * Get the fourth quadrant icon
     */
    private Bitmap getFourthQuadrantBitmap() {
        PointF point = mBoxInfo.getCornerPointF(2);
        Bitmap bitmap = null;
        if (mBoxInfo.getFourthQuadrantIcon() > 0) {
            bitmap = BitmapFactory.decodeResource(getContext().getResources(), mBoxInfo.getFourthQuadrantIcon());
        }
        mFourthQuadrant.setEmpty();
        if (bitmap != null) {
            mFourthQuadrant.set(point.x - bitmap.getWidth() / 2f, point.y - bitmap.getHeight() / 2f,
                    point.x + bitmap.getWidth() / 2f, point.y + bitmap.getHeight() / 2f);

        }
        return bitmap;
    }

    /**
     * 获取内部填充的图片
     * Get the icon filled inside
     */
    private Bitmap getInsideBitmap() {
        PointF point0 = mBoxInfo.getCornerPointF(0);
        PointF point1 = mBoxInfo.getCornerPointF(1);
        PointF point2 = mBoxInfo.getCornerPointF(2);
        Bitmap bitmap = ImageConverter.convertImageScaleByWidth(getContext(), mBoxInfo.getInsideCover(), (int) (point2.x - point1.x));
        if (bitmap != null) {
            mInsideBitmapRect.set(0, 0, bitmap.getWidth(), bitmap.getHeight());
            mInsideBitmapDesRect.set(point0.x, point0.y, point2.x, point2.y);
        } else {
            mInsideBitmapDesRect.setEmpty();
        }
        return bitmap;
    }

    /**
     * 绘制内部边框路径
     * Draw the internal border path
     */
    private void drawInsidePath(Canvas canvas) {
        Path path;
        List<List<PointF>> insidePointList = mBoxInfo.getInsidePointList();
        if (insidePointList != null) {
            int subCount = insidePointList.size();
            for (int idx = 0; idx < subCount; idx++) {
                List<PointF> listPointF = insidePointList.get(idx);
                if (listPointF == null || listPointF.size() != 4) {
                    continue;
                }
                path = getRectPath(listPointF);
                canvas.drawPath(path, mInsideRectPaint);
            }
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (!mIsVisible) {
            return;
        }
        if (mBoxInfo == null || mBoxInfo.getCornerPointList() == null || mBoxInfo.getCornerPointList().size() != 4) {
            return;
        }
        Bitmap bitmap;
        canvas.drawPath(getRectPath(mBoxInfo.getCornerPointList()), mRectPaint);
        bitmap = getInsideBitmap();
        if (bitmap != null) {
            canvas.drawBitmap(bitmap, mInsideBitmapRect, mInsideBitmapDesRect, null);
        }
        bitmap = getFirstQuadrantBitmap();
        if (bitmap != null) {
            canvas.drawBitmap(bitmap, mFirstQuadrant.left, mFirstQuadrant.top, mRectPaint);
        }
        bitmap = getSecondQuadrantBitmap();
        if (bitmap != null) {
            canvas.drawBitmap(bitmap, mSecondQuadrant.left, mSecondQuadrant.top, mRectPaint);
        }
        bitmap = getThirdQuadrantBitmap();
        if (bitmap != null) {
            canvas.drawBitmap(bitmap, mThirdQuadrant.left, mThirdQuadrant.top, mRectPaint);
        }
        bitmap = getFourthQuadrantBitmap();
        if (bitmap != null) {
            canvas.drawBitmap(bitmap, mFourthQuadrant.left, mFourthQuadrant.top, mRectPaint);
        }
        drawInsidePath(canvas);
    }

    /**
     * 所给点的是否在操作框内部
     * Whether the point given is inside the operation box
     *
     * @return true is inside ,false not
     */
    public boolean insideOperationBox(int x, int y) {
        if (mBoxInfo == null) {
            return false;
        }
        // 判断手指是否在字幕框内 Determine if the finger is inside the subtitle box.
        return insideOperationBox(mBoxInfo.getCornerPointList(), x, y);
    }

    /**
     * 所给点的集合是否在操作框内部
     * Whether the set of points given is inside the operation box
     *
     * @return true is inside ,false not
     */
    public boolean insideOperationBox(List<PointF> pointList, int x, int y) {
        if (pointList == null || pointList.size() != 4) {
            return false;
        }
        // 判断手指是否在编辑框内 Determine if the finger is in the edit box.
        RectF r = new RectF();
        Path path = new Path();
        path.moveTo(pointList.get(0).x, pointList.get(0).y);
        path.lineTo(pointList.get(1).x, pointList.get(1).y);
        path.lineTo(pointList.get(2).x, pointList.get(2).y);
        path.lineTo(pointList.get(3).x, pointList.get(3).y);
        path.close();
        path.computeBounds(r, true);
        Region region = new Region();
        region.setPath(path, new Region((int) r.left, (int) r.top, (int) r.right, (int) r.bottom));
        return region.contains(x, y);
    }

    /**
     * 获取象限索引
     * Get the quadrant index
     */
    private int getQuadrantIndex(float upX, float upY) {
        if (mFirstQuadrant.contains(upX, upY)) {
            return FIRST_QUADRANT;
        }
        if (mSecondQuadrant.contains(upX, upY)) {
            return SECOND_QUADRANT;
        }
        if (mThirdQuadrant.contains(upX, upY)) {
            return THIRD_QUADRANT;
        }
        if (mFourthQuadrant.contains(upX, upY)) {
            return FOURTH_QUADRANT;
        }
        return NONE_QUADRANT;
    }

    /**
     * 获取象限图标区域
     * Get the quadrant index
     */
    private RectF getQuadrantRect(int quadrantIndex) {
        if (quadrantIndex == FIRST_QUADRANT) {
            return mFirstQuadrant;
        } else if (quadrantIndex == SECOND_QUADRANT) {
            return mSecondQuadrant;
        } else if (quadrantIndex == THIRD_QUADRANT) {
            return mThirdQuadrant;
        } else if (quadrantIndex == FOURTH_QUADRANT) {
            return mFourthQuadrant;
        }
        return new RectF();
    }

    private PointF mLastScalePointF = new PointF();
    private boolean hadMove;
    private float targetX, targetY;

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {

        if (mOperationListener == null) {
            return false;
        }
        int pointerCount = event.getPointerCount();
        if (pointerCount > TWO_FINGER) {
            return false;
        }

        if (((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_DOWN) && (pointerCount == ONE_FINGER)) {
            mIsTwoFingerEvent = false;
        }

        targetX = event.getX();
        targetY = event.getY();

        if (pointerCount == TWO_FINGER) {
            mIsTwoFingerEvent = true;
            twoFingerTouch(event);
        } else {
            oneFingerTouch(event);
        }


        return true;
    }

    private void twoFingerTouch(MotionEvent event) {
        if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_POINTER_DOWN) {
            mLastScalePointF.set(targetX, targetY);
            float xLen = event.getX(0) - event.getX(1);
            float yLen = event.getY(0) - event.getY(1);
            mTwoFingerStartLength = Math.sqrt((xLen * xLen) + (yLen * yLen));
            mTotalRotationAngle = calculateBoxRotationAngle();
            mHadInRotationAdsorbRange = (Math.abs(mTotalRotationAngle % RIGHT_ANGLE) <= ROTATION_ADSORB_DISTANCE ||
                    Math.abs(Math.abs(mTotalRotationAngle % RIGHT_ANGLE) - RIGHT_ANGLE) <= ROTATION_ADSORB_DISTANCE);
        } else if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_MOVE) {

            if (!mIsVisible || mBoxInfo == null) {
                return;
            }
            // 防止移出屏幕 Prevent screen removal.
            if (targetX <= 100 || targetX >= getWidth() || targetY >= getHeight() || targetY <= 20) {
                mMoveOutScreen = true;
                return;
            }
            if (mMoveOutScreen) {
                mMoveOutScreen = false;
                return;
            }
            PointF centerPointF = mBoxInfo.getCenterPointF();
            PointF newPointF = new PointF(targetX + mNeedTranslateX, targetY + mNeedTranslateY);
            float xLen = event.getX(0) - event.getX(1);
            float yLen = event.getY(0) - event.getY(1);
            double mTwoFingerEndLength = Math.sqrt(xLen * xLen + yLen * yLen);
            float scale = (float) (mTwoFingerEndLength / mTwoFingerStartLength);
            double theta = Math.atan2(targetY - centerPointF.y, targetX - centerPointF.x) -
                    Math.atan2(mLastPointF.y - centerPointF.y, mLastPointF.x - centerPointF.x);
            //得出移动的角度 Determine the angle of movement.
            theta = theta * RIGHT_ANGLE * 2 / Math.PI;
            theta = dealWithRotationAdsorb(theta);
            if (theta != Double.MAX_VALUE) {
                //旋转与缩放 Rotate and Scale.
                if (mOperationListener != null) {
                    mOperationListener.onRotationAndScale(scale, (float) -theta, centerPointF);
                }
            } else {
                //在吸附范围内也要执行缩放,此时旋转角度是无效的。
                // Scaling should also be performed within the adsorption range, where the rotation angle is invalid.
                mOperationListener.onRotationAndScale(scale, Float.MAX_VALUE, centerPointF);
                mLastScalePointF = newPointF;
                mTwoFingerStartLength = mTwoFingerEndLength;
                return;
            }

            mTwoFingerStartLength = mTwoFingerEndLength;
            mLastScalePointF = newPointF;
            mLastPointF = newPointF;
        } else if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_UP) {
            mTotalRotationAngle = 0;
            mNeedTranslateX = 0;
            mNeedTranslateY = 0;
        }
    }

    private void oneFingerTouch(MotionEvent event) {
        if (mIsTwoFingerEvent) {
            if (event.getAction() == MotionEvent.ACTION_UP) {
                mIsTwoFingerEvent = false;
                mOperationListener.onTouchUp(new PointF(targetX, targetY));
            }
            return;
        }

        int action = event.getAction();
        if (action == MotionEvent.ACTION_DOWN) {
            mOperationListener.onTouchDown(new PointF(targetX, targetY));
            //是否在操作框内 Is it in the operation box.
            mIsInsideBox = insideOperationBox((int) targetX, (int) targetY);
            //获取四个角落图标的索引（判断是否点在四个角的图标区域）
            // Obtain the index of the four corner icons
            // (determine if the dots are in the icon area of the four corners).
            mQuadrantIndex = getQuadrantIndex(targetX, targetY);
            mLastPointF.set(targetX, targetY);
            hadMove = false;
            mLastScalePointF.set(targetX, targetY);
            if (mBoxInfo != null) {
                if (getQuadrantRect(mBoxInfo.getScaleQuadrant()).contains(targetX, targetY)) {
                    mQuadrantIndex = mBoxInfo.getScaleQuadrant();
                }
                PointF centerPointF = mBoxInfo.getCenterPointF();
                mHadInXAdsorbRange = Math.abs(centerPointF.x - (getWidth() / 2f)) <= TRANSLATE_ADSORB_DISTANCE;
                mHadInYAdsorbRange = Math.abs(centerPointF.y - (getHeight() / 2f)) <= TRANSLATE_ADSORB_DISTANCE;
                mTotalRotationAngle = calculateBoxRotationAngle();
                mHadInRotationAdsorbRange = (Math.abs(mTotalRotationAngle % RIGHT_ANGLE) <= ROTATION_ADSORB_DISTANCE ||
                        Math.abs(Math.abs(mTotalRotationAngle % RIGHT_ANGLE) - RIGHT_ANGLE) <= ROTATION_ADSORB_DISTANCE);
            }
        } else if (action == MotionEvent.ACTION_UP) {
            PointF pointF = new PointF(targetX, targetY);
            if (!mOperationListener.interceptClick(pointF) && !hadMove) {
                int quadrantIndex = getQuadrantIndex(targetX, targetY);
                if (quadrantIndex != NONE_QUADRANT) {
                    mOperationListener.onClickCorner(quadrantIndex, pointF);
                } else {
                    mOperationListener.onClickOther(insideOperationBox((int) targetX, (int) targetY), pointF);
                }
            }
            hadMove = false;
            mOperationListener.onTouchUp(pointF);
            mNeedTranslateX = 0;
            mNeedTranslateY = 0;
            mTotalRotationAngle = 0;
        } else if (action == MotionEvent.ACTION_MOVE) {
            if (!mIsVisible || mBoxInfo == null) {
                return;
            }
            if (Math.abs(mLastPointF.x - targetX) > 5 || Math.abs(mLastPointF.y - targetY) > 5) {
                hadMove = true;
            }
            // 防止移出屏幕 Prevent screen removal.
            if (targetX <= 100 || targetX >= getWidth() || targetY >= getHeight() || targetY <= 20) {
                mMoveOutScreen = true;
                return;
            }
            if (mMoveOutScreen) {
                mMoveOutScreen = false;
                return;
            }
            //操作框中心点 Operation box center point.
            PointF centerPointF = mBoxInfo.getCenterPointF();
            PointF newPointF = new PointF(targetX + mNeedTranslateX, targetY + mNeedTranslateY);
            //按下位置是否是缩放、旋转图标所在的象限（是不是在拖动缩放、旋转图标）
            // Is the pressed position the quadrant where the zoom or rotate icon is located (is it dragging the zoom or rotate icon).
            if (mQuadrantIndex == mBoxInfo.getScaleQuadrant()) {
                float scale;
                //区分x和y的缩放 Distinguishing scaling between x and y.
                if (!mOperationListener.onScale((targetX - centerPointF.x) / (mLastPointF.x - centerPointF.x),
                        (targetY - centerPointF.y) / (mLastPointF.y - centerPointF.y), centerPointF)) {

                    //如果没有拦截区分x和y的缩放，走旋转缩放逻辑
                    // If there is no interception to distinguish the scaling of x and y, follow the rotation scaling logic.
                    double theta = Math.atan2(targetY - centerPointF.y, targetX - centerPointF.x) -
                            Math.atan2(mLastPointF.y - centerPointF.y, mLastPointF.x - centerPointF.x);
                    //得出移动的角度 Determine the angle of movement.
                    theta = theta * RIGHT_ANGLE * 2 / Math.PI;
                    theta = dealWithRotationAdsorb(theta);
                    if (theta != Double.MAX_VALUE) {
                        //整体缩放倍数(不区分x和y的缩放)
                        // Overall scaling factor (scaling without distinguishing between x and y).
                        scale = (float) (Math.sqrt(Math.pow(targetX - centerPointF.x, 2) +
                                Math.pow(targetY - centerPointF.y, 2)) / Math.sqrt(Math.pow(mLastPointF.x - centerPointF.x, 2)
                                + Math.pow(mLastPointF.y - centerPointF.y, 2)));

                        //旋转与缩放 Rotate and Scale.
                        mOperationListener.onRotationAndScale(scale, (float) -theta, centerPointF);
                        mLastScalePointF = newPointF;
                    } else {
                        //整体缩放倍数(不区分x和y的缩放)
                        //在吸附范围内也要执行缩放,此时旋转角度是无效的。
                        // Overall scaling factor (scaling without distinguishing
                        // between x and y).
                        // Scaling should also be performed within the adsorption range,
                        // where the rotation angle is invalid.

                        scale = (float) (Math.sqrt(Math.pow(targetX - centerPointF.x, 2) +
                                Math.pow(targetY - centerPointF.y, 2)) / Math.sqrt(Math.pow(mLastScalePointF.x - centerPointF.x, 2)
                                + Math.pow(mLastScalePointF.y - centerPointF.y, 2)));

                        mOperationListener.onRotationAndScale(scale, Float.MAX_VALUE, centerPointF);
                        mLastScalePointF = newPointF;
                        return;
                    }
                }
            } else if (mIsInsideBox) {
                if (!dealWithTranslateAdsorb(centerPointF, newPointF)) {
                    mOperationListener.onTranslate(mLastPointF, newPointF, centerPointF);
                } else {
                    return;
                }
            }
            mLastPointF = newPointF;
        }

    }

    /**
     * 计算操作框的旋转角度
     * Calculates the rotation Angle of the operation box
     *
     * @return the angle
     */
    private double calculateBoxRotationAngle() {
        if (mBoxInfo == null) {
            return 0;
        }
        List<PointF> cornerPointList = mBoxInfo.getCornerPointList();
        if (cornerPointList == null || cornerPointList.size() < 2) {
            return 0;
        }
        PointF pointF0 = cornerPointList.get(0);
        PointF pointF1 = cornerPointList.get(1);
        float tan = (float) (Math.atan(Math.abs((pointF1.y - pointF0.y)
                / (pointF1.x - pointF0.x))) * 180 / Math.PI);
        if (pointF1.x > pointF0.x && pointF1.y > pointF0.y) {
            // 第一象限 first quadrant
            return Math.abs(-tan % RIGHT_ANGLE);
        } else if (pointF1.x > pointF0.x && pointF1.y < pointF0.y) {
            // 第二象限 second quadrant
            return Math.abs(tan % RIGHT_ANGLE);
        } else if (pointF1.x < pointF0.x && pointF1.y > pointF0.y) {
            // 第三象限 third quadrant
            return Math.abs((tan - RIGHT_ANGLE * 2) % RIGHT_ANGLE);
        } else {
            return Math.abs((RIGHT_ANGLE * 2 - tan) % RIGHT_ANGLE);
        }
    }

    /**
     * 已经在旋转吸附范围内的标识
     */
    private boolean mHadInRotationAdsorbRange;

    /**
     * 处理旋转吸附
     * Deal with the rotation adsorb
     */
    private double dealWithRotationAdsorb(double theta) {
        double r = (mTotalRotationAngle + theta) % RIGHT_ANGLE;
        if (!mHadInRotationAdsorbRange) {
            if (Math.abs(r) <= ROTATION_ADSORB_DISTANCE) {
                //余数接近0，符合吸附条件的
                // Remainder close to 0, meeting adsorption conditions.
                // double b = theta < 0 ? -r + theta : -r + theta;
                theta = -r + theta;
                // LogUtils.d("余数接近0，需要移动的角度=" + b + ",LastRotationAngle=" + mTotalRotationAngle + ",r=" + r + ",total=" + (mTotalRotationAngle + b));
                mHadInRotationAdsorbRange = true;
                if (mVibrator != null) {
                    mVibrator.vibrate(50);
                }
            } else if (Math.abs(Math.abs(r) - RIGHT_ANGLE) <= ROTATION_ADSORB_DISTANCE) {
                //接近90，符合吸附条件的
                // Approaching 90, meeting the adsorption conditions.
                //double b = theta < 0 ? -(r + RIGHT_ANGLE) + theta : RIGHT_ANGLE - r + theta;
                theta = theta < 0 ? -(r + RIGHT_ANGLE) + theta : RIGHT_ANGLE - r + theta;
                //LogUtils.d("余数接近90，需要移动的角度=" + b + ",LastRotationAngle=" + mTotalRotationAngle + ",r=" + r + ",total=" + (mTotalRotationAngle + b));
                mHadInRotationAdsorbRange = true;
                if (mVibrator != null) {
                    mVibrator.vibrate(50);
                }
            }
        } else {
            mHadInRotationAdsorbRange = (Math.abs(r) <= ROTATION_ADSORB_DISTANCE || Math.abs(Math.abs(r) - RIGHT_ANGLE) <= ROTATION_ADSORB_DISTANCE);
            if (mHadInRotationAdsorbRange && Math.abs(theta) < ROTATION_ADSORB_DISTANCE) {
                //吸附保持。单次旋转角度较小，则保持吸附
                // Adsorption retention. If the single rotation angle is small, maintain adsorption.
                // LogUtils.d("移动角度小，保持，angle=" + theta);
                return Double.MAX_VALUE;
            }
        }
        mTotalRotationAngle = theta + mTotalRotationAngle;
        //LogUtils.d("theta=" + theta + ",mTotalRotationAngle=" + mTotalRotationAngle + ",r=" + r);
        return theta;
    }

    /**
     * 已经在x轴方向吸附范围内的标识
     */

    private boolean mHadInXAdsorbRange;
    /**
     * 已经在y轴吸附范围内的标识
     */
    private boolean mHadInYAdsorbRange;

    /**
     * 处理平移吸附
     * Deal with the translate adsorb
     */
    private boolean dealWithTranslateAdsorb(PointF centerPointF, PointF newPointF) {
        float translateDistance;
        if (!mHadInXAdsorbRange) {
            //没有在x轴方向吸附范围内 Not within the adsorption range in the x-axis direction
            if (Math.abs(translateDistance = centerPointF.x - (getWidth() / 2f)) <= TRANSLATE_ADSORB_DISTANCE) {
                mHadInXAdsorbRange = true;
                //计算需要累计的x轴方向的平移值(位置变动了，但是在屏幕上触摸位置并没有等效变动，所以补上差值)
                // Calculate the cumulative translation value in the x-axis direction
                // (the position has changed, but there is no equivalent change when
                // touching the position on the screen, so add the difference)
                mNeedTranslateX = mNeedTranslateX - translateDistance;
                //更新新的点的位置 Update the position of new points.
                newPointF.set(newPointF.x - translateDistance, newPointF.y);
                if (mVibrator != null) {
                    mVibrator.vibrate(50);
                }
            }
        } else {
            //在x轴方向吸附范围内 Within the adsorption range in the x-axis direction.
            mHadInXAdsorbRange = Math.abs(centerPointF.x - (getWidth() / 2f)) <= TRANSLATE_ADSORB_DISTANCE;
        }
        if (!mHadInYAdsorbRange) {
            //没有在y轴方向吸附范围内 Not within the adsorption range in the y-axis direction.
            if (Math.abs(translateDistance = centerPointF.y - (getHeight() / 2f)) <= TRANSLATE_ADSORB_DISTANCE) {
                mHadInYAdsorbRange = true;
                //计算需要累计的y轴方向的平移值(位置变动了，但是在屏幕上触摸位置并没有等效变动，所以补上差值)
                // Calculate the cumulative translation value in the y-axis direction
                // (the position has changed, but there is no equivalent change when
                // touching the position on the screen, so add the difference)
                mNeedTranslateY = mNeedTranslateY - translateDistance;
                //更新新的点的位置 Update the position of new points.
                newPointF.set(newPointF.x, newPointF.y - translateDistance);
                if (mVibrator != null) {
                    mVibrator.vibrate(50);
                }
            }
        } else {
            //在y轴方向吸附范围内 Within the adsorption range in the y-axis direction.
            mHadInYAdsorbRange = Math.abs(centerPointF.y - (getHeight() / 2f)) <= TRANSLATE_ADSORB_DISTANCE;
        }
        boolean hold = false;
        float dx = Math.abs(newPointF.x - mLastPointF.x);
        float dy = Math.abs(newPointF.y - mLastPointF.y);
        //吸附保持范围 Adsorption retention range.
        double holdRange = TRANSLATE_ADSORB_DISTANCE;
        //如果x和y都在吸附保持范围内
        //如果x在吸附保持范围并且是沿x轴方向移动
        //如果y在吸附保持范围并且是沿y轴方向移动
        //这三种情况就保持吸附
        //If both x and y are within the adsorption retention range,
        // If x is within the adsorption holding range and moves along the x-axis direction,
        // If y is within the adsorption retention range and moves along the y-axis direction,
        // Maintain adsorption in these three situations
        if (mHadInXAdsorbRange && mHadInYAdsorbRange) {
            //x和y方向都进入吸附范围，
            // Both the x and y directions enter the adsorption range.
            hold = (dx <= holdRange && dy <= holdRange)
                    || dx <= holdRange && dx >= dy
                    || dy <= holdRange && dy >= dx;
        } else if (mHadInXAdsorbRange) {
            hold = dx <= holdRange && dx > dy;
        } else if (mHadInYAdsorbRange) {
            hold = dy <= holdRange && dy > dx;
        }
        return hold;
    }

    public static abstract class OperationListener {
        public boolean onTouchDown(PointF pointF) {
            return false;
        }

        public boolean onTouchUp(PointF pointF) {
            return false;
        }

        /**
         * 拦截点击事件
         * Intercept click event
         *
         * @return true intercept 拦截,false not 不拦截
         */
        public boolean interceptClick(PointF pointF) {
            return false;
        }

        public void onClickOther(boolean insideBox, PointF pointF) {

        }

        public void onClickCorner(@Quadrant int quadrant, PointF pointF) {
        }

        public void onTranslate(PointF lastPointF, PointF newPointF, PointF centerPointF) {
        }

        public boolean onScale(float xFactor, float yFactor, PointF centerPointF) {
            return false;
        }

        public void onRotationAndScale(float scaleFactor, float rotate, PointF centerPointF) {
        }
    }

    @IntDef({NONE_QUADRANT, FIRST_QUADRANT, SECOND_QUADRANT, THIRD_QUADRANT, FOURTH_QUADRANT})
    @Retention(RetentionPolicy.SOURCE)
    public @interface Quadrant {
    }
}
