package com.meishe.draft.db;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/5/18 15:53
 * @Description :文件信息 entity. The entity of file information.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
@Entity
public class FileInfoEntity {
   @PrimaryKey
   @NonNull
   private String id = "123456789";

   private String localPath;
   /**
    * 本地倒放路径
    * The local path of reverse file.
    */
   private String localReversePath;
   private String md5;
   private String url;
   private String userId;
   private String m3u8CommonUrl;
   private String m3u8AlphaUrl;
   private String m3u8ReverseUrl;
   private String m3u8ReverseAlphaUrl;
   private String resourceId;

   public String getLocalPath() {
      return localPath;
   }

   public void setLocalPath(String localPath) {
      this.localPath = localPath;
   }

   public String getLocalReversePath() {
      return localReversePath;
   }

   public void setLocalReversePath(String localReversePath) {
      this.localReversePath = localReversePath;
   }

   public String getMd5() {
      return md5;
   }

   public void setMd5(String md5) {
      this.md5 = md5;
   }

   public String getUrl() {
      return url;
   }

   public void setUrl(String url) {
      this.url = url;
   }

   @NonNull
   public String getId() {
      return id;
   }

   public void setId(@NonNull String id) {
      this.id = id;
   }

   public String getUserId() {
      return userId;
   }

   public void setUserId(String userId) {
      this.userId = userId;
   }

   public String getM3u8CommonUrl() {
      return m3u8CommonUrl;
   }

   public void setM3u8CommonUrl(String m3u8CommonUrl) {
      this.m3u8CommonUrl = m3u8CommonUrl;
   }

   public String getM3u8AlphaUrl() {
      return m3u8AlphaUrl;
   }

   public void setM3u8AlphaUrl(String m3u8AlphaUrl) {
      this.m3u8AlphaUrl = m3u8AlphaUrl;
   }

   public String getM3u8ReverseUrl() {
      return m3u8ReverseUrl;
   }

   public void setM3u8ReverseUrl(String m3u8ReverseUrl) {
      this.m3u8ReverseUrl = m3u8ReverseUrl;
   }

   public String getM3u8ReverseAlphaUrl() {
      return m3u8ReverseAlphaUrl;
   }

   public void setM3u8ReverseAlphaUrl(String m3u8ReverseAlphaUrl) {
      this.m3u8ReverseAlphaUrl = m3u8ReverseAlphaUrl;
   }

   public String getResourceId() {
      return resourceId;
   }

   public void setResourceId(String resourceId) {
      this.resourceId = resourceId;
   }
}
