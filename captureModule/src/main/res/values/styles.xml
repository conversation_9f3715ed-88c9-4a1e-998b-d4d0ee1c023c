<resources >

    <style name="dialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <!--边框-->
        <item name="android:windowIsFloating">true</item>
        <!--是否浮现在activity之上-->
        <item name="android:windowIsTranslucent">true</item>
        <!--半透明-->
        <item name="android:windowNoTitle">true</item>
        <!--无标题-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--背景透明-->

    </style>


    <style name="fx_dlg_style" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/dialog_enter</item>
        <item name="android:windowExitAnimation">@anim/dialog_exit</item>
    </style>


    <style name="CustomTabLayout" parent="Widget.Design.TabLayout">
        <item name="tabPaddingStart">8dp</item>
        <item name="tabPaddingEnd">8dp</item>
        <item name="android:textSize">16sp</item>   
        <item name="android:textColor">#00000000</item> 
        <item name="tabSelectedTextColor">#4A90E2</item> 
    </style>


    <!-- 这里是使用动画的 -->
    <style name="BaseAnimationTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/ff000000</item>
        <item name="colorPrimaryDark">@color/ff000000</item>
        <item name="colorAccent">@color/colorAccent</item>
        <!--activity的动画-->
        <item name="android:windowAnimationStyle">@style/HoloThemeActivityAnimation</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <!--<item name="android:windowBackground">@android:color/transparent</item>-->

    </style>

    <!--Activity入场与退出动画-->
    <style name="HoloThemeActivityAnimation" parent="@android:style/Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/slide_left</item>
        <!--<item name="android:activityOpenExitAnimation">@anim/slide_right</item>-->
        <!--<item name="android:activityCloseEnterAnimation">@anim/slide_left</item>-->
        <item name="android:activityCloseExitAnimation">@anim/slide_right</item>
    </style>



</resources>
