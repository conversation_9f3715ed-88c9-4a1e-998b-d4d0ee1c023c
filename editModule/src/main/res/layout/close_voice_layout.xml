<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_close_original_voice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@mipmap/ic_voice" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/iv_close_original_voice"
        android:layout_marginTop="@dimen/dp17"
        android:textColor="@color/white_8"
        android:text="@string/close_original_voice"
        android:textSize="@dimen/close_original_voice_text_size" />

</RelativeLayout>