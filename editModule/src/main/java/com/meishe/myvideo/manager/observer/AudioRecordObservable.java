package com.meishe.myvideo.manager.observer;

import android.database.Observable;
import android.os.Looper;

import com.meishe.base.utils.ThreadUtils;


/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2021/6/11 10:30
 * @Description: 音频录制被观察者 The audio record observable
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class AudioRecordObservable extends Observable<AudioRecordObserver> {

    /**
     * On record start.
     * 记录开始
     *
     * @param id       the id 音频id
     * @param filePath the file path 文件路径
     */
    public void onRecordStart(final Long id, final String filePath) {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            for (int i = mObservers.size() - 1; i >= 0; i--) {
                mObservers.get(i).onRecordStart(id, filePath);
            }
        } else {
            ThreadUtils.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    for (int i = mObservers.size() - 1; i >= 0; i--) {
                        mObservers.get(i).onRecordStart(id, filePath);
                    }
                }
            });
        }
    }

    /**
     * On record progress.
     * 记录进度
     *
     * @param wave   the wave 波形数据
     * @param during the during 时长
     * @param path   the path 文件路径
     */
    public void onRecordProgress(final float[] wave, final int during, final String path) {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            for (int i = mObservers.size() - 1; i >= 0; i--) {
                mObservers.get(i).onRecordProgress(wave, during, path);
            }
        } else {
            ThreadUtils.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    for (int i = mObservers.size() - 1; i >= 0; i--) {
                        mObservers.get(i).onRecordProgress(wave, during, path);
                    }
                }
            });
        }
    }

    /**
     * On record fail.
     * 记录失败
     *
     * @param msg the msg 错误信息
     */
    public void onRecordFail(final String msg) {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            for (int i = mObservers.size() - 1; i >= 0; i--) {
                mObservers.get(i).onRecordFail(msg);
            }
        } else {
            ThreadUtils.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    for (int i = mObservers.size() - 1; i >= 0; i--) {
                        mObservers.get(i).onRecordFail(msg);
                    }
                }
            });
        }
    }

    /**
     * On record end.
     * 记录结束
     */
    public void onRecordEnd() {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            for (int i = mObservers.size() - 1; i >= 0; i--) {
                mObservers.get(i).onRecordEnd();
            }
        } else {
            ThreadUtils.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    for (int i = mObservers.size() - 1; i >= 0; i--) {
                        mObservers.get(i).onRecordEnd();
                    }
                }
            });
        }
    }

    @Override
    public void registerObserver(AudioRecordObserver observer) {
        if (!mObservers.contains(observer)) {
            super.registerObserver(observer);
        }
    }

    @Override
    public void unregisterObserver(AudioRecordObserver observer) {
        if (mObservers.contains(observer)) {
            super.unregisterObserver(observer);
        }
    }
}
