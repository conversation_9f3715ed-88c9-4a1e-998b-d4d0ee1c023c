package com.meishe.myvideo.fragment;

import android.view.View;

import com.meishe.base.model.BaseFragment;
import com.meishe.myvideo.R;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> <PERSON>H<PERSON>Zhou
 * @CreateDate :2020/11/11
 * @Description :剪辑草稿底部的删除view  the View at the bottom of the clip draft
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class BottomDeleteFragment extends BaseFragment {
    private OnEventListener mListener;

    public BottomDeleteFragment() {
    }

    public static BottomDeleteFragment create(OnEventListener listener) {
        return new BottomDeleteFragment().setListener(listener);
    }

    private BottomDeleteFragment setListener(OnEventListener listener) {
        mListener = listener;
        return this;
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_bottom_delete;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        rootView.findViewById(R.id.bt_delete).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mListener != null) {
                    mListener.onDelete();
                }
            }
        });
        rootView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mListener != null) {
                    mListener.onDelete();
                }
            }
        });
    }

    @Override
    protected void initData() {

    }

    public interface OnEventListener {
        /**
         * On delete.
         */
        void onDelete();
    }
}
