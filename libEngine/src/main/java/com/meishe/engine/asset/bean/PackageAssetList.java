package com.meishe.engine.asset.bean;

import java.io.Serializable;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZ<PERSON>
 * @CreateDate :2021/2/24 14:53
 * @Description :包内资源包裹实体类 An in-package resource wraps an entity class
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class PackageAssetList implements Serializable {
    private List<AssetInfo> assetList;

    public List<AssetInfo> getAssetList() {
        return assetList;
    }

    public void setAssetList(List<AssetInfo> assetList) {
        this.assetList = assetList;
    }
}
