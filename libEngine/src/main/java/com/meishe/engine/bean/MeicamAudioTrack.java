package com.meishe.engine.bean;

import com.meicam.sdk.NvsAudioClip;
import com.meicam.sdk.NvsAudioTrack;
import com.meicam.sdk.NvsObject;
import com.meicam.sdk.NvsTimeline;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.local.LMeicamAudioClip;
import com.meishe.engine.local.LMeicamAudioTrack;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 版权所有:www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/05/20 10:07
 * @Description : 音频轨道 The audio track
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MeicamAudioTrack extends TrackInfo<NvsAudioTrack> implements Cloneable, Serializable, TimelineDataParserAdapter<LMeicamAudioTrack> {

    /**
     * 音频片段集合
     * Audio clip list
     */
    private final List<MeicamAudioClip> mAudioClipList = new ArrayList<>();

    MeicamAudioTrack(NvsAudioTrack nvsAudioTrack, int index) {
        super(nvsAudioTrack, CommonData.TRACK_AUDIO, index);
    }

    List<MeicamAudioClip> getAudioClipList() {
        return mAudioClipList;
    }

    @Override
    void setIndex(int index) {
        super.setIndex(index);
        for (MeicamAudioClip meicamAudioClip : mAudioClipList) {
            meicamAudioClip.setTrackIndex(index);
        }
    }

    /**
     * 添加音频片段
     * Add audio clip
     *
     * @param audioClip the audio clip 音频路径
     * @param inPoint   the in point  入点
     * @param trimIn    the trim in point  裁入点
     * @param trimOut   the trim out point  裁出点
     * @return MeicamAudioClip the audio clip音频片段
     **/
    public MeicamAudioClip addAudioClip(MeicamAudioClip audioClip, long inPoint, long trimIn, long trimOut) {
        if (audioClip == null) {
            LogUtils.e("audio clip is null");
            return null;
        }
        NvsAudioTrack nvsAudioTrack = getObject();
        if (nvsAudioTrack != null) {
            NvsAudioClip nvsAudioClip = nvsAudioTrack.addClip(audioClip.getFilePath(), inPoint, trimIn, trimOut);
            if (nvsAudioClip != null) {
                MeicamAudioClip newClip = new MeicamAudioClip(nvsAudioClip, audioClip.getFilePath(), inPoint, trimIn, trimOut);
                newClip.copyData(audioClip);
                addAudioClip(newClip, nvsAudioClip.getIndex());
                return newClip;
            }
        }
        return null;
    }


    /**
     * 添加音频片段
     * Add audio clip
     *
     * @param audioPath the video path 音频路径
     * @param inPoint   the in point  入点
     * @param trimIn    the trim in point  裁入点
     * @param trimOut   the trim out point  裁出点
     * @return MeicamAudioClip the audio clip音频片段
     **/
    public MeicamAudioClip addAudioClip(String audioPath, long inPoint, long trimIn, long trimOut) {
        NvsAudioTrack nvsAudioTrack = getObject();
        if (nvsAudioTrack != null) {
            NvsAudioClip nvsAudioClip = nvsAudioTrack.addClip(audioPath, inPoint, trimIn, trimOut);
            if (nvsAudioClip != null) {
                MeicamAudioClip audioClip = new MeicamAudioClip(nvsAudioClip, audioPath, inPoint, trimIn, trimOut);
                audioClip.outPoint = (inPoint + trimOut - trimIn);
                addAudioClip(audioClip, nvsAudioClip.getIndex());
                return audioClip;
            }
        }
        return null;
    }

    /**
     * 恢复上层数据
     * Add meicam audio clip meicam audio clip.
     *
     * @param nvsAudioClip the nvs audio clip
     * @return the meicam audio clip
     */
    public MeicamAudioClip addMeicamAudioClip(NvsAudioClip nvsAudioClip) {
        if (nvsAudioClip != null) {
            MeicamAudioClip audioClip = new MeicamAudioClip(nvsAudioClip, nvsAudioClip.getFilePath(),
                    nvsAudioClip.getInPoint(), nvsAudioClip.getTrimIn(), nvsAudioClip.getTrimOut());
            audioClip.outPoint = nvsAudioClip.getOutPoint();
            addAudioClip(audioClip, nvsAudioClip.getIndex());
            return audioClip;
        }
        return null;
    }

    /**
     * 添加音频片段
     * Add audio clip
     *
     * @param fromAudioClip the old audioClip  旧的片段
     * @return MeicamAudioClip the audio clip音频片段
     **/
    public MeicamAudioClip addAudioClip(MeicamAudioClip fromAudioClip) {
        NvsAudioTrack nvsAudioTrack = getObject();
        if (nvsAudioTrack != null && fromAudioClip != null) {
            NvsAudioClip nvsAudioClip = nvsAudioTrack.addClip(fromAudioClip.getFilePath(), fromAudioClip.getInPoint(),
                    fromAudioClip.getTrimIn(), fromAudioClip.getTrimOut());
            if (nvsAudioClip != null) {
                MeicamAudioClip newClip = new MeicamAudioClip(nvsAudioClip, nvsAudioClip.getFilePath(), nvsAudioClip.getInPoint(), nvsAudioClip.getTrimIn(), nvsAudioClip.getTrimOut());
                newClip.setObject(nvsAudioClip);
                addAudioClip(newClip, nvsAudioClip.getIndex());
                newClip.copyData(fromAudioClip);
                return newClip;
            }
        }
        return null;
    }

    /**
     * 添加音频片段
     * Add audio clip
     *
     * @param audioClip the meicam audio clip 音频片段
     * @param index     the insert index  插入的index
     */
    private void addAudioClip(MeicamAudioClip audioClip, int index) {
        audioClip.setTrackIndex(getIndex());
        audioClip.setIndex(index);
        audioClip.setAddIndex(index);
        mAudioClipList.add(index, audioClip);

        boolean needDelete;
        for (int i = index + 1; i < mAudioClipList.size(); i++) {
            MeicamAudioClip afterClip = mAudioClipList.get(i);
            if (afterClip.getObject() != null) {
                afterClip.setIndex(afterClip.getObject().getIndex());
                afterClip.updateInAndOutPoint();
                //后边的片段如果在添加的片段出入点内，说明需要删除
                //If the following clip is within the in and out point of the added clip,
                // it means that it needs to be deleted.
                needDelete = audioClip.getInPoint() >= afterClip.getInPoint() &&
                        audioClip.getOutPoint() >= afterClip.getOutPoint();
            } else {
                needDelete = true;
            }
            if (needDelete) {
                //底层可能出现覆盖的情况，如果有就移除上层多余的片段
                //The native layer may be overwritten. If it is, remove the redundant clip.
                mAudioClipList.remove(i);
                i--;
            }
        }
    }

    /**
     * 获取音频片段的数量
     * Get meicam audio clip count
     *
     * @return the count of audio clip 片段数量
     */

    @Override
    public int getClipCount() {
        return mAudioClipList.size();
    }

    /**
     * 获取轨道时长
     * Get audio track duration
     */
    public long getDuration() {
        NvsAudioTrack audioTrack = getObject();
        if (audioTrack != null) {
            return audioTrack.getDuration();
        }
        return 0;
    }

    /**
     * 获取音频片段
     * Get meicam audio clip
     *
     * @param index the index 索引;
     * @return MeicamVideoClip 音频片段
     */
    public MeicamAudioClip getAudioClip(int index) {
        if (index >= 0 && index < mAudioClipList.size()) {
            return mAudioClipList.get(index);
        }
        return null;
    }

    /**
     * 获取音频片段
     * Get meicam audio clip
     *
     * @param inPoint the in point 入点;
     * @return MeicamAudioClip 音频片段
     */
    public MeicamAudioClip getAudioClip(long inPoint) {
        for (MeicamAudioClip item : mAudioClipList) {
            if (inPoint == item.getInPoint()) {
                return item;
            }
        }
        return null;
    }

    /**
     * 删除音频片段
     * Remove meicam audio clip
     *
     * @param index     the index 索引
     * @param keepSpace Whether need keep space or not. 是否需要保持间距
     * @return the remove MeicamAudioClip被删除的片段
     */
    public MeicamAudioClip removeAudioClip(int index, boolean keepSpace) {
        NvsAudioTrack nvsAudioTrack = getObject();
        if (nvsAudioTrack != null && index >= 0 && index < mAudioClipList.size()) {
            NvsAudioClip nvsAudioClip = nvsAudioTrack.getClipByIndex(index);
            if (nvsAudioClip == null) {
                LogUtils.e("removeAudioClip failed!!!");
                return null;
            }
            MeicamAudioClip audioClip = mAudioClipList.get(index);
            if (audioClip.getInPoint() != nvsAudioClip.getInPoint()) {
                LogUtils.e("removeAudioClip failed!!!");
                return null;
            }
            //移除底层数据
            //Remove native data
            if (nvsAudioTrack.removeClip(index, keepSpace)) {
                /*更改上层索引,以及出入点*/
                //Update index，in point and out point
                for (int i = index + 1; i < mAudioClipList.size(); i++) {
                    audioClip = mAudioClipList.get(i);
                    audioClip.setIndex(audioClip.getObject().getIndex());
                    if (!keepSpace) {
                        audioClip.updateInAndOutPoint();
                    }
                }
                return mAudioClipList.remove(index);
            }
        }
        return null;
    }

    public NvsAudioTrack bindToTimeline(NvsTimeline timeline) {
        if (timeline == null) {
            return null;
        }
        NvsAudioTrack track = timeline.appendAudioTrack();
        setObject(track);
        return track;
    }

    @Override
    public LMeicamAudioTrack parseToLocalData() {
        LMeicamAudioTrack local = new LMeicamAudioTrack(getIndex());
        for (MeicamAudioClip clipInfo : mAudioClipList) {
            local.getAudioClipList().add(clipInfo.parseToLocalData());
        }
        setCommondData(local);
        return local;
    }

    @Override
    public void recoverFromLocalData(LMeicamAudioTrack local) {
        setShow(local.isShow());
        setVolume(local.getVolume());
        List<LMeicamAudioClip> audioClipList = local.getAudioClipList();
        if (!CommonUtils.isEmpty(audioClipList)) {
            for (LMeicamAudioClip lAudioClip : audioClipList) {
                MeicamAudioClip audioClip = addAudioClip(lAudioClip.getFilePath(), lAudioClip.getInPoint(),
                        lAudioClip.getTrimIn(), lAudioClip.getTrimOut());
                if (audioClip != null) {
                    audioClip.recoverFromLocalData(lAudioClip);
                }
            }
        }
    }

    @Override
    public void recoverFromTimelineData(NvsObject nvsObject) {
        if (!(nvsObject instanceof NvsAudioTrack)) {
            return;
        }
        setObject((NvsAudioTrack) nvsObject);
        loadData();
    }

    @Override
    void loadData() {
        NvsAudioTrack nvsAudioTrack = getObject();
        setVolume(nvsAudioTrack.getVolumeGain().leftVolume);
        if (mAudioClipList.size() != 0) {
            mAudioClipList.clear();
        }
        int clipCount = nvsAudioTrack.getClipCount();
        for (int i = 0; i < clipCount; i++) {
            NvsAudioClip nvsAudioClip = nvsAudioTrack.getClipByIndex(i);
            MeicamAudioClip audioClip = addMeicamAudioClip(nvsAudioClip);
            if (audioClip != null) {
                audioClip.recoverFromTimelineData(nvsAudioClip);
            }
        }
    }

    /**
     * split audio clip
     * <p>
     * 分割音频clip
     *
     * @param index           the index of clip分割音频的index
     * @param currentPosition current position in timeline timeline上的时间点
     * @return Success or not.true:yes; false:no.
     */
    public boolean splitClip(int index, long currentPosition) {
        NvsAudioTrack object = getObject();
        if (object != null) {
            boolean success = object.splitClip(index, currentPosition);
            /*
             * 分割成功
             * Segmentation success
             * */
            if (success) {
                MeicamAudioClip audioClip = getAudioClip(index);
                audioClip.loadData();
                /*
                 * 分割后的音乐片段也要加在TimelineData里
                 * The split music will also be added to the TimelineData
                 * */
                NvsAudioClip cutAudioClip = object.getClipByIndex(audioClip.getIndex() + 1);
                if (cutAudioClip != null) {
                    MeicamAudioClip meicamAudioInfo = new MeicamAudioClip();
                    meicamAudioInfo.setObject(cutAudioClip);
                    meicamAudioInfo.loadData();
                    meicamAudioInfo.setDrawText(audioClip.getDrawText());
                    meicamAudioInfo.setAudioType(audioClip.getAudioType());
                    addAudioClip(meicamAudioInfo, cutAudioClip.getIndex());
                }
            }
            return success;
        }
        return false;
    }

    /**
     * Merge audio clip.
     * 合并audio clip
     * @param firstIndex the first index
     */
    public void mergeVideoClip(int firstIndex) {
        MeicamAudioClip preClip = getAudioClip(firstIndex);
        MeicamAudioClip nextClip = getAudioClip(firstIndex + 1);
        if (preClip == null || nextClip == null) {
            return;
        }
        long duration = nextClip.getTrimOut() - nextClip.getTrimIn();
        removeAudioClip(firstIndex + 1, false);
        preClip.setTrimOut(preClip.getTrimOut() + duration, true);
        preClip.updateInAndOutPoint();
    }

    /**
     * Copy audio clip
     * <p>
     * 复制音频clip
     *
     * @param inPoint      the in point of copied audio clip from oldClip in timeline
     *                     复制出的音频clip的inPoint点
     * @param oldAudioClip the audio clip 音频
     * @return the MeicamAudioClip
     */
    public MeicamAudioClip copyClip(long inPoint, MeicamAudioClip oldAudioClip) {
        if (oldAudioClip == null) {
            LogUtils.e("old audio clip is null");
            return null;
        }
        MeicamAudioClip meicamAudioClip = addAudioClip(oldAudioClip.getFilePath(), inPoint,
                oldAudioClip.getTrimIn(), oldAudioClip.getTrimOut());
        if (meicamAudioClip != null) {
            meicamAudioClip.copyData(oldAudioClip);
            meicamAudioClip.setOutPoint(inPoint + oldAudioClip.getOutPoint() - oldAudioClip.getInPoint());
        }
        return meicamAudioClip;
    }
}
