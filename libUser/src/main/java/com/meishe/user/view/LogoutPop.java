package com.meishe.user.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.meishe.user.R;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/3/23 17:35
 * @Description :退出登录弹窗 Pop for logout
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class LogoutPop {

    public static void show(Context context, View view, String content, View.OnClickListener clickListener){
        View rootView = LayoutInflater.from(context).inflate(R.layout.view_pop_user_logout, null);
        TextView contentView = rootView.findViewById(R.id.content);
        contentView.setText(content);
        final PopupWindow popupWindow = new PopupWindow(rootView,
                context.getResources().getDimensionPixelOffset(R.dimen.dp_px_297),
                context.getResources().getDimensionPixelOffset(R.dimen.dp_px_84));
        popupWindow.showAsDropDown(view, view.getWidth(), 0);
        popupWindow.setFocusable(true);
        popupWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        popupWindow.setOutsideTouchable(true);
        popupWindow.update();
        rootView.setOnClickListener(clickListener);
        rootView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (clickListener != null) {
                    clickListener.onClick(v);
                }
                popupWindow.dismiss();
            }
        });
    }
}
