package com.meishe.myvideo.fragment;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.bean.MediaData;
import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseFragment;
import com.meishe.base.utils.ImageLoader;
import com.meishe.base.utils.SizeUtils;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.observer.AssetObserver;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.activity.MaterialSingleSelectActivity;
import com.meishe.myvideo.interfaces.OnAssetsClickedListener;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;

import java.util.ArrayList;
import java.util.List;

import static com.meishe.logic.constant.PagerConstants.MEDIA_TYPE;
import static com.meishe.logic.constant.PagerConstants.NEXT_PAGE_ACTION;

/**
 * The type Sticker custom fragment.
 * 贴纸自定义片段
 *
 * <AUTHOR>
 * @Description 自定义贴纸
 */
public class StickerCustomFragment extends BaseFragment {
    /**
     * 两次点击的间隔
     * The interval between two clicks
     */
    private long duration = 500;
    private long lastClickTime = 0;
    private StickerAdapter mAdapter;
    private int mStickerWidth;
    private int mStickerHeight;
    private AssetObserver mAssetObserver;
    private OnAssetsClickedListener mOnAssetsClickedListener;

    public StickerCustomFragment() {
    }

    public StickerCustomFragment setOnAssetsClickedListener(OnAssetsClickedListener listener) {
        this.mOnAssetsClickedListener = listener;
        return this;
    }

    public static StickerCustomFragment create(OnAssetsClickedListener listener){
        return new StickerCustomFragment().setOnAssetsClickedListener(listener);
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_custom_sticker;
    }

    @Override
    protected void onLazyLoad() {
        initData();
    }

    @Override
    protected void initView(View rootView) {
        RecyclerView rvStickerList = rootView.findViewById(R.id.recyclerView);
        GridLayoutManager gridLayoutManager = new GridLayoutManager(getContext(), 4);
        rvStickerList.setLayoutManager(gridLayoutManager);
        mAdapter = new StickerAdapter();
        rvStickerList.setAdapter(mAdapter);
        rvStickerList.addItemDecoration(new ItemDecoration(SizeUtils.dp2px(7), SizeUtils.dp2px(12), SizeUtils.dp2px(7), 0));
        initListener();
    }

    private void initListener() {
        AssetsManager.get().registerAssetObserver(mAssetObserver = new AssetObserver() {
            @Override
            public void onAssetAdd(IBaseInfo assetInfo) {
                if (assetInfo != null && assetInfo.getType() == AssetInfo.ASSET_CUSTOM_STICKER) {
                    mAdapter.addData(assetInfo);
                }
            }
        });
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                //这里添加一个防连击的操作
                //Add an anti-combo operation here
                long currentTime = System.currentTimeMillis();
                if (currentTime - lastClickTime > duration) {
                    if (position == 0) {
                        //添加
                        Bundle bundle = new Bundle();
                        bundle.putInt(MEDIA_TYPE, MediaData.TYPE_PHOTO);
                        bundle.putString(NEXT_PAGE_ACTION, "com.meishe.video.CustomAnimate");
                        AppManager.getInstance().jumpActivity(getContext(), MaterialSingleSelectActivity.class, bundle);
                    } else {
                        IBaseInfo baseInfo = mAdapter.getItem(position);
                        if (baseInfo != null) {
                            if (mOnAssetsClickedListener != null) {
                                mOnAssetsClickedListener.onItemClicked(baseInfo);
                            }
                        }
                    }
                }
                lastClickTime = currentTime;
            }
        });
    }

    /**
     * Init data.
     * 初始化数据
     */
    @Override
    public void initData() {
        final List<IBaseInfo> stickerDataList = new ArrayList<>();
        IBaseInfo stickerInfo = new AssetInfo();
        stickerInfo.setName(getString(R.string.add));
        stickerInfo.setCoverId(R.mipmap.ic_add);
        stickerDataList.add(stickerInfo);
        AssetsManager.get().getCombinedLocalAssetList(AssetInfo.ASSET_CUSTOM_STICKER, new AssetsManager.AssetCallback() {
            @Override
            public void onSuccess(List<AssetInfo> assetInfoList) {
                if (assetInfoList != null && assetInfoList.size() > 0) {
                    stickerDataList.addAll(assetInfoList);
                }
                mAdapter.setNewData(stickerDataList);
            }

            @Override
            public void onFailure() {
                mAdapter.setNewData(stickerDataList);
            }
        });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        AssetsManager.get().unregisterAssetObserver(mAssetObserver);
    }

    private static class StickerAdapter extends BaseQuickAdapter<IBaseInfo, BaseViewHolder> {

        private StickerAdapter() {
            super(R.layout.item_custom_sticker);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, IBaseInfo item) {
            ImageView imageAdd = helper.getView(R.id.image_add);
            ImageView ivCover = helper.getView(R.id.iv_cover);
            if (mContext.getResources().getString(R.string.add).equals(item.getName())) {
                imageAdd.setVisibility(View.VISIBLE);
                ivCover.setVisibility(View.GONE);
            } else {
                if (ivCover.getVisibility() != View.VISIBLE) {
                    imageAdd.setVisibility(View.GONE);
                    ivCover.setVisibility(View.VISIBLE);
                }
                ImageLoader.loadUrl(mContext, "file://" + item.getCoverPath(), ivCover);
            }
        }
    }
}
