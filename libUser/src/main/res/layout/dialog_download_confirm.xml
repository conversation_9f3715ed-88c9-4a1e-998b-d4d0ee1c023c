<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp_px_720"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@drawable/bg_round_corners_solid_white_2">

    <TextView
        android:paddingLeft="@dimen/dp_px_87"
        android:paddingStart="@dimen/dp_px_87"
        android:paddingEnd="@dimen/dp_px_87"
        android:paddingRight="@dimen/dp_px_87"
        android:layout_marginTop="@dimen/dp_px_100"
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/hint_cloud_download_pop_top"
        android:textSize="@dimen/sp_px_39"
        android:textColor="@color/black"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>


    <TextView
        android:id="@+id/tv_confirm"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_90"
        android:layout_marginLeft="@dimen/dp_px_120"
        android:layout_marginRight="@dimen/dp_px_120"
        android:background="@drawable/user_selector_cloud_button_bg"
        android:gravity="center"
        android:text="@string/download_replace"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="@dimen/dp_px_75"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_36" />

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/download_new"
        android:textColor="@color/color_ff7c7c7c"
        android:layout_marginTop="@dimen/dp_px_45"
        android:layout_marginBottom="@dimen/dp_px_66"
        android:textSize="@dimen/sp_px_36"
        app:layout_constraintTop_toBottomOf="@+id/tv_confirm"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>