package com.meishe.myvideo.fragment;

import android.view.View;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.tabs.TabLayout;
import com.meishe.base.adapter.CommonFragmentAdapter;
import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.model.BaseMvpFragment;
import com.meishe.base.view.CustomViewPager;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.bean.CaptionFontInfo;
import com.meishe.myvideo.fragment.adapter.CaptionFontAdapter;
import com.meishe.myvideo.fragment.iview.CaptionStyleView;
import com.meishe.myvideo.fragment.presenter.CaptionStylePresenter;
import com.meishe.myvideo.manager.MenuDataManager;
import com.meishe.third.adpater.BaseQuickAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * T字幕样式页面
 * The caption style fragment
 */
public class CaptionStyleFragment extends BaseMvpFragment<CaptionStylePresenter> implements CaptionStyleView, View.OnClickListener {

    private TextView mTvFontBold;
    private TextView mTvFontItalics;
    private TextView mTvFontShadow;

    private TabLayout mTabLayout;
    private CustomViewPager mViewPager;
    private List<Fragment> mFragmentList;

    private CaptionTextFragment mTextFragment;
    private CaptionOutlineFragment mOutlineFragment;
    private CaptionBackgroundFragment mBackgroundFragment;
    private CaptionLetterSpacingFragment mLetterSpacingFragment;
    private CaptionPositionFragment mPositionFragment;
    private CaptionFontAdapter mAdapter;
    private CaptionStyleEventListener mEventListener;
    private MeicamCaptionClip meicamCaptionClip;
    private long mKeyFrameAtTime = -1;

    public CaptionStyleFragment() {
    }

    public CaptionStyleFragment(MeicamCaptionClip captionClip, long keyFrameAtTime, CaptionStyleEventListener listener) {
        this.mEventListener = listener;
        mKeyFrameAtTime = keyFrameAtTime;
        mPresenter = new CaptionStylePresenter(captionClip);
        meicamCaptionClip = captionClip;
    }

    /**
     *这里重写，不使用模板自动生成的Presenter了.
     *Rewrite here, do not use the Presenter generated automatically by the template
     * @return The presenter
     */
    @Override
    protected CaptionStylePresenter createPresenter() {
        return mPresenter;
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_caption_style;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        RecyclerView rvFontList = rootView.findViewById(R.id.rv_font_list);
        mTvFontBold = rootView.findViewById(R.id.tv_font_bold);
        mTvFontItalics = rootView.findViewById(R.id.tv_font_italics);
        mTvFontShadow = rootView.findViewById(R.id.tv_font_shadow);
        mTabLayout = rootView.findViewById(R.id.tab_layout);
        mViewPager = rootView.findViewById(R.id.viewPager);
        mViewPager.setScanScroll(false);
        mViewPager.setOffscreenPageLimit(5);
        initFragment();
        mAdapter = new CaptionFontAdapter(getResources().getDimension(R.dimen.sp_px_30), (int) getResources().getDimension(R.dimen.dp_px_195));
        rvFontList.setAdapter(mAdapter);
        rvFontList.setLayoutManager(new LinearLayoutManagerWrapper(getContext(), RecyclerView.HORIZONTAL, false));
        rvFontList.addItemDecoration(new ItemDecoration(10, 10));
        initListener();

    }

    private void initFragment() {
        if (mPresenter != null) {
            mFragmentList = new ArrayList<>();
            MeicamCaptionClip captionClip = mPresenter.getCaptionClip();
            mFragmentList.add(mTextFragment = CaptionTextFragment.create(captionClip));
            mFragmentList.add(mOutlineFragment = CaptionOutlineFragment.create(captionClip));
            mFragmentList.add(mBackgroundFragment = CaptionBackgroundFragment.create(captionClip));
            mFragmentList.add(mLetterSpacingFragment = CaptionLetterSpacingFragment.create(captionClip, mEventListener));
            mFragmentList.add(mPositionFragment = CaptionPositionFragment.create(captionClip, mKeyFrameAtTime, mEventListener));
            FragmentPagerAdapter adapter = new CommonFragmentAdapter(getChildFragmentManager(), mFragmentList);
            mViewPager.setAdapter(adapter);
            mTabLayout.setupWithViewPager(mViewPager);
            final String[] tabs = getResources().getStringArray(R.array.menu_tab_sub_caption);
            mTabLayout.removeAllTabs();
            for (String tab : tabs) {
                mTabLayout.addTab(mTabLayout.newTab().setText(tab));
            }
        }
    }

    private void initListener() {
        mTvFontBold.setOnClickListener(this);
        mTvFontItalics.setOnClickListener(this);
        mTvFontShadow.setOnClickListener(this);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                mAdapter.selected(position);
                IBaseInfo item = mAdapter.getItem(position);
                if (item instanceof CaptionFontInfo) {
                    if (mPresenter != null) {
                        mPresenter.setCaptionFont(item.getAssetPath());
                    }
                }
            }
        });
    }

    @Override
    protected void initData() {
        if (getContext() != null) {
            mAdapter.setNewData(MenuDataManager.getCaptionFontList(getContext()));
        }
        updateCaptionStyle(meicamCaptionClip);
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.tv_font_bold) {
            mTvFontBold.setSelected(!mTvFontBold.isSelected());
            if (mPresenter != null) {
                mPresenter.setCaptionBold(mTvFontBold.isSelected());
            }
        } else if (id == R.id.tv_font_italics) {
            mTvFontItalics.setSelected(!mTvFontItalics.isSelected());
            if (mPresenter != null) {
                mPresenter.setCaptionItalics(mTvFontItalics.isSelected());
            }
        } else if (id == R.id.tv_font_shadow) {
            mTvFontShadow.setSelected(!mTvFontShadow.isSelected());
            if (mPresenter != null) {
                mPresenter.setCaptionShadow(mTvFontShadow.isSelected());
            }
        }
    }

    /**
     * 更新字幕片段
     * Update caption clip
     *
     * @param captionClip the new caption clip
     */
    public void updateCaptionClip(MeicamCaptionClip captionClip) {
        if (mPresenter != null) {
            mPresenter.updateCaptionClip(captionClip);
        }
        if (isInitView) {
            mTextFragment.updateCaptionClip(captionClip);
            mOutlineFragment.updateCaptionClip(captionClip);
            mBackgroundFragment.updateCaptionClip(captionClip);
            mLetterSpacingFragment.updateCaptionClip(captionClip);
            mPositionFragment.updateCaptionClip(captionClip);
            updateCaptionStyle(captionClip);
        }
    }

    private void updateCaptionStyle(MeicamCaptionClip captionClip) {
        if (mAdapter == null || mTvFontBold == null || captionClip == null) {
            return;
        }
        mTvFontBold.setSelected(captionClip.isBold());
        mTvFontItalics.setSelected(captionClip.isItalic());
        mTvFontShadow.setSelected(captionClip.isShadow());
        mAdapter.selected(captionClip.getFont(), captionClip.getFontPath());
    }

    public interface CaptionStyleEventListener {
        /**
         * 字幕改变时的回调
         * On caption local changed.
         */
        void onCaptionLocalChanged();
    }
}
