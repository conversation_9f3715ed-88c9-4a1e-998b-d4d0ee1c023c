package com.meishe.user.manager;

import android.text.TextUtils;

import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FileIOUtils;
import com.meishe.base.utils.GsonUtils;
import com.meishe.draft.DraftManager;
import com.meishe.draft.data.DraftData;
import com.meishe.engine.EngineNetApi;
import com.meishe.engine.asset.bean.TemplateUploadParam;
import com.meishe.engine.bean.template.ExportTemplateDescInfo;
import com.meishe.engine.util.CompileXmlCreator;
import com.meishe.engine.util.PathUtils;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;
import com.meishe.user.bean.VideoCreateResponse;
import com.meishe.user.manager.ali.ALiHelper;
import com.meishe.user.manager.ali.UploadBean;
import com.meishe.user.manager.ali.Uploader;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/7/11 17:03
 * @Description :云合成工具 The compiler utils by cloud
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CloudCompiler {

   private int sizeLevel;
   private String title;
   private int fps;
   private String token;
   private CompileListener listener;
   private String mProjectId;
   private Uploader mUploader;
   private ALiHelper mUploadHelper;

   /**
    * Sets title.
    *
    * @param title the title
    * @return the title
    */
   public CloudCompiler setTitle(String title) {
      this.title = title;
      return this;
   }

   /**
    * Sets size level.
    *
    * @param sizeLevel the size level
    * @return the size level
    */
   public CloudCompiler setSizeLevel(int sizeLevel) {
      this.sizeLevel = sizeLevel;
      return this;
   }

   public CloudCompiler setFps(int fps) {
      this.fps = fps;
      return this;
   }

   /**
    * Sets listener.
    *
    * @param listener the listener
    * @return the listener
    */
   public CloudCompiler setListener(CompileListener listener) {
      this.listener = listener;
      return this;
   }

   /**
    * Compile draft boolean.
    *
    * @param draftData the draft data
    * @return the boolean
    */
   public boolean compileDraft(DraftData draftData, String tag){
      IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
      if (userPlugin == null) {
         return false;
      }
      token = userPlugin.getToken();
      if (TextUtils.isEmpty(token)) {
         return false;
      }
      mProjectId = tag;
      startCompile(draftData);
      return true;
   }

   private void startCompile(DraftData draftData) {
      mUploadHelper = ALiHelper.create();
      mUploadHelper.setUuid(mProjectId);
      if (draftData.isOnlyCloud()) {
         uploadNow(mUploadHelper, draftData, token);
      } else {
         mUploadHelper.tryToUploadDraft(draftData, token);
      }
      mUploadHelper.setOnUploadListener(new ALiHelper.UploadListener() {
         @Override
         public void onSuccess(DraftData data, String userName) {
            if (data == null) {
               if (listener != null) {
                  listener.onError(mProjectId);
               }
               return;
            }
            DraftData.CloudInfo cloudInfo = data.getCloudInfo();
            if (cloudInfo == null) {
               if (listener != null) {
                  listener.onError(mProjectId);
               }
               return;
            }
            String infoPath = cloudInfo.infoPath;
            if (TextUtils.isEmpty(infoPath)) {
               if (listener != null) {
                  listener.onError(mProjectId);
               }
               return;
            }
            String jsonData = FileIOUtils.readFile2String(infoPath, "utf-8");
            ExportTemplateDescInfo descInfo = GsonUtils.fromJson(jsonData, ExportTemplateDescInfo.class);
            String cover = descInfo.getCover();
            List<ExportTemplateDescInfo.FootageInfo> footageInfo = descInfo.getFootageInfos().get(0).getInfos();
            List<ExportTemplateDescInfo.InnerAsset> assetsInfo = null;
            List<ExportTemplateDescInfo.InnerAssetWrapper> innerAssets = descInfo.getInnerAssets();
            if (!CommonUtils.isEmpty(innerAssets)) {
               assetsInfo = innerAssets.get(0).getAssets();
            }
            String defaultAspectRatio = descInfo.getDefaultAspectRatio();
            if (TextUtils.isEmpty(defaultAspectRatio)) {
               defaultAspectRatio = "3:4";
            }

            DraftManager.getInstance().updateCloudDraft(mProjectId,  data.getCloudInfo().projectId, data.getFileName(), data.getCoverPath(), data.getCloudInfo().templatePath,
                    data.getCloudInfo().infoPath, data.getCloudInfo().cloudToLocalMapInfo, data.getDurationLong(), data.getFileSizeLong(), String.valueOf(data.getLastModifyTimeLong()), null);


            String fillData = CompileXmlCreator.fillData(defaultAspectRatio.replaceAll("v", ":"), String.valueOf(sizeLevel), cloudInfo.templateUrl, cloudInfo.uuid, String.valueOf(fps), footageInfo, assetsInfo);
            if (TextUtils.isEmpty(fillData)) {
               if (listener != null) {
                  listener.onError(mProjectId);
               }
               return;
            }
            String cloudCompileInfo = PathUtils.getCloudCompileTempFolder(cloudInfo.uuid) + File.separator + "compile.xml";
            boolean success = FileIOUtils.writeFileFromString(cloudCompileInfo, fillData);
            if (!success) {
               if (listener != null) {
                  listener.onError(mProjectId);
               }
               return;
            }
            List<UploadBean> uploadData = new ArrayList<>();
            UploadBean uploadBean = new UploadBean(0, UploadBean.KEY_PROJECT_XML, cloudCompileInfo);
            uploadBean.uuid = mProjectId;
            uploadBean.setUploadModule("multi_project");
            uploadData.add(uploadBean);
            mUploader = new Uploader();
            mUploader.setUploadListener(new UploadXmlCallBack(cover, mProjectId));
            mUploader.startUpload(token, uploadData);
         }

         @Override
         public void onFailed(String message, int code) {
            if (listener != null) {
               listener.onError(mProjectId);
            }
         }

         @Override
         public void onProgress(long progress, long total) {
            if (listener != null) {
               listener.onProgress(mProjectId,  progress * 0.95F);
            }
         }

         @Override
         public void onStart(DraftData data) {

         }
      });
   }

   private class UploadXmlCallBack implements Uploader.UploadListener{
      private String coverPath;
      private String projectId;

      /**
       * Instantiates a new Upload xml call back.
       *
       * @param cover     the cover
       * @param projectId the project id
       */
      public UploadXmlCallBack(String cover, String projectId) {
         coverPath = cover;
         this.projectId = projectId;
      }

      @Override
      public void onSuccess(List<UploadBean> uploadBean, String projectId) {
         createCompileJob(token, projectId, coverPath, uploadBean.get(0).remoteInfo.url);
      }

      @Override
      public void onFailed(String message, int code) {

      }

      @Override
      public void onProgress(long progress, long total) {
         if (listener != null) {
            listener.onProgress(projectId, progress * 0.05F + 95);
         }
      }
   }

   private void createCompileJob(String token, String projectId, String coverUrl, String projectUrl) {
      EngineNetApi.CompileJobParam compileJobParam = new EngineNetApi.CompileJobParam();
      compileJobParam.coverUrl = coverUrl;
      compileJobParam.projectUrl = projectUrl;
      compileJobParam.projectId = projectId;
      compileJobParam.sizeLevel = sizeLevel;
      compileJobParam.title = title;
      EngineNetApi.createCompileJob(token, compileJobParam, new RequestCallback<VideoCreateResponse>() {
         @Override
         public void onSuccess(BaseResponse<VideoCreateResponse> response) {
            VideoCreateResponse data = response.getData();
            if (data == null) {
               if (listener != null) {
                  listener.onError(getProjectId());
               }
               return;
            }
            if (listener != null) {
               listener.onSuccess(getProjectId(), data.getJobId());
            }
         }

         @Override
         public void onError(BaseResponse<VideoCreateResponse> response) {
            if (listener != null) {
               listener.onError(getProjectId());
            }
         }
      });
   }

   private String getProjectId() {
      return mProjectId;
   }

   private void uploadNow(ALiHelper aLiHelper, DraftData draftData, String token) {
      TemplateUploadParam templateUploadParam = new TemplateUploadParam();
      DraftData.CloudInfo cloudInfo = draftData.getCloudInfo();
      templateUploadParam.materialFile = new File(cloudInfo.templatePath);
      templateUploadParam.coverFile = new File(draftData.getCoverPath());
      templateUploadParam.templateDescFilePath = cloudInfo.infoPath;
      aLiHelper.tryToUploadResource(templateUploadParam, draftData, token, true);
   }

   /**
    * Cancel task.
    * 取消任务
    */
   public void cancelTask(){
      if (mUploader != null) {
         mUploader.cancelAllNetTask();
      }
      if (mUploadHelper != null) {
         mUploadHelper.cancelAllNetTask();
      }
   }

   /**
    * The interface Compile listener.
    */
   public interface CompileListener{
      /**
       * On progress.
       *
       * @param projectId the project id
       * @param progress  the progress
       */
      void onProgress(String projectId, float progress);

      /**
       * On success.
       *
       * @param projectId the project id
       */
      void onSuccess(String projectId, String jobId);

      /**
       * On error.
       *
       * @param projectId the project id
       */
      void onError(String projectId);
   }
}
