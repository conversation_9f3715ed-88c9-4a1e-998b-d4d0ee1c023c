<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_left"
        android:layout_width="@dimen/dp_px_198"
        android:layout_height="match_parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_right"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_toRightOf="@+id/recycler_left"
        android:layout_above="@+id/ll_bottom_view"/>

    <LinearLayout
        android:id="@+id/ll_bottom_view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_132"
        android:layout_alignParentBottom="true"
        android:background="@color/black"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/activity_cut_export_template_cancel"
            android:textColor="@color/ffc1c1c1"
            android:textSize="@dimen/sp_px_36" />

        <View
            android:layout_width="@dimen/dp_px_3"
            android:layout_height="@dimen/dp_px_39"
            android:layout_gravity="center_vertical"
            android:background="@color/white_707" />

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/activity_cut_export_template_confirm"
            android:textColor="@color/red_fc2b55"
            android:textSize="@dimen/sp_px_36" />

    </LinearLayout>

</RelativeLayout>