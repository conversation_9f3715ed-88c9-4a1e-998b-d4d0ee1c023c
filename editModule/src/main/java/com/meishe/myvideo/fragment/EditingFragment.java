package com.meishe.myvideo.fragment;

import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;
import com.meishe.base.adapter.CommonFragmentAdapter;
import com.meishe.base.model.BaseMvpFragment;
import com.meishe.base.utils.CommonUtils;
import com.meishe.myvideo.R;
import com.meishe.myvideo.event.MessageEvent;
import com.meishe.myvideo.fragment.iview.EditingView;
import com.meishe.myvideo.fragment.presenter.EditingPresenter;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiH<PERSON><PERSON>hou
 * @CreateDate :2020/12/2 20:42
 * @Description :剪辑页面 The page of Edit
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class EditingFragment extends BaseMvpFragment<EditingPresenter> implements EditingView, View.OnClickListener {

    private static final String BOTTOM_LAYOUT_ID = "bottomLayoutId";
    private TextView mTvDraftManager;
    private TabLayout mTabLayout;
    private ViewPager mViewPager;
    private final List<Fragment> mFragmentList = new ArrayList<>();
    private View mRefreshButton;
    private boolean isEditState;
    /**
     * 底部图id，用于Activity的弹窗
     * Bottom figure ID, pop-up window for activity
     */
    private int mBottomLayoutId;
    /**
     * 更新草稿管理
     * Update draft management
     */
    public static final int MESSAGE_UPDATE_DRAFT_MANAGER = 0;

    public EditingFragment() {
    }

    public static EditingFragment create(int id) {
        EditingFragment editingFragment = new EditingFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(BOTTOM_LAYOUT_ID, id);
        editingFragment.setArguments(bundle);
        return editingFragment;
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_editing;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EventBus.getDefault().register(this);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

    @Override
    protected void initView(View rootView) {
        mTvDraftManager = rootView.findViewById(R.id.tv_draft_manager);
        mTabLayout = rootView.findViewById(R.id.tab_layout);
        mViewPager = rootView.findViewById(R.id.vp_pager);
        mRefreshButton = rootView.findViewById(R.id.ib_refresh);
        initViewPager();
        initListener();
    }

    private void initViewPager() {
        mFragmentList.add(DraftFragment.create());
        String[] tabs = getResources().getStringArray(R.array.tab_draft_c);
        FragmentPagerAdapter adapter = new CommonFragmentAdapter(getChildFragmentManager(), mFragmentList);
        mViewPager.setAdapter(adapter);
        mViewPager.setOffscreenPageLimit(1);
        mTabLayout.setupWithViewPager(mViewPager);
        mTabLayout.removeAllTabs();
        for (String tab : tabs) {
            mTabLayout.addTab(mTabLayout.newTab().setText(tab));
        }
    }

    private void initListener() {
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                mRefreshButton.setVisibility(position == 0 ? View.GONE : View.VISIBLE);
                mTvDraftManager.setVisibility(position == 2 ? View.GONE : View.VISIBLE);
                exitManagerState();

            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
        mTvDraftManager.setOnClickListener(this);
        mRefreshButton.setOnClickListener(this);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    @Override
    protected void initData() {
        Bundle arguments = getArguments();
        if (arguments != null) {
            mBottomLayoutId = arguments.getInt(BOTTOM_LAYOUT_ID, 0);
        }
    }

    /**
     * Refresh data.Reflection call, do not delete it.
     * 刷新数据, 反射调用，勿删
     */
    public void refreshData() {
        for (Fragment fragment : mFragmentList) {
            if (fragment instanceof DraftFragment) {
                ((DraftFragment) fragment).refreshData();
            }
        }
    }


    /**
     * Refresh data.
     * 刷新数据
     * @param index the index 页面的index the index of fragment
     */
    public void refreshData(int index) {
        if (CommonUtils.isIndexAvailable(index, mFragmentList)) {
            Fragment fragment = mFragmentList.get(index);
            if (fragment instanceof DraftFragment) {
                ((DraftFragment) fragment).refreshData();
            }
        }
    }

    private void goManagerState() {
        isEditState = true;
        mTvDraftManager.setText(getResources().getString(R.string.draft_manage_cancel));
        int currentItem = mViewPager.getCurrentItem();
        if (CommonUtils.isIndexAvailable(currentItem, mFragmentList)) {
            Fragment fragment = mFragmentList.get(currentItem);
            if (fragment instanceof DraftFragment) {
                ((DraftFragment) fragment).goManagerState(mBottomLayoutId);
            }
        }
    }


    private void exitManagerState() {
        isEditState = false;
        mTvDraftManager.setText(getResources().getString(R.string.draft_manage));
        for (Fragment fragment : mFragmentList) {
            if (fragment instanceof DraftFragment) {
                ((DraftFragment) fragment).exitManagerState();
            }
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.tv_draft_manager) {
            if (isEditState) {
                exitManagerState();
            } else {
                goManagerState();
            }
        } else if (id == R.id.ib_refresh) {
            refreshData(mViewPager.getCurrentItem());
        }
    }

    /**
     * On message event.
     * 消息事件
     *
     * @param event the event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(MessageEvent event) {
        if (event.getEventType() == MESSAGE_UPDATE_DRAFT_MANAGER) {
            mTvDraftManager.setText(getResources().getString(R.string.draft_manage));
        }
    }

    /**
     * On message event.
     * 消息事件
     *
     * @param event the event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(Integer event) {
        if (event == MESSAGE_UPDATE_DRAFT_MANAGER) {
            mTvDraftManager.setText(getResources().getString(R.string.draft_manage));
        }
    }

}
