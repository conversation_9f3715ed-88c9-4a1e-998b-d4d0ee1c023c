package com.czc.cutsame.iview;

import android.graphics.Bitmap;

import com.meishe.base.model.IBaseView;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/5/13 16:07
 * @Description :导出模板视图接口 the interface of export template
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface IExportTemplate extends IBaseView {
    /**
     * 抓图成功
     * On image grabbed arrived
     *
     * @param bitmap the bitmap 图片数据
     */
    void imageGrabbedArrived(Bitmap bitmap);

    /**
     * 导出开始
     * On compile start
     */
    void onCompileStart();

    /**
     * 导出中
     * On compile progress
     *
     * @param progress the progress 进度
     */
    void onCompileProgress(int progress);

    /**
     * 导出完成
     * On compile complete
     *
     * @param success true success 成功, false failed 失败
     */
    void onCompileComplete(boolean success);

    /**
     * 是否是活动的
     * Is active or not
     *
     * @return true：yes；false：no
     */
    boolean isActive();
}
