package com.meishe.engine.bean;

import android.graphics.PointF;
import android.text.TextUtils;

import com.meicam.sdk.NvsCaption;
import com.meicam.sdk.NvsClipCaption;
import com.meicam.sdk.NvsColor;
import com.meicam.sdk.NvsObject;
import com.meicam.sdk.NvsTimelineCaption;
import com.meicam.sdk.NvsTrackCaption;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.interf.IKeyFrameProcessor;
import com.meishe.engine.local.LMeicamCaptionClip;
import com.meishe.engine.local.LMeicamKeyFrame;
import com.meishe.engine.util.ColorUtil;
import com.meishe.engine.util.DeepCopyUtil;
import com.meishe.engine.util.gson.GsonContext;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2021/5/20 10:30
 * @Description: 字幕
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class MeicamCaptionClip extends ClipInfo<NvsCaption> implements Cloneable, Serializable, TimelineDataParserAdapter<LMeicamCaptionClip>, IKeyFrameProcessor<NvsCaption> {
    /**
     * The constant CAPTION_ALIGN_LEFT.
     * 常数CAPTION_ALIGN_LEFT
     */
    public static final int CAPTION_ALIGN_LEFT = 0;
    /**
     * The constant CAPTION_ALIGN_HORIZ_CENTER.
     * 常数CAPTION_ALIGN_HORIZ_CENTER
     */
    public static final int CAPTION_ALIGN_HORIZ_CENTER = 1;
    /**
     * The constant CAPTION_ALIGN_RIGHT.
     * 常数CAPTION_ALIGN_RIGHT
     */
    public static final int CAPTION_ALIGN_RIGHT = 2;
    /**
     * The constant CAPTION_ALIGN_TOP.
     * 常数CAPTION_ALIGN_TOP
     */
    public static final int CAPTION_ALIGN_TOP = 3;
    /**
     * The constant CAPTION_ALIGN_VERT_CENTER.
     * 常数CAPTION_ALIGN_VERT_CENTER
     */
    public static final int CAPTION_ALIGN_VERT_CENTER = 4;
    /**
     * The constant CAPTION_ALIGN_BOTTOM.
     * 常数CAPTION_ALIGN_BOTTOM
     */
    public static final int CAPTION_ALIGN_BOTTOM = 5;

    public static final String CAPTION_TYPE_NORMAL = "normal";

    public static final String CAPTION_TYPE_MODULAR = "modular";

    public static final int LETTER_SPACING_TYPE_PERCENTAGE = 0;
    public static final int LETTER_SPACING_TYPE_ABSOLUTE = 1;

    /**
     * Caption content
     * 字幕内容
     */
    private String text;
    /**
     * caption style
     * 字幕样式  styleId是常规字幕才使用的字段，目前美映里使用的模块字幕此字段无效
     */
    private String styleId;
    /**
     * scale X
     * 缩放 X
     */
    private float scaleX = 1;

    /**
     * 用于缩放的临时锚点数据
     */
    private transient PointF anchorForScale;
    /**
     * scale Y
     * 缩放 Y
     */
    private float scaleY = 1;
    /**
     * Rotation Angle
     * 旋转角度
     */
    private float rotation = 0;
    /**
     * Horizontal translation
     * 水平方向平移
     */
    private float translationX = 0;
    /**
     * vertical translation
     * 竖直方向平移
     */
    private float translationY = 0;
    /**
     * font
     * 字体
     */
    private String font = "";

    /**
     * font path
     * 字体 路径
     */
    private String fontPath = "";
    /**
     * Text color
     * 文字颜色
     */
    private float[] textColor = {1f, 1f, 1f, 1f};
    /**
     * Whether the bold
     * 是否加粗
     */
    private boolean bold = false;
    /**
     * Whether in italics
     * 是否斜体
     */
    private boolean italic = false;
    /**
     * Is there a shadow
     * 是否有阴影
     */
    private boolean shadow = false;
    /**
     * Whether to turn Stroke on true, false off
     * 是否开启描边 true开启，false 不开启
     */
    private boolean outline = false;
    /**
     * outline color
     * 描边颜色
     */
    private float[] outlineColor = new float[4];
    /**
     * The background color
     * 背景颜色
     */
    private float[] backgroundColor = new float[4];
    /**
     * Stroke Width The default stroke is 5 and ranges from 0 to 10
     * 描边宽度 描边默认是5，范围为0-10
     */
    private float outlineWidth = 5;
    /**
     * The radius of the rounded corner of the background box of the subtitle text
     * 字幕文本的背景框的圆角半径
     */
    private float backgroundRadius = 0;

    /**
     * Letter Spacing
     * 字间距
     */
    private float letterSpacing = 0;

    /**
     * Letter spacing type. Default is absolute.
     * 字间距类型，默认绝对值类型
     */
    private int letterSpacingType = NvsCaption.LETTER_SPACING_TYPE_ABSOLUTE;

    /**
     * line Spacing
     * 行间距
     */
    private float lineSpacing;
    /**
     * Horizontal alignment of subtitles
     * 字幕水平对齐方式
     * <p>
     * TEXT_ALIGNMENT_LEFT = 0
     * 居左对齐 默认值
     * TEXT_ALIGNMENT_CENTER = 1
     * 居中对齐
     * TEXT_ALIGNMENT_RIGHT = 2
     * 居右对齐
     */
    private int textAlignment;
    /**
     * richWord Uuid
     * 花字Uuid
     */
    private String richWordUuid;
    /**
     * bubble Uuid
     * 气泡Uuid
     */
    private String bubbleUuid;
    /**
     * combination Animation Uuid
     * 组合动画Uuid
     */
    private String combinationAnimationUuid;
    /**
     * In the animation uuid
     * 入场动画Uuid
     */
    private String marchInAnimationUuid;
    /**
     * Out the animation uuid
     * 出场动画Uuid
     */
    private String marchOutAnimationUuid;
    /**
     * combination Animation Duration
     * 组合动画时长
     */
    private int combinationAnimationDuration;
    /**
     * In Animation Duration
     * 入场动画时长
     */
    private int marchInAnimationDuration;
    /**
     * Out Animation Duration
     * 出场动画时长
     */
    private int marchOutAnimationDuration;
    /**
     * Subtitle Type 0: Normal Subtitle 1 AI Subtitle (iOS is defined as the same)
     * 字幕类型 0：普通字幕 1 AI字幕 （ios 这样定义的保持一致）
     */
    private int operationType;

    /**
     * public static final int ROLE_IN_THEME_GENERAL = 0;
     * public static final int ROLE_IN_THEME_TITLE = 1;
     * public static final int ROLE_IN_THEME_TRAILER = 2;
     * <p>
     * Subtitles in the theme. For the time being, use this field for subtitles.
     * 主题中的字幕，暂时先用这个字段表示，
     */
    private int themeType;
    /**
     * 在最外层的偏移量
     * Offset at outermost layer
     */
    private float[] mTranslationInOutTimeline;

    /**
     * 在最外层的缩放量
     * Scale amount at outermost layer
     */
    private float mScaleInOutTimeline = 1;

    /**
     * 字幕类型，包括传统字幕和模块字幕
     * Subtitle types, including traditional subtitles and module subtitles
     *
     */
    private String captionType = CAPTION_TYPE_MODULAR;

    /**
     * 字体大小
     * The font size
     */
    private float fontSize;

    MeicamCaptionClip(NvsCaption caption, String text, long inPoint, long outPoint) {
        super(caption, CommonData.CLIP_CAPTION);
        this.text = text;
        setInPoint(inPoint);
        setOutPoint(outPoint);
        generateCreateTag();
        if (caption != null) {
            this.fontSize = caption.getFontSize();
            this.bold = caption.getBold();
            if (caption.isModular()) {
                setCaptionType(MeicamCaptionClip.CAPTION_TYPE_MODULAR);
            } else {
                setCaptionType(MeicamCaptionClip.CAPTION_TYPE_NORMAL);
            }
        }
        setLetterSpacingType(NvsCaption.LETTER_SPACING_TYPE_ABSOLUTE);
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        NvsCaption object = getObject();
        if (object != null) {
            object.setText(text);
            this.text = text;
        }
    }

    public String getStyleId() {
        return styleId;
    }

    public void setStyleId(String styleId) {
        if (TextUtils.isEmpty(styleId)) {
            return;
        }
        NvsCaption object = getObject();
        if (object != null) {
            object.setRecordingUserOperation(false);
            object.applyCaptionStyle(styleId);
            object.setRecordingUserOperation(true);
            this.styleId = styleId;
            updateLetterSpace();
            fontSize = object.getFontSize();
        }
    }

    @Override
    public String getType() {
        return type;
    }

    @Override
    public void setType(String type) {
        this.type = type;
    }

    public float getScaleX() {
        return scaleX;
    }

    public void setScaleX(float scaleX) {
        if (invalidFloat(scaleX)) {
            return;
        }
        NvsCaption object = getObject();
        if (object != null) {
            object.setScaleX(scaleX);
            this.scaleX = scaleX;
        }
    }

    public float getScaleY() {
        return scaleY;
    }

    public void setScaleY(float scaleY) {
        if (invalidFloat(scaleY)) {
            return;
        }
        NvsCaption object = getObject();
        if (object != null) {
            object.setScaleY(scaleY);
            this.scaleY = scaleY;
        }
    }

    public float getRotation() {
        return rotation;
    }

    public void setRotation(float rotation) {
        if (invalidFloat(rotation)) {
            return;
        }
        NvsCaption object = getObject();
        if (object != null) {
            object.setRotationZ(rotation);
            this.rotation = rotation;
        }

    }

    public float getTranslationX() {
        return translationX;
    }

    public void setTranslationX(float translationX) {
        if (invalidFloat(translationX)) {
            return;
        }
        NvsCaption object = getObject();
        if (object != null) {
            object.setCaptionTranslation(new PointF(translationX, getTranslationY()));
            this.translationX = translationX;
        }
    }

    public void setTranslation(PointF captionTranslation) {
        if (captionTranslation == null || invalidFloat(captionTranslation.x) || invalidFloat(captionTranslation.y)) {
            return;
        }
        NvsCaption object = getObject();
        if (object != null) {
            object.setCaptionTranslation(captionTranslation);
            this.translationX = captionTranslation.x;
            this.translationY = captionTranslation.y;
        }
    }

    public void translateCaption(PointF timelinePoint) {
        if (timelinePoint == null || invalidFloat(timelinePoint.x) || invalidFloat(timelinePoint.y)) {
            return;
        }
        NvsCaption object = getObject();
        if (object != null) {
            object.translateCaption(timelinePoint);
            this.translationX += timelinePoint.x;
            this.translationY += timelinePoint.y;
        }
    }

    public float getTranslationY() {
        return translationY;
    }

    public void setTranslationY(float translationY) {
        if (invalidFloat(translationY)) {
            return;
        }
        NvsCaption object = getObject();
        if (object != null) {
            object.setCaptionTranslation(new PointF(getTranslationX(), translationY));
            this.translationY = translationY;
        }
    }

    public String getFont() {
        return font;
    }

    public String getFontPath() {
        return fontPath;
    }

    public void setFont(String font) {
        NvsCaption object = getObject();
        if (object != null) {
            object.setFontByFilePath(font);
            this.font = font;
        }
    }

    public void setFontByFilePath(String font) {
        NvsCaption object = getObject();
        if (object != null) {
            object.setFontByFilePath(font);
            this.fontPath = font;
        }
    }

    public float getLineSpacing() {
        NvsCaption object = getObject();
        if (object != null) {
            return object.getLineSpacing();
        }
        return lineSpacing;
    }

    public void setLineSpacing(float lineSpacing) {
        if (invalidFloat(lineSpacing)) {
            return;
        }
        NvsCaption object = getObject();
        if (object != null) {
            object.setLineSpacing(lineSpacing);
            this.lineSpacing = lineSpacing;
        }
    }

    public float[] getTextColor() {
        NvsCaption object = getObject();
        if (object != null) {
            NvsColor textColor = object.getTextColor();
            return ColorUtil.getColorArray(textColor);
        }
        return textColor;
    }

    public void setTextColor(float[] textColor) {
        NvsCaption object = getObject();
        if (object != null) {
            NvsColor nvsColor = ColorUtil.colorFloatToNvsColor(textColor);
            if (nvsColor != null) {
                object.setTextColor(nvsColor);
                this.textColor = textColor;
            }
        }
    }

    public boolean isBold() {
        NvsCaption object = getObject();
        if (object != null) {
            return object.getBold();
        }
        return bold;
    }

    public void setBold(boolean bold) {
        NvsCaption object = getObject();
        if (object != null) {
            object.setBold(bold);
            this.bold = bold;
        }
    }

    public boolean isItalic() {
        NvsCaption object = getObject();
        if (object != null) {
            return object.getItalic();
        }
        return italic;
    }

    public void setItalic(boolean italic) {
        NvsCaption object = getObject();
        if (object != null) {
            object.setItalic(italic);
            this.italic = italic;
        }
    }

    public boolean isShadow() {
        NvsCaption object = getObject();
        if (object != null) {
            return object.getDrawShadow();
        }
        return shadow;
    }

    public void setShadow(boolean shadow) {
        NvsCaption object = getObject();
        if (object != null) {
            object.setDrawShadow(shadow);
            this.shadow = shadow;
        }
    }

    public boolean isOutline() {
        NvsCaption object = getObject();
        if (object != null) {
            return object.getDrawOutline();
        }
        return outline;
    }

    public void setOutline(boolean outline) {
        NvsCaption object = getObject();
        if (object != null) {
            object.setDrawOutline(outline);
            this.outline = outline;
        }
    }

    public float[] getOutlineColor() {
        NvsCaption object = getObject();
        if (object != null) {
            NvsColor outlineColor = object.getOutlineColor();
            return ColorUtil.getColorArray(outlineColor);
        }
        return outlineColor;
    }

    public void setOutlineColor(float[] outlineColor) {
        NvsCaption object = getObject();
        if (object != null) {
            NvsColor nvsColor = ColorUtil.colorFloatToNvsColor(outlineColor);
            if (nvsColor != null) {
                object.setOutlineColor(nvsColor);
                this.outlineColor = outlineColor;
            }
        }
    }

    public float getOutlineWidth() {
        NvsCaption object = getObject();
        if (object != null) {
            return object.getOutlineWidth();
        }
        return outlineWidth;
    }

    public void setOutlineWidth(float outlineWidth) {
        if (invalidFloat(outlineWidth)) {
            return;
        }
        NvsCaption object = getObject();
        if (object != null) {
            object.setDrawOutline(true);
            object.setOutlineWidth(outlineWidth);
            this.outlineWidth = outlineWidth;
        }

    }

    @Override
    public float getZValue() {
        NvsCaption object = getObject();
        if (object != null) {
            return object.getZValue();
        }
        return super.getZValue();
    }

    @Override
    public void setZValue(float zValue) {
        if (invalidFloat(zValue)) {
            return;
        }
        NvsCaption object = getObject();
        if (object != null) {
            object.setZValue(zValue);
            super.setZValue(zValue);
        }
    }

    public float getLetterSpacing() {
        NvsCaption object = getObject();
        if (object != null) {
            return object.getLetterSpacing();
        }
        return letterSpacing;
    }

    public void setLetterSpacing(float letterSpacing) {
        if (invalidFloat(letterSpacing)) {
            return;
        }
        NvsCaption object = getObject();
        if (object != null) {
            object.setRecordingUserOperation(false);
            object.setLetterSpacing(letterSpacing);
            object.setRecordingUserOperation(true);
            this.letterSpacing = letterSpacing;
        }
    }

    public void setLetterSpacingType(int letterSpacingType) {
        NvsCaption object = getObject();
        if (object != null) {
            object.setLetterSpacingType(letterSpacingType);
            this.letterSpacingType = letterSpacingType;
        }
    }

    public int getLetterSpacingType() {
        NvsCaption object = getObject();
        if (object != null) {
            return object.getLetterSpacingType();
        }
        return letterSpacingType;
    }

    public int getTextAlignment() {
        NvsCaption object = getObject();
        if (object != null) {
            return object.getTextAlignment();
        }
        return textAlignment;
    }

    public void setTextAlignment(int textAlignment) {
        NvsCaption object = getObject();
        if (object != null) {
            object.setTextAlignment(textAlignment);
            this.textAlignment = textAlignment;
        }
    }

    public float[] getBackgroundColor() {
        NvsCaption object = getObject();
        if (object != null) {
            NvsColor backgroundColor = object.getBackgroundColor();
            return ColorUtil.getColorArray(backgroundColor);
        }
        return backgroundColor;
    }

    public void setBackgroundColor(float[] backgroundColor) {
        NvsCaption object = getObject();
        if (object != null) {
            NvsColor nvsColor = ColorUtil.colorFloatToNvsColor(backgroundColor);
            if (nvsColor != null) {
                object.setBackgroundColor(nvsColor);
                this.backgroundColor = backgroundColor;
            }
        }
    }

    public float getBackgroundRadius() {
        NvsCaption object = getObject();
        if (object != null) {
            return object.getBackgroundRadius();
        }
        return backgroundRadius;
    }

    public void setBackgroundRadius(float backgroundRadius) {
        if (invalidFloat(backgroundRadius)) {
            return;
        }
        NvsCaption object = getObject();
        if (object != null) {
            object.setBackgroundRadius(backgroundRadius);
            this.backgroundRadius = backgroundRadius;
        }
    }

    @Override
    public void setInPoint(long inPoint) {
        NvsCaption object = getObject();
        if (object != null) {
            if (object instanceof NvsTimelineCaption) {
                NvsTimelineCaption timelineCaption = (NvsTimelineCaption) object;
                timelineCaption.changeInPoint(inPoint);
            } else if (object instanceof NvsTrackCaption) {
                NvsTrackCaption trackCaption = (NvsTrackCaption) object;
                trackCaption.changeInPoint(inPoint);
            } else if (object instanceof NvsClipCaption) {
                NvsClipCaption clipCaption = (NvsClipCaption) object;
                clipCaption.changeInPoint(inPoint);
            }
            this.inPoint = inPoint;
        }
    }

    @Override
    public void setOutPoint(long outPoint) {
        NvsCaption object = getObject();
        if (object != null) {
            if (object instanceof NvsTimelineCaption) {
                NvsTimelineCaption timelineCaption = (NvsTimelineCaption) object;
                timelineCaption.changeOutPoint(outPoint);
            } else if (object instanceof NvsTrackCaption) {
                NvsTrackCaption trackCaption = (NvsTrackCaption) object;
                trackCaption.changeOutPoint(outPoint);
            } else if (object instanceof NvsClipCaption) {
                NvsClipCaption nvsClipCaption = (NvsClipCaption) object;
                nvsClipCaption.changeOutPoint(outPoint);
            }
            this.outPoint = outPoint;
        }
    }

    public String getRichWordUuid() {
        return richWordUuid;
    }

    public boolean setRichWordUuid(String richWordUuid) {
        NvsCaption object = getObject();
        if (object != null) {
            object.setRecordingUserOperation(false);
            boolean isSuccess = object.applyModularCaptionRenderer(richWordUuid);
            if (isSuccess) {
                this.richWordUuid = richWordUuid;
                updateParamAfterAddingFx(object);
            }
            object.setRecordingUserOperation(true);
            updateLetterSpace();
            return isSuccess;
        }
        return false;
    }

    private void updateLetterSpace() {
        NvsCaption object = getObject();
        int letterSpacingType = object.getLetterSpacingType();
        letterSpacing = object.getLetterSpacing();
        if (letterSpacingType == NvsCaption.LETTER_SPACING_TYPE_PERCENTAGE) {
            letterSpacing = parseLetterSpace(letterSpacing);
        }
        setLetterSpacingType(NvsCaption.LETTER_SPACING_TYPE_ABSOLUTE);
        setLetterSpacing(letterSpacing);
    }

    /**
     * 设置特效后更新参数
     *
     * @param object the NvsTimelineCaption
     */
    private void updateParamAfterAddingFx(NvsCaption object) {
        NvsColor textColor = object.getTextColor();
        if (textColor != null) {
            this.textColor = ColorUtil.getColorArray(textColor);
        }
        PointF translation = object.getCaptionTranslation();
        if (translation != null) {
            this.translationX = translation.x;
            this.translationY = translation.y;
        }
        this.scaleX = object.getScaleX();
        this.scaleY = object.getScaleY();
        this.bold = object.getBold();
        this.textAlignment = object.getTextAlignment();
    }

    public String getBubbleUuid() {
        return bubbleUuid;
    }

    public boolean setBubbleUuid(String bubbleUuid) {
        NvsCaption object = getObject();
        if (object != null) {
            object.setRecordingUserOperation(false);
            boolean isSuccess = object.applyModularCaptionContext(bubbleUuid);
            if (isSuccess) {
                this.bubbleUuid = bubbleUuid;
                updateParamAfterAddingFx(object);
            }
            object.setRecordingUserOperation(true);
            updateLetterSpace();
            return isSuccess;
        }
        return false;
    }

    public String getCombinationAnimationUuid() {
        return combinationAnimationUuid;
    }

    public boolean setCombinationAnimationUuid(String combinationAnimationUuid) {
        NvsCaption object = getObject();
        if (object != null) {
            object.setRecordingUserOperation(false);
            boolean isSuccess = object.applyModularCaptionAnimation(combinationAnimationUuid);
            if (isSuccess) {
                this.combinationAnimationUuid = combinationAnimationUuid;
            }
            object.setRecordingUserOperation(true);
            updateLetterSpace();
            return isSuccess;
        }
        return false;
    }

    public String getMarchInAnimationUuid() {
        return marchInAnimationUuid;
    }

    public boolean setMarchInAnimationUuid(String marchInAnimationUuid) {
        NvsCaption object = getObject();
        if (object != null) {
            object.setRecordingUserOperation(false);
            boolean isSuccess = object.applyModularCaptionInAnimation(marchInAnimationUuid);
            if (isSuccess) {
                this.marchInAnimationUuid = marchInAnimationUuid;
            }
            object.setRecordingUserOperation(true);
            updateLetterSpace();
            return isSuccess;
        }
        return false;
    }

    public String getMarchOutAnimationUuid() {
        return marchOutAnimationUuid;
    }

    public boolean setMarchOutAnimationUuid(String marchOutAnimationUuid) {
        NvsCaption object = getObject();
        if (object != null) {
            object.setRecordingUserOperation(false);
            boolean isSuccess = object.applyModularCaptionOutAnimation(marchOutAnimationUuid);
            if (isSuccess) {
                this.marchOutAnimationUuid = marchOutAnimationUuid;
            }
            object.setRecordingUserOperation(true);
            updateLetterSpace();
            return isSuccess;
        }
        return false;
    }

    public int getCombinationAnimationDuration() {
        return combinationAnimationDuration;
    }

    public void setCombinationAnimationDuration(int combinationAnimationDuration) {
        NvsCaption object = getObject();
        if (object != null) {
            object.setRecordingUserOperation(false);
            object.setModularCaptionAnimationPeroid(combinationAnimationDuration);
            this.combinationAnimationDuration = combinationAnimationDuration;
            object.setRecordingUserOperation(true);
            updateLetterSpace();
        }
    }

    public int getMarchInAnimationDuration() {
        return marchInAnimationDuration;
    }

    public void setMarchInAnimationDuration(int marchInAnimationDuration) {
        NvsCaption object = getObject();
        if (object != null) {
            object.setRecordingUserOperation(false);
            object.setModularCaptionInAnimationDuration(marchInAnimationDuration);
            this.marchInAnimationDuration = marchInAnimationDuration;
            object.setRecordingUserOperation(true);
            updateLetterSpace();
        }
    }

    public int getMarchOutAnimationDuration() {
        return marchOutAnimationDuration;
    }

    public void setMarchOutAnimationDuration(int marchOutAnimationDuration) {
        NvsCaption object = getObject();
        if (object != null) {
            object.setRecordingUserOperation(false);
            object.setModularCaptionOutAnimationDuration(marchOutAnimationDuration);
            this.marchOutAnimationDuration = marchOutAnimationDuration;
            object.setRecordingUserOperation(true);
            updateLetterSpace();
        }
    }

    /**
     * Gets the vertex position of the original envelope of the caption in the timeline coordinate system
     * Return List&lt;PointF&gt;Object containing four vertex positions corresponding to the top left, bottom left,
     * bottom right, and top right vertices of the original envelope
     * <p>
     * 获取字幕在时间线坐标系下原始包络框的顶点位置
     * 返回List<PointF>对象，包含四个顶点位置，分别对应原始包络框的左上，左下，右下，右上顶点
     *
     * @return
     */
    public List<PointF> getBoundingRectangleVertices() {
        NvsCaption object = getObject();
        if (object != null) {
            return object.getBoundingRectangleVertices();
        }
        return null;

    }

    /**
     * Gets the vertex position of the original border of the caption after transformation
     * Return List&lt;PointF&gt;Object containing four vertex positions corresponding to the top left, bottom left,
     * bottom right, and top right vertices of the original envelope
     * <p>
     * 获取字幕原始边框变换后的顶点位置
     * 返回List<PointF>对象，包含四个顶点位置，分别对应原始包络框的左上，左下，右下，右上顶点
     *
     * @param boundingTypeText the boundingTypeText
     *                         static final int 	BOUNDING_TYPE_TEXT = 0
     *                         包括装饰在内的整体边框
     *                         <p>
     *                         static final int 	BOUNDING_TYPE_TEXT_FRAME = 1
     *                         文字框的边框
     *                         <p>
     *                         static final int 	BOUNDING_TYPE_FRAME = 2
     *                         文字的实际边框
     * @return
     */

    public List<PointF> getCaptionBoundingVertices(int boundingTypeText) {
        NvsCaption object = getObject();
        if (object != null) {
            return object.getCaptionBoundingVertices(boundingTypeText);
        }
        return null;
    }

    /**
     * Gets template attachment.
     * <p>
     * 根据key设置相关属性
     *
     * @param type the type
     * @return the template attachment
     */
    public String getTemplateAttachment(String type) {
        NvsCaption object = getObject();
        if (object != null) {
            return object.getTemplateAttachment(type);
        }
        return null;
    }

    /**
     * scale Caption
     * 缩放字幕
     *
     * @param scaleFactor 字幕缩放的因子
     * @param anchor      字幕缩放的锚点
     */
    public void scaleCaption(float scaleFactor, PointF anchor) {
        NvsCaption object = getObject();
        if (object != null) {
            object.scaleCaption(scaleFactor, anchor);
            scaleX = object.getScaleX();
            scaleY = object.getScaleY();
            PointF point = object.getCaptionTranslation();
            if (point != null) {
                translationX = point.x;
                translationY = point.y;
            }
        }
        anchorForScale = anchor;
    }

    public PointF getAnchorForScale() {
        return anchorForScale;
    }

    /**
     * 设置模板附件
     * Set template attachment
     *
     * @param key   the key 键
     * @param value the value 值
     */
    public void setTemplateAttachment(String key, String value) {
        NvsCaption captionClip = getObject();
        if (captionClip != null) {
            captionClip.setTemplateAttachment(key, value);
        }
    }

    /**
     * 旋转字幕
     * Rotate caption.
     *
     * @param angle the angle
     */
    public void rotateCaption(float angle) {
        NvsCaption captionClip = getObject();
        if (captionClip != null) {
            captionClip.rotateCaption(angle);
            rotation = captionClip.getRotationZ();
        }
    }

    public int getThemeType() {
        return themeType;
    }

    public void setThemeType(int themeType) {
        this.themeType = themeType;
    }

    public int getOperationType() {
        return operationType;
    }

    public void setOperationType(int operationType) {
        this.operationType = operationType;
    }

    public String getCaptionType() {
        return captionType;
    }

    public void setCaptionType(String captionType) {
        this.captionType = captionType;
    }


    public float getFontSize() {
        return fontSize;
    }

    public void setFontSize(float fontSize) {
        this.fontSize = fontSize;
    }

    boolean bindToTimeline() {
        NvsCaption object = getObject();
        if (object == null) {
            return false;
        }
        if (object.isModular()) {
            setCaptionType(CAPTION_TYPE_MODULAR);
        } else {
            setCaptionType(CAPTION_TYPE_NORMAL);
        }
        String styleId = getStyleId();
        if (!TextUtils.isEmpty(styleId)) {
            object.applyCaptionStyle(styleId);
        }
        object.setFontSize(fontSize);
        int maxDuration = 0;
        if (object instanceof NvsTimelineCaption) {
            NvsTimelineCaption nvsTimelineCaption = (NvsTimelineCaption) object;
            nvsTimelineCaption.setClipAffinityEnabled(false);
            maxDuration = (int) ((nvsTimelineCaption.getOutPoint() - nvsTimelineCaption.getInPoint()) / 1000);
        } else if (object instanceof NvsTrackCaption) {
            NvsTrackCaption trackCaption = (NvsTrackCaption) object;
            trackCaption.setClipAffinityEnabled(false);
            maxDuration = (int) ((trackCaption.getOutPoint() - trackCaption.getInPoint()) / 1000);
        } else if (object instanceof NvsClipCaption) {
            NvsClipCaption nvsClipCaption = (NvsClipCaption) object;
            maxDuration = (int) ((nvsClipCaption.getOutPoint() - nvsClipCaption.getInPoint()) / 1000);
        }

        if (!TextUtils.isEmpty(combinationAnimationUuid)) {
            //优先使用组合动画，和入场、出场动画互斥
            object.applyModularCaptionAnimation(combinationAnimationUuid);
            if (combinationAnimationDuration >= 0) {
                object.setModularCaptionAnimationPeroid(combinationAnimationDuration);
            }
        } else {
            object.applyModularCaptionInAnimation(marchInAnimationUuid);

            //Log.d("lhz","in,duration="+caption.getMarchInAnimationDuration()+"**out duration="+caption.getMarchOutAnimationDuration());
            if (marchInAnimationDuration >= 0) {
                if (maxDuration - marchInAnimationDuration < 500) {
                    //如果设置的入动画时间后，剩余的默认时间小于500毫秒（出入动画默认时长500ms，不论设置不设置出动画）
                    object.setModularCaptionOutAnimationDuration(maxDuration - marchInAnimationDuration);
                }
                //先后顺序不可乱，因为出入动画默认时长500ms，不论设置不设置出动画
                object.setModularCaptionInAnimationDuration(marchInAnimationDuration);
            }
            object.applyModularCaptionOutAnimation(marchOutAnimationUuid);
            if (marchOutAnimationDuration >= 0) {
                if (maxDuration - marchOutAnimationDuration < 500) {
                    //如果设置的出动画时间后，剩余的默认时间小于500毫秒（出入动画默认时长500ms，不论设置不设置出动画）
                    object.setModularCaptionInAnimationDuration(maxDuration - marchOutAnimationDuration);
                }
                //先后顺序不可乱，//先后顺序不可乱，因为出入动画默认时长500ms，不论设置不设置出动画
                object.setModularCaptionOutAnimationDuration(marchOutAnimationDuration);
            }
        }

        if (textAlignment >= 0) {
            object.setTextAlignment(textAlignment);
        }

        NvsColor textColorNvsColor = ColorUtil.colorFloatToNvsColor(textColor);
        if (textColorNvsColor != null) {
            if (textColor[0] != 1.0 || textColor[1] != 1.0 || textColor[2] != 1.0 || textColor[3] != 1.0) {
                object.setTextColor(textColorNvsColor);
            }
        }

        /*
         * 放缩字幕
         * Shrink captions
         * */
        object.setScaleX(scaleX);
        object.setScaleY(scaleY);
        /*
         * 旋转字幕
         * Spin subtitles
         * */
        object.setRotationZ(rotation);
        object.setZValue(zValue);
        object.setDrawOutline(outline);
        if (outline) {
            NvsColor outlineColorNvsColor = ColorUtil.colorFloatToNvsColor(outlineColor);
            if (outlineColorNvsColor != null) {
                object.setOutlineColor(outlineColorNvsColor);
                object.setOutlineWidth(outlineWidth);
            }
        }
        NvsColor backgroundColorNvsColor = ColorUtil.colorFloatToNvsColor(backgroundColor);
        if (backgroundColorNvsColor != null) {
            object.setBackgroundColor(backgroundColorNvsColor);
        }

//        if (!font.isEmpty()) {
//            nvsTimelineCaption.setFontByFilePath(font);
//        }
        object.setBold(bold);
        object.setItalic(italic);
        object.setDrawShadow(shadow);
        object.setBackgroundRadius(backgroundRadius);
        PointF translation = new PointF(translationX, translationY);
        object.setCaptionTranslation(translation);
        /*
         * 应用字符间距
         * Apply character spacing
         * */
        if (letterSpacing == 0 && getLetterSpacingType() == MeicamCaptionClip.LETTER_SPACING_TYPE_PERCENTAGE) {
            letterSpacing = 100;
        }
        object.setLetterSpacing(letterSpacing);
        if (lineSpacing > 0) {
            object.setLineSpacing(lineSpacing);
        }

        object.setLetterSpacingType(letterSpacingType);

        if (!TextUtils.isEmpty(font)) {
            object.setFontFamily(font);
        }

        if (!TextUtils.isEmpty(fontPath)) {
            object.setFontByFilePath(fontPath);
        }

        if (!TextUtils.isEmpty(richWordUuid)) {
            object.applyModularCaptionRenderer(richWordUuid);
        }
        if (!TextUtils.isEmpty(bubbleUuid)) {
            object.applyModularCaptionContext(bubbleUuid);
        }
        /*还原关键帧*/
        keyFrameProcessor().bindToTimeline();
        return true;
    }


    @Override
    public void loadData() {
        NvsCaption caption = getObject();
        if (caption == null) {
            return;
        }
        setObject(caption);
        captionType = caption.isModular() ? CAPTION_TYPE_MODULAR : CAPTION_TYPE_NORMAL;
        if (caption instanceof NvsTimelineCaption) {
            NvsTimelineCaption nvsTimelineCaption = (NvsTimelineCaption) caption;
            setInPoint(nvsTimelineCaption.getInPoint());
            setOutPoint(nvsTimelineCaption.getOutPoint());
        } else if (caption instanceof NvsTrackCaption) {
            NvsTrackCaption trackCaption = (NvsTrackCaption) caption;
            setInPoint(trackCaption.getInPoint());
            setOutPoint(trackCaption.getOutPoint());
        } else if (caption instanceof NvsClipCaption) {
            NvsClipCaption clipCaption = (NvsClipCaption) caption;
            setInPoint(clipCaption.getInPoint());
            setOutPoint(clipCaption.getOutPoint());
        }

        text = caption.getText();
        if (text.contains("\r")) {
            text = text.replace("\r", "\n");
            caption.setText(text);
        }
        styleId = caption.getCaptionStylePackageId();
        fontSize = caption.getFontSize();
        NvsColor color = caption.getTextColor();
        if (textColor != null && color != null) {
            textColor[0] = color.r;
            textColor[1] = color.g;
            textColor[2] = color.b;
            textColor[3] = color.a;
        }
        PointF point = caption.getCaptionTranslation();
        if (point != null) {
            translationX = point.x;
            translationY = point.y;
        }
        scaleX = caption.getScaleX();
        scaleY = caption.getScaleY();
        rotation = caption.getRotationZ();
        letterSpacing = caption.getLetterSpacing();
        letterSpacingType = caption.getLetterSpacingType();
        lineSpacing = caption.getLineSpacing();
        font = caption.getFontFamily();
        fontPath = caption.getFontFilePath();
        bold = caption.getBold();
        italic = caption.getItalic();
        shadow = caption.getDrawShadow();
        outline = caption.getDrawOutline();
        zValue = (int) caption.getZValue();
        textAlignment = caption.getTextAlignment();
        backgroundRadius = caption.getBackgroundRadius();
        NvsColor nvsColor = caption.getOutlineColor();
        if (outlineColor != null && nvsColor != null) {
            outlineColor[3] = nvsColor.a;
            outlineColor[0] = nvsColor.r;
            outlineColor[1] = nvsColor.g;
            outlineColor[2] = nvsColor.b;
        }

        NvsColor nvsBgColor = caption.getBackgroundColor();
        if (backgroundColor != null && nvsBgColor != null) {
            backgroundColor[3] = nvsBgColor.a;
            backgroundColor[0] = nvsBgColor.r;
            backgroundColor[1] = nvsBgColor.g;
            backgroundColor[2] = nvsBgColor.b;
        }
        fontSize = caption.getFontSize();

        outlineWidth = caption.getOutlineWidth();
        richWordUuid = caption.getModularCaptionRendererPackageId();
        bubbleUuid = caption.getModularCaptionContextPackageId();
        combinationAnimationUuid = caption.getModularCaptionAnimationPackageId();
        combinationAnimationDuration = caption.getModularCaptionAnimationPeroid();
        marchInAnimationUuid = caption.getModularCaptionInAnimationPackageId();
        marchInAnimationDuration = caption.getModularCaptionInAnimationDuration();
        marchOutAnimationUuid = caption.getModularCaptionOutAnimationPackageId();
        marchOutAnimationDuration = caption.getModularCaptionOutAnimationDuration();
        //平移值要放在设置动画后面，这样动画才能完全执行完。放在前面的话，例如上滚屏，不能执行完毕。
        //The translation value should be placed after the set animation so that the animation can be fully executed.
        // If put in front, such as scroll up, it will not finish.
        setOperationType(CommonData.TYPE_COMMON_CAPTION);
        keyFrameProcessor().recoverFromTimelineData(caption);
    }

    @Override
    public Object clone() {
        return clone(true);
    }

    /**
     * 复制，
     * Clone
     *
     * @param changeCreateTag true change the create tag,false not
     * @return the MeicamCaptionClip
     */
    @Override
    public Object clone(boolean changeCreateTag) {
        Object clone = DeepCopyUtil.deepClone(this);
        if (clone == null) {
            String cloneString = GsonContext.getInstance().toJson(this);
            if (!TextUtils.isEmpty(cloneString)) {
                clone = GsonContext.getInstance().fromJson(cloneString, this.getClass());

            }
        }
        if (changeCreateTag && clone instanceof ClipInfo) {
            ((ClipInfo<?>) clone).generateCreateTag();
        }
        if (clone == null) {
            clone = new MeicamCaptionClip(null, "", 0, 1);
        }
        return clone;
    }

    @Override
    public void recoverFromLocalData(LMeicamCaptionClip lMeicamCaptionClip) {
        setStyleId(lMeicamCaptionClip.getStyleId());
        setFontSize(lMeicamCaptionClip.getFontSize());
        String font = lMeicamCaptionClip.getFont();
        if (!TextUtils.isEmpty(font)) {
            setFont(font);
        }
        String fontPath = lMeicamCaptionClip.getFontPath();
        if (!TextUtils.isEmpty(fontPath)) {
            setFontByFilePath(fontPath);
        }

        float[] textColor = lMeicamCaptionClip.getTextColor();
        if (textColor != null) {
            float[] localTextColor = new float[4];
            System.arraycopy(textColor, 0, localTextColor, 0, textColor.length);
            setTextColor(localTextColor);
        }

        setBold(lMeicamCaptionClip.isBold());
        setItalic(lMeicamCaptionClip.isItalic());
        setShadow(lMeicamCaptionClip.isShadow());

        setOutline(lMeicamCaptionClip.isOutline());
        float[] outlineColor = lMeicamCaptionClip.getOutlineColor();
        if (outlineColor != null) {
            float[] localOutlineColor = new float[4];
            System.arraycopy(outlineColor, 0, localOutlineColor, 0, outlineColor.length);
            setOutlineColor(localOutlineColor);
        }


        float[] bgColor = lMeicamCaptionClip.getBackgroundColor();
        if (bgColor != null) {
            float[] localBgColor = new float[4];
            System.arraycopy(bgColor, 0, localBgColor, 0, bgColor.length);
            setBackgroundColor(localBgColor);
        }

        setOutlineWidth(lMeicamCaptionClip.getOutlineWidth());
        setBackgroundRadius(lMeicamCaptionClip.getBackgroundAngle());
        setZValue(lMeicamCaptionClip.getzValue());
        setLineSpacing(lMeicamCaptionClip.getLineSpacing());
        setTextAlignment(lMeicamCaptionClip.getTextAlign());
        setRichWordUuid(lMeicamCaptionClip.getRichWordUuid());
        setBubbleUuid(lMeicamCaptionClip.getBubbleUuid());
        setCombinationAnimationUuid(lMeicamCaptionClip.getCombinationAnimationUuid());
        setCombinationAnimationDuration(lMeicamCaptionClip.getCombinationAnimationDuration());
        setMarchInAnimationUuid(lMeicamCaptionClip.getMarchInAnimationUuid());
        setMarchInAnimationDuration(lMeicamCaptionClip.getMarchInAnimationDuration());
        setMarchOutAnimationUuid(lMeicamCaptionClip.getMarchOutAnimationUuid());
        setMarchOutAnimationDuration(lMeicamCaptionClip.getMarchOutAnimationDuration());
        //平移值要放在设置动画后面，这样动画才能完全执行完。放在前面的话，例如上滚屏，不能执行完毕。
        //The translation value should be placed after the set animation so that the animation can be fully executed.
        // If put in front, such as scroll up, it will not finish.
        setTranslationX(lMeicamCaptionClip.getTranslationX());
        setTranslationY(lMeicamCaptionClip.getTranslationY());
        setScaleX(lMeicamCaptionClip.getScaleX());
        setScaleY(lMeicamCaptionClip.getScaleY());
        setRotation(lMeicamCaptionClip.getRotation());
        setCaptionType(lMeicamCaptionClip.getCaptionType());
        setOperationType(lMeicamCaptionClip.getOperationType());
        setZValue(lMeicamCaptionClip.getzValue());
        //需要最后字体设置，不然不起效果
        //The last font setting is required, otherwise it will not work
        int letterSpacingType = lMeicamCaptionClip.getLetterSpacingType();
        if (letterSpacingType < 0) {
            setLetterSpacingType(NvsCaption.LETTER_SPACING_TYPE_PERCENTAGE);
        } else {
            setLetterSpacingType(letterSpacingType);
        }
        Map<String, Object> attachment = lMeicamCaptionClip.getAttachment();
        if (attachment != null) {
            Set<Map.Entry<String, Object>> entries = attachment.entrySet();
            if (!entries.isEmpty()) {
                for (Map.Entry<String, Object> entry : entries) {
                    setAttachment(entry.getKey(), entry.getValue());
                }
            }
        }
        setLetterSpacing(lMeicamCaptionClip.getLetterSpacing());
        keyFrameProcessor().recoverKeyFrame(lMeicamCaptionClip);
        keyFrameProcessor().recoverFromLocalData(lMeicamCaptionClip.getKeyFrameProcessor());
    }

    @Override
    public void recoverFromTimelineData(NvsObject nvsObject) {
        NvsCaption caption = (NvsCaption) nvsObject;
        setObject(caption);
        loadData();
    }

    @Override
    public LMeicamCaptionClip parseToLocalData() {
        LMeicamCaptionClip local = new LMeicamCaptionClip();
        setCommonData(local);
        local.setLetterSpacingType(getLetterSpacingType());
        local.setFontSize(getFontSize());
        local.setFontPath(fontPath);
        local.setText(getText());
        local.setStyleId(getStyleId());
        local.setScaleX(getScaleX());
        local.setScaleY(getScaleY());
        local.setRotation(getRotation());
        local.setTranslationX(getTranslationX());
        local.setTranslationY(getTranslationY());
        local.setFont(getFont());
        local.setCaptionType(getCaptionType());
        float[] textColor = getTextColor();
        float[] localTextColor = new float[4];
        System.arraycopy(textColor, 0, localTextColor, 0, textColor.length);
        local.setTextColor(localTextColor);
        local.setBold(isBold());
        local.setItalic(isItalic());
        local.setShadow(isShadow());
        local.setOutline(isOutline());

        float[] outlineColor = getOutlineColor();
        float[] localOutlineColor = new float[4];
        System.arraycopy(outlineColor, 0, localOutlineColor, 0, textColor.length);
        local.setOutlineColor(localOutlineColor);

        float[] bgColor = getBackgroundColor();
        float[] localBgColor = new float[4];
        System.arraycopy(bgColor, 0, localBgColor, 0, bgColor.length);
        local.setBackgroundColor(localBgColor);
        local.setBackgroundAngle(getBackgroundRadius());
        local.setOutlineWidth(getOutlineWidth());
        local.setzValue(getZValue());
        local.setLetterSpacing(getLetterSpacing());
        local.setLineSpacing(getLineSpacing());
        local.setTextAlign(getTextAlignment());
        local.setRichWordUuid(getRichWordUuid());
        local.setBubbleUuid(getBubbleUuid());
        local.setCombinationAnimationUuid(getCombinationAnimationUuid());
        local.setCombinationAnimationDuration(getCombinationAnimationDuration());
        local.setMarchInAnimationUuid(getMarchInAnimationUuid());
        local.setMarchInAnimationDuration(getMarchInAnimationDuration());
        local.setMarchOutAnimationUuid(getMarchOutAnimationUuid());
        local.setMarchOutAnimationDuration(getMarchOutAnimationDuration());
        local.setOperationType(getOperationType());
        local.setAttachment(attachment);
        if (keyFrameMap != null && keyFrameMap.size() > 0) {
            List<LMeicamKeyFrame> lKeyFrameList = new ArrayList<>();
            for (Map.Entry<Long, MeicamKeyFrame> entry : keyFrameMap.entrySet()) {
                MeicamKeyFrame value = entry.getValue();
                if (value != null) {
                    lKeyFrameList.add(value.parseToLocalData());
                }
            }
            local.setKeyFrameList(lKeyFrameList);
        }
        local.setKeyFrameProcessor(keyFrameProcessor().parseToLocalData());
        return local;
    }

    public void setTranslationInOutTimeline(float[] trans) {
        mTranslationInOutTimeline = trans;
    }

    public void setScaleInOutTimeline(float scale) {
        mScaleInOutTimeline = scale;
    }

    public float[] getTranslationInOutTimeline() {
        return mTranslationInOutTimeline;
    }

    public float getScaleInOutTimeline() {
        return mScaleInOutTimeline;
    }

    private float parseLetterSpace(float space) {
        return (float) ((space - 100) / 100.0 * 80);
    }

    private KeyFrameProcessor<NvsCaption> mKeyFrameHolder;

    @Override
    public KeyFrameProcessor<NvsCaption> keyFrameProcessor() {
        if (mKeyFrameHolder == null) {
            mKeyFrameHolder = new KeyFrameProcessor<NvsCaption>(this);
        }
        return mKeyFrameHolder;
    }

    public void syncParam(MeicamCaptionClip captionClip) {
        this.combinationAnimationUuid = captionClip.getCombinationAnimationUuid();
        this.combinationAnimationDuration = captionClip.getCombinationAnimationDuration();
        this.marchInAnimationUuid = captionClip.getMarchInAnimationUuid();
        this.marchInAnimationDuration = captionClip.getMarchInAnimationDuration();
        this.marchOutAnimationUuid = captionClip.getMarchOutAnimationUuid();
        this.marchOutAnimationDuration = captionClip.getMarchOutAnimationDuration();
        this.textAlignment = captionClip.getTextAlignment();
        float[] textColor = captionClip.getTextColor();
        if (textColor != null) {
            for (int index = 0; index < textColor.length; index++) {
                this.textColor[index] = textColor[index];
            }
        }
        /*
         * 放缩字幕
         * Shrink captions
         * */
        this.scaleX = captionClip.getScaleX();
        this.scaleY = captionClip.getScaleY();
        /*
         * 旋转字幕
         * Spin subtitles
         * */
        this.rotation = captionClip.getRotation();
        this.zValue = captionClip.getZValue();
        this.outline = captionClip.isOutline();
        float[] outlineColor = captionClip.getOutlineColor();
        if (outlineColor != null) {
            for (int index = 0; index < outlineColor.length; index++) {
                this.outlineColor[index] = outlineColor[index];
            }
        }
        this.outlineWidth = captionClip.getOutlineWidth();

        float[] backgroundColor = captionClip.getBackgroundColor();
        if (backgroundColor != null) {
            for (int index = 0; index < backgroundColor.length; index++) {
                this.backgroundColor[index] = backgroundColor[index];
            }
        }
        this.bold = captionClip.isBold();
        this.italic = captionClip.isItalic();
        this.shadow = captionClip.isShadow();
        this.backgroundRadius = captionClip.getBackgroundRadius();
        this.translationX = captionClip.getTranslationX();
        this.translationY = captionClip.getTranslationY();
        /*
         * 应用字符间距
         * Apply character spacing
         * */
        this.letterSpacing = captionClip.getLetterSpacing();
        this.lineSpacing = captionClip.getLineSpacing();
        this.letterSpacingType = captionClip.getLetterSpacingType();
        this.font = captionClip.getFont();
        this.fontPath = captionClip.getFontPath();
        this.richWordUuid = captionClip.getRichWordUuid();
        this.bubbleUuid = captionClip.getBubbleUuid();
        /*还原关键帧*/
        this.mKeyFrameHolder = captionClip.mKeyFrameHolder;
        bindToTimeline();
    }
}
