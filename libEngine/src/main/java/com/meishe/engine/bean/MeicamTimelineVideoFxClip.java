package com.meishe.engine.bean;

import static com.meishe.engine.bean.CommonData.TYPE_BUILD_IN;
import static com.meishe.engine.bean.CommonData.TYPE_PACKAGE;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_BOOLEAN;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_COLOR;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_FLOAT;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_INT;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_MENU;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_OBJECT;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_POSITION_2D;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_STRING;
import static com.meishe.engine.bean.MeicamVideoFx.INVALID_VALUE;
import static com.meishe.engine.bean.MeicamVideoFx.INVALID_VALUE_INT;
import static com.meishe.engine.constant.NvsConstants.KEY_REGION;
import static com.meishe.engine.constant.NvsConstants.TEMPLATE_KEY_FOOTAGE_FX_GROUP;

import android.graphics.PointF;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.google.gson.annotations.SerializedName;
import com.meicam.sdk.NvsArbitraryData;
import com.meicam.sdk.NvsColor;
import com.meicam.sdk.NvsLiveWindowExt;
import com.meicam.sdk.NvsMaskRegionInfo;
import com.meicam.sdk.NvsObject;
import com.meicam.sdk.NvsPosition2D;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsTimelineVideoFx;
import com.meishe.base.constants.Constants;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.GsonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.bean.bridges.FileInfoBridge;
import com.meishe.engine.bean.bridges.AtomicFxBridge;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.interf.IKeyFrameProcessor;
import com.meishe.engine.local.LMeicamFxParam;
import com.meishe.engine.local.LMeicamMaskRegionInfo;
import com.meishe.engine.local.LMeicamTimelineVideoFxClip;
import com.meishe.engine.util.ColorUtil;
import com.meishe.engine.util.DeepCopyUtil;
import com.meishe.engine.util.gson.GsonContext;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> CaoZhiChao
 * @CreateDate :2020/7/3 19:25
 * @Description :timeline特效clip  timeline video fx clip
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MeicamTimelineVideoFxClip extends ClipInfo<NvsTimelineVideoFx> implements Serializable,
        TimelineDataParserAdapter<LMeicamTimelineVideoFxClip>, IKeyFrameProcessor<NvsTimelineVideoFx> {
    /**
     * clipType. builtin: built in; package
     * 特效类型（取值：builtin： 内建；package：包）
     */
    private String clipType;
    /**
     * Clip description
     * If it is a built-in  effect, it is the name of the effect.
     * If it is a package  effect, it is the package ID of the  effect
     * <p>
     * 特效描述，如果是内建特效，是特效的名称，如果是包特效，则是特效的包id
     */
    private String desc;

    /**
     * The type of filter controlled on the web side, but not on the app side.
     * <p>
     * 网页端控制滤镜子类型的，APP端用不到。
     */
    private int clipSubType;

    /**
     * The sub type
     * <p>
     * 子类型
     */
    private String subType;

    /**
     * intensity
     * <p>
     * 强度
     */
    private float intensity;

    /**
     * Indicates whether it is a local effect. true:yes false:no
     * <p>
     * 表明是否是局部的特效 true:yes false:no
     */
    private boolean isRegional = false;

    /**
     * Region data for region effect
     * <p>
     * 区域特效数据
     */
    private float[] regionData;


    /**
     * Indicates whether need ignore background. true:yes false:no
     * <p>
     * 表明是否需要忽略背景 true:yes false:no
     */
    private boolean isIgnoreBackground = false;
    /**
     * Indicates whether it is a inverse region. true:yes false:no
     * <p>
     * 表明是否是翻转的局部特效 true:yes false:no
     */
    private boolean isInverseRegion = false;
    /**
     * Regional feather width
     * <p>
     * 局部特效的羽化值
     */
    private float regionalFeatherWidth = 0;

    /**
     * English name for display.
     * <p>
     * 英文展示用名称
     */
    private String displayName;

    /**
     * Chinese name for display.
     * <p>
     * 中文展示用名称
     */
    private String displayNamezhCN;

    /**
     * Temp data for point list.
     * <p>
     * 临时数据，用来记录点的数组
     */
    private transient List<PointF> pointList;


    /**
     * Identification, used to mark special effects
     * <p>
     * 标识，用于标记特效
     */
    private String indicate;


    @SerializedName("fxParams")
    protected Map<String, MeicamFxParam<?>> mMeicamFxParam = new TreeMap<>();

    /**
     * The info for region effect of fx
     * 特效的局部特效数据
     */
    private MeicamMaskRegionInfo regionInfo;

    private KeyFrameProcessor<NvsTimelineVideoFx> mKeyFrameHolder;

    /**
     * Constructs an MeicamTimelineVideoFxClip
     *
     * @param timelineVideoFx 时间线特效 timelineVideoFx
     * @param clipType        特效类型 clip type
     * @param inPoint         特效入点 inPoint
     * @param duration        特效时长 duration
     * @param desc            特效描述 desc
     */
    public MeicamTimelineVideoFxClip(NvsTimelineVideoFx timelineVideoFx, String clipType, long inPoint, long duration, String desc) {
        super(timelineVideoFx, CommonData.CLIP_TIMELINE_FX);
        this.clipType = clipType;
        this.desc = desc;
        this.outPoint = inPoint + duration;
        this.inPoint = inPoint;
    }

    @Override
    public KeyFrameProcessor<NvsTimelineVideoFx> keyFrameProcessor() {
        if (mKeyFrameHolder == null) {
            mKeyFrameHolder = new KeyFrameProcessor<>(this);
        }
        return mKeyFrameHolder;
    }

    public class ClipFxType {
        /**
         * The timeline fx
         * <p>
         * 特效
         */
        public static final String SUB_TYPE_TIMELINE_FX = "timelineFx";
        /**
         * The timeline filter
         * <p>
         * 滤镜
         */
        public static final String SUB_TYPE_TIMELINE_FILTER = "timelineFilter";
        /**
         * The timeline adjust
         * <p>
         * 调节
         */
        public static final String SUB_TYPE_TIMELINE_ADJUST = "timelineAdjust";

        /**
         * The timeline mosaic
         * <p>
         * 马赛克
         */
        public static final String SUB_TYPE_TIMELINE_MOSAIC = "timelineMosaic";

        /**
         * The timeline blur
         * <p>
         * 模糊
         */
        public static final String SUB_TYPE_TIMELINE_BLUR = "timelineBlur";
    }

    public boolean isBuildFx() {
        return CommonData.TYPE_BUILD_IN.equals(clipType);
    }

    @Override
    public void setInPoint(long inPoint) {
        NvsTimelineVideoFx object = getObject();
        if (object != null) {
            object.changeInPoint(inPoint);
            super.setInPoint(inPoint);
        }
    }

    @Override
    public void setOutPoint(long outPoint) {
        NvsTimelineVideoFx object = getObject();
        if (object != null) {
            object.changeOutPoint(outPoint);
            super.setOutPoint(outPoint);
        }
    }

    public String getIndicate() {
        return indicate;
    }

    public void setIndicate(String indicate) {
        this.indicate = indicate;
        NvsTimelineVideoFx object = getObject();
        if (object != null) {
            object.setTemplateAttachment(NvsConstants.TEMPLATE_KEY_FOOTAGE_FX_GROUP, indicate);
        }
    }

    public List<PointF> getPointList() {
        return pointList;
    }

    public void updatePointList(List<PointF> pointList) {
        this.pointList = pointList;
    }

    public String getClipType() {
        return clipType;
    }

    public void setClipType(String clipType) {
        this.clipType = clipType;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getClipSubType() {
        return clipSubType;
    }

    public void setClipSubType(int clipSubType) {
        this.clipSubType = clipSubType;
    }

    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public float getIntensity() {
        return intensity;
    }

    public void setIntensity(float intensity) {
        if (Float.isNaN(intensity)) {
            return;
        }
        NvsTimelineVideoFx object = getObject();
        if (object != null) {
            object.setFilterIntensity(intensity);
            this.intensity = intensity;
        }
    }

    public boolean isRegional() {
        return isRegional;
    }

    public void setRegional(boolean regional) {
        NvsTimelineVideoFx object = getObject();
        if (object != null) {
            object.setRegional(regional);
            isRegional = regional;
        }
    }

    public void setRegionData(float[] regionData) {
        if (regionData == null) {
            return;
        }

        NvsTimelineVideoFx object = getObject();
        if (object == null) {
            LogUtils.e("object is null");
            return;
        }
        object.setRegion(regionData);
        this.regionData = regionData;
        setRegional(true);
    }

    public float[] getRegionData() {
        return regionData;
    }

    public boolean isIgnoreBackground() {
        return isIgnoreBackground;
    }

    public void setIgnoreBackground(boolean ignoreBackground) {
        NvsTimelineVideoFx object = getObject();
        if (object != null) {
            object.setIgnoreBackground(ignoreBackground);
            isIgnoreBackground = ignoreBackground;
        }
    }

    public boolean isInverseRegion() {
        return isInverseRegion;
    }

    public void setInverseRegion(boolean inverseRegion) {
        NvsTimelineVideoFx object = getObject();
        if (object != null) {
            object.setInverseRegion(inverseRegion);
            isInverseRegion = inverseRegion;
        }
    }

    public float getRegionalFeatherWidth() {
        return regionalFeatherWidth;
    }

    public void setRegionalFeatherWidth(float regionalFeatherWidth) {
        NvsTimelineVideoFx object = getObject();
        if (object != null) {
            object.setRegionalFeatherWidth(regionalFeatherWidth);
            this.regionalFeatherWidth = regionalFeatherWidth;
        }
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayNamezhCN() {
        return displayNamezhCN;
    }

    public void setDisplayNamezhCN(String displayNamezhCN) {
        this.displayNamezhCN = displayNamezhCN;
    }

    @Deprecated
    public List<MeicamFxParam<?>> getMeicamFxParamList() {
        return null;
    }

    /**
     * Set String value into fxParam map
     * * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value.
     * 设置String类型的特效参数，如果有这个key值，新的会替换旧的
     *
     * @param key   key
     * @param value value
     */
    public void setStringVal(String key, String value) {
        MeicamFxParam<String> param = new MeicamFxParam<>(TYPE_STRING, key, value);
        mMeicamFxParam.put(param.getKey(), param);
        NvsTimelineVideoFx object = getObject();
        if (object != null) {
            object.setStringVal(key, value);
        }
    }

    /**
     * Get String val from fxParam map
     * <p>
     * 从fxParam中获取String类型的值
     *
     * @param key key
     * @return value
     */
    public String getStringVal(String key) {
        MeicamFxParam<?> meicamFxParam = mMeicamFxParam.get(key);
        if (meicamFxParam == null) {
            return null;
        }
        if (TYPE_STRING.equals(meicamFxParam.getType())) {
            return (String) meicamFxParam.getValue();
        }
        return null;
    }

    public void setPosition2DVal(String key, MeicamPosition2D value) {

        NvsTimelineVideoFx object = getObject();
        if (object != null) {
            object.setPosition2DVal(key, new NvsPosition2D(value.x, value.y));
        }
        MeicamFxParam<MeicamPosition2D> param = new MeicamFxParam<>(TYPE_POSITION_2D, key, value);
        mMeicamFxParam.put(param.getKey(), param);
    }

    public MeicamPosition2D getPosition2DVal(String key) {
        MeicamFxParam<?> meicamFxParam = mMeicamFxParam.get(key);
        if (meicamFxParam == null) {
            return null;
        }
        if (TYPE_POSITION_2D.equals(meicamFxParam.getType())) {
            return (MeicamPosition2D) meicamFxParam.getValue();
        }
        return null;
    }

    /**
     * Set Menu value into fxParam map
     * * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value.
     * 设置Menu类型的特效参数，如果有这个key值，新的会替换旧的
     *
     * @param key   key
     * @param value value
     */
    public void setMenuVal(String key, String value) {
        MeicamFxParam<String> param = new MeicamFxParam<>(TYPE_MENU, key, value);
        mMeicamFxParam.put(param.getKey(), param);
        NvsTimelineVideoFx object = getObject();
        if (object != null) {
            object.setMenuVal(key, value);
        }
    }

    /**
     * Get String val from fxParam map
     * <p>
     * 从fxParam中获取String类型的值
     *
     * @param key key
     * @return value
     */
    public String getMenuVal(String key) {
        MeicamFxParam<?> meicamFxParam = mMeicamFxParam.get(key);
        if (meicamFxParam == null) {
            return null;
        }
        if (TYPE_MENU.equals(meicamFxParam.getType())) {
            return (String) meicamFxParam.getValue();
        }
        return null;
    }


    /**
     * Get float val from fxParam map
     * <p>
     * 从fxParam中获取float类型的值
     *
     * @param key key
     * @return value
     */
    public Float getFloatVal(String key) {
        MeicamFxParam<?> meicamFxParam = mMeicamFxParam.get(key);
        if (meicamFxParam == null) {
            return INVALID_VALUE;
        }
        if (TYPE_FLOAT.equals(meicamFxParam.getType())) {
            Object value = meicamFxParam.getValue();
            if (value instanceof Float) {
                return (float) value;
            } else if (value instanceof Double) {
                double resultD = (double) value;
                return (float) resultD;
            }
        }
        return INVALID_VALUE;
    }

    /**
     * Gets float val.
     * 从fxParam中获取float类型的值
     *
     * @param key          the key
     * @param defaultValue the default value
     * @return the float val
     */
    public Float getFloatVal(String key, float defaultValue) {
        Float floatVal = getFloatVal(key);
        return floatVal == INVALID_VALUE ? defaultValue : floatVal;
    }


    /**
     * Get boolean val from fxParam map
     * <p>
     * 从fxParam中获取boolean类型的值
     *
     * @param key key
     * @return value
     */
    public boolean getBooleanVal(String key) {
        MeicamFxParam<?> meicamFxParam = mMeicamFxParam.get(key);
        if (meicamFxParam == null) {
            return false;
        }
        if (TYPE_BOOLEAN.equals(meicamFxParam.getType())) {
            Object value = meicamFxParam.getValue();
            if (value instanceof Boolean) {
                return (boolean) value;
            }
        }
        return false;
    }

    /**
     * Get  val from fxParam map
     * <p>
     * 从fxParam中是否存在该值
     *
     * @param key key
     * @return value
     */
    public boolean getParamVal(String key) {
        MeicamFxParam<?> meicamFxParam = mMeicamFxParam.get(key);
        return meicamFxParam != null;
    }

    /**
     * Set Boolean value into fxParam map
     * * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value.
     * 设置Boolean类型的特效参数，如果有这个key值，新的会替换旧的
     *
     * @param key   key
     * @param value value
     */
    public void setBooleanVal(String key, boolean value) {
        MeicamFxParam<Boolean> param = new MeicamFxParam<>(TYPE_BOOLEAN, key, value);
        mMeicamFxParam.put(param.getKey(), param);
        NvsTimelineVideoFx object = getObject();
        if (object != null) {
            object.setBooleanVal(key, value);
        }
    }

    /**
     * Set float value into fxParam map
     * * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value.
     * 设置float类型的特效参数，如果有这个key值，新的会替换旧的
     *
     * @param key   key
     * @param value value
     */
    public void setFloatVal(String key, float value) {
        if (Float.isNaN(value)) {
            //防止gson解析报错,IllegalArgumentException: JSON forbids NaN and infinities: NaN
            //To prevent gson parsing from reporting errors, IllegalArgumentException:
            // JSON forces NaN and infinities: NaN
            value = 0f;
        }
        Log.e("tell", "setFloatVal: key = "+key + ", value = "+value );
        MeicamFxParam<Float> param = new MeicamFxParam<>(TYPE_FLOAT, key, value);
        mMeicamFxParam.put(param.getKey(), param);
        NvsTimelineVideoFx object = getObject();
        if (object != null) {
            object.setFloatVal(key, value);
        }
    }

    /**
     * Set Int value into fxParam map
     * * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value.
     * 设置int类型的特效参数，如果有这个key值，新的会替换旧的
     *
     * @param key   key
     * @param value value
     */
    public void setIntVal(String key, int value) {
        if (Float.isNaN(value)) {
            //防止gson解析报错,IllegalArgumentException: JSON forbids NaN and infinities: NaN
            //To prevent gson parsing from reporting errors, IllegalArgumentException:
            // JSON forces NaN and infinities: NaN
            value = 0;
        }
        MeicamFxParam<Integer> param = new MeicamFxParam<>(TYPE_INT, key, value);
        mMeicamFxParam.put(param.getKey(), param);
        NvsTimelineVideoFx object = getObject();
        if (object != null) {
            object.setIntVal(key, value);
        }
    }

    /**
     * Get int val from fxParam map
     * <p>
     * 从fxParam中获取int类型的值
     *
     * @param key key
     * @return value
     */
    public int getIntVal(String key) {
        MeicamFxParam<?> meicamFxParam = mMeicamFxParam.get(key);
        if (meicamFxParam == null) {
            return INVALID_VALUE_INT;
        }
        if (TYPE_INT.equals(meicamFxParam.getType())) {
            Object value = meicamFxParam.getValue();
            if (value instanceof Integer) {
                return (int) value;
            }
        }
        return INVALID_VALUE_INT;
    }

    /**
     * 设置颜色
     * Set color
     *
     * @param key   key 键值
     * @param value color 颜色
     */
    public void setColor(String key, String value) {
        if (TextUtils.isEmpty(key) || TextUtils.isEmpty(value)) {
            return;
        }
        setColor(key, ColorUtil.colorToNvsColor(value));
        MeicamFxParam<String> param = new MeicamFxParam<>(TYPE_COLOR, key, value);
        mMeicamFxParam.put(param.getKey(), param);
    }

    /**
     * Get color string.
     * 获取color值
     *
     * @param key the key
     * @return the string
     */
    public String getColor(String key) {
        MeicamFxParam<?> meicamFxParam = mMeicamFxParam.get(key);
        if (meicamFxParam == null) {
            return "";
        }
        if (TYPE_COLOR.equals(meicamFxParam.getType())) {
            Object value = meicamFxParam.getValue();
            if (value instanceof String) {
                return (String) value;
            }
        }
        return "";
    }

    /**
     * 设置颜色
     * Set color
     *
     * @param key   key 键值
     * @param color color 颜色
     */
    private boolean setColor(String key, NvsColor color) {
        NvsTimelineVideoFx object = getObject();
        if (object != null) {
            object.setColorVal(key, color);
            return true;
        }
        return false;
    }

    /**
     * Set Object value into fxParam map.
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value.
     * 给特效参数设置对象，如果有这个key值，新的会替换旧的
     *
     * @param key   key
     * @param value value
     * @param <T>   参数化对象 Parameterized object
     */
    public <T> void setObjectVal(String key, T value) {
        MeicamFxParam<T> param = new MeicamFxParam<>(TYPE_OBJECT, key, value);
        mMeicamFxParam.put(param.getKey(), param);
        NvsTimelineVideoFx object = getObject();
        if (object != null) {
            if (value instanceof NvsMaskRegionInfo) {
                object.setArbDataVal(key, (NvsArbitraryData) value);
            }
        }
    }

    /**
     * Get Object val from fxParam map
     * <p>
     * 从fxParam中获取Object类型的值
     *
     * @param key key
     * @return value
     */
    public Object getObjectVal(String key) {
        MeicamFxParam<?> meicamFxParam = mMeicamFxParam.get(key);
        if (meicamFxParam == null) {
            return null;
        }
        if (TYPE_OBJECT.equals(meicamFxParam.getType())) {
            return meicamFxParam.getValue();
        }
        return null;
    }

    @Deprecated
    public NvsTimelineVideoFx bindToTimeline(NvsTimeline timeline) {
        if (timeline == null) {
            return null;
        }
        NvsTimelineVideoFx nvsTimelineVideoFx;
        if (TYPE_BUILD_IN.equals(clipType)) {
            nvsTimelineVideoFx = timeline.addBuiltinTimelineVideoFx(getInPoint(), getOutPoint() - getInPoint(), desc);
        } else {
            nvsTimelineVideoFx = timeline.addPackagedTimelineVideoFx(getInPoint(), getOutPoint() - getInPoint(), desc);
        }
        setObject(nvsTimelineVideoFx);
        /*还原关键帧
        * Restore key frame
        * */
        keyFrameProcessor().bindToTimeline();
        return nvsTimelineVideoFx;
    }

    @Deprecated
    public void loadData(NvsTimelineVideoFx effect) {
        if (effect == null) {
            return;
        }
        setObject(effect);
        setInPoint(effect.getInPoint());
        setOutPoint(effect.getOutPoint());
        setIndicate(effect.getTemplateAttachment(TEMPLATE_KEY_FOOTAGE_FX_GROUP));
        if (TYPE_BUILD_IN.equals(clipType)) {
            setDesc(effect.getBuiltinTimelineVideoFxName());
            PlugDetail plugDetail = AtomicFxBridge.getParamListByEffectID(desc);
            if (plugDetail != null) {
                for (PlugDetail.Param param : plugDetail.paramList) {
                    String valueType = param.valueType;
                    String paramName = param.paramName;
                    if (Constants.PlugType.BOOL.equals(valueType)) {
                        //bool
                        boolean nvsValue = effect.getBooleanVal(paramName);
                        setBooleanVal(paramName, nvsValue);
                    } else if (Constants.PlugType.FLOAT.equals(valueType)) {
                        //float
                        float nvsValue = (float) effect.getFloatVal(paramName);
                        setFloatVal(paramName, nvsValue);
                    } else if (Constants.PlugType.PATH.equals(valueType)
                            || Constants.PlugType.STRING.equals(valueType)
                            || Constants.PlugType.CURVE.equals(valueType)) {
                        String nvsValue = effect.getStringVal(paramName);
                        setStringVal(paramName, nvsValue);
                    } else if (Constants.PlugType.COLOR.equals(valueType)) {
                        NvsColor nvsColor = effect.getColorVal(paramName);
                        if (nvsColor != null) {
                            String color = ColorUtil.nvsColorToHexString(nvsColor);
                            setColor(paramName, color);
                        }
                    } else if (Constants.PlugType.MENU.equals(valueType)) {
                        String nvsValue = effect.getMenuVal(paramName);
                        setMenuVal(paramName, nvsValue);
                    } else if (Constants.PlugType.INT.equals(valueType) ||
                            Constants.PlugType.INT_CHOOSE.equals(valueType)) {
                        int nvsValue = effect.getIntVal(paramName);
                        setIntVal(paramName, nvsValue);
                    }
                }
            }
        } else {
            setDesc(effect.getTimelineVideoFxPackageId());
        }
        keyFrameProcessor().recoverFromTimelineData(getObject());
    }

    @Override
    @NonNull
    public MeicamTimelineVideoFxClip clone() {
        MeicamTimelineVideoFxClip clone = (MeicamTimelineVideoFxClip) DeepCopyUtil.deepClone(this);
        if (clone == null) {
            String jsonData = GsonContext.getInstance().toJson(this);
            if (!TextUtils.isEmpty(jsonData)) {
                return GsonContext.getInstance().fromJson(jsonData, MeicamTimelineVideoFxClip.class);
            }
        }
        if (clone == null) {
            clone = new MeicamTimelineVideoFxClip(null, getClipType(), getInPoint(), getOutPoint() - getInPoint(), getDesc());
        }
        return clone;
    }

    /**
     * Set point list.
     * This method requires NvsLiveWindowExt to transform normalized points to View points
     * <p>
     * 设置坐标点，这个方法需要借助NvsLiveWindowExt转换
     *
     * @param mLiveWindow NvsLiveWindowExt
     */
    public void updatePointList(NvsLiveWindowExt mLiveWindow) {
        float[] point = new float[8];

        Object value = getObjectVal(KEY_REGION);
        if (value instanceof ArrayList) {
            List<Double> valueList = new ArrayList<>();
            ArrayList<?> data = (ArrayList<?>) value;
            for (Object object : data) {
                if (object instanceof Double) {
                    valueList.add((Double) object);
                }
            }
            for (int j = 0; j < valueList.size(); j++) {
                point[j] = Float.parseFloat(valueList.get(j).toString());
            }
        } else if (value instanceof float[]) {
            point = (float[]) value;
        }
        if (pointList == null) {
            pointList = new ArrayList<>();
        } else {
            pointList.clear();
        }
        PointF leftTop = new PointF(point[0], point[1]);
        PointF leftBottom = new PointF(point[2], point[3]);
        PointF rightBottom = new PointF(point[4], point[5]);
        PointF rightTop = new PointF(point[6], point[7]);
        pointList.add(mLiveWindow.mapNormalizedToView(leftTop));
        pointList.add(mLiveWindow.mapNormalizedToView(leftBottom));
        pointList.add(mLiveWindow.mapNormalizedToView(rightBottom));
        pointList.add(mLiveWindow.mapNormalizedToView(rightTop));
    }

    @Override
    public void setZValue(float zValue) {
        NvsTimelineVideoFx object = getObject();
        if (object != null) {
            object.setZValue(zValue);
            super.setZValue(zValue);
        }
    }

    @Override
    public float getZValue() {
        NvsTimelineVideoFx object = getObject();
        if (object != null) {
            return object.getZValue();
        }
        return super.getZValue();
    }

    public MeicamMaskRegionInfo getRegionInfo() {
        return regionInfo;
    }

    public void setRegionInfo(MeicamMaskRegionInfo regionInfo) {
        NvsTimelineVideoFx object = getObject();
        if (object != null) {
            object.setRegionInfo(regionInfo.getMaskRegionInfo());
            this.regionInfo = regionInfo;
        }
    }

    @Override
    public String getExtraTag() {
        return getTrackIndex() + "|" + getIndex() + "|" + getDesc() + "|" + getSubType();
    }

    @Override
    public LMeicamTimelineVideoFxClip parseToLocalData() {
        LMeicamTimelineVideoFxClip local = new LMeicamTimelineVideoFxClip();
        setCommonData(local);
        local.setClipType(getClipType());
        local.setSubType(getSubType());
        local.setDesc(getDesc());
        local.setClipSubType(getClipSubType());
        local.setIntensity(getIntensity());
        local.setRegional(isRegional());
        local.setIgnoreBackground(isIgnoreBackground());
        local.setInverseRegion(isInverseRegion());
        local.setRegionalFeatherWidth((int) getRegionalFeatherWidth());
        local.setDisplayName(getDisplayName());
        local.setDisplayNamezhCN(getDisplayNamezhCN());
        local.setRegionData(getRegionData());
        local.setzValue(getZValue());
        local.setIndicate(getIndicate());
        Set<String> keys = mMeicamFxParam.keySet();
        if (!mMeicamFxParam.isEmpty()) {
            for (String key : keys) {
                MeicamFxParam<?> meicamFxParam = mMeicamFxParam.get(key);
                if (meicamFxParam != null) {
                    local.getMeicamFxParamList().add(meicamFxParam.parseToLocalData());
                }
            }
        }
        if (regionInfo != null) {
            local.setRegionInfo(regionInfo.parseToLocalData());
        }
        local.setKeyFrameProcessor(keyFrameProcessor().parseToLocalData());
        return local;
    }

    @Override
    public void recoverFromLocalData(LMeicamTimelineVideoFxClip local) {
        setIndex(local.getIndex());
        setInPoint(local.getInPoint());
        setOutPoint(local.getOutPoint());
        setSubType(local.getSubType());
        setType(local.getType());
        setCreateTag(local.getCreateTag());
        String clipType = local.getClipType();
        if ("0".equals(clipType)) {
            clipType = TYPE_BUILD_IN;
        } else if ("1".equals(clipType)) {
            clipType = TYPE_PACKAGE;
        }
        setClipType(clipType);
        setDesc(local.getDesc());
        setClipSubType(local.getClipSubType());
        setIntensity(local.getIntensity());
        setRegional(local.isRegional());
        setRegionData(local.getRegionData());
        setIgnoreBackground(local.isIgnoreBackground());
        setInverseRegion(local.isInverseRegion());
        setRegionalFeatherWidth(local.getRegionalFeatherWidth());
        setDisplayName(local.getDisplayName());
        setDisplayNamezhCN(local.getDisplayNamezhCN());
        setZValue(local.getzValue());
        setIndicate(local.getIndicate());
        List<LMeicamFxParam<?>> meicamFxParamList = local.getMeicamFxParamList();
        if (!CommonUtils.isEmpty(meicamFxParamList)) {
            for (LMeicamFxParam<?> meicamFxParam : meicamFxParamList) {
                setValue(meicamFxParam);
            }
        }
        LMeicamMaskRegionInfo localRegionInfo = local.getRegionInfo();
        if (localRegionInfo != null) {
            MeicamMaskRegionInfo regionInfo = new MeicamMaskRegionInfo();
            regionInfo.recoverFromLocalData(localRegionInfo);
            setRegional(true);
            setRegionInfo(regionInfo);
        }
        keyFrameProcessor().recoverFromLocalData(local.getKeyFrameProcessor());
    }

    @Override
    public void recoverFromTimelineData(NvsObject nvsObject) {
        if (nvsObject instanceof NvsTimelineVideoFx) {
            setObject((NvsTimelineVideoFx) nvsObject);
            loadData();
        }
    }

    @Override
    void loadData() {
        NvsTimelineVideoFx nvsTimelineVideoFx = getObject();
        zValue = nvsTimelineVideoFx.getZValue();
        desc = nvsTimelineVideoFx.getTimelineVideoFxType() == NvsTimelineVideoFx.TIMELINE_VIDEOFX_TYPE_BUILTIN ?
                nvsTimelineVideoFx.getBuiltinTimelineVideoFxName() : nvsTimelineVideoFx.getTimelineVideoFxPackageId();
        FileInfoBridge.FileInfo fileInfo = FileInfoBridge.getFileInfo(desc);
        if (fileInfo != null) {
            displayName = fileInfo.fileName;
            if (!TextUtils.isEmpty(fileInfo.fileNameZh)) {
                displayNamezhCN = fileInfo.fileNameZh;
            } else {
                displayNamezhCN = displayName;
            }
        } else {
            Plug plugDetail = AtomicFxBridge.getPlugByEffectID(desc);
            if (plugDetail != null) {
                displayName = plugDetail.getName();
                displayNamezhCN = plugDetail.getName();
            }
        }
        indicate = nvsTimelineVideoFx.getTemplateAttachment(TEMPLATE_KEY_FOOTAGE_FX_GROUP);
        intensity = nvsTimelineVideoFx.getFilterIntensity();
        isRegional = nvsTimelineVideoFx.getRegional();
        regionData = nvsTimelineVideoFx.getRegion();
        clipType = nvsTimelineVideoFx.getTimelineVideoFxType() == NvsTimelineVideoFx.TIMELINE_VIDEOFX_TYPE_BUILTIN ?
                TYPE_BUILD_IN : TYPE_PACKAGE;
        isIgnoreBackground = nvsTimelineVideoFx.getIgnoreBackground();
        isInverseRegion = nvsTimelineVideoFx.getInverseRegion();
        regionalFeatherWidth = nvsTimelineVideoFx.getRegionalFeatherWidth();
        setInPoint(nvsTimelineVideoFx.getInPoint());
        setOutPoint(nvsTimelineVideoFx.getOutPoint());
        String fxName = nvsTimelineVideoFx.getBuiltinTimelineVideoFxName();
        List<String[]> fxParams = NvsConstants.getFxParams(fxName);
        for (String[] fxParam : fxParams) {
            String type = fxParam[0];
            String key = fxParam[1];
            if (TYPE_FLOAT.equals(type)) {
                setFloatVal(key, (float) nvsTimelineVideoFx.getFloatVal(key));
            } else if (TYPE_BOOLEAN.equals(type)) {
                setBooleanVal(key, nvsTimelineVideoFx.getBooleanVal(key));
            } else if (TYPE_STRING.equals(type)) {
                setStringVal(key, nvsTimelineVideoFx.getStringVal(key));
            } else if (TYPE_OBJECT.equals(type)) {
                if (NvsConstants.KEY_MASK_REGION_INFO.equals(key)) {
                    MeicamMaskRegionInfo maskRegionInfo = new MeicamMaskRegionInfo();
                    maskRegionInfo.setRegionInfo((NvsMaskRegionInfo) nvsTimelineVideoFx.getArbDataVal(key));
                    setObjectVal(key, maskRegionInfo);
                } else if (NvsConstants.Mosic.REGION.equals(key)) {
                    setObjectVal(key, nvsTimelineVideoFx.getArbDataVal(key));
                }
            }
        }
        NvsMaskRegionInfo regionInfo = nvsTimelineVideoFx.getRegionInfo();
        if (regionInfo != null) {
            MeicamMaskRegionInfo maskRegionInfo = new MeicamMaskRegionInfo();
            maskRegionInfo.setRegionInfo(regionInfo);
            setRegionInfo(maskRegionInfo);
        }
        keyFrameProcessor().recoverFromTimelineData(getObject());
    }

    private void setValue(LMeicamFxParam<?> fxParam) {
        if (TYPE_STRING.equalsIgnoreCase(fxParam.getType())) {
            setStringVal(fxParam.getKey(), (String) fxParam.getValue());
        } else if (TYPE_POSITION_2D.equals(fxParam.getType())) {
            try {
                String json = GsonUtils.toJson(fxParam.getValue());
                MeicamPosition2D value = GsonUtils.fromJson(json, MeicamPosition2D.class);
                setPosition2DVal(fxParam.getKey(), value);
            } catch (Exception e) {
                LogUtils.e("error:" + e.getMessage());
            }

        } else if (TYPE_BOOLEAN.equals(fxParam.getType())) {
            setBooleanVal(fxParam.getKey(), (Boolean) fxParam.getValue());
        } else if (TYPE_FLOAT.equals(fxParam.getType())) {
            Object value = fxParam.getValue();
            if (value instanceof Float) {
                float floatValue = (float) value;
                setFloatVal(fxParam.getKey(), floatValue);
            } else if (value instanceof Double) {
                Double dValue = (Double) value;
                Float fValue = new Float(dValue);
                setFloatVal(fxParam.getKey(), fValue);
            }
        } else if (TYPE_OBJECT.equals(fxParam.getType())) {
            setObjectVal(fxParam.getKey(), fxParam.getValue());
        } else if (TYPE_MENU.equals(fxParam.getType())) {
            setMenuVal(fxParam.getKey(), (String) fxParam.getValue());
        } else if (TYPE_INT.equals(fxParam.getType())) {
            Object value = fxParam.getValue();
            double dValue = (Double) value;
            setIntVal(fxParam.getKey(), (int) dValue);
        } else if (TYPE_COLOR.equals(fxParam.getType())) {
            setColor(fxParam.getKey(), (String) fxParam.getValue());
        }
    }

    private void setValue(MeicamFxParam<?> fxParam) {
        if (TYPE_STRING.equals(fxParam.getType())) {
            setStringVal(fxParam.getKey(), (String) fxParam.getValue());
        } else if (TYPE_POSITION_2D.equals(fxParam.getType())) {
            setPosition2DVal(fxParam.getKey(), (MeicamPosition2D) fxParam.getValue());
        } else if (TYPE_BOOLEAN.equals(fxParam.getType())) {
            setBooleanVal(fxParam.getKey(), (Boolean) fxParam.getValue());
        } else if (TYPE_FLOAT.equals(fxParam.getType())) {
            Object value = fxParam.getValue();
            if (value instanceof Float) {
                float floatValue = (float) value;
                setFloatVal(fxParam.getKey(), floatValue);
            } else if (value instanceof Double) {
                Double dValue = (Double) value;
                Float fValue = new Float(dValue);
                setFloatVal(fxParam.getKey(), fValue);
            }
        } else if (TYPE_OBJECT.equals(fxParam.getType())) {
            setObjectVal(fxParam.getKey(), fxParam.getValue());
        } else if (TYPE_MENU.equals(fxParam.getType())) {
            setMenuVal(fxParam.getKey(), (String) fxParam.getValue());
        } else if (TYPE_INT.equals(fxParam.getType())) {
            Object value = fxParam.getValue();
            if (value instanceof Double) {
                double dValue = (Double) value;
                setIntVal(fxParam.getKey(), (int) dValue);
            } else if (value instanceof Integer) {
                setIntVal(fxParam.getKey(), (Integer) value);
            } else if (value instanceof Float) {
                float fValue = (Float) value;
                setIntVal(fxParam.getKey(), (int) fValue);
            }
        } else if (TYPE_COLOR.equals(fxParam.getType())) {
            setColor(fxParam.getKey(), (String) fxParam.getValue());
        }
    }

    public void copyData(MeicamTimelineVideoFxClip fxClip) {
        setDisplayName(fxClip.getDisplayName());
        setSubType(fxClip.getSubType());
        setIntensity(fxClip.getIntensity());
        setDisplayNamezhCN(fxClip.getDisplayNamezhCN());
        setRegionData(fxClip.getRegionData());
        setIndicate(fxClip.getIndicate());
        setCreateTag(fxClip.getCreateTag());
        Set<String> keys = fxClip.mMeicamFxParam.keySet();
        if (!CommonUtils.isEmpty(keys)) {
            for (String key : keys) {
                MeicamFxParam<?> meicamFxParam = fxClip.mMeicamFxParam.get(key);
                if (meicamFxParam != null) {
                    setValue(meicamFxParam);
                }
            }
        }
    }
}
