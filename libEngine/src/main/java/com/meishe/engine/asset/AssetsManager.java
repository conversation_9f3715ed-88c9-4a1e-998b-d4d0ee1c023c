package com.meishe.engine.asset;

import android.content.Context;
import android.content.res.AssetManager;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsAssetPackageManager;
import com.meicam.sdk.NvsStreamingContext;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.GsonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.NetUtils;
import com.meishe.base.utils.ResourceUtils;
import com.meishe.base.utils.ThreadUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;
import com.meishe.base.utils.ZipUtils;
import com.meishe.engine.EngineNetApi;
import com.meishe.engine.R;
import com.meishe.engine.asset.bean.AssetDownloadInfo;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.bean.AssetList;
import com.meishe.engine.asset.bean.BackgrondBean;
import com.meishe.engine.asset.bean.CloudPathMapBean;
import com.meishe.engine.asset.bean.PackageAssetList;
import com.meishe.engine.asset.bean.RequestParam;
import com.meishe.engine.asset.bean.TemplateUploadParam;
import com.meishe.engine.asset.observer.AssetObservable;
import com.meishe.engine.asset.observer.AssetObserver;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.db.AssetDao;
import com.meishe.engine.db.AssetEntity;
import com.meishe.engine.db.DbManager;
import com.meishe.engine.db.UserAssetsDao;
import com.meishe.engine.db.UserAssetsEntity;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.engine.util.PathUtils;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;
import com.meishe.net.custom.SimpleDownListener;
import com.meishe.net.model.Progress;
import com.meishe.net.server.download.DownloadTask;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * 资源管理者,用于管理各种特效包资源。
 * Resource manager, used to manage various special effects pack resources
 */
public class AssetsManager extends IAssetsManager {
    public static boolean IS_NEW_ASSETS = true;
    public static boolean IS_NEW_API = true;
    private final AssetDao mAssetDao;
    private final UserAssetsDao mAssetUserDao;
    private final AssetObservable mAssetObservable;
    private IAssetsManager mAssetsProxy;
    private final Map<String, AssetList> mAssetsCache = new HashMap<>();

    private AssetsManager() {
        mAssetObservable = new AssetObservable();
        mAssetDao = DbManager.get().getAssetDao();
        mAssetUserDao = DbManager.get().getUserDao();
        if (IS_NEW_ASSETS) {
            mAssetsProxy = new AssetsUserManager();
        } else {
            mAssetsProxy = new AssetsProxy();
        }
    }

    public void setAssetsProxy(IAssetsManager assetsProxy) {
        mAssetsProxy = assetsProxy;
    }

    @Override
    public int[] getAspectRatio(float ratio) {
        return mAssetsProxy.getAspectRatio(ratio);
    }


    public void getAssetsList(RequestParam param, int aspectRatio, int ratioFlag, final int page,
                              int pageSize, boolean needUpdate,
                              final AssetsRequestCallback<AssetList> callback) {
        String token = "";
        IUserPlugin userPlugin = null;
        if (mAssetsProxy != null) {
            userPlugin = mAssetsProxy.getUserPlugin();
        }
        if (userPlugin != null) {
            token = userPlugin.getToken();
        }
        final int assetType = param.type;
        final int categoryId = param.categoryId;
        final int kind = param.kind;
        String paramKey = String.valueOf(param.hashCode());
        final String key = token + paramKey;
        //先拿缓存
        //Get data from cache first.
        if (!needUpdate) {
            AssetList assetList = mAssetsCache.get(key);
            if (assetList != null) {
                int totalNow = (page + 1) * pageSize;
                if (totalNow > assetList.total) {
                    totalNow = assetList.total;
                }
                List<AssetInfo> realAssetList = assetList.realAssetList;
                //有数据，并且数据包含请求的数据，不需要刷新，直接返回
                //There is data, and the data contains the requested data.
                // It does not need to be refreshed, and it is returned directly.
                if (realAssetList != null && realAssetList.size() >= totalNow) {
                    LogUtils.d("getAssetsList: total = " + assetList.total
                            + ", totalNow = " + totalNow + ", Size = " + realAssetList.size() + ", page = " + page);
                    if (callback != null) {
                        BaseResponse<AssetList> response = new BaseResponse<>();
                        response.setCode(0);
                        assetList.hasNext = false;
                        response.setData(assetList);
                        callback.onSuccess(response);
                        LogUtils.d("getAssetsList: fromCache");
                        return;
                    }
                }
            }
        }
        //如果无网络，查询数据库
        //If there is no network, query the database
        if (!NetUtils.isNetworkAvailable(Utils.getApp().getApplicationContext())) {
            getAssetsListFromDb(token, param, new AssetCallback() {
                @Override
                public void onSuccess(final List<AssetInfo> assetList) {
                    if (callback != null) {
                        AssetList data = new AssetList();
                        data.hasNext = false;
                        data.type = parseTypeFromNewData(assetType, categoryId, kind);
                        data.realAssetList = assetList;
                        data.total = assetList == null ? 0 : assetList.size();
                        BaseResponse<AssetList> response = new BaseResponse<>();
                        response.setCode(0);
                        response.setData(data);
                        callback.onSuccess(response);
                        mAssetsCache.put(key, data);
                        LogUtils.d("getAssetsList: fromDb");
                    }
                }

                @Override
                public void onFailure() {
                    if (callback != null) {
                        callback.onError(new BaseResponse<AssetList>());
                    }
                }
            });
            return;
        }
        LogUtils.d("getAssetsList: fromNet ////// ");
        //从网络获取 Get assets from net.
        getAssetsList(token, param, aspectRatio, ratioFlag, page, pageSize, new RequestCallback<AssetList>() {
            @Override
            public void onSuccess(BaseResponse<AssetList> response) {
                AssetList data = response.getData();
                if (data == null) {
                    return;
                }
                AssetList list = mAssetsCache.get(key);
                if (list == null) {
                    list = new AssetList();
                    list.type = data.type;
                    list.total = data.total;
                    mAssetsCache.put(key, list);
                }
                if (list.realAssetList == null) {
                    list.realAssetList = new ArrayList<>();
                } else {
                    if (page == 0) {
                        list.realAssetList.clear();
                    }
                }
                if (data.realAssetList != null) {
                    list.realAssetList.addAll(data.realAssetList);
                }
                if (callback != null) {
                    callback.onSuccess(response);
                }
            }

            @Override
            public void onError(BaseResponse<AssetList> response) {
                if (callback != null) {
                    callback.onError(response);
                }
            }
        });
    }

    @Override
    void getAssetsList(String token, RequestParam param, int aspectRatio, int ratioFlag, int page, int pageSize, RequestCallback<AssetList> callback) {
        if (mAssetsProxy != null) {
            mAssetsProxy.getAssetsList(token, param, aspectRatio, ratioFlag, page, pageSize, callback);
        }
    }

    @Override
    public void getLocalAssetList(RequestParam param, final AssetCallback callback) {
        if (mAssetsProxy != null) {
            mAssetsProxy.getLocalAssetList(param, callback);
        }
    }

    @Override
    public String getErrorMsg(Context context, int type) {
        if (mAssetsProxy != null) {
            return mAssetsProxy.getErrorMsg(context, type);
        }
        return "";
    }

    public void clearCache() {
        mAssetsCache.clear();
    }

    private final static class Holder {
        private final static AssetsManager INSTANCE = new AssetsManager();
    }

    public static AssetsManager get() {
        return Holder.INSTANCE;
    }

    /**
     * 初始化
     * init
     */
    public void init(boolean isToC) {
        adaptOldVersion();
        if (!isToC) {
            mAssetsProxy.setUserPlugin(PluginManager.get().getUserPlugin());
        }
        installFont();
        installARSenseEffect();
        installAllCaptureAssets();
    }

    /**
     * 安装拍摄的资源
     * install capture assets
     */
    private void installAllCaptureAssets() {
        getLocalAssetList(AssetInfo.ASSET_PACKAGE_TYPE_FACE_MESH, null);
        getLocalAssetList(AssetInfo.ASSET_PACKAGE_TYPE_FACE_WARP, null);
    }

    /**
     * 安装所有道具特效包
     * Install ar sense effect.
     */
    public void installARSenseEffect() {
        getDatabaseAssetList(AssetInfo.ASSET_AR_SCENE_FACE, new AssetCallback() {
            @Override
            public void onSuccess(List<AssetInfo> assetInfoList) {
                if (CommonUtils.isEmpty(assetInfoList)) {
                    return;
                }
                for (AssetInfo assetInfo : assetInfoList) {
                    installAssetPackage(assetInfo, false, false);
                }
            }

            @Override
            public void onFailure() {

            }
        });
    }

    /**
     * 取消任务
     * Cancel the task
     *
     * @param tag the task tag
     */
    public void cancelTask(String tag) {
        EngineNetApi.cancelTask(tag);
    }

    /**
     * 适配旧版本
     * Adapt to older versions
     */
    private void adaptOldVersion() {
        ThreadUtils.getCachedPool().execute(new Runnable() {
            @Override
            public void run() {
                dataConvert(AssetInfo.ASSET_ANIMATION_IN);
                dataConvert(AssetInfo.ASSET_ANIMATION_OUT);
                dataConvert(AssetInfo.ASSET_ANIMATION_GROUP);
                dataConvert(AssetInfo.ASSET_CUSTOM_CAPTION_FLOWER);
                dataConvert(AssetInfo.ASSET_CUSTOM_CAPTION_BUBBLE);
                dataConvert(AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_IN);
                dataConvert(AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_OUT);
                dataConvert(AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_COMBINATION);
                dataConvert(AssetInfo.ASSET_CAPTION_STYLE);
                dataConvert(AssetInfo.ASSET_COMPOUND_CAPTION);
                customStickerConvert();
                watermarkConvert();
                dataConvert(AssetInfo.ASSET_EFFECT_DREAM);
                dataConvert(AssetInfo.ASSET_EFFECT_FRAME);
                dataConvert(AssetInfo.ASSET_EFFECT_LIVELY);
                dataConvert(AssetInfo.ASSET_EFFECT_SHAKING);
                dataConvert(AssetInfo.ASSET_FILTER);
                dataConvert(AssetInfo.ASSET_FONT);
                dataConvert(AssetInfo.ASSET_ANIMATED_STICKER);
                dataConvert(AssetInfo.ASSET_THEME);
                dataConvert(AssetInfo.ASSET_VIDEO_TRANSITION);
                dataConvert(AssetInfo.ASSET_VIDEO_TRANSITION_3D);
                dataConvert(AssetInfo.ASSET_VIDEO_TRANSITION_EFFECT);
            }
        });
    }

    /**
     * 数据转换
     * data transformation
     *
     * @param assetType asset type
     */
    private void dataConvert(int assetType) {
        try {
            File infoFile = new File(PathUtils.getAssetDownloadDir() + File.separator + "info_" + assetType + ".json");
            if (!infoFile.exists()) {
                return;
            }
            LogUtils.d("assetType=" + assetType + ",path = " + infoFile.getAbsolutePath());
            List<File> files = FileUtils.listFilesInDir(PathUtils.getAssetDownloadPath(assetType));
            BufferedReader bufferedReader = new BufferedReader(new FileReader(infoFile));
            StringBuilder assetsInfo = new StringBuilder();
            String temp;
            while ((temp = bufferedReader.readLine()) != null) {
                assetsInfo.append(temp);
            }
            JSONObject jsonObject = new JSONObject(assetsInfo.toString());
            Iterator<String> iterator = jsonObject.keys();
            List<AssetEntity> tempAssetList = new ArrayList<>();
            while (iterator.hasNext()) {
                String packageId = (String) iterator.next();
                for (File file : files) {
                    // LogUtils.d("fileName=" + file.getName() + ",packageId=" + packageId);
                    if (file.getName().startsWith(packageId)) {
                        AssetEntity assetEntity = new AssetEntity();
                        assetEntity.setId(packageId);
                        assetEntity.setPackageId(packageId);
                        assetEntity.setAssetPath(file.getAbsolutePath());
                        tempAssetList.add(assetEntity);
                        String name = file.getName();
                        String[] split = name.split("\\.");
                        if (split.length == 3) {
                            assetEntity.setVersion(Integer.parseInt(split[1]));
                        }
                        String assetInfo = jsonObject.getString(packageId);
                        String[] assetList = assetInfo.split(";");
                        for (String str : assetList) {
                            if (str.contains("name:")) {
                                assetEntity.setName(str.replaceAll("name:", ""));
                            } else if (str.contains("coverUrl:")) {
                                assetEntity.setCoverPath(str.replaceAll("coverUrl:", ""));
                            } else if (str.contains("aspectRatio:")) {
                                assetEntity.setSupportedAspectRatio(Integer.parseInt(str.replaceAll("aspectRatio:", "")));
                            } else if (str.contains("assetType:")) {
                                assetEntity.setType(Integer.parseInt(str.replaceAll("assetType:", "")));
                            }
                        }
                        if (assetEntity.getType() == -1) {
                            assetEntity.setType(assetType);
                        }
                        if (existInDB(packageId)) {
                            mAssetDao.updateAsset(assetEntity);
                        } else {
                            mAssetDao.insertAsset(assetEntity);
                        }
                        LogUtils.d("assetEntity=" + assetEntity);
                        break;
                    }
                }
            }
            //过滤，json文件中没有记录的文件全部删除
            //Filter, delete all files not recorded in the json file.
            if (tempAssetList.size() > 0) {
                for (File file : files) {
                    boolean fileExist = false;
                    for (AssetEntity assetEntity : tempAssetList) {
                        if (file.getName().startsWith(assetEntity.getPackageId())) {
                            fileExist = true;
                            break;
                        }
                    }
                    if (!fileExist) {
                        FileUtils.delete(file);
                    }
                }

            }
            //删除旧版本json文件
            //Delete the old version json file.
            FileUtils.delete(infoFile);
        } catch (Exception e) {
            LogUtils.e(e);
            e.printStackTrace();
        }
    }

    /**
     * 自定义贴纸转换
     * Custom sticker transformation
     */
    private void customStickerConvert() {
        File infoFile = new File(PathUtils.getAssetDownloadDir() + File.separator + "customStickerInfo.json");
        try {
            if (!infoFile.exists()) {
                return;
            }
            BufferedReader bufferedReader = new BufferedReader(new FileReader(infoFile));
            StringBuilder assetsInfo = new StringBuilder();
            String temp;
            while ((temp = bufferedReader.readLine()) != null) {
                assetsInfo.append(temp);
            }
            bufferedReader.close();
            JSONObject jsonObject = new JSONObject(assetsInfo.toString());
            Iterator<String> iterator = jsonObject.keys();
            List<AssetEntity> tempAssetList = new ArrayList<>();
            while (iterator.hasNext()) {
                String uuid = (String) iterator.next();
                String assetInfo = jsonObject.getString(uuid);
                AssetEntity assetEntity = new AssetEntity();
                assetEntity.setId(uuid);
                tempAssetList.add(assetEntity);
                //自定义贴纸
                //Custom sticker
                assetEntity.setType(AssetInfo.ASSET_CUSTOM_STICKER);
                String[] assetList = assetInfo.split(";");
                for (String str : assetList) {
                    if (str.contains("templateUuid:")) {
                        assetEntity.setPackageId(str.replaceAll("templateUuid:", ""));
                    } else if (str.contains("imagePath:")) {
                        assetEntity.setCoverPath(str.replaceAll("imagePath:", ""));
                        assetEntity.setAssetPath(assetEntity.getCoverPath());
                    }
                }
                if (FileUtils.isFileExists(assetEntity.getCoverPath())) {
                    if (existInDB(assetEntity.getPackageId())) {
                        mAssetDao.updateAsset(assetEntity);
                    } else {
                        mAssetDao.insertAsset(assetEntity);
                    }
                }

            }
            //自定义贴纸特效包
            //Custom sticker package
            List<File> files = FileUtils.listFilesInDir(PathUtils.getAssetDownloadPath(AssetInfo.ASSET_CUSTOM_STICKER_PACKAGE));
            //再次过滤，json文件中没有记录的文件全部删除
            //Filter again, and delete all the files not recorded in the json file
            if (tempAssetList.size() > 0) {
                for (File file : files) {
                    boolean fileExist = false;
                    for (AssetEntity assetEntity : tempAssetList) {
                        if (file.getName().startsWith(assetEntity.getPackageId()) || file.getAbsolutePath().equals(assetEntity.getAssetPath())) {
                            fileExist = true;
                            break;
                        }
                    }
                    if (!fileExist) {
                        FileUtils.delete(file);
                    }
                }

            }
            //删除旧版本json文件
            //Delete the old version json file
            FileUtils.delete(infoFile);
        } catch (Exception e) {
            LogUtils.e(e);
            e.printStackTrace();
        }
    }

    /**
     * 水印数据转化
     * Watermark data convert
     */
    private void watermarkConvert() {
        final String watermarkDirPath = PathUtils.getWatermarkCafDirectoryDir();
        final File infoFile = new File(watermarkDirPath + "/cache.txt");
        if (!infoFile.exists()) {
            return;
        }
        ThreadUtils.getCachedPool().execute(new Runnable() {
            @Override
            public void run() {
                try {
                    BufferedReader bufferedReader = new BufferedReader(new FileReader(infoFile));
                    StringBuilder assetsInfo = new StringBuilder();
                    String temp;
                    while ((temp = bufferedReader.readLine()) != null) {
                        assetsInfo.append(temp);
                    }
                    bufferedReader.close();
                    JSONObject jsonObject = new JSONObject(assetsInfo.toString());
                    JSONArray watermarkArray = jsonObject.optJSONArray("picturePathName");
                    if (watermarkArray != null) {
                        for (int i = 0; i < watermarkArray.length(); i++) {
                            JSONObject jsonObject1 = watermarkArray.getJSONObject(i);
                            String watermarkPath = jsonObject1.optString("mWaterMarkPath");
                            if (TextUtils.isEmpty(watermarkPath)) {
                                watermarkPath = jsonObject1.optString("mPicPath");
                            }
                            if (!TextUtils.isEmpty(watermarkPath)) {
                                if (FileUtils.isFileExists(watermarkPath)) {
                                    AssetEntity assetEntity = new AssetEntity();
                                    assetEntity.setId(UUID.randomUUID().toString());
                                    assetEntity.setType(AssetInfo.ASSET_WATER);
                                    assetEntity.setAssetPath(watermarkPath);
                                    assetEntity.setCoverPath(watermarkPath);
                                    mAssetDao.insertAsset(assetEntity);
                                }
                            }
                        }
                    }
                    FileUtils.delete(infoFile);
                } catch (Exception e) {
                    LogUtils.e(e);
                }
            }
        });

    }

    /**
     * 查询数据库中的资源包信息。在主线程中执行慎用。
     * Query the resource bundle information in the databas
     *
     * @param id the asset id
     * @return AssetInfo
     */
    public AssetInfo getAssetInfo(String id) {
        AssetEntity dbAsset = mAssetDao.getAsset(id);
        if (dbAsset != null) {
            return AssetInfo.create(dbAsset);
        }
        return null;
    }

    /**
     * 查询数据库中的资源包信息。在主线程中执行慎用。
     * Query the resource bundle information in the databas
     *
     * @param id   the asset id
     * @param type the asset type
     * @return AssetInfo
     */
    public AssetInfo getAssetInfo(String id, int type) {
        AssetEntity dbAsset = mAssetDao.getAsset(id, type);
        if (dbAsset != null) {
            return AssetInfo.create(dbAsset);
        }
        return null;
    }

    /**
     * 资源包是否在数据库中
     * Whether the resource bundle is in the database
     *
     * @param id the asset id
     * @return true in the database,false not
     */
    private boolean existInDB(String id) {
        return mAssetDao.getAsset(id) != null;
    }

    /**
     * 获取组合的本地资源包信息列表(组合资源包是由B和C资源包组合而成的)
     * Gets a list of local resource bundle information for the composition,A composite resource
     * bundle is composed of B and C resource bundles
     *
     * @param type     asset type
     * @param callback the callback
     */
    public void getCombinedLocalAssetList(final int type, final AssetCallback callback) {
        ThreadUtils.getCachedPool().execute(new Runnable() {
            @Override
            public void run() {
                List<AssetEntity> dbAssetList = mAssetDao.getAssetList(type);
                final List<AssetInfo> localList = new ArrayList<>();
                if (dbAssetList != null) {
                    AssetInfo assetInfo;
                    for (AssetEntity item : dbAssetList) {
                        assetInfo = AssetInfo.create(item);
                        if (type == AssetInfo.ASSET_CUSTOM_STICKER) {
                            //自定义贴纸，如果资源路径所对应的文件不存在，则该条记录非法，删除之。
                            //Custom sticker.
                            // If the file corresponding to the resource path does not exist,
                            // the record is illegal and deleted.
                            if (!FileUtils.isFileExists(item.getAssetPath())) {
                                mAssetDao.deleteAsset(item);
                                continue;
                            }

                        }
                        //组合Entity里面并没有特效包路径，只有PackageId，使用它查找到对应特效包进行安装
                        //There is no special effect package path in the composite Entity, only PackageId.
                        // Use it to find the corresponding special effect package for installation.
                        AssetEntity effectAsset = mAssetDao.getAsset(item.getPackageId());
                        if (effectAsset != null) {
                            if (assetFileExist(effectAsset.getAssetPath())) {
                                //安装自定义贴纸的对应的特效包
                                //Install the special effect package corresponding to the custom sticker
                                installAssetPackage(AssetInfo.create(effectAsset), false, false);
                            } else {
                                //两条记录都非法，删除之
                                //Both records are illegal, delete them
                                mAssetDao.deleteAsset(effectAsset);
                                mAssetDao.deleteAsset(item);
                                continue;
                            }
                        }
                        if (checkInvalid(assetInfo)) {
                            localList.add(assetInfo);
                        }
                    }
                    if (callback != null) {
                        ThreadUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                callback.onSuccess(localList);
                            }
                        });
                    }
                } else {
                    if (callback != null) {
                        ThreadUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                callback.onFailure();
                            }
                        });
                    }
                }
            }
        });
    }

    /**
     * 资源包文件是否存在
     * Whether the resource bundle file exists
     *
     * @param path the asset path
     */
    private boolean assetFileExist(String path) {
        try {
            if (!TextUtils.isEmpty(path)) {
                if (path.startsWith("assets:/")) {
                    Utils.getApp().getAssets().open(path.replace("assets:/", ""));
                    return true;
                } else {
                    return FileUtils.isFileExists(path);
                }
            }
        } catch (IOException e) {
            LogUtils.e(e);
            //如果没有文件，肯定会有异常,自然就返回false,
            //也可以获取列表遍历查找的方式判断文件是否存在
            //If there is no file, there must be an exception and false will be returned,
            // You can also obtain the list traversal search method to determine whether the file exists.
        }
        return false;
    }


    /**
     * 获取本地资源包信息列表,
     * Gets a list of local resource bundle information
     *
     * @param type     asset type
     * @param callback the callback
     */
    public void getLocalAssetList(final int type, final AssetCallback callback) {
        ThreadUtils.getCachedPool().execute(new Runnable() {
            @Override
            public void run() {
                //数据库中的数据
                //Data in the database
                List<AssetEntity> dbAssetList = mAssetDao.getAssetList(type);
                //包内asset目录下的数据
                //Data under the asset directory in the package
                List<AssetInfo> assetList = getPackageAssetList(type);
                final List<AssetInfo> localList = handleDBDataAndBuildInData(dbAssetList, assetList, type);
                /*
                 * 检查下载目录中手动添加的数据
                 * Check the manually added data in the download directory
                 */
                checkDownloadDir(localList, type);
                if (CommonUtils.isEmpty(localList)) {
                    if (callback != null) {
                        ThreadUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                callback.onSuccess(localList);
                            }
                        });
                    }
                } else {
                    if (callback != null) {
                        ThreadUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                callback.onFailure();
                            }
                        });
                    }
                }
            }
        });
    }

    /**
     * 获取包内asset目录下的资源列表
     * Gets a list of resources in the asset directory within the package
     *
     * @param type the asset type
     */
    public List<AssetInfo> getPackageAssetList(int type) {
        String assetPath = getAssetPath(type);
        if (!TextUtils.isEmpty(assetPath)) {
            try {
                String json = ResourceUtils.readAssets2String(assetPath + "/info.json");
                if (!TextUtils.isEmpty(json)) {
                    PackageAssetList packageAssetList = GsonUtils.fromJson(json, PackageAssetList.class);
                    return packageAssetList == null ? null : packageAssetList.getAssetList();
                }
            } catch (Exception e) {
                return null;
            }

        }
        return null;
    }


    /**
     * 获取数据库中资源列表
     * Gets a list of resources in the database
     *
     * @param type the asset type
     */
    public void getDatabaseAssetList(final int type, final AssetCallback callback) {
        ThreadUtils.getCachedPool().execute(new Runnable() {
            @Override
            public void run() {
                List<AssetEntity> dbAssetList = mAssetDao.getAssetList(type);
                final List<AssetInfo> dbList = new ArrayList<>();
                if (dbAssetList != null) {
                    for (AssetEntity item : dbAssetList) {
                        if (FileUtils.isFileExists(item.getAssetPath())) {
                            dbList.add(AssetInfo.create(item));
                        } else {
                            mAssetDao.deleteAsset(item);
                        }
                    }
                }
                if (callback != null) {
                    ThreadUtils.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            callback.onSuccess(dbList);
                        }
                    });
                }
            }
        });

    }

    /**
     * 获取包内(apk)资源路径
     * Gets the path to the resources in the package(apk)
     *
     * @param assetType 资源类型 asset type
     * @return the asset path
     **/
    private String getAssetPath(int assetType) {
        String path = "";
        switch (assetType) {
            case AssetInfo.ASSET_ANIMATION_IN:
                path = "animation/in";
                break;
            case AssetInfo.ASSET_ANIMATION_OUT:
                path = "animation/out";
                break;
            case AssetInfo.ASSET_ANIMATION_GROUP:
                path = "animation/group";
                break;
            case AssetInfo.ASSET_CUSTOM_CAPTION_FLOWER:
                path = "captionrichword";
                break;
            case AssetInfo.ASSET_CUSTOM_CAPTION_BUBBLE:
                path = "captionbubble";
                break;
            case AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_IN:
                path = "captionanimation/in";
                break;
            case AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_OUT:
                path = "captionanimation/out";
                break;
            case AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_COMBINATION:
                path = "captionanimation/combination";
                break;
            case AssetInfo.ASSET_CAPTION_STYLE:
                path = "captionstyle";
                break;
            case AssetInfo.ASSET_COMPOUND_CAPTION:
                path = "compoundcaption";
                break;
            case AssetInfo.ASSET_CUSTOM_STICKER_PACKAGE:
                path = "customsticker";
                break;
            case AssetInfo.ASSET_EFFECT_DREAM:
                path = "effect_dream";
                break;
            case AssetInfo.ASSET_EFFECT_FRAME:
                path = "effect_frame";
                break;
            case AssetInfo.ASSET_EFFECT_LIVELY:
                path = "effect_lively";
                break;
            case AssetInfo.ASSET_EFFECT_SHAKING:
                path = "effect_shaking";
                break;
            case AssetInfo.ASSET_FILTER:
                path = "filter";
                break;
            case AssetInfo.ASSET_FONT:
                path = "font";
                break;
            case AssetInfo.ASSET_ANIMATED_STICKER:
                path = "sticker";
                break;
            case AssetInfo.ASSET_THEME:
                path = "theme";
                break;
            case AssetInfo.ASSET_VIDEO_TRANSITION_3D:
                path = "transition_3d";
                break;
            case AssetInfo.ASSET_VIDEO_TRANSITION_EFFECT:
                path = "transition_effect";
                break;
            case AssetInfo.ASSET_PACKAGE_TYPE_FACE_MESH:
                path = "beauty/shapePackage/facemesh";
                break;
            case AssetInfo.ASSET_PACKAGE_TYPE_FACE_WARP:
                path = "beauty/shapePackage/warp";
                break;
        }
        return path;
    }

    /**
     * 根据资源包的类型获取资源包的后缀名
     * Gets the suffix name of a resource bundle based on its type
     *
     * @param assetType 资源类型 asset type
     * @return the asset suffix name
     **/
    private String getAssetSuffixName(int assetType) {
        String nameSuffix = "";
        switch (assetType) {
            case AssetInfo.ASSET_ANIMATION_IN:
            case AssetInfo.ASSET_ANIMATION_OUT:
            case AssetInfo.ASSET_ANIMATION_GROUP:
            case AssetInfo.ASSET_EFFECT_DREAM:
            case AssetInfo.ASSET_EFFECT_FRAME:
            case AssetInfo.ASSET_EFFECT_LIVELY:
            case AssetInfo.ASSET_EFFECT_SHAKING:
            case AssetInfo.ASSET_FILTER:
                nameSuffix = ".videofx";
                break;
            case AssetInfo.ASSET_CUSTOM_CAPTION_FLOWER:
                nameSuffix = ".captionrenderer";
                break;
            case AssetInfo.ASSET_CUSTOM_CAPTION_BUBBLE:
                nameSuffix = ".captioncontext";
                break;
            case AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_IN:
                nameSuffix = ".captioninanimation";
                break;
            case AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_OUT:
                nameSuffix = ".captionoutanimation";
                break;
            case AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_COMBINATION:
                nameSuffix = ".captionanimation";
                break;
            case AssetInfo.ASSET_CAPTION_STYLE:
                nameSuffix = ".captionstyle";
                break;
            case AssetInfo.ASSET_COMPOUND_CAPTION:
                nameSuffix = ".compoundcaption";
                break;
            case AssetInfo.ASSET_CUSTOM_STICKER_PACKAGE:
            case AssetInfo.ASSET_ANIMATED_STICKER:
                nameSuffix = ".animatedsticker";
                break;
            case AssetInfo.ASSET_FONT:
                nameSuffix = ".ttf";
                break;
            case AssetInfo.ASSET_THEME:
                nameSuffix = ".theme";
                break;
            case AssetInfo.ASSET_VIDEO_TRANSITION_3D:
            case AssetInfo.ASSET_VIDEO_TRANSITION_EFFECT:
                nameSuffix = ".videotransition";
                break;
            case AssetInfo.ASSET_PACKAGE_TEMPLATE:
                nameSuffix = ".template";
                break;
            case AssetInfo.ASSET_AR_SCENE_FACE:
                nameSuffix = ".arscene";
                break;
        }
        return nameSuffix;
    }

    /**
     * 检查下载目录是否有额外的资源包，用于支持内部人员手动往下载目录添加资源包进行测试。
     * Check the download directory for additional resource packs,Used to support internal personnel
     * manually add resource packs to the download directory for testing
     *
     * @param assetInfoList asset info list
     * @param assetType     asset type
     */
    private void checkDownloadDir(List<AssetInfo> assetInfoList, int assetType) {
        if (assetInfoList != null) {
            List<File> files = FileUtils.listFilesInDir(PathUtils.getAssetDownloadPath(assetType));
            String suffixName = getAssetSuffixName(assetType);
            if (files != null && files.size() > 0 && !TextUtils.isEmpty(suffixName)) {
                List<AssetInfo> extraList = new ArrayList<>();
                boolean exist = false;
                String licPath = "";
                String packagePath = "";
                String name = "";
                String coverPath = "";
                for (File file : files) {
                    if (!file.isFile()) {
                        for (File tempFile : Objects.requireNonNull(file.listFiles())) {
                            name = tempFile.getName();
                            if (name.endsWith(suffixName)) {
                                packagePath = tempFile.getAbsolutePath();
                            }
                            if (name.endsWith(".lic")) {
                                licPath = tempFile.getAbsolutePath();
                            }
                            if (name.endsWith(".png") || name.endsWith(".webp")) {
                                coverPath = tempFile.getAbsolutePath();
                            }
                        }

                    } else {
                        name = file.getName();
                        if (name.endsWith(suffixName)) {
                            packagePath = file.getAbsolutePath();
                        }
                    }
                }
                for (IBaseInfo assetInfo : assetInfoList) {
                    if (name.startsWith(assetInfo.getPackageId())) {
                        exist = true;
                        break;
                    }
                }
                if (!exist) {
                    AssetInfo assetInfo = new AssetInfo();
                    String[] split = name.split("\\.");
                    if (split.length == 3) {
                        assetInfo.setVersion(Integer.parseInt(split[1]));
                    }
                    assetInfo.setId(split[0]);
                    assetInfo.setType(assetType);
                    assetInfo.setPackageId(split[0]);
                    assetInfo.setEffectMode(BaseInfo.EFFECT_MODE_PACKAGE);
                    assetInfo.setAssetPath(packagePath);
                    assetInfo.setLicPath(licPath);
                    assetInfo.setCoverPath(coverPath);
                    assetInfo.setHadDownloaded(true);
                    extraList.add(assetInfo);
                    installAssetPackage(assetInfo, false, false);
                }
                assetInfoList.addAll(extraList);
            }

        }

    }


    /**
     * 获取资源列表
     * Get asset list
     *
     * @param param       请求参数 Request param
     * @param aspectRatio 资源比例 asset ratio
     * @param page        资源页数 page num
     * @param pageSize    资源单页数量 page size
     * @param callback    结果回调 the callback
     */
    @Deprecated
    public void getAssetList(final RequestParam param, final int aspectRatio, final int page, int pageSize,
                             final RequestCallback<AssetList> callback) {
        final int assetType = param.type;
        final int categoryId = param.categoryId;
        EngineNetApi.getMaterialList(null, getSpecialType(assetType), aspectRatio, getCategoryId(assetType, categoryId)
                , page, pageSize, new RequestCallback<AssetList>() {

                    @Override
                    public void onSuccess(final BaseResponse<AssetList> response) {
                        if (response != null) {
                            ThreadUtils.getCachedPool().execute(new Runnable() {
                                @Override
                                public void run() {
                                    AssetList data = response.getData();
                                    if (data != null) {
                                        data.type = assetType;
                                        data.realAssetList = getAssetInfo(data, -1);
                                        if (page == 0) {
                                            //检查下载目录是否有额外的资源包，用于支持内部人员手动往下载目录添加资源包进行测试。
                                            //Check the download directory for additional resource packs,
                                            // Used to support internal personnel manually add resource packs to
                                            // the download directory for testing
                                            checkLocalData(data, assetType);
                                        }
                                        try {
                                            if (!CommonUtils.isEmpty(data.realAssetList)) {
                                                String paramKey = String.valueOf(param.hashCode());
                                                String realToken = "All";
                                                for (AssetInfo assetInfo : data.realAssetList) {
                                                    UserAssetsEntity entity = UserAssetsEntity.create(realToken, paramKey, assetInfo);
                                                    UserAssetsEntity userAsset = mAssetUserDao.getUserAsset(realToken, paramKey, entity.getAssetsId());
                                                    if (userAsset != null) {
                                                        mAssetUserDao.updateAsset(entity);
                                                    } else {
                                                        mAssetUserDao.insertAsset(entity);
                                                    }
                                                }
                                            }
                                        } catch (Exception e) {
                                            LogUtils.e("e:" + e.getMessage());
                                        }
                                    }
                                    ThreadUtils.runOnUiThread(new Runnable() {
                                        @Override
                                        public void run() {
                                            callback.onSuccess(response);
                                        }
                                    });
                                }
                            });

                        }
                    }

                    @Override
                    public void onError(BaseResponse<AssetList> response) {
                        if (response != null) {
                            callback.onError(response);
                        }
                    }
                });
    }

    /**
     * 获取资源列表
     * Get asset list
     *
     * @param param       请求参数 Request param
     * @param aspectRatio 资源比例 asset ratio
     * @param page        资源页数 page num
     * @param pageSize    资源单页数量 page size
     * @param callback    结果回调 the callback
     */
    public void getBackGroundAssetList(String token, final RequestParam param, final int aspectRatio, final int page, int pageSize,
                                       final RequestCallback<AssetList> callback) {
        final int assetType = param.type;

        EngineNetApi.getMaterialListOldExt(null, token, assetType,
                page, pageSize, false, new RequestCallback<BackgrondBean>() {

                    @Override
                    public void onSuccess(final BaseResponse<BackgrondBean> response) {
                        if (response != null) {
                            ThreadUtils.getCachedPool().execute(new Runnable() {
                                @Override
                                public void run() {
                                    final BackgrondBean data = response.getData();
                                    if (data != null) {
                                        ThreadUtils.runOnUiThread(new Runnable() {
                                            @Override
                                            public void run() {
                                                BaseResponse<AssetList> baseResponse = new BaseResponse<>();
                                                baseResponse.setCode(response.getCode());
                                                baseResponse.setData(data.toAssetsList());
                                                callback.onSuccess(baseResponse);
                                            }
                                        });
                                    }
                                }
                            });
                        }
                    }

                    @Override
                    public void onError(BaseResponse<BackgrondBean> response) {
                        if (response != null) {
                            callback.onError(new BaseResponse<AssetList>());
                        }
                    }
                });
    }


    /**
     * 获取资源列表
     * Get asset list
     *
     * @param token       the token
     * @param param       请求参数 the request param
     * @param aspectRatio 资源比例 asset ratio
     * @param page        资源页数 page num
     * @param pageSize    资源单页数量 page size
     * @param callback    结果回调 the callback
     */

    void getAssetListNew(final String token, final RequestParam param, final int aspectRatio, final int ratioFlag, final int page, int pageSize,
                         final RequestCallback<AssetList> callback) {
        final int assetType = param.type;
        final int subType = param.subType;
        final int categoryId = param.categoryId;
        final int kind = param.kind;
        EngineNetApi.getMaterialList(null, token, assetType, subType, categoryId, kind, param.keyword, aspectRatio, ratioFlag,
                page, pageSize, new RequestCallback<AssetList>() {

                    @Override
                    public void onSuccess(final BaseResponse<AssetList> response) {
                        if (response != null) {
                            ThreadUtils.getCachedPool().execute(new Runnable() {
                                @Override
                                public void run() {
                                    AssetList data = response.getData();
                                    if (data != null) {
                                        int oldType = AssetsManager.parseTypeFromNewData(assetType, categoryId, kind);
                                        data.type = oldType;
                                        data.realAssetList = getAssetInfoEx(data, subType);
                                        if (page == 0) {
                                            checkLocalData(data, oldType);
                                        }
                                        try {
                                            if (!CommonUtils.isEmpty(data.realAssetList)) {
                                                String paramKey = String.valueOf(new RequestParam(assetType, subType, categoryId, kind).hashCode());
                                                String realToken = "All";
                                                if (subType != 0) {
                                                    realToken = token;
                                                }
                                                for (AssetInfo assetInfo : data.realAssetList) {
                                                    UserAssetsEntity entity = UserAssetsEntity.create(realToken, paramKey, assetInfo);
                                                    UserAssetsEntity userAsset = mAssetUserDao.getUserAsset(realToken, paramKey, entity.getAssetsId());
                                                    if (userAsset != null) {
                                                        mAssetUserDao.updateAsset(entity);
                                                    } else {
                                                        mAssetUserDao.insertAsset(entity);
                                                    }
                                                }
                                            }
                                        } catch (Exception e) {
                                            LogUtils.e("e:" + e.getMessage());
                                        }
                                    }
                                    ThreadUtils.runOnUiThread(new Runnable() {
                                        @Override
                                        public void run() {
                                            callback.onSuccess(response);
                                        }
                                    });
                                }
                            });

                        }
                    }

                    @Override
                    public void onError(BaseResponse<AssetList> response) {
                        if (response != null) {
                            callback.onError(response);
                        }
                    }
                });
    }

    /**
     * Check the download directory for additional resource packs,Used to support internal personnel
     * manually add resource packs to the download directory for testing
     * <p></>
     * 检查下载目录是否有额外的资源包，用于支持内部人员手动往下载目录添加资源包进行测试。
     *
     * @param data      data from net
     * @param assetType type of asset
     */
    private void checkLocalData(AssetList data, int assetType) {
        List<File> files = FileUtils.listFilesInDir(PathUtils.getAssetDownloadPath(assetType));
        String suffixName = getAssetSuffixName(assetType);
        if (files.size() > 0 && !TextUtils.isEmpty(suffixName)) {
            List<AssetInfo> extraList = new ArrayList<>();
            String licPath = "";
            String packagePath = "";
            String name;
            String packageId = "";
            String[] split = new String[0];
            for (File file : files) {
                if (!file.isFile()) {
                    for (File fileTemp : Objects.requireNonNull(file.listFiles())) {
                        name = fileTemp.getName();
                        if (name.endsWith(suffixName)) {
                            packagePath = fileTemp.getAbsolutePath();
                            if (name.endsWith(suffixName)) {
                                split = name.split("\\.");
                                LogUtils.d("file.getName() = "+file.getName());
                                if (split.length > 0) {
                                    packageId = split[0];
                                }
                            }
                        }
                        if (name.endsWith(".lic")) {
                            licPath = fileTemp.getAbsolutePath();
                        }
                    }
                } else {
                    name = file.getName();
                    if (name.endsWith(suffixName)) {
                        packagePath = file.getAbsolutePath();
                        if (name.endsWith(suffixName)) {
                            split = name.split("\\.");
                            LogUtils.d("file.getName() = "+file.getName());
                            if (split.length > 0) {
                                packageId = split[0];
                            }
                        }
                    }
                }

            }
            if (TextUtils.isEmpty(packageId)) {
                return;
            }
            if (!existInDB(packageId)) {
                boolean exist = false;
                for (AssetInfo item : data.realAssetList) {
                    if (packageId.equals(item.getPackageId())) {
                        exist = true;
                        break;
                    }
                }
                if (!exist) {
                    AssetInfo assetInfo = new AssetInfo();
                    if (split.length == 3) {
                        assetInfo.setVersion(Integer.parseInt(split[1]));
                    }
                    assetInfo.setId(packageId);
                    assetInfo.setType(assetType);
                    assetInfo.setPackageId(packageId);
                    assetInfo.setEffectMode(BaseInfo.EFFECT_MODE_PACKAGE);
                    assetInfo.setHadDownloaded(true);
                    assetInfo.setAssetPath(packagePath);
                    assetInfo.setLicPath(licPath);
                    installAssetPackage(assetInfo, false, false);
                    extraList.add(assetInfo);
                }
            }

            if (extraList.size() > 0) {
                if (data.realAssetList == null) {
                    data.realAssetList = extraList;
                } else {
                    data.realAssetList.addAll(0, extraList);
                }
            }
        }
    }


    /**
     * Get assets data from database
     * <P></>
     * 从数据库中获取资源列表
     *
     * @param token      用户token token of user
     * @param assetType  类型 asset type
     * @param categoryId 分类 categoryId
     * @param kind       kind
     */
    @Deprecated
    public AssetList getAssetsListFromDb(String token, String paramKey, final int assetType, final int subType, final int categoryId, final int kind) {
        if (subType == 0 || TextUtils.isEmpty(token)) {
            token = "All";
        }
        List<AssetEntity> assetList = mAssetUserDao.getAssetList(token, paramKey);
        if (CommonUtils.isEmpty(assetList)) {
            return null;
        }
        AssetList data = new AssetList();
        data.total = Integer.MAX_VALUE;
        data.type = parseTypeFromNewData(assetType, categoryId, kind);
        List<AssetInfo> dataInfo = new ArrayList<>();
        for (AssetEntity assetEntity : assetList) {
            dataInfo.add(AssetInfo.create(assetEntity));
        }
        data.realAssetList = dataInfo;
        return data;
    }

    /**
     * Get assets data from database
     * <P></>
     * 从数据库中获取资源列表
     *
     * @param token    用户token token of user
     * @param param    请求参数 the  parameter
     * @param callback the  callback
     */
    public void getAssetsListFromDb(String token, final RequestParam param, final AssetCallback callback) {
        if (param == null) {
            if (callback != null) {
                callback.onFailure();
            }
            return;
        }
        if (param.subType == 0 || TextUtils.isEmpty(token)) {
            token = "All";
        }
        final String finalToken = token;
        ThreadUtils.getCachedPool().execute(new Runnable() {
            @Override
            public void run() {
                final List<AssetEntity> dbAssetList = mAssetUserDao.getAssetList(finalToken, String.valueOf(param.hashCode()));
                final int type = parseTypeFromNewData(param.type, param.categoryId, param.kind);
                //包内asset目录下的数据
                //Data under the asset directory in the package.
                List<AssetInfo> assetList = null;
                if (param.subType == 0) {
                    assetList = getPackageAssetList(type);
                }
                final List<AssetInfo> assetInfos = handleDBDataAndBuildInData(dbAssetList, assetList, type);
                if (param.subType == 0) {
                    /*
                     * 检查下载目录中手动添加的数据
                     * Check the manually added data in the download directory.
                     */
                    checkDownloadDir(assetInfos, type);
                }
                if (callback != null) {
                    ThreadUtils.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            callback.onSuccess(assetInfos);
                        }
                    });
                }
            }
        });
    }


    /***
     * handle database data and build-in data
     * <p>
     * 处理数据库中的数据和内置数据
     * @param dbAssetList 数据库中的数据 database data
     * @param assetList  内置数据 build-in data
     * @param type 数据类型 the assets type
     * @return all date
     */
    public List<AssetInfo> handleDBDataAndBuildInData(List<AssetEntity> dbAssetList, List<AssetInfo> assetList, int type) {
        final List<AssetInfo> localList = new ArrayList<>();
        /*
         * 添加内置数据,不包含数据库中的数据
         * Add built-in data, excluding data in the database.
         */
        if (!CommonUtils.isEmpty(assetList)) {
            for (AssetInfo assetInfo : assetList) {
                assetInfo.setEffectMode(BaseInfo.EFFECT_MODE_PACKAGE);
                boolean inDb = false;
                if (!CommonUtils.isEmpty(dbAssetList)) {
                    for (AssetEntity item : dbAssetList) {
                        if (item.getPackageId().equals(assetInfo.getPackageId())) {
                            inDb = true;
                            break;
                        }
                    }
                }
                if (!inDb) {
                    //在包内Asset目录下有数据，但是数据库中也有，以数据库中的为准，
                    // 因为数据库中的肯定是下载而来的，一般是最新的
                    //There are data under the Asset directory in the package,
                    // but there are also data in the database, which shall prevail,
                    // Because the database must be downloaded, it is generally the latest.
                    installAssetPackage(assetInfo, false, false);
                    localList.add(assetInfo);
                }
            }
        }
        /*
         添加数据库数据
         Add database data
         */
        if (!CommonUtils.isEmpty(dbAssetList)) {
            for (AssetEntity item : dbAssetList) {
                AssetInfo assetInfo = AssetInfo.create(item);
                installAssetPackage(assetInfo, false, false);
                if (checkInvalid(assetInfo)) {
                    localList.add(assetInfo);
                }
            }
        }
        return localList;
    }


    public void updateUserAssetsInfo(String oldToken, String newToken) {
        if (TextUtils.isEmpty(oldToken) || TextUtils.isEmpty(newToken)) {
            return;
        }
        List<UserAssetsEntity> assetList = mAssetUserDao.getAssetList(oldToken);
        mAssetUserDao.deleteAllAsset(oldToken);
        for (UserAssetsEntity entity : assetList) {
            entity.setToken(newToken);
            mAssetUserDao.insertAsset(entity);
        }
    }


    /**
     * Check unavailable assets
     * <p></>
     * 检查无效资源
     *
     * @param token        用户token user token
     * @param assetsIdList 素材id数组  List of assets id
     * @param callback     回调 callback
     */
    public void checkUnavailableAssets(String token, List<String> assetsIdList, final RequestCallback<AssetList> callback) {
        EngineNetApi.checkUnavailableAssets(token, assetsIdList, new RequestCallback<List<AssetList.NvAssetInfo>>() {

            @Override
            public void onSuccess(final BaseResponse<List<AssetList.NvAssetInfo>> response) {
                BaseResponse<AssetList> result = getAssetListBaseResponse(response);
                List<AssetList.NvAssetInfo> elements = response.getData();
                if (elements != null && elements.size() > 0) {
                    AssetList assetList = new AssetList();
                    List<AssetInfo> resultData = new ArrayList<>();
                    for (AssetList.NvAssetInfo element : elements) {
                        int type = AssetsManager.parseTypeFromNewData(element.type, element.category, element.kind);
                        resultData.add(AssetInfo.create(element, type));
                    }
                    assetList.realAssetList = resultData;
                    result.setData(assetList);
                }
                callback.onSuccess(result);
            }

            @Override
            public void onError(BaseResponse<List<AssetList.NvAssetInfo>> response) {
                BaseResponse<AssetList> result = getAssetListBaseResponse(response);
                if (callback != null) {
                    callback.onError(result);
                }
            }

            private BaseResponse<AssetList> getAssetListBaseResponse(BaseResponse<List<AssetList.NvAssetInfo>> response) {
                BaseResponse<AssetList> result = new BaseResponse<>();
                result.setCode(response.getCode());
                result.setEnMsg(response.getEnMsg());
                result.setErrNo(response.getErrNo());
                result.setMessage(response.getMessage());
                result.setMsg(response.getMsg());
                return result;
            }
        });
    }


    /**
     * Commit unavailable assets
     * <p></>
     * 提交无效资源
     *
     * @param token        用户token user token
     * @param assetsIdList 素材id数组  List of assets id
     * @param callback     回调 callback
     */
    public void commitUnavailableAssets(String token, List<String> assetsIdList, final RequestCallback<Object> callback) {
        EngineNetApi.commitUnavailableAssets(token, assetsIdList, callback);
    }


    /**
     * 检测资源是否非法
     * Check if the resource is illegal
     *
     * @param assetInfo the info
     */
    private boolean checkInvalid(AssetInfo assetInfo) {
        if (TextUtils.isEmpty(assetInfo.getMinAppVersion())) {
            return true;
        }
        if (!checkVersion(assetInfo.getMinAppVersion())) {
            LogUtils.e("version is invalid,version = " + assetInfo.getMinAppVersion() +
                    ", uuid = " + assetInfo.getPackageId());
            return false;
        }
        return true;
    }

    private boolean checkVersion(String minAppVersion) {
        try {
            String[] split = minAppVersion.split("\\.");
            if (split.length < 3) {
                return false;
            }
            NvsStreamingContext.SdkVersion sdkVersion = NvsConstants.getSdkVersion();
            long sdkLongVersion = getVersionInteger(sdkVersion.majorVersion, sdkVersion.minorVersion, sdkVersion.revisionNumber);
            long minLongVersion = getVersionInteger(Integer.parseInt(split[0]), Integer.parseInt(split[1]), Integer.parseInt(split[2]));
            return sdkLongVersion >= minLongVersion;
        } catch (Exception e) {
            LogUtils.e("error:" + e.getMessage());
        }
        return false;
    }

    private int getVersionInteger(int majorVersion, int minorVersion, int revisionNumber) {
        return majorVersion * 10000 + minorVersion * 100 + revisionNumber;
    }

    private int getSpecialType(int assetType) {
        switch (assetType) {
            case AssetInfo.ASSET_VIDEO_TRANSITION:
            case AssetInfo.ASSET_VIDEO_TRANSITION_3D:
            case AssetInfo.ASSET_VIDEO_TRANSITION_EFFECT:
                return AssetInfo.ASSET_VIDEO_TRANSITION;
            case AssetInfo.ASSET_CUSTOM_STICKER_PACKAGE:
                return AssetInfo.ASSET_ANIMATED_STICKER;
            case AssetInfo.ASSET_AR_SCENE_FACE:
                return 14;
            case AssetInfo.ASSET_COMPOUND_CAPTION:
                return 15;
            case AssetInfo.ASSET_EFFECT_FRAME:
            case AssetInfo.ASSET_EFFECT_DREAM:
            case AssetInfo.ASSET_EFFECT_LIVELY:
            case AssetInfo.ASSET_EFFECT_SHAKING:
            case AssetInfo.ASSET_ANIMATION_IN:
            case AssetInfo.ASSET_ANIMATION_OUT:
            case AssetInfo.ASSET_ANIMATION_GROUP:
                return 2;
            case AssetInfo.ASSET_CUSTOM_CAPTION_FLOWER:
            case AssetInfo.ASSET_CUSTOM_CAPTION_BUBBLE:
            case AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_IN:
            case AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_OUT:
            case AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_COMBINATION:
                return AssetInfo.ASSET_CAPTION_STYLE;
            default:
                return assetType;
        }
    }

    private int getCategoryId(int assetType, int categoryId) {
        switch (assetType) {
            case AssetInfo.ASSET_CUSTOM_STICKER_PACKAGE:
                return AssetInfo.NV_CATEGORY_ID_CUSTOM;
            case AssetInfo.ASSET_EFFECT_FRAME:
                return 6;
            case AssetInfo.ASSET_EFFECT_DREAM:
                return 5;
            case AssetInfo.ASSET_EFFECT_LIVELY:
                return 4;
            case AssetInfo.ASSET_EFFECT_SHAKING:
                return 7;
            case AssetInfo.ASSET_FILTER:
            case AssetInfo.ASSET_VIDEO_TRANSITION_3D:
                return 2;
            case AssetInfo.ASSET_VIDEO_TRANSITION:
                return 1;
            case AssetInfo.ASSET_VIDEO_TRANSITION_EFFECT:
                return 3;
            default:
                return categoryId;
        }
    }

    /**
     * 获取SDK中的素材类型表示方式
     * Get the representation of the material type in the SDK
     *
     * @return the package type
     */
    private int getPackageType(int assetType) {
        switch (assetType) {
            case AssetInfo.ASSET_FILTER:
            case AssetInfo.ASSET_EFFECT_FRAME:
            case AssetInfo.ASSET_EFFECT_DREAM:
            case AssetInfo.ASSET_EFFECT_LIVELY:
            case AssetInfo.ASSET_EFFECT_SHAKING:
            case AssetInfo.ASSET_EFFECT_OTHER:
            case AssetInfo.ASSET_PARTICLE:
            case AssetInfo.ASSET_ANIMATION_IN:
            case AssetInfo.ASSET_ANIMATION_OUT:
            case AssetInfo.ASSET_ANIMATION_GROUP:
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_VIDEOFX;
            case AssetInfo.ASSET_CAPTION_STYLE:
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_CAPTIONSTYLE;
            case AssetInfo.ASSET_ANIMATED_STICKER:
            case AssetInfo.ASSET_CUSTOM_STICKER_PACKAGE:
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_ANIMATEDSTICKER;
            case AssetInfo.ASSET_VIDEO_TRANSITION:
            case AssetInfo.ASSET_VIDEO_TRANSITION_EFFECT:
            case AssetInfo.ASSET_VIDEO_TRANSITION_3D:
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_VIDEOTRANSITION;
            case AssetInfo.ASSET_CAPTURE_SCENE:
            case AssetInfo.ASSET_FACE_STICKER:
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_CAPTURESCENE;
            case AssetInfo.ASSET_AR_SCENE_FACE:
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_ARSCENE;
            case AssetInfo.ASSET_COMPOUND_CAPTION:
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_COMPOUND_CAPTION;
            case AssetInfo.ASSET_CUSTOM_CAPTION_FLOWER:
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_CAPTION_RENDERER;
            case AssetInfo.ASSET_CUSTOM_CAPTION_BUBBLE:
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_CAPTION_CONTEXT;
            case AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_IN:
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_CAPTION_IN_ANIMATION;
            case AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_OUT:
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_CAPTION_OUT_ANIMATION;
            case AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_COMBINATION:
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_CAPTION_ANIMATION;
            case AssetInfo.ASSET_STICKER_ANIMATION_COMP:
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_ANIMATEDSTICKER_ANIMATION;
            case AssetInfo.ASSET_STICKER_ANIMATION_IN:
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_ANIMATEDSTICKER_IN_ANIMATION;
            case AssetInfo.ASSET_STICKER_ANIMATION_OUT:
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_ANIMATEDSTICKER_OUT_ANIMATION;
            case AssetInfo.ASSET_PACKAGE_TYPE_FACE_MESH:
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_FACE_MESH;
            case AssetInfo.ASSET_PACKAGE_TYPE_FACE_WARP:
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_WARP;
            case AssetInfo.ASSET_THEME:
            default:
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_THEME;
        }
    }

    /**
     * Gets asset type.
     * 获取文件类型
     *
     * @param filePath the file path 文件路径
     * @return the asset type 文件类型
     */
    public static int getAssetType(@NonNull String filePath) {
        return getAssetTypeBySuffix(FileUtils.getFileSuffix(filePath));
    }

    /**
     * Gets asset type by suffix.
     * 根据后缀名获取特效类型
     *
     * @param suffix the suffix 后缀名
     * @return the asset type by suffix 特效类型
     */
    public static int getAssetTypeBySuffix(@NonNull String suffix) {
        switch (suffix) {
            case "theme":
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_THEME;
            case "videofx":
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_VIDEOFX;
            case "captionstyle":
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_CAPTIONSTYLE;
            case "animatedsticker":
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_ANIMATEDSTICKER;
            case "videotransition":
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_VIDEOTRANSITION;
            case "arscene":
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_ARSCENE;
            case "compoundcaption":
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_COMPOUND_CAPTION;
            case "captionrenderer":
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_CAPTION_RENDERER;
            case "captioncontext":
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_CAPTION_CONTEXT;
            case "captionanimation":
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_CAPTION_ANIMATION;
            case "captioninanimation":
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_CAPTION_IN_ANIMATION;
            case "captionoutanimation":
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_CAPTION_OUT_ANIMATION;
            case "animatedstickeranimation":
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_ANIMATEDSTICKER_ANIMATION;
            case "animatedstickerinanimation":
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_ANIMATEDSTICKER_IN_ANIMATION;
            case "animatedstickeroutanimation":
                return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_ANIMATEDSTICKER_OUT_ANIMATION;
        }
        return NvsAssetPackageManager.ASSET_PACKAGE_TYPE_VIDEOFX;
    }

    /**
     * 安装资源包
     * Install asset package
     *
     * @param assetInfo 资源信息 asset info
     * @param saveToDB  是否保存到数据库 true save to database,false not
     * @param notify    是否通知观察者true notify observer,false not
     */
    private void installAssetPackage(final AssetInfo assetInfo, final boolean saveToDB, final boolean notify) {
        if (TextUtils.isEmpty(assetInfo.getPackageId())) {
            return;
        }
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                NvsStreamingContext instance = NvsStreamingContext.getInstance();
                if (instance == null) {
                    return;
                }
                NvsAssetPackageManager manager = instance.getAssetPackageManager();
                StringBuilder packageId = new StringBuilder();
                int packageType = getPackageType(assetInfo.getType());
                int error = manager.installAssetPackage(assetInfo.getAssetPath(), assetInfo.getLicPath(), packageType, true, packageId);
                LogUtils.d(": installAssetPackage error = " + error + ", path = " + assetInfo.getAssetPath() + ", name = " + assetInfo.getName() + ", type = " + assetInfo.getType());
                if (error == NvsAssetPackageManager.ASSET_PACKAGE_MANAGER_ERROR_ALREADY_INSTALLED) {
                    int version = manager.getAssetPackageVersionFromAssetPackageFilePath(assetInfo.getAssetPath());
                    if (version > assetInfo.getVersion()) {
                        error = manager.upgradeAssetPackage(assetInfo.getAssetPath(), assetInfo.getLicPath(), packageType, false, packageId);
                        if (error == NvsAssetPackageManager.ASSET_PACKAGE_MANAGER_ERROR_NO_ERROR) {
                            assetInfo.setVersion(version);
                        }
                    }
                } else if (error == NvsAssetPackageManager.ASSET_PACKAGE_MANAGER_ERROR_NO_ERROR) {
                    assetInfo.setVersion(manager.getAssetPackageVersion(packageId.toString(), packageType));
                }
                if (!TextUtils.isEmpty(packageId.toString())) {
                    assetInfo.setPackageId(packageId.toString());
                }
                if (saveToDB && !TextUtils.isEmpty(assetInfo.getPackageId())) {
                    //下载完毕后，把信息保存到数据库
                    //After downloading, save the information to the database.
                    updateDatabase(assetInfo);
                }
                if (notify) {
                    mAssetObservable.addAsset(assetInfo);
                }
            }
        });
    }

    public static void installFont() {
        AssetManager assets = Utils.getApp().getAssets();
        if (assets != null) {
            try {
                String[] fonts = assets.list("font");
                if (fonts != null && fonts.length > 0) {
                    for (String font : fonts) {
                        if (font.toLowerCase().endsWith("ttf") || font.toLowerCase().endsWith("otf")) {
                            NvsStreamingContext.getInstance().registerFontByFilePath("assets:/font/" + font);
                        }
                    }
                }
            } catch (IOException e) {
                LogUtils.e(e);
            }
        }
        /*
         *检查安装本地字体
         *Check and install local font
         */
        List<File> files = FileUtils.listFilesInDir(PathUtils.getFontFilePath());
        for (File file : files) {
            String fontPathLocal = file.getAbsolutePath();
            NvsStreamingContext.getInstance().registerFontByFilePath(fontPathLocal);
        }

    }

    /**
     * 获取实际的资源包的信息列表
     * Gets a list of information for the actual resource bundle
     *
     * @param asset asset from network
     */
    private List<AssetInfo> getAssetInfo(AssetList asset, int subType) {
        return handleAssetInfos(asset.list, asset.type, subType);
    }

    private ArrayList<AssetInfo> handleAssetInfos(List<AssetList.NvAssetInfo> rawData, int type, int subType) {
        ArrayList<AssetInfo> assetList = null;
        if (rawData != null && rawData.size() > 0) {
            assetList = new ArrayList<>(rawData.size());
            List<AssetInfo> packageAssetList = getPackageAssetList(type);
            for (AssetList.NvAssetInfo itemInfo : rawData) {
                AssetInfo assetInfo = AssetInfo.create(itemInfo, type);
                assetInfo.setSubType(subType);
                AssetEntity dbAsset = mAssetDao.getAsset(assetInfo.getId());
                if (dbAsset != null) {
                    //如果数据库有该条数据，根据下载的文件是否存在设置是否已经下载
                    //If the database has this data, set the file is exists by whether it has been downloaded.
                    assetInfo.setHadDownloaded(FileUtils.isFileExists(dbAsset.getAssetPath()));
                    if (!assetInfo.isHadDownloaded()) {
                        //如果文件不存在，则删除本地数据库记录
                        //If the file does not exist, delete the local database record.
                        mAssetDao.deleteAsset(dbAsset);
                    } else {
                        assetInfo.setLocalVersion(dbAsset.getVersion());
                        assetInfo.setAssetPath(dbAsset.getAssetPath());
                        assetInfo.setLicPath(dbAsset.getLicPath());
                        installAssetPackage(assetInfo, false, false);
                    }
                    if (!TextUtils.isEmpty(dbAsset.getName()) && !dbAsset.getName().equals(assetInfo.getName())) {
                        dbAsset.setName(assetInfo.getName());
                        mAssetDao.updateAsset(dbAsset);
                    }
                    // LogUtils.d("dbAsset=" + dbAsset + ",assetInfo=" + assetInfo);
                } else {
                    if (packageAssetList != null && packageAssetList.size() > 0) {
                        //检查包内资源
                        for (AssetInfo packageAsset : packageAssetList) {
                            if (assetInfo.getPackageId().equals(packageAsset.getPackageId())) {
                                assetInfo.setHadDownloaded(true);
                                assetInfo.setLocalVersion(packageAsset.getVersion());
                                assetInfo.setAssetPath(packageAsset.getAssetPath());
                                assetInfo.setLicPath(packageAsset.getLicPath());
                                installAssetPackage(assetInfo, false, false);
                                break;
                            }
                        }
                    } else {
                        //检查该资源是否正在被下载，若正在下载则更新进度
                        //Check whether the resource is being downloaded, and update the progress if it is being downloaded
                        DownloadTask downloadTask = EngineNetApi.getDownloadTask(assetInfo.getDownloadUrl());
                        if (downloadTask != null && downloadTask.progress != null && !TextUtils.isEmpty(downloadTask.progress.tag)) {
                            assetInfo.setDownloadProgress((int) (downloadTask.progress.currentSize / downloadTask.progress.totalSize));
                        }
                    }


                    assetInfo.setHadDownloaded(false);
                }
                if (checkInvalid(assetInfo)) {
                    assetList.add(assetInfo);
                }
            }
        }
        return assetList;
    }

    /**
     * 获取实际的资源包的信息列表
     * Gets a list of information for the actual resource bundle
     *
     * @param asset asset from network
     */
    private List<AssetInfo> getAssetInfoEx(AssetList asset, int subType) {
        return handleAssetInfos(asset.elements, asset.type, subType);
    }


    /**
     * 下载资源包
     * Download resource packs
     *
     * @param assetInfo 资源信息 asset info
     * @param saveToDB  是否存入数据库 true save to db ,false not
     * @param listener  下载监听 download listener
     */
    public void downloadAsset(final AssetInfo assetInfo, final boolean saveToDB, final SimpleDownListener listener) {
        if (!NetUtils.isNetworkAvailable(Utils.getApp())) {
            ToastUtils.showShort(Utils.getApp().getResources().getString(R.string.user_hint_assets_net_error));
            return;
        }
        final String downloadUrl = assetInfo.getDownloadUrl();
        if (!TextUtils.isEmpty(downloadUrl)) {
            if (!downloadUrl.startsWith("http")) {
                LogUtils.d("path is wrong!");
                if (listener != null) {
                    listener.onError(new Progress());
                }
                return;
            }

            String packageId = assetInfo.getPackageId();
            String fileSavedPath = PathUtils.getAssetDownloadPath(assetInfo.getType());
            File file = new File(fileSavedPath, packageId);
            if (!file.exists() && !file.mkdirs()) {
                if (listener != null) {
                    listener.onError(new Progress());
                }
                FileUtils.delete(fileSavedPath);
                return;
            }
            fileSavedPath = file.getAbsolutePath();
            download(assetInfo, downloadUrl, fileSavedPath, "", saveToDB, listener);
        } else {
            final String packageId = assetInfo.getId();
            EngineNetApi.downloadOrClick(packageId, "1", new RequestCallback<AssetDownloadInfo>() {
                @Override
                public void onSuccess(BaseResponse<AssetDownloadInfo> response) {
                    if (response == null || response.getData() == null) {
                        if (listener != null) {
                            listener.onError(new Progress());
                        }
                        return;
                    }
                    AssetDownloadInfo data = response.getData();
                    String packageUrl = data.getPackageUrl();
                    if (TextUtils.isEmpty(packageUrl)) {
                        if (listener != null) {
                            listener.onError(new Progress());
                        }
                        return;
                    }
                    String fileSavedPath = PathUtils.getAssetDownloadPath(assetInfo.getType());
                    File file = new File(fileSavedPath, packageId);
                    if (!file.exists() && !file.mkdirs()) {
                        if (listener != null) {
                            listener.onError(new Progress());
                        }
                        FileUtils.delete(fileSavedPath);
                        return;
                    }
                    fileSavedPath = file.getAbsolutePath();
                    download(assetInfo, packageUrl, fileSavedPath, "", saveToDB, listener);
                }

                @Override
                public void onError(BaseResponse<AssetDownloadInfo> response) {
                    if (listener != null) {
                        listener.onError(new Progress());
                    }
                }
            });
        }

    }

    public void downLoad(String uuid, final String downloadUrl, final String fileSavedPath
            , final String name, int subType, final SimpleDownListener listener) {
        String token = "";
        IUserPlugin userPlugin = null;
        if (mAssetsProxy != null) {
            userPlugin = mAssetsProxy.getUserPlugin();
        }
        if (userPlugin != null) {
            token = userPlugin.getToken();
        }
        if (token != null && (subType == 1 || subType == 2)) {
            //登录，并且自制和已购的才会重新请求下载连接
            //Login, and only self-made and purchased ones can request download connection again.
            EngineNetApi.checkDownloadUrl(token, subType == 1, uuid, new RequestCallback<AssetDownloadInfo>() {
                @Override
                public void onSuccess(BaseResponse<AssetDownloadInfo> response) {
                    if (response.getData() == null) {
                        return;
                    }
                    LogUtils.d("checkDownloadUrl onSuccess");
                    AssetDownloadInfo assetDownloadInfo = (AssetDownloadInfo) response.getData();
                    String zipUrl = assetDownloadInfo.getPackageUrl();
                    EngineNetApi.download(zipUrl, zipUrl, fileSavedPath, name, listener);
                }

                @Override
                public void onError(BaseResponse<AssetDownloadInfo> response) {
                    if (listener != null) {
                        listener.onError(new Progress());
                    }
                }
            });
        } else {
            EngineNetApi.download(downloadUrl, downloadUrl, fileSavedPath, name, listener);
        }

    }

    private void download(final AssetInfo assetInfo, final String downloadUrl, final String fileSavedPath,
                          final String name, final boolean saveToDB, final SimpleDownListener listener) {
        downLoad(assetInfo.getPackageId(),
                downloadUrl,
                fileSavedPath,
                name,
                assetInfo.getSubType(),
                new SimpleDownListener(downloadUrl) {

                    @Override
                    public void onStart(Progress progress) {
                        super.onStart(progress);
                        if (listener != null) {
                            listener.onStart(progress);
                        }
                    }

                    @Override
                    public void onProgress(Progress progress) {
                        super.onProgress(progress);
                        if (progress.totalSize <= 0) {
                            return;
                        }
                        assetInfo.setDownloadProgress((int) (progress.currentSize / progress.totalSize) * 99);
                        if (listener != null) {
                            listener.onProgress(progress);
                        }
                    }

                    @Override
                    public void onError(Progress progress) {
                        super.onError(progress);
                        LogUtils.e("onError:" + (progress.exception != null ? progress.exception.getMessage() : "") + ",fileSavedPath=" + fileSavedPath + ",name=" + name);
                        assetInfo.setDownloadProgress(AssetInfo.DOWNLOAD_FAILED_PROGRESS);
                        onFailed(progress, listener, fileSavedPath);
                    }

                    @Override
                    public void onFinish(final File file, final Progress progress) {
                        super.onFinish(file, progress);
                        final String filePath = file.getAbsolutePath();
                        if (filePath.endsWith(".zip")) {
                            ThreadUtils.getIoPool().execute(new Runnable() {
                                @Override
                                public void run() {
                                    try {
                                        final List<File> fileList = ZipUtils.unzipFile(file, new File(fileSavedPath));
                                        /*
                                         * 删除webp文件，文件较大
                                         * Delete the webp files， it is big.
                                         */
                                        try {
                                            if (!CommonUtils.isEmpty(fileList)) {
                                                for (File fileItem : fileList) {
                                                    if (fileItem.getName().endsWith(".webp")) {
                                                        FileUtils.delete(fileItem);
                                                    }
                                                    if (fileItem.getName().endsWith(".lic")) {
                                                        assetInfo.setLicPath(fileItem.getAbsolutePath());
                                                    }
                                                }
                                            }
                                        } catch (Exception e) {
                                            LogUtils.d(e);
                                        }
                                        /*
                                         * 删除zip文件
                                         * Delete the zip files
                                         */
                                        FileUtils.delete(filePath);
                                        ThreadUtils.runOnUiThread(new Runnable() {
                                            @Override
                                            public void run() {
                                                if (CommonUtils.isEmpty(fileList)) {
                                                    onFailed(progress, listener, fileSavedPath);
                                                    return;
                                                }
                                                for (File fileItem : fileList) {
                                                    String absolutePath = fileItem.getAbsolutePath();

                                                    if (absolutePath.endsWith(getAssetSuffixName(assetInfo.getType()))) {
                                                        doAfterDownload(fileItem, progress);
                                                        return;
                                                    }
                                                }
                                                onFailed(progress, listener, fileSavedPath);
                                            }
                                        });
                                    } catch (IOException e) {
                                        onFailed(progress, listener, fileSavedPath);
                                    }
                                }
                            });
                        } else {
                            doAfterDownload(file, progress);
                        }
                    }

                    private void onFailed(Progress progress, SimpleDownListener listener, String fileSavedPath) {
                        if (listener != null) {
                            listener.onError(progress);
                        }
                        FileUtils.delete(fileSavedPath);
                    }

                    private void doAfterDownload(File file, Progress progress) {
                        assetInfo.setAssetPath(file.getAbsolutePath());
                        assetInfo.setDownloadProgress(100);
                        assetInfo.setHadDownloaded(true);
                        assetInfo.setLocalVersion(assetInfo.getVersion());
                        //安装或者升级资源包
                        //Install or upgrade assets package.
                        if (assetInfo.getType() != AssetInfo.ASSET_PACKAGE_BACKGROUND) {
                            installAssetPackage(assetInfo, saveToDB, true);
                        }
                        if (listener != null) {
                            listener.onFinish(file, progress);
                        }
                    }
                });
    }

    /**
     * Upload template
     * <p></>
     * 上传模板
     *
     * @param tag      任务tag task tag
     * @param token    token
     * @param param    请求参数 request param
     * @param callback callback
     */
    public void uploadTemplate(String tag, String token, TemplateUploadParam param, final RequestCallback<Object> callback) {
        EngineNetApi.uploadTemplate(tag, token, param, callback);
    }

    /**
     * 更新数据库
     * updated database
     *
     * @param assetInfo asset info
     */
    private void updateDatabase(AssetInfo assetInfo) {
        updateDatabase(assetInfo, false);
    }

    /**
     * 更新数据库
     * updated database
     *
     * @param assetInfo 资源信息 asset info
     * @param notify    为真则通知观察者true notify observer ,false not
     */
    public void updateDatabase(final AssetInfo assetInfo, boolean notify) {
        ThreadUtils.getCachedPool().execute(new Runnable() {
            @Override
            public void run() {
                AssetEntity oldAsset = mAssetDao.getAsset(assetInfo.getId());
                if (oldAsset != null) {
                    //旧版本文件删除
                    //Delete the old version file.
                    if (!TextUtils.isEmpty(oldAsset.getAssetPath()) && !oldAsset.getAssetPath()
                            .equals(assetInfo.getAssetPath())) {
                        FileUtils.delete(oldAsset.getAssetPath());
                    }
                    mAssetDao.updateAsset(oldAsset.update(assetInfo));
                } else {
                    mAssetDao.insertAsset(AssetEntity.create(assetInfo));
                }
            }
        });
        if (notify) {
            mAssetObservable.addAsset(assetInfo);
        }
    }

    /**
     * Gets assets url.
     * 获取资源包路径
     *
     * @param assetsIdList the assets id list 资源uuid列表
     * @param callBack     the call back 回调
     */
    public static void getAssetsUrl(List<String> assetsIdList, final CloudMapRequestCallBack callBack) {
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin == null) {
            if (callBack != null) {
                callBack.onError();
            }
            return;
        }
        EngineNetApi.getMaterialList(userPlugin.getToken(), assetsIdList, new RequestCallback<CloudPathMapBean>() {
            @Override
            public void onSuccess(BaseResponse<CloudPathMapBean> response) {
                if (response == null || response.getCode() != 0) {
                    if (callBack != null) {
                        callBack.onError();
                    }
                    return;
                }
                if (callBack != null) {
                    callBack.onSuccess(response.getData());
                }

            }

            @Override
            public void onError(BaseResponse<CloudPathMapBean> response) {
                if (callBack != null) {
                    callBack.onError();
                }
            }
        });
    }

    /**
     * Get assets asset entity.
     * 获取资源数据
     *
     * @param uuid the uuid
     * @return the asset entity
     */
    public AssetEntity getAssets(String uuid) {
        return mAssetDao.getAsset(uuid);
    }

    /**
     * Insert assets.
     * 插入资源数据
     *
     * @param entity the entity
     */
    public void insertAssets(AssetEntity entity) {
        mAssetDao.insertAsset(entity);
    }

    /**
     * 注册资源变动观察者
     * Register as a resource change observer
     *
     * @param observer the observer
     */
    public void registerAssetObserver(AssetObserver observer) {
        try {
            mAssetObservable.registerObserver(observer);
        } catch (Exception e) {
            LogUtils.e(e);
        }

    }

    /**
     * 取消注册资源变动观察者
     * Unregister as a resource change observer
     *
     * @param observer the observer
     */
    public void unregisterAssetObserver(AssetObserver observer) {
        try {
            mAssetObservable.unregisterObserver(observer);
        } catch (Exception e) {
            LogUtils.e(e);
        }

    }

    /**
     * Parse type from new type data
     * <P></>
     * 将新数据的type转换为旧数据的type
     *
     * @param type     一级类型 type
     * @param category 二级类型 category
     * @param kind     三级类型 kind
     * @return 旧数据的type oldType
     */
    public static int parseTypeFromNewData(int type, int category, int kind) {
        switch (type) {
            case 1:
                return AssetInfo.ASSET_THEME;
            case 2:
                if (category == 1) {
                    return AssetInfo.ASSET_FILTER;
                } else if (category == 2) {
                    switch (kind) {
                        case 4:
                            return AssetInfo.ASSET_EFFECT_LIVELY;
                        case 3:
                            return AssetInfo.ASSET_EFFECT_SHAKING;
                        case 6:
                            return AssetInfo.ASSET_EFFECT_DREAM;
                        case 9:
                            return AssetInfo.ASSET_EFFECT_FRAME;
                        default:
                            return AssetInfo.ASSET_EFFECT_OTHER;
                    }
                } else {
                    switch (kind) {
                        case 1:
                            return AssetInfo.ASSET_ANIMATION_IN;
                        case 2:
                            return AssetInfo.ASSET_ANIMATION_OUT;
                        default:
                            return AssetInfo.ASSET_ANIMATION_GROUP;
                    }
                }
            case 3:
                if (category == 1) {
                    return AssetInfo.ASSET_CAPTION_STYLE;
                } else if (category == 2) {
                    switch (kind) {
                        case 1:
                            return AssetInfo.ASSET_CUSTOM_CAPTION_FLOWER;
                        case 2:
                            return AssetInfo.ASSET_CUSTOM_CAPTION_BUBBLE;
                        case 3:
                            return AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_IN;
                        case 4:
                            return AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_OUT;
                        default:
                            return AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_COMBINATION;
                    }
                }
            case 4:
                if (category == 3) {
                    switch (kind) {
                        case 1:
                            return AssetInfo.ASSET_STICKER_ANIMATION_IN;
                        case 2:
                            return AssetInfo.ASSET_STICKER_ANIMATION_OUT;
                        default:
                            return AssetInfo.ASSET_STICKER_ANIMATION_COMP;
                    }
                }
                return AssetInfo.ASSET_ANIMATED_STICKER;
            case 5:
                if (category == 1) {
                    return AssetInfo.ASSET_VIDEO_TRANSITION_EFFECT;
                } else {
                    return AssetInfo.ASSET_VIDEO_TRANSITION_3D;
                }
            case 14:
                return AssetInfo.ASSET_AR_SCENE_FACE;
            case 15:
                return AssetInfo.ASSET_COMPOUND_CAPTION;
            case 19:
                return AssetInfo.ASSET_PACKAGE_TEMPLATE;
        }
        return AssetInfo.ASSET_FILTER;
    }

    /**
     * The interface Request call back.
     */
    public interface CloudMapRequestCallBack {
        /**
         * On success.
         *
         * @param mapBeans the map beans
         */
        void onSuccess(CloudPathMapBean mapBeans);

        /**
         * On error.
         */
        void onError();
    }
}
