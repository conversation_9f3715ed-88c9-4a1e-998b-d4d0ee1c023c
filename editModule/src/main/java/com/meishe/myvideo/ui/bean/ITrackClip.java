package com.meishe.myvideo.ui.bean;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> Li<PERSON><PERSON>Z<PERSON>
 * @CreateDate :2021/3/5 11:24
 * @Description :轨道视图相关的接口 Interfaces associated with track views
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface ITrackClip {
    /**
     * 获取片段名称（也可以是文字类描述,如字幕）
     * Get clip name
     */
    String getName();

    /**
     * 设置片段名称（也可以是文字类描述,如字幕）
     * Set clip name
     */
    void setName(String name);

    /**
     * 获取片段类型
     * Get clip type
     */
    String getType();

    /**
     * 设置片段类型
     * Set clip type
     */
    void setType(String type);

    /**
     * 获取该片段所在轨道索引
     * Gets the track index of the clip
     */
    int getTrackIndex();

    /**
     * 设置该片段所在轨道索引
     * Sets the track index of the clip
     */
    void setTrackIndex(int trackIndex);

    /**
     * 获取该片段在该轨道中的索引
     * Gets the index of the clip in the track
     */
    int getIndexInTrack();

    /**
     * 设置该片段在该轨道中的索引
     * Sets the index of the clip in the track
     */
    void setIndexInTrack(int index);

    /**
     * 获取该片段的入点
     * Get the clip in point
     */
    long getInPoint();

    /**
     * 设置该片段的入点
     * Set the clip in point
     */
    void setInPoint(long inPoint);

    /**
     * 获取该片段的出点
     * Get the clip out point
     */
    long getOutPoint();

    /**
     * 设置该片段的出点
     * Set the clip out point
     */
    void setOutPoint(long outPoint);

    /**
     * 获取该片段的裁入点
     * Get the clip trim in point
     */
    long getTrimIn();

    /**
     * 设置该片段的裁入点
     * Set the clip trim in point
     */
    void setTrimIn(long trimIn);

    /**
     * 获取该片段的裁出点
     * Get the clip trim out point
     */
    long getTrimOut();

    /**
     * 设置该片段的裁出点
     * Set the clip trim out point
     */
    void setTrimOut(long trimOut);

    /**
     * 获取该片段的原始时长
     * Get the clip original duration
     */
    long getOriginalDuration();

    /**
     * 设置该片段的原始时长
     * Set the clip original duration
     */
    void setOriginalDuration(long originalDuration);

    /**
     * 获取该片段的速度信息
     * Get the clip speed
     */
    SpeedInfo getSpeedInfo();

    /**
     * 设置该片段的关键帧信息
     * Set the clip key frame info
     */
    void setKeyFrameInfo(KeyFrameInfo keyFrameInfo);

    /**
     * 获取该片段的速度信息
     * Get the clip key frame info
     */
    KeyFrameInfo getKeyFrameInfo();

    /**
     * 设置该片段的速度信息
     * Set the clip speed info
     */
    void setSpeedInfo(SpeedInfo speedInfo);

    /**
     * 设置该片段所需的资源路径
     * Set the clip asset path
     */
    void setAssetPath(String assetPath);

    /**
     * 获取该片段所需的资源路径
     * Get the clip asset path
     */
    String getAssetPath();

    /**
     * 设置该片段所需封面图路径
     * Set the clip cover path
     */
    void setCoverPath(String coverPath);

    /**
     * 获取该片段所需封面图路径
     * Get the clip asset path
     */
    String getCoverPath();

    /**
     * 复制一个新的片段
     * Copy a new clip
     */
    ITrackClip copy();

    /**
     * 是否有道具
     * Has prop boolean.
     *
     * @return the boolean
     */
    boolean hasProp();

    /**
     * 设置是否有道具
     * Sets has prop.
     */
    void setHasProp(boolean hasProp);

    /**
     * Gets volume.
     * 获取音量
     * @return the volume
     */
    float getVolume();

    /**
     * Sets volume.
     * 设置音量
     * @param volume the volume
     */
    void setVolume(float volume);

    ThumbNailInfo getThumbNailInfo();

    void setThumbNailInfo(ThumbNailInfo thumbNailInfo);

    class ThumbNailInfo{
        public String urlPrefix;
        public long interval;
        public String extension;
        public boolean isImage;

        public ThumbNailInfo(String urlPrefix, long interval, String extension, boolean isImage) {
            this.urlPrefix = urlPrefix;
            this.interval = interval;
            this.extension = extension;
            this.isImage = isImage;
        }
    }

}
