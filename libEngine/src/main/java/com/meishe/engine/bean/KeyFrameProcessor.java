package com.meishe.engine.bean;

import android.text.TextUtils;
import android.util.Pair;

import com.meicam.sdk.NvsFx;
import com.meicam.sdk.NvsTimelineAnimatedSticker;
import com.meicam.sdk.NvsTimelineCaption;
import com.meicam.sdk.NvsVideoFx;
import com.meishe.base.bean.FloatPoint;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.interf.IKeyFrameHolder;
import com.meishe.engine.local.LClipInfo;
import com.meishe.engine.local.LMeicamKeyFrame;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.meicam.sdk.NvsFx.KEY_FRAME_FIND_MODE_INPUT_TIME_AFTER;
import static com.meishe.engine.bean.MeicamKeyFrame.CAPTION_SCALE_X;
import static com.meishe.engine.bean.MeicamKeyFrame.SCALE_X;
import static com.meishe.engine.bean.MeicamKeyFrame.STICKER_SCALE;
import static com.meishe.engine.constant.NvsConstants.KEY_CROPPER_TRANS_X;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/7/1 15:06
 * @Description :关键帧处理器 The key frame processor
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class KeyFrameProcessor<T> implements Serializable, IKeyFrameHolder, TimelineDataParserAdapter<LMeicamKeyFrameHolder> {
    /**
     * 关键帧信息集合
     * The key frame info map
     */
    protected Map<Long, MeicamKeyFrame> keyFrameMap = new HashMap<>();

    void setKeyFrameMap(Map<Long, MeicamKeyFrame> keyFrameMap) {
        this.keyFrameMap = keyFrameMap;
    }

    private NvsObject<T> outObject;

    public KeyFrameProcessor(NvsObject<T> nvsObject) {
        outObject = nvsObject;
    }

    /**
     * 获取关键帧集合，注意：keyFrameMap集合是允许外部修改的，所以重新new了一个。
     * Gets key frame map.
     * Note: The keyFrameMap set is allowed to be modified externally, so a new one is added.
     *
     * @return the key frame map
     */
    public Map<Long, MeicamKeyFrame> getKeyFrameMap(String key) {
        if (TextUtils.isEmpty(key)) {
            return new HashMap<>(keyFrameMap);
        }
        if (keyFrameMap.isEmpty()) {
            return keyFrameMap;
        }
        Map<Long, MeicamKeyFrame> result = new HashMap<>();
        Set<Map.Entry<Long, MeicamKeyFrame>> entries = keyFrameMap.entrySet();
        for (Map.Entry<Long, MeicamKeyFrame> entry : entries) {
            MeicamKeyFrame keyFrame = entry.getValue();
            if (keyFrame != null) {
                List<MeicamFxParam<?>> paramList = keyFrame.getParamList();
                if (!CommonUtils.isEmpty(paramList)) {
                    for (MeicamFxParam<?> meicamFxParam : paramList) {
                        if (key.equals(meicamFxParam.getKey())) {
                            result.put(entry.getKey(), keyFrame);
                        }
                    }
                }
            }
        }
        return result;
    }


    /**
     * 添加关键帧
     * Add key frame
     *
     * @param atTime the time 时间点
     * @return MeicamKeyFrame 关键帧
     */
    public MeicamKeyFrame addKeyFrame(long atTime) {
        long l = getOutPoint() - getInPoint();
        if (atTime >= 0 && atTime <= l) {
            MeicamKeyFrame meicamKeyFrame = new MeicamKeyFrame();
            meicamKeyFrame.setAtTime(atTime);
            T object = getObject();
            if (object instanceof NvsFx) {
                meicamKeyFrame.setObject((NvsFx) object);
            } else {
                //throw new ClassCastException("You need add a NvsFx object");
            }
            keyFrameMap.put(atTime, meicamKeyFrame);
            return meicamKeyFrame;
        }
        return null;
    }

    /**
     * 添加关键帧
     * Add key frame
     *
     * @param atTime      the time 时间点
     * @param oldKeyFrame the old key frame 旧的关键帧(来自于复制)
     * @return MeicamKeyFrame 关键帧
     */
    public MeicamKeyFrame addKeyFrame(long atTime, MeicamKeyFrame oldKeyFrame) {
        if (oldKeyFrame != null) {
            if (atTime >= 0 && atTime <= getOutPoint() - getInPoint()) {
                oldKeyFrame.setAtTime(atTime);
                oldKeyFrame.bindToTimeline(true);
                keyFrameMap.put(oldKeyFrame.getAtTime(), oldKeyFrame);
                return oldKeyFrame;
            }
        }
        return null;
    }

    /**
     * 获取关键帧的数量
     * Get key frame count
     */
    public int getKeyFrameCount() {
        return keyFrameMap.size();
    }


    /**
     * 获取关键帧的数量
     * Gets key frame count.
     *
     * @param key the key 关键帧的关键字
     * @return the key frame count
     */
    public int getKeyFrameCount(String key) {
        if (TextUtils.isEmpty(key)) {
            return keyFrameMap.size();
        }
        T object = getObject();
        if (object instanceof NvsFx) {
            NvsFx nvsFx = (NvsFx) object;
            long keyframeTime = nvsFx.findKeyframeTime(key, -1, KEY_FRAME_FIND_MODE_INPUT_TIME_AFTER);
            int count = 0;
            while (keyframeTime >= 0) {
                count++;
                keyframeTime = nvsFx.findKeyframeTime(key, keyframeTime, KEY_FRAME_FIND_MODE_INPUT_TIME_AFTER);
            }
            return count;
        }
        return keyFrameMap.size();
    }

    /**
     * 是否有关键帧
     * Whether there are keyframes
     *
     * @param key 关键帧的关键字
     * @return the boolean
     */
    public boolean haveKeyFrame(String key) {
        if (TextUtils.isEmpty(key)) {
            return !keyFrameMap.isEmpty();
        }
        T object = getObject();
        if (object instanceof NvsFx) {
            NvsFx nvsFx = (NvsFx) object;
            long keyframeTime = nvsFx.findKeyframeTime(key, -1, KEY_FRAME_FIND_MODE_INPUT_TIME_AFTER);
            return keyframeTime >= 0;
        }
        return false;
    }

    /**
     * 获取关键帧
     * Get key frame
     *
     * @param atTime the key frame time关键帧的时间点
     * @return MeicamKeyFrame 关键帧
     */
    public MeicamKeyFrame getKeyFrame(long atTime) {
        return keyFrameMap.get(atTime);
    }

    /**
     * 获取关键帧
     * Get key frame
     *
     * @param key    the key of frame 关键帧的关键字
     * @param atTime the key frame time关键帧的时间点
     * @return MeicamKeyFrame 关键帧
     */
    public MeicamKeyFrame getKeyFrame(String key, long atTime) {
        if (TextUtils.isEmpty(key)) {
            return getKeyFrame(atTime);
        }
        MeicamKeyFrame keyFrame = keyFrameMap.get(atTime);
        if (keyFrame != null) {
            List<MeicamFxParam<?>> paramList = keyFrame.getParamList();
            if (!CommonUtils.isEmpty(paramList)) {
                for (MeicamFxParam<?> meicamFxParam : paramList) {
                    if (key.equals(meicamFxParam.getKey())) {
                        return keyFrame;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 查找所给关键帧之前或者之后的关键帧
     * Find the key frame before or after the given key frame
     *
     * @param keyFrame the key frame 所给关键帧
     * @param before   true find before查找之前的，false find after 查找之后的
     * @return MeicamKeyFrame关键帧
     */
    public MeicamKeyFrame findKeyFrame(MeicamKeyFrame keyFrame, boolean before) {
        if (keyFrame != null) {
            return findKeyFrame(keyFrame.getAtTime(), before);
        }
        return null;
    }

    /**
     * 查找所给时间点之前或者之后的关键帧
     * Find the key frame before or after the given key frame
     *
     * @param atTime the at time时间点
     * @param before true find before查找之前的，false find after 查找之后的
     * @return MeicamKeyFrame关键帧
     */
    public MeicamKeyFrame findKeyFrame(long atTime, boolean before) {
        MeicamKeyFrame findKeyFrame = null;
        MeicamKeyFrame tempKeyFrame;
        for (Map.Entry<Long, MeicamKeyFrame> entry : keyFrameMap.entrySet()) {
            if (before) {
                if (entry.getKey() < atTime) {
                    tempKeyFrame = entry.getValue();
                    if (findKeyFrame == null) {
                        findKeyFrame = tempKeyFrame;
                        continue;
                    }
                    if (findKeyFrame.getAtTime() < tempKeyFrame.getAtTime()) {
                        findKeyFrame = tempKeyFrame;
                    }
                }
            } else {
                if (entry.getKey() > atTime) {
                    tempKeyFrame = entry.getValue();
                    if (findKeyFrame == null) {
                        findKeyFrame = entry.getValue();
                    }
                    if (findKeyFrame.getAtTime() > tempKeyFrame.getAtTime()) {
                        findKeyFrame = tempKeyFrame;
                    }
                }
            }

        }
        return findKeyFrame;
    }

    /**
     * 查找所给时间点之前或者之后的关键帧
     * Find the key frame before or after the given key frame
     *
     * @param key    the key 关键字
     * @param atTime the at time时间点
     * @param before true find before查找之前的，false find after 查找之后的
     * @return MeicamKeyFrame关键帧
     */
    public MeicamKeyFrame findKeyFrame(String key, long atTime, boolean before) {
        if (TextUtils.isEmpty(key)) {
            return findKeyFrame(atTime, before);
        }
        MeicamKeyFrame keyFrame = findKeyFrame(atTime, before);
        while (keyFrame != null) {
            List<MeicamFxParam<?>> paramList = keyFrame.getParamList();
            if (!CommonUtils.isEmpty(paramList)) {
                for (MeicamFxParam<?> meicamFxParam : paramList) {
                    if (key.equals(meicamFxParam.getKey())) {
                        return keyFrame;
                    }
                }
            }
            keyFrame = findKeyFrame(keyFrame.getAtTime(), before);
        }
        return null;
    }

    /**
     * 移动所有关键帧
     * Move all key frame
     *
     * @param offset the offset时间差
     */
    public void moveAllKeyFrame(long offset) {
        Map<Long, MeicamKeyFrame> newKeyFrameMap = new HashMap<>(keyFrameMap.size());
        long newPoint;
        long duration = getOutPoint() - getInPoint();
        /*
        * 分两步处理的原因，如果某一个新的时间点和某个旧的时间点相同，会造成覆盖，或删除的旧时候把新的删除掉
        * The reason for two-step processing is that if a new time point is the same as an old time point,
        * it will cause overwriting or delete the new one when deleting the old one
        * */
        for (Map.Entry<Long, MeicamKeyFrame> keyFrameEntry : keyFrameMap.entrySet()) {
            MeicamKeyFrame keyFrame = keyFrameEntry.getValue();
            newPoint = keyFrame.getAtTime() + offset;
            if (newPoint >= 0 && newPoint <= duration) {
                keyFrame.removeKeyFrame(false);
                MeicamKeyFrame next = keyFrame.getNext();
                keyFrame.setAtTime(newPoint);

                if (next != null) {
                    next.setAtTime(next.getAtTime() + offset);
                }

                MeicamKeyFrame font = keyFrame.getFont();
                if (font != null) {
                    font.setAtTime(font.getAtTime() + offset);
                }

                newKeyFrameMap.put(newPoint, keyFrame);
            }
        }
        keyFrameMap = newKeyFrameMap;
        for (Map.Entry<Long, MeicamKeyFrame> keyFrameEntry : newKeyFrameMap.entrySet()) {
            MeicamKeyFrame keyFrame = keyFrameEntry.getValue();
            keyFrame.bindToTimeline(true);
        }
        updateKeyFrameControlPoints();
    }

    /**
     * 倒转关键帧
     * Reverse key frame
     */
    public void reverseKeyFrame() {
        Map<Long, MeicamKeyFrame> newKeyFrameMap = new HashMap<>(keyFrameMap.size());
        long newPoint;
        long duration = getOutPoint() - getInPoint();
        /*
        * 分两步处理的原因，如果某一个新的时间点和某个旧的时间点相同，会造成覆盖，或删除的旧时候把新的删除掉
        * The reason for two-step processing is that if a new time point is the same as an old time point,
        * it will cause overwriting or delete the new one when deleting the old one
        * */
        for (Map.Entry<Long, MeicamKeyFrame> keyFrameEntry : keyFrameMap.entrySet()) {
            MeicamKeyFrame keyFrame = keyFrameEntry.getValue();
            newPoint = duration - keyFrame.getAtTime();
            keyFrame.removeKeyFrame(false);
            if (newPoint >= 0 && newPoint <= duration) {
                keyFrame.setAtTime(newPoint);
                newKeyFrameMap.put(newPoint, keyFrame);
            }
        }
        keyFrameMap = newKeyFrameMap;
        for (Map.Entry<Long, MeicamKeyFrame> keyFrameEntry : newKeyFrameMap.entrySet()) {
            MeicamKeyFrame keyFrame = keyFrameEntry.getValue();
            keyFrame.bindToTimeline(true);
        }
        updateKeyFrameControlPoints();
    }

    /**
     * 缩放关键帧
     * Zoom key frame
     *
     * @param scaleFactor the scale factor 缩放因子
     */
    public void zoomKeyFrame(double scaleFactor) {
        if (keyFrameMap.size() <= 0) {
            return;
        }
        Map<Long, MeicamKeyFrame> newKeyFrameMap = new HashMap<>(keyFrameMap.size());
        long newPoint;
        long duration = getOutPoint() - getInPoint();
        /*
         * 分两步处理的原因，如果某一个新的时间点和某个旧的时间点相同，会造成覆盖，或删除的旧时候把新的删除掉
         * The reason for two-step processing is that if a new time point is the same as an old time point,
         * it will cause overwriting or delete the new one when deleting the old one
         * */
        for (Map.Entry<Long, MeicamKeyFrame> keyFrameEntry : keyFrameMap.entrySet()) {
            MeicamKeyFrame keyFrame = keyFrameEntry.getValue();
            newPoint = (long) (keyFrame.getAtTime() / scaleFactor);
            // LogUtils.d("newPoint=" + newPoint + ",duration=" + duration + ",out=" + (newPoint > duration) + ",scaleFactor=" + scaleFactor);
            if (newPoint >= 0 && newPoint <= duration) {
                keyFrame.removeKeyFrame(false);
                keyFrame.setAtTime(newPoint);
                newKeyFrameMap.put(newPoint, keyFrame);
            }
        }
        zoomKeyframeCurve(newKeyFrameMap);
        keyFrameMap = newKeyFrameMap;
        for (Map.Entry<Long, MeicamKeyFrame> keyFrameEntry : newKeyFrameMap.entrySet()) {
            MeicamKeyFrame keyFrame = keyFrameEntry.getValue();
            keyFrame.bindToTimeline(true);
        }
        updateKeyFrameControlPoints();
    }

    /**
     * Zoom key frame curve, you need to call when the time of scaling the keyframe position is completed
     * 缩放关键帧曲线，需要缩放关键帧位置时间完成的时候调用
     *
     * @param keyFrameMap 关键帧map the keyframe map
     */
    private void zoomKeyframeCurve(Map<Long, MeicamKeyFrame> keyFrameMap) {
        if (keyFrameMap == null || keyFrameMap.isEmpty()) {
            return;
        }
        Set<Map.Entry<Long, MeicamKeyFrame>> entries = keyFrameMap.entrySet();
        List<MeicamKeyFrame> sortList = new ArrayList<>();
        for (Map.Entry<Long, MeicamKeyFrame> entry : entries) {
            sortList.add(entry.getValue());
        }
        Collections.sort(sortList, new Comparator<MeicamKeyFrame>() {
            @Override
            public int compare(MeicamKeyFrame o1, MeicamKeyFrame o2) {
                return (int) (o1.getAtTime() - o2.getAtTime());
            }
        });
        int size = sortList.size();
        for (int index = 0; index < size; index++) {
            MeicamKeyFrame firstKeyFrame = sortList.get(index);
            if (index + 1 < size) {
                MeicamKeyFrame nextKeyFrame = sortList.get(index + 1);
                MeicamKeyframeControlPoints fontControlPoints = firstKeyFrame.getControlPoints();
                MeicamKeyframeControlPoints backControlPoints = nextKeyFrame.getControlPoints();
                if (fontControlPoints != null && backControlPoints != null && firstKeyFrame.getNext() != null &&
                        firstKeyFrame.getNext().getAtTime() == nextKeyFrame.getAtTime()) {
                    addControlPoints(firstKeyFrame, nextKeyFrame, fontControlPoints, backControlPoints);
                }
            }
        }
    }


    /**
     * Add control points
     * 添加控制点
     *
     * @param forwardKeyframe   前关键帧 the forward keyframe node
     * @param backwardKeyframe  后关键帧 the backward keyframe  node
     * @param fontControlPoints 前关键帧的控制点 the control point of forward keyframe
     * @param backControlPoints 后关键帧的控制点 the control point of backward keyframe
     *                          * @return 关键控制点对 the pair of MeicamKeyframeControlPoints
     */
    private Pair<MeicamKeyframeControlPoints, MeicamKeyframeControlPoints> addControlPoints(MeicamKeyFrame forwardKeyframe, MeicamKeyFrame backwardKeyframe,
                                                                                            MeicamKeyframeControlPoints fontControlPoints,
                                                                                            MeicamKeyframeControlPoints backControlPoints) {
        if (forwardKeyframe == null || backwardKeyframe == null || fontControlPoints == null || backControlPoints == null) {
            LogUtils.e("params are error!");
            return null;
        }
        return addControlPoints(forwardKeyframe, backwardKeyframe, fontControlPoints.getForwardControlPoint(), backControlPoints.getBackwardControlPoint());
    }

    /**
     * Add control points
     * 添加控制点
     *
     * @param forwardKeyframe   前关键帧 the forward keyframe node
     * @param backwardKeyframe  后关键帧 the backward keyframe  node
     * @param fontControlPoints 前置控制点 the forward control point
     * @param backControlPoints 后置控制点 the backward control point
     * @return 关键控制点对 the pair of MeicamKeyframeControlPoints
     */
    private Pair<MeicamKeyframeControlPoints, MeicamKeyframeControlPoints> addControlPoints(MeicamKeyFrame forwardKeyframe, MeicamKeyFrame backwardKeyframe,
                                                                                            FloatPoint fontControlPoints,
                                                                                            FloatPoint backControlPoints) {
        if (forwardKeyframe == null || backwardKeyframe == null || fontControlPoints == null || backControlPoints == null) {
            LogUtils.e("params are error!");
            return null;
        }
        MeicamKeyframeControlPoints forward = forwardKeyframe.addControlPoints(backwardKeyframe, fontControlPoints, true);
        MeicamKeyframeControlPoints backward = backwardKeyframe.addControlPoints(forwardKeyframe, backControlPoints, false);
        if (forward != null || backward != null) {
            return new Pair<>(forward, backward);
        }
        return null;
    }


    /**
     * Get remove param keys string [ ].
     * 获取要删除的关键帧帧参数的kye值
     *
     * @param keyOfKeyFrame the key of key frame 关键帧的key
     * @return the string [ ]
     */
    public static String[] getRemoveParamKeys(String keyOfKeyFrame) {
        String[] keys = null;
        if (!TextUtils.isEmpty(keyOfKeyFrame)) {
            keys = new String[]{keyOfKeyFrame};
        }
        if (KEY_CROPPER_TRANS_X.equals(keyOfKeyFrame)) {
            keys = new String[]{NvsConstants.KEY_CROPPER_TRANS_X,
                    NvsConstants.KEY_CROPPER_TRANS_Y,
                    NvsConstants.KEY_CROPPER_ROTATION,
                    NvsConstants.KEY_CROPPER_SCALE_X,
                    NvsConstants.KEY_CROPPER_SCALE_Y
            };
        } else if (NvsConstants.KEY_MASK_OPACITY.equals(keyOfKeyFrame)) {
            keys = new String[]{NvsConstants.KEY_MASK_OPACITY};
        } else if (NvsConstants.KEY_MASK_REGION_INFO.equals(keyOfKeyFrame)) {
            keys = new String[]{NvsConstants.KEY_MASK_REGION_INFO,
                    NvsConstants.KEY_MASK_INVERSE_REGION,
                    NvsConstants.KEY_MASK_KEEP_RGB,
                    NvsConstants.KEY_MASK_INVERSE_REGION
            };
        } else if (MeicamKeyFrame.CAPTION_SCALE_X.equals(keyOfKeyFrame)) {
        } else if (MeicamKeyFrame.STICKER_SCALE.equals(keyOfKeyFrame)) {
        }
        return keys;
    }

    /**
     * 移除关键帧
     * delete key frame
     *
     * @param atTime the time 时间点
     * @return true success成功,false失败
     */
    public boolean removeKeyFrame(long atTime) {
        return removeKeyFrame(null, atTime, true);
    }

    /**
     * 移除关键帧
     * delete key frame
     *
     * @param keys   the keys 关键值
     * @param atTime the time 时间点
     * @return true success成功,false失败
     */
    public boolean removeKeyFrame(String[] keys, long atTime) {
        return removeKeyFrame(keys, atTime, true);
    }

    /**
     * 移除所有关键帧
     * delete all key frame
     */
    public void removeAllKeyFrame() {
        Map<Long, MeicamKeyFrame> keyFrameMap = getKeyFrameMap(null);
        if (CommonUtils.isEmpty(keyFrameMap)) {
            return;
        }
        Set<Long> atTimes = keyFrameMap.keySet();
        for (Long atTime : atTimes) {
            removeKeyFrame(null, atTime, true);
        }
    }


    /**
     * 移除关键帧
     * delete key frame
     *
     * @param keys          the keys 关键值
     * @param atTime        the time 时间点
     * @param removeContent true remove the list of MeicamKeyFrame ,false not
     * @return true success成功,false失败
     */
    boolean removeKeyFrame(String[] keys, long atTime, boolean removeContent) {
        MeicamKeyFrame meicamKeyFrame = keyFrameMap.get(atTime);
        if (meicamKeyFrame != null) {
            HashSet<String> keySet = null;
            if (keys != null && keys.length > 0) {
                keySet = new HashSet<>(Arrays.asList(keys));
            }
            meicamKeyFrame.removeKeyFrame(keySet, removeContent);
            meicamKeyFrame.setObject(null);
            if (!CommonUtils.isEmpty(keySet)) {
                meicamKeyFrame.removeParam(keySet);
            }
            List<MeicamFxParam<?>> paramList = meicamKeyFrame.getParamList();
            if (paramList.isEmpty()) {
                keyFrameMap.remove(atTime);
            }
            return true;
        }
        return false;
    }

    /**
     * Restore key frame.
     * 恢复关键帧
     * @param oldData the old data
     */
    public void restoreKeyFrame(Map<Long, MeicamKeyFrame> oldData){
        removeAllKeyFrame();
        this.keyFrameMap = oldData;
        if (keyFrameMap != null && !keyFrameMap.isEmpty()) {
            Iterator<Map.Entry<Long, MeicamKeyFrame>> item = keyFrameMap.entrySet().iterator();
            while (item.hasNext()) {
                Map.Entry<Long, MeicamKeyFrame> next = item.next();
                if (next.getKey() > getOutPoint()) {
                    /*删除无效的关键帧*/
                    item.remove();
                } else {
                    MeicamKeyFrame meicamKeyFrame = next.getValue();
                    meicamKeyFrame.setObject((NvsFx) getObject());
                    meicamKeyFrame.bindToTimeline(true);
                }
            }
            updateKeyFrameControlPoints();
        }
    }

    /**
     * 重新设置一下关键帧
     * reset key frame
     *
     * @param fromNvsFx true reset from nvsFx 根据底层重置关键帧,false not 根据上层数据重置关键帧
     */
    public void resetKeyFrame(boolean fromNvsFx) {
        if (fromNvsFx) {
            MeicamKeyFrame oneKeyFrame = null;
            String oneKey = "";
            for (Map.Entry<Long, MeicamKeyFrame> keyFrameEntry : keyFrameMap.entrySet()) {
                MeicamKeyFrame keyFrame = keyFrameEntry.getValue();
                MeicamFxParam<?> fxParam = keyFrame.getFxParam(0);
                if (fxParam != null) {
                    oneKey = fxParam.getKey();
                    oneKeyFrame = keyFrame;
                    break;
                }
            }
            if (!TextUtils.isEmpty(oneKey) && oneKeyFrame != null) {
                /*
                * 清空上层数据
                * clear data
                * */
                keyFrameMap.clear();
                /*
                * 从底层获取所有关键帧的点
                * Get all point of key frame from native
                * */
                long keyframeTime = oneKeyFrame.findKeyframeTime(oneKey, -1, KEY_FRAME_FIND_MODE_INPUT_TIME_AFTER);
                while (keyframeTime >= 0) {
                    /*
                    * 添加关键帧
                    * Add key frame
                    * */
                    LogUtils.d("atTime=" + keyframeTime);
                    MeicamKeyFrame keyFrame = addKeyFrame(keyframeTime);
                    if (keyFrame != null) {
                        keyFrame.update(false);
                    }
                    keyframeTime = oneKeyFrame.findKeyframeTime(oneKey, keyframeTime, KEY_FRAME_FIND_MODE_INPUT_TIME_AFTER);
                }
            }
        } else {
            for (Map.Entry<Long, MeicamKeyFrame> item : keyFrameMap.entrySet()) {
                MeicamKeyFrame value = item.getValue();
                if (value != null) {
                    value.removeKeyFrame(false);
                    value.setParamList(value.getParamList());
                }
            }
        }
        updateBrothers();
        updateKeyFrameControlPoints();
    }

    /**
     * Update key frame control points.
     * 批量更新关键帧控制点
     */
    public void updateKeyFrameControlPoints() {
        if (keyFrameMap != null && !keyFrameMap.isEmpty()) {
            Set<Map.Entry<Long, MeicamKeyFrame>> entries = keyFrameMap.entrySet();
            for (Map.Entry<Long, MeicamKeyFrame> entry : entries) {
                entry.getValue().bindCurveToTimeline();
            }
        }
    }

    /**
     * Update key frame control points.
     * 批量更新关键帧控制点
     */
    private void updateBrothers() {
        if (keyFrameMap != null && !keyFrameMap.isEmpty()) {
            Set<Map.Entry<Long, MeicamKeyFrame>> entries = keyFrameMap.entrySet();
            for (Map.Entry<Long, MeicamKeyFrame> entry : entries) {
                updateBrothers(entry.getValue());
            }
        }
    }

    /**
     * Update control points.
     * 批量更新控制点
     */
    private void updateControlPoints() {
        updateBrothers();
        if (keyFrameMap != null && !keyFrameMap.isEmpty()) {
            Set<Map.Entry<Long, MeicamKeyFrame>> entries = keyFrameMap.entrySet();
            for (Map.Entry<Long, MeicamKeyFrame> entry : entries) {
                MeicamKeyFrame value = entry.getValue();
                value.updateControlPoint();
            }
        }
    }


    /**
     * Update brothers.
     * 更新兄弟节点
     *
     * @param keyFrame the key frame
     */
    public void updateBrothers(MeicamKeyFrame keyFrame) {
        if (keyFrame == null) {
            return;
        }
        Pair<MeicamKeyFrame, MeicamKeyFrame> framePair = getFramePair(keyFrame.getAtTime());
        if (framePair != null && framePair.first != null && framePair.second != null) {
            if (framePair.first.getAtTime() == keyFrame.getAtTime()) {
                framePair.second.setFont(framePair.first.clone());
            }
            if (framePair.second.getAtTime() == keyFrame.getAtTime()) {
                framePair.first.setNext(framePair.second.clone());
            }
        }
    }

    /**
     * Add key frame curve
     * 添加关键帧曲线
     *
     * @param keyFramePair      关键帧对 the pair of key frame
     * @param startControlPoint 开始控制点 the start control point
     * @param endControlPoint   结束控制点  the end control point
     * @return true success成功,false失败
     */
    public boolean addKeyFrameCurve(Pair<MeicamKeyFrame, MeicamKeyFrame> keyFramePair, FloatPoint startControlPoint, FloatPoint endControlPoint, int id) {
        if (keyFramePair == null || keyFramePair.first == null || keyFramePair.second == null) {
            LogUtils.e("key frames are null!");
            return false;
        }
        Pair<MeicamKeyframeControlPoints, MeicamKeyframeControlPoints> controlPointsPair = addControlPoints(keyFramePair.first, keyFramePair.second, startControlPoint, endControlPoint);
        if (controlPointsPair == null) {
            return false;
        }
        MeicamKeyframeControlPoints fontControlPoints = controlPointsPair.first;
        if (fontControlPoints != null) {
            fontControlPoints.setFontPointId(id);
        }
        MeicamKeyframeControlPoints backControlPoints = controlPointsPair.second;
        if (backControlPoints != null) {
            backControlPoints.setBackPointId(id);
        }
        return fontControlPoints != null && backControlPoints != null;
    }

    /**
     * Cut key frame curve
     * 切割关键帧曲线
     *
     * @param atTime atTime 时间点 the at time
     * @param key             关键帧的关键字 the key of key frame
     * @return true success成功,false失败
     */
    public boolean cutKeyFrameCurve(long atTime, String key) {
        MeicamKeyFrame newBackKeyFrame = getKeyFrame(key, atTime);
        if (newBackKeyFrame == null) {
            return false;
        }
        MeicamKeyFrame beforeFrame = findKeyFrame(key, atTime, true);
        MeicamKeyFrame afterFrame = findKeyFrame(key, atTime, false);
        boolean result = false;
        if (beforeFrame != null && afterFrame != null) {
            if (beforeFrame.getNext() != null && beforeFrame.getNext().getAtTime() == afterFrame.getAtTime()) {
                MeicamKeyframeControlPoints firstControlPoints = beforeFrame.getControlPoints();
                MeicamKeyframeControlPoints afterControlPoints = afterFrame.getControlPoints();
                Pair<MeicamKeyframeControlPoints, MeicamKeyframeControlPoints> controlPointsPair =
                        addControlPoints(beforeFrame, newBackKeyFrame, firstControlPoints, afterControlPoints);
                afterFrame.removeBackwardControlPoints();
                if (controlPointsPair != null && controlPointsPair.second != null) {
                    controlPointsPair.second.setId(firstControlPoints.getId());
                    controlPointsPair.second.setFontPointId(afterControlPoints.getFontPointId());
                    controlPointsPair.second.setBackPointId(afterControlPoints.getBackPointId());
                    result = controlPointsPair.first != null && controlPointsPair.second != null;
                }
                updateBrothers();
                updateKeyFrameControlPoints();
                return result;
            }
        }
        return false;
    }

    /**
     * Cut key frame curve
     * 切割关键帧曲线
     *
     * @param newBackKeyFrame 新的末尾关键帧 the new back key frame
     * @param key             关键帧的关键字 the key of key frame
     * @return true success成功,false失败
     */
    public boolean cutKeyFrameCurve(MeicamKeyFrame newBackKeyFrame, String key) {
        if (newBackKeyFrame == null) {
            return false;
        }
        long atTime = newBackKeyFrame.getAtTime();
        MeicamKeyFrame beforeFrame = findKeyFrame(key, atTime, true);
        MeicamKeyFrame afterFrame = findKeyFrame(key, atTime, false);
        boolean result = false;
        if (beforeFrame != null && afterFrame != null) {
            if (beforeFrame.getNext() != null && beforeFrame.getNext().getAtTime() == afterFrame.getAtTime()) {
                MeicamKeyframeControlPoints firstControlPoints = beforeFrame.getControlPoints();
                MeicamKeyframeControlPoints afterControlPoints = afterFrame.getControlPoints();
                Pair<MeicamKeyframeControlPoints, MeicamKeyframeControlPoints> controlPointsPair =
                        addControlPoints(beforeFrame, newBackKeyFrame, firstControlPoints, afterControlPoints);
                afterFrame.removeBackwardControlPoints();
                if (controlPointsPair != null && controlPointsPair.second != null) {
                    controlPointsPair.second.setId(firstControlPoints.getId());
                    controlPointsPair.second.setFontPointId(afterControlPoints.getFontPointId());
                    controlPointsPair.second.setBackPointId(afterControlPoints.getBackPointId());
                    result = controlPointsPair.first != null && controlPointsPair.second != null;
                }
                updateBrothers();
                updateKeyFrameControlPoints();
                return result;
            }
        }
        return false;
    }

    /**
     * Merge key frame curve
     * 合并关键帧曲线，这个方法会删除中间点
     *
     * @param atTime 时间点 the at time
     * @param key    关键帧的关键字 the key of key frame
     * @return true success成功,false失败
     */
    public boolean mergeKeyFrame(long atTime, String key) {
        MeicamKeyFrame middleKeyFrame = getKeyFrame(key, atTime);
        MeicamKeyFrame beforeFrame = findKeyFrame(key, atTime, true);
        MeicamKeyFrame afterFrame = findKeyFrame(key, atTime, false);
        if (beforeFrame != null && afterFrame != null && middleKeyFrame != null) {
            if (middleKeyFrame.getNext() != null && middleKeyFrame.getNext().getAtTime() == beforeFrame.getAtTime()) {
                MeicamKeyframeControlPoints firstControlPoints = middleKeyFrame.getControlPoints();
                MeicamKeyframeControlPoints afterControlPoints = beforeFrame.getControlPoints();
                //删除中间点
                //Delete middle point
                removeKeyFrame(new String[]{key}, atTime);
                //连接前后点，实现关键帧曲线合并
                //Connect front and rear points to merge keyframe curves.
                Pair<MeicamKeyframeControlPoints, MeicamKeyframeControlPoints> controlPointsPair =
                        addControlPoints(beforeFrame, afterFrame, firstControlPoints, afterControlPoints);
                if (controlPointsPair != null && controlPointsPair.second != null) {
                    controlPointsPair.second.setId(firstControlPoints.getId());
                    controlPointsPair.second.setFontPointId(firstControlPoints.getFontPointId());
                    controlPointsPair.second.setBackPointId(firstControlPoints.getBackPointId());
                    return controlPointsPair.first != null && controlPointsPair.second != null;
                }
            }
        }
        return false;
    }

    /**
     * Gets frame pair.
     * 获取关键帧对
     *
     * @param time 时间 the time
     * @return 关键帧对the frame pair
     */
    public Pair<MeicamKeyFrame, MeicamKeyFrame> getFramePair(long time) {
        MeicamKeyFrame keyFrame = getKeyFrame(time);
        MeicamKeyFrame beforeFrame;
        MeicamKeyFrame backFrame;
        beforeFrame = findKeyFrame(time, true);
        if (keyFrame != null) {
            if (beforeFrame != null) {
                backFrame = keyFrame;
            } else {
                beforeFrame = keyFrame;
                backFrame = findKeyFrame(time, false);
            }
        } else {
            backFrame = findKeyFrame(time, false);
        }
        if (beforeFrame != null && backFrame != null) {
            return new Pair<>(beforeFrame, backFrame);
        }
        return null;
    }

    /**
     * Gets frame pair.
     * 获取关键帧对
     *
     * @param key  关键帧的关键字 the key
     * @param time 时间 the time
     * @return 关键帧对the frame pair
     */
    @Override
    public Pair<MeicamKeyFrame, MeicamKeyFrame> getFramePair(String key, long time) {
        if (TextUtils.isEmpty(key)) {
            return getFramePair(time);
        }
        MeicamKeyFrame keyFrame = getKeyFrame(time);
        MeicamKeyFrame beforeFrame;
        MeicamKeyFrame backFrame;
        beforeFrame = findKeyFrame(key, time, true);
        if (keyFrame != null) {
            if (beforeFrame != null) {
                backFrame = keyFrame;
            } else {
                beforeFrame = keyFrame;
                backFrame = findKeyFrame(key, time, false);
            }
        } else {
            backFrame = findKeyFrame(key, time, false);
        }
        if (beforeFrame != null && backFrame != null) {
            return new Pair<>(beforeFrame, backFrame);
        }
        return null;
    }

    /**
     * Recover key frame.
     * 恢复关键帧
     *
     * @param lClipInfo the l clip info
     */
    protected void recoverKeyFrame(LClipInfo lClipInfo) {
        List<LMeicamKeyFrame> keyFrameList = lClipInfo.getKeyFrameList();
        if (keyFrameList != null) {
            for (LMeicamKeyFrame lMeicamKeyFrame : keyFrameList) {
                MeicamKeyFrame keyFrame = addKeyFrame(lMeicamKeyFrame.getAtTime());
                if (keyFrame != null) {
                    keyFrame.recoverFromLocalData(lMeicamKeyFrame);
                }
            }
            updateKeyFrameControlPoints();
        }
    }

    private boolean findAndAddKeyFrame(NvsVideoFx nvsVideoFx, String key) {
        boolean addSuccess = false;
        long keyframeTime = nvsVideoFx.findKeyframeTime(key, -1, KEY_FRAME_FIND_MODE_INPUT_TIME_AFTER);
        while (keyframeTime >= 0) {
            /*
            *添加关键帧
            *Add key frame
            *  */
            MeicamKeyFrame keyFrame = addKeyFrame(keyframeTime);
            if (keyFrame != null) {
                keyFrame.recoverFromTimelineData(nvsVideoFx);
            }
            addSuccess = true;
            keyframeTime = nvsVideoFx.findKeyframeTime(key, keyframeTime, KEY_FRAME_FIND_MODE_INPUT_TIME_AFTER);
        }
        return addSuccess;
    }

    /**
     * Gets key frame params.
     * <p>
     * 获取关键帧参数
     *
     * @param key    the key
     * @param atTime the at time
     * @return the key frame params
     */
    public List<MeicamFxParam<?>> getKeyFrameParams(String key, long atTime) {
        MeicamKeyFrame keyFrame = getKeyFrame(key, atTime);
        if (keyFrame != null) {
            return keyFrame.getParamsFromAtTime(atTime);
        }
        return getNearbyKeyFrameParamsAtTime(key, atTime);
    }

    /**
     * Gets nearby key frame params at time.
     * <p>
     * 获取当前时间的附近的关键帧参数
     *
     * @param key    the key
     * @param atTime the at time
     * @return the key frame params
     */
    public List<MeicamFxParam<?>> getNearbyKeyFrameParamsAtTime(String key, long atTime) {
        if (TextUtils.isEmpty(key)) {
            return getNearbyKeyFrameParamsAtTime(atTime);
        }
        MeicamKeyFrame keyFrame = findKeyFrame(key, atTime, true);
        if (keyFrame == null) {
            keyFrame = findKeyFrame(key, atTime, false);
        }
        if (keyFrame != null) {
            return keyFrame.getParamsFromAtTime(atTime);
        }
        return new ArrayList<>();
    }

    /**
     * Gets nearby key frame params at time.
     * <p>
     * 获取当前时间的附近的关键帧参数
     *
     * @param atTime the at time
     * @return the key frame params
     */
    private List<MeicamFxParam<?>> getNearbyKeyFrameParamsAtTime(long atTime) {
        MeicamKeyFrame keyFrame = findKeyFrame(atTime, true);
        if (keyFrame == null) {
            keyFrame = findKeyFrame(atTime, false);
        }
        if (keyFrame != null) {
            return keyFrame.getParamsFromAtTime(atTime);
        }
        return new ArrayList<>();
    }

    public long getInPoint() {
        return outObject.getInPoint();
    }

    public long getOutPoint() {
        return outObject.getOutPoint();
    }

    public T getObject() {
        return outObject.getObject();
    }

    public void bindToTimeline() {
        /*
        * 还原关键帧
        * Restore key frame
        * */
        if (keyFrameMap != null && !keyFrameMap.isEmpty()) {
            Iterator<Map.Entry<Long, MeicamKeyFrame>> item = keyFrameMap.entrySet().iterator();
            while (item.hasNext()) {
                Map.Entry<Long, MeicamKeyFrame> next = item.next();
                if (next.getKey() > getOutPoint()) {
                    /*
                    * 删除无效的关键帧
                    * Delete invalid key frame
                    * */
                    item.remove();
                } else {
                    MeicamKeyFrame meicamKeyFrame = next.getValue();
                    meicamKeyFrame.setObject((NvsFx) getObject());
                    meicamKeyFrame.bindToTimeline(true);
                }
            }
            updateKeyFrameControlPoints();
        }
    }

    @Override
    public void recoverFromLocalData(LMeicamKeyFrameHolder local) {
        if (local == null) {
            return;
        }
        if (!CommonUtils.isEmpty(local.keyFrameMap)) {
            Map<Long, LMeicamKeyFrame> keyFrameMap = local.keyFrameMap;
            for (Map.Entry<Long, LMeicamKeyFrame> keyFrameEntry : keyFrameMap.entrySet()) {
                Long atTime = keyFrameEntry.getKey();
                MeicamKeyFrame keyFrame = addKeyFrame(atTime);
                if (keyFrame != null) {
                    keyFrame.recoverFromLocalData(keyFrameEntry.getValue());
                }
            }
            updateKeyFrameControlPoints();
        }
    }

    @Override
    public LMeicamKeyFrameHolder parseToLocalData() {
        LMeicamKeyFrameHolder holder = new LMeicamKeyFrameHolder();
        if (keyFrameMap != null && keyFrameMap.size() > 0) {
            Map<Long, LMeicamKeyFrame> lKeyFrameList = new HashMap<>();
            for (Map.Entry<Long, MeicamKeyFrame> entry : keyFrameMap.entrySet()) {
                MeicamKeyFrame value = entry.getValue();
                if (value != null) {
                    lKeyFrameList.put(entry.getKey(), value.parseToLocalData());
                }
            }
            holder.setKeyFrameMap(lKeyFrameList);
        }
        return holder;
    }

    /**
     * 恢复关键帧上层数据
     * Recover meicam key frame.
     *
     * @param nvsObject the nvs object
     */
    @Override
    public void recoverFromTimelineData(com.meicam.sdk.NvsObject nvsObject) {
        if (nvsObject instanceof NvsTimelineCaption) {
            NvsTimelineCaption nvsTimelineCaption = (NvsTimelineCaption) nvsObject;
            long keyframeTime = nvsTimelineCaption.findKeyframeTime(CAPTION_SCALE_X, -1, KEY_FRAME_FIND_MODE_INPUT_TIME_AFTER);
            while (keyframeTime >= 0) {
                /*
                * 添加关键帧
                * Add key frame
                * */
                MeicamKeyFrame keyFrame = addKeyFrame(keyframeTime);
                if (keyFrame != null) {
                    keyFrame.recoverFromTimelineData(nvsTimelineCaption);
                }
                keyframeTime = nvsTimelineCaption.findKeyframeTime(CAPTION_SCALE_X, keyframeTime, KEY_FRAME_FIND_MODE_INPUT_TIME_AFTER);
            }
        } else if (nvsObject instanceof NvsTimelineAnimatedSticker) {
            NvsTimelineAnimatedSticker nvsTimelineSticker = (NvsTimelineAnimatedSticker) nvsObject;
            long keyframeTime = nvsTimelineSticker.findKeyframeTime(STICKER_SCALE, -1, KEY_FRAME_FIND_MODE_INPUT_TIME_AFTER);
            while (keyframeTime >= 0) {
                /*
                 * 添加关键帧
                 * Add key frame
                 * */
                MeicamKeyFrame keyFrame = addKeyFrame(keyframeTime);
                if (keyFrame != null) {
                    keyFrame.recoverFromTimelineData(nvsTimelineSticker);
                }
                keyframeTime = nvsTimelineSticker.findKeyframeTime(STICKER_SCALE, keyframeTime, KEY_FRAME_FIND_MODE_INPUT_TIME_AFTER);
            }
        } else if (nvsObject instanceof NvsVideoFx) {
            //这里恢复的属性特技和蒙版的关键帧
            //Here restore the key frames of attribute stunts and masks.
            NvsVideoFx nvsVideoFx = (NvsVideoFx) nvsObject;
            if (!findAndAddKeyFrame(nvsVideoFx, SCALE_X)) {
                findAndAddKeyFrame(nvsVideoFx, NvsConstants.KEY_MASK_FEATHER_WIDTH);
            }
        }
        //更新控制点
        //Update control points
        updateControlPoints();
    }

    /**
     * Gets key of key frame. The default is the key of VideoClip
     *获取关键帧的关键字，默认返回视频的关键字
     * @return the key of key frame
     */
    public String getKeyOfKeyFrame() {
        T object = getObject();
        if (object instanceof NvsTimelineAnimatedSticker) {
            return STICKER_SCALE;
        } else if (object instanceof NvsTimelineCaption) {
            return CAPTION_SCALE_X;
        } else{
            return SCALE_X;
        }
    }
}
