package com.meishe.engine.command;

import com.meishe.annotation.Undo;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.MeicamAudioClip;
import com.meishe.engine.bean.MeicamAudioTrack;
import com.meishe.engine.bean.MeicamTimeline;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/9/6 11:06
 * @Description :音频轨道命令 The audio track command.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class AudioTrackCommand {
    private static String TAG = "audioTrack";

    @Undo(className = "AddAudioClipCommand", function = "removeAudioClip",
            param = {"int|index", "boolean|keepSpace", "boolean...|needSaveOperate"})
    public static MeicamAudioClip addAudioClip(MeicamAudioTrack audioTrack, String audioPath,
                                               long inPoint, long trimIn, long trimOut, boolean... needSaveOperate) {
        MeicamAudioClip audioClip = audioTrack.addAudioClip(audioPath, inPoint, trimIn, trimOut);
        if (audioClip == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(audioTrack);
            String tagAndKey = tag + inPoint;
            Object[] unDoParam = new Object[]{audioClip.getIndex(), true, new boolean[]{false}};
            Object[] redoParam = new Object[]{audioPath, inPoint, trimIn, trimOut};
            CommandUtil.saveOperate("AddAudioClipCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        return audioClip;
    }

    @Undo(className = "RemoveAudioClipCommand", function = "addAudioClip",
            param = {"MeicamAudioClip|audioClip", "long|inPoint", "long|trimIn", "long|trimOut", "boolean...|needSaveOperate"})
    public static MeicamAudioClip removeAudioClip(MeicamAudioTrack audioTrack, int index, boolean keepSpace, boolean... needSaveOperate) {
        MeicamAudioClip audioClip = audioTrack.removeAudioClip(index, keepSpace);
        if (audioClip == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(audioTrack);
            String tagAndKey = tag + audioClip.getIndex();
            Object[] unDoParam = new Object[]{audioClip, audioClip.getInPoint(), audioClip.getTrimIn(), audioClip.getTrimOut(), new boolean[]{false}};
            Object[] redoParam = new Object[]{index, keepSpace};
            CommandUtil.saveOperate("RemoveAudioClipCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        return audioClip;
    }

    @Undo(className = "AddAudioClipCommand1", function = "removeAudioClip",
            param = {"int|index", "boolean|keepSpace", "boolean...|needSaveOperate"})
    public static MeicamAudioClip addAudioClip(MeicamAudioTrack audioTrack, MeicamAudioClip audioClip,
                                               long inPoint, long trimIn, long trimOut, boolean... needSaveOperate) {
        MeicamAudioClip newAudioClip = audioTrack.addAudioClip(audioClip, inPoint, trimIn, trimOut);
        if (newAudioClip == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(audioTrack);
            String tagAndKey = tag + audioClip.getIndex();
            Object[] unDoParam = new Object[]{newAudioClip.getIndex(), true, new boolean[]{false}};
            Object[] redoParam = new Object[]{audioClip, inPoint, trimIn, trimOut};
            CommandUtil.saveOperate("AddAudioClipCommand1", unDoParam, redoParam, tag, tagAndKey);
        }
        return audioClip;
    }

    @Undo(className = "CopyAudioClipCommand", function = "removeAudioClip",
            param = {"int|index", "boolean|keepSpace", "boolean...|needSaveOperate"})
    public static MeicamAudioClip copyClip(MeicamAudioTrack audioTrack, MeicamAudioClip oldAudioClip, long inPoint, boolean... needSaveOperate) {
        MeicamAudioClip audioClip = audioTrack.copyClip(inPoint, oldAudioClip);
        if (audioClip == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(audioTrack);
            String tagAndKey = tag;
            Object[] unDoParam = new Object[]{audioClip.getIndex(), true, new boolean[]{false}};
            Object[] redoParam = new Object[]{oldAudioClip, inPoint};
            CommandUtil.saveOperate("CopyAudioClipCommand", unDoParam, redoParam, tag, tagAndKey);
        }

        return audioClip;
    }

    @Undo(className = "AudioSplitCommand", function = "mergeClip", param = {"int|index"})
    public static boolean splitClip(MeicamAudioTrack audioTrack, int index, long currentPosition, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(audioTrack);
            String tagAndKey = tag;
            Object[] unDoParam = new Object[]{index};
            Object[] redoParam = new Object[]{index, currentPosition};
            CommandUtil.saveOperate("AudioSplitCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        return audioTrack.splitClip(index, currentPosition);
    }

    static void mergeClip(MeicamAudioTrack audioTrack, int index) {
        audioTrack.mergeVideoClip(index);
    }


    private static String getTag(MeicamAudioTrack track) {
        return TAG + track.getIndex();
    }

    public static MeicamAudioTrack getItByTag(String tag) {
        tag = tag.replaceAll(TAG, "");
        int trackIndex = Integer.parseInt(tag);
        MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
        MeicamAudioTrack audioTrack = timeline.getAudioTrack(trackIndex);
        if (audioTrack == null) {
            audioTrack = timeline.appendAudioTrack();
        }
        return audioTrack;
    }
}
