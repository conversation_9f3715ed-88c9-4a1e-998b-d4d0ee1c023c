<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dp_px_660"
    android:layout_height="wrap_content"
    android:background="@drawable/draft_dialog_bg">

    <ImageView
        android:id="@+id/iv_cancel"
        android:layout_width="@dimen/dp_px_81"
        android:layout_height="@dimen/dp_px_81"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:contentDescription="@null"
        android:padding="@dimen/dp_px_18"
        android:scaleType="fitXY"
        android:src="@mipmap/ic_cancel" />

    <TextView
        android:id="@+id/tv_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dp_px_60"
        android:text="@string/draft_input_draft_name"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_42"
        tools:ignore="RelativeOverlap" />

    <EditText
        android:id="@+id/et_draft_name"
        android:layout_width="@dimen/dp_px_570"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_hint"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dp_px_60"
        android:hint="@null"
        android:importantForAutofill="no"
        android:inputType="text"
        android:textColor="@color/white_8"
        android:textCursorDrawable="@drawable/edit_text_cursor"
        android:textSize="@dimen/sp_px_36"
        android:theme="@style/DialogEditText" />

    <Button
        android:id="@+id/bt_confirm"
        android:layout_width="@dimen/dp_px_570"
        android:layout_height="wrap_content"
        android:layout_below="@+id/et_draft_name"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dp_px_60"
        android:layout_marginBottom="@dimen/dp_px_60"
        android:minHeight="@dimen/dp_px_51"
        android:text="@string/draft_confirm"
        android:textColor="@color/color_333333"
        android:textSize="@dimen/sp_px_42" />
</RelativeLayout>
