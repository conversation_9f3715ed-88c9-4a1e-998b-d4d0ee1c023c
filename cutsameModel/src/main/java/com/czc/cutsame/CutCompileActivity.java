package com.czc.cutsame;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Point;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.czc.cutsame.fragment.iview.CutCompileVpView;
import com.czc.cutsame.fragment.presenter.CutCompilePresenter;
import com.meicam.sdk.NvsRational;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseMvpActivity;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.base.view.CompileProgress;
import com.meishe.engine.EngineCallbackManager;
import com.meishe.engine.editor.EditorController;
import com.meishe.engine.observer.EngineCallbackObserver;
import com.meishe.engine.util.PathUtils;

import java.util.Hashtable;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * <AUTHOR> CaoZhiChao
 * @CreateDate : 2020/11/27 16:29
 * @Description : 通用模板的生成界面  video compile Activity
 * @Copyright : www.meishesdk.com Inc. All rights reserved.
 */
public class CutCompileActivity extends BaseMvpActivity<CutCompilePresenter> implements CutCompileVpView {
    public static final String TEMPLATE_ID = "templateId";
    public static final String COMPILE_RESOLUTION = "compileResolution";
    private ImageView mCutCompileSource;
    private CompileProgress mCutCompileProgress;
    private Button mCutCompileCancel;
    private Button mCutCompileOk;
    private TextView mCutCompileProgressText;
    private TextView mCutCompileTip;
    private String mCompilePath;
    private String mTemplateId;
    private int mCompileResolution;
    private Point mBeforeChangeVideoPoint;
    private EngineCallbackObserver mEngineObserver;

    @Override
    protected int bindLayout() {
        return R.layout.activity_cut_compile;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        Intent intent = getIntent();
        if (intent != null) {
            Bundle bundle = intent.getExtras();
            if (bundle != null) {
                mTemplateId = bundle.getString(TEMPLATE_ID);
                mCompileResolution = bundle.getInt(COMPILE_RESOLUTION, NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_720);
            }
        }

        EngineCallbackManager.get().registerCallbackObserver(mEngineObserver = new EngineCallbackObserver() {
            @Override
            public boolean isActive() {
                Activity activity = AppManager.getInstance().currentActivity();
                return !isFinishing() && CutCompileActivity.this.equals(activity);
            }

            @Override
            public void onImageGrabbedArrived(final Bitmap bitmap, long l) {
                if (mCutCompileSource != null) {
                    mCutCompileSource.setImageBitmap(bitmap);
                }
                mPresenter.updateTemplateInteraction(mTemplateId);
//                        changeVideoTo4K();
                compileTimeLine();
            }

            @Override
            public void onCompileProgress(NvsTimeline nvsTimeline, int i) {
                setCenterProgress(i);
            }

            @Override
            public void onCompileFailed(NvsTimeline nvsTimeline) {
                onCompileFiled();
            }

            @Override
            public void onCompileCompleted(NvsTimeline nvsTimeline, boolean b) {
                if (!b) {
                    PathUtils.scanInnerFile(mCompilePath, nvsTimeline.getDuration(), new PathUtils.OnScanCallBack() {
                        @Override
                        public void onSuccess(String filePath) {
                            onCompileSuccess();
                        }

                        @Override
                        public void onFail() {
                            onCompileFiled();
                        }
                    });
                    //路径置空，防止，在点击返回键等操作的时候删除文件
                    // Leave the path blank to prevent deleting files when clicking the return button or other operations.
                    mCompilePath = null;
                    EditorController.getInstance().clearCompileConfigurations();
                } else {
                    onCompileFiled();
                }
                EditorController.getInstance().changeVideoSize(mBeforeChangeVideoPoint.x, mBeforeChangeVideoPoint.y);
            }
        });

        EditorController.getInstance().grabImageFromTimelineAsync(0, new NvsRational(1, 1), 0);
    }

    private void onCompileSuccess() {
        mCutCompileProgress.setVisibility(View.GONE);
        mCutCompileProgressText.setVisibility(View.GONE);
        mCutCompileCancel.setVisibility(View.GONE);
        mCutCompileTip.setText(R.string.activity_cut_compile_tip2);
        mCutCompileOk.setVisibility(View.VISIBLE);
    }

    /**
     * 修改时间线为4K(3840 * 2160)。因为生成的时候，不会大于时间线的大小，所以要先修改画幅
     * Modify the timeline to 4K(3840 * 2160).Since it will not be larger than the timeline when it is generated, you need to modify the frame first
     */
    private void changeVideoTo4K() {
        int k4W = 3840;
        int k4H = 2160;
        mBeforeChangeVideoPoint = EditorController.getInstance().getTimelineWidthAndHeight();
        int width = mBeforeChangeVideoPoint.x;
        int height = mBeforeChangeVideoPoint.y;
        if (width > height) {
            height = k4W * height / width;
            width = k4W;
            if (height > k4H) {
                float scale = 1.0f * k4H / height;
                height = k4H;
                width = (int) (width * scale);
            }
        } else {
            width = width * k4H / height;
            height = k4H;
        }
        width = width - width % 4;
        height = height - height % 2;
        EditorController.getInstance().changeVideoSize(width, height);
    }

    private void compileTimeLine() {
        mCompilePath = PathUtils.getVideoSavePathNew_Q(PathUtils.getVideoSaveName(), EditorController.getInstance().getTimelineDuration());
        if (TextUtils.isEmpty(mCompilePath)) {
            LogUtils.e("Compile path is null!");
            return;
        }
        Hashtable<String, Object> mParamsTable = new Hashtable<>();
        //mParamsTable.put(NvsStreamingContext.COMPILE_FPS, new NvsRational(30, 1));


        //计算新高度
        //Compute new height.
        int setHeightOfCompile = 0;
        if (mCompileResolution == NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_360) {
            setHeightOfCompile = 360;
        } else if (mCompileResolution == NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_480) {
            setHeightOfCompile = 480;
        } else if (mCompileResolution == NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_720) {
            setHeightOfCompile = 720;
        } else if (mCompileResolution == NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_1080) {
            setHeightOfCompile = 1080;
        } else if (mCompileResolution == NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_CUSTOM) {
            setHeightOfCompile = 576;
        }

        setHeightOfCompile = EditorController.getInstance().getCustomHeight(setHeightOfCompile);
        LogUtils.d(" compileResolution = " + mCompileResolution + ", setHeightOfCompile = " + setHeightOfCompile);
        if (!EditorController.getInstance().compileTimeLineCustom(mCompilePath, setHeightOfCompile, mParamsTable)) {
            onCompileFiled();
        }
    }

    private void onCompileFiled() {
        mCutCompileProgress.setVisibility(View.GONE);
        mCutCompileProgressText.setVisibility(View.GONE);
        mCutCompileCancel.setVisibility(View.GONE);
        mCutCompileTip.setText(R.string.activity_cut_compile_tip3);
    }

    private void setCenterProgress(int i) {
        mCutCompileProgress.setProgress(i);
        mCutCompileProgressText.setText(i + "%");
    }

    @Override
    protected void initView() {
        ImageView cutCompileClose = findViewById(R.id.cut_compile_close);
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) cutCompileClose.getLayoutParams();
        layoutParams.topMargin = (int) (ScreenUtils.getStatusBarHeight() + getResources().getDimension(R.dimen.title_margin_top));
        cutCompileClose.setLayoutParams(layoutParams);
        cutCompileClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                cancelCompile();
                finish();
            }
        });
        mCutCompileSource = findViewById(R.id.cut_compile_source);
        mCutCompileProgress = findViewById(R.id.cut_compile_progress);
        mCutCompileCancel = findViewById(R.id.cut_compile_cancel);
        mCutCompileOk = findViewById(R.id.cut_compile_ok);
        mCutCompileCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                cancelCompile();
                finish();
            }
        });
        mCutCompileOk.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        mCutCompileProgressText = findViewById(R.id.cut_compile_progress_text);
        mCutCompileTip = findViewById(R.id.cut_compile_tip);
    }

    /**
     * 取消导出
     * Cancel compiling
     */
    private void cancelCompile() {
        EditorController.getInstance().stop();
        FileUtils.delete(mCompilePath);
    }

    @Override
    public void onBackPressed() {
        cancelCompile();
        super.onBackPressed();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EngineCallbackManager.get().unregisterCallbackObserver(mEngineObserver);
    }
}