<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp15"
        android:gravity="center"
        android:text="@string/upload_toast_upload_fail"
        android:textColor="@color/white"
        android:textSize="@dimen/sp14" />

    <TextView
        android:id="@+id/tv_toast_fail"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp15"
        android:layout_marginBottom="@dimen/dp28"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/sp12" />
</LinearLayout>