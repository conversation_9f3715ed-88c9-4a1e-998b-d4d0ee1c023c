package com.meishe.user.bean;

import java.io.Serializable;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/8/11 18:04
 * @Description :批量信息查询bean
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class ProjectBatchBean implements Serializable {
   private List<BatchBean> projectList;


   public List<BatchBean> getProjectList() {
      return projectList;
   }

   public void setProjectList(List<BatchBean> projectList) {
      this.projectList = projectList;
   }

   public static class BatchBean implements Serializable {
      private String id;
      private String modifyAt;

      public String getId() {
         return id;
      }

      public void setId(String id) {
         this.id = id;
      }

      public String getModifyAt() {
         return modifyAt;
      }

      public void setModifyAt(String modifyAt) {
         this.modifyAt = modifyAt;
      }
   }
}
