package com.meishe.myvideo.view.presenter;

import android.content.Context;
import android.text.TextUtils;

import com.meishe.engine.EditorEngine;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.observer.AssetObserver;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.engine.bean.MeicamTheme;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.event.MessageEvent;
import com.meishe.myvideo.view.MYThemeMenu;

import java.util.ArrayList;
import java.util.List;

import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_THEME_CONFIRM;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_UPDATE_SELECT_POSITION;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_WIDTH_CONFIRM_EFFECT;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/1/6 14:25
 * @Description :Presenter of theme menu view
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class ThemePresenter extends BaseConfirmPresenter<MYThemeMenu> {
    private AssetObserver mAssetObserver;

    @Override
    public void attachView(MYThemeMenu view) {
        super.attachView(view);
        AssetsManager.get().registerAssetObserver(mAssetObserver = new AssetObserver() {
            @Override
            public void onAssetAdd(IBaseInfo assetInfo) {
                if (assetInfo.getType() == AssetInfo.ASSET_THEME) {
                    List<IBaseInfo> data = getView().getAdapter().getData();
                    boolean canAdd = true;
                    for (int i = 0; i < data.size(); i++) {
                        IBaseInfo baseInfo = data.get(i);
                        if (assetInfo.getPackageId().equals(baseInfo.getPackageId())) {
                            baseInfo.update(assetInfo);
                            canAdd = false;
                            break;
                        }
                    }
                    if (canAdd) {
                        getView().onDataChanged(assetInfo);
                    }
                }
            }
        });
    }

    @Override
    public void detach() {
        super.detach();
        AssetsManager.get().unregisterAssetObserver(mAssetObserver);
    }

    @Override
    public void onItemClicked(IBaseInfo info, boolean isSelected) {
        MessageEvent.sendEvent(info, MESSAGE_TYPE_WIDTH_CONFIRM_EFFECT);
    }

    @Override
    public void onProgressChanged(float progress, String tag, boolean isFromUser) {

    }

    @Override
    public void onStopTrackingTouch() {

    }

    @Override
    public void reset(List<IBaseInfo> data) {

    }

    @Override
    public void confirm() {
        MessageEvent.sendEvent(MESSAGE_TYPE_THEME_CONFIRM);
    }

    @Override
    public void applyToAll() {

    }

    @Override
    public void getData(Context context) {
        final List<IBaseInfo> list = new ArrayList<>();
        IBaseInfo effectInfo = new AssetInfo();
        effectInfo.setName(context.getResources().getString(R.string.more));
        effectInfo.setCoverId(R.drawable.more_icon);
        effectInfo.setType(AssetInfo.ASSET_THEME);
        effectInfo.setEffectMode(BaseInfo.EFFECT_MODE_BUILTIN);
        list.add(effectInfo);
        effectInfo = new AssetInfo();
        effectInfo.setName(context.getResources().getString(R.string.no));
        effectInfo.setCoverId(R.drawable.none_icon);
        effectInfo.setType(AssetInfo.ASSET_THEME);
        effectInfo.setEffectMode(BaseInfo.EFFECT_MODE_BUILTIN);
        list.add(effectInfo);
        AssetsManager.get().getLocalAssetList(AssetInfo.ASSET_THEME, new AssetsManager.AssetCallback() {
            @Override
            public void onSuccess(List<AssetInfo> assetInfoList) {
                if (assetInfoList != null && assetInfoList.size() > 0) {
                    list.addAll(assetInfoList);
                }
                getView().onDataBack(list, 0);
                getView().updateSelectPosition(getThemeSelection(list));
            }

            @Override
            public void onFailure() {
                getView().onDataBack(list, 0);
            }
        });
    }

    @Override
    public boolean updateData(int subType, boolean needForceUpdate) {
        return true;
    }

    @Override
    public void onMessageEvent(MessageEvent event) {
        int eventType = event.getEventType();
        if (eventType == MESSAGE_TYPE_UPDATE_SELECT_POSITION) {
            int intValue = event.getIntValue();
            getView().updateSelectPosition(intValue);
        }
    }

    /**
     * 获取选择的主题的index
     * <p>
     * get selection of theme
     *
     * @param data 主题数据列表 List of menu data
     * @return 选择主题的索引 index
     */
    private int getThemeSelection(List<IBaseInfo> data) {
        int position = 1;
        MeicamTheme theme = EditorEngine.getInstance().getCurrentTimeline().getMeicamTheme();
        if (theme == null) {
            return position;
        }
        String themePackageId = theme.getThemePackageId();
        if (TextUtils.isEmpty(themePackageId)) {
            return position;
        }
        IBaseInfo baseInfo;
        for (int index = 0; index < data.size(); index++) {
            baseInfo = data.get(index);
            if (themePackageId.equals(baseInfo.getPackageId())) {
                position = index;
                break;
            }
        }
        return position;
    }
}
