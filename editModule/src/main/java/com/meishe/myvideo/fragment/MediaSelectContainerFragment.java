package com.meishe.myvideo.fragment;

import static com.meishe.logic.constant.PagerConstants.BUNDLE_DATA;
import static com.meishe.logic.constant.PagerConstants.MEDIA_DATA;
import static com.meishe.logic.constant.PagerConstants.MEDIA_MAX_NUM;
import static com.meishe.logic.constant.PagerConstants.MEDIA_REQUEST_CODE_PREVIEW;
import static com.meishe.logic.constant.PagerConstants.MEDIA_TAG;
import static com.meishe.logic.constant.PagerConstants.SELECTED_TYPE;
import static com.meishe.logic.constant.PagerConstants.TYPE_DEFAULT;
import static com.meishe.logic.constant.PagerConstants.TYPE_ONE_FINISH;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.meishe.base.adapter.CommonFragmentAdapter;
import com.meishe.base.bean.MediaData;
import com.meishe.base.bean.MediaTag;
import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseFragment;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.Utils;
import com.meishe.myvideo.R;
import com.meishe.myvideo.activity.MaterialPreviewActivity;
import com.meishe.third.tablayout.SlidingTabLayout;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 素材选择容器Fragment，可嵌入其他页面
 * Material selection container Fragment that can be embedded in other pages
 */
public class MediaSelectContainerFragment extends BaseFragment {
    private static final String TAG = "MediaSelectContainerFragment";
    
    private final List<Fragment> mFragmentList = new ArrayList<>(4);
    private List<String> mTabTitleList;
    private ArrayList<MediaData> mSelectedMaterialList = new ArrayList<>();
    private int mSelectedType = TYPE_DEFAULT;
    private OnMediaSelectedListener mOnMediaSelectedListener;

    /**
     * 创建MediaSelectContainerFragment实例
     */
    public static MediaSelectContainerFragment newInstance(int selectedType) {
        MediaSelectContainerFragment fragment = new MediaSelectContainerFragment();
        Bundle args = new Bundle();
        args.putInt(SELECTED_TYPE, selectedType);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_media_container;
    }

    @Override
    protected void initData() {
        if (getArguments() != null) {
            mSelectedType = getArguments().getInt(SELECTED_TYPE, TYPE_DEFAULT);
        }
        
        mTabTitleList = Arrays.asList(getResources().getStringArray(R.array.select_media));
        mFragmentList.clear();
        mFragmentList.add(MaterialSelectFragment.create(MediaData.TYPE_ALL, mSelectedType, true, mMediaListener));
        mFragmentList.add(MaterialSelectFragment.create(MediaData.TYPE_VIDEO, mSelectedType, true, mMediaListener));
        mFragmentList.add(MaterialSelectFragment.create(MediaData.TYPE_PHOTO, mSelectedType, true, mMediaListener));
        mFragmentList.add(MaterialSelectFragment.createWithExternalData(MediaData.TYPE_BUSINESS, mSelectedType, true, mMediaListener));
    }

    @Override
    protected void initView(View rootView) {
        SlidingTabLayout tabLayout = rootView.findViewById(R.id.tl_select_media);
        ViewPager viewPager = rootView.findViewById(R.id.vp_select_media);

        viewPager.setOffscreenPageLimit(3);
        viewPager.setAdapter(new CommonFragmentAdapter(getChildFragmentManager(), mFragmentList));
        tabLayout.setViewPager(viewPager, mTabTitleList);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        for (Fragment fragment : mFragmentList) {
            fragment.onActivityResult(requestCode, resultCode, data);
        }
    }

    /**
     * 获取当前选中的素材列表
     */
    public ArrayList<MediaData> getSelectedMaterialList() {
        return mSelectedMaterialList;
    }

    /**
     * 设置素材选择监听器
     */
    public void setOnMediaSelectedListener(OnMediaSelectedListener listener) {
        this.mOnMediaSelectedListener = listener;
    }

    /**
     * 处理素材选中
     * deal material selected
     *
     * @param mediaData The MediaData
     */
    private void dealMaterialSelected(MediaData mediaData) {
        MediaData selectedMedia;
        if (mSelectedType == TYPE_ONE_FINISH || mSelectedMaterialList.size() == 0) {
            mSelectedMaterialList.clear();
            mSelectedMaterialList.add(selectedMedia = getMaterialWidthTag(mediaData));
            updateSelectedNumber(0, selectedMedia, false);
        } else {
            int currentType = ((MediaTag) mediaData.getTag()).getType();
            for (int i = 0; i < mSelectedMaterialList.size(); i++) {
                selectedMedia = mSelectedMaterialList.get(i);
                if (selectedMedia.getId() == mediaData.getId()) {
                    selectedMedia.setState(mediaData.isState());
                    if (!selectedMedia.isState()) {
                        MediaTag[] mediaTags = (MediaTag[]) selectedMedia.getTag();
                        MediaTag tag;
                        for (int j = 0; j < mFragmentList.size(); j++) {
                            tag = mediaTags[j];
                            if (tag.getType() == currentType) {
                                /*
                                 * 当前页面已经取消过了
                                 * The current page has been cancelled
                                 * */
                                continue;
                            }
                            /*
                             * 通知其他子页面，取消选中
                             * Notify other subpages to deselect them
                             * */
                            ((MaterialSelectFragment) mFragmentList.get(j)).dealSelectedState(tag.getIndex(), false);
                        }
                        /*
                         * 未选中，则在选中集合中删除
                         * Unchecked, the selected collection is deleted
                         * */
                        mSelectedMaterialList.remove(i);
                        updateSelectedNumber(i, selectedMedia, true);
                        return;
                    }
                }
            }
            /*
             * 没有选中，则添加选中集合。
             * If not selected, the selected collection is added.
             * */
            mSelectedMaterialList.add(selectedMedia = getMaterialWidthTag(mediaData));
            updateSelectedNumber(mSelectedMaterialList.size() - 1, selectedMedia, false);
        }
        
        // 通知选择变化
        if (mOnMediaSelectedListener != null) {
            mOnMediaSelectedListener.onMediaSelectionChanged(mSelectedMaterialList);
        }
    }

    /**
     * 更新选中的数字
     * Update the selected number
     */
    private void updateSelectedNumber(int startIndex, MediaData data, boolean useSelectedListTag) {
        MediaTag[] mediaTags = (MediaTag[]) data.getTag();
        MediaTag tag;
        for (int i = startIndex; i < mSelectedMaterialList.size(); i++) {
            if (useSelectedListTag) {
                mediaTags = (MediaTag[]) mSelectedMaterialList.get(i).getTag();
            }
            for (int j = 0; j < mFragmentList.size(); j++) {
                tag = mediaTags[j];
                /*
                 * 更新选中索引
                 * Update selected index
                 * */
                if (tag != null) {
                    ((MaterialSelectFragment) mFragmentList.get(j)).updateSelectedNumber(tag.getIndex(), i + 1);
                }
            }
        }
    }

    /**
     * 获取带有其他子页面Tag的素材
     * Gets the material with the other sub-page tags
     */
    private MediaData getMaterialWidthTag(MediaData mediaData) {
        MediaTag[] mediaTags = new MediaTag[mFragmentList.size()];
        MediaTag tag = (MediaTag) mediaData.getTag();
        
        if (tag.getType() == MediaData.TYPE_ALL) {
            mediaTags[0] = tag;
            mediaTags[1] = ((MaterialSelectFragment) mFragmentList.get(1)).dealSelected(mediaData.getThumbPath());
            mediaTags[2] = ((MaterialSelectFragment) mFragmentList.get(2)).dealSelected(mediaData.getThumbPath());
        } else if (tag.getType() == MediaData.TYPE_VIDEO) {
            mediaTags[1] = tag;
            mediaTags[0] = ((MaterialSelectFragment) mFragmentList.get(0)).dealSelected(mediaData.getThumbPath());
            mediaTags[2] = ((MaterialSelectFragment) mFragmentList.get(2)).dealSelected(mediaData.getThumbPath());
        } else if (tag.getType() == MediaData.TYPE_PHOTO) {
            mediaTags[2] = tag;
            mediaTags[0] = ((MaterialSelectFragment) mFragmentList.get(0)).dealSelected(mediaData.getThumbPath());
            mediaTags[1] = ((MaterialSelectFragment) mFragmentList.get(1)).dealSelected(mediaData.getThumbPath());
        } else {
            mediaTags[3] = tag;
            ((MaterialSelectFragment) mFragmentList.get(3)).dealSelected(mediaData.getThumbPath());
        }
        MediaData newData = mediaData.copy();
        newData.setTag(mediaTags);
        return newData;
    }

    private final MaterialSelectFragment.MediaChangeListener mMediaListener = new MaterialSelectFragment.MediaChangeListener() {
        @Override
        public void onMediaChange(MediaData mediaData) {
            dealMaterialSelected(mediaData);
        }

        @Override
        public void onMediaPreView(MediaData mediaData) {
            if (Utils.isFastClick()) {
                return;
            }
            if (mediaData == null) {
                LogUtils.e("mediaData is null !");
                return;
            }
            Bundle bundle = new Bundle();
            if (mSelectedType == TYPE_ONE_FINISH) {
                mediaData.setPosition(1);
                if (mediaData.isState()) {
                    bundle.putInt(MEDIA_MAX_NUM, 1);
                } else {
                    bundle.putInt(MEDIA_MAX_NUM, 0);
                }
            } else {
                bundle.putInt(MEDIA_MAX_NUM, mSelectedMaterialList == null ? 0 : mSelectedMaterialList.size());
            }
            bundle.putParcelable(MEDIA_DATA, mediaData);
            bundle.putParcelable(MEDIA_TAG, ((MediaTag) mediaData.getTag()));

            // 跳转预览页面
            // Jump to preview page
            AppManager.getInstance().jumpActivityForResult(getActivity(), MaterialPreviewActivity.class, bundle, MEDIA_REQUEST_CODE_PREVIEW);
        }
    };

    /**
     * 素材选择监听接口
     */
    public interface OnMediaSelectedListener {
        /**
         * 当素材选择变化时回调
         * @param selectedList 当前选中的素材列表
         */
        void onMediaSelectionChanged(ArrayList<MediaData> selectedList);
    }
}