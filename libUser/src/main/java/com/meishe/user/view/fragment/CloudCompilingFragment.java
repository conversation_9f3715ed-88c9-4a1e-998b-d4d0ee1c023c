package com.meishe.user.view.fragment;

import android.view.Gravity;

import com.meishe.base.utils.ToastUtils;
import com.meishe.base.view.PullToRefreshAndPushToLoadView;
import com.meishe.user.R;
import com.meishe.user.bean.VideoCompileBean;
import com.meishe.user.manager.CloudCompileManager;
import com.meishe.user.view.iview.CloudDraftView;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/9 13:28
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CloudCompilingFragment extends BaseCloudCompileFragment implements CloudDraftView {
    private int mCurrentClickedPosition;
    private CloudCompileManager.CompileObserver mCompileObserver;
    private CloudCompileManager.CompileUpDownloadDataObserver mCompileUploadDataObserver;

    public static CloudCompilingFragment create() {
        return new CloudCompilingFragment();
    }

    Set<String> uploadProjectSet = new HashSet<>();

    @Override
    public void initListener() {
        CloudCompileManager.getInstance().registerUpDownloadDataObserver(mCompileUploadDataObserver = new CloudCompileManager.CompileUpDownloadDataObserver(){
            @Override
            public void onUploadDataChanged(List<VideoCompileBean> data) {
                onNewDataBack(data);
            }
        });
        CloudCompileManager.getInstance().registerCompileObserver(mCompileObserver = new CloudCompileManager.CompileObserver() {
            @Override
            public void onProgress(String projectId, float progress) {
                List<VideoCompileBean> videoCompileBeanList = mAdapter.getData();
                int selectIndex = -1;
                VideoCompileBean selectBean = null;
                for (int index = 0; index < videoCompileBeanList.size(); index++) {
                    VideoCompileBean bean = videoCompileBeanList.get(index);
                    if (projectId.equals(bean.getProjectId())) {
                        selectIndex = index;
                        selectBean = bean;
                        break;
                    }
                }
                if (selectBean != null) {
                    int realProgress = (int) (progress * 0.5F);
                    uploadProjectSet.add(projectId);
                    selectBean.setCompileProgress(realProgress);
                    mAdapter.notifyItemChanged(selectIndex, 1);
                }
            }

            @Override
            public void onSuccess(String projectId, String jobId) {
                List<VideoCompileBean> videoCompileBeanList = mAdapter.getData();
                for (int index = 0; index < videoCompileBeanList.size(); index++) {
                    VideoCompileBean bean = videoCompileBeanList.get(index);
                    if (projectId.equals(bean.getProjectId())) {
                        bean.setJobId(jobId);
                        break;
                    }
                }
                List<Integer> data = new ArrayList<>();
                data.add(Integer.valueOf(jobId));
                CloudCompileManager.getInstance().getJobInfo(data);
            }

            @Override
            public void onError(String projectId) {
                ToastUtils.make().setGravity(Gravity.CENTER, 0, 0).show(R.string.cloud_compile_error);
            }
        });

        mRefreshLayout.setOnRefreshAndLoadMoreListener(new PullToRefreshAndPushToLoadView.PullToRefreshAndPushToLoadMoreListener() {
            @Override
            public void onRefresh() {
                finishRefresh();
            }

            @Override
            public void onLoadMore() {
                finishLoading();
            }
        });
        CloudCompileManager.getInstance().setJobStateChangedListener(new CloudCompileManager.JobStateChangedListener() {
            @Override
            public void onChanged(CloudCompileManager.JobItem jobItem) {
                List<VideoCompileBean> videoCompileBeanList = mAdapter.getData();
                if (jobItem.progress == 100) {
                    if (jobItem.state == 2 || jobItem.state == 3) {
                        ToastUtils.make().setGravity(Gravity.CENTER, 0, 0).show(R.string.cloud_compile_error);
                    }
                    for (int index = 0; index < videoCompileBeanList.size(); index++) {
                        VideoCompileBean bean = videoCompileBeanList.get(index);
                        if (jobItem.jobId.equals(bean.getJobId())) {
                            mAdapter.remove(index);
                        }
                    }
                } else {
                    for (int index = 0; index < videoCompileBeanList.size(); index++) {
                        VideoCompileBean bean = videoCompileBeanList.get(index);
                        if (jobItem.jobId.equals(bean.getJobId())) {
                            int realProgress = jobItem.progress;
                            if (uploadProjectSet.contains(bean.getProjectId())) {
                                realProgress = (int) (jobItem.progress * 0.5F) + 50;
                            }
                            bean.setCompileProgress(realProgress);
                            mAdapter.notifyItemChanged(index, 1);
                            break;
                        }
                    }
                }
            }

            @Override
            public void onFailed() {

            }
        });
        mAdapter.setOnItemChildClickListener((adapter, view, position) -> {
            if (view.getId() == R.id.iv_delete) {
                mCurrentClickedPosition = position;
                VideoCompileBean item = mAdapter.getItem(position);
                mPresenter.deleteCompilingJob(item.getJobId(), item.getProjectId());
                onDelete(null);
            }
        });

    }

    @Override
    public void onResume() {
        super.onResume();
        mPresenter.getCompilingData();
    }

    @Override
    protected void onLazyLoad() {
        super.onLazyLoad();
    }

    @Override
    public void onDelete(String tag) {
        mAdapter.remove(mCurrentClickedPosition);
    }

    @Override
    public void onDownload() {

    }


    @Override
    public void refreshData(boolean isByUser) {
        if (isByUser) {
            mRefreshLayout.autoRefresh();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        CloudCompileManager.getInstance().unregisterCompileObserver(mCompileObserver);
        CloudCompileManager.getInstance().unregisterUpDownloadDataObserver(mCompileUploadDataObserver);
    }

    @Override
    protected void initData() {
    }
}
