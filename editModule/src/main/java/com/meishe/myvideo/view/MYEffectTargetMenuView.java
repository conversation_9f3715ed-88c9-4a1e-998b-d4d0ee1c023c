package com.meishe.myvideo.view;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.ImageLoader;
import com.meishe.base.utils.SizeUtils;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.myvideo.R;
import com.meishe.myvideo.adapter.BaseSelectAdapter;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/12/29 10:50
 * @Description :色度抠图菜单 The color pick menu view
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MYEffectTargetMenuView extends LinearLayout implements View.OnClickListener {
    private RecyclerView mRecyclerView;
    private OnEventChangedListener mOnEventChangedListener;
    private EffectTargetAdapter mAdapter;
    private List<TargetInfo> mMenuData;
    private int mSelection;
    private boolean mNeedSelect;
    private View mMaskView;

    public void setOnEventChangedListener(OnEventChangedListener listener) {
        this.mOnEventChangedListener = listener;
    }

    public MYEffectTargetMenuView(Context context, List<TargetInfo> menuData, int selection, boolean needSelect) {
        super(context);
        mMenuData = menuData;
        mSelection = selection;
        mNeedSelect = needSelect;
        initView();
    }

    private void initView() {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.view_menu_effect_target, this);
        mRecyclerView = view.findViewById(R.id.rv_list);
        mMaskView = view.findViewById(R.id.mask);
        if (mNeedSelect) {
            mMaskView.setVisibility(GONE);
        }
        view.findViewById(R.id.iv_confirm).setOnClickListener(this);
        view.findViewById(R.id.mask).setOnClickListener(this);
        initRecyclerView();
        initListener();
    }

    private void initListener() {
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                mAdapter.setSelectPosition(position);
                if (mOnEventChangedListener != null) {
                    mOnEventChangedListener.onMenuClicked(mAdapter.getItem(position));
                }
            }
        });
    }

    private void initRecyclerView() {
        LinearLayoutManager layoutManager = new LinearLayoutManagerWrapper(getContext(), LinearLayoutManager.HORIZONTAL, false);
        mRecyclerView.setLayoutManager(layoutManager);
        mAdapter = new EffectTargetAdapter();
        mRecyclerView.setAdapter(mAdapter);
        mAdapter.setNewData(mMenuData);
        mAdapter.setSelectPosition(mSelection);
        int decoration = SizeUtils.dp2px(5);
        mRecyclerView.addItemDecoration(new ItemDecoration(decoration, decoration));
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.iv_confirm) {
            if (mOnEventChangedListener != null) {
                mOnEventChangedListener.onConfirm();
            }
        }
    }

    public void setSelection(int selection){
        if (mAdapter != null){
            mAdapter.setSelectPosition(selection);
        }
    }

    public static class EffectTargetAdapter extends BaseSelectAdapter<TargetInfo> {
        private int mImagePadding;
        public EffectTargetAdapter() {
            super(R.layout.view_effect_target_item);
            mImagePadding = SizeUtils.dp2px(13.5F);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, TargetInfo item) {
            TextView name = helper.getView(R.id.tv_name);
            ImageView coverView = helper.getView(R.id.iv_cover);
            if (!TextUtils.isEmpty(item.coverPath)) {
                ImageLoader.loadUrl(mContext, item.coverPath, coverView);
                coverView.setPadding(0, 0, 0, 0);
            } else {
                coverView.setImageResource(item.coverResId);
                coverView.setPadding(mImagePadding, mImagePadding, mImagePadding, mImagePadding);
            }
            name.setText(item.name);
            View coverRound = helper.getView(R.id.iv_cover_round);
            if (helper.getAdapterPosition() == getSelectPosition()) {
                coverRound.setBackground(CommonUtils.getRadiusDrawable(3, mContext.getResources().getColor(R.color.color_fffc2b55), 6, -1));
            } else {
                coverRound.setBackgroundResource(0);
            }
        }
    }

    /**
     * The effect target info.
     * 特效作用对象信息
     */
    public static class TargetInfo {
        public int trackIndex;
        public int clipIndex;
        public String coverPath;
        public int coverResId;
        public String name;
        public String tag;

        public TargetInfo(String coverPath, String name, int trackIndex, int clipIndex) {
            this.trackIndex = trackIndex;
            this.clipIndex = clipIndex;
            this.coverPath = coverPath;
            this.name = name;
        }

        @Override
        public String toString() {
            return "TargetInfo{" +
                    "trackIndex=" + trackIndex +
                    ", clipIndex=" + clipIndex +
                    ", coverPath='" + coverPath + '\'' +
                    ", coverResId=" + coverResId +
                    ", name='" + name + '\'' +
                    ", tag='" + tag + '\'' +
                    '}';
        }
    }

    public interface OnEventChangedListener {
        /**
         * On menu clicked.
         *
         * @param targetInfo the target information
         */
        void onMenuClicked(TargetInfo targetInfo);

        /**
         * On confirm.
         */
        void onConfirm();
    }
}
