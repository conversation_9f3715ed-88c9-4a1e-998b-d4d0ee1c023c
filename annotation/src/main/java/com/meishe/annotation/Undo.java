package com.meishe.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/8/22 14:04
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
@Retention(RetentionPolicy.CLASS)
@Target(ElementType.METHOD)
public @interface Undo {
    String className() default "";
    String[] param() default "";
    String function();
    int id() default 0;
}
