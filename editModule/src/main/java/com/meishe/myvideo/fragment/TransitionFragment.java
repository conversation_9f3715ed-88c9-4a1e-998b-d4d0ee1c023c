package com.meishe.myvideo.fragment;

import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.constants.Constants;
import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.model.BaseMvpFragment;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FormatUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.NetUtils;
import com.meishe.base.utils.SizeUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;
import com.meishe.base.view.SeekBarTextView;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.business.assets.fragment.adapter.CommonAdapter;
import com.meishe.business.assets.view.AssetsTypeTabView;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.bean.AssetsConstants;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.MeicamTransition;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.logic.constant.PagerConstants;
import com.meishe.myvideo.R;
import com.meishe.myvideo.edit.manager.CommandOperateManager;
import com.meishe.myvideo.fragment.iview.TransitionView;
import com.meishe.myvideo.fragment.presenter.TransitionPresenter;
import com.meishe.third.adpater.BaseQuickAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * The type Transition fragment.
 * 转场
 *
 * <AUTHOR>
 * @Description 转场
 * @CreateDate 2020 /7/23 16:10
 */
public class TransitionFragment extends BaseMvpFragment<TransitionPresenter> implements TransitionView {
    public static final int TRANS_TYPE_GENERAL = 0;
    public static final int TRANS_TYPE_3D = 1;
    public static final int TRANS_TYPE_EFFECT = 2;

    private static final float MIN_DURATION = 0.1f;

    private int mTargetTransitionIndex;
    private MeicamTransition mMeicamTransition;
    private int mType;
    private int mSubType;
    /**
     * The M transition refresh listener.
     * 转场监听
     */
    private TransitionEventListener mTransitionRefreshListener;

    protected CommonAdapter mAdapter;
    private View mRlTopView;
    private RecyclerView mRecyclerView;
    private SeekBarTextView mSeekBar;
    private AssetsTypeTabView mTabTypeView;
    private String currAssetUuid;
    private TextView mHintText;
    private TextView mTvMaxDuration;
    private float maxTransitionDuration = 0;
    private String mDownloadTag;

    public TransitionFragment() {
    }

    /**
     * 实例化一个新的转场3d
     * <p>
     * Instantiates a Transition fragment.
     *
     * @param targetTransitionIndex     the target transition index
     * @param type                      the transition type
     *                                  TRANS_TYPE_GENERAL = 1;
     *                                  TRANS_TYPE_3D = 2;
     *                                  TRANS_TYPE_EFFECT = 4;
     * @param transitionRefreshListener the transition refresh listener
     */
    public static TransitionFragment getInstance(int targetTransitionIndex, int type, TransitionEventListener transitionRefreshListener) {
        TransitionFragment fragment = new TransitionFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PagerConstants.BUNDLE_KEY_TRANSITION_TARGET_INDEX, targetTransitionIndex);
        bundle.putInt(PagerConstants.BUNDLE_KEY_TRANSITION_TYPE, type);
        fragment.setArguments(bundle);
        fragment.mTransitionRefreshListener = transitionRefreshListener;
        return fragment;
    }

    @Override
    protected void initData() {
        Bundle bundle = getArguments();
        if (bundle != null) {
            mTargetTransitionIndex = bundle.getInt(PagerConstants.BUNDLE_KEY_TRANSITION_TARGET_INDEX, 0);
            mType = bundle.getInt(PagerConstants.BUNDLE_KEY_TRANSITION_TYPE);
            mMeicamTransition = mPresenter.getTransition(mTargetTransitionIndex);
            long maxDuration = EditorEngine.getInstance().getMaxTransitionDurationByVideoClip(mTargetTransitionIndex);
            maxTransitionDuration = microsecond2Second(maxDuration);
        }
        if (String.valueOf(MIN_DURATION).equals(String.valueOf(maxTransitionDuration))) {
            mRlTopView.setVisibility(View.INVISIBLE);
        } else {
            String text = maxTransitionDuration + "s";
            mTvMaxDuration.setText(text);
        }
        mSeekBar.setSuffix("s");
        mSeekBar.setMinProgress((int) (MIN_DURATION * 10));
        mSeekBar.setMaxProgress((int) (maxTransitionDuration * 10));
        float currTransitionDuration;
        if (mMeicamTransition != null) {
            currAssetUuid = mMeicamTransition.getDesc();
            float rate = microsecond2Second(mMeicamTransition.getDuration()) / maxTransitionDuration;
            currTransitionDuration = rate * mSeekBar.getMaxProgress();
        } else {
            currTransitionDuration = mSeekBar.getMaxProgress();
        }
        mSeekBar.setProgress((int) currTransitionDuration);
        mSeekBar.setProgressTextVisible((int) currTransitionDuration);
        mPresenter.loadData(AssetsConstants.AssetsTypeData.TRANSITION.type, mSubType, mType, -1, true);
    }


    @Override
    protected int bindLayout() {
        return R.layout.fragment_transition;
    }

    @Override
    protected void onLazyLoad() {
    }

    @Override
    protected void initView(View rootView) {
        mRecyclerView = rootView.findViewById(R.id.recyclerView);
        mRlTopView = rootView.findViewById(R.id.rl_top_view);
        mTabTypeView = rootView.findViewById(R.id.ttv_tab_type);
        mSeekBar = rootView.findViewById(R.id.seek_bar_progress);
        mHintText = rootView.findViewById(R.id.tv_hint);
        mTvMaxDuration = rootView.findViewById(R.id.end_text);

        initRecyclerView();
        initListener();
    }

    private void initRecyclerView() {
        LinearLayoutManager layoutManager = new LinearLayoutManagerWrapper(getContext(), LinearLayoutManager.HORIZONTAL, false);
        mRecyclerView.setLayoutManager(layoutManager);
        mAdapter = new CommonAdapter(R.layout.view_menu_transition_item, true, true);
        mRecyclerView.setAdapter(mAdapter);
        mRecyclerView.addItemDecoration(new ItemDecoration(SizeUtils.dp2px(5), SizeUtils.dp2px(5)));
    }

    private void initListener() {
        mAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                if (!mPresenter.loadMoreData(AssetsConstants.AssetsTypeData.TRANSITION.type, mSubType, mType, -1, false)) {
                    mAdapter.loadMoreEnd(true);
                }
            }
        }, mRecyclerView);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                AssetInfo item = mAdapter.getItem(position);
                if (item != null) {
                    if (getResources().getString(R.string.top_menu_no).equals(item.getName())) {
                        mRlTopView.setVisibility(View.INVISIBLE);
                    } else {
                        mRlTopView.setVisibility(View.VISIBLE);
                    }
                    if ((!item.isHadDownloaded() || item.needUpdate())) {
                        downloadAssets(position, item);
                    } else {
                        mAdapter.selected(position);
                        currAssetUuid = item.getEffectMode() == BaseInfo.EFFECT_MODE_PACKAGE ? item.getPackageId() : item.getEffectId();
                        if (mTransitionRefreshListener != null) {
                            mTransitionRefreshListener.onTransitionClick(item, mTargetTransitionIndex, false);
                        }
                        mPresenter.applyTransition(item, mTargetTransitionIndex, (long) (mSeekBar.getProgress() / mSeekBar.getMaxProgress() * maxTransitionDuration * Constants.NS_TIME_BASE));
                        mPresenter.clickAssetItem(item);
                    }
                }
            }
        });
        mSeekBar.setListener(new SeekBarTextView.OnSeekBarListener() {
            @Override
            public void onStopTrackingTouch(int progress) {
                if (mMeicamTransition != null) {
                    mPresenter.updateTransitionDuration(mMeicamTransition,
                            (long) (progress * 1.0f / mSeekBar.getMaxProgress() * maxTransitionDuration * Constants.NS_TIME_BASE));
                    CommandOperateManager.get().addOperate();
                }
                mMeicamTransition = null;
            }

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                mMeicamTransition = mPresenter.getTransition(mTargetTransitionIndex);
            }
        });

        mTabTypeView.setItemClickedListener(new AssetsTypeTabView.ItemClickedListener() {
            @Override
            public void onItemClicked(int position) {
                mSubType = position;
                mAdapter.setAssetSubType(position);
                mPresenter.loadData(AssetsConstants.AssetsTypeData.TRANSITION.type, mSubType, mType, -1, false);
            }
        });
    }

    private void downloadAssets(int position, AssetInfo item) {
        item.setDownloadProgress(0);
        mAdapter.notifyItemChanged(position);
        mDownloadTag = item.getPackageId();
        mPresenter.downloadAsset(item, position);
    }


    /**
     * 选中目标转场
     * Selected the transition
     *
     * @param uuid the uuid
     */
    public void selected(String uuid) {
        selected(uuid, true);
    }


    /**
     * 选中目标转场
     * Selected the transition
     *
     * @param uuid       the uuid
     * @param autoScroll Whether need scroll to position.true:yes;false:no
     */
    public void selected(String uuid, boolean autoScroll) {
        currAssetUuid = uuid;
        if (mAdapter != null) {
            mAdapter.selected(uuid);
            int selectPosition = mAdapter.getSelectedPosition();
            IBaseInfo item = mAdapter.getItem(selectPosition);
            if (item != null) {
                if (getResources().getString(R.string.top_menu_no).equals(item.getName())) {
                    mRlTopView.setVisibility(View.INVISIBLE);
                } else {
                    mRlTopView.setVisibility(View.VISIBLE);
                }
            }
            if (selectPosition > 1 && autoScroll) {
                mRecyclerView.scrollToPosition(selectPosition);
            }
        }
    }

    public int getTransitionIndex() {
        return mTargetTransitionIndex;
    }


    /**
     * 应用所有转场
     * Apply transition to all.
     */
    public boolean applyTransitionToAll() {
        if (mPresenter == null) {
            LogUtils.e("applyTransitionToAll  mPresenter==null");
            return false;
        }
        boolean result = mPresenter.applyTransitionToAll(mTargetTransitionIndex);
        if (mTransitionRefreshListener != null && mAdapter != null && mAdapter.getSelectedPosition() > 0) {
            mTransitionRefreshListener.onTransitionClick(mAdapter.getItem(mAdapter.getSelectedPosition()), mTargetTransitionIndex, true);
        }
        return result;
    }

    @Override
    public boolean isActive() {
        return isAdded();
    }

    @Override
    public void onNewDataBack(List<AssetInfo> list, int subType, boolean needUpdate) {
        if (!CommonUtils.isEmpty(list)) {
            mAdapter.setNewData(list);
        }
        updateViewState(subType, needUpdate);
        selected(currAssetUuid);
    }

    /**
     * Update the view state
     * 更新view状态
     *
     * @param subType    子类型 the sub type
     * @param needUpdate 是否在刷新数据 Whether is updating data.
     */
    private void updateViewState(int subType, boolean needUpdate) {
        if (mAdapter.getItemCount() > 2) {
            mRlTopView.setVisibility(View.VISIBLE);
            mRecyclerView.setVisibility(View.VISIBLE);
            mHintText.setVisibility(View.GONE);
            if (!NetUtils.isNetworkAvailable(getContext()) && needUpdate) {
                /*
                 * 防止移动到最后面后，自动加载更多，造成加载重复数据
                 * Prevent automatic loading of more data after moving the rear end, resulting in loading duplicate data
                 */
                mAdapter.setEnableLoadMore(false);
                ToastUtils.showShort(Utils.getApp().getResources().getString(R.string.user_hint_assets_net_error));
            } else {
                /*
                 * 网络正常后，开启自动加载
                 * After the network is normal, turn on automatic loading
                 */
                mAdapter.setEnableLoadMore(true);
            }
        } else {
            mRlTopView.setVisibility(View.GONE);
            mRecyclerView.setVisibility(View.GONE);
            mHintText.setVisibility(View.VISIBLE);
            if (!NetUtils.isNetworkAvailable(getContext())) {
                mHintText.setText(R.string.user_hint_assets_net_error_refresh);
                Drawable drawable = getResources().getDrawable(R.mipmap.ic_assets_data_update);
                drawable.setBounds(new Rect(0, 4, drawable.getIntrinsicHeight(), drawable.getIntrinsicHeight() + 4));
                mHintText.setCompoundDrawables(null, null, drawable, null);
                mHintText.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        mHintText.setCompoundDrawables(null, null, null, null);
                        initData();
                    }
                });
            } else {
                mHintText.setText(AssetsManager.get().getErrorMsg(getContext(), subType));
            }
        }
    }

    @Override
    public void onMoreDataBack(List<AssetInfo> list, int subType, boolean needUpdate) {
        if (!CommonUtils.isEmpty(list)) {
            mAdapter.addData(list);
        }
        mAdapter.loadMoreComplete();
        updateViewState(subType, needUpdate);
        selected(currAssetUuid, false);
    }

    @Override
    public void onDataError(int subType, boolean needUpdate) {
        mAdapter.setNewData(new ArrayList<>());
        mAdapter.loadMoreComplete();
        updateViewState(subType, needUpdate);
    }

    @Override
    public int getItemCount() {
        return mAdapter == null ? 0 : mAdapter.getItemCount() - 2;
    }

    @Override
    public void onDownloadFinish(int position, AssetInfo assetInfo) {
        if (TextUtils.equals(mDownloadTag, assetInfo.getPackageId())) {
            mAdapter.selected(position);
            if (mTransitionRefreshListener != null) {
                mTransitionRefreshListener.onTransitionClick(assetInfo, mTargetTransitionIndex, false);
            }
            mPresenter.applyTransition(assetInfo, mTargetTransitionIndex, (long) (mSeekBar.getProgress() / mSeekBar.getMaxProgress() * maxTransitionDuration * Constants.NS_TIME_BASE));
        } else {
            mAdapter.notifyItemChanged(position);
        }
    }

    @Override
    public void onDownloadError(int position) {
        if (mAdapter != null) {
            mAdapter.notifyItemChanged(position);
        }
    }

    @Override
    public void onDownloadProgress(int position) {
        if (mAdapter != null) {
            mAdapter.notifyItemChanged(position);
        }
    }


    /**
     * The interface Transition refresh listener.
     * 接口转场刷新监听器
     */
    public interface TransitionEventListener {
        void onTransitionClick(IBaseInfo baseInfo, int transitionIndex, boolean applyAll);
    }


    /**
     * 微秒转化为秒
     * Microsecond 2 second float.
     *
     * @param duration the duration
     * @return the float
     */
    public static float microsecond2Second(long duration) {
        return FormatUtils.floatFormat(duration * 1.0f / CommonData.TIMEBASE);
    }
}
