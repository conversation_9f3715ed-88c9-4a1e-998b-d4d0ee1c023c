package com.meishe.engine.asset.bean;

import android.text.TextUtils;

import java.util.Objects;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/6/23 14:01
 * @Description :请求参数 Request parameter
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class RequestParam {
    public int type;
    public int subType;
    public int categoryId;
    public int kind;
    public String keyword;

    public RequestParam(int type, int subType, int categoryId, int kind) {
        this.type = type;
        this.subType = subType;
        this.categoryId = categoryId;
        this.kind = kind;
    }

    public RequestParam(int type, int subType, int categoryId, int kind, String keyword) {
        this(type, subType, categoryId, kind);
        this.keyword = keyword;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RequestParam param = (RequestParam) o;
        return type == param.type &&
                subType == param.subType &&
                categoryId == param.categoryId &&
                kind == param.kind && TextUtils.equals(keyword, param.keyword);
    }

    @Override
    public int hashCode() {
        return Objects.hash(type, subType, categoryId, kind, keyword);
    }
}