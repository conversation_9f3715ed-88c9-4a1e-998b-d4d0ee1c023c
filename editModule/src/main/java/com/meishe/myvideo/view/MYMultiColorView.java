package com.meishe.myvideo.view;

import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.reflect.TypeToken;
import com.meicam.sdk.NvsColor;
import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.utils.GsonUtils;
import com.meishe.base.utils.ResourceUtils;
import com.meishe.engine.util.ColorUtil;
import com.meishe.myvideo.R;
import com.meishe.myvideo.bean.ColorInfo;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;

import java.util.List;

/**
 * 横向颜色控件
 * Horizontal color control
 */
public class MYMultiColorView extends FrameLayout {

    private static final String COLOR_ASSETS_PATH = "background/color/colorAxis.json";
    private ColorViewAdapter mColorAdapter;
    private OnColorClickListener mListener;

    public MYMultiColorView(Context context) {
        this(context, null);
    }

    public MYMultiColorView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MYMultiColorView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
        initData();
    }

    private void initView() {
        RecyclerView rvColorList = new RecyclerView(getContext());
        addView(rvColorList, LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT);
        LinearLayoutManager layoutManager = new LinearLayoutManagerWrapper(getContext(), LinearLayoutManager.HORIZONTAL, false);
        rvColorList.setLayoutManager(layoutManager);
        mColorAdapter = new ColorViewAdapter();
        rvColorList.setAdapter(mColorAdapter);
        mColorAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (mListener != null) {
                    mColorAdapter.selected(position);
                    mListener.onClick(mColorAdapter.getItem(position));
                }
            }
        });
    }

    private void initData() {
        //这个是写死的公用控件，其他的view不建议拥有默认数据源 This is a written public control. Other Views are not recommended to have a default data source
        String colorJson = ResourceUtils.readAssets2String(COLOR_ASSETS_PATH, "UTF-8");
        if (!TextUtils.isEmpty(colorJson)) {
            List<ColorInfo> colorList = GsonUtils.fromJson(colorJson, new TypeToken<List<ColorInfo>>() {
            }.getType());
            for (ColorInfo colorInfo : colorList) {
                colorInfo.setCommonInfo(ColorUtil.nvsColorToHexString(new NvsColor(colorInfo.r, colorInfo.g, colorInfo.b, 1.0F)));
            }
            mColorAdapter.setNewData(colorList);
        }

    }

    /**
     * 设置颜色选中监听
     * Set the color click listener
     *
     * @param listener the listener
     */
    public void setColorClickListener(OnColorClickListener listener) {
        mListener = listener;
    }

    /**
     * 选中目标颜色值
     * Selected the item
     *
     * @param colorValue the color value
     */
    public void selected(String colorValue) {
        mColorAdapter.selected(colorValue);
    }

    /**
     * 选中目标
     * Selected the item
     *
     * @param position the position
     */
    public void selected(int position) {
        mColorAdapter.selected(position);
    }

    /**
     * Gets selected color.
     * 获得选中颜色
     * @return the selected color
     */
    public ColorInfo getSelectedColor() {
        return mColorAdapter.getSelected();
    }

    /**
     * The interface On color click listener.
     * 点击颜色监听的接口
     */
    public interface OnColorClickListener {
        /**
         * On click.
         * 点击
         * @param colorInfo the color info 颜色信息
         */
        void onClick(ColorInfo colorInfo);
    }

    private static class ColorViewAdapter extends BaseQuickAdapter<ColorInfo, BaseViewHolder> {
        private int mSelectedPosition = -1;

        private ColorViewAdapter() {
            super(R.layout.item_canvas_color);
        }

        /**
         * 选中某一项
         * Selected item .
         *
         * @param colorValue The item color value
         */
        public void selected(String colorValue) {
            if (!TextUtils.isEmpty(colorValue)) {
                int size = getData().size();
                ColorInfo colorInfo;
                for (int i = 0; i < size; i++) {
                    colorInfo = getData().get(i);
                    if (colorValue.equals(colorInfo.getCommonInfo())) {
                        selected(i);
                        return;
                    }
                }
            }
            selected(-1);
        }

        /**
         * 选中某一项
         * Selected item .
         *
         * @param position The index of list
         */
        public void selected(int position) {
            if (mSelectedPosition >= 0) {
                notifyItemChanged(mSelectedPosition);
            }
            mSelectedPosition = position;
            if (position >= 0 && position < getData().size()) {
                notifyItemChanged(position);
            }
        }

        /**
         * Gets selected.
         * 获得选择
         * @return the selected
         */
        public ColorInfo getSelected() {
            return getItem(mSelectedPosition);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, ColorInfo item) {
            helper.setBackgroundColor(R.id.v_color, Color.parseColor(item.getCommonInfo()));
            helper.setVisible(R.id.v_selected_bg, helper.getAdapterPosition() == mSelectedPosition);
        }
    }
}
