package com.czc.cutsame.bean;

import static com.meicam.sdk.NvsAssetPackageManager.ASSET_PACKAGE_ASPECT_RATIO_16v9;
import static com.meicam.sdk.NvsAssetPackageManager.ASSET_PACKAGE_ASPECT_RATIO_18v9;
import static com.meicam.sdk.NvsAssetPackageManager.ASSET_PACKAGE_ASPECT_RATIO_1v1;
import static com.meicam.sdk.NvsAssetPackageManager.ASSET_PACKAGE_ASPECT_RATIO_3v4;
import static com.meicam.sdk.NvsAssetPackageManager.ASSET_PACKAGE_ASPECT_RATIO_4v3;
import static com.meicam.sdk.NvsAssetPackageManager.ASSET_PACKAGE_ASPECT_RATIO_9v16;
import static com.meicam.sdk.NvsAssetPackageManager.ASSET_PACKAGE_ASPECT_RATIO_9v18;

import android.os.Parcel;
import android.os.Parcelable;

import com.meishe.engine.asset.bean.AssetInfo;

/**
 * author : lhz
 * date   : 2020/11/4
 * desc   :模板实体类
 * Template entity class
 * <p>
 */
public class Template implements Parcelable, Comparable<Template> {
    /**
     * 比例大小 Size proportion
     */
    private float ratio;
    /**
     * 通用模板模板ID Cut the same template ID
     */
    private String id;
    /**
     * 模板名称 template name
     */
    private String displayName;
    /**
     * 模板描述 Descript
     */
    private String description;

    /**
     * 中文模板描述 Descript for ZH
     */
    private String descriptionZhCn;
    /**
     * 模板封面URL Template cover URL
     */
    private String coverUrl;
    /**
     * 模板预览URL Template cover URL
     */
    private String previewVideoUrl;
    /**
     * 模板info.json URL Template info. Json URL
     */
    private String infoUrl;
    /**
     * 模板描述 Descript
     */
    private String packageInfo;
    /**
     * 模板包URL The template package URL
     */
    private String packageUrl;

    /**
     * 时长 duration
     */
    private long duration;
    /**
     * 片段数量 The number of pieces
     */
    private int shotsNumber;
    /**
     * 使用次数 number of use
     */
    private int useNum;
    /**
     * 点赞次数 Thumb up number
     */
    private int likeNum;
    /**
     * 支持的画幅比例 Supported picture scale
     */
    private int supportedAspectRatio;
    /**
     * 默然画幅比例 the default aspect ratio
     */
    private int defaultAspectRatio;
    /**
     * 创建者信息 Creator information
     */
    private Producer producer;
    /**
     * 版本 versions
     */
    private int version;
    /**
     * 创建时间 the createTime
     */
    private long createTime;
    /**
     * 是否已购 Is authorized or not.
     */
    private int isAuthorized;

    /**
     * 是否是本地模板 Is from local or not.
     */
    private String isFromLocal = String.valueOf(Boolean.FALSE);

    /**
     * 授权路径 lic path
     */
    private String licPath;

    /**
     * 判断是否是自制，已购
     * Determine if it is self-made and purchased.
     */

    private int subType;

    private int category;

    public Template() {
    }

    protected Template(Parcel in) {
        ratio = in.readFloat();
        id = in.readString();
        displayName = in.readString();
        description = in.readString();
        descriptionZhCn = in.readString();
        coverUrl = in.readString();
        previewVideoUrl = in.readString();
        infoUrl = in.readString();
        packageInfo = in.readString();
        packageUrl = in.readString();
        duration = in.readLong();
        shotsNumber = in.readInt();
        useNum = in.readInt();
        likeNum = in.readInt();
        supportedAspectRatio = in.readInt();
        defaultAspectRatio = in.readInt();
        producer = in.readParcelable(Producer.class.getClassLoader());
        version = in.readInt();
        createTime = in.readLong();
        isAuthorized = in.readInt();
        isFromLocal = in.readString();
        licPath = in.readString();
        subType = in.readInt();
        category = in.readInt();
    }

    public static final Creator<Template> CREATOR = new Creator<Template>() {
        @Override
        public Template createFromParcel(Parcel in) {
            return new Template(in);
        }

        @Override
        public Template[] newArray(int size) {
            return new Template[size];
        }
    };

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    /**
     * Sets ratio.
     * 设置比例
     *
     * @param ratio the ratio 设置比例
     */
    public void setRatio(float ratio) {
        this.ratio = ratio;
    }

    public String getLicPath() {
        return licPath;
    }

    public void setLicPath(String licPath) {
        this.licPath = licPath;
    }

    /**
     * Gets id.
     * 获取id
     *
     * @return the id id
     */
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescriptionZhCn() {
        return descriptionZhCn;
    }

    public void setDescriptionZhCn(String descriptionZhCn) {
        this.descriptionZhCn = descriptionZhCn;
    }

    public String getCoverUrl() {
        return coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    public String getPreviewVideoUrl() {
        return previewVideoUrl;
    }

    public void setPreviewVideoUrl(String previewVideoUrl) {
        this.previewVideoUrl = previewVideoUrl;
    }

    public String getInfoUrl() {
        return infoUrl;
    }

    public void setInfoUrl(String infoUrl) {
        this.infoUrl = infoUrl;
    }

    public String getPackageInfo() {
        return packageInfo;
    }

    public void setPackageInfo(String packageInfo) {
        this.packageInfo = packageInfo;
    }

    public int getUseNum() {
        return useNum;
    }

    public void setUseNum(int useNum) {
        this.useNum = useNum;
    }

    public int getLikeNum() {
        return likeNum;
    }

    public void setLikeNum(int likeNum) {
        this.likeNum = likeNum;
    }

    public Producer getProducer() {
        return producer;
    }

    public void setProducer(Producer producer) {
        this.producer = producer;
    }

    public int getSupportedAspectRatio() {
        return supportedAspectRatio;
    }

    public String getPackageUrl() {
        return packageUrl;
    }

    public void setPackageUrl(String packageUrl) {
        this.packageUrl = packageUrl;
    }

    public long getDuration() {
        return duration;
    }

    public void setDuration(long duration) {
        this.duration = duration;
    }

    public int getShotsNumber() {
        return shotsNumber;
    }

    public void setShotsNumber(int shotsNumber) {
        this.shotsNumber = shotsNumber;
    }

    public void setSupportedAspectRatio(int supportedAspectRatio) {
        this.supportedAspectRatio = supportedAspectRatio;
    }

    public int getDefaultAspectRatio() {
        return defaultAspectRatio;
    }

    public boolean isAuthorized() {
        return isAuthorized == 1;
    }

    public void setAuthorized(boolean authorized) {
        isAuthorized = authorized ? 1 : 0;
    }

    public String getIsFromLocal() {
        return isFromLocal;
    }

    public void setIsFromLocal(String isFromLocal) {
        this.isFromLocal = isFromLocal;
    }

    public boolean isFromLocal() {
        return "true".equals(isFromLocal);
    }

    public void setFromLocal(boolean fromLocal) {
        isFromLocal = String.valueOf(fromLocal);
    }

    public int getSubType() {
        return subType;
    }

    public void setSubType(int subType) {
        this.subType = subType;
    }

    public void setCategory(int category) {
        this.category = category;
    }

    public int getCategory() {
        return category;
    }

    /**
     * Sets default aspect ratio.
     * 设置默认长宽比
     *
     * @param defaultAspectRatio the default aspect ratio 默认长宽比
     */
    public void setDefaultAspectRatio(int defaultAspectRatio) {
        this.defaultAspectRatio = defaultAspectRatio;
        if (defaultAspectRatio == ASSET_PACKAGE_ASPECT_RATIO_16v9) {
            ratio = 16 / 9f;
        } else if (defaultAspectRatio == ASSET_PACKAGE_ASPECT_RATIO_1v1) {
            ratio = 1;
        } else if (defaultAspectRatio == ASSET_PACKAGE_ASPECT_RATIO_9v16) {
            ratio = 9 / 16f;
        } else if (defaultAspectRatio == ASSET_PACKAGE_ASPECT_RATIO_4v3) {
            ratio = 4 / 3f;
        } else if (defaultAspectRatio == ASSET_PACKAGE_ASPECT_RATIO_3v4) {
            ratio = 3 / 4f;
        } else if (defaultAspectRatio == ASSET_PACKAGE_ASPECT_RATIO_18v9) {
            ratio = 18 / 9f;
        } else if (defaultAspectRatio == ASSET_PACKAGE_ASPECT_RATIO_9v18) {
            ratio = 9 / 18f;
        } else if (defaultAspectRatio == 128) {
            ratio = 2.39f;
        } else if (defaultAspectRatio == 256) {
            ratio = 2.55f;
        } else if (defaultAspectRatio == 512) {
            ratio = 21 / 9f;
        } else if (defaultAspectRatio == 1024) {
            ratio = 9 / 21f;
        } else {
            ratio = 1f;
        }
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public float getRatio() {
        if (ratio == 0) {
            setDefaultAspectRatio(getDefaultAspectRatio());
        }
        return ratio;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeFloat(ratio);
        dest.writeString(id);
        dest.writeString(displayName);
        dest.writeString(description);
        dest.writeString(descriptionZhCn);
        dest.writeString(coverUrl);
        dest.writeString(previewVideoUrl);
        dest.writeString(infoUrl);
        dest.writeString(packageInfo);
        dest.writeString(packageUrl);
        dest.writeLong(duration);
        dest.writeInt(shotsNumber);
        dest.writeInt(useNum);
        dest.writeInt(likeNum);
        dest.writeInt(supportedAspectRatio);
        dest.writeInt(defaultAspectRatio);
        dest.writeParcelable(producer, flags);
        dest.writeInt(version);
        dest.writeLong(createTime);
        dest.writeInt(isAuthorized);
        dest.writeString(isFromLocal);
        dest.writeString(licPath);
        dest.writeInt(subType);
        dest.writeInt(category);
    }


    /**
     * The type Producer.
     * 制作类型
     */
    public static class Producer implements Parcelable {
        private String nickname;// 昵称 nickname
        private String iconUrl;// 头像 head portrait

        public Producer() {
        }

        public String getNickName() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public String getIconUrl() {
            return iconUrl;
        }

        public void setIconUrl(String iconUrl) {
            this.iconUrl = iconUrl;
        }

        protected Producer(Parcel in) {
            nickname = in.readString();
            iconUrl = in.readString();
        }

        public static final Creator<Producer> CREATOR = new Creator<Producer>() {
            @Override
            public Producer createFromParcel(Parcel in) {
                return new Producer(in);
            }

            @Override
            public Producer[] newArray(int size) {
                return new Producer[size];
            }
        };

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(nickname);
            dest.writeString(iconUrl);
        }
    }

    @Override
    public int compareTo(Template o) {
        /*
         * 先按照创建时间排序 time sort first
         */
        if (this == o) {
            return 0;
        }
        if (o == null) {
            return -1;
        }
        if (o.getCreateTime() == this.getCreateTime()) {
            return 0;
        }
        if (o.getCreateTime() - this.getCreateTime() > 0) {
            return 1;
        }
        return -1;
    }


    public static Template create(AssetInfo assetInfo) {
        Template template = new Template();
        template.setId(assetInfo.getId());
        int aspectRatio = assetInfo.getSupportedAspectRatio();
        int defaultAspectRatio = assetInfo.getDefaultAspectRatio();
        template.setSupportedAspectRatio(aspectRatio);
        template.setDefaultAspectRatio(defaultAspectRatio <= 0 ? aspectRatio : defaultAspectRatio);
        template.setDisplayName(assetInfo.getName());
        template.setCoverUrl(assetInfo.getCoverPath());
        template.setPackageUrl(assetInfo.getDownloadUrl());
        template.setPreviewVideoUrl(assetInfo.getPreviewSampleUrl());
        template.setInfoUrl(assetInfo.getInfoUrl());
        template.setVersion(assetInfo.getVersion());
        template.setDuration(assetInfo.getDuration());
        template.setDescription(assetInfo.getDescription());
        template.setDescriptionZhCn(assetInfo.getDescriptionZhCn());
        template.setAuthorized(assetInfo.isAuthorized());
        template.setLicPath(assetInfo.getLicPath());
        template.setSubType(assetInfo.getSubType());
        template.setCategory(assetInfo.getCategory());
        AssetInfo.ExtendedInfo extendedInfo = assetInfo.getExtendedInfo();
        if (extendedInfo != null) {
            AssetInfo.UserInfo userInfo = extendedInfo.userInfo;
            if (userInfo != null) {
                Producer producer = new Producer();
                producer.setNickname(userInfo.nickname);
                producer.setIconUrl(userInfo.iconUrl);
                template.setProducer(producer);
            }
            AssetInfo.InteractiveResultDto interactiveResultDto = extendedInfo.interactiveResultDto;
            if (interactiveResultDto != null) {
                template.setUseNum(interactiveResultDto.useNum);
                template.setLikeNum(interactiveResultDto.likeNum);
            }
        }
        return template;
    }
}
