package com.meishe.engine.local;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 版权所有:www.meishesdk.com
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>Z<PERSON>
 * @CreateDate :2021/05/20 10:07
 * @Description : 音频特效草稿数据 The draft data of audio fx
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class LMeicamAudioFx extends LNvsObject implements Cloneable{
    int index;
    // builtin package
    String type;
    String desc;
    @SerializedName("fxParams")
    List<LMeicamFxParam> mMeicamFxParam = new ArrayList<>();

    public LMeicamAudioFx() {
    }

    public LMeicamAudioFx(int index, String type, String desc) {
        this.index = index;
        this.type = type;
        this.desc = desc;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public List<LMeicamFxParam> getMeicamFxParam() {
        return mMeicamFxParam;
    }

    public void setMeicamFxParam(List<LMeicamFxParam> meicamFxParam) {
        mMeicamFxParam = meicamFxParam;
    }
}
