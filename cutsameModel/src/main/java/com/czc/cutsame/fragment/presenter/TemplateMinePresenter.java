package com.czc.cutsame.fragment.presenter;

import android.content.Context;
import android.text.TextUtils;

import com.czc.cutsame.R;
import com.czc.cutsame.bean.Template;
import com.czc.cutsame.fragment.iview.TemplateView;
import com.czc.cutsame.util.ConfigUtil;
import com.meishe.base.model.Presenter;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FileIOUtils;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.GsonUtils;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.asset.IAssetsManager;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.bean.AssetList;
import com.meishe.engine.asset.bean.AssetsConstants;
import com.meishe.engine.asset.bean.RequestParam;
import com.meishe.engine.bean.template.ExportTemplateDescInfo;
import com.meishe.engine.util.PathUtils;
import com.meishe.engine.util.RatioUtil;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.net.custom.BaseResponse;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * author : lhz
 * date   : 2020/11/3
 * desc   :模板逻辑处理类
 * Template logic handles classes
 */
public class TemplateMinePresenter extends Presenter<TemplateView> {
    private boolean hasNext;
    private int currentTotal;
    private int mPage;
    private String mKeyword;

    @Override
    public void attachView(TemplateView templateView) {
        super.attachView(templateView);
    }


    /**
     * 获取模板对应分类的列表
     * <p></p>
     * Gets the list of categories corresponding to the template
     *
     * @param page int 请求页数
     */
    public void getTemplateList(Context context, final int page, boolean isRefresh) {
        mPage = page;
        if (ConfigUtil.isToC()) {
            getView().onTemplateListBack(getDataFromLocal(context));
        } else {
            IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
            if (userPlugin != null && userPlugin.isLogin()) {
                getDataFromNet(context, page, isRefresh);
            } else {
                getView().onTemplateListBack(null);
            }
        }
    }

    public boolean isFromNet() {
        if (ConfigUtil.isToC()) {
            return false;
        } else {
            IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
            return (userPlugin != null && userPlugin.isLogin());
        }
    }

    /**
     * 加载更多模板
     * <p></p>
     * Loading more templates
     *
     * @return the more template 更多的模板
     */
    public boolean getMoreTemplate(Context context) {
        if (hasNext) {
            getTemplateList(context, (mPage + 1), true);
        }
        return hasNext;
    }


    private List<Template> getDataFromLocal(Context context) {
        String templateFileFolder = PathUtils.getGenerateTemplateFileFolder();
        List<File> files = FileUtils.listFilesInDir(templateFileFolder);
        if (files.isEmpty()) {
            return null;
        }
        boolean needFilter = !TextUtils.isEmpty(mKeyword);
        final List<Template> list = new ArrayList<>();
        for (int i = files.size() - 1; i >= 0; i--) {
            File file = files.get(i);
            String templateDescPath = "";
            String templatePath = "";
            String licPath = "";
            String outPath = file.getAbsolutePath();
            if (outPath.endsWith(".template")) {
                templatePath = outPath;
            } else {
                List<File> templateFiles = FileUtils.listFilesInDir(file);
                if (templateFiles.size() > 0) {
                    for (int j = 0; j < templateFiles.size(); j++) {
                        File templateFile = templateFiles.get(j);
                        if (templateFile == null) {
                            continue;
                        }
                        String fileAbsolutePath = templateFile.getAbsolutePath();
                        if (TextUtils.isEmpty(fileAbsolutePath)) {
                            continue;
                        }
                        if (fileAbsolutePath.endsWith(".json")) {
                            templateDescPath = fileAbsolutePath;
                        }
                        if (fileAbsolutePath.endsWith(".template")) {
                            templatePath = fileAbsolutePath;
                        }
                        if (fileAbsolutePath.equals(".lic")) {
                            licPath = fileAbsolutePath;
                        }
                    }
                }
            }
            String jsonStr = FileIOUtils.readFile2String(templateDescPath);
            ExportTemplateDescInfo exportTemplateDescInfo = null;
            try {
                exportTemplateDescInfo = GsonUtils.fromJson(jsonStr, ExportTemplateDescInfo.class);
            } catch (Exception e) {
            }
            Template template = null;
            if (exportTemplateDescInfo != null) {
                String name = exportTemplateDescInfo.getName();
                if (!needFilter || name.contains(mKeyword)) {
                    template = new Template();
                    template.setId(exportTemplateDescInfo.getUuid());
                    int ratio = RatioUtil.getAspectRatio(exportTemplateDescInfo.getSupportedAspectRatio());
                    template.setSupportedAspectRatio(ratio);
                    template.setDefaultAspectRatio(ratio);
                    template.setDisplayName(name);
                    template.setDescription(exportTemplateDescInfo.getDescription());
                    template.setCoverUrl(exportTemplateDescInfo.getCover());
                    template.setPreviewVideoUrl(exportTemplateDescInfo.getTemplateVideoPath());
                    template.setDuration(exportTemplateDescInfo.getDuration());
                    template.setCreateTime(exportTemplateDescInfo.getCreateTime());
                }
            } else {
                template = new Template();;
            }
            if (template != null) {
                template.setUseNum(-1);
                template.setFromLocal(true);
                template.setPackageUrl(templatePath);
                template.setLicPath(licPath);
                Template.Producer producer = new Template.Producer();
                producer.setNickname(context.getString(R.string.template_default_creator));
                producer.setIconUrl("https://qasset.meishesdk.com/my/default_icon.png");
                template.setProducer(producer);
                list.add(template);
            }
        }
        Collections.sort(list);
        return list;
    }

    private void getDataFromNet(final Context context, final int page, final boolean isRefresh) {
        final List<Template> list = new ArrayList<>();
        RequestParam param = new RequestParam(AssetsConstants.AssetsTypeData.TEMPLATE.type,
                2, AssetsConstants.AssetsTypeData.TEMPLATE.category, AssetsConstants.AssetsTypeData.TEMPLATE.kind, mKeyword);
        AssetsManager.get().getAssetsList(param, -1, -1, page, 25, true, new IAssetsManager.AssetsRequestCallback<AssetList>() {

            @Override
            public void onSuccess(BaseResponse<AssetList> response) {
                if (response == null || response.getData() == null) {
                    if (page == 0) {
                        getView().onTemplateListBack(null);
                    } else {
                        getView().onMoreTemplateBack(null);
                    }
                    return;
                }
                List<AssetInfo> realAssetList = response.getData().realAssetList;
                if (CommonUtils.isEmpty(realAssetList)) {
                    if (page == 0) {
                        getView().onTemplateListBack(null);
                    } else {
                        getView().onMoreTemplateBack(null);
                    }
                    return;
                }
                for (AssetInfo assetInfo : realAssetList) {
                    if (assetInfo.getCategory() == 2) {
                        continue;
                    }
                    Template template = Template.create(assetInfo);
                    template.setUseNum(-1);
                    if (template.getProducer() == null) {
                        Template.Producer producer = new Template.Producer();
                        producer.setNickname(context.getResources().getString(R.string.template_default_creator));
                        producer.setIconUrl("https://qasset.meishesdk.com/my/default_icon.png");
                        template.setProducer(producer);
                        template.setFromLocal(false);
                    }
                    list.add(template);
                }

                if (page == 0) {
                    getView().onTemplateListBack(list);
                    currentTotal = list.size();
                } else {
                    currentTotal += list.size();
                    getView().onMoreTemplateBack(list);
                }
                hasNext = response.getData().total > currentTotal;
            }

            @Override
            public void onError(BaseResponse<AssetList> response) {
                getView().onTemplateListBack(null);

            }
        });
    }

    public void setKeyWord(String keyword) {
        mKeyword = keyword;
    }

    public String getKeyword() {
        return mKeyword;
    }
}
