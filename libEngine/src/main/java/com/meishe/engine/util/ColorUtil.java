package com.meishe.engine.util;

import android.graphics.Color;

import com.meicam.sdk.NvsColor;

/**
 * author：yangtailin on 2020/6/19 11:48
 * 颜色工具类。主要用于SDK中的NvsColor与UI中的颜色值的变换
 * Color tool class. It is mainly used to transform the NvsColor in the SDK and the color value in the UI
 */
public class ColorUtil {
    public static String[] code = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f"};

    /**
     * #FFFFFFFF变换为NvsColor
     * Color to nvs color
     *
     * @param colorString the color string
     * @return the nvs color
     */
    public static NvsColor colorToNvsColor(String colorString) {
        if (colorString == null || colorString.isEmpty())
            return null;
        NvsColor color = new NvsColor(1, 1, 1, 1);
        int parseColor = Color.parseColor(colorString);
        color.a = (float) ((parseColor & 0xff000000) >>> 24) / 0xFF;
        color.r = (float) ((parseColor & 0x00ff0000) >> 16) / 0xFF;
        color.g = (float) ((parseColor & 0x0000ff00) >> 8) / 0xFF;
        color.b = (float) ((parseColor) & 0x000000ff) / 0xFF;
        return color;
    }

    /**
     * #FFFFFFFF变换为记录rgba的数组
     * String color to color float [ ].
     *
     * @param colorString the color string
     * @return the float [ ]
     */
    public static float[] stringColorToColor(String colorString) {
        if (colorString == null || colorString.isEmpty())
            return null;
        int parseColor = Color.parseColor(colorString);
        float a = (float) ((parseColor & 0xff000000) >>> 24) / 0xFF;
        float r = (float) ((parseColor & 0x00ff0000) >> 16) / 0xFF;
        float g = (float) ((parseColor & 0x0000ff00) >> 8) / 0xFF;
        float b = (float) ((parseColor) & 0x000000ff) / 0xFF;
        return new float[]{r, g, b, a};
    }

    /**
     * rgba的数组变换为#FFFFFFFF格式
     * Color float to nvs color nvs color.
     *
     * @param colorString the color string
     * @return the nvs color
     */
    public static NvsColor colorFloatToNvsColor(float[] colorString) {
        if (colorString == null || colorString.length != 4) {
            return null;
        }
        return new NvsColor(colorString[0], colorString[1], colorString[2], colorString[3]);
    }

    /**
     * NvsColor变换为记录rgba的数组
     * Nvs color to rgba int [ ].
     *
     * @param color the color
     * @return the int [ ]。数组值范围[0, 255]
     */
    public static int[] nvsColorToRgba(NvsColor color) {
        int[] rgba = {255, 255, 255, 255};
        if (color == null)
            return rgba;
        int red = (int) Math.floor(color.r * 255 + 0.5D);
        int green = (int) Math.floor(color.g * 255 + 0.5D);
        int blue = (int) Math.floor(color.b * 255 + 0.5D);
        int alpha = (int) Math.floor(color.a * 255 + 0.5D);
        rgba[0] = alpha;
        rgba[1] = red;
        rgba[2] = green;
        rgba[3] = blue;
        for (int i = 0; i < rgba.length; i++) {
            if (rgba[i] < 0) {
                rgba[i] = 0;
            } else if (rgba[i] > 255) {
                rgba[i] = 255;
            }
        }
        return rgba;
    }

    /**
     * NvsColor变换为#FFFFFFFF格式
     * Nvs color to hex string string.
     *
     * @param color the color
     * @return the string
     */
    public static String nvsColorToHexString(NvsColor color) {
        int[] rgba = nvsColorToRgba(color);
        StringBuilder sb = new StringBuilder();
        sb.append("#");
        for (int rgbItem : rgba) {
            int lCode = rgbItem / 16;
            int rCode = rgbItem % 16;
            sb.append(code[lCode]);
            sb.append(code[rCode]);
        }
        return sb.toString();
    }


    /**
     * rgba数组转换为#FFFFFF格式
     * Int color to hex string string.
     *
     * @param color the color
     * @return the string
     */
    public static String intColorToHexString(int color) {
        return "#" + toHexString(Color.alpha(color)) +
                toHexString(Color.red(color)) +
                toHexString(Color.green(color)) +
                toHexString(Color.blue(color));
    }

    private static String toHexString(int num) {
        int lCode = num / 16;
        int rCode = num % 16;
        return code[lCode] + code[rCode] + "";
    }

    /**
     * NvsColor变换为记录rgba的数组
     * Get color array float [ ].
     *
     * @param nvsColor the nvs color
     * @return the float [ ].数组中为[0, 1)的颜色值
     */
    public static float[] getColorArray(NvsColor nvsColor) {
        if (nvsColor == null) {
            return new float[]{1, 1, 1, 1};
        }
        return new float[]{nvsColor.r, nvsColor.g, nvsColor.b, nvsColor.a};
    }
}
