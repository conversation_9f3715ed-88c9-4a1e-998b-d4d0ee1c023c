package com.meishe.engine.command;

import static com.meishe.engine.constant.NvsConstants.TYPE_BUILD_IN;

import android.text.TextUtils;

import com.meishe.annotation.Undo;
import com.meishe.base.utils.CommonUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamTrackVideoFx;
import com.meishe.engine.bean.MeicamTransition;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoTrack;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/8/12 15:45
 * @Description :videoTrack命令 The command of the videoTrack
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class VideoTrackCommand {
   private static final String TAG = "videoTrack";

    /**
     * Split video clip.
     * 分割 video clip
     *
     * @param videoTrack   the video track 视频轨
     * @param index        the index 音频clip的index
     * @param timeStamp    the time stamp 分割时间点，是timeline上的时间点
     * @param needSaveData the need save data 是否需要保存数据
     * @return the meicam video clip
     */
    @Undo(className = "SplitCommand", function = "merge", param = {"int|index"})
    public static MeicamVideoClip split(MeicamVideoTrack videoTrack, int index, long timeStamp, boolean... needSaveData) {
        if (CommandUtil.needSaveOperate(needSaveData)) {
            String tag = getTag(videoTrack);
            Object[] unDoParam = new Object[]{index};
            Object[] redoParam = new Object[]{index, timeStamp};
            CommandUtil.saveOperate("SplitCommand", unDoParam, redoParam, tag, tag);
        }
        return videoTrack.splitVideoClip(index, timeStamp);
    }

    static void merge(MeicamVideoTrack videoTrack, int index) {
        videoTrack.mergeVideoClip(index);
    }


    @Undo(className = "AddClipCommand", function = "removeVideoClip",
            param = {"int|index", "boolean|keepSpace", "boolean...|needSaveData"})
    public static MeicamVideoClip addVideoClip(MeicamVideoTrack videoTrack, String videoPath, int index, long trimIn, long trimOut, boolean... needSaveData) {
        if (CommandUtil.needSaveOperate(needSaveData)) {
            String tag = getTag(videoTrack);
            String keyAndTag = tag + index;
            Object[] unDoParam = new Object[]{index, false, new boolean[]{false}};
            Object[] redoParam = new Object[]{videoPath, index, trimIn, trimOut};
            CommandUtil.saveOperate("AddClipCommand", unDoParam, redoParam, tag, keyAndTag);
        }
        return videoTrack.addVideoClip(videoPath, index, trimIn, trimOut);
    }

    @Undo(className = "RemoveClipCommand", function = "insertVideoClip",
            param = {"MeicamVideoClip|videoClip", "int|index"})
    public static MeicamVideoClip removeVideoClip(MeicamVideoTrack videoTrack, int index, boolean keepSpace, boolean... needSaveData) {
        MeicamVideoClip videoClip = videoTrack.getVideoClip(index);
        if (videoClip == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSaveData)) {
            String tag = getTag(videoTrack);
            String keyAndTag = tag + index;
            Object[] unDoParam = new Object[]{videoClip.clone(), videoClip.getIndex()};
            Object[] redoParam = new Object[]{index, keepSpace};
            CommandUtil.saveOperate("RemoveClipCommand", unDoParam, redoParam, tag, keyAndTag);
        }
        return videoTrack.removeVideoClip(index, keepSpace);
    }

    @Undo(className = "RemoveClipWidthSpaceCommand", function = "addVideoClip",
            param = {"MeicamVideoClip|videoClip", "long|inPoint", "long|trimIn", "long|trimOut", "boolean...|needSaveData"})
    public static MeicamVideoClip removeVideoClipWidthSpace(MeicamVideoTrack videoTrack, int index, boolean... needSaveData) {
        MeicamVideoClip videoClip = videoTrack.getVideoClip(index);
        if (videoClip == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSaveData)) {
            String tag = getTag(videoTrack);
            Object[] unDoParam = new Object[]{videoClip.clone(), videoClip.getInPoint(), videoClip.getTrimIn(),
                    videoClip.getTrimOut(), new boolean[]{false}};
            Object[] redoParam = new Object[]{index};
            CommandUtil.saveOperate("RemoveClipWidthSpaceCommand", unDoParam, redoParam, tag, tag);
        }
        return videoTrack.removeVideoClip(index, true);
    }

    static MeicamVideoClip insertVideoClip(MeicamVideoTrack videoTrack, MeicamVideoClip videoClip, int index) {
        return videoTrack.insertVideoClip(videoClip, index);
    }

    @Undo(className = "AddClipCommand1", function = "removeVideoClip",
            param = {"int|index", "boolean|keepSpace", "boolean...|needSaveData"})
    public static MeicamVideoClip addVideoClip(MeicamVideoTrack videoTrack, MeicamVideoClip videoClip, long inPoint, long trimIn, long trimOut, boolean... needSaveData) {
        MeicamVideoClip newVideoClip = videoTrack.addVideoClip(videoClip, inPoint, trimIn, trimOut);
        if (newVideoClip == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSaveData)) {
            String tag = getTag(videoTrack);
            Object[] unDoParam = new Object[]{newVideoClip.getIndex(), videoTrack.getIndex() > 0, new boolean[]{false}};
            Object[] redoParam = new Object[]{videoClip, inPoint, trimIn, trimOut};
            CommandUtil.saveOperate("AddClipCommand1", unDoParam, redoParam, tag, tag);
        }
        return newVideoClip;
    }

    @Undo(className = "AddClipCommand2", function = "removeVideoClip",
            param = {"int|index", "boolean|keepSpace", "boolean...|needSaveData"})
    public static MeicamVideoClip addVideoClip(MeicamVideoTrack videoTrack, String path, long inPoint, long trimIn, long trimOut, boolean... needSaveData) {
        MeicamVideoClip newVideoClip = videoTrack.addVideoClip(path, inPoint, trimIn, trimOut);
        if (newVideoClip == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSaveData)) {
            String tag = getTag(videoTrack);
            Object[] unDoParam = new Object[]{newVideoClip.getIndex(), true, new boolean[]{false}};
            Object[] redoParam = new Object[]{path, inPoint, trimIn, trimOut};
            CommandUtil.saveOperate("AddClipCommand2", unDoParam, redoParam, tag, tag);
        }
        return newVideoClip;
    }

    /**
     * 移动片段
     * Move clip from index to index
     *
     * @param from index 开始的位置
     * @param to   index 最后的位置
     * @return true success 移动成功，false failed 移动失败
     */
    @Undo(className = "MoveVideoClip", function = "moveClip",
            param = {"int|from", "int|to", "boolean...|needSaveData"})
    public static boolean moveClip(MeicamVideoTrack videoTrack, int from, int to, boolean... needSaveData) {
        boolean success = false;
        if (videoTrack != null) {
            success = videoTrack.moveClip(from, to);
            if (CommandUtil.needSaveOperate(needSaveData)) {
                String tag = getTag(videoTrack);
                Object[] unDoParam = new Object[]{to, from, new boolean[]{false}};
                Object[] redoParam = new Object[]{from, to};
                CommandUtil.saveOperate("MoveVideoClip", unDoParam, redoParam, tag, tag);
            }
        }
        return success;
    }

    @Undo(className = "BuildTransitionCommand", function = "checkBuildTransition",
            param = {"int|index", "String|type", "String|transitionId", "boolean...|needSaveData"})
    public static MeicamTransition buildTransition(MeicamVideoTrack videoTrack, int index, String type, String transitionId, boolean... needSaveData) {
        MeicamTransition transition = videoTrack.getTransition(index);
        String oldType = TYPE_BUILD_IN;
        String oldTransitionId = "";
        if (transition != null) {
            oldType = transition.getType();
            oldTransitionId = transition.getDesc();
        }
        if (CommandUtil.needSaveOperate(needSaveData)) {
            String tag = getTag(videoTrack);
            Object[] unDoParam = new Object[]{index, oldType, oldTransitionId, new boolean[]{false}};
            Object[] redoParam = new Object[]{index, type, transitionId};
            CommandUtil.saveOperate("BuildTransitionCommand", unDoParam, redoParam, tag, tag);
        }
        return videoTrack.buildTransition(index, type, transitionId);
    }


    @Undo(className = "AppendTrackVideoFxCommand", function = "removeTrackVideoFx",
            param = {"long|inPoint", "boolean...|needSaveData"})
    public static MeicamTrackVideoFx appendTrackVideoFx(MeicamVideoTrack videoTrack, String fxType, String desc, long inPoint, long duration, int flag, String fxTag, boolean... needSaveOperate){
        MeicamTrackVideoFx trackVideoFx = videoTrack.appendTrackVideoFx(fxType, desc, inPoint, duration, flag, fxTag);
        if (trackVideoFx != null) {
            trackVideoFx.setExtraTag(getExtraTag(videoTrack, trackVideoFx));
            if (CommandUtil.needSaveOperate(needSaveOperate)) {
                String tag = getTag(videoTrack);
                Object[] unDoParam = new Object[]{trackVideoFx.getInPoint(), new boolean[]{false}};
                Object[] redoParam = new Object[]{fxType, desc, inPoint, duration, flag, fxTag};
                CommandUtil.saveOperate("AppendTrackVideoFxCommand", unDoParam, redoParam, tag, tag);
            }
        }
        return trackVideoFx;
    }


    @Undo(className = "RemoveTrackVideoFxCommand", function = "appendTrackVideoFx",
            param = {"MeicamTrackVideoFx|oldTrackVideoFx"})
    public static void removeTrackVideoFx(MeicamVideoTrack videoTrack, long inPoint, boolean... needSaveOperate){
        MeicamTrackVideoFx removedVideoFx = videoTrack.removeTrackVideoFx(inPoint);
        if (CommandUtil.needSaveOperate(needSaveOperate) && removedVideoFx != null) {
            String tag = getTag(videoTrack);
            Object[] unDoParam = new Object[]{removedVideoFx.clone()};
            Object[] redoParam = new Object[]{inPoint};
            CommandUtil.saveOperate("RemoveTrackVideoFxCommand", unDoParam, redoParam, tag, tag);
        }
    }

    public static MeicamTrackVideoFx appendTrackVideoFx(MeicamVideoTrack videoTrack, MeicamTrackVideoFx oldTrackVideoFx){
        return videoTrack.appendTrackVideoFx(oldTrackVideoFx);
    }

    public static MeicamTransition checkBuildTransition(MeicamVideoTrack videoTrack, int index, String type, String transitionId, boolean... needSaveData) {
        if (TextUtils.isEmpty(transitionId)) {
            return removeTransition(videoTrack, index, needSaveData);
        }
        return buildTransition(videoTrack, index, type, transitionId, needSaveData);
    }

    @Undo(className = "RemoveTransitionCommand", function = "buildTransition",
            param = {"int|index", "MeicamTransition|transition","boolean...|needSaveData"})
    public static MeicamTransition removeTransition(MeicamVideoTrack videoTrack, int index, boolean... needSaveData) {
        MeicamTransition transition = videoTrack.removeTransition(index);
        if (transition == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSaveData)) {
            String tag = getTag(videoTrack);
            Object[] unDoParam = new Object[]{index, transition.clone(), new boolean[]{false}};
            Object[] redoParam = new Object[]{index};
            CommandUtil.saveOperate("RemoveTransitionCommand", unDoParam, redoParam, tag, tag);
        }
        return transition;
    }

    @Undo(className = "BuildTransitionCommand", function = "removeTransition",
            param = {"int|index", "boolean...|needSaveData"})
    public static MeicamTransition buildTransition(MeicamVideoTrack videoTrack, int index, MeicamTransition transition, boolean... needSaveData) {
        MeicamTransition meicamTransition = videoTrack.buildTransition(transition, index);
        if (meicamTransition == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSaveData)) {
            String tag = getTag(videoTrack);
            Object[] unDoParam = new Object[]{index, new boolean[]{false}};
            Object[] redoParam = new Object[]{index,transition.clone()};
            CommandUtil.saveOperate("BuildTransitionCommand", unDoParam, redoParam, tag, tag);
        }
        return transition;
    }

   @Undo(className ="ApplyToAllCommand", function = "restoreTransition",
           param = {"java.util.List<MeicamTransition>|transitions"})
   public static boolean applyTransitionToAll(MeicamVideoTrack videoTrack, MeicamTransition transition, boolean... needSaveData) {
      int transitionCount = videoTrack.getTransitionCount();
      List<MeicamTransition> transitions = new ArrayList<>();
      if (transitionCount > 0) {
         for (int index = 0; index < transitionCount; index++) {
            MeicamTransition transitionItem = videoTrack.getTransitionByCollectionIndex(index);
            if (transitionItem != null) {
               transitions.add((MeicamTransition) transitionItem.clone());
            }
         }
      }
      boolean result = videoTrack.applyTransitionToAll(transition);
      if (!result) {
         return false;
      }
      if (CommandUtil.needSaveOperate(needSaveData)) {
         String tag = getTag(videoTrack);
         Object[] unDoParam = new Object[]{transitions};
         Object[] redoParam = new Object[]{transition};
         CommandUtil.saveOperate("ApplyToAllCommand", unDoParam, redoParam, tag, tag);
      }
      return true;
   }

    static void restoreTransition(MeicamVideoTrack videoTrack, List<MeicamTransition> transitions) {
        videoTrack.removeAllTransition();
        if (CommonUtils.isEmpty(transitions)) {
            return;
        }
        for (MeicamTransition transition : transitions) {
            videoTrack.buildTransition(transition, transition.getIndex());
        }
    }

    @Undo(className = "InsertClipCommand", function = "removeVideoClip",
            param = {"int|index", "boolean|keepSpace", "boolean...|needSaveData"})
    public static MeicamVideoClip insertVideoClip(MeicamVideoTrack videoTrack, MeicamVideoClip videoClip,
                                                  int index, long trimIn, long trimOut, boolean... needSaveData) {
        MeicamVideoClip newVideoClip = videoTrack.insertVideoClip(videoClip, index, trimIn, trimOut);
        if (newVideoClip == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSaveData)) {
            String tag = getTag(videoTrack);
            InsertClipCommand command = CommandManager.getInstance().getCommand(tag, InsertClipCommand.class);
            if (command == null) {
                command = new InsertClipCommand(tag, new InsertClipCommand.UndoParam(index, videoTrack.getIndex() > 0, false));
                CommandManager.getInstance().putCommand(tag, command);
            }
            command.setRedoParam(new InsertClipCommand.RedoParam(videoClip, index, trimIn, trimOut));
        }
        return newVideoClip;
    }


    private static String getExtraTag(MeicamVideoTrack videoTrack, MeicamTrackVideoFx trackVideoFx) {
        return videoTrack.getIndex() + "|" + trackVideoFx.getCreateTag();
    }

    private static String getTag(MeicamVideoTrack videoTrack) {
        return TAG + videoTrack.getIndex();
    }

    public static MeicamVideoTrack getItByTag(String tag) {
        tag = tag.replaceAll(TAG, "");
        MeicamTimeline currentTimeline = EditorEngine.getInstance().getCurrentTimeline();
        MeicamVideoTrack videoTrack = currentTimeline.getVideoTrack(Integer.parseInt(tag));
        if (videoTrack == null) {
            videoTrack = currentTimeline.appendVideoTrack();
        }
        return videoTrack;
    }
}
