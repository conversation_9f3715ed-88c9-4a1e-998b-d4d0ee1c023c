<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_ff181818"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/ll_mosaic"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:baselineAligned="false"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:paddingStart="@dimen/dp_px_45"
        android:paddingTop="@dimen/dp_px_44"
        android:paddingBottom="@dimen/dp_px_31">

        <LinearLayout
            android:id="@+id/ll_mosaic_level_layout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_px_98"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/level"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_px_30" />

            <com.meishe.myvideo.view.MYSeekBarTextView
                android:id="@+id/sb_mosaic_level"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_px_15"
                android:layout_marginLeft="@dimen/dp_px_15"
                android:layout_marginEnd="@dimen/dp_px_101"
                android:layout_marginRight="@dimen/dp_px_101" />
        </LinearLayout>


        <LinearLayout
            android:id="@+id/ll_mosaic_num_layout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_px_98"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/number"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_px_30" />

            <com.meishe.myvideo.view.MYSeekBarTextView
                android:id="@+id/sb_mosaic_number"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_px_15"
                android:layout_marginLeft="@dimen/dp_px_15"
                android:layout_marginEnd="@dimen/dp_px_101"
                android:layout_marginRight="@dimen/dp_px_101" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_blur"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_px_98"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/level"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_px_30" />

            <com.meishe.myvideo.view.MYSeekBarTextView
                android:id="@+id/sb_blur_level"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_px_15"
                android:layout_marginLeft="@dimen/dp_px_15"
                android:layout_marginEnd="@dimen/dp_px_101"
                android:layout_marginRight="@dimen/dp_px_101" />

        </LinearLayout>
    </LinearLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_px_30"
        android:layout_marginLeft="@dimen/dp_px_30"
        android:layout_marginTop="@dimen/dp_px_30"
        android:layout_marginBottom="@dimen/dp_px_24" />

    <include layout="@layout/layout_confirm" />
</LinearLayout>