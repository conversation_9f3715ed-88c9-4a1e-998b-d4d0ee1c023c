package com.meishe.engine.asset;

import android.content.Context;

import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.bean.AssetList;
import com.meishe.engine.asset.bean.RequestParam;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/6/11 15:02
 * @Description :资源文件管理类 The interface of assets manager
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public abstract class IAssetsManager {
    protected IUserPlugin mUserPlugin;

    /**
     * Get aspect ratio data.
     * 获取比例值参数
     *
     * @param ratio the ratio 比例
     * @return the aspect ratio data int [ ] 比例值，包括比例的int 值和是否是通用比例的flag值
     */
    public abstract int[] getAspectRatio(float ratio);

    /**
     * Gets assets list.
     * 获取资源文件列表
     *
     * @param token       the token token
     * @param param       the param 请求参数
     * @param aspectRatio the aspect ratio 比例参数
     * @param ratioFlag   the ratio flag 比例flag
     * @param page        the page 请求页数
     * @param pageSize    the page size 请求页的item数量
     * @param callback    the callback 回调
     */
    abstract void getAssetsList(final String token, RequestParam param, final int aspectRatio, final int ratioFlag,
                                final int page, final int pageSize, final RequestCallback<AssetList> callback);

    /**
     * Gets local asset list.
     * 获取本地资源列表
     *
     * @param param    the param 请求参数
     * @param callback the callback 回调
     */
    abstract void getLocalAssetList(RequestParam param, final AssetCallback callback);

    /**
     * Gets error msg.
     * 获取错误信息
     *
     * @param context the context 上下文
     * @param type    the type 类型
     * @return the error msg 错误信息
     */
    public abstract String getErrorMsg(Context context, int type);

    /**
     * Gets user plugin.
     * 获取和用户相关插件
     *
     * @return the user plugin 和用户相关的插件
     */
    public IUserPlugin getUserPlugin() {
        return mUserPlugin;
    }

    /**
     * Sets user plugin.
     * 设置和用户相关的插件
     *
     * @param userPlugin the user plugin 和用户相关的插件
     */
    public void setUserPlugin(IUserPlugin userPlugin) {
        this.mUserPlugin = userPlugin;
    }

    public interface AssetsRequestCallback<T> {

        /**
         * On success.
         *
         * @param response the response
         */
        void onSuccess(BaseResponse<T> response);

        /**
         * On error.
         *
         * @param response the response
         */
        void onError(BaseResponse<T> response);
    }

    public interface AssetCallback {
        /**
         * On success.
         *
         * @param assetInfoList the asset info list
         */
        void onSuccess(List<AssetInfo> assetInfoList);

        /**
         * On failure.
         */
        void onFailure();
    }
}
