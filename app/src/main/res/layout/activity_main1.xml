<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <ImageView
        android:id="@+id/iv_create"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_px_67"
        android:layout_marginTop="@dimen/dp_px_45"
        android:contentDescription="@null"
        android:scaleType="fitXY"
        android:src="@mipmap/ic_main_top_bg"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_login" />

    <LinearLayout
        android:id="@+id/ll_capture"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_px_30"
        android:layout_marginLeft="@dimen/dp_px_30"
        android:layout_marginEnd="@dimen/dp_px_67"
        android:layout_marginBottom="@dimen/dp_px_15"
        android:background="@drawable/bg_round_corner_solid_ff292929_5"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="@+id/iv_create"
        app:layout_constraintBottom_toTopOf="@+id/ll_template"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintLeft_toRightOf="@+id/iv_create"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_create"
        app:layout_constraintVertical_weight="1">

        <ImageView
            android:layout_width="@dimen/dp_px_75"
            android:layout_height="@dimen/dp_px_75"
            android:contentDescription="@null"
            android:src="@mipmap/ic_main_capture" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/main_start_capture"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_px_30" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_template"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_px_30"
        android:layout_marginLeft="@dimen/dp_px_30"
        android:layout_marginEnd="@dimen/dp_px_67"
        android:background="@drawable/bg_round_corner_solid_ff292929_5"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="@+id/iv_create"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintLeft_toRightOf="@+id/iv_create"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_capture"
        app:layout_constraintVertical_weight="1">

        <ImageView
            android:layout_width="@dimen/dp_px_75"
            android:layout_height="@dimen/dp_px_75"
            android:contentDescription="@null"
            android:src="@mipmap/ic_main_template" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/main_start_template"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_px_30" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_login"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_marginTop="@dimen/dp_px_147"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_px_67"
        android:paddingLeft="@dimen/dp_px_45"
        android:paddingEnd="@dimen/dp_px_0"
        android:paddingRight="@dimen/dp_px_0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_login"
            android:layout_width="@dimen/dp_px_99"
            android:layout_height="@dimen/dp_px_99"
            android:layout_gravity="center_vertical"
            android:background="@drawable/selector_icon_user_login"
            android:contentDescription="@null" />

        <TextView
            android:id="@+id/tv_login"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dp_px_18"
            android:layout_marginLeft="@dimen/dp_px_18"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_px_42" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="@+id/iv_create"
        app:layout_constraintLeft_toLeftOf="@+id/iv_create"
        app:layout_constraintRight_toRightOf="@+id/iv_create"
        app:layout_constraintTop_toTopOf="@+id/iv_create">

        <Button
            android:id="@+id/bt_create"
            android:layout_width="@dimen/dp_px_90"
            android:layout_height="@dimen/dp_px_75"
            android:layout_gravity="center_horizontal"
            android:background="@mipmap/draft_manager_start" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_px_30"
            android:text="@string/draft_manage_start"
            android:textColor="@color/white_8"
            android:textSize="14sp" />
    </LinearLayout>

    <ImageButton
        android:id="@+id/bt_setting"
        android:layout_width="@dimen/dp_px_60"
        android:layout_height="@dimen/dp_px_54"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_marginEnd="@dimen/dp_px_67"
        android:layout_marginRight="@dimen/dp_px_45"
        android:background="@mipmap/draft_manager_setting"
        android:contentDescription="@null"
        app:layout_constraintBottom_toBottomOf="@+id/ll_login"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ll_login" />

    <FrameLayout
        android:id="@+id/fl_fragment_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_px_54"
        android:paddingStart="@dimen/dp_px_33"
        android:paddingLeft="@dimen/dp_px_33"
        android:paddingEnd="@dimen/dp_px_33"
        android:paddingRight="@dimen/dp_px_33"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/fl_main_bottom_container"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_create"
        app:layout_constraintVertical_weight="1" />

    <com.meishe.third.tablayout.CommonTabLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_200"
        android:layout_alignParentBottom="true"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/iv_create"
        app:tl_indicator_height="@dimen/dp_px_0"
        app:tl_tab_space_equal="true"
        app:tl_textSelectColor="@color/red_ff365"
        app:tl_textSize="@dimen/sp_px_30"
        app:tl_textUnselectedColor="@color/white_8" />
    <!--底部公用显示容器,注意，不要添加某一个单一view，只能使用容器-->
    <FrameLayout
        android:id="@+id/fl_main_bottom_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>