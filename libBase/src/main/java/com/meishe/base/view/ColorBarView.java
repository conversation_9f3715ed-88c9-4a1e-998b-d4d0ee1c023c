package com.meishe.base.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.graphics.Shader;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.Nullable;

import com.meishe.base.R;
import com.meishe.base.utils.SizeUtils;


/**
 * Author: Aeolou
 * Date:2020/3/14 0014
 * Email:<EMAIL>
 */

public class ColorBarView extends View {
    private static int[] colors;
    private OnColorChangeListener onColorChangeListener;

    private float width;
    private float height;

    /**
     * 长条宽高
     * The bar width and height
     */
    private float barWidth, barHeight;

    /**
     * 滑块
     * Thumb drawable
     */
    private int thumbDrawable;
    private Bitmap thumbBitmap;
    /**
     * 滑块宽高
     * Thumb width and height
     */
    private float thumbWidth, thumbHeight;
    /**
     * 滑块当前的位置
     * The current position of the slider
     */
    private float currentThumbOffset;
    /**
     * 彩色长条开始位置
     * Start position of colored strip
     */
    private float barStartX, barStartY;

    private static int STATUS;
    private static final int STATUS_INIT = 0;

    /***
     * 移动了action bar
     * Moved the action bar
     */
    private static final int STATUS_SEEK = 1;

    /**
     * 长条画笔
     * The bar paint
     */
    private Paint barPaint;
    /**
     * 滑块画笔
     * The thumb paint
     */
    private Paint thumbPaint;

    private int currentColor = 1;

    private int mHalfStrokeWidth;
    private int topMargin;

    public interface OnColorChangeListener {
        void onColorChange(int color);

        void onColorChangeStart(int color);

        void onColorChangeFinish();
    }


    public ColorBarView(Context context) {
        this(context, null);
    }

    public ColorBarView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ColorBarView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
        initCustomAttrs(context, attrs);
        initView();

    }

    private void initView() {
        thumbBitmap = BitmapFactory.decodeResource(getResources(), thumbDrawable);
        mHalfStrokeWidth = SizeUtils.dp2px(12f) / 2;
        topMargin = SizeUtils.dp2px(2.0f);
    }


    private void init(Context context) {
        //初始化渐变色 Init colors
        initColors();
        STATUS = STATUS_INIT;
        barPaint = new Paint();
        barPaint.setAntiAlias(true);
        barPaint.setStrokeCap(Paint.Cap.ROUND);
        thumbPaint = new Paint();
        thumbPaint.setAntiAlias(true);
        thumbPaint.setStrokeCap(Paint.Cap.ROUND);
    }

    private void initColors() {
        int colorCount = 12;
        int colorAngleStep = 360 / colorCount;
        colors = new int[colorCount + 1];
        float[] hsv = new float[]{0f, 1f, 1f};
        for (int i = 0; i < colors.length; i++) {
            hsv[0] = 360 - (i * colorAngleStep) % 360;
            if (hsv[0] == 360) hsv[0] = 359;
            colors[i] = Color.HSVToColor(hsv);
        }
    }

    private void initCustomAttrs(Context context, AttributeSet attrs) {
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.ColorBarView);
        if (typedArray != null) {
            thumbDrawable = typedArray.getResourceId(R.styleable.ColorBarView_thumbDrawable, R.mipmap.icon_color_thumb);
            barHeight = typedArray.getDimension(R.styleable.ColorBarView_barHeight, 30);
            thumbHeight = typedArray.getDimension(R.styleable.ColorBarView_thumbHeight, 80);
            typedArray.recycle();
        }
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        setMeasuredDimension(measureWidth(widthMeasureSpec), measureHeight(heightMeasureSpec));
    }

    /**
     * 测量高
     * Measure height
     *
     * @param heightMeasureSpec the heightMeasureSpec
     * @return the height
     */
    private int measureHeight(int heightMeasureSpec) {
        int result = 0;
        int specMode = MeasureSpec.getMode(heightMeasureSpec);
        int specSize = MeasureSpec.getSize(heightMeasureSpec);
        if (specMode == MeasureSpec.EXACTLY) {
            //精确值模式 EXACTLY
            result = Math.max(Math.max((int) thumbHeight, (int) barHeight), specSize);
        } else if (specMode == MeasureSpec.AT_MOST) {
            //最大值模式 AT_MOST
            result = Math.max((int) thumbHeight, (int) barHeight + getPaddingTop() + getPaddingBottom());
        } else {
            result = specSize;
        }

        return result;
    }

    /**
     * 测量宽
     * Measure width
     *
     * @param widthMeasureSpec the widthMeasureSpec
     * @return the width
     */
    private int measureWidth(int widthMeasureSpec) {
        int result = 0;
        int specMode = MeasureSpec.getMode(widthMeasureSpec);
        int specSize = MeasureSpec.getSize(widthMeasureSpec);
        if (specMode == MeasureSpec.EXACTLY) {
            //精确值模式 EXACTLY
            result = specSize;
        } else if (specMode == MeasureSpec.AT_MOST) {
            //最大值模式 AT_MOST
            result = 200;
        }
        return result;
    }


    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        width = w;
        height = h;
        thumbWidth = thumbHeight * ((float) thumbBitmap.getWidth() / (float) thumbBitmap.getHeight());
        barWidth = width - thumbWidth;
        //不从0开始，左右边缘用于显示滑块
        // Not starting from 0, left and right edges are used to display sliders.
        barStartX = thumbWidth / 2;
        barStartY = height / 2 - barHeight / 2;
        super.onSizeChanged(w, h, oldw, oldh);
    }


    /**
     * 处理点击和滑动事件
     * Handling click and slide events
     *
     * @param event
     * @return
     */

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                currentThumbOffset = (int) event.getX();
                if (currentThumbOffset <= thumbWidth / 2) currentThumbOffset = thumbWidth / 2 + 1;
                if (currentThumbOffset >= barWidth + thumbWidth / 2)
                    currentThumbOffset = barWidth + thumbWidth / 2;
                STATUS = STATUS_SEEK;
                changColor();
                if (onColorChangeListener != null) {
                    onColorChangeListener.onColorChangeStart(currentColor);
                }
                break;
            //滑动时 When sliding
            case MotionEvent.ACTION_MOVE:
                currentThumbOffset = (int) event.getX();
                if (currentThumbOffset <= thumbWidth / 2) currentThumbOffset = thumbWidth / 2 + 1;
                if (currentThumbOffset >= barWidth + thumbWidth / 2)
                    currentThumbOffset = barWidth + thumbWidth / 2;
                changColor();
                if (onColorChangeListener != null) {
                    onColorChangeListener.onColorChange(currentColor);
                }
                break;
            case MotionEvent.ACTION_UP:
                if (onColorChangeListener != null) {
                    onColorChangeListener.onColorChangeFinish();
                }
                break;
        }

        invalidate();
        return true;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        drawBar(canvas);
        drawThumb(canvas);
        super.onDraw(canvas);
    }

    /**
     * 滑动滑块使颜色发生变化
     * Chang color
     */
    private void changColor() {
        //当前滑块中心的长度
        // The length of the current slider center
        float position = currentThumbOffset - thumbWidth / 2.0f;
        float colorH = 360 - position / barWidth * 360;
        currentColor = Color.HSVToColor(new float[]{colorH, 1.0f, 1.0f});
    }


    /**
     * 获取当前颜色
     * Get current color
     *
     * @return the color value
     */
    private int getCurrentColor() {
        return currentColor;
    }

    /**
     * 设置当前颜色
     * Set current color
     *
     * @param currentColor the color
     */
    public void setCurrentColor(int currentColor) {
        this.currentColor = currentColor;
        if (onColorChangeListener != null) {
            onColorChangeListener.onColorChange(currentColor);
        }
        invalidate();
    }

    /**
     * 绘制底部颜色条
     * Draw bar
     *
     * @param canvas the canvas
     */
    private void drawBar(Canvas canvas) {
        barPaint.setShader(
                new LinearGradient(barStartX, barStartY + barHeight / 2,
                        barStartX + barWidth, barStartY + barHeight / 2,
                        colors, null, Shader.TileMode.CLAMP));
        canvas.drawRect(new RectF(barStartX, barStartY, barStartX + barWidth,
                barStartY + barHeight), barPaint);
    }


    /**
     * 绘制滑块
     * Draw bitmap thumb
     *
     * @param canvas the canvas
     */
    private void drawBitmapThumb(Canvas canvas) {
        float[] currentColorHSV = new float[3];
        Color.RGBToHSV(Color.red(currentColor), Color.green(currentColor), Color.blue(currentColor), currentColorHSV);
        //根据HSV计算颜色所在位置 Calculate the position of the color based on HSV
        float position = barWidth * currentColorHSV[0] / 360.0f;
        currentThumbOffset = barWidth - position + thumbWidth / 2;
        canvas.drawBitmap(thumbBitmap, null, getThumbRect(), thumbPaint);
    }

    /**
     * 绘制滑块
     * Draw thumb
     *
     * @param canvas the canvas
     */
    private void drawThumb(Canvas canvas) {
        float[] currentColorHSV = new float[3];
        Color.RGBToHSV(Color.red(currentColor), Color.green(currentColor), Color.blue(currentColor), currentColorHSV);
        //根据HSV计算颜色所在位置 Calculate the position of the color based on HSV
        float position = barWidth * currentColorHSV[0] / 360.0f;
        currentThumbOffset = barWidth - position + thumbWidth / 2;

        float startX = currentThumbOffset;
        float startY = height / 2 + barHeight / 2 + topMargin;
        Path path = new Path();
        path.lineTo(startX, startY);
        path.lineTo(startX - mHalfStrokeWidth, startY + mHalfStrokeWidth * 2);
        path.lineTo(startX + mHalfStrokeWidth, startY + mHalfStrokeWidth * 2);
        path.lineTo(startX, startY);
        Paint paint = new Paint();
        paint.setAntiAlias(true);
        paint.setColor(Color.WHITE);
        paint.setStyle(Paint.Style.FILL);
        canvas.drawPath(path, paint);
    }

    /**
     * 获取滑块所在的矩形区域
     * Get thumb rect
     */
    private RectF getThumbRect() {
        return new RectF(currentThumbOffset - thumbWidth / 2, height / 2 + barHeight / 2,
                currentThumbOffset + thumbWidth / 2, height / 2 + barHeight / 2 + thumbHeight / 2);
    }

    public void setOnColorChangerListener(OnColorChangeListener onColorChangerListener) {
        this.onColorChangeListener = onColorChangerListener;
    }

}
