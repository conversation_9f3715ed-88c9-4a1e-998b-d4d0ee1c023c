package com.meishe.myvideo.view;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.meishe.myvideo.R;

/**
 * The type Common confirm dialog.
 * 常见确认对话框类
 */
public class CommonConfirmDialog extends Dialog {
    private OnConfirmClickListener mPrivacyListener;

    /**
     * The interface On confim click listener.
     * 确认点击接口
     */
    public interface OnConfirmClickListener {
        /**
         * On button confim click.
         * 点击确认按钮
         */
        void onButtonConfirmClick();
    }

    /**
     * Sets on confim click listener.
     * 设置确认单击监听器
     * @param listener the listener
     */
    public void setOnConfirmClickListener(OnConfirmClickListener listener) {
        this.mPrivacyListener = listener;
    }

    public CommonConfirmDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_confim_layout);
        setCanceledOnTouchOutside(false);
        setCancelable(false);
        TextView tvConfirm = findViewById(R.id.agreeButton);
        TextView tvCancel = findViewById(R.id.notUsedButton);
        tvConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mPrivacyListener.onButtonConfirmClick();
                dismiss();
            }
        });
        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
    }
}
