package com.meishe.draft.db;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.PrimaryKey;


/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/18 15:25
 * @Description :工程entity The resource for local draft.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
@Entity
public class ProjectEntity{
   @PrimaryKey
   @NonNull
   private String projectResourceId = "123456789";

   /**
    * 工程Id The project id
    */
   private String projectId;

   private String resourceId;

   @NonNull
   public String getProjectResourceId() {
      return projectResourceId;
   }

   public void setProjectResourceId(@NonNull String projectResourceId) {
      this.projectResourceId = projectResourceId;
   }

   public String getProjectId() {
      return projectId;
   }

   public void setProjectId(String projectId) {
      this.projectId = projectId;
   }

   public String getResourceId() {
      return resourceId;
   }

   public void setResourceId(String resourceId) {
      this.resourceId = resourceId;
   }
}
