package com.meishe.myvideo.activity;

import android.os.Bundle;

import com.meishe.base.bean.MediaData;
import com.meishe.base.model.BaseActivity;
import com.meishe.myvideo.R;
import com.meishe.myvideo.fragment.MediaSelectFragment;

import static com.meishe.logic.constant.PagerConstants.TYPE_DEFAULT;

public class TestActivity extends BaseActivity {

    @Override
    protected int bindLayout() {
        return R.layout.activity_test;
    }

    @Override
    protected void initView() {
        // Add MediaSelectFragment
        MediaSelectFragment fragment = MediaSelectFragment.create(
                MediaData.TYPE_ALL,
                TYPE_DEFAULT,
                true,
                new MediaSelectFragment.MediaChangeListener() {
                    @Override
                    public void onMediaChange(MediaData mediaData) {
                        // Handle media selection change
                    }

                    @Override
                    public void onMediaPreView(MediaData mediaData) {
                        // Handle media preview
                    }
                }
        );

        getSupportFragmentManager()
                .beginTransaction()
                .replace(R.id.fragment_container, fragment)
                .commit();
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        // Initialize data if needed
    }
} 