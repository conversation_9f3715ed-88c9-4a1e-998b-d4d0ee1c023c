package com.meishe.engine.bean;

import com.meishe.engine.local.LTrackInfo;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/7/3 17:11
 */
public abstract class TrackInfo<T extends com.meicam.sdk.NvsObject> extends NvsObject<T> implements Cloneable {
    /**
     * 类型， 必传
     * type， It is necessary.
     */
    private String type = "base";

    /**
     * 索引，必传
     * index， It is necessary.
     */
    private int index;
    /**
     * 是否展示轨道，移动端默认为true
     * Whether to display the track or not, the mobile terminal defaults to true
     */
    private boolean show = true;
    /**
     * 轨道音量，默认为1
     * Volume Default is 1.
     */
    private float volume = 1;

    /**
     * zValue，用来标识堆叠数据，越大越靠上
     * zvalue，Used to identify stacked data. The larger the stack, the higher it is.
     */
    private float zValue;

    public TrackInfo(T t) {
        super(t);
    }

    @Deprecated
    public TrackInfo(String type, int index) {
        this.type = type;
        this.index = index;
    }

    public TrackInfo(T t, String type, int index) {
        super(t);
        this.type = type;
        this.index = index;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getIndex() {
        return index;
    }

    void setIndex(int index) {
        this.index = index;
    }

    public boolean isShow() {
        return show;
    }

    public void setShow(boolean show) {
        this.show = show;
    }

    public float getVolume() {
        return volume;
    }

    public void setVolume(float volume) {
        if (Float.isNaN(volume)) {
            return;
        }
        this.volume = volume;
    }

    public float getzValue() {
        return zValue;
    }

    public void setZValue(float zValue) {
        this.zValue = zValue;
    }

    /**
     * 获取clip的数量
     * Get clip count
     *
     * @return clip的数量 clip count
     */
    public abstract int getClipCount();

    protected void setCommondData(LTrackInfo lTrackInfo) {
        lTrackInfo.setShow(show);
        lTrackInfo.setVolume(volume);
        lTrackInfo.setzValue(zValue);
    }
}
