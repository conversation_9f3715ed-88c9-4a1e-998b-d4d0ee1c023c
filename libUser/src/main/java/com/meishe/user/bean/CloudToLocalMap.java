package com.meishe.user.bean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/14 16:33
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CloudToLocalMap implements Serializable {
   List<Item> cloudToLocalList = new ArrayList<>();

   public void clear(){
      cloudToLocalList.clear();
   }

   public void add(String key, String value, boolean isAssets) {
      cloudToLocalList.add(new Item(key, value, isAssets));
   }

   public List<Item> getCloudToLocalList() {
      return cloudToLocalList;
   }

   public void setCloudToLocalList(List<Item> cloudToLocalList) {
      this.cloudToLocalList = cloudToLocalList;
   }

   public class Item implements Serializable{
      public String url;
      public String path;
      public boolean isAssets;

      public Item(String url, String path, boolean isAssets) {
         this.url = url;
         this.path = path;
         this.isAssets = isAssets;
      }
   }
}
