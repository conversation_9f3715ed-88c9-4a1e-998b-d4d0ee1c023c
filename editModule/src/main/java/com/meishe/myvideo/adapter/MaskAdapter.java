package com.meishe.myvideo.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.engine.bean.MaskInfoData;
import com.meishe.myvideo.R;

import java.util.List;

/**
 * The type Mask adapter.
 * 此类为蒙版适配器
 *
 * <AUTHOR>
 * @CreateDate :2020/9/17 18:14
 * @Description : 底部蒙版view 适配器 The mask adapter
 */
public class MaskAdapter extends RecyclerView.Adapter<MaskAdapter.MaskItemViewHolder> {
    private int mSelectPosition = -1;
    private final Context mContext;
    private List<MaskInfoData> mData;
    private OnItemClickedListener mOnItemClickedListener;

    /**
     * Sets on item clicked listener.
     * 设置条目单击监听器
     *
     * @param listener the listener
     */
    public void setOnItemClickedListener(OnItemClickedListener listener) {
        this.mOnItemClickedListener = listener;
    }

    /**
     * Instantiates a new Mask adapter.
     * 实例化一个新的蒙版适配器
     *
     * @param context the context
     */
    public MaskAdapter(Context context) {
        this.mContext = context;
    }

    /**
     * Gets data.
     * 获取数据
     *
     * @return the data
     */
    public List<MaskInfoData> getData() {
        return mData;
    }

    /**
     * Sets data.
     * 设置数据
     *
     * @param data the data
     */
    public void setData(List<MaskInfoData> data) {
        this.mData = data;
        notifyDataSetChanged();
    }

    /**
     * Sets selection.
     * 设置选择
     *
     * @param index the index
     */
    public void setSelection(int index) {
        if (mSelectPosition >= 0) {
            notifyItemChanged(mSelectPosition);
        }
        if (index >= 0 && index < getData().size()) {
            mSelectPosition = index;
            notifyItemChanged(index);
        }
    }

    @NonNull
    @Override
    public MaskItemViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.view_menu_mask_item, parent, false);
        return new MaskItemViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull MaskItemViewHolder holder, int position) {
        holder.bindViewHolder(getItem(position), position, v -> {
            if (mOnItemClickedListener != null) {
                int adapterPosition = holder.getAdapterPosition();
                mOnItemClickedListener.onItemClicked(mData.get(adapterPosition), adapterPosition);
            }
        });
    }


    /**
     * Gets item.
     * 获取条目
     *
     * @param position the position
     * @return the item
     */
    public MaskInfoData getItem(int position) {
        if (position < 0 || mData == null || position >= mData.size()) {
            return null;
        }
        return mData.get(position);
    }

    @Override
    public int getItemCount() {
        return mData == null ? 0 : mData.size();
    }

    /**
     * The type Mask item view holder.
     * 此类类型为蒙版视图条目持有者
     */
    class MaskItemViewHolder extends RecyclerView.ViewHolder {
        private final View mRootView;
        private final ImageView mIcon;
        private final TextView mName;

        /**
         * Instantiates a new Mask item view holder.
         * 实例化一个新的蒙版项视图持有者
         *
         * @param itemView the item view
         */
        MaskItemViewHolder(@NonNull View itemView) {
            super(itemView);
            mRootView = itemView;
            mIcon = itemView.findViewById(R.id.icon);
            mName = itemView.findViewById(R.id.name);
        }


        /**
         * Bind view holder.
         * 绑定视图
         *
         * @param info     the info
         * @param position the position
         * @param listener the listener
         */
        @SuppressLint("UseCompatLoadingForDrawables")
        public void bindViewHolder(MaskInfoData info, final int position, final View.OnClickListener listener) {
            if (position == mSelectPosition) {
                mIcon.setBackground(mContext.getResources().getDrawable(R.drawable.bg_shape_edit_mask));
            } else {
                mIcon.setBackground(mContext.getResources().getDrawable(R.drawable.bg_shape_edit_mask_unselected));
            }
            mIcon.setImageResource(info.getCoverId());
            mName.setText(info.getName());
            mRootView.setOnClickListener(listener);
        }
    }

    /**
     * The interface On item clicked listener.
     * 接口为条目点击监听
     */
    public interface OnItemClickedListener {
        /**
         * On item clicked.
         * 点击条目
         *
         * @param itemInfo the item info
         * @param position the position
         */
        void onItemClicked(MaskInfoData itemInfo, int position);
    }
}
