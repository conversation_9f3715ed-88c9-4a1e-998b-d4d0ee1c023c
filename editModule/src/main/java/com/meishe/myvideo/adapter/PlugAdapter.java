package com.meishe.myvideo.adapter;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.meishe.base.utils.ImageLoader;
import com.meishe.myvideo.R;
import com.meishe.engine.bean.Plug;
import com.meishe.third.adpater.BaseViewHolder;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/30 16:19
 * @Description :插件特效 Adapter Adapter for speed curve.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class PlugAdapter extends BaseSelectAdapter<Plug> {
    private final Context mContext;

    public PlugAdapter(Context context) {
        super(R.layout.view_recy_plug);
        mContext = context;
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, Plug item) {
        ImageView icon = helper.getView(R.id.icon);
        ImageView iconStop = helper.getView(R.id.icon_stop);
        TextView name = helper.getView(R.id.name);
        if (item.isDisplay()) {
            iconStop.setVisibility(View.INVISIBLE);
        } else {
            iconStop.setVisibility(View.VISIBLE);
        }
        name.setText(item.getName());
        ImageLoader.loadUrl(mContext, item.coverPath, icon);
    }
}
