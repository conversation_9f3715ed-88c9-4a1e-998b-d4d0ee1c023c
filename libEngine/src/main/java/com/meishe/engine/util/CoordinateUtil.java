package com.meishe.engine.util;

import android.graphics.Matrix;
import android.graphics.PointF;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/18 11:30
 * @Description :坐标系转换相关工具类 Utils for coordinate parsing.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CoordinateUtil {

    /**
     * 将坐标点由经典坐标系下转换为View坐标系下的点
     * Parse points in canonical coordinate to view coordinate points.
     *
     * @param verticesList 坐标集合 List of point
     * @param width        view的宽 Width of view
     * @param height       view的高 Height of view
     * @return 转换后的点 List of points after parsing.
     */
    public static List<PointF> parseCanonicalToView(List<PointF> verticesList, int width, int height) {
        List<PointF> newList = new ArrayList<>();
        for (int i = 0; i < verticesList.size(); i++) {
            PointF pointF = mapCanonicalToView(verticesList.get(i), width, height);
            newList.add(pointF);
        }
        return newList;
    }

    /**
     * 将坐标点由经典坐标系下转换为View坐标系下的点
     * Parse point in canonical coordinate to view coordinate point.
     *
     * @param oldPointF 原始点 Point in canonical coordinate.
     * @param width     view的宽 Width of view
     * @param height    view的高 Height of view
     * @return 转换后的点 The point after parsing.
     */
    public static PointF mapCanonicalToView(PointF oldPointF, int width, int height) {
        PointF pointF = new PointF();
        pointF.x = oldPointF.x + width / 2F;
        pointF.y = height / 2F - oldPointF.y;
        return pointF;
    }

    /**
     * 将坐标点由View坐标系下转换为经典坐标系下的点
     * Parse points in view coordinate to  canonical coordinate point.
     *
     * @param oldPointF 原始点 Point in view coordinate.
     * @param width     view的宽 Width of view
     * @param height    view的高 Height of view
     * @return 转换后的点 The point after parsing.
     */
    public static PointF mapViewToCanonical(PointF oldPointF, int width, int height) {
        PointF pointF = new PointF();
        pointF.x = oldPointF.x - width / 2F;
        pointF.y = height / 2F - oldPointF.y;
        return pointF;
    }

    /**
     * Transform data.
     * 对点位进行坐标转换
     *
     * @param point       the point 要转换的点
     * @param centerPoint the center point 坐标中心点
     * @param scale       the scale 缩放值
     * @param rotation      the rotation 旋转值
     */
    public static void transformData(PointF point, PointF centerPoint, float scale, float rotation) {
        float[] src = new float[]{point.x, point.y};
        Matrix matrix = new Matrix();
        matrix.setRotate(rotation, centerPoint.x, centerPoint.y);
        matrix.mapPoints(src);
        matrix.setScale(scale, scale, centerPoint.x, centerPoint.y);
        matrix.mapPoints(src);
        point.x = Math.round(src[0]);
        point.y = Math.round(src[1]);
    }
}
