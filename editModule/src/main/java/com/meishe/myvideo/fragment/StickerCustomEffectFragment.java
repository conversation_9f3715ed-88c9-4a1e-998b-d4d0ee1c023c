package com.meishe.myvideo.fragment;

import android.os.Bundle;

import com.meishe.base.utils.SizeUtils;
import com.meishe.business.assets.fragment.FlowFragment;
import com.meishe.business.assets.iview.AssetsView;
import com.meishe.business.assets.presenter.FlowPresenter;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.logic.constant.PagerConstants;
import com.meishe.myvideo.R;

/**
 * 自定义贴纸动画特效fragment
 * Custom stickers animation effects ,fragment
 */
public class StickerCustomEffectFragment extends FlowFragment<FlowPresenter> implements AssetsView {
    private onEventListener mOnEventListener;

    public StickerCustomEffectFragment() {
        leftItemDecoration = SizeUtils.dp2px(7);
    }

    public static StickerCustomEffectFragment create() {
        StickerCustomEffectFragment fragment = new StickerCustomEffectFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE, AssetInfo.ASSET_CUSTOM_STICKER_PACKAGE);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int getItemLayoutResId() {
        return R.layout.item_sticker_animation_effect;
    }

    @Override
    protected void onAdapterItemClick(int position) {
        IBaseInfo item = mAdapter.getItem(position);
        if (item != null && mOnEventListener != null) {
            mOnEventListener.onStickerClick(item.getPackageId());
        }
    }

    @Override
    protected String getAssetId() {
        return null;
    }


    public StickerCustomEffectFragment setOnEventListener(onEventListener listener) {
        mOnEventListener = listener;
        return this;
    }

    public interface onEventListener {
        void onStickerClick(String stickerId);
    }
}
