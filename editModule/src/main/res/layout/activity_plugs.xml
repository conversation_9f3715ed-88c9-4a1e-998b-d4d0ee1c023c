<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_1010"
    android:orientation="vertical"
    tools:context=".activity.AtomicActivity"
    tools:ignore="MissingDefaultResource">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_200">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/dp_px_70"
            android:layout_height="@dimen/dp_px_70"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dp_px_42"
            android:layout_marginLeft="@dimen/dp_px_42"
            android:contentDescription="@null"
            android:padding="@dimen/dp_px_11"
            android:scaleType="fitXY"
            android:src="@mipmap/close_white" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/title_plug"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_px_48" />
    </FrameLayout>

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:clipToPadding="false"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        app:tabGravity="center"
        app:tabIndicatorHeight="0dp"
        app:tabMode="scrollable"
        app:tabRippleColor="@android:color/transparent" />

    <com.meishe.base.view.CustomViewPager
        android:id="@+id/viewPager"
        android:layout_marginTop="@dimen/dp_px_30"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:ignore="NestedWeights" />
</LinearLayout>