package com.meishe.myvideo.fragment;

import android.view.View;

import com.meishe.base.model.BaseFragment;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.myvideo.R;
import com.meishe.player.view.TimelineFrameSelectView;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/12/14 17:52
 * @Description :编辑页面选择帧fragment The cover edit Fragment for selecting Frame
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CoverEditSelectFrameFragment extends BaseFragment {
    private TimelineFrameSelectView mTimelineView;
    private OnTimelineScrollListener mOnTimelineScrollListener;
    private MeicamTimeline mTimeline;

    public void setOnTimelineScrollListener(OnTimelineScrollListener listener) {
        this.mOnTimelineScrollListener = listener;
    }

    public CoverEditSelectFrameFragment() {
        mTimeline = EditorEngine.getInstance().getCurrentTimeline();
    }

    public static CoverEditSelectFrameFragment create() {
        return new CoverEditSelectFrameFragment();
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_cover_edit_select_frame;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        mTimelineView = rootView.findViewById(R.id.frame_select_view);
        mTimelineView.setTrackData(mTimeline.getVideoTrack(0));
        mTimelineView.setOnScrollListener((dx, oldDx) -> {
            if (mOnTimelineScrollListener != null) {
                mOnTimelineScrollListener.onScrollChanged(mTimelineView.getScrollTimeInTimeline(dx));
            }
        });
    }

    @Override
    protected void initData() {

    }


    /**
     * Smooth scroll to.
     * 滑动到某个时间点
     *
     * @param duration the duration
     */
    public void smoothScrollTo(long duration){
        mTimelineView.smoothScrollTo(duration);
    }


    /**
     * The interface On scroll listener.
     * 滑动监听的接口
     */
    public interface OnTimelineScrollListener {


        /**
         * 滑动回调
         * On scroll changed.
         *
         * @param point the point 在timeline上的时间点
         */
        void onScrollChanged(long point);
    }
}
