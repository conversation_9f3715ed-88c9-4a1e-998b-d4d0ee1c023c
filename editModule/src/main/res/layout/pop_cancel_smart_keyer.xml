<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp_px_720"
    android:layout_height="@dimen/dp_px_480"
    android:background="@color/ffffffff"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_px_100"
        android:gravity="center"
        android:text="@string/cancel_smart_keyer_hint"
        android:textColor="@color/ff000000"
        android:textSize="@dimen/sp_px_39" />

    <TextView
        android:id="@+id/tv_confirm"
        android:layout_width="@dimen/dp_px_480"
        android:layout_height="@dimen/dp_px_90"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_px_100"
        android:background="@color/red_fc2b55"
        android:gravity="center"
        android:text="@string/confirm"
        android:textColor="@color/ffffffff"
        android:textSize="@dimen/sp_px_36" />

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_px_30"
        android:gravity="center"
        android:text="@string/cancel"
        android:textColor="@color/ff000000"
        android:textSize="@dimen/sp_px_36" />
</LinearLayout>