package com.meishe.user;

import android.text.TextUtils;

import com.meishe.base.utils.NetUtils;
import com.meishe.base.utils.Utils;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.libplugin.user.UserConstant;
import com.meishe.logic.manager.PreferencesManager;
import com.meishe.logic.utils.NvsServerClient;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;
import com.meishe.user.bean.UserLoginToken;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/3/22 15:39
 * @Description :用户相关工具类 Util for user case.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class UserUtil {

    /**
     * login
     * <p>
     * 登录
     *
     * @param callBack 回调 callback
     */
    public static void login(final IUserPlugin.ILoginCallBack callBack) {
            boolean isLogin = PreferencesManager.get().getBoolean(UserState.UserDataKey.IS_LOGIN, false);
            if (isLogin && NetUtils.isNetworkAvailable(Utils.getApp().getApplicationContext())) {
                String account = PreferencesManager.get().getString(UserState.UserDataKey.ACCOUNT);
                String pass = PreferencesManager.get().getString(UserState.UserDataKey.PASSWORD);
                boolean isSub = PreferencesManager.get().getBoolean(UserState.UserDataKey.IS_SUB_ACCOUNT);
                startLogin(account, pass, isSub, "649c42dce714423fb42860e483316a50", callBack);
            } else {
                if (callBack != null) {
                    callBack.onLoginFailed(UserConstant.ResultCode.DO_NOT_NEED);
                }
            }
    }

    /**
     * Logout
     * <p>
     * 退出登录
     */
    public static void logOut() {
        UserState.get().setLogin(false);
        UserState.get().setToken(null);
        UserState.get().setUserInfo(null, null, false);

    }

    /**
     * Login
     * <p></>
     * 登录
     * @param loginName 用户名 Login name
     * @param passWord 密码 password
     * @param appId appId appId
     * @param callBack 登录回调 Callback
     */
    public static void startLogin(String loginName,  String passWord, boolean isSubAccount, String appId, final IUserPlugin.ILoginCallBack callBack) {

        final String newPassWord = passWord;
        final String newLoginName = loginName;
        try {
            passWord = URLEncoder.encode(passWord,"utf-8");
            loginName = URLEncoder.encode(loginName,"utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        final Map<String, String> map = new HashMap<>();
        map.put("loginName", loginName);
        map.put("password", passWord);
        map.put("appId", appId);
        map.put("isSubAccount", String.valueOf(isSubAccount));
        String apiName = "materialcenter/myvideo/user/login";
        NvsServerClient.get().requestPost(null, NvsServerClient.getAssetsHost(), apiName, map,
                new RequestCallback<UserLoginToken>() {

            @Override
            public void onSuccess(BaseResponse<UserLoginToken> response) {
                if (response != null && response.getData() != null) {
                    String token = response.getData().getToken();
                    if (TextUtils.isEmpty(token)) {
                        onFailed();
                        if (callBack != null) {
                            callBack.onLoginFailed(response.getCode());
                        }
                    } else {
                        UserState.get().setLogin(true);
                        UserState.get().setUserInfo(newLoginName, newPassWord, isSubAccount);
                        UserState.get().setToken(token);
                        if (callBack != null) {
                            callBack.onLoginSuccess(token);
                        }
                    }
                } else {
                    onFailed();
                    if (callBack != null) {
                        callBack.onLoginFailed(response.getCode());
                    }
                }
            }

            @Override
            public void onError(BaseResponse<UserLoginToken> response) {
                onFailed();
                if (callBack != null) {
                    callBack.onLoginFailed(response.getCode());
                }
            }
        });
    }

    /**
     * Is user state is login
     * <p>
     * 是否是登录状态
     *
     * @return true:yes false: no
     */
    public static boolean isLogin() {
        return UserState.get().isLogin();
    }

    private static void onFailed() {
        UserState.get().setLogin(false);
        UserState.get().setToken(null);
    }
}
