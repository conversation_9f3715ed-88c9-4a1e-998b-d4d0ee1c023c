package com.meishe.myvideo.interfaces;


import com.meishe.engine.interf.IBaseInfo;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZ<PERSON>
 * @CreateDate :2020/12/29 14:10
 * @Description :底部视图事件监听  注意：加方法要慎重，轻易不要加，能改造原有方法就改造,如果差异非常大可以考虑继承。
 * The bottom view event listener
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public abstract class BottomEventListener {
    /**
     * 使视图消失
     * Not show the view
     */
    public void dismiss() {

    }

    /**
     * 当视图消失
     * On dismiss the view
     *
     * @param confirm ,true is clicked the confirm view,false not
     */
    public void onDismiss(boolean confirm) {

    }

    /**
     * 某一项被点击
     * A certain item is clicked
     *
     * @param baseInfo 基础信息类 the base info
     * @param applyAll 应用全部 true apply all ,false not
     * @param type     类型
     */
    public void onItemClick(IBaseInfo baseInfo, boolean applyAll, int type) {

    }

    /**
     * 某一项被点击
     * A certain item is clicked
     *
     * @param baseInfo 基础信息类 the base info
     * @param applyAll 应用全部 true apply all ,false not
     */
    public void onItemClick(IBaseInfo baseInfo, boolean applyAll) {

    }

    /**
     * 进度发生变化
     * The progress changed
     *
     * @param progress 进度  the progress
     * @param fromUser 用户操作 true from user,false not
     * @param type     类型 the type
     */
    public void onProgressChanged(int progress, boolean fromUser, int type) {

    }

    /**
     * 停止拖动
     * On stop tracking touch
     *
     * @param type 类型 the type
     */
    public void onStopTrackingTouch(int type) {

    }

    /**
     * 开始拖动
     * On start tracking touch.
     *
     * @param type 类型 the type
     */
    public void onStartTrackingTouch(int type) {

    }
}
