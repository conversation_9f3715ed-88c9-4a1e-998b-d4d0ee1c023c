package com.meishe.engine.editor;

import static com.meicam.sdk.NvsAssetPackageManager.TEIMPLATE_FOOTAGE_TYPE_AUDIO;

import android.graphics.Bitmap;
import android.graphics.Point;
import android.graphics.PointF;
import android.text.TextUtils;
import android.util.Log;

import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsAssetPackageManager;
import com.meicam.sdk.NvsLiveWindow;
import com.meicam.sdk.NvsLiveWindowExt;
import com.meicam.sdk.NvsObject;
import com.meicam.sdk.NvsRational;
import com.meicam.sdk.NvsSize;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsVideoResolution;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.ClipInfo;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.engine.bean.MeicamCompoundCaptionClip;
import com.meishe.engine.bean.MeicamStickerCaptionTrack;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.engine.bean.template.MeicamNvsTemplateFootageCorrespondingClipInfo;
import com.meishe.engine.bean.template.MeicamNvsTemplateFootageDesc;
import com.meishe.engine.bean.template.TemplateCaptionDesc;
import com.meishe.engine.constant.NvsConstants;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> CaoZhiChao
 * @CreateDate :2020/11/3 16:50
 * @Description :通用模板中SDK方法的管理类。详细方法请参考：https://www.meishesdk.com/android/doc_ch/html/content/index.html
 * @Description :cutsameModel to manager function of meicam sdk。Please refer to the detailed method：https://www.meishesdk.com/android/doc_ch/html/content/index.html
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class EditorController {
    public static final int ASSET_PACKAGE_TYPE_VIDEOFX = 0;
    public static final int ASSET_PACKAGE_TYPE_VIDEOTRANSITION = 1;
    public static final int ASSET_PACKAGE_TYPE_CAPTIONSTYLE = 2;
    public static final int ASSET_PACKAGE_TYPE_ANIMATEDSTICKER = 3;
    public static final int ASSET_PACKAGE_TYPE_THEME = 4;
    public static final int ASSET_PACKAGE_TYPE_CAPTURESCENE = 5;
    public static final int ASSET_PACKAGE_TYPE_ARSCENE = 6;
    public static final int ASSET_PACKAGE_TYPE_COMPOUND_CAPTION = 7;
    public static final int ASSET_PACKAGE_TYPE_CAPTION_CONTEXT = 8;
    public static final int ASSET_PACKAGE_TYPE_CAPTION_RENDERER = 9;
    public static final int ASSET_PACKAGE_TYPE_CAPTION_ANIMATION = 10;
    public static final int ASSET_PACKAGE_TYPE_CAPTION_IN_ANIMATION = 11;
    public static final int ASSET_PACKAGE_TYPE_CAPTION_OUT_ANIMATION = 12;
    public static final int ASSET_PACKAGE_TYPE_TEMPLATE = 13;
    public static final int ASSET_PACKAGE_STATUS_NOTINSTALLED = 0;
    public static final int ASSET_PACKAGE_STATUS_INSTALLING = 1;
    public static final int ASSET_PACKAGE_STATUS_READY = 2;
    public static final int ASSET_PACKAGE_STATUS_UPGRADING = 3;
    public static final int ASSET_PACKAGE_MANAGER_ERROR_NO_ERROR = 0;
    public static final int ASSET_PACKAGE_MANAGER_ERROR_NAME = 1;
    public static final int ASSET_PACKAGE_MANAGER_ERROR_ALREADY_INSTALLED = 2;
    public static final int ASSET_PACKAGE_MANAGER_ERROR_WORKING_INPROGRESS = 3;
    public static final int ASSET_PACKAGE_MANAGER_ERROR_NOT_INSTALLED = 4;
    public static final int ASSET_PACKAGE_MANAGER_ERROR_IMPROPER_STATUS = 5;
    public static final int ASSET_PACKAGE_MANAGER_ERROR_DECOMPRESSION = 6;
    public static final int ASSET_PACKAGE_MANAGER_ERROR_INVALID_PACKAGE = 7;
    public static final int ASSET_PACKAGE_MANAGER_ERROR_ASSET_TYPE = 8;
    public static final int ASSET_PACKAGE_MANAGER_ERROR_PERMISSION = 9;
    public static final int ASSET_PACKAGE_MANAGER_ERROR_META_CONTENT = 10;
    public static final int ASSET_PACKAGE_MANAGER_ERROR_SDK_VERSION = 11;
    public static final int ASSET_PACKAGE_MANAGER_ERROR_UPGRADE_VERSION = 12;
    public static final int ASSET_PACKAGE_MANAGER_ERROR_IO = 13;
    public static final int ASSET_PACKAGE_MANAGER_ERROR_RESOURCE = 14;

    private static final String TAG = "EditorController";
    private NvsStreamingContext mStreamingContext;
    private NvsStreamingContext mAuxiliaryStreamingContext;
    private MeicamTimeline mNvsTimeline;
    private MeicamTimeline mAuxiliaryTimeline;
    private int mRatio;

    private static final EditorController ourInstance = new EditorController();

    public static EditorController getInstance() {
        return ourInstance;
    }

    private EditorController() {
        mStreamingContext = getStreamingContext();
    }

    /**
     * 获取当前的时间线对象
     * get now timeline object
     *
     * @return 时间线对象 timeline object
     */
    public MeicamTimeline getNvsTimeline() {
        return mNvsTimeline;
    }

    /**
     * 设置当前的时间线对象
     * set now timeline object
     *
     * @param nvsTimeline 时间线对象 timeline object
     */
    public void setNvsTimeline(MeicamTimeline nvsTimeline) {
        mNvsTimeline = nvsTimeline;
        EditorEngine.getInstance().setCurrentTimeline(mNvsTimeline);
        EditorEngine.getInstance().checkBeautyShape();
    }

    /**
     * 设置当前的时间线对象,不赋值给EditorEngine
     * set now timeline object no EditorEngine
     *
     * @param nvsTimeline 时间线对象 timeline object
     */
    public void setMeicamTimeNoEngine(MeicamTimeline nvsTimeline) {
        mNvsTimeline = nvsTimeline;
    }

    public void removeTimeline() {
        mNvsTimeline.removeTimeline(getStreamingContext());
    }

    /**
     * 设置当前的辅助对象
     * get now timeline object
     *
     * @param nvsTimeline 时间线对象 timeline object
     */
    public void setAuxiliaryTimeline(MeicamTimeline nvsTimeline) {
        mAuxiliaryTimeline = nvsTimeline;
    }


    /**
     * Create auxiliary StreamingContext
     * <P></>
     * 创建辅助上下文
     *
     * @return NvsStreamingContext
     */
    public NvsStreamingContext createAuxiliaryStreamingContext() {
        if (mAuxiliaryStreamingContext == null) {
            mAuxiliaryStreamingContext = mStreamingContext.createAuxiliaryStreamingContext(
                    NvsStreamingContext.STREAMING_CONTEXT_FLAG_SUPPORT_4K_EDIT
                            | NvsStreamingContext.STREAMING_CONTEXT_FLAG_ENABLE_HDR_DISPLAY_WHEN_SUPPORTED
                            | NvsStreamingContext.STREAMING_CONTEXT_FLAG_INTERRUPT_STOP_FOR_INTERNAL_STOP
                            | NvsStreamingContext.STREAMING_CONTEXT_FLAG_NEED_GIF_MOTION
            );
        }
        return mAuxiliaryStreamingContext;
    }

    /**
     * Destroy auxiliary StreamingContext
     * <P></>
     * 销毁辅助上下文
     */
    public void destroyAuxiliaryStreamingContext() {
        if (mAuxiliaryStreamingContext != null) {
            if (mAuxiliaryTimeline != null) {
                mAuxiliaryTimeline.removeTimeline(mAuxiliaryStreamingContext);
            }
            mStreamingContext.destoryAuxiliaryStreamingContext(mAuxiliaryStreamingContext);
            mAuxiliaryStreamingContext = null;
        }
    }

    /**
     * 停止context引擎
     * stop context engine
     */
    public void stop() {
        if (mStreamingContext == null) {
            Log.e(TAG, "stop: mStreamingContext is null!");
            return;
        }
        mStreamingContext.stop();
    }

    /**
     * 从当前时间线位置开始播放
     * start play from now time in timeline
     */
    public void playNow() {
        if ((mStreamingContext == null) || (mNvsTimeline == null)) {
            Log.e(TAG, "playNow: mStreamingContext or mNvsTimeline is null!");
            return;
        }
        playNow(mNvsTimeline.getCurrentPosition(), -1);
    }

    /**
     * 从输入的参数时间开始播放时间线
     * start play from input param
     *
     * @param start 开始时间 start time
     */
    public void playNow2(long start) {
        if ((mStreamingContext == null) || (mNvsTimeline == null)) {
            Log.e(TAG, "playNow: mStreamingContext or mNvsTimeline is null!");
            return;
        }
        playNow(start, -1);
    }

    /**
     * 从时间线的当前时间播放到输入的结束时间
     * start play from now timeline time to the end of input param
     *
     * @param end 结束时间 end time
     */
    public void playNow(long end) {
        if ((mStreamingContext == null) || (mNvsTimeline == null)) {
            Log.e(TAG, "playNow: mStreamingContext or mNvsTimeline is null!");
            return;
        }
        playNow(mNvsTimeline.getCurrentPosition(), end);
    }

    /**
     * Now time long.
     *
     * @return 时间线的当前时间 now time of timeline
     */
    public long nowTime() {
        if ((mStreamingContext == null) || (mNvsTimeline == null)) {
            Log.e(TAG, "nowTime: mStreamingContext or mNvsTimeline is null!");
            return -1;
        }
        return mNvsTimeline.getCurrentPosition();
    }

    /**
     * 从输入的起点播放到输入的终点
     * start play from start to end
     *
     * @param start 起始时间 start time
     * @param end   结束时间 end time
     */
    public void playNow(long start, long end) {
        if ((mStreamingContext == null) || (mNvsTimeline == null)) {
            Log.e(TAG, "playNow: mStreamingContext or mNvsTimeline is null!");
            return;
        }
        if (end == -1) {
            end = mNvsTimeline.getDuration();
        }
        if ((end - start) <= 100000) {
            start = 0;
        }
        mNvsTimeline.playBack(mStreamingContext, start, end);
    }

    /**
     * 时间线当前位置移动到输入位置，同时刷新预览画面
     * seek timeline time to timestamp and refresh liveWindow frame
     *
     * @param timestamp    新的时间线位置  new timeline position
     * @param seekShowMode 界面刷新的flag,默认是0  the flag to refresh liveWindow frame，The default is 0。
     */
    public void seekTimeline(long timestamp, int seekShowMode) {
        if (mNvsTimeline == null) {
            LogUtils.e("timeline is null!");
            return;
        }
        mNvsTimeline.seekTimeline(mStreamingContext, timestamp, seekShowMode);
    }

    /**
     * 时间线当前位置移动到输入位置，同时刷新预览画面
     * seek timeline time to timestamp and refresh liveWindow frame
     *
     * @param timestamp 新的时间线位置  new timeline position
     */
    public void seekTimeline(long timestamp) {
        seekTimeline(timestamp, 0);
    }

    /**
     * 刷新当前预览画面
     * refresh liveWindow frame of now position
     */
    public void seekTimeline() {
        if (mNvsTimeline == null) {
            LogUtils.e("timeline is null!");
            return;
        }
        seekTimeline(mNvsTimeline.getCurrentPosition(), 0);
    }

    /**
     * Gets current engine state.
     *
     * @return 当前引擎状态 now context engine state
     */
    public int getCurrentEngineState() {
        if (mStreamingContext == null) {
            LogUtils.e("StreamingContext is null!");
            return -1;
        }
        return mStreamingContext.getStreamingEngineState();
    }

    /**
     * Gets streaming context.
     *
     * @return 获取context对象 get Streaming Context
     */
    public NvsStreamingContext getStreamingContext() {
        if (mStreamingContext == null) {
            synchronized (NvsStreamingContext.class) {
                if (mStreamingContext == null) {
                    mStreamingContext = NvsStreamingContext.getInstance();
                }
            }
        }
        return mStreamingContext;
    }

    /**
     * 链接时间线到liveWindow上，同时设置相应的接口
     * Connect timeline with live window.Set the interface at the same time
     *
     * @param mLiveWindow the live window
     */
    public void connectTimelineWithLiveWindow(Object mLiveWindow) {
        if (mStreamingContext == null || mNvsTimeline == null || mLiveWindow == null) {
            return;
        }

        if (mLiveWindow instanceof NvsLiveWindow) {
            NvsLiveWindow liveWindow = (NvsLiveWindow) mLiveWindow;
            mNvsTimeline.connectToLiveWindow(mStreamingContext, liveWindow);
        } else if (mLiveWindow instanceof NvsLiveWindowExt) {
            NvsLiveWindowExt liveWindow = (NvsLiveWindowExt) mLiveWindow;
            mNvsTimeline.connectToLiveWindow(mStreamingContext, liveWindow);
        }
    }

    /**
     * 根据引擎状态判断当前是否是处于播放状态。
     * Determine whether the current state is playing based on the engine state.
     *
     * @return the boolean
     */
    public boolean isPlaying() {
        if (mStreamingContext == null) {
            Log.e(TAG, "isPlaying: mStreamingContext is null!");
            return false;
        }
        int state = mStreamingContext.getStreamingEngineState();
        return state == NvsStreamingContext.STREAMING_ENGINE_STATE_PLAYBACK;
    }

    /**
     * 获取时间线的长度
     * Get timeline duration.
     *
     * @return the timeline duration
     */
    public long getTimelineDuration() {
        if (mNvsTimeline == null) {
            Log.e(TAG, "getTimelineDuration: mNvsTimeline is null!");
            return 0;
        }
        return mNvsTimeline.getDuration();
    }

    /**
     * 获取时间线的帧率
     * Get timeline video fps.
     *
     * @return the NvsRational of timeline video fps
     */
    public NvsRational getTimelineFps() {
        if (mNvsTimeline == null) {
            Log.e(TAG, "mNvsTimeline is null!");
            return new NvsRational(30, 1);
        }
        return mNvsTimeline.getVideoFps();
    }

    /**
     * 根据视频路径获取视频的长度
     * Gets video duration by filePath
     *
     * @param videoPath the video path
     * @return the video duration
     */
    public long getVideoDuration(String videoPath) {
        if (mStreamingContext == null) {
            Log.e(TAG, "getVideoDuration: mStreamingContext is null!");
            return -1;
        }
        NvsAVFileInfo fileInfo = mStreamingContext.getAVFileInfo(videoPath);
        if (fileInfo == null) {
            return -1;
        }
        return fileInfo.getDuration();
    }

    /**
     * 缩略图控件中，时间线时间转化为对应的长度
     * In thumbnail view, the timeline time convert to the view length
     *
     * @param duration             the duration
     * @param mPixelPerMicrosecond 比例尺。the m pixel per microsecond
     * @return the int
     */
    public int durationToLength(long duration, double mPixelPerMicrosecond) {
        return (int) Math.floor(duration * mPixelPerMicrosecond + 0.5D);
    }

    /**
     * 缩略图控件中，view中的长度转化为时间线时间
     * In thumbnail view, the view length convert to the timeline time
     *
     * @param dx                   the dx
     * @param mPixelPerMicrosecond 比例尺。the m pixel per microsecond
     * @return the long
     */
    public long lengthToDuration(int dx, double mPixelPerMicrosecond) {
        return (long) Math.floor(dx / mPixelPerMicrosecond + 0.5D);
    }

    /**
     * 根据输入时间捕捉当前画面并转化为bitmap，默认图片比例为1:1
     * Grab image from timeline async.Default image scale 1:1
     *
     * @param timestamp 输入的时间线时间。the timeline position
     */
    public void grabImageFromTimelineAsync(long timestamp) {
        if (mNvsTimeline != null) {
            mNvsTimeline.grabImageFromTimeline(mStreamingContext, timestamp, new NvsRational(1, 1));
        }
    }

    /**
     * 根据输入时间捕捉当前画面并转化为bitmap
     * Grab image from timeline async.
     *
     * @param timestamp  输入的时间线时间。the timeline position
     * @param proxyScale 需要的图片比例。the proxy scale
     * @param flags      自定义的flag。 the flags
     */
    public void grabImageFromTimelineAsync(long timestamp, NvsRational proxyScale, int flags) {
        if (mNvsTimeline != null) {
            mNvsTimeline.grabImageFromTimelineAsync(mStreamingContext, timestamp, proxyScale, flags);
        }
    }

    /**
     * 同步的捕捉当前时间线位置的画面
     * Grab bitmap from timeline bitmap.
     *
     * @param timestamp 输入的时间线时间。the timeline position
     * @return the bitmap
     */
    public Bitmap grabBitmapFromTimeline(long timestamp) {
        return grabBitmapFromTimeline(timestamp, new NvsRational(1, 1));
    }

    /**
     * 异步的捕捉当前时间线位置的画面
     * Grab bitmap from auxiliary timeline bitmap Async.
     *
     * @param timestamp 输入的时间线时间。the timeline position
     * @param callback  the callback
     */
    public void grabBitmapFromAuxiliaryTimelineAsync(long timestamp, NvsStreamingContext.ImageGrabberCallback callback) {
        if (mAuxiliaryStreamingContext == null || mAuxiliaryTimeline == null) {
            return;
        }
        int flag = 0;
        if (EditorEngine.getInstance().isUseFaceShape()) {
            flag |= NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_BUDDY_HOST_VIDEO_FRAME;
        }
        mAuxiliaryStreamingContext.setImageGrabberCallback(callback);
        mAuxiliaryTimeline.grabImageFromTimelineAsync(mAuxiliaryStreamingContext, timestamp, new NvsRational(1, 1), flag);
    }

    /**
     * 同步的捕捉当前时间线位置的画面
     * Grab bitmap from timeline bitmap.
     *
     * @param timestamp  输入的时间线时间。the timeline position
     * @param proxyScale 需要的图片比例。the proxy scale
     * @return the bitmap
     */
    public Bitmap grabBitmapFromTimeline(long timestamp, NvsRational proxyScale) {
        int flag = 0;
        if (EditorEngine.getInstance().isUseFaceShape()) {
            flag = NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_BUDDY_HOST_VIDEO_FRAME;
        }
        if (mNvsTimeline == null) {
            LogUtils.e("timeline is null!");
            return null;
        }
        return mNvsTimeline.grabImageFromTimeline(mStreamingContext, timestamp, proxyScale, flag);
    }

    /**
     * 自定义生成时间线。
     * Compile time line custom.
     *
     * @param path         生成路径。the path
     * @param videoHeight  自定义的视频高度。the video height
     * @param mParamsTable 自定义参数。the params table
     * @return is success or not true：yes; false:no
     */
    public boolean compileTimeLineCustom(String path, int videoHeight, Hashtable<String, Object> mParamsTable) {
        if (mNvsTimeline == null) {
            LogUtils.e("timeline is null!");
            return false;
        }
        return mNvsTimeline.compileTimeline(mStreamingContext, 0, mNvsTimeline.getDuration(), path, NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_CUSTOM
                , videoHeight, NvsStreamingContext.COMPILE_BITRATE_GRADE_HIGH, NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_DISABLE_ALIGN_VIDEO_SIZE
                        | NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_BUDDY_HOST_VIDEO_FRAME
                        | NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_IGNORE_TIMELINE_VIDEO_SIZE, mParamsTable);

    }

    /**
     * 清理生成时的自定义配置
     * Clear compile configurations.
     */
    public void clearCompileConfigurations() {
        if (mStreamingContext != null) {
            mStreamingContext.setCompileConfigurations(null);
        }
    }

    /**
     * 异步安装包的方法
     * Install asset packaged no synchronous.
     *
     * @param path          the path
     * @param type          the type
     * @param stringBuilder the string builder
     */
    public void installAssetPackagedNoSynchronous(String path, String licPath, int type, StringBuilder stringBuilder) {
        if (mStreamingContext == null) {
            Log.e(TAG, "installAssetPackaged: mStreamingContext is null!");
            return;
        }
        mStreamingContext.getAssetPackageManager().installAssetPackage(path, licPath, type, false, stringBuilder);
    }

    /**
     * 卸载包
     * Uninstall asset package.
     *
     * @param packageId 包id。the package id
     * @param type      the type
     */
    public void uninstallAssetPackage(String packageId, int type) {
        if (mStreamingContext == null) {
            Log.e(TAG, "uninstallAssetPackage: mStreamingContext is null!");
            return;
        }
        int state = mStreamingContext.getAssetPackageManager().uninstallAssetPackage(packageId, type);
        Log.d(TAG, "uninstallAssetPackage: " + state);
    }


    /**
     * 同步安装包的方法
     * Install asset packaged synchronous int.
     *
     * @param path          the path
     * @param type          the type
     * @param stringBuilder the string builder
     * @return the int
     */
    public int installAssetPackagedSynchronous(String path, String licPath, int type, StringBuilder stringBuilder) {
        if (mStreamingContext == null) {
            Log.e(TAG, "installAssetPackaged: mStreamingContext is null!");
            return ASSET_PACKAGE_MANAGER_ERROR_RESOURCE;
        }
        return mStreamingContext.getAssetPackageManager().installAssetPackage(path, licPath, type, true, stringBuilder);
    }

    /**
     * 获取模板中全部的footage信息
     * Gets template footage desc.
     *
     * @param uuidString 模板的uuid。the uuid string
     * @return the template footage desc
     */
    public List<NvsAssetPackageManager.NvsTemplateFootageDesc> getTemplateFootageDesc(String uuidString) {
        List<NvsAssetPackageManager.NvsTemplateFootageDesc> nvsTemplateFootageDescs = new ArrayList<>();
        if (mStreamingContext == null) {
            Log.e(TAG, "getTemplateFootageDesc: mStreamingContext is null!");
            return nvsTemplateFootageDescs;
        }
        nvsTemplateFootageDescs = mStreamingContext.getAssetPackageManager().getTemplateFootages(uuidString);
        return nvsTemplateFootageDescs;
    }

    /**
     * 获取模板中视频的footage信息
     * Gets template footage desc video.
     *
     * @param uuidString 模板的uuid 。the uuid string
     * @return the template footage desc video
     */
    public List<MeicamNvsTemplateFootageDesc> getTemplateFootageDescVideo(String uuidString) {
//       原方案，没有序列嵌套的时候
//        List<NvsAssetPackageManager.NvsTemplateFootageDesc> nvsTemplateFootageDescs = new ArrayList<>();
//
//        if (mStreamingContext == null) {
//            Log.e(TAG, "getTemplateFootageDesc: mStreamingContext is null!");
//
//
//            return nvsTemplateFootageDescs;
//        }
//        List<NvsAssetPackageManager.NvsTemplateFootageDesc> nvsTemplateFootageDescList = mStreamingContext.getAssetPackageManager().getTemplateFootages(uuidString);
//        for (NvsAssetPackageManager.NvsTemplateFootageDesc nvsTemplateFootageDesc : nvsTemplateFootageDescList) {
//            //3代表音频
//            if (nvsTemplateFootageDesc.type != NvsAssetPackageManager.TEIMPLATE_FOOTAGE_TYPE_AUDIO) {
//                nvsTemplateFootageDescs.add(nvsTemplateFootageDesc);
//            }
//        }
//        return nvsTemplateFootageDescs;

        List<MeicamNvsTemplateFootageDesc> nvsTemplateFootageDescs = new ArrayList<>();
        if (mStreamingContext == null) {
            Log.e(TAG, "getTemplateFootageDesc: mStreamingContext is null!");
            return nvsTemplateFootageDescs;
        }

        List<NvsAssetPackageManager.NvsTemplateFootageDesc> nvsTemplateFootageDescList = mStreamingContext.getAssetPackageManager().getTemplateFootages(uuidString);
        List<MeicamNvsTemplateFootageDesc> meicamNvsTemplateFootageDescList = new ArrayList<>();
        for (NvsAssetPackageManager.NvsTemplateFootageDesc desc : nvsTemplateFootageDescList) {
            MeicamNvsTemplateFootageDesc meicamNvsTemplateFootageDesc = new MeicamNvsTemplateFootageDesc();
            meicamNvsTemplateFootageDesc.setNvsTemplateFootageDesc(desc);
            meicamNvsTemplateFootageDescList.add(meicamNvsTemplateFootageDesc);
        }

        for (MeicamNvsTemplateFootageDesc nvsTemplateFootageDesc : meicamNvsTemplateFootageDescList) {
            if (needDealFootageDesc(nvsTemplateFootageDesc)) {
                getAllNvsTemplateFootageDescs(nvsTemplateFootageDescs, nvsTemplateFootageDesc);
            }
        }
        return nvsTemplateFootageDescs;
    }


    public void handleTemplateFootageCorrespondingClipInfo(MeicamNvsTemplateFootageCorrespondingClipInfo item) {
        List<Integer> clipList = item.getClipIndexInTimelineList();
        List<Integer> trackList = item.getClipTrackIndexInTimelineList();
        long clipInPoint = 0;
        MeicamVideoClip meicamVideoClip;
        MeicamTimeline meicamTimeline = mNvsTimeline;
        if (meicamTimeline == null) {
            meicamTimeline = getNvsTimeline();
        }
        for (int i = 0; i < trackList.size(); i++) {
            int trackIndexTimeline = trackList.get(i) - meicamTimeline.getRealMainTrackIndex();
            int clipIndexTimeline = clipList.get(i);
            MeicamVideoTrack meicamVideoTrack = meicamTimeline.getVideoTrack(trackIndexTimeline);
            if (meicamVideoTrack != null) {
                meicamVideoClip = meicamVideoTrack.getVideoClip(clipIndexTimeline);
                if (meicamVideoClip != null) {
                    clipInPoint += meicamVideoClip.getInPoint();
                    meicamTimeline = meicamVideoClip.getInternalTimeline();
                }
            }
        }
        if (meicamTimeline != null) {
            MeicamVideoTrack meicamVideoTrack = meicamTimeline.getVideoTrack(item.trackIndex - meicamTimeline.getRealMainTrackIndex());
            if (meicamVideoTrack != null) {
                MeicamVideoClip videoClip = meicamVideoTrack.getVideoClip(item.clipIndex);
                if (videoClip != null) {
                    if (videoClip.getInternalTimeline() == null) {
                        String bestSeekTime = videoClip.getTemplateAttachment(NvsObject.TEMPLATE_KEY_BEST_SEEK_TIME);
                        if (Long.parseLong(bestSeekTime) < 0) {
                            item.setRealInpoint(clipInPoint + videoClip.getInPoint());
                        } else {
                            item.setRealInpoint(Long.parseLong(bestSeekTime));
                        }
                    }
                }
            }
        }
    }

    /**
     * 递归获取所有的NvsTemplateFootage
     * Gets all nvs template footage descs.
     *
     * @param nvsTemplateFootageDescs the nvs template footage
     * @param nvsTemplateFootageDesc  the nvs template footage desc
     */


    private void getAllNvsTemplateFootageDescs(List<MeicamNvsTemplateFootageDesc> nvsTemplateFootageDescs,
                                               MeicamNvsTemplateFootageDesc nvsTemplateFootageDesc) {


        if (nvsTemplateFootageDesc.timelineClipFootages != null && nvsTemplateFootageDesc.timelineClipFootages.size() != 0) {
            //保存中间的trackIndex和clipIndex
            //Save the intermediate trackIndex and clipIndex
            nvsTemplateFootageDesc.addClipTrackIndexInTimeline(nvsTemplateFootageDesc.correspondingClipInfos.get(0).trackIndex);
            nvsTemplateFootageDesc.addClipIndexInTimeline(nvsTemplateFootageDesc.correspondingClipInfos.get(0).clipIndex);

            List<MeicamNvsTemplateFootageDesc> meicamNvsTemplateFootageDescList = new ArrayList<>();
            for (NvsAssetPackageManager.NvsTemplateFootageDesc desc : nvsTemplateFootageDesc.timelineClipFootages) {
                MeicamNvsTemplateFootageDesc meicamNvsTemplateFootageDesc = new MeicamNvsTemplateFootageDesc();
                meicamNvsTemplateFootageDesc.setNvsTemplateFootageDesc(desc);
                meicamNvsTemplateFootageDescList.add(meicamNvsTemplateFootageDesc);
            }
            for (int i = 0; i < meicamNvsTemplateFootageDescList.size(); i++) {
                MeicamNvsTemplateFootageDesc timelineTemplateFootageDesc = meicamNvsTemplateFootageDescList.get(i);
                if (needDealFootageDesc(timelineTemplateFootageDesc)) {
                    timelineTemplateFootageDesc.setClipIndexInTimelineList(nvsTemplateFootageDesc.getClipIndexInTimelineList());
                    timelineTemplateFootageDesc.setClipTrackIndexInTimelineList(nvsTemplateFootageDesc.getClipTrackIndexInTimelineList());
                    getAllNvsTemplateFootageDescs(nvsTemplateFootageDescs, timelineTemplateFootageDesc);
                }
            }

        } else {
            if (needDealFootageDesc(nvsTemplateFootageDesc)) {
                nvsTemplateFootageDescs.add(nvsTemplateFootageDesc);
            }
        }
    }

    /**
     * 是否需要处理该FootageDesc
     * Need deal footage desc boolean.
     *
     * @param nvsTemplateFootageDesc the nvs template footage desc
     * @return the boolean
     */
    private boolean needDealFootageDesc(NvsAssetPackageManager.NvsTemplateFootageDesc nvsTemplateFootageDesc) {
        return nvsTemplateFootageDesc.type != NvsAssetPackageManager.TEIMPLATE_FOOTAGE_TYPE_AUDIO;
    }


    /**
     * 获取所有视频片段
     * Gets template video clip.
     *
     * @param uuidString the uuid string
     * @return the template video clip
     */
    public List<MeicamNvsTemplateFootageCorrespondingClipInfo> getTemplateVideoClip(String uuidString) {
        List<MeicamNvsTemplateFootageCorrespondingClipInfo> mClipList = new ArrayList<>();
        List<MeicamNvsTemplateFootageDesc> templateList = EditorController.getInstance().getTemplateFootageDescVideo(uuidString);
        if (templateList != null) {
            for (MeicamNvsTemplateFootageDesc footage : templateList) {
                if (footage.type == TEIMPLATE_FOOTAGE_TYPE_AUDIO) {
                    //audio类型无用。
                    //Audio is not used.
                    continue;
                }
                List<MeicamNvsTemplateFootageCorrespondingClipInfo> correspondingClipInfos = new ArrayList<>();
                for (NvsAssetPackageManager.NvsTemplateFootageCorrespondingClipInfo clipInfo : footage.correspondingClipInfos) {
                    MeicamNvsTemplateFootageCorrespondingClipInfo correspondingClipInfo = new MeicamNvsTemplateFootageCorrespondingClipInfo();
                    correspondingClipInfo.setNvsTemplateFootageCorrespondingClipInfo(clipInfo);
                    correspondingClipInfos.add(correspondingClipInfo);
                }
                for (MeicamNvsTemplateFootageCorrespondingClipInfo info : correspondingClipInfos) {
                    info.setClipTrackIndexInTimelineList(footage.getClipTrackIndexInTimelineList());
                    info.setClipIndexInTimelineList(footage.getClipIndexInTimelineList());
                    if (!EditorController.getInstance().isPlaceHolder(info.trackIndex, info.clipIndex)) {
                        info.canReplace = footage.canReplace;
                        info.setFootageID(footage.id);
                        mClipList.add(info);
                    }
                }
            }

            Collections.sort(mClipList, new Comparator<MeicamNvsTemplateFootageCorrespondingClipInfo>() {
                @Override
                public int compare(MeicamNvsTemplateFootageCorrespondingClipInfo o1, MeicamNvsTemplateFootageCorrespondingClipInfo o2) {
                    int i = (int) (o1.inpoint - o2.inpoint);
                    if (i == 0) {
                        return o1.trackIndex - o2.trackIndex;
                    }
                    return i;
                }
            });
            int groupIndex = -1;
            //适配AE模板，再计算一下编组信息
            //Adapt the AE template and calculate the grouping information again.
            Map<String, List<MeicamNvsTemplateFootageCorrespondingClipInfo>> map = new HashMap<>();
            String key;
            List<MeicamNvsTemplateFootageCorrespondingClipInfo> tempList;
            for (MeicamNvsTemplateFootageCorrespondingClipInfo templateClip : mClipList) {
                key = templateClip.getFootageID();
                tempList = map.get(key);
                if (null == tempList) {
                    tempList = new ArrayList<>();
                    map.put(key, tempList);
                }
                tempList.add(templateClip);
            }

            for (List<MeicamNvsTemplateFootageCorrespondingClipInfo> templateClips : map.values()) {
                if (templateClips.size() > 1) {
                    //有同一编组
                    //With the same group.
                    groupIndex += 1;
                    for (MeicamNvsTemplateFootageCorrespondingClipInfo templateClip : templateClips) {
                        templateClip.setGroupIndex(groupIndex);
                    }
                }
            }
            return mClipList;
        }
        return mClipList;
    }


    /**
     * 获取模板中的字幕信息。
     * Gets template captions.
     *
     * @param uuidString 模板的uuid。the uuid string
     * @return the template captions
     */
    public List<TemplateCaptionDesc> getTemplateCaptions(String uuidString) {
        List<TemplateCaptionDesc> templateCaptionDescs = new ArrayList<>();
        if (mStreamingContext == null) {
            Log.e(TAG, "getTemplateFootageDesc: mStreamingContext is null!");
            return templateCaptionDescs;
        }
        List<TemplateCaptionDesc> templateCaptionDescArrayList = new ArrayList<>();
        List<NvsAssetPackageManager.NvsTemplateCaptionDesc> nvsTemplateCaptionDescs = mStreamingContext.getAssetPackageManager().getTemplateCaptions(uuidString);
        for (NvsAssetPackageManager.NvsTemplateCaptionDesc nvsTemplateCaptionDesc : nvsTemplateCaptionDescs) {
            templateCaptionDescArrayList.add(TemplateCaptionDesc.create(nvsTemplateCaptionDesc));
        }

        for (TemplateCaptionDesc templateCaptionDesc : templateCaptionDescArrayList) {
            getAllNvsTemplateCaptionDescs(templateCaptionDescs, templateCaptionDesc);
        }
        return templateCaptionDescs;
    }

    private void getAllNvsTemplateCaptionDescs(List<TemplateCaptionDesc> templateCaptionDescs,
                                               TemplateCaptionDesc nvsTemplateCaptionDesc) {

        if (nvsTemplateCaptionDesc != null && nvsTemplateCaptionDesc.subCaptions.size() != 0) {
            //保存中间的trackIndex和clipIndex
            //Save the intermediate trackIndex and clipIndex.
            nvsTemplateCaptionDesc.addClipTrackIndexInTimeline(nvsTemplateCaptionDesc.trackIndex);
            nvsTemplateCaptionDesc.addClipIndexInTimeline(nvsTemplateCaptionDesc.clipIndex);
            for (TemplateCaptionDesc timelineTemplateFootageDesc : nvsTemplateCaptionDesc.subCaptions) {
                timelineTemplateFootageDesc.setClipIndexInTimelineList(nvsTemplateCaptionDesc.getClipIndexInTimelineList());
                timelineTemplateFootageDesc.setClipTrackIndexInTimelineList(nvsTemplateCaptionDesc.getClipTrackIndexInTimelineList());
                getAllNvsTemplateCaptionDescs(templateCaptionDescs, timelineTemplateFootageDesc);
            }
        } else {
            if (nvsTemplateCaptionDesc != null && !TextUtils.isEmpty(nvsTemplateCaptionDesc.replaceId)) {
                templateCaptionDescs.add(nvsTemplateCaptionDesc);
            }
        }
    }

    /**
     * 获取模板中的复合字幕信息。
     * Gets template captions.
     *
     * @param uuidString 模板的uuid。the uuid string
     * @return the template compound captions
     */
    public List<TemplateCaptionDesc> getTemplateCompoundCaptions(String uuidString) {
        List<TemplateCaptionDesc> templateCompoundCaptionDescs = new ArrayList<>();
        if (mStreamingContext == null) {
            Log.e(TAG, "getTemplateCompoundCaptions: mStreamingContext is null!");
            return templateCompoundCaptionDescs;
        }
        List<TemplateCaptionDesc> templateCompoundCaptionDescArrayList = new ArrayList<>();
        List<NvsAssetPackageManager.NvsTemplateCompoundCaptionDesc> nvsTemplateCaptionDescs =
                mStreamingContext.getAssetPackageManager().getTemplateCompoundCaptions(uuidString);
        for (NvsAssetPackageManager.NvsTemplateCompoundCaptionDesc nvsTemplateCaptionDesc : nvsTemplateCaptionDescs) {
            templateCompoundCaptionDescArrayList.add(TemplateCaptionDesc.create(nvsTemplateCaptionDesc));
        }

        for (TemplateCaptionDesc templateCaptionDesc : templateCompoundCaptionDescArrayList) {
            getAllNvsTemplateCaptionDescs(templateCompoundCaptionDescs, templateCaptionDesc);
        }
        return templateCompoundCaptionDescs;
    }

    /**
     * 根据模板uuid创建时间线。
     * Create timeline nvs timeline.
     *
     * @param templateId       安装返回的值
     * @param templateFootAges key:NvsTemplateFootageDesc里记录的id；value:添加clip的文件路径
     * @return nvs timeline
     */
    public MeicamTimeline createTimeline(String templateId, List<NvsStreamingContext.templateFootageInfo> templateFootAges) {
        if (mStreamingContext == null) {
            Log.e(TAG, "createTimeline: mStreamingContext is null!");
            return null;
        }
        return createTimeline(mStreamingContext, templateId, templateFootAges);
    }

    /**
     * 根据模板uuid创建时间线。
     * Create timeline nvs timeline.
     *
     * @param context          NvsStreaming 上下文
     * @param templateId       安装返回的值
     * @param templateFootAges key:NvsTemplateFootageDesc里记录的id；value:添加clip的文件路径
     * @return nvs timeline
     */
    public MeicamTimeline createTimeline(NvsStreamingContext context, String templateId, List<NvsStreamingContext.templateFootageInfo> templateFootAges) {
        if (context == null) {
            Log.e(TAG, "createTimeline: mStreamingContext is null!");
            return null;
        }

        return new MeicamTimeline.TimelineBuilder(context, MeicamTimeline.TimelineBuilder.BUILD_FORM_TEMPLATE)
                .setTemplateId(templateId)
                .setTemplateFootageInfo(templateFootAges)
                .build();
    }


    /**
     * 根据轨道和片段index获取对应的对象
     * Gets video clip by index.
     *
     * @param trackIndex the track index
     * @param clipIndex  the clip index
     * @return the video clip by index
     */
    public MeicamVideoClip getVideoClipByIndex(int trackIndex, int clipIndex) {
        if (mNvsTimeline == null) {
            Log.e(TAG, "getVideoClipByIndex: mNvsTimeline is null!");
            return null;
        }
        int trackCount = mNvsTimeline.videoTrackCount();
        if (trackIndex >= trackCount) {
            Log.e(TAG, "getVideoClipByIndex: trackIndex is to BIG! trackIndex: " + trackIndex + "  trackCount: " + trackCount);
            return null;
        }
        MeicamVideoTrack nvsVideoTrack = mNvsTimeline.getVideoTrack(trackIndex);
        int clipCount = nvsVideoTrack.getClipCount();
        if (clipIndex >= clipCount) {
            Log.e(TAG, "getVideoClipByIndex: clipIndex is to BIG! clipIndex: " + clipIndex + "  clipCount: " + clipCount);
            return null;
        }
        return nvsVideoTrack.getVideoClip(clipIndex);
    }

    /**
     * 是否是占位clip
     * Whether is the clip placeholder.
     *
     * @param trackIndex the track index
     * @param clipIndex  the clip index
     * @return Whether is the clip placeholder.true:yes:false:no.
     */
    public boolean isPlaceHolder(int trackIndex, int clipIndex) {
        if (mNvsTimeline == null) {
            LogUtils.e("timeline is null!");
            return false;
        }
        if (trackIndex != 0) {
            return false;
        }
        MeicamVideoTrack videoTrack = mNvsTimeline.getVideoTrack(0);
        if (videoTrack == null) {
            return false;
        }
        int clipCount = videoTrack.getClipCount();
        if (clipIndex != clipCount - 1) {
            return false;
        }
        MeicamVideoClip videoClip = videoTrack.getVideoClip(clipIndex);
        return videoClip != null && videoClip.getFilePath() != null && videoClip.getFilePath().endsWith(CommonData.IMAGE_BLACK_FILE_NAME);
    }


    /**
     * 获取文件的宽高
     * Get AV file size
     *
     * @param path 文件路径 file path
     * @return 文件宽高尺寸 size of width and height。
     */
    public static int[] getAVFileSize(String path) {
        if (TextUtils.isEmpty(path)) {
            LogUtils.e("path is null!");
            return null;
        }
        NvsAVFileInfo avFileInfo = NvsStreamingContext.getInstance().getAVFileInfo(path);
        if (avFileInfo == null) {
            LogUtils.e("avFileInfo is null!");
            return null;
        }
        int videoStreamRotation = avFileInfo.getVideoStreamRotation(0);
        NvsSize videoStreamDimension = avFileInfo.getVideoStreamDimension(0);
        int[] size = new int[2];
        if (videoStreamRotation % 2 == 0) {
            size[0] = videoStreamDimension.width;
            size[1] = videoStreamDimension.height;
        } else {
            size[1] = videoStreamDimension.width;
            size[0] = videoStreamDimension.height;
        }
        return size;
    }

    private static MeicamCaptionClip loadTimelineCaptionToTemplate(MeicamTimeline
                                                                           timeline, String replaceId) {
        int stickerCaptionTrackCount = timeline.getStickerCaptionTrackCount();
        for (int index = 0; index < stickerCaptionTrackCount; index++) {
            MeicamStickerCaptionTrack stickerCaptionTrack = timeline.findStickCaptionTrack(index);
            if (stickerCaptionTrack == null) {
                continue;
            }
            int clipCount = stickerCaptionTrack.getClipCount();
            for (int i = 0; i < clipCount; i++) {
                ClipInfo<?> captionStickerClip = stickerCaptionTrack.getCaptionStickerClip(i);
                if (captionStickerClip instanceof MeicamCaptionClip) {
                    MeicamCaptionClip meicamCaptionClip = (MeicamCaptionClip) captionStickerClip;
                    String id = meicamCaptionClip.getTemplateAttachment(NvsObject.TEMPLATE_KEY_REPLACE_ID);
                    if (TextUtils.equals(id, replaceId)) {
                        return meicamCaptionClip;
                    }
                }
            }
        }
        return null;
    }


    private static MeicamCompoundCaptionClip loadTimelineCompoundCaptionToTemplate(MeicamTimeline
                                                                                           timeline, String replaceId) {
        int stickerCaptionTrackCount = timeline.getStickerCaptionTrackCount();
        for (int index = 0; index < stickerCaptionTrackCount; index++) {
            MeicamStickerCaptionTrack stickerCaptionTrack = timeline.findStickCaptionTrack(index);
            if (stickerCaptionTrack == null) {
                continue;
            }
            int clipCount = stickerCaptionTrack.getClipCount();
            for (int i = 0; i < clipCount; i++) {
                ClipInfo<?> captionStickerClip = stickerCaptionTrack.getCaptionStickerClip(i);
                if (captionStickerClip instanceof MeicamCompoundCaptionClip) {
                    MeicamCompoundCaptionClip meicamCaptionClip = (MeicamCompoundCaptionClip) captionStickerClip;
                    String id = meicamCaptionClip.getTemplateAttachment(NvsObject.TEMPLATE_KEY_REPLACE_ID);
                    if (TextUtils.equals(id, replaceId)) {
                        return meicamCaptionClip;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 更改时间线的画幅
     * Change video size.
     *
     * @param width  宽是4的倍数。the width
     * @param height 高是2的倍数。the height
     */
    public void changeVideoSize(int width, int height) {
        if (mNvsTimeline == null) {
            Log.e(TAG, "changeVideoSize: mNvsTimeline is null!");
            return;
        }
        mNvsTimeline.changeVideoSize(width, height);
    }

    /**
     * 获取时间线的宽高
     * Gets timeline width and height.
     *
     * @return the timeline width and height
     */
    public Point getTimelineWidthAndHeight() {
        if (mNvsTimeline == null) {
            Log.e(TAG, "getTimelineWidthAndHeight: mNvsTimeline is null!");
            return new Point(0, 0);
        }
        NvsVideoResolution nvsVideoResolution = mNvsTimeline.getVideoResolution();
        return new Point(nvsVideoResolution.imageWidth, nvsVideoResolution.imageHeight);
    }

    public void changeTemplateAspectRatio(String templateId, int ratio) {
        mRatio = ratio;
        LogUtils.d("mRatio===" + mRatio);
        if (mStreamingContext == null) {
            Log.e(TAG, "changeTemplateAspectRatio: mStreamingContext is null!");
            return;
        }
        mStreamingContext.getAssetPackageManager().changeTemplateAspectRatio(templateId, ratio);
    }

    public int getAssetPackageSupportedAspectRatio(String assetPackageId, int type) {
        if (mStreamingContext == null) {
            Log.e(TAG, "getAssetPackageSupportedAspectRatio: mStreamingContext is null!");
            return -1;
        }
        return mStreamingContext.getAssetPackageManager().getAssetPackageSupportedAspectRatio(assetPackageId, type);
    }

    public NvsAVFileInfo getFileInfo(String path) {
        if (mStreamingContext == null) {
            Log.e(TAG, "getFileInfo: mStreamingContext is null!");
            return null;
        }
        return mStreamingContext.getAVFileInfo(path);
    }

    /**
     * 获取导出自定义高度
     *
     * @param resolution 分辨率
     * @return height
     */
    public int getCustomHeight(int resolution) {
        int height;
        if (mRatio == 0) {
            MeicamTimeline nvsTimeline = getNvsTimeline();
            if (nvsTimeline == null) {
                LogUtils.e("timeline is null!");
                return 720;
            }
            NvsVideoResolution videoResolution = nvsTimeline.getVideoResolution();
            int widthImage = videoResolution.imageWidth;
            int heightImage = videoResolution.imageHeight;
            height = (widthImage > heightImage ? resolution : (resolution / widthImage * heightImage));
            return height;
        }
        if (mRatio == NvsConstants.AspectRatio.AspectRatio_16v9) {
            height = resolution;
        } else if (mRatio == NvsConstants.AspectRatio.AspectRatio_1v1) {
            height = resolution;
        } else if (mRatio == NvsConstants.AspectRatio.AspectRatio_9v16) {
            height = (int) (resolution / 9.0 * 16);
        } else if (mRatio == NvsConstants.AspectRatio.AspectRatio_3v4) {
            height = (int) (resolution / 3.0 * 4.0);
        } else if (mRatio == NvsConstants.AspectRatio.AspectRatio_4v3) {
            height = resolution;
        } else if (mRatio == NvsConstants.AspectRatio.AspectRatio_18v9) {
            height = resolution;
        } else if (mRatio == NvsConstants.AspectRatio.AspectRatio_9v18) {
            height = (int) (resolution / 9.0 * 18);
        } else if (mRatio == NvsConstants.AspectRatio.AspectRatio_21v9) {
            height = resolution;
        } else if (mRatio == NvsConstants.AspectRatio.AspectRatio_9v21) {
            height = (int) (resolution / 9.0 * 21);
        } else {
            if (mNvsTimeline == null) {
                LogUtils.e("timeline is null!");
                return 720;
            }
            NvsVideoResolution videoResolution = mNvsTimeline.getVideoResolution();
            if (videoResolution != null) {
                height = videoResolution.imageWidth > videoResolution.imageHeight ? 720 : 1280;
            } else {
                height = 720;
            }
        }
        return height;
    }

    public MeicamVideoClip getVideoClipByTemplateFootageCorrespondingClipInfo(MeicamNvsTemplateFootageCorrespondingClipInfo item) {
        List<Integer> clipList = item.getClipIndexInTimelineList();
        List<Integer> trackList = item.getClipTrackIndexInTimelineList();
        long clipInPoint = 0;
        MeicamVideoClip meicamVideoClip;
        MeicamTimeline meicamTimeline = mNvsTimeline;
        if (meicamTimeline == null) {
            meicamTimeline = getNvsTimeline();
        }
        for (int i = 0; i < trackList.size(); i++) {
            int trackIndexTimeline = trackList.get(i) - meicamTimeline.getRealMainTrackIndex();
            int clipIndexTimeline = clipList.get(i);
            MeicamVideoTrack meicamVideoTrack = meicamTimeline.getVideoTrack(trackIndexTimeline);
            if (meicamVideoTrack != null) {
                meicamVideoClip = meicamVideoTrack.getVideoClip(clipIndexTimeline);
                if (meicamVideoClip != null) {
                    clipInPoint += meicamVideoClip.getInPoint();
                    meicamTimeline = meicamVideoClip.getInternalTimeline();
                }
            }
        }
        if (meicamTimeline != null) {
            MeicamVideoTrack meicamVideoTrack = meicamTimeline.getVideoTrack(item.trackIndex - meicamTimeline.getRealMainTrackIndex());
            if (meicamVideoTrack != null) {
                MeicamVideoClip videoClip = meicamVideoTrack.getVideoClip(item.clipIndex);
                if (videoClip != null) {
                    if (videoClip.getInternalTimeline() == null) {
                        String bestSeekTime = videoClip.getTemplateAttachment(NvsObject.TEMPLATE_KEY_BEST_SEEK_TIME);
                        if (Long.parseLong(bestSeekTime) < 0) {
                            item.setRealInpoint(clipInPoint + videoClip.getInPoint());
                        } else {
                            item.setRealInpoint(Long.parseLong(bestSeekTime));
                        }

                        return videoClip;
                    }
                }
            }
        }
        return null;
    }

    public MeicamVideoClip getVideoClipByTemplateFootageCorrespondingClipInfo
            (MeicamTimeline meicamTimeline, List<Integer> clipList, List<Integer> trackList, int trackIndex, int clipIndex) {
        MeicamVideoClip meicamVideoClip;
        if (meicamTimeline == null) {
            meicamTimeline = getNvsTimeline();
        }
        for (int i = 0; i < trackList.size(); i++) {
            int trackIndexTimeline = trackList.get(i) - meicamTimeline.getRealMainTrackIndex();
            int clipIndexTimeline = clipList.get(i);
            MeicamVideoTrack meicamVideoTrack = meicamTimeline.getVideoTrack(trackIndexTimeline);
            if (meicamVideoTrack != null) {
                meicamVideoClip = meicamVideoTrack.getVideoClip(clipIndexTimeline);
                if (meicamVideoClip != null) {
                    meicamTimeline = meicamVideoClip.getInternalTimeline();
                }
            }
        }
        if (meicamTimeline != null) {
            MeicamVideoTrack meicamVideoTrack = meicamTimeline.getVideoTrack(trackIndex - meicamTimeline.getRealMainTrackIndex());
            if (meicamVideoTrack != null) {
                MeicamVideoClip videoClip = meicamVideoTrack.getVideoClip(clipIndex);
                if (videoClip == null || videoClip.getInternalTimeline() == null) {
                    return videoClip;
                }
            }
        }
        return null;
    }


    public MeicamCaptionClip getCaptionByTemplateCaptionDesc(TemplateCaptionDesc templateCaptionDesc) {
        setCaptionSeekPointAndGroupId(templateCaptionDesc);
        MeicamVideoClip meicamVideoClip;
        MeicamTimeline meicamTimeline = getNvsTimeline();
        float[] trans;
        float realScale;
        List<MeicamTimeline> allTimeline = new ArrayList<>();
        List<float[]> allTrans = new ArrayList<>();
        List<Float> allScale = new ArrayList<>();
        for (int i = 0; i < templateCaptionDesc.getClipTrackIndexInTimelineList().size(); i++) {
            int trackIndexTimeline = templateCaptionDesc.getClipTrackIndexInTimelineList().get(i);
            int clipIndexTimeline = templateCaptionDesc.getClipIndexInTimelineList().get(i);
            MeicamVideoTrack meicamVideoTrack = meicamTimeline.getVideoTrack(trackIndexTimeline);
            meicamVideoClip = meicamVideoTrack.getVideoClip(clipIndexTimeline);
            meicamTimeline = meicamVideoClip.getInternalTimeline();

            float[] trackTranslation = meicamVideoTrack.getTranslation();
            float[] translation = meicamVideoClip.getTranslation(templateCaptionDesc.getInPoint());
            LogUtils.d("getCaptionByTemplateCaptionDesc: trackId =  " +trackIndexTimeline + ", clipId = "+ clipIndexTimeline + " ,translation = "+ translation[0] + " y = "+translation[1] );
            trackTranslation[0] += translation[0];
            trackTranslation[1] += translation[1];
            allTrans.add(trackTranslation);
            allScale.add(meicamVideoTrack.getScale() * meicamVideoClip.getScale());
            allTimeline.add(meicamTimeline);
        }
        if (templateCaptionDesc.trackIndex < 0 && templateCaptionDesc.clipIndex < 0) {
            //timeline字幕
            //Timeline caption.
            return loadTimelineCaptionToTemplate(meicamTimeline, templateCaptionDesc.replaceId);

        } else if (templateCaptionDesc.trackIndex >= 0 && templateCaptionDesc.clipIndex < 0) {
            //轨道字幕
            //Track caption.
            MeicamVideoTrack meicamVideoTrack = meicamTimeline.getVideoTrack(templateCaptionDesc.trackIndex);
            for (int i = 0; i < meicamVideoTrack.getCaptionList().size(); i++) {
                MeicamCaptionClip meicamCaptionClip = meicamVideoTrack.getCaptionList().get(i);
                String id = meicamCaptionClip.getTemplateAttachment(NvsObject.TEMPLATE_KEY_REPLACE_ID);
                if (TextUtils.equals(id, templateCaptionDesc.replaceId)) {
                    //timeline的偏移量和缩放值 The offset and scaling values of the timeline.
                    trans = getRealTimelineTrans(allTimeline, allTrans, getNvsTimeline());
                    realScale = getRealTimelineScale(allScale);

                    //由于外部timeline有缩放，所以timeline内部的字幕的偏移量需要进行修正，字幕的偏移量是轨道的偏移和字幕本身的偏移相加
                    //Due to the scaling of the external timeline, the offset of the subtitles inside the timeline needs to be corrected.
                    // The offset of the subtitles is the sum of the offset of the track and the offset of the subtitles themselves.
                    float[] trackTranslation = meicamVideoTrack.getTranslation();
                    //字幕在timeline中实际的偏移量 The actual offset of subtitles in the timeline
                    float[] allCaptionTrans = new float[]{trackTranslation[0] + meicamCaptionClip.getTranslationX(), trackTranslation[1] + meicamCaptionClip.getTranslationY()};

                    //timeline缩放后，字幕的实际偏移量的差值，这个差值就是要修正的偏移量
                    //The difference in the actual offset of the subtitle after the timeline is scaled is the offset to be corrected.
                    float[] deltaTans = new float[]{allCaptionTrans[0]* (realScale - 1), allCaptionTrans[1]* (realScale - 1)};
                    //deltaTans 是timeline中偏移的修正值，还需要将这个修正值转换为外部timeline的修正值
                    //The deltaTans is the correction value for the offset in the timeline, and it is also necessary
                    // to convert this correction value to the correction value for the external timeline.
                    float[] realTrans = getRealTransInInnerTimeline(allTimeline, deltaTans, getNvsTimeline());
                    trans[0] += realTrans[0];
                    trans[1] += realTrans[1];

                    meicamCaptionClip.setTranslationInOutTimeline(trans);
                    meicamCaptionClip.setScaleInOutTimeline(realScale);
                    return meicamCaptionClip;
                }
            }
        } else if (templateCaptionDesc.trackIndex >= 0) {
            //片段字幕
            //Clip caption.
            MeicamVideoTrack meicamVideoTrackCaption = meicamTimeline.
                    getVideoTrack(templateCaptionDesc.trackIndex);
            MeicamVideoClip meicamVideoClipCaption = meicamVideoTrackCaption.
                    getVideoClip(templateCaptionDesc.clipIndex);
            for (int i = 0; i < meicamVideoClipCaption.getCaptionList().size(); i++) {
                MeicamCaptionClip meicamCaptionClip = meicamVideoClipCaption.getCaptionList().get(i);
                String id = meicamCaptionClip.getTemplateAttachment(NvsObject.TEMPLATE_KEY_REPLACE_ID);
                if (TextUtils.equals(id, templateCaptionDesc.replaceId)) {
                    return meicamCaptionClip;
                }
            }
        }
        return null;
    }


    /**
     * Sets caption seek point and group id.
     * 更新seek point和组id.
     *
     * @param templateCaptionDesc the template caption desc
     */
    public void setCaptionSeekPointAndGroupId(TemplateCaptionDesc templateCaptionDesc) {
        MeicamVideoClip meicamVideoClip;
        MeicamTimeline meicamTimeline = getNvsTimeline();
        long timelineDuration = meicamTimeline.getDuration();
        long clipInPoint = 0;
        for (int i = 0; i < templateCaptionDesc.getClipTrackIndexInTimelineList().size(); i++) {
            int trackIndexTimeline = templateCaptionDesc.getClipTrackIndexInTimelineList().get(i);
            int clipIndexTimeline = templateCaptionDesc.getClipIndexInTimelineList().get(i);
            MeicamVideoTrack meicamVideoTrack = meicamTimeline.getVideoTrack(trackIndexTimeline);
            meicamVideoClip = meicamVideoTrack.getVideoClip(clipIndexTimeline);
            clipInPoint += meicamVideoClip.getInPoint();
            meicamTimeline = meicamVideoClip.getInternalTimeline();

        }
        if (templateCaptionDesc.trackIndex < 0 && templateCaptionDesc.clipIndex < 0) {
            //timeline字幕
            //Timeline caption.
            MeicamCaptionClip meicamCaptionClip = loadTimelineCaptionToTemplate(meicamTimeline, templateCaptionDesc.replaceId);
            if (meicamCaptionClip != null) {
                long bestSeekTime = Long.parseLong(meicamCaptionClip.getTemplateAttachment(NvsObject.TEMPLATE_KEY_BEST_SEEK_TIME));
                if (bestSeekTime < 0) {
                    long halfCaptionDuration = (meicamCaptionClip.getOutPoint() - meicamCaptionClip.getInPoint()) / 2;
                    long inPoint = clipInPoint + meicamCaptionClip.getInPoint() + halfCaptionDuration;
                    if (inPoint > timelineDuration) {
                        inPoint = clipInPoint + meicamCaptionClip.getInPoint();
                    }
                    templateCaptionDesc.setInPoint(inPoint);
                } else {
                    templateCaptionDesc.setInPoint(bestSeekTime);
                }

            } else {
                LogUtils.e("字幕获取失败");
            }
            if (meicamCaptionClip != null) {
                String groupID = meicamCaptionClip.getTemplateAttachment(NvsObject.TEMPLATE_KEY_FX_GROUP);
                if (!TextUtils.isEmpty(groupID)) {
                    templateCaptionDesc.setGroupID(groupID);
                }
            }
        } else if (templateCaptionDesc.trackIndex >= 0 && templateCaptionDesc.clipIndex < 0) {
            //轨道字幕
            //Track caption.
            MeicamVideoTrack meicamVideoTrack = meicamTimeline.getVideoTrack(templateCaptionDesc.trackIndex);
            for (int i = 0; i < meicamVideoTrack.getCaptionList().size(); i++) {
                MeicamCaptionClip meicamCaptionClip = meicamVideoTrack.getCaptionList().get(i);
                String id = meicamCaptionClip.getTemplateAttachment(NvsObject.TEMPLATE_KEY_REPLACE_ID);
                if (TextUtils.equals(id, templateCaptionDesc.replaceId)) {
                    long bestSeekTime = Long.parseLong(meicamCaptionClip.getTemplateAttachment(NvsObject.TEMPLATE_KEY_BEST_SEEK_TIME));
                    if (bestSeekTime < 0) {

                        long halfCaptionDuration = (meicamCaptionClip.getOutPoint() - meicamCaptionClip.getInPoint()) / 2;
                        long inPoint = clipInPoint + meicamCaptionClip.getInPoint() + halfCaptionDuration;
                        if (inPoint> timelineDuration) {
                            inPoint = clipInPoint + meicamCaptionClip.getInPoint();
                        }
                        templateCaptionDesc.setInPoint(inPoint);
                    } else {
                        templateCaptionDesc.setInPoint(bestSeekTime);
                    }
                    String groupID = meicamCaptionClip.getTemplateAttachment(NvsObject.TEMPLATE_KEY_FX_GROUP);
                    if (!TextUtils.isEmpty(groupID)) {
                        templateCaptionDesc.setGroupID(groupID);
                    }
                }
            }
        } else if (templateCaptionDesc.trackIndex >= 0) {
            //片段字幕
            //Clip caption.
            MeicamVideoTrack meicamVideoTrackCaption = meicamTimeline.
                    getVideoTrack(templateCaptionDesc.trackIndex);
            MeicamVideoClip meicamVideoClipCaption = meicamVideoTrackCaption.
                    getVideoClip(templateCaptionDesc.clipIndex);
            for (int i = 0; i < meicamVideoClipCaption.getCaptionList().size(); i++) {
                MeicamCaptionClip meicamCaptionClip = meicamVideoClipCaption.getCaptionList().get(i);
                String id = meicamCaptionClip.getTemplateAttachment(NvsObject.TEMPLATE_KEY_REPLACE_ID);
                if (TextUtils.equals(id, templateCaptionDesc.replaceId)) {
                    long bestSeekTime = Long.parseLong(meicamCaptionClip.getTemplateAttachment(NvsObject.TEMPLATE_KEY_BEST_SEEK_TIME));
                    if (bestSeekTime < 0) {
                        long halfCaptionDuration = (meicamCaptionClip.getOutPoint() - meicamCaptionClip.getInPoint()) / 2;
                        long inPoint = clipInPoint + meicamCaptionClip.getInPoint() + halfCaptionDuration;
                        if (inPoint > timelineDuration) {
                            inPoint = clipInPoint + meicamCaptionClip.getInPoint();
                        }
                        templateCaptionDesc.setInPoint(inPoint);
                    } else {
                        templateCaptionDesc.setInPoint(bestSeekTime);
                    }
                    String groupID = meicamCaptionClip.getTemplateAttachment(NvsObject.TEMPLATE_KEY_FX_GROUP);
                    if (!TextUtils.isEmpty(groupID)) {
                        templateCaptionDesc.setGroupID(groupID);
                    }
                }
            }
        }
    }

    public MeicamCompoundCaptionClip getCompCaptionByTemplateCaptionDesc(TemplateCaptionDesc templateCaptionDesc) {
        MeicamVideoClip meicamVideoClip;
        MeicamTimeline meicamTimeline = getNvsTimeline();
        long clipInPoint = 0;
        for (int i = 0; i < templateCaptionDesc.getClipTrackIndexInTimelineList().size(); i++) {
            int trackIndexTimeline = templateCaptionDesc.getClipTrackIndexInTimelineList().get(i);
            int clipIndexTimeline = templateCaptionDesc.getClipIndexInTimelineList().get(i);
            MeicamVideoTrack meicamVideoTrack = meicamTimeline.getVideoTrack(trackIndexTimeline);
            meicamVideoClip = meicamVideoTrack.getVideoClip(clipIndexTimeline);
            clipInPoint += meicamVideoClip.getInPoint();
            meicamTimeline = meicamVideoClip.getInternalTimeline();
        }
        if (templateCaptionDesc.trackIndex < 0 && templateCaptionDesc.clipIndex < 0) {
            //timeline组合字幕
            //Timeline compound caption.
            MeicamCompoundCaptionClip meicamCompoundCaptionClip = loadTimelineCompoundCaptionToTemplate(
                    meicamTimeline, templateCaptionDesc.replaceId);
            if (meicamCompoundCaptionClip != null) {
                long bestSeekTime = Long.parseLong(meicamCompoundCaptionClip.getTemplateAttachment(NvsObject.TEMPLATE_KEY_BEST_SEEK_TIME));
                if (bestSeekTime < 0) {
                    templateCaptionDesc.setInPoint(clipInPoint + meicamCompoundCaptionClip.getInPoint() +
                            (meicamCompoundCaptionClip.getOutPoint() - meicamCompoundCaptionClip.getInPoint()) / 2);
                } else {
                    templateCaptionDesc.setInPoint(bestSeekTime);
                }
            } else {
                LogUtils.e("组合字幕获取失败");
            }
            return meicamCompoundCaptionClip;

        } else if (templateCaptionDesc.trackIndex >= 0 && templateCaptionDesc.clipIndex < 0) {
            //轨道组合字幕
            //Track compound caption.
            MeicamVideoTrack meicamVideoTrack = meicamTimeline.getVideoTrack(templateCaptionDesc.trackIndex);
            for (int i = 0; i < meicamVideoTrack.getCaptionList().size(); i++) {
                MeicamCompoundCaptionClip meicamCompoundCaptionClip = meicamVideoTrack.getCompoundCaptionList().get(i);
                String id = meicamCompoundCaptionClip.getTemplateAttachment(NvsObject.TEMPLATE_KEY_REPLACE_ID);
                if (TextUtils.equals(id, templateCaptionDesc.replaceId)) {
                    long bestSeekTime = Long.parseLong(meicamCompoundCaptionClip.getTemplateAttachment(NvsObject.TEMPLATE_KEY_BEST_SEEK_TIME));
                    if (bestSeekTime < 0) {
                        templateCaptionDesc.setInPoint(clipInPoint + meicamCompoundCaptionClip.getInPoint() +
                                (meicamCompoundCaptionClip.getOutPoint() - meicamCompoundCaptionClip.getInPoint()) / 2);
                    } else {
                        templateCaptionDesc.setInPoint(bestSeekTime);
                    }
                    return meicamCompoundCaptionClip;
                }
            }
        } else if (templateCaptionDesc.trackIndex >= 0) {
            //片段组合字幕
            //Clip compound caption.
            MeicamVideoTrack meicamVideoTrackCaption = meicamTimeline.
                    getVideoTrack(templateCaptionDesc.trackIndex);
            MeicamVideoClip meicamVideoClipCaption = meicamVideoTrackCaption.
                    getVideoClip(templateCaptionDesc.clipIndex);
            for (int i = 0; i < meicamVideoClipCaption.getCompoundCaptionList().size(); i++) {
                MeicamCompoundCaptionClip meicamCompoundCaptionClip =
                        meicamVideoClipCaption.getCompoundCaptionList().get(i);
                String id = meicamCompoundCaptionClip.getTemplateAttachment(NvsObject.TEMPLATE_KEY_REPLACE_ID);
                if (TextUtils.equals(id, templateCaptionDesc.replaceId)) {
                    long bestSeekTime = Long.parseLong(meicamCompoundCaptionClip.getTemplateAttachment(NvsObject.TEMPLATE_KEY_BEST_SEEK_TIME));
                    if (bestSeekTime < 0) {
                        templateCaptionDesc.setInPoint(clipInPoint + meicamCompoundCaptionClip.getInPoint() +
                                (meicamCompoundCaptionClip.getOutPoint() - meicamCompoundCaptionClip.getInPoint()) / 2);
                    } else {
                        templateCaptionDesc.setInPoint(bestSeekTime);
                    }
                    return meicamCompoundCaptionClip;
                }
            }
        }

        return null;
    }


    /**
     * 获取真实的缩放量
     * Get the real scale
     *
     * @param allScale the list of scale 所有的缩放集合
     * @return The scale
     */
    private float getRealTimelineScale(List<Float> allScale) {
        float scale = 1F;
        for (int index = 0; index < allScale.size(); index++) {
            float scaleV = allScale.get(index);
            scale *= scaleV;
        }
        return scale;
    }


    /**
     * 获取真实的偏移量
     * Get the real transform
     *
     * @param allTimeline the list of timeline 所有的时间线集合
     * @param allTrans the list of transform 所有的位移集合
     * @param baseTimeline The base timeline 基础timeline
     * @return the transform
     */
    private float[] getRealTimelineTrans(List<MeicamTimeline> allTimeline, List<float[]> allTrans, MeicamTimeline baseTimeline) {
        float[] trans = new float[2];
        NvsVideoResolution boxResolution;
        NvsVideoResolution innerResolution;
        PointF sizeInOutTimeline = new PointF();
        for (int index = 0; index < allTimeline.size(); index++) {
            MeicamTimeline meicamTimeline = allTimeline.get(index);
            NvsVideoResolution videoResolution = meicamTimeline.getVideoResolution();
            float[] floats = allTrans.get(index);
            floats[0] /= videoResolution.imageWidth;
            floats[1] /= videoResolution.imageHeight;
            if (index == 0) {
                boxResolution = baseTimeline.getVideoResolution();
                innerResolution = meicamTimeline.getVideoResolution();
                sizeInOutTimeline = assetSizeInBox(new PointF(boxResolution.imageWidth, boxResolution.imageHeight),
                        innerResolution.imageWidth * 1F / innerResolution.imageHeight);
            } else {
                innerResolution = allTimeline.get(index).getVideoResolution();
                sizeInOutTimeline = assetSizeInBox(sizeInOutTimeline,
                        innerResolution.imageWidth * 1F / innerResolution.imageHeight);
            }
            floats[0] *= sizeInOutTimeline.x;
            floats[1] *= sizeInOutTimeline.y;

            trans[0] += floats[0];
            trans[1] += floats[1];
        }
        return trans;
    }


    /**
     * 获取在内嵌timeline中真实的偏移量
     * Get real trans in inner timeline
     *
     * @param allTimeline the list of timeline 所有的时间线集合
     * @param transValue the trans value 偏移数值
     * @param baseTimeline The base timeline 基础timeline
     * @return the transform
     */
    private float[] getRealTransInInnerTimeline(List<MeicamTimeline> allTimeline, float[] transValue, MeicamTimeline baseTimeline) {
        float[] trans = new float[2];
        NvsVideoResolution boxResolution;
        NvsVideoResolution innerResolution;
        PointF sizeInOutTimeline = new PointF();
        MeicamTimeline lastMeicamTimeline = null;
        for (int index = 0; index < allTimeline.size(); index++) {
            MeicamTimeline meicamTimeline = allTimeline.get(index);
            if (index == 0) {
                boxResolution = baseTimeline.getVideoResolution();
                innerResolution = meicamTimeline.getVideoResolution();
                sizeInOutTimeline = assetSizeInBox(new PointF(boxResolution.imageWidth, boxResolution.imageHeight),
                        innerResolution.imageWidth * 1F / innerResolution.imageHeight);
            } else {
                innerResolution = allTimeline.get(index).getVideoResolution();
                sizeInOutTimeline = assetSizeInBox(sizeInOutTimeline,
                        innerResolution.imageWidth * 1F / innerResolution.imageHeight);
            }
            if(index == allTimeline.size() - 1) {
                lastMeicamTimeline = meicamTimeline;
            }
        }
        if (lastMeicamTimeline != null) {
            NvsVideoResolution videoResolution = lastMeicamTimeline.getVideoResolution();
            transValue[0] = transValue[0] / videoResolution.imageWidth * sizeInOutTimeline.x;
            transValue[1] = transValue[1] / videoResolution.imageHeight * sizeInOutTimeline.y;

            trans[0] = transValue[0];
            trans[1] = transValue[1];
        }
        return trans;
    }

    private PointF assetSizeInBox(PointF boxSize, float assetAspectRatio) {
        PointF pointF = new PointF();
        float boxSizeRate = boxSize.x / boxSize.y;
        if (boxSizeRate > assetAspectRatio) {
            //高对齐
            //High alignment
            pointF.y = boxSize.y;
            pointF.x = pointF.y * assetAspectRatio;
        } else {
            //宽对齐
            //Wide alignment
            pointF.x = boxSize.x;
            pointF.y = pointF.x / assetAspectRatio;
        }
        return pointF;
    }

    /**
     * 获取所有的字幕信息
     *
     * @param templateCaptionDescList The list templateCaptionDesc
     * @return The list of MeicamCaptionClip
     */
    public List<MeicamCaptionClip> getAllCaption(List<TemplateCaptionDesc> templateCaptionDescList) {
        List<MeicamCaptionClip> list = new ArrayList<>();
        for (TemplateCaptionDesc templateCaptionDesc : templateCaptionDescList) {
            if (templateCaptionDesc.isCaption()) {
                MeicamCaptionClip meicamCaptionClip = getCaptionByTemplateCaptionDesc(templateCaptionDesc);
                if (meicamCaptionClip != null) {
                    list.add(meicamCaptionClip);
                }
            }
        }
        return list;
    }

}
