package com.meishe.engine.command;

import com.meishe.annotation.Undo;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.MeicamMaskRegionInfo;
import com.meishe.engine.bean.MeicamPosition2D;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamTimelineVideoFilterAndAdjustClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxTrack;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/8/30 15:15
 * @Description :时间线特技命令 The timeline fx command
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class TimelineFxCommand {

    private static final String TAG = "TimelineFx";
    @Undo(className = "TimelineFxSetStringValCommand", function = "setStringVal",
            param = {"String|key", "String|value", "boolean...|needSaveOperate"})
    public static void setStringVal(MeicamTimelineVideoFxClip videoFx, String key, String value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key;
            TimelineFxSetStringValCommand command = CommandManager.getInstance().getCommand(tagAndKey, TimelineFxSetStringValCommand.class);
            if (command == null) {
                command = new TimelineFxSetStringValCommand(tag, new TimelineFxSetStringValCommand.UndoParam(key, videoFx.getStringVal(key), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            TimelineFxSetStringValCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam == null) {
                redoParam = new TimelineFxSetStringValCommand.RedoParam(key, value);
                command.setRedoParam(redoParam);
            } else {
                redoParam.setKey(key);
                redoParam.setValue(value);
            }
        }
        videoFx.setStringVal(key, value);
    }

    @Undo(className = "TimelineFxSetBooleanValCommand", function = "setBooleanVal",
            param = {"String|key", "boolean|value", " boolean...|needSaveOperate"})
    public static void setBooleanVal(MeicamTimelineVideoFxClip videoFx, String key, boolean value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key;
            TimelineFxSetBooleanValCommand command = CommandManager.getInstance().getCommand(tagAndKey, TimelineFxSetBooleanValCommand.class);
            if (command == null) {
                command = new TimelineFxSetBooleanValCommand(tag, new TimelineFxSetBooleanValCommand.UndoParam(key, videoFx.getBooleanVal(key), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            TimelineFxSetBooleanValCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam == null) {
                redoParam = new TimelineFxSetBooleanValCommand.RedoParam(key, value);
                command.setRedoParam(redoParam);
            } else {
                redoParam.setKey(key);
                redoParam.setValue(value);
            }
        }
        videoFx.setBooleanVal(key, value);
    }

    @Undo(className = "TimelineFxSetFloatValCommand", function = "setFloatVal",
            param = {"String|key", "float|value", " boolean...|needSaveOperate"})
    public static void setFloatVal(MeicamTimelineVideoFxClip videoFx, String key, float value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key;
            TimelineFxSetFloatValCommand command = CommandManager.getInstance().getCommand(tagAndKey, TimelineFxSetFloatValCommand.class);
            if (command == null) {
                command = new TimelineFxSetFloatValCommand(tag, new TimelineFxSetFloatValCommand.UndoParam(key, videoFx.getFloatVal(key, 0), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            TimelineFxSetFloatValCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam == null) {
                redoParam = new TimelineFxSetFloatValCommand.RedoParam(key, value);
                command.setRedoParam(redoParam);
            } else {
                redoParam.setKey(key);
                redoParam.setValue(value);
            }
        }
        videoFx.setFloatVal(key, value);
    }

    @Undo(className = "TimelineFxSetIntValCommand", function = "setIntVal",
            param = {"String|key", "int|value", " boolean...|needSaveOperate"})
    public static void setIntVal(MeicamTimelineVideoFxClip videoFx, String key, int value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key;
            TimelineFxSetIntValCommand command = CommandManager.getInstance().getCommand(tagAndKey, TimelineFxSetIntValCommand.class);
            if (command == null) {
                command = new TimelineFxSetIntValCommand(tag, new TimelineFxSetIntValCommand.UndoParam(key, videoFx.getIntVal(key), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            TimelineFxSetIntValCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam == null) {
                redoParam = new TimelineFxSetIntValCommand.RedoParam(key, value);
                command.setRedoParam(redoParam);
            } else {
                redoParam.setKey(key);
                redoParam.setValue(value);
            }
        }
        videoFx.setIntVal(key, value);
    }

    @Undo(id = 1, className = "TimelineFxSetColorValCommand", function = "setColorVal",
            param = {"String|key", "String|value", " boolean...|needSaveOperate"})
    public static void setColorVal(MeicamTimelineVideoFxClip videoFx, String key, String value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key;
            TimelineFxSetColorValCommand command = CommandManager.getInstance().getCommand(tagAndKey, TimelineFxSetColorValCommand.class);
            if (command == null) {
                command = new TimelineFxSetColorValCommand(tag, new TimelineFxSetColorValCommand.UndoParam(key, videoFx.getColor(key), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            TimelineFxSetColorValCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam == null) {
                redoParam = new TimelineFxSetColorValCommand.RedoParam(key, value);
                command.setRedoParam(redoParam);
            } else {
                redoParam.setKey(key);
                redoParam.setValue(value);
            }
        }
        videoFx.setColor(key, value);
    }

    @Undo(className = "TimelineFxSetMenuValCommand", function = "setMenuVal",
            param = {"String|key", "String|value", " boolean...|needSaveOperate"})
    public static void setMenuVal(MeicamTimelineVideoFxClip videoFx, String key, String value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key;
            TimelineFxSetMenuValCommand command = CommandManager.getInstance().getCommand(tagAndKey, TimelineFxSetMenuValCommand.class);
            if (command == null) {
                command = new TimelineFxSetMenuValCommand(tag, new TimelineFxSetMenuValCommand.UndoParam(key, videoFx.getMenuVal(key), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            TimelineFxSetMenuValCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam == null) {
                redoParam = new TimelineFxSetMenuValCommand.RedoParam(key, value);
                command.setRedoParam(redoParam);
            } else {
                redoParam.setKey(key);
                redoParam.setValue(value);
            }
        }
        videoFx.setMenuVal(key, value);
    }

    @Undo(className = "TimelineFxSetPosition2DCommand", function = "setPosition2DVal",
            param = {"String|key", "MeicamPosition2D|value", " boolean...|needSaveOperate"})
    public static void setPosition2DVal(MeicamTimelineVideoFxClip videoFx, String key, MeicamPosition2D value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key;
            TimelineFxSetPosition2DCommand command = CommandManager.getInstance().getCommand(tagAndKey, TimelineFxSetPosition2DCommand.class);
            if (command == null) {
                command = new TimelineFxSetPosition2DCommand(tag, new TimelineFxSetPosition2DCommand.UndoParam(key, videoFx.getPosition2DVal(key), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            TimelineFxSetPosition2DCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam == null) {
                redoParam = new TimelineFxSetPosition2DCommand.RedoParam(key, value);
                command.setRedoParam(redoParam);
            } else {
                redoParam.setKey(key);
                redoParam.setValue(value);
            }
        }
        videoFx.setPosition2DVal(key, value);
    }

    @Undo(className = "TimelineFxSetDisplayNameCommand", function = "setDisplayName",
            param = {"String|name", " boolean...|needSaveOperate"})
    public static void setDisplayName(MeicamTimelineVideoFxClip videoFx, String name, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            TimelineFxSetDisplayNameCommand command = CommandManager.getInstance().getCommand(tag, TimelineFxSetDisplayNameCommand.class);
            if (command == null) {
                command = new TimelineFxSetDisplayNameCommand(tag, new TimelineFxSetDisplayNameCommand.UndoParam(name, false));
                CommandManager.getInstance().putCommand(tag, command);
            }
            TimelineFxSetDisplayNameCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam == null) {
                redoParam = new TimelineFxSetDisplayNameCommand.RedoParam(name);
                command.setRedoParam(redoParam);
            } else {
                redoParam.setName(name);
            }
        }
        videoFx.setDisplayName(name);
    }

    @Undo(className = "TimelineFxSetIntensityCommand", function = "setIntensity",
            param = {"float|intensity", " boolean...|needSaveOperate"})
    public static void setIntensity(MeicamTimelineVideoFxClip videoFx, float intensity, boolean... needSaveOperate) {
        if (videoFx != null) {
            if (CommandUtil.needSaveOperate(needSaveOperate)) {
                String tag = getTag(videoFx);
                String tagAndKey = tag + videoFx.getInPoint();
                Object[] unDoParam = new Object[]{videoFx.getIntensity(), new boolean[]{false}};
                Object[] redoParam = new Object[]{intensity};
                CommandUtil.saveOperate("TimelineFxSetIntensityCommand", unDoParam, redoParam, tag, tagAndKey);
            }
            videoFx.setIntensity(intensity);
        }
    }


    @Undo(className = "TimelineFxSetObjectValCommand", function = "setObjectVal",
            param = {"String|key", "Object|value", "boolean...|needSaveOperate"})
    public static void setObjectVal(MeicamTimelineVideoFxClip videoFx, String key, Object value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key;
            TimelineFxSetObjectValCommand command = CommandManager.getInstance().getCommand(tagAndKey, TimelineFxSetObjectValCommand.class);
            if (command == null) {
                command = new TimelineFxSetObjectValCommand(tag, new TimelineFxSetObjectValCommand.UndoParam(key, videoFx.getObjectVal(key), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            TimelineFxSetObjectValCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam == null) {
                redoParam = new TimelineFxSetObjectValCommand.RedoParam(key, value);
                command.setRedoParam(redoParam);
            } else {
                redoParam.setKey(key);
                redoParam.setValue(value);
            }
        }
        videoFx.setObjectVal(key, value);
    }

    @Undo(className = "TimelineFxSetRegionalCommand", function = "setRegional",
            param = {"boolean|isRegional", " boolean...|needSaveOperate"})
    public static void setRegional(MeicamTimelineVideoFxClip videoFx, boolean isRegional, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + videoFx.getInPoint();
            TimelineFxSetRegionalCommand command = CommandManager.getInstance().getCommand(tagAndKey, TimelineFxSetRegionalCommand.class);
            if (command == null) {
                command = new TimelineFxSetRegionalCommand(tag, new TimelineFxSetRegionalCommand.UndoParam(videoFx.isRegional(), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            TimelineFxSetRegionalCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam == null) {
                redoParam = new TimelineFxSetRegionalCommand.RedoParam(isRegional);
                command.setRedoParam(redoParam);
            } else {
                redoParam.setIsRegional(isRegional);
            }
        }
        videoFx.setRegional(isRegional);
    }

    @Undo(className = "TimelineFxSetRegionInfoCommand", function = "setRegionInfo",
            param = {"MeicamMaskRegionInfo|regionInfo", " boolean...|needSaveOperate"})
    public static void setRegionInfo(MeicamTimelineVideoFxClip videoFx, MeicamMaskRegionInfo regionInfo, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + videoFx.getInPoint();
            TimelineFxSetRegionInfoCommand command = CommandManager.getInstance().getCommand(tagAndKey, TimelineFxSetRegionInfoCommand.class);
            if (command == null) {
                command = new TimelineFxSetRegionInfoCommand(tag, new TimelineFxSetRegionInfoCommand.UndoParam(videoFx.getRegionInfo(), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            TimelineFxSetRegionInfoCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam == null) {
                redoParam = new TimelineFxSetRegionInfoCommand.RedoParam(regionInfo);
                command.setRedoParam(redoParam);
            } else {
                redoParam.setRegionInfo(regionInfo);
            }
        }
        videoFx.setRegionInfo(regionInfo);
    }

    @Undo(className = "TimelineFxSetRegionDataCommand", function = "setRegionData",
            param = {"float[]|regionData", " boolean...|needSaveOperate"})
    public static void setRegionData(MeicamTimelineVideoFxClip videoFx, float[] regionData, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + videoFx.getInPoint();
            TimelineFxSetRegionDataCommand command = CommandManager.getInstance().getCommand(tagAndKey, TimelineFxSetRegionDataCommand.class);
            if (command == null) {
                command = new TimelineFxSetRegionDataCommand(tag, new TimelineFxSetRegionDataCommand.UndoParam(videoFx.getRegionData(), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            TimelineFxSetRegionDataCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam == null) {
                redoParam = new TimelineFxSetRegionDataCommand.RedoParam(regionData);
                command.setRedoParam(redoParam);
            } else {
                redoParam.setRegionData(regionData);
            }
        }
        videoFx.setRegionData(regionData);
    }


    @Undo(className = "TimelineFxSetFeatherWidthCommand", function = "setRegionalFeatherWidth",
            param = {"float|regionalFeatherWidth", " boolean...|needSaveOperate"})
    public static void setRegionalFeatherWidth(MeicamTimelineVideoFxClip videoFx, float regionalFeatherWidth, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + videoFx.getInPoint();
            TimelineFxSetFeatherWidthCommand command = CommandManager.getInstance().getCommand(tagAndKey, TimelineFxSetFeatherWidthCommand.class);
            if (command == null) {
                command = new TimelineFxSetFeatherWidthCommand(tag, new TimelineFxSetFeatherWidthCommand.UndoParam(videoFx.getRegionalFeatherWidth(), false));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            TimelineFxSetFeatherWidthCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam == null) {
                redoParam = new TimelineFxSetFeatherWidthCommand.RedoParam(regionalFeatherWidth);
                command.setRedoParam(redoParam);
            } else {
                redoParam.setRegionalFeatherWidth(regionalFeatherWidth);
            }
        }
        videoFx.setRegionalFeatherWidth(regionalFeatherWidth);
    }


    public static String getTag(MeicamTimelineVideoFxClip videoFx) {
        return TAG + videoFx.getExtraTag();
    }

    public static MeicamTimelineVideoFxClip getItByTag(String tag) {
        String[] split = tag.replaceAll(TAG, "").split("\\|");
        int trackIndex = Integer.parseInt(split[0]);
        int clipIndex = Integer.parseInt(split[1]);
        String desc = split[2];
        String subType = split[3];
        MeicamTimeline currentTimeline = EditorEngine.getInstance().getCurrentTimeline();
        if (currentTimeline == null) {
            return null;
        }
        MeicamTimelineVideoFxTrack fxTrack;
        if (MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FX.equals(subType)) {
            fxTrack = currentTimeline.getTimelineFxTrack(trackIndex);
        } else if (MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_MOSAIC.equals(subType)
                || MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_BLUR.equals(subType)) {
            return currentTimeline.getTimelineFxFromClipList(clipIndex);
        } else {
            fxTrack = currentTimeline.getFilterAndAdjustTimelineTrack(trackIndex);
        }
        if (fxTrack == null) {
            return null;
        }
        MeicamTimelineVideoFxClip clip = null;
        if (MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FX.equals(subType)) {
            clip = fxTrack.getClip(clipIndex);
        } else if (MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FILTER.equals(subType)) {
            fxTrack = currentTimeline.getFilterAndAdjustTimelineTrack(trackIndex);
            MeicamTimelineVideoFilterAndAdjustClip filterAndAdjustClip = fxTrack.getFilterAndAdjustClip(clipIndex);
            if (filterAndAdjustClip != null) {
                clip = filterAndAdjustClip.getAdjustTimelineFx(MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FILTER);
            }
        } else {
            MeicamTimelineVideoFilterAndAdjustClip filterAndAdjustClip = fxTrack.getFilterAndAdjustClip(clipIndex);
            if (filterAndAdjustClip != null) {
                clip = filterAndAdjustClip.getAdjustTimelineFx(desc);
            }
        }
        return clip;
    }
}
