<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.meishe.business.assets.view.AssetsTypeTabView
        android:id="@+id/ttv_tab_type"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_102" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/width_confirm_menu_recycleView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_px_24"
            android:minHeight="@dimen/dp_px_147" />

        <TextView
            android:id="@+id/tv_hint"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_px_331"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:textColor="@color/color_ff808080"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/rl_top_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/width_confirm_menu_recycleView"
            android:layout_marginTop="@dimen/dp_px_36"
            android:layout_marginStart="@dimen/dp_px_65"
            android:layout_marginEnd="@dimen/dp_px_87"
            android:orientation="horizontal"
            android:visibility="gone">

            <TextView
                android:id="@+id/tv_seekbar_desc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_px_66"
                android:layout_marginBottom="@dimen/dp_px_6"
                android:layout_gravity="center_vertical"
                android:gravity="center"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_px_30"
                android:visibility="gone" />

            <com.meishe.myvideo.view.MYSeekBarTextView
                android:id="@+id/view_seek_bar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </LinearLayout>
    </RelativeLayout>

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_1"
        android:layout_marginTop="@dimen/dp_px_24"
        android:background="@color/menu_divide_color" />

    <FrameLayout
        android:id="@+id/fl_confirm"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_225">

        <TextView
            android:id="@+id/tv_apply_all"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_px_42"
            android:layout_marginLeft="@dimen/dp_px_42"
            android:layout_marginTop="@dimen/dp_px_57"
            android:text="@string/apply_all"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_30" />

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_px_48"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_42" />

        <ImageView
            android:id="@+id/iv_confirm"
            android:layout_width="@dimen/dp_px_69"
            android:layout_height="@dimen/dp_px_69"
            android:layout_gravity="end"
            android:layout_marginTop="@dimen/dp_px_48"
            android:layout_marginEnd="@dimen/dp_px_39"
            android:layout_marginRight="@dimen/dp_px_39"
            android:background="@mipmap/ic_confirm"
            android:contentDescription="@null"
            android:padding="@dimen/dp_px_18" />
    </FrameLayout>
</LinearLayout>