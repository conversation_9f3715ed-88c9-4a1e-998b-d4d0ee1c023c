package com.meishe.myvideo.ui.trackview.bean;

import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.KeyFrameProcessor;
import com.meishe.engine.bean.MeicamCompoundCaptionClip;
import com.meishe.engine.bean.MeicamCompoundCaptionItem;
import com.meishe.engine.command.ClipCommand;
import com.meishe.myvideo.R;
import com.meishe.myvideo.ui.bean.BaseUIClip;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/5/25 11:04
 * @Description :组合字幕代理 Compound caption proxy
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CompoundCaptionProxy extends BaseUIClip {

    private MeicamCompoundCaptionClip mCaptionClip;

    public CompoundCaptionProxy(MeicamCompoundCaptionClip captionClip, int trackIndex) {
        super(CommonData.CLIP_COMPOUND_CAPTION, trackIndex);
        this.mCaptionClip = captionClip;
        if (captionClip != null) {
            super.setInPoint(captionClip.getInPoint());
            super.setOutPoint(captionClip.getOutPoint());
        }
    }

    @Override
    public int getSubType() {
        return INVALID;
    }

    @Override
    public int getClipIndexInTrack() {
        if (mCaptionClip != null) {
            return mCaptionClip.getIndex();
        }
        return INVALID;
    }

    @Override
    public long getInPoint() {
        if (mCaptionClip != null) {
            return mCaptionClip.getInPoint();
        }
        return INVALID;
    }

    @Override
    public void setInPoint(long inPoint) {
        if (mCaptionClip != null) {
            ClipCommand.setInPoint(mCaptionClip, inPoint);
        }
        super.setInPoint(inPoint);
    }

    @Override
    public void setOutPoint(long outPoint) {
        if (mCaptionClip != null) {
            ClipCommand.setOutPoint(mCaptionClip, outPoint);
        }
        super.setOutPoint(outPoint);
    }

    @Override
    public long getOutPoint() {
        if (mCaptionClip != null) {
            return mCaptionClip.getOutPoint();
        }
        return super.getOutPoint();
    }

    @Override
    public double getSpeed() {
        return 1.0D;
    }

    @Override
    public String getIconFilePath() {
        return "";
    }

    @Override
    public String getFilePath() {
        return "";
    }

    @Override
    public float[] getRecordArray() {
        return new float[0];
    }

    @Override
    public void setDuration(long duration) {
    }

    @Override
    public long getDuration() {
        if (mCaptionClip != null) {
            return mCaptionClip.getOutPoint() - mCaptionClip.getInPoint();
        }
        return INVALID;
    }

    @Override
    public long getFadeIn() {
        return 0;
    }

    @Override
    public long getFadeOut() {
        return 0;
    }

    @Override
    public String getDisplayName() {
        if (mCaptionClip != null) {
            MeicamCompoundCaptionItem meicamCompoundCaptionItem = mCaptionClip.getCaptionItem(0);
            if (meicamCompoundCaptionItem != null) {
                return meicamCompoundCaptionItem.getText();
            }
        }
        return null;
    }

    @Override
    public boolean canExceedLength() {
        return true;
    }

    @Override
    public boolean canDrag() {
        return true;
    }

    @Override
    public int getBackGroundColor() {
        return R.color.track_background_color_compound_caption;
    }

    @Override
    public KeyFrameProcessor keyFrameProcessor() {
        return super.keyFrameProcessor();
    }
}
