package com.meishe.user.manager;

import android.text.TextUtils;

import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.ThreadUtils;
import com.meishe.draft.data.DraftData;
import com.meishe.draft.db.DraftDbManager;
import com.meishe.draft.db.JobInfoDao;
import com.meishe.draft.db.JobInfoEntity;
import com.meishe.engine.EngineNetApi;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;
import com.meishe.user.bean.JobInfo;
import com.meishe.user.bean.VideoCompileBean;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/8/18 13:50
 * @Description :云任务管理器 The manager for cloud job
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CloudJobManager {
   /**
    * 请求的间隔时间
    * The interval time of request
    */
   private static final int REQUEST_INTERVAL_TIME = 500;

   private ConcurrentMap<String,JobInfoEntity> mCurrentJobEntityMap = new ConcurrentHashMap<>();
   private Set<String> taskSet = new HashSet<>();
   private JobInfoDao mJobInfoDao;

   private JobStateChangedListener mJobStateChangedListener;

   public void setJobStateChangedListener(JobStateChangedListener listener) {
      this.mJobStateChangedListener = listener;
   }

   public CloudJobManager() {
      mJobInfoDao = DraftDbManager.get().getJobInfoDao();
   }

   public void getJobInfo(List<Integer> jobList){
      if (CommonUtils.isEmpty(jobList)) {
         return;
      }
      //先取消任务， Cancel all task first.
      cancelTask();

      IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
      if (userPlugin == null) {
         return;
      }
      String token = userPlugin.getToken();
      if (TextUtils.isEmpty(token)) {
         return;
      }
      for (Integer jobId : jobList) {
         String jobString = String.valueOf(jobId);
         if (!taskSet.contains(jobString)) {
            taskSet.add(jobString);
         }
         ThreadUtils.runOnUiThreadDelayed(new JobTask(token, jobString), 200);
      }
   }

   public void createTemporaryJob(DraftData draftData, String title) {
      String projectId = draftData.getProjectId();
      JobInfoEntity entity = new JobInfoEntity();
      entity.setCoverUrl(draftData.getCoverPath());
      entity.setCreateAt(String.valueOf(System.currentTimeMillis()));
      entity.setDuration(draftData.getDuration());
      entity.setTitle(title);
      entity.setFileSize(draftData.getFileSize());
      entity.setProjectUuid(projectId);
      entity.setId(entity.getCreateAt());
      mCurrentJobEntityMap.put(projectId, entity);
   }

   public void insertTempDataToDb(String projectId, String jobId) {
      JobInfoEntity jobInfoEntity = mCurrentJobEntityMap.get(projectId);
      if (jobInfoEntity != null) {
         jobInfoEntity.setJobId(jobId);
         mJobInfoDao.insert(jobInfoEntity);
         mCurrentJobEntityMap.remove(projectId);
      }
   }

   public void updateJobInfo(String projectId, String jobId) {
      JobInfoEntity jobInfoEntity = mCurrentJobEntityMap.get(projectId);
      if (jobInfoEntity != null) {
         jobInfoEntity.setJobId(jobId);
      }
   }

   public void deleteJob(String projectId) {
      mJobInfoDao.deleteJob(projectId);
      mCurrentJobEntityMap.remove(projectId);
   }

   public void createTemporaryJob(String projectId) {
      JobInfoEntity entity = new JobInfoEntity();
      entity.setCreateAt(String.valueOf(System.currentTimeMillis()));
      entity.setProjectUuid(projectId);
      entity.setId(entity.getCreateAt());
      mCurrentJobEntityMap.put(projectId, entity);
   }

   public String getJobUUid(String jobId) {
      if (mCurrentJobEntityMap.isEmpty()) {
         return null;
      }
      Set<Map.Entry<String, JobInfoEntity>> entries = mCurrentJobEntityMap.entrySet();
      for (Map.Entry<String, JobInfoEntity> entry : entries) {
         JobInfoEntity value = entry.getValue();
         if (jobId.equals(value.getJobId())) {
            return value.getProjectUuid();
         }
      }
      return null;
   }


   private class JobTask implements Runnable{
      private String jobId;
      private String token;
      private CloudCompileManager.JobItem jobItem;

      public JobTask(String token, String jobId) {
         this.jobId = jobId;
         this.token = token;
         jobItem = new CloudCompileManager.JobItem(jobId);
      }

      @Override
      public void run() {
         EngineNetApi.getJobInfo(token, jobId, new RequestCallback<JobInfo>() {
            @Override
            public void onSuccess(BaseResponse<JobInfo> response) {
               JobInfo data = response.getData();
               if (data == null) {
                  if (mJobStateChangedListener != null) {
                     mJobStateChangedListener.onFailed();
                  }
                  return;
               }
               if (data.getProgress() < 100 || data.getStatus() != 1) {
                  ThreadUtils.runOnUiThreadDelayed(JobTask.this, REQUEST_INTERVAL_TIME);
                  if (mJobStateChangedListener != null) {
                     jobItem.state = data.getStatus();
                     jobItem.progress = data.getProgress();
                     jobItem.compiledUrl = data.getVideoUrl();
                     mJobStateChangedListener.onChanged(jobItem);
                  }
               } else {
                  EngineNetApi.cancelTask(jobId);
                  ThreadUtils.getSinglePool().execute(new Runnable() {
                     @Override
                     public void run() {
                        JobInfoEntity jobInfo = mJobInfoDao.getJobInfo(data.getJobId());
                        if (jobInfo != null) {
                           mJobInfoDao.deleteByJobId(data.getJobId());
                        }
                        ThreadUtils.runOnUiThread(new Runnable() {
                           @Override
                           public void run() {
                              if (mJobStateChangedListener != null) {
                                 jobItem.state = data.getStatus();
                                 jobItem.progress = data.getProgress();
                                 jobItem.compiledUrl = data.getVideoUrl();
                                 mJobStateChangedListener.onChanged(jobItem);
                              }
                           }
                        });
                     }
                  });
               }
            }

            @Override
            public void onError(BaseResponse<JobInfo> response) {

            }
         });
      }
   }

   public List<VideoCompileBean> getAllJobData() {
      List<JobInfoEntity> jobList = mJobInfoDao.getJob();
      if (CommonUtils.isEmpty(jobList) && mCurrentJobEntityMap.isEmpty()) {
         return new ArrayList<>();
      }
      List<VideoCompileBean> result = new ArrayList<>();
      Set<Map.Entry<String, JobInfoEntity>> entries = mCurrentJobEntityMap.entrySet();
      for (Map.Entry<String, JobInfoEntity> entry : entries) {
         result.add(VideoCompileBean.create(entry.getValue()));
      }
      for (JobInfoEntity entity : jobList) {
         result.add(VideoCompileBean.create(entity));
      }
      return result;
   }

   /**
    * Cancel task.
    * 取消任务
    */
   public void cancelTask(){
      if (taskSet.isEmpty()) {
         return;
      }
      for (String taskId : taskSet) {
         EngineNetApi.cancelTask(taskId);
      }
      taskSet.clear();
   }

   public interface JobStateChangedListener {
      void onChanged(CloudCompileManager.JobItem jobItem);
      void onFailed();
   }
}
