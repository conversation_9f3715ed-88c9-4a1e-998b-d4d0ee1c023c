<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_speed_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/menu_bg">

    <com.meishe.myvideo.view.editview.EditChangeSpeedScrollView
        android:id="@+id/speed_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_px_60" />


    <FrameLayout
        android:id="@+id/fl_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_225"
        android:layout_below="@+id/speed_view">

        <CheckBox
            android:id="@+id/ck_change_voice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_px_30"
            android:layout_marginLeft="@dimen/dp_px_30"
            android:layout_marginTop="@dimen/dp_px_45"
            android:button="@drawable/bg_checkbox"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp_px_30"
            android:paddingLeft="@dimen/dp_px_30"
            android:paddingEnd="@dimen/dp_px_0"
            android:paddingRight="@dimen/dp_px_0"
            android:text="@string/video_change_voice"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_33" />

        <View
            android:id="@+id/v_bottom_line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_px_1"
            android:background="@color/menu_divide_color" />

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_px_45"
            android:gravity="center"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_36" />

        <ImageView
            android:id="@+id/iv_confirm"
            android:layout_width="@dimen/dp_px_70"
            android:layout_height="@dimen/dp_px_70"
            android:layout_gravity="end"
            android:layout_marginTop="@dimen/dp_px_30"
            android:layout_marginEnd="@dimen/dp_px_45"
            android:layout_marginRight="@dimen/dp_px_45"
            android:background="@mipmap/ic_confirm"
            android:contentDescription="@null" />


    </FrameLayout>

</merge>