package com.meishe.user.view.fragment;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.meishe.base.model.BaseFragment;
import com.meishe.user.R;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> Li<PERSON><PERSON><PERSON>hou
 * @CreateDate :2020/11/11
 * @Description :剪辑草稿底部的删除view  the View at the bottom of the clip draft
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CloudBottomDeleteFragment extends BaseFragment {
    private static final String CAN_DOWNLOAD = "canDownload";
    private OnEventListener mListener;
    private boolean mCanDownload;
    private TextView mDownloadTextView;
    private ImageView mDownloadImage;

    public CloudBottomDeleteFragment() {
    }

    public static CloudBottomDeleteFragment create(boolean canDownload, OnEventListener listener) {
        CloudBottomDeleteFragment fragment = new CloudBottomDeleteFragment().setListener(listener);
        Bundle bundle = new Bundle();
        bundle.putBoolean(CAN_DOWNLOAD, canDownload);
        fragment.setArguments(bundle);
        return fragment;
    }

    private CloudBottomDeleteFragment setListener(OnEventListener listener) {
        mListener = listener;
        return this;
    }

    @Override
    protected int bindLayout() {
        return R.layout.dialog_manage_draft_cloud;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        Bundle arguments = getArguments();
        if (arguments != null) {
            mCanDownload = arguments.getBoolean(CAN_DOWNLOAD);
        }
        rootView.findViewById(R.id.tv_delete).setOnClickListener(v -> {
            if (mListener != null) {
                mListener.onDelete();
            }
        });
        mDownloadTextView = rootView.findViewById(R.id.tv_download);
        mDownloadImage = rootView.findViewById(R.id.iv_download);
        View.OnClickListener download = v -> {
            if (mListener != null) {
                mListener.onDownload();
            }
        };
        mDownloadImage.setOnClickListener(download);
        mDownloadTextView.setOnClickListener(download);
        switchDownload(false);
    }

    /**
     * Switch download.
     * 切换下载状态
     * @param canDownload the can download 是否可下载
     */
    public void switchDownload(boolean canDownload) {
        if (canDownload) {
            mDownloadImage.setClickable(true);
            mDownloadTextView.setClickable(true);
            mDownloadTextView.setTextColor(getResources().getColor(R.color.white_8));
            mDownloadImage.setImageResource(R.mipmap.ic_draft_download);
        } else {
            mDownloadImage.setClickable(false);
            mDownloadTextView.setClickable(false);
            mDownloadTextView.setTextColor(getResources().getColor(R.color.white_5));
            mDownloadImage.setImageResource(R.mipmap.ic_draft_download_unselected);
        }
    }

    @Override
    protected void initData() {

    }

    public interface OnEventListener {
        /**
         * On delete.
         */
        void onDelete();

        /**
         * On download.
         */
        void onDownload();
    }
}
