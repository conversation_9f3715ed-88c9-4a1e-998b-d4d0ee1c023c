package com.meishe.myvideo.view;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.model.BaseMvpView;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.ImageLoader;
import com.meishe.base.utils.NetUtils;
import com.meishe.base.utils.SizeUtils;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.engine.util.PathUtils;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.myvideo.R;
import com.meishe.myvideo.interfaces.BottomEventListener;
import com.meishe.myvideo.view.interf.BackgroundView;
import com.meishe.myvideo.view.presenter.BackGroundPresenter;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 画布样式
 * The canvas style
 */
public class MYCanvasStyle extends BaseMvpView<BackGroundPresenter> implements BackgroundView {

    private ImageView mIvConfirm;
    private CanvasStyleAdapter mAdapter;
    private TextView mTvApplyAll;
    private ImageView mIvApplyAll;
    private BottomEventListener mEventListener;
    private String mSelectTag;
    private RecyclerView mRecyclerView;
    private CharSequence mDownloadTag;

    public MYCanvasStyle(Context context) {
        this(context, null);
    }

    public MYCanvasStyle(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MYCanvasStyle(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
        initListener();
    }

    @Override
    protected BackGroundPresenter createPresenter() {
        return new BackGroundPresenter();
    }

    /**
     * Init view.
     * 初始化视图
     */
    @Override
    public void initView() {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.view_canvas_style, this);
        mRecyclerView = view.findViewById(R.id.recyclerView);
        mIvConfirm = view.findViewById(R.id.iv_confirm);
        mIvApplyAll = view.findViewById(R.id.iv_apply_all);
        mTvApplyAll = view.findViewById(R.id.tv_apply_all);

        LinearLayoutManager gridLayoutManager = new LinearLayoutManagerWrapper(getContext(), LinearLayoutManager.HORIZONTAL, false);
        mRecyclerView.setLayoutManager(gridLayoutManager);
        mAdapter = new CanvasStyleAdapter();
        mRecyclerView.setAdapter(mAdapter);
        mRecyclerView.addItemDecoration(new ItemDecoration(SizeUtils.dp2px(3), SizeUtils.dp2px(12)));
    }

    @Override
    protected void initData() {
        mPresenter.loadData(true);
    }


    /**
     * Init listener.
     * 初始化监听
     */
    public void initListener() {
        mIvConfirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mEventListener != null) {
                    mEventListener.onDismiss(true);
                }
            }
        });
        mTvApplyAll.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                IBaseInfo selectedInfo = mAdapter.getSelected();
                if (mEventListener != null && selectedInfo != null) {
                    mEventListener.onItemClick(selectedInfo, true);
                }
            }
        });
        mIvApplyAll.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                IBaseInfo selectedInfo = mAdapter.getSelected();
                if (mEventListener != null && selectedInfo != null) {
                    mEventListener.onItemClick(selectedInfo, true);
                }
            }
        });

        mAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                if (!mPresenter.loadMoreData(false)) {
                    mAdapter.loadMoreEnd(true);
                }
            }
        }, mRecyclerView);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
                if (userPlugin != null && userPlugin.isLogin()) {
                    onItemClicked(mAdapter.getItem(position), position);
                } else {
                    mAdapter.selected(position);
                    if (mEventListener != null) {
                        mEventListener.onItemClick(mAdapter.getItem(position), false);
                    }
                }
            }
        });
    }

    protected void onItemClicked(IBaseInfo baseInfo, int position) {
        AssetInfo assetInfo = (AssetInfo) baseInfo;
        if (assetInfo != null) {
            String downloadUrl = assetInfo.getDownloadUrl();
            if (position < 2) {
                onFileDownloaded(position);
                return;
            }
            if (!TextUtils.isEmpty(downloadUrl)) {
                String assetPath = assetInfo.getAssetPath();
                if (TextUtils.isEmpty(assetPath)) {
                    String[] split = downloadUrl.split("/");
                    String fileName = split[split.length - 1];
                    File file = new File(PathUtils.getBackgroundDir()
                            + File.separator + assetInfo.getPackageId() + File.separator + fileName);
                    String filePath = null;
                    if (file.exists()) {
                        filePath = file.getAbsolutePath();
                    }
                    if (TextUtils.isEmpty(filePath)) {
                        File cloudFile = new File(PathUtils.getCloudDraftFootageFileFolder()
                                + File.separator + fileName);
                        if (cloudFile.exists()) {
                            filePath = cloudFile.getAbsolutePath();
                        }
                    }
                    if (!TextUtils.isEmpty(filePath)) {
                        assetInfo.setAssetPath(filePath);
                        onFileDownloaded(position);
                    } else {
                        downloadAssets(assetInfo, position);
                    }
                } else {
                    onFileDownloaded(position);
                }
            }
        }
    }

    private void onFileDownloaded(int position) {
        setSelection(position);
        if (mEventListener != null) {
            mEventListener.onItemClick(mAdapter.getItem(position), false);
        }
    }

    public void setSelection(int selection) {
        mAdapter.selected(selection);
        IBaseInfo selected = mAdapter.getSelected();
        mSelectTag = selected == null ? "" : selected.getPackageId();
    }

    private void downloadAssets(AssetInfo assetInfo, int position) {
        assetInfo.setDownloadProgress(0);
        assetInfo.setHadDownloaded(false);
        mAdapter.notifyItemChanged(position);
        mDownloadTag = assetInfo.getPackageId();
        mPresenter.downloadAsset(assetInfo, position);
    }

    /**
     * 设置事件监听
     * Sets the listener
     *
     * @param listener the listener
     */
    public void setListener(BottomEventListener listener) {
        mEventListener = listener;
    }

    /**
     * 设置选中
     * Selected the item
     *
     * @param stylePath the style path
     */
    public void selected(final String stylePath) {
        mSelectTag = stylePath;
        if (mAdapter != null) {
            mAdapter.selected(stylePath);
        }
    }

    @Override
    public boolean isActive() {
        return isShown();
    }

    @Override
    public void onNewDataBack(List<AssetInfo> list, int subType, boolean needUpdate) {
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin != null && userPlugin.isLogin()) {
            mAdapter.loadMoreComplete();
            Log.e("tell", "onNewDataBack: -----------------------------");
            if (!CommonUtils.isEmpty(list)) {
                for (AssetInfo assetInfo : list) {
                    Log.e("tell", "onNewDataBack: assetInfo = "+assetInfo.getName() + ", url = "+assetInfo.getDownloadUrl() );
                }
                mAdapter.setNewData(new ArrayList<AssetInfo>(list));
                selected(mSelectTag);
            }
            updateViewState();
        } else {
            post(new Runnable() {
                @Override
                public void run() {
                    mAdapter.loadMoreComplete();
                    if (!CommonUtils.isEmpty(list)) {
                        mAdapter.setNewData(new ArrayList<AssetInfo>(list));
                        selected(mSelectTag);
                    }
                    updateViewState();
                }
            });
        }
    }

    @Override
    public void onMoreDataBack(List<AssetInfo> list, int subType, boolean needUpdate) {
        mAdapter.loadMoreComplete();
        if (!CommonUtils.isEmpty(list)) {
            Log.e("tell", "onNewDataBack: -----------------------------");
            for (AssetInfo assetInfo : list) {
                Log.e("tell", "onNewDataBack: assetInfo = "+assetInfo.getName() + ", url = "+assetInfo.getDownloadUrl() );
            }
            mAdapter.addData(new ArrayList<AssetInfo>(list));
            selected(mSelectTag);
            updateViewState();
        }
    }

    @Override
    public void onDataError(int subType, boolean needUpdate) {
        mAdapter.setNewData(new ArrayList<AssetInfo>());
        selected(mSelectTag);
        updateViewState();
        mAdapter.loadMoreEnd(true);
    }

    @Override
    public int getItemCount() {
        //最少数量是2，包括null和加载更多item
        //The minimum number is 2, including null and loading more items.
        return mAdapter == null ? 0 : mAdapter.getItemCount() - 2;
    }

    @Override
    public void onDownloadProgress(int position) {
        mAdapter.notifyItemChanged(position);
    }

    @Override
    public void onDownloadFinish(int position, AssetInfo assetInfo) {
        if (TextUtils.equals(mDownloadTag, assetInfo.getPackageId())) {
            onFileDownloaded(position);
        } else {
            mAdapter.notifyItemChanged(position);
        }
    }

    @Override
    public void onDownloadError(int position) {
        mAdapter.notifyItemChanged(position);
    }

    private void updateViewState() {
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin != null) {
            if (userPlugin.isLogin() && NetUtils.isNetworkAvailable(getContext())) {
                /*
                 * 网络正常后，开启自动加载
                 * After the network is normal, turn on automatic loading
                 */
                mAdapter.setEnableLoadMore(true);
            } else {
                mAdapter.setEnableLoadMore(false);
            }
        } else {
            mAdapter.setEnableLoadMore(false);
        }
    }

    private static class CanvasStyleAdapter extends BaseQuickAdapter<AssetInfo, BaseViewHolder> {
        private ImageLoader.Options mOptions;
        private int mSelectedPosition = -1;

        private CanvasStyleAdapter() {
            super(R.layout.item_canvas_style);
            mOptions = new ImageLoader.Options()
                    .centerCrop()
                    .roundedCorners(4)
                    .skipMemoryCache(true)
                    .dontAnimate();
        }

        /**
         * 选中某一项
         * Selected item .
         *
         * @param filePath The item style path
         */
        public void selected(String filePath) {
            if (!TextUtils.isEmpty(filePath)) {
                int size = getData().size();
                IBaseInfo styleInfo;
                for (int i = 0; i < size; i++) {
                    styleInfo = getData().get(i);
                    if (filePath.equals(styleInfo.getPackageId())) {
                        selected(i);
                        return;
                    }
                }
            }
        }

        /**
         * 选中某一项
         * Selected item .
         *
         * @param position The index of list
         */
        public void selected(int position) {
            //注意：这里0位置不允许被选中
            // Note: The 0 position is not allowed to be selected here.
            if (mSelectedPosition > 0) {
                notifyItemChanged(mSelectedPosition);
            }
            mSelectedPosition = position;
            if (position > 0 && position < getData().size()) {
                notifyItemChanged(position);
            }
        }

        public IBaseInfo getSelected() {
            return getItem(mSelectedPosition);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, AssetInfo item) {
            ImageView cover = helper.getView(R.id.iv_picture);
            ImageLoader.loadUrl(mContext, item.getCoverPath(), cover, mOptions);
            if (helper.getAdapterPosition() == mSelectedPosition) {
                cover.setBackground(CommonUtils.getRadiusDrawable(3, mContext.getResources().getColor(R.color.color_fffc2b55), 6, -1));
            } else {
                cover.setBackgroundResource(0);
            }

            ImageView ivDownload = helper.getView(R.id.iv_downloading);
            if (!item.isHadDownloaded() || item.needUpdate()) {
                ImageLoader.loadUrl(mContext, R.mipmap.downloading, ivDownload);
                int progress = item.getDownloadProgress();
                if (progress >= 0 && progress < 100) {
                    ivDownload.setVisibility(View.VISIBLE);
                    cover.setVisibility(View.GONE);
                } else {
                    ivDownload.setVisibility(View.GONE);
                    cover.setVisibility(View.VISIBLE);
                }
            } else {
                ivDownload.setVisibility(View.GONE);
                cover.setVisibility(View.VISIBLE);
            }
        }
    }
}
