package com.meishe.myvideo.fragment.adapter;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.meishe.base.bean.MediaData;
import com.meishe.base.bean.MediaSection;
import com.meishe.base.utils.AndroidVersionUtils;
import com.meishe.base.utils.FormatUtils;
import com.meishe.base.utils.ImageLoader;
import com.meishe.myvideo.R;
import com.meishe.third.adpater.BaseViewHolder;


/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: ChuChenGuang
 * @CreateDate: 2022/10/31 14:18
 * @Description: clip 选中的视频媒体适配器
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class MediaSelectAdapter extends BaseSelectAdapter {
    private final ImageLoader.Options mOptions;
    private final int mItemSize;
    private final long mMinDuration;

    public MediaSelectAdapter(int itemSize, long minDuration) {
        super(R.layout.item_material_select_preview, itemSize);
        this.mItemSize = itemSize;
        this.mMinDuration = minDuration;
        mOptions = new ImageLoader.Options()
                .centerCrop()
                .skipMemoryCache(false);
    }

    @Override
    public void addChildClick(BaseViewHolder holder) {
        holder.addOnClickListener(R.id.v_touch);
    }
    /**
     * Convert.
     * 转换
     *
     * @param holder  the holder 持有
     * @param section the section 截面
     */
    @Override
    protected void convert(BaseViewHolder holder, MediaSection section) {
        MediaData mediaData = section.t;
        ImageView view = holder.getView(R.id.iv_material_cover);
        String path = AndroidVersionUtils.isAboveAndroid_Q() ? mediaData.getThumbPath() : "file://" + mediaData.getThumbPath();
        ImageLoader.loadUrl(mContext, path, view, mOptions);
        TextView tvMaterialType = holder.getView(R.id.tv_material_type);
        ImageView ivDuration = holder.getView(R.id.iv_duration);
        if (mediaData.getType() == MediaData.TYPE_VIDEO) {
            if (mediaData.getIsGif()) {
                if (tvMaterialType.getVisibility() == View.VISIBLE) {
                    tvMaterialType.setVisibility(View.INVISIBLE);
                }
                ivDuration.setVisibility(View.INVISIBLE);
            } else {
                if (tvMaterialType.getVisibility() != View.VISIBLE) {
                    tvMaterialType.setVisibility(View.VISIBLE);
                }
                ivDuration.setVisibility(View.VISIBLE);
                tvMaterialType.setText(FormatUtils.sec2Time((int) (mediaData.getDuration() / 1000)));
            }
        } else if (mediaData.getType() == MediaData.TYPE_PHOTO) {
            if (tvMaterialType.getVisibility() == View.VISIBLE) {
                tvMaterialType.setVisibility(View.INVISIBLE);
            }
            ivDuration.setVisibility(View.INVISIBLE);
        }
        TextView tvSelectedIndex = holder.getView(R.id.tv_selected_num);

        if (mediaData.isState()) {
            tvSelectedIndex.setBackgroundResource(R.drawable.bg_round_red);
            int position = mediaData.getPosition();
            if (position > 0) {
                String text = position + "";
                tvSelectedIndex.setText(text);
            } else {
                tvSelectedIndex.setText("1");
            }
        } else {
            tvSelectedIndex.setBackgroundResource(R.drawable.bg_annulus_white);
            tvSelectedIndex.setText("");
        }

        boolean canNotClickAble = !isClickAble(mediaData);
        View touchView = holder.getView(R.id.v_touch);
        if (canNotClickAble) {
            tvSelectedIndex.setVisibility(View.INVISIBLE);
            touchView.setVisibility(View.GONE);
        }else {
            tvSelectedIndex.setVisibility(View.VISIBLE);
            touchView.setVisibility(View.VISIBLE);
        }

        View mask = holder.getView(R.id.mask);
        if (canNotClickAble) {
            if (mask.getVisibility() != View.VISIBLE) {
                mask.setVisibility(View.VISIBLE);
                mask.setBackgroundResource(R.color.color_cc000000);
            }
        }else {
            mask.setVisibility(View.GONE);
        }
    }

    public boolean isClickAble(MediaData mediaData) {
        return mediaData.getType() == MediaData.TYPE_PHOTO || mMinDuration == -1 || (mediaData.getDuration() * 1000) >= mMinDuration;
    }
}
