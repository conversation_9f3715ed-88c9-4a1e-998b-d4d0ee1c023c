package com.meishe.myvideo.view;

import android.content.Context;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.meishe.myvideo.R;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiH<PERSON><PERSON>hou
 * @CreateDate :2021/3/2 15:42
 * @Description :识别字幕的后台执行视图
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class IdentifyCaptionView extends FrameLayout {
    private TextView mTvHint;
    private ImageView mIvClose;
    private EventListener mListener;

    public IdentifyCaptionView(@NonNull Context context) {
        this(context, null);
    }

    public IdentifyCaptionView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public IdentifyCaptionView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
        initListener();
    }

    private void initView() {
        setBackgroundColor(getResources().getColor(R.color.color_ff3a3c4c));
        setLayoutParams(new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, (int) getResources().getDimension(R.dimen.dp_px_200)));
        mTvHint = new TextView(getContext());
        mTvHint.setGravity(Gravity.CENTER_VERTICAL);
        mTvHint.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimension(R.dimen.sp_px_33));
        mTvHint.setTextColor(getResources().getColor(R.color.color_ffd1d1d1));
        LayoutParams textParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, (int) getResources().getDimension(R.dimen.dp_px_100));
        textParams.leftMargin = (int) getResources().getDimension(R.dimen.dp_px_64);
        textParams.bottomMargin = (int) getResources().getDimension(R.dimen.dp_px_20);
        textParams.gravity = Gravity.BOTTOM;
        addView(mTvHint, textParams);

        mIvClose = new ImageView(getContext());
        LayoutParams closeParams = new LayoutParams((int) getResources().getDimension(R.dimen.dp_px_100), (int) getResources().getDimension(R.dimen.dp_px_100));
        closeParams.rightMargin = (int) getResources().getDimension(R.dimen.dp_px_34);
        closeParams.bottomMargin = (int) getResources().getDimension(R.dimen.dp_px_20);
        closeParams.gravity = Gravity.BOTTOM | Gravity.END;
        mIvClose.setImageResource(R.mipmap.close_gray);
        int padding = (int) getResources().getDimension(R.dimen.dp_px_30);
        mIvClose.setPadding(padding, padding, padding, padding);
        addView(mIvClose, closeParams);
    }

    private void initListener() {
        mIvClose.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mListener != null) {
                    mListener.onDismiss();
                }
            }
        });
    }

    /**
     * 展示开始识别视图
     * Show identify
     */
    public void showIdentify() {
        mTvHint.setText(R.string.identifying_caption);
        mIvClose.setVisibility(VISIBLE);
    }

    /**
     * 展示完成识别视图
     * Show identify complete
     */
    public void showIdentifyComplete() {
        mTvHint.setText(R.string.identify_success_turn_to_caption);
        mIvClose.setVisibility(GONE);
    }

    /**
     * 设置监听
     * Sets listener
     *
     * @param listener the listener
     */
    public void setEventListener(EventListener listener) {
        mListener = listener;
    }

    public interface EventListener {
        void onDismiss();
    }
}
