package com.meishe.myvideo.view.interf;

import com.meishe.base.model.IBaseView;
import com.meishe.engine.bean.CurveAdjustData;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/8/5 19:26
 * @Description :关键帧View接口定义 Key Frame view interface definition.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface CurveKeyFrameView extends IBaseView {
    /**
     * On new data back
     * 首次加载数据返回
     *
     * @param list       the data list
     */
    void onDataBack(List<CurveAdjustData> list);
}
