package com.meishe.capturemodule.utils;

import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.net.Uri;
import android.os.Environment;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;

import com.meishe.base.bean.MediaData;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.MediaScannerUtil;
import com.meishe.base.utils.Utils;
import com.meishe.engine.util.WhiteList;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Enumeration;
import java.util.List;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * Created by admin on 2018-6-5.
 */

public class PathUtils {

    private static final String TAG = PathUtils.class.getName();

    private static String SDK_FILE_ROOT_DIRECTORY = "Fun_Cut" + File.separator;
    private static String RECORDING_DIRECTORY = "Record";
    private static String RECORD_SAVE_DIRECTORY = "DCIM" + File.separator + "Record";
    private static String AUDIO_RECORD_DIRECTORY = SDK_FILE_ROOT_DIRECTORY + "AudioRecord";
    private static String DOUYIN_RECORDING_DIRECTORY = SDK_FILE_ROOT_DIRECTORY + "DouYinRecord";
    private static String DOUYIN_CONVERT_DIRECTORY = SDK_FILE_ROOT_DIRECTORY + "DouYinConvert";
    private static String COVER_IMAGE_DIRECTORY = SDK_FILE_ROOT_DIRECTORY + "Cover";
    private static String PARTICLE_DIRECTORY = SDK_FILE_ROOT_DIRECTORY + "Particle";
    private static String CREASH_LOG_DIRECTORY = SDK_FILE_ROOT_DIRECTORY + "Log";
    private static String WATERMARK_CAF_DIRECTORY = SDK_FILE_ROOT_DIRECTORY + "WaterMark";
    private static String PICINPIC_DIRECTORY = SDK_FILE_ROOT_DIRECTORY + "PicInPic";
    private static String VIDEOCOMPILE_DIRECTORY = SDK_FILE_ROOT_DIRECTORY + "Compile";
    private static String CAPTURESCENE_RECORDING_DIRECTORY = SDK_FILE_ROOT_DIRECTORY + "CaptureScene";
    private static String BOOMRANG_RECORDING_DIRECTORY = SDK_FILE_ROOT_DIRECTORY + "BoomRang";
    private static String FLASH_EFFECT_RECORDING_DIRECTORY = SDK_FILE_ROOT_DIRECTORY + "FlashEffect";
    private static String SUPERZOOM_RECORDING_DIRECTORY = SDK_FILE_ROOT_DIRECTORY + "SuperZoom";
    private static String PHOTO_ALBUM_DIRECTORY = SDK_FILE_ROOT_DIRECTORY + "PhotoAlbum";

    private static String ASSET_DOWNLOAD_DIRECTORY = SDK_FILE_ROOT_DIRECTORY + "Asset";
    private static String ASSET_DOWNLOAD_DIRECTORY_FILTER = ASSET_DOWNLOAD_DIRECTORY + File.separator + "Filter";
    private static String ASSET_DOWNLOAD_DIRECTORY_THEME = ASSET_DOWNLOAD_DIRECTORY + File.separator + "Theme";
    private static String ASSET_DOWNLOAD_DIRECTORY_CAPTION = ASSET_DOWNLOAD_DIRECTORY + File.separator + "Caption";
    private static String ASSET_DOWNLOAD_DIRECTORY_ANIMATEDSTICKER = ASSET_DOWNLOAD_DIRECTORY + File.separator + "AnimatedSticker";
    private static String ASSET_DOWNLOAD_DIRECTORY_TRANSITION = ASSET_DOWNLOAD_DIRECTORY + File.separator + "Transition";
    private static String ASSET_DOWNLOAD_DIRECTORY_FONT = ASSET_DOWNLOAD_DIRECTORY + File.separator + "Font";
    private static String ASSET_DOWNLOAD_DIRECTORY_CAPTURE_SCENE = ASSET_DOWNLOAD_DIRECTORY + File.separator + "CaptureScene";
    private static String ASSET_DOWNLOAD_DIRECTORY_PARTICLE = ASSET_DOWNLOAD_DIRECTORY + File.separator + "Particle";
    private static String ASSET_DOWNLOAD_DIRECTORY_FACE_STICKER = ASSET_DOWNLOAD_DIRECTORY + File.separator + "FaceSticker";
    private static String ASSET_DOWNLOAD_DIRECTORY_CUSTOM_ANIMATED_STICKER = ASSET_DOWNLOAD_DIRECTORY + File.separator + "CustomAnimatedSticker";
    private static String ASSET_DOWNLOAD_DIRECTORY_FACE1_STICKER = ASSET_DOWNLOAD_DIRECTORY + File.separator + "Face1Sticker";
    private static String ASSET_DOWNLOAD_DIRECTORY_SUPER_ZOOM = ASSET_DOWNLOAD_DIRECTORY + File.separator + "Meicam";
    private static String ASSET_DOWNLOAD_DIRECTORY_ARSCENE = ASSET_DOWNLOAD_DIRECTORY + File.separator + "ArScene";
    private static String ASSET_DOWNLOAD_DIRECTORY_GIFCONVERT = ASSET_DOWNLOAD_DIRECTORY + File.separator + "GifConvert";
    private static String ASSET_DOWNLOAD_DIRECTORY_COMPOUND_CAPTION = ASSET_DOWNLOAD_DIRECTORY + File.separator + "CompoundCaption";
    private static String ASSET_DOWNLOAD_DIRECTORY_PHOTO_ALBUM = ASSET_DOWNLOAD_DIRECTORY + File.separator + "PhotoAlbum";
    private static String ASSET_DOWNLOAD_DIRECTORY_MIMO = ASSET_DOWNLOAD_DIRECTORY + File.separator + "mimo";
    private static String ASSET_DOWNLOAD_DIRECTORY_CAPTION_RICH_WORD = ASSET_DOWNLOAD_DIRECTORY + File.separator + "CaptionRichWord";
    private static String ASSET_DOWNLOAD_DIRECTORY_CAPTION_ANIMATION = ASSET_DOWNLOAD_DIRECTORY + File.separator + "CaptionAnimation";
    private static String ASSET_DOWNLOAD_DIRECTORY_CAPTION_IN_ANIMATION = ASSET_DOWNLOAD_DIRECTORY + File.separator + "CaptionInAnimation";
    private static String ASSET_DOWNLOAD_DIRECTORY_CAPTION_OUT_ANIMATION = ASSET_DOWNLOAD_DIRECTORY + File.separator + "CaptionOutAnimation";
    private static String ASSET_DOWNLOAD_DIRECTORY_CAPTION_BUBBLE = ASSET_DOWNLOAD_DIRECTORY + File.separator + "CaptionBubble";
    private static String ASSET_DOWNLOAD_DIRECTORY_ANIMATION_IN_BUBBLE = ASSET_DOWNLOAD_DIRECTORY + File.separator + "Animation/In";
    private static String ASSET_DOWNLOAD_DIRECTORY_ANIMATION_OUT_BUBBLE = ASSET_DOWNLOAD_DIRECTORY + File.separator + "Animation/Out";
    private static String ASSET_DOWNLOAD_DIRECTORY_ANIMATION_COMPANY_BUBBLE = ASSET_DOWNLOAD_DIRECTORY + File.separator + "Animation/Company";
    private static final String IMAGE_BACKGROUND_FOLDER = "imageBackground";

    private static final String LICENSE_FILE_FOLDER = SDK_FILE_ROOT_DIRECTORY + "License";

    public static void deleteFile(String filePath) {
        File file = new File(filePath);
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            if (files != null) {
                for (int i = 0; i < files.length; i++) {
                    File f = files[i];
                    deleteDirectoryFile(f);
                }
            }
            /*
             * 如要保留文件夹，只删除文件，请注释这行
             * To keep the folder and delete only the files, comment this line
             * */
            file.delete();
        } else if (file.exists()) {
            file.delete();
        }
    }

    public static void deleteDirectoryFile(File file) {
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            if (files != null) {
                for (int i = 0; i < files.length; i++) {
                    File f = files[i];
                    deleteDirectoryFile(f);
                }
            }
            /*
             * 如要保留文件夹，只删除文件，请注释这行
             * To keep the folder and delete only the files, comment this line
             * */
            //file.delete();
        } else if (file.exists()) {
            file.delete();
        }
    }

    public static String getDouYinRecordDir() {
        return getFolderDirPath(DOUYIN_RECORDING_DIRECTORY);
    }

    public static String getGifConvertDir() {
        return getFolderDirPath(ASSET_DOWNLOAD_DIRECTORY_GIFCONVERT);
    }

    public static String getDouYinRecordVideoPath() {
        String dstDirPath = getDouYinRecordDir();
        if (dstDirPath == null) {
            return null;
        }
        String fileName = getCharacterAndNumber() + ".mp4";
        return getFileDirPath(dstDirPath, fileName);
    }

    public static String getDouYinConvertDir() {
        return getFolderDirPath(DOUYIN_CONVERT_DIRECTORY);
    }

    public static String getLogDir() {
        return getFolderDirPath(CREASH_LOG_DIRECTORY);
    }

    public static String getWatermarkCafDirectoryDir() {
        return getFolderDirPath(WATERMARK_CAF_DIRECTORY);
    }

    /**
     * 获取画中画文件目录
     * Get PIP file directory
     * */
    public static String getPicInPicDirPath() {
        return getFolderDirPath(PICINPIC_DIRECTORY);
    }

    /**
     * 获取视频生成目录
     * Get video generation directory
     * */
    public static String getVideoCompileDirPath() {
        return getFolderDirPath(VIDEOCOMPILE_DIRECTORY);
    }

    /**
     * 获取影集文件目录
     * Get album file directory
     * */
    public static String getPhotoAblumDirPath() {
        return getFolderDirPath(PHOTO_ALBUM_DIRECTORY);
    }

    /**
     * 获取影集文件视频导出路径
     * Get album file video export path
     * */
    public static String getPhotoAlbumVideoPath() {
        String dstDirPath = getFolderDirPath(PHOTO_ALBUM_DIRECTORY);
        if (dstDirPath == null) {
            return null;
        }
        String fileName = getCharacterAndNumber() + ".mp4";
        return getFileDirPath(dstDirPath, fileName);
    }

    public static String getRecordVideoPath() {
        String dstDirPath = getRecordPath();
        if (dstDirPath == null) {
            return null;
        }
        String fileName = getCharacterAndNumber() + ".mp4";
        return getFileDirPath(dstDirPath, fileName);
    }

    /**
     * 获取录制路径
     * Gets record path.
     *
     * @return the record path
     */
    public static String getRecordPath() {
        return getRecordRootDirPath(RECORDING_DIRECTORY);
    }

    /**
     * 获取录制保存路径
     * Gets record save path.
     *
     * @return the record save path
     */
    public static String getRecordSavePath() {
        File dstFileDir = new File(Environment.getExternalStorageDirectory(), RECORD_SAVE_DIRECTORY);

        if (!dstFileDir.exists() && !dstFileDir.mkdirs()) {
            LogUtils.e("Failed to create file dir path--->" + RECORD_SAVE_DIRECTORY);
            return null;
        }
        return dstFileDir.getAbsolutePath();
    }

    /**
     * 保存录制的文件到相册
     * Save record file to dcim.
     */
    public static boolean saveRecordFileToDCIM(List<MediaData> mediaDataArrayList) {
        if (WhiteList.isNeedCopyCompileVideo()) {
            for (MediaData mediaData : mediaDataArrayList) {
                String path = mediaData.getPath();
                if (!FileUtils.isFileExists(path)) {
                    continue;
                }
                copyPrivateToDCIM(path, FileUtils.getFileName(path), mediaData.getDuration(), path.endsWith(".mp4"));
            }
        } else {
            File dstFileDir = new File(Environment.getExternalStorageDirectory(), RECORD_SAVE_DIRECTORY);
            if (!dstFileDir.exists() && !dstFileDir.mkdirs()) {
                LogUtils.e("Failed to create file dir path--->" + RECORD_SAVE_DIRECTORY);
                return false;
            }
            for (MediaData mediaData : mediaDataArrayList) {
                String path = mediaData.getPath();
                if (!FileUtils.isFileExists(path)) {
                    continue;
                }
                String destPath = dstFileDir.getAbsolutePath() + File.separator + FileUtils.getFileName(path);
                if (FileUtils.copy(path, destPath)) {
                    if (destPath.endsWith(".mp4")) {
                        MediaScannerUtil.scanFile(destPath, "video/mp4");
                    } else if (destPath.endsWith(".jpg")) {
                        MediaScannerUtil.scanFile(destPath, "image/jpg");
                    }
                    FileUtils.delete(path);
                }
            }
        }

        return true;
    }

    private static String copyPrivateToDCIM(final String filePath, final String fileName, final long duration, boolean isVideo) {
        Uri insertUri = null;
        try {
            String relativePath = RECORD_SAVE_DIRECTORY;
            long currTime = System.currentTimeMillis();
            ContentResolver resolver = Utils.getApp().getApplicationContext().getContentResolver();
            //设置文件参数到ContentValues中 Set file parameters to ContentValues
            ContentValues values = new ContentValues();
            //设置文件名 Set file name
            values.put(MediaStore.Video.Media.DISPLAY_NAME, fileName);
            //设置文件描述，这里以文件名代替
            // Set the file description, replacing it with a file name here
            values.put(MediaStore.Video.Media.DESCRIPTION, fileName);
            values.put(MediaStore.Video.Media.DATE_ADDED, currTime);
            values.put(MediaStore.Video.Media.DATE_MODIFIED, currTime);
            values.put(MediaStore.Video.Media.DATA, filePath);
            //注意：MediaStore.Images.Media.RELATIVE_PATH需要targetSdkVersion=29,
            //故该方法只可在Android10的手机上执行
            //Attention: MediaStore. Images. Media.RELATIVE_ PATH requires targetSdkVersion=29,
            // Therefore, this method can only be executed on Android 10 phones
            values.put(MediaStore.Video.Media.RELATIVE_PATH, relativePath);

            //EXTERNAL_CONTENT_URI代表外部存储器
            //insertUri表示文件保存的uri路径
            //EXTERNAL_ CONTENT_ URI represents external storage
            // InsertUri represents the uri path where the file is saved
            Uri external;
            //设置文件类型 Set file type
            if (isVideo) {
                values.put(MediaStore.Video.Media.MIME_TYPE, "video/mp4");
                values.put(MediaStore.Video.Media.DURATION, duration / 1000);
                //EXTERNAL_CONTENT_URI代表外部存储器
                // EXTERNAL_CONTENT_URI represents external storage
                external = MediaStore.Video.Media.EXTERNAL_CONTENT_URI;
            } else {
                values.put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg");
                //EXTERNAL_CONTENT_URI代表外部存储器
                // EXTERNAL_CONTENT_URI represents external storage
                external = MediaStore.Images.Media.EXTERNAL_CONTENT_URI;
            }

            insertUri = resolver.insert(external, values);

            InputStream ist = null;
            OutputStream ost = null;
            try {
                ist = new FileInputStream(new File(filePath));
                if (insertUri != null) {
                    ost = resolver.openOutputStream(insertUri);
                }
                if (ost != null) {
                    byte[] buffer = new byte[4096];
                    int byteCount = 0;
                    while ((byteCount = ist.read(buffer)) != -1) {
                        ost.write(buffer, 0, byteCount);
                    }
                    // write what you want
                }
            } catch (IOException e) {
                LogUtils.e(e);
            } finally {
                try {
                    if (ist != null) {
                        ist.close();
                    }
                    if (ost != null) {
                        ost.close();
                    }
                } catch (IOException e) {
                    LogUtils.e(e);
                }
            }
            FileUtils.delete(filePath);
        } catch (Exception e) {
            LogUtils.e(e);
        }
        return String.valueOf(insertUri);

    }

    public static String getCaptureSceneRecordVideoPath() {
        String dstDirPath = getFolderDirPath(CAPTURESCENE_RECORDING_DIRECTORY);
        if (dstDirPath == null) {
            return null;
        }
        String fileName = getCharacterAndNumber() + ".mp4";
        return getFileDirPath(dstDirPath, fileName);
    }

    /**
     * 获取boomrang的文件名称
     * Get the file name of boomrang
     *
     * @param endName The end name
     * @return the file name
     */
    public static String getBoomrangRecordingDirectory(String endName) {
        String dstDirPath = getFolderDirPath(BOOMRANG_RECORDING_DIRECTORY);
        if (dstDirPath == null) {
            return null;
        }
        String fileName = getCharacterAndNumber() + ".mp4";
        return getFileDirPath(dstDirPath, fileName);
    }

    /**
     * 获取flashEffect的文件名称
     * Get the file name of flashEffect
     *
     * @return the dir
     */
    public static String getFlashEffectRecordingDirectory() {
        String dstDirPath = getFolderDirPath(FLASH_EFFECT_RECORDING_DIRECTORY);
        if (dstDirPath == null) {
            return null;
        }
        String fileName = getCharacterAndNumber() + ".mp4";
        return getFileDirPath(dstDirPath, fileName);
    }

    public static String getPhotoAlbumPicturePath() {
        String dstDirPath = getFolderDirPath(PHOTO_ALBUM_DIRECTORY);
        if (dstDirPath == null) {
            return null;
        }
        String fileName = getCharacterAndNumber() + ".jpg";
        return getFileDirPath(dstDirPath, fileName);
    }

    /**
     * 获取superZoom的文件名称
     * <p>
     * Get the file name of superZoom
     */
    public static String getSuperZoomRecordingDirectory(String endName) {
        String dstDirPath = getFolderDirPath(SUPERZOOM_RECORDING_DIRECTORY);
        if (dstDirPath == null) {
            return null;
        }
        String fileName = getCharacterAndNumber() + ".mp4";
        return getFileDirPath(dstDirPath, fileName);
    }

    public static String getRecordPicturePath() {
        String dstDirPath = getRecordPath();
        if (dstDirPath == null) {
            return null;
        }
        String fileName = getCharacterAndNumber() + ".jpg";
        return getFileDirPath(dstDirPath, fileName);
    }

    public static String getCoverImagePath() {
        String dstDirPath = getFolderDirPath(COVER_IMAGE_DIRECTORY);
        if (dstDirPath == null) {
            return null;
        }
        String fileName = getCharacterAndNumber() + ".jpg";
        //String fileName = getFileNameByFilterName();
        return getFileDirPath(dstDirPath, fileName);
    }

    /**
     * 通过滤镜名生成路径
     * Get cover image path by filter name
     *
     * @param filterName the filter name
     * @param currentId the current id
     * @return the cover image path
     */
    public static String getCoverImagePath(String filterName, String currentId) {
        String dstDirPath = getFolderDirPath(COVER_IMAGE_DIRECTORY);
        if (dstDirPath == null) {
            return null;
        }
        //String fileName = getCharacterAndNumber( ) + ".jpg";
        String fileName = filterName + currentId + ".jpg";
        return getFileDirPath(dstDirPath, fileName);
    }


    public static String getParticleRecordPath() {
        String dstDirPath = getFolderDirPath(PARTICLE_DIRECTORY);
        if (dstDirPath == null) {
            return null;
        }
        String fileName = getCharacterAndNumber() + ".mp4";
        return getFileDirPath(dstDirPath, fileName);
    }

    public static String getAudioRecordFilePath() {
        return getFolderDirPath(AUDIO_RECORD_DIRECTORY);
    }

    public static String getCharacterAndNumber() {
        return String.valueOf(System.nanoTime());
    }

    /**
     * 获取文件名字通过滤镜和uuid生成
     * Get filter name by filter name and uuid
     *
     * @param filtername the filter name
     * @param currentId the uuid
     * @return the final filter name
     */
    public static String getFileNameByFilterName(String filtername, String currentId) {
        String name = filtername;
        String uuid = UUID.randomUUID().toString();
        return name + uuid;
    }

    public static String getFileDirPath(String dstDirPathToCreate, String fileName) {
        File file = new File(dstDirPathToCreate, fileName);
        if (file.exists()) {
            file.delete();
        }

        return file.getAbsolutePath();
    }

    public static String getRecordRootDirPath(String dstDirPathToCreate) {
        File dstFileDir = new File(Utils.getApp().getExternalFilesDir(null), dstDirPathToCreate);

        if (!dstFileDir.exists() && !dstFileDir.mkdirs()) {
            LogUtils.e("Failed to create file dir path--->" + dstDirPathToCreate);
            return null;
        }
        return dstFileDir.getAbsolutePath();
    }

    public static String getFolderDirPath(String dstDirPathToCreate) {
        File dstFileDir = new File(Environment.getExternalStorageDirectory(), dstDirPathToCreate);

        if (!dstFileDir.exists() && !dstFileDir.mkdirs()) {
            LogUtils.e("Failed to create file dir path--->" + dstDirPathToCreate);
            return null;
        }
        return dstFileDir.getAbsolutePath();
    }


    public static boolean unZipFile(String zipFile, String folderPath) {
        ZipFile zfile;
        try {
            /*
             * 转码为GBK格式，支持中文
             * Transcode to GBK format, support Chinese
             * */
            zfile = new ZipFile(zipFile);
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
        Enumeration zList = zfile.entries();
        ZipEntry ze ;
        byte[] buf = new byte[1024];
        while (zList.hasMoreElements()) {
            ze = (ZipEntry) zList.nextElement();
            /*
             * 列举的压缩文件里面的各个文件，判断是否为目录
             * To determine whether each file in the compressed file is a directory
             * */
            if (ze.isDirectory()) {
                String dirstr = folderPath + ze.getName();
                dirstr.trim();
                File f = new File(dirstr);
                f.mkdir();
                continue;
            }
            OutputStream os = null;
            FileOutputStream fos = null;

            File realFile = getRealFileName(folderPath, ze.getName());
            try {
                fos = new FileOutputStream(realFile);
            } catch (FileNotFoundException e) {
                return false;
            }
            os = new BufferedOutputStream(fos);
            InputStream is = null;
            try {
                is = new BufferedInputStream(zfile.getInputStream(ze));
            } catch (IOException e) {
                return false;
            }
            int readLen = 0;
            try {
                while ((readLen = is.read(buf, 0, 1024)) != -1) {
                    os.write(buf, 0, readLen);
                }
            } catch (IOException e) {
                return false;
            }
            try {
                is.close();
                os.close();
            } catch (IOException e) {
                return false;
            }
        }
        try {
            zfile.close();
        } catch (IOException e) {
            return false;
        }
        return true;
    }

    public static File getRealFileName(String baseDir, String absFileName) {
        absFileName = absFileName.replace("\\", "/");
        String[] dirs = absFileName.split("/");
        File ret = new File(baseDir);
        String substr = null;
        if (dirs.length > 1) {
            for (int i = 0; i < dirs.length - 1; i++) {
                substr = dirs[i];
                ret = new File(ret, substr);
            }

            if (!ret.exists()) {
                ret.mkdirs();
            }
            substr = dirs[dirs.length - 1];
            ret = new File(ret, substr);
            return ret;
        } else {
            ret = new File(ret, absFileName);
        }
        return ret;
    }

    public static String getFileNameNoExt(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int dot = filename.indexOf('.');
            int lastSeparator = filename.lastIndexOf('/');
            if ((dot > -1) && (dot < (filename.length()))) {
                return filename.substring(lastSeparator + 1, dot);
            }
        }
        return filename;
    }

    public static String getFileName(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int dot = filename.indexOf('.');
            int lastSeparator = filename.lastIndexOf('/');
            if ((dot > -1) && (dot < (filename.length()))) {
                return filename.substring(lastSeparator + 1);
            }
        }
        return filename;
    }

    public static int getAssetVersionWithPath(String path) {
        String[] strings = path.split("/");
        if (strings.length > 0) {
            String filename = strings[strings.length - 1];
            String[] parts = filename.split(".");
            if (parts.length == 3) {
                return Integer.parseInt(parts[1]);
            } else {
                return 1;
            }
        } else {
            return 1;
        }
    }

    public static long getFileModifiedTime(String filePath) {
        File file = new File(filePath);
        if (!file.exists()) {
            return 0;
        } else {
            return file.lastModified();
        }
    }

    public static String getCaptureSceneLocalFilePath() {
        File assetDownloadDirCaptureScene = new File(Environment.getExternalStorageDirectory(), ASSET_DOWNLOAD_DIRECTORY_CAPTURE_SCENE);
        if (!assetDownloadDirCaptureScene.exists()) {
            if (!assetDownloadDirCaptureScene.mkdirs()) {
                Log.e(TAG, "Failed to make asset download capture scene directory");
                return "";
            }
        }
        return assetDownloadDirCaptureScene.getPath();
    }

    public static String getMimoCacheFolderPath(Context context) {
        File folder = new File(context.getCacheDir(), "mimo");
        if (!folder.exists()) {
            folder.mkdir();
        }
        return folder.getAbsolutePath();
    }

    public static String getMimoPreviewVideoPath(Context context, String url) {
        if (TextUtils.isEmpty(url) || (!url.contains("/")) || (!url.endsWith(".mp4"))) {
            return null;
        }
        String fileName = url.substring(url.lastIndexOf("/"));
        return getMimoCacheFolderPath(context) + fileName;
    }


    public static String getColorPath(Context context) {
        File dataDir = context.getApplicationContext().getExternalFilesDir(null);
        if (dataDir != null) {
            String folderPath = dataDir.getAbsolutePath() + File.separator + IMAGE_BACKGROUND_FOLDER;

            File folder = new File(folderPath);
            if (!folder.exists()) {
                folder.mkdir();
            }
            return folderPath;
        }
        return null;
    }


    public static String getLicenseFileFolder() {
        String dstDirPath = getFolderDirPath(LICENSE_FILE_FOLDER);
        if (dstDirPath == null) {
            return null;
        }
        return dstDirPath;
    }

}
