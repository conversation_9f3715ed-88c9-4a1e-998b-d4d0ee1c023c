<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_music_list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="invisible"
        />

    <TextView
        android:id="@+id/tv_nothing"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_px_285"
        android:drawableTop="@mipmap/icon_no_music"
        android:drawablePadding="@dimen/dp_px_21"
        android:text="@string/noneMusic"
        android:textAllCaps="false"
        android:textColor="@color/white_5"
        android:textSize="@dimen/sp_px_36" />
</FrameLayout>