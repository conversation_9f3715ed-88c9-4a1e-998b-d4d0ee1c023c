package com.meishe.engine.observer;

import android.graphics.Bitmap;

import com.meicam.sdk.NvsTimeline;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/5/18 15:11
 * @Description :编辑Engine的回调观察者 The observer of engine callback
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public abstract class EngineCallbackObserver {
    private String mCallbackFromTag;

    /**
     * 设置当前回调来自页面的标记
     * Sets the tag for the callback from the page
     *
     * @param callbackFromTag the callback from tag 标记
     */
    void setCallbackFromTag(String callbackFromTag) {
        this.mCallbackFromTag = callbackFromTag;
    }

    /**
     * 获取当前回调来自页面的标记
     * Sets the tag for the callback from the page
     *
     * @return the callback from tag 标记
     */
    public String getCallbackFromTag() {
        return mCallbackFromTag;
    }

    /**
     * 是否是活跃的，如果是活跃的则会调用所需的回调接口，否则不回调
     * If it is active, the required callback interface will be called, otherwise no callback will be given
     *
     * @return true is active 活跃则有效，false not 不活跃则无效
     */
    public abstract boolean isActive();

    /**
     * 导出中
     * On compile progress
     *
     * @param nvsTimeline the nvsTimeline 时间线
     * @param progress    the progress 进度
     */
    public void onCompileProgress(NvsTimeline nvsTimeline, int progress) {

    }

    /**
     * 导出完成
     * On compile finished
     *
     * @param nvsTimeline the nvsTimeline 时间线
     */
    public void onCompileFinished(NvsTimeline nvsTimeline) {

    }


    /**
     * 导出完成
     * On compile complete
     *
     * @param nvsTimeline the nvsTimeline 时间线
     * @param isCanceled  true cancel 取消，false 成功
     */
    public void onCompileCompleted(NvsTimeline nvsTimeline, boolean isCanceled) {

    }

    /**
     * 导出失败
     * On compile failed
     *
     * @param nvsTimeline the nvsTimeline 时间线
     */
    public void onCompileFailed(NvsTimeline nvsTimeline) {

    }

    /**
     * 抓图成功返回
     * on image grabbed  arrived
     *
     * @param bitmap the bitmap图片数据
     * @param time   the timestamp 获取到的图像时间戳
     */
    public void onImageGrabbedArrived(Bitmap bitmap, long time) {

    }

    /**
     * 时间线播放预先加载完成
     * on playback preload completion
     *
     * @param nvsTimeline the nvsTimeline 时间线
     */
    public void onPlaybackPreloadingCompletion(NvsTimeline nvsTimeline) {

    }

    /**
     * 时间线播放停止
     * On playback stopped
     *
     * @param nvsTimeline the nvsTimeline 时间线
     */
    public void onPlaybackStopped(NvsTimeline nvsTimeline) {

    }

    /**
     * 时间线播放到结尾
     * On playback end
     *
     * @param nvsTimeline the nvsTimeline 时间线
     */
    public void onPlaybackEOF(NvsTimeline nvsTimeline) {

    }

    /**
     * 时间线播放的当前位置
     * On current playback  timestamp
     *
     * @param nvsTimeline the nvsTimeline 时间线
     * @param timestamp   the playing timestamp 当前的时间戳
     */
    public void onPlaybackTimelinePosition(NvsTimeline nvsTimeline, long timestamp) {

    }

    /**
     * 流媒体引擎状态改变
     * On streaming engine state changed
     */
    public void onStreamingEngineStateChanged(int state) {

    }

    /**
     * 当播放的首帧到来的时候
     * On first video frame presented
     */
    public void onFirstVideoFramePresented(NvsTimeline nvsTimeline) {

    }

    /**
     * seekTimeline 回调
     * On seeking timeline position callback
     *
     * @param nvsTimeline timeline
     * @param timeStamp   时间
     */
    public void onSeekingTimelinePosition(NvsTimeline nvsTimeline, long timeStamp) {

    }

    /**
     * On finish asset package installation
     *
     * @param packageId   包id package id
     * @param filePath    文件路径 file path
     * @param packageType 包类型 package type
     * @param error       错误类型 error
     */
    public void onFinishAssetPackageInstallation(String packageId, String filePath, int packageType, int error) {

    }

    /**
     * On finish asset package upgrading
     *
     * @param packageId   包id package id
     * @param filePath    文件路径 file path
     * @param packageType 包类型 package type
     * @param error       错误类型 error
     */
    public void onFinishAssetPackageUpgrading(String packageId, String filePath, int packageType, int error) {

    }
}
