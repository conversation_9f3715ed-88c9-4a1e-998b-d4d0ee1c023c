package com.meishe.user.view;

import android.content.Context;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.meishe.base.utils.KeyboardUtils;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.third.pop.XPopup;
import com.meishe.third.pop.core.CenterPopupView;
import com.meishe.user.R;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/16 19:57
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class DownloadConfirmPop extends CenterPopupView {
    private IUserPlugin.OnEventListener<Object> mListener;
    private int hintText;
    private int confirmText;
    private int cancelText;

    public static class Builder {
        int hintText;
        int confirmText;
        int cancelText;

        public Builder setHintText(int hintText) {
            this.hintText = hintText;
            return this;
        }

        public Builder setConfirmText(int confirmText) {
            this.confirmText = confirmText;
            return this;
        }

        public Builder setCancelText(int cancelText) {
            this.cancelText = cancelText;
            return this;
        }
        public DownloadConfirmPop build(Context context, IUserPlugin.OnEventListener<Object> listener){
            DownloadConfirmPop pop = create(context, listener);
            pop.setConfirmText(confirmText);
            pop.setCancelText(cancelText);
            pop.setHintText(hintText);
            return pop;
        }
    }

    private DownloadConfirmPop(@NonNull Context context, IUserPlugin.OnEventListener<Object> listener) {
        super(context);
        mListener = listener;
    }

    private static DownloadConfirmPop create(Context context, IUserPlugin.OnEventListener<Object> listener) {
        return ((DownloadConfirmPop) new XPopup
                .Builder(context)
                .asCustom(new DownloadConfirmPop(context, listener)));
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_download_confirm;
    }

    @Override
    protected int getPopupWidth() {
        return (int) (ScreenUtils.getScreenWidth() * 19 / 20f);
    }


    @Override
    protected void onDismiss() {
        super.onDismiss();
        KeyboardUtils.hideSoftInput(this);
    }


    @Override
    protected void onCreate() {
        super.onCreate();
        TextView titleView = findViewById(R.id.tv_title);
        if (hintText > 0) {
            titleView.setText(hintText);
        }
        TextView conformView = findViewById(R.id.tv_confirm);
        if (confirmText > 0) {
            conformView.setText(confirmText);
        }
        conformView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mListener != null) {
                    mListener.onConfirm(null);
                }
                dismiss();
            }
        });
        TextView cancelView = findViewById(R.id.tv_cancel);
        if (cancelText > 0) {
            cancelView.setText(cancelText);
        }
        cancelView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mListener != null) {
                    mListener.onCancel();
                }
                dismiss();
            }
        });
    }

    public void setHintText(int hintText) {
        this.hintText = hintText;
    }

    public void setConfirmText(int confirmText) {
        this.confirmText = confirmText;
    }

    public void setCancelText(int cancelText) {
        this.cancelText = cancelText;
    }
}
