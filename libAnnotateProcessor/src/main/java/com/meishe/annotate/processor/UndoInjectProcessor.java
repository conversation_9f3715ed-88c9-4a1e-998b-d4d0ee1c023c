package com.meishe.annotate.processor;

import com.meishe.annotation.Undo;

import java.io.IOException;
import java.io.Writer;
import java.util.List;
import java.util.Set;

import javax.annotation.processing.AbstractProcessor;
import javax.annotation.processing.Filer;
import javax.annotation.processing.ProcessingEnvironment;
import javax.annotation.processing.RoundEnvironment;
import javax.annotation.processing.SupportedAnnotationTypes;
import javax.annotation.processing.SupportedSourceVersion;
import javax.lang.model.SourceVersion;
import javax.lang.model.element.Element;
import javax.lang.model.element.ExecutableElement;
import javax.lang.model.element.TypeElement;
import javax.lang.model.element.VariableElement;
import javax.lang.model.util.Elements;
import javax.tools.JavaFileObject;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/8/22 14:03
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
@SupportedAnnotationTypes({"com.meishe.annotation.Undo"})
@SupportedSourceVersion(SourceVersion.RELEASE_7)
public class UndoInjectProcessor extends AbstractProcessor {

   private Filer mFiler;
   private Elements mElementUtils;

   @Override
   public synchronized void init(ProcessingEnvironment processingEnv) {
      super.init(processingEnv);
      //初始化我们需要的基础工具
      mFiler = processingEnv.getFiler();
      mElementUtils = processingEnv.getElementUtils();
   }

   @Override
   public boolean process(Set<? extends TypeElement> set, RoundEnvironment roundEnvironment) {
      // 遍历所有注解元素
      for (Element annotatedElement : roundEnvironment.getElementsAnnotatedWith(Undo.class)) {
         try {
            analysisAnnotated(annotatedElement);
         }catch (Exception e) {
            e.printStackTrace();
         }
      }
      return false;
   }

   /**
    * 生成java文件
    * @param classElement 注解
    */
   private void analysisAnnotated(Element classElement) {
      Undo annotation = classElement.getAnnotation(Undo.class);
      String packageName = mElementUtils.getPackageOf(classElement).getQualifiedName().toString();
      String className = classElement.getEnclosingElement().getSimpleName().toString();
      String functionName = classElement.getSimpleName().toString();
      String[] redoParam = getMethodParam((ExecutableElement) classElement);
      String name = annotation.className();
      String[] param = annotation.param();
      String[] undoParam = param;
      String operatorParam = redoParam[0];

      String function = annotation.function();
      StringBuilder builder = new StringBuilder();
      builder.append("package ").append(packageName).append(";\n\n")
              .append("import com.meishe.engine.bean.*;\n")
              .append("import com.meishe.engine.command.Command;\n")
              .append("import java.io.Serializable;\n")
              .append("import ").append(packageName).append(".").append(className).append(";\n")
              .append("public class ")
              .append(name)
              .append(" implements Command, Serializable")
              .append(" {\n\n");

      createConstructor(builder, name, new String[]{"String|tag", "UndoParam|undoParam"}, "");

      declareVar(builder, new String[]{"String|tag"}, "");
      declareVar(builder, new String[]{"int|index"}, "");
      declareVar(builder, new String[]{"RedoParam|redoParam"}, "");
      declareVar(builder, new String[]{"UndoParam|undoParam"}, "");
      builder.append("\n");
      createSeterAndGeter(builder, new String[]{"String|tag"}, "");
      createSeterAndGeter(builder, new String[]{"int|index"}, "");
      createSeterAndGeter(builder, new String[]{"RedoParam|redoParam"}, "");
      createSeterAndGeter(builder, new String[]{"UndoParam|undoParam"}, "");

      StringBuilder methodParamsForInvoke = new StringBuilder();
      methodParamsForInvoke.append("operator,");
      for (int i = 1; i < redoParam.length -1; i++) {
         String paramItem = redoParam[i];
         String[] split = paramItem.split("\\|");
         methodParamsForInvoke.append("this.redoParam.").append(split[1]);
         if (i != redoParam.length - 1) {
            methodParamsForInvoke.append(", ");
         }
      }
      methodParamsForInvoke.append("false");
      String[] operatorParamSplit = operatorParam.split("\\|");
      builder.append("\t@Override\n").append("\tpublic void doIt() {\n")
              .append("\t\t").append(operatorParamSplit[0]).append(" operator = ")
              .append(className).append(".getItByTag(").append("this.tag);\n")
              .append("\t\tif(operator == null){return;}\n")
              //.append("\t\tthis.redoParam.").append(operatorParamSplit[1]).append(" = ").append("operator;\n")
              .append("\t\t").append(className).append(".").append(functionName).append("(").append(methodParamsForInvoke).append(");\n");
      builder.append("\t}\n");

      methodParamsForInvoke = new StringBuilder();
      methodParamsForInvoke.append("operator");
      if (undoParam.length != 1 || (!undoParam[0].isEmpty())) {
         methodParamsForInvoke.append(",");
      }
      if (undoParam.length == 1 && undoParam[0].isEmpty()) {
      } else {
         for (int i = 0; i < undoParam.length; i++) {
            String paramItem = undoParam[i];
            String[] split = paramItem.split("\\|");
            if (split.length == 2) {
               methodParamsForInvoke.append("this.undoParam.").append(split[1]);
            }
            if (i != undoParam.length - 1) {
               methodParamsForInvoke.append(",");
            }
         }
      }
      builder.append("\t@Override\n").append("\tpublic void undo() {\n")
              .append("\t\t").append(operatorParamSplit[0]).append(" operator = ")
              .append(className).append(".getItByTag(").append("this.tag);\n")
              .append("\t\tif(operator == null){return;}\n")
              .append("\t\t").append(className).append(".").append(function).append("(").append(methodParamsForInvoke).append(");\n");
      builder.append("\t}\n");

      //去掉第一个参数和最后一个参数，第一个参数和最后一个参数用不到
      String[] newRedoParam = new String[redoParam.length - 2];
      System.arraycopy(redoParam, 1, newRedoParam, 0, redoParam.length - 2);
      createInnerClass(builder, "RedoParam", newRedoParam);

      createInnerClass(builder, "UndoParam", undoParam);

      // close class
      builder.append("}\n");

      try { // write the file
         StringBuilder stringBuilder = new StringBuilder();
         stringBuilder.append(packageName).append(".").append(name);
         JavaFileObject source = mFiler.createSourceFile(stringBuilder.toString());
         Writer writer = source.openWriter();
         writer.write(builder.toString());
         writer.flush();
         writer.close();
      } catch (IOException e) {
         // Note: calling e.printStackTrace() will print IO errors
         // that occur from the file already existing after its first run, this is normal
      }
   }

   private String[] getMethodParam(ExecutableElement methodElement) {
      List<? extends VariableElement> parameters = methodElement.getParameters();
      String[] result = new String[parameters.size()];
      for (int index = 0; index < parameters.size(); index++) {
         VariableElement parameter = parameters.get(index);
         StringBuilder stringBuilder = new StringBuilder();
         stringBuilder.append(parameter.asType()).append("|").append(parameter.getSimpleName());
         result[index] = stringBuilder.toString();
      }
      return result;
   }

   private void createInnerClass(StringBuilder builder, String className, String[] param) {
      builder.append("\tpublic static class ").append(className).append(" implements Serializable ").append("{\n");
      //创建构造方法
      createConstructor(builder, className, param, "\t");
      createMethod(builder, "setParam", param, "\t");

      if (param.length == 1 && param[0].isEmpty()) {
      } else {
         for (int i = 0; i < param.length; i++) {
            String paramItem = param[i];
            declareVar(builder, new String[]{paramItem}, "\t");
         }
      }

      builder.append("\n");

      //创建get方法和set方法
      if (param.length == 1 && param[0].isEmpty()) {
      } else {
         for (int i = 0; i < param.length; i++) {
            String paramItem = param[i];
            createSeterAndGeter(builder, new String[]{paramItem}, "\t");
         }
      }
      builder.append("\t}\n");
   }

   private void createConstructor(StringBuilder builder, String className, String[] param, String perfix) {
      builder.append(perfix).append("\tpublic ").append(className).append(" (");
      for (int i = 0; i < param.length; i++) {
         String paramItem = param[i];
         String[] split = paramItem.split("\\|");
         if (split.length == 2) {
            builder.append(split[0]).append(" ").append(split[1]);
         }
         if (i != param.length - 1) {
            builder.append(", ");
         }
      }
      builder.append(")").append("{\n");
      for (int i = 0; i < param.length; i++) {
         String paramItem = param[i];
         String[] split = paramItem.split("\\|");
         if (split.length == 2) {
            builder.append(perfix).append("\t\tthis.").append(split[1]).append("= ").append(split[1]).append(";\n");
         }
      }
      builder.append(perfix).append("\t}\n");
   }

   private void createMethod(StringBuilder builder, String methodName, String[] param, String perfix) {
      builder.append(perfix).append("\tpublic void ").append(methodName).append(" (");
      for (int i = 0; i < param.length; i++) {
         String paramItem = param[i];
         String[] split = paramItem.split("\\|");
         if (split.length == 2) {
            builder.append(split[0]).append(" ").append(split[1]);
         }
         if (i != param.length - 1) {
            builder.append(", ");
         }
      }
      builder.append(")").append("{\n");
      for (int i = 0; i < param.length; i++) {
         String paramItem = param[i];
         String[] split = paramItem.split("\\|");
         if (split.length == 2) {
            builder.append(perfix).append("\t\tthis.").append(split[1]).append("= ").append(split[1]).append(";\n");
         }
      }
      builder.append(perfix).append("\t}\n");
   }

   private void createSeterAndGeter(StringBuilder root, String[] strings, String perfix) {
      if (root == null || strings== null || strings.length == 0) {
         return;
      }
      StringBuilder builder = new StringBuilder();
      for (int index = 0; index < strings.length; index++) {
         String param = strings[index];
         String[] split = param.split("\\|");
         String type = split[0];
         if (type.endsWith("...")) {
            type = type.replaceAll("\\.\\.\\.", "[]");
         }
         String name = split[1];
         String methodName = name.substring(0, 1).toUpperCase() + name.substring(1);

         builder.append(perfix);
         builder.append("\tpublic void ").append("set").append(methodName).append("(");
         builder.append(type).append(" ").append(name);
         builder.append("){ this.").append(name).append(" = ").append(name).append(";}\n\n");

         builder.append(perfix);
         builder.append("\tpublic ").append(type).append(" get").append(methodName).append("(");
         builder.append("){ return ").append("this.").append(name).append(";}\n\n");
      }
      root.append(builder);
   }

   private void declareVar(StringBuilder root, String[] strings, String perfix) {
      if (root == null || strings== null || strings.length == 0) {
         return;
      }
      StringBuilder builder = new StringBuilder();
      for (int index = 0; index < strings.length; index++) {
         String param = strings[index];
         String[] split = param.split("\\|");
         String type = split[0];
         if (type.endsWith("...")) {
            type = type.replaceAll("\\.\\.\\.", "[]");
         }
         String name = split[1];
         builder.append(perfix);
         builder.append("\tprivate ").append(type).append(" ").append(name).append(";\n");
      }
      root.append(builder);
   }


}
