package com.meishe.myvideo.activity.iview;

import com.meishe.base.model.IBaseView;
import com.meishe.engine.asset.bean.AssetList;
import com.meishe.engine.bean.AnimationData;
import com.meishe.engine.bean.MeicamKeyFrame;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.myvideo.ui.bean.LineRegionClip;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2020/12/17 20:23
 * @Description :编辑页面View
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface DraftEditView extends IBaseView {
    /**
     * 显示素材不可用弹窗
     * Show unavailable pop.
     *
     * @param data          the data
     * @param isForTemplate the is for template
     */
    void showUnavailablePop(AssetList data, boolean isForTemplate);

    /**
     * 进入导出模板视图
     * Go to export template view.
     */
    void goToExportTemplateView();

    /**
     * 进入导出视频视图
     * Go to export video view.
     */
    void goToExportVideoView();

    /**
     * 添加视频片段集合
     * Add  video clip list
     *
     * @param videoClipList 视频片段集合
     * @param trackIndex    轨道索引
     * @param seekTime      the seek time
     */
    void onAddVideoClip(List<MeicamVideoClip> videoClipList, int trackIndex, long seekTime);

    /**
     * 添加视频片段
     * Add  video clip
     *
     * @param videoClip 视频片段
     */
    void onAddVideoClip(MeicamVideoClip videoClip);


    /**
     * On clip changed to sub track.
     * 主轨道变到子轨道
     *
     * @param removedClip the removed clip 删除的clip
     * @param addedClip   the added clip 添加的clip
     */
    void onClipChangedToSubTrack(MeicamVideoClip removedClip, MeicamVideoClip addedClip);

    /**
     * On clip changed to main track.
     * 子轨道变到主轨道
     *
     * @param removedClip the removed clip 删除的clip
     * @param addedClip   the added clip 添加的clip
     */
    void onClipChangedToMainTrack(MeicamVideoClip removedClip, MeicamVideoClip addedClip);

    /**
     * 检查主轨道是否补黑的结果
     * On check main track result
     *
     * @param videoClip main track clip 主轨道片段
     * @param state     DELETE_HOLDER = 0 delete it,ADD_HOLDER = 1 add it ,CHANGE_HOLDER = 2 change it
     */
    void onCheckTrackResult(MeicamVideoClip videoClip, int state);

    /**
     * 主轨道线图变化了
     * On main track line region view change
     *
     * @param captionRegion         caption line region
     * @param compoundCaptionRegion compound caption line region
     * @param stickerRegion         sticker line region
     * @param pipRegion             pip region
     */
    void onMainTrackLineRegionChange(List<LineRegionClip> captionRegion, List<LineRegionClip> compoundCaptionRegion,
                                     List<LineRegionClip> stickerRegion, List<LineRegionClip> pipRegion);

    /**
     * 定格完成
     * On freeze frame complete
     *
     * @param isPip           true is pip ,false not 真则是画中画，假则不是
     * @param trackIndex      the track index 轨道索引
     * @param preVideoClip    the pre-video clip 定格处理后分割后的前片段
     * @param postVideoClip   the post-video clip 定格处理后分割后的后片段
     * @param insertVideoClip the insert video clip 定格处理后 要插入到前后片段之间的片段
     */
    void onFreezeFrameComplete(boolean isPip, int trackIndex, MeicamVideoClip preVideoClip, MeicamVideoClip postVideoClip,
                               MeicamVideoClip insertVideoClip);

    /**
     * 添加关键帧
     * On add key frame
     *
     * @param keyFrame    the key frame 关键帧
     * @param isMainTrack true is main track 主轨道，false not 子轨道
     */
    void onAddKeyFrame(MeicamKeyFrame keyFrame, boolean isMainTrack);

    /**
     * 智能抠像进度
     * On smart keyer progress
     *
     * @param progress the progress 进度
     */
    void onSmartKeyerProgress(int progress);

    /**
     * 智能抠像完成
     * On smart keyer complete
     *
     * @param success true complete完成，false not 没有完成
     */
    void onSmartKeyerComplete(boolean success);

    /**
     * 更新转场
     * Update transition by video clip duration.
     *
     * @param clipIndex the clip index
     */
    @Deprecated
    void updateTransitionByVideoClipDuration(int clipIndex);

    /**
     * 当删除视频转场的时候
     * On delete video clip transition
     *
     * @param index the transition index
     */
    void onDeleteTransition(int index);

    /**
     * 当删除视频片段的时候
     * On delete video clip
     *
     * @param videoClip the video clip
     */
    void onDeleteVideoClip(MeicamVideoClip videoClip);

    /**
     * On replace video clip finish.
     * 替换video clip 完成
     *
     * @param replacedVideoClip the replaced video clip
     */
    void onReplaceVideoClipFinish(MeicamVideoClip replacedVideoClip);

    /**
     * Sets cover.
     * 设置封面
     * @param imagePath the image path
     */
    void setCover(String imagePath);

    /**
     * Refresh cover view.
     * 刷新封面视图
     *
     * @param animationData the animation data 动画数据
     */
    void refreshCoverView(AnimationData animationData);
}
