package com.meishe.engine.asset.bean;


import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.meishe.base.utils.GsonUtils;
import com.meishe.base.utils.Utils;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.engine.db.AssetEntity;
import com.meishe.engine.interf.IBaseInfo;

/**
 * 资源信息实体类，和网络资源具有对应关系
 * Resource information entity class, and NVAsset has a corresponding relationship
 */
public class AssetInfo extends BaseInfo {
    public static final int ASSET_THEME = 1;
    public static final int ASSET_FILTER = 2;
    public static final int ASSET_CAPTION_STYLE = 3;
    public static final int ASSET_ANIMATED_STICKER = 4;
    public static final int ASSET_VIDEO_TRANSITION = 5;
    public static final int ASSET_FONT = 6;
    public static final int ASSET_CAPTURE_SCENE = 8;
    public static final int ASSET_PARTICLE = 9;
    public static final int ASSET_FACE_STICKER = 10;
    public static final int ASSET_FACE1_STICKER = 11;
    /**
     * 注意和ASSET_CUSTOM_STICKER_PACKAGE作区分，本类型是自定义贴纸所需特效包类型
     * Note the difference between ASSET_CUSTOM_STICKER_PACKAGE
     * this type is the special effect package type required for custom stickers
     */
    public static final int ASSET_CUSTOM_STICKER_PACKAGE = 12;
    public static final int ASSET_SUPER_ZOOM = 13;
    public static final int ASSET_FACE_BUNDLE_STICKER = 14;
    public static final int ASSET_AR_SCENE_FACE = 15;
    public static final int ASSET_COMPOUND_CAPTION = 16;
    public static final int ASSET_PHOTO_ALBUM = 17;
    public static final int ASSET_EFFECT_FRAME = 18;
    public static final int ASSET_EFFECT_DREAM = 19;
    public static final int ASSET_EFFECT_LIVELY = 20;
    public static final int ASSET_EFFECT_SHAKING = 21;
    /**
     * 注意和ASSET_CUSTOM_STICKER_PACKAGE作区分，本类型是自定义贴纸类型
     * Note the difference between ASSET_CUSTOM_STICKER_PACKAGE,
     * This type is a custom sticker type
     */
    public static final int ASSET_CUSTOM_STICKER = 22;

    /**
     * 自定义贴纸
     * Custom sticker
     */
    public static final int ASSET_ANIMATED_STICKER_CUSTOM = 22;
    /**
     * 效果 模糊 马赛克
     * Effect blur Mosaic
     */
    public static final int ASSET_WATER_EFFECT = 23;
    /**
     * 效果 水印
     * Effect of watermark
     */
    public static final int ASSET_WATER = 24;

    /**
     * 3D转场
     * 3 d transitions
     */
    public static final int ASSET_VIDEO_TRANSITION_3D = 25;
    /**
     * 特效转场
     * The special effects transitions
     */
    public static final int ASSET_VIDEO_TRANSITION_EFFECT = 26;


    public static final int ASSET_ANIMATION_IN = 27;
    public static final int ASSET_ANIMATION_OUT = 28;
    public static final int ASSET_ANIMATION_GROUP = 29;
    public static final int ASSET_CUSTOM_CAPTION_FLOWER = 30;
    public static final int ASSET_CUSTOM_CAPTION_BUBBLE = 31;
    public static final int ASSET_CUSTOM_CAPTION_ANIMATION_IN = 32;
    public static final int ASSET_CUSTOM_CAPTION_ANIMATION_OUT = 33;
    public static final int ASSET_CUSTOM_CAPTION_ANIMATION_COMBINATION = 34;

    /**
     * 曲线变速
     * speed curve
     */
    public static final int ASSET_CHANGE_SPEED_CURVE = 35;

    /**
     * 其它特效
     * Other effect
     */
    public static final int ASSET_EFFECT_OTHER = 36;

    /**
     * 贴纸动画
     */
    public static final int ASSET_STICKER_ANIMATION_IN = 37;
    public static final int ASSET_STICKER_ANIMATION_OUT = 38;
    public static final int ASSET_STICKER_ANIMATION_COMP = 39;
    /**
     * 美型
     * The capture shape
     */
    public static final int ASSET_PACKAGE_TYPE_FACE_MESH = 40;
    /**
     * 美型 wrap
     */
    public static final int ASSET_PACKAGE_TYPE_FACE_WARP = 41;

    /**
     * 背景 background
     */
    public static final int ASSET_PACKAGE_BACKGROUND = 42;
    /**
     * 模版 template
     */
    public static final int ASSET_PACKAGE_TEMPLATE = 43;
    public static final int NV_CATEGORY_ID_ALL = 0;
    public static final int NV_CATEGORY_ID_DOUYINFILTER = 7;
    public static final int NV_CATEGORY_ID_CUSTOM = 20000;
    public static final int NV_CATEGORY_ID_PARTICLE_TOUCH_TYPE = 2;
    /**
     * 下载失败的下载进度
     * Download failed download progress
     */
    public static final int DOWNLOAD_FAILED_PROGRESS = 101;
    /**
     * 英文名称
     * English name
     */
    private String enName;
    private String id;
    private String packageId;
    private String effectId;
    private int effectMode;
    private String assetPath;
    private String downloadUrl;
    private int assetSize;
    private String minAppVersion;
    private int downloadProgress = -1;
    private boolean hadDownloaded;
    /**
     * （最新）资源版本号
     * The asset version (latest)
     */
    private int version;
    /**
     * 本地已经下载的（如果有的话）资源版本号
     * The local version (old)
     */
    private int localVersion;
    /**
     * 资源支持的比例
     * The asset supported ratio
     */
    private int supportedAspectRatio;

    /**
     * 默认支持的比例
     * Default aspect ratio
     */
    private int defaultAspectRatio;

    /**
     * 资源支持的比例标记
     * The asset supported ratio flag
     */
    private int ratioFlag;

    /**
     *  预览样例路径
     * Url of sample for preview
     *
     */
    private String previewSampleUrl;

    /**
     * 信息文件路径
     * Url of information
     *
     */
    private String infoUrl;

    /**
     * 拥有者， 如果是1，是当前拥有者
     * Possessor.
     * If it is 1, it is the current owner.
     */
    private int possessor;

    private ExtendedInfo extendedInfo;


    private boolean isNewData = false;
    /**
     * 是 post package
     * is Post package
     */
    private int isPostPackage = 1;

    /**
     * 授权路径
     * The lic path
     */
    private String licPath;
    /**
     * 用于判读是否是自制素材
     * Judge whether it is self-made material
     */
    private int subType;

    /**
     * 类别
     * The category
     */
    private int category;

    public int getSubType() {
        return subType;
    }

    public void setSubType(int subType) {
        this.subType = subType;
    }

    public String getLicPath() {
        return licPath;
    }

    public void setLicPath(String licPath) {
        this.licPath = licPath;
    }

    @Override
    public String getName() {
        return Utils.isZh() ? super.getName() : getEnName();
    }


    public String getEnName() {
        if (TextUtils.isEmpty(enName)) {
            return super.getName();
        }
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setPackageId(String packageId) {
        this.packageId = packageId;
    }

    @Override
    public String getPackageId() {
        return packageId;
    }

    @Override
    public void setEffectMode(int effectMode) {
        this.effectMode = effectMode;
    }

    @Override
    public int getEffectMode() {
        return effectMode;
    }

    @Override
    public String getEffectId() {
        return effectId;
    }

    @Override
    public void setEffectId(String effectId) {
        this.effectId = effectId;
    }

    @Override
    public void setAssetPath(String assetPath) {
        this.assetPath = assetPath;
    }

    @Override
    public String getAssetPath() {
        return assetPath;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public int getAssetSize() {
        return assetSize;
    }

    public void setAssetSize(int assetSize) {
        this.assetSize = assetSize;
    }

    public void setDownloadProgress(int downloadProgress) {
        this.downloadProgress = downloadProgress;
    }

    public int getDownloadProgress() {
        return downloadProgress;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public int getLocalVersion() {
        return localVersion;
    }

    public void setLocalVersion(int localVersion) {
        this.localVersion = localVersion;
    }

    public boolean needUpdate() {
        return isHadDownloaded() && version > localVersion && (!TextUtils.isEmpty(downloadUrl));

    }

    public int getSupportedAspectRatio() {
        return supportedAspectRatio;
    }

    public void setSupportedAspectRatio(int supportedAspectRatio) {
        this.supportedAspectRatio = supportedAspectRatio;
    }

    public String getMinAppVersion() {
        return minAppVersion;
    }

    public void setMinAppVersion(String minAppVersion) {
        this.minAppVersion = minAppVersion;
    }

    public boolean isHadDownloaded() {
        return hadDownloaded;
    }

    public void setHadDownloaded(boolean hadDownloaded) {
        this.hadDownloaded = hadDownloaded;
    }

    public String getPreviewSampleUrl() {
        return previewSampleUrl;
    }

    public void setPreviewSampleUrl(String previewSampleUrl) {
        this.previewSampleUrl = previewSampleUrl;
    }

    public String getInfoUrl() {
        return infoUrl;
    }

    public void setInfoUrl(String infoUrl) {
        this.infoUrl = infoUrl;
    }

    public boolean isNewData() {
        return isNewData;
    }

    public void setNewData(boolean newData) {
        isNewData = newData;
    }

    public int getRatioFlag() {
        return ratioFlag;
    }

    public void setRatioFlag(int ratioFlag) {
        this.ratioFlag = ratioFlag;
    }

    public int getPossessor() {
        return possessor;
    }

    public void setPossessor(int possessor) {
        this.possessor = possessor;
    }

    public ExtendedInfo getExtendedInfo() {
        return extendedInfo;
    }

    public void setExtendedInfo(ExtendedInfo extendedInfo) {
        this.extendedInfo = extendedInfo;
    }

    public int isPostPackage() {
        return isPostPackage;
    }

    public void setPostPackage(int postPackage) {
        this.isPostPackage = postPackage;
    }

    public int getDefaultAspectRatio() {
        return defaultAspectRatio;
    }

    public void setDefaultAspectRatio(int defaultAspectRatio) {
        this.defaultAspectRatio = defaultAspectRatio;
    }

    public int getCategory() {
        return category;
    }

    public void setCategory(int category) {
        this.category = category;
    }

    @Override
    public void update(IBaseInfo newInfo) {
        super.update(newInfo);
        AssetInfo info = (AssetInfo) newInfo;
        setDownloadProgress(info.getDownloadProgress());
        setVersion(info.getVersion());
        setLocalVersion(info.getLocalVersion());
        setMinAppVersion(info.getMinAppVersion());
        setHadDownloaded(info.isHadDownloaded());
        setAssetSize(info.getAssetSize());
        setDownloadUrl(info.getDownloadUrl());
        setSupportedAspectRatio(info.getSupportedAspectRatio());
        setPreviewSampleUrl(info.getPreviewSampleUrl());
        setInfoUrl(info.getInfoUrl());
        setNewData(info.isNewData);
        setDefaultAspectRatio(info.defaultAspectRatio);
        setLicPath(info.getLicPath());
    }

    public String getExtents() {
        return GsonUtils.toJson(extendedInfo);
    }

    public static class ExtendedInfo {
        public UserInfo userInfo;
        public InteractiveResultDto interactiveResultDto;
    }

    public static class UserInfo {
        public String nickname;
        public String iconUrl;

        public UserInfo(String nickname, String iconUrl) {
            this.nickname = nickname;
            this.iconUrl = iconUrl;
        }
    }

    public static class InteractiveResultDto {
        public int likeNum;
        public int useNum;
        public String materialId;

        public InteractiveResultDto(int likeNum, int useNum, String materialId) {
            this.likeNum = likeNum;
            this.useNum = useNum;
            this.materialId = materialId;
        }
    }

    @NonNull
    @Override
    public String toString() {
        return "AssetInfo{id=" + id + ",name=" + getName() + ",packageId=" + getPackageId() + ",version=" + getVersion()
                + ",localVersion=" + getLocalVersion() + ",type=" + getType() + ",hadDownload=" + hadDownloaded + "}";
    }

    public static AssetInfo create(AssetList.NvAssetInfo info, int type) {
        AssetInfo assetInfo = new AssetInfo();
        /*
         * 新网络接口用uuid表示packageId， 老接口用id表示packageId
         * The new net interface uses UUID to represent the packageId, and the old interface uses
         * ID to represent the packageId.
         */
        String id = info.uuid;
        if (TextUtils.isEmpty(id)) {
            id = info.id;
        }
        assetInfo.setId(id);
        assetInfo.setPackageId(id);
        assetInfo.setEffectMode(BaseInfo.EFFECT_MODE_PACKAGE);
        assetInfo.setHadDownloaded(false);
        if (!TextUtils.isEmpty(info.name)) {
            assetInfo.setName(info.name);
        } else {
            if (Utils.isZh()) {
                assetInfo.setName(info.displayNameZhCn);
            } else {
                assetInfo.setEnName(info.displayName);
            }
        }

        assetInfo.setName(info.displayNameZhCn);

       /* if (Utils.isZh()) {
            assetInfo.setDescription(info.descriptionZhCn);
        } else {
            assetInfo.setDescription(info.description);
        }*/
        assetInfo.setCategory(info.category);
        assetInfo.setDescription(info.description);
        assetInfo.setDescriptionZhCn(info.descriptionZhCn);
        if (TextUtils.isEmpty(assetInfo.getName())) {
            assetInfo.setName(info.customDisplayName);
        }
        assetInfo.setCoverPath(info.coverUrl);
        assetInfo.setVersion(info.version);
        assetInfo.setMinAppVersion(info.minAppVersion);
        assetInfo.setAssetSize(info.packageSize);
        assetInfo.setDownloadUrl(info.packageUrl);

        assetInfo.setType(type);
        assetInfo.setAuthorized(info.authed);
        assetInfo.setPostPackage(info.isPostPackage);
        assetInfo.setPreviewSampleUrl(info.previewVideoUrl);
        if (TextUtils.isEmpty(assetInfo.getDownloadUrl())) {
            assetInfo.setDownloadUrl(info.packageRelativePath);
        }
        assetInfo.setLicPath(info.getLicPath());
        assetInfo.setInfoUrl(info.infoUrl);
        assetInfo.setRatioFlag(info.ratioFlag);
        assetInfo.setDuration(info.templateTotalDuration <= 0 ? info.duration : info.templateTotalDuration);
        assetInfo.setSupportedAspectRatio(info.supportedAspectRatio);
        assetInfo.setPossessor(info.possessor);
        assetInfo.setDefaultAspectRatio(info.defaultAspectRatio);
        ExtendedInfo extendedInfo = new ExtendedInfo();
        assetInfo.setExtendedInfo(extendedInfo);
        if (info.userInfo != null && !TextUtils.isEmpty(info.userInfo.nickname)) {
            extendedInfo.userInfo = new UserInfo(info.userInfo.nickname, info.userInfo.iconUrl);
        }
        if (info.queryInteractiveResultDto != null) {
            extendedInfo.interactiveResultDto = new InteractiveResultDto(
                    info.queryInteractiveResultDto.likeNum,
                    info.queryInteractiveResultDto.useNum,
                    info.queryInteractiveResultDto.materialId);
        }
        return assetInfo;
    }

    public static AssetInfo create(AssetEntity entity) {
        AssetInfo assetInfo = new AssetInfo();
        assetInfo.setId(entity.getId());
        assetInfo.setName(entity.getName());
        assetInfo.setType(entity.getType());
        assetInfo.setPackageId(entity.getPackageId());
        assetInfo.setEffectMode(BaseInfo.EFFECT_MODE_PACKAGE);
        assetInfo.setCoverPath(entity.getCoverPath());
        assetInfo.setAssetPath(entity.getAssetPath());
        assetInfo.setVersion(entity.getVersion());
        assetInfo.setLocalVersion(entity.getVersion());
        assetInfo.setHadDownloaded(true);
        assetInfo.setRatioFlag(entity.getRatioFlag());
        assetInfo.setDefaultAspectRatio(entity.getSupportedAspectRatio());
        assetInfo.setLicPath(entity.getLicPath());
        String extended = entity.getExtended();
        if (TextUtils.isEmpty(extended)) {
            assetInfo.extendedInfo = GsonUtils.getGson().fromJson(extended, ExtendedInfo.class);
        }
        return assetInfo;
    }
}
