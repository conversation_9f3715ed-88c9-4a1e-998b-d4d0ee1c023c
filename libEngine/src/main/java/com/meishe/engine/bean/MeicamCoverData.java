package com.meishe.engine.bean;

import android.graphics.PointF;
import android.graphics.RectF;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsStreamingContext;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.util.gson.GsonContext;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/12/30 18:21
 * @Description :用于封面编辑的数据 The data for cover edit
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MeicamCoverData implements Serializable, Cloneable {
    private String filePath;

    /**
     * 是否是新建的timeline
     * Whether it is a newly created timeline
     */
    private boolean isNewTimeline;
    /**
     * 封面在timeline上的时间点
     * The point in timeline
     */
    private long coverPoint;
    private Map<String, ClipInfo<?>> captionClipMap = new LinkedHashMap<>();
    private TransformData mTransformData;

    /**
     * Gets caption clip map.
     *
     * @return the caption clip map
     */
    public Map<String, ClipInfo<?>> getCaptionClipMap() {
        return captionClipMap;
    }

    /**
     * Sets caption clip map.
     *
     * @param captionClipMap the caption clip map
     */
    public void setCaptionClipMap(Map<String, ClipInfo<?>> captionClipMap) {
        this.captionClipMap = captionClipMap;
    }

    /**
     * Gets file path.
     *
     * @return the file path
     */
    public String getFilePath() {
        return filePath;
    }

    /**
     * Sets file path.
     *
     * @param filePath the file path
     */
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    /**
     * Gets cover point.
     *
     * @return the cover point
     */
    public long getCoverPoint() {
        return coverPoint;
    }

    /**
     * Sets cover point.
     *
     * @param coverPoint the cover point
     */
    public void setCoverPoint(long coverPoint) {
        this.coverPoint = coverPoint;
    }

    /**
     * Sets transform data.
     *
     * @param transformData the transform data
     */
    public void setTransformData(TransformData transformData) {
        mTransformData = transformData;
    }

    /**
     * Gets transform data.
     *
     * @return the transform data
     */
    public TransformData getTransformData() {
        return mTransformData;
    }


    /**
     * To json string.
     * 转换成json数据
     *
     * @return the string
     */
    public String toJson() {
        return GsonContext.getInstance().toJson(this);
    }

    /**
     * From json meicam cover data.
     * Json转换成实体对象
     *
     * @param jsonData the json data json 数据
     * @return the meicam cover data
     */
    public static MeicamCoverData fromJson(String jsonData) {
        return GsonContext.getInstance().fromJson(jsonData, MeicamCoverData.class);
    }

    /**
     * Gets cover handler.
     *
     * @return the cover handler
     */
    public CoverHandler getCoverHandler() {
        return new CoverHandler();
    }

    public boolean isNewTimeline() {
        return isNewTimeline;
    }

    public void setNewTimeline(boolean newTimeline) {
        isNewTimeline = newTimeline;
    }

    /**
     * The type Cover handler.
     */
    public class CoverHandler {
        private final EditorEngine mEditorEngine;
        private final Map<String, MeicamStickerCaptionTrack> captionTrackMap = new HashMap<>();
        private final List<MeicamTimeline> bindTimelineList = new ArrayList<>();

        /**
         * Instantiates a new Cover handler.
         */
        public CoverHandler() {
            this.mEditorEngine = EditorEngine.getInstance();
        }

        /**
         * Add caption
         * 添加字幕
         *
         * @param text         the text 字幕内容
         * @param captionStyle the caption style 字幕样式
         * @param isNormal     the is normal 是否是普通字幕
         * @return the caption clip 字幕实体
         */
        public MeicamCaptionClip addCaption(String text, String captionStyle, boolean isNormal) {
            MeicamCaptionClip result = null;
            String captionId = UUID.randomUUID().toString();
            for (MeicamTimeline meicamTimeline : bindTimelineList) {
                MeicamStickerCaptionTrack track = captionTrackMap.get(meicamTimeline.getProjectId());
                if (track == null) {
                    return null;
                }
                MeicamCaptionClip meicamCaptionClip;
                long duration = meicamTimeline.getDuration();
                if (isNormal) {
                    meicamCaptionClip = track.addNormalCaption(text, 0, duration);
                } else {
                    meicamCaptionClip = track.addModularCaption(text, 0, duration);
                }
                if (meicamCaptionClip == null) {
                    return null;
                }
                if (!TextUtils.isEmpty(captionStyle)) {
                    meicamCaptionClip.setStyleId(captionStyle);
                }
                meicamCaptionClip.setAttachment(CommonData.ATTACHMENT_KEY_IS_COVER_CAPTION, captionId);
                if (mEditorEngine.getCurrentTimeline().equals(meicamTimeline)) {
                    result = meicamCaptionClip;
                }
            }
            return result;
        }

        /**
         * 添加组合字幕，如果有旧的，则替换
         * Add or replace compound caption
         *
         * @param uuid Uuid of compound caption 复合标题的Uuid
         * @return the meicam compound caption clip
         */
        public MeicamCompoundCaptionClip addCompoundCaption(String uuid) {
            MeicamCompoundCaptionClip result = null;
            String captionId = UUID.randomUUID().toString();
            for (MeicamTimeline meicamTimeline : bindTimelineList) {
                MeicamStickerCaptionTrack track = captionTrackMap.get(meicamTimeline.getProjectId());
                if (track == null) {
                    return null;
                }
                MeicamCompoundCaptionClip compoundCaptionClip = track.addCompoundCaption(0, meicamTimeline.getDuration(), uuid);
                if (compoundCaptionClip == null) {
                    return null;
                }
                compoundCaptionClip.setAttachment(CommonData.ATTACHMENT_KEY_IS_COVER_CAPTION, captionId);
                if (mEditorEngine.getCurrentTimeline().equals(meicamTimeline)) {
                    result = compoundCaptionClip;
                }
            }
            return result;
        }


        /**
         * Update caption.
         * 更新字幕
         *
         * @param caption the caption 字幕
         * @param content the content 字幕内容
         */
        public void updateCaption(MeicamCaptionClip caption, String content) {
            if (caption == null) {
                return;
            }
            for (MeicamTimeline timeline : bindTimelineList) {
                MeicamStickerCaptionTrack track = captionTrackMap.get(timeline.getProjectId());
                if (track == null) {
                    return;
                }
                int clipCount = track.getClipCount();
                Object attachment = caption.getAttachment(CommonData.ATTACHMENT_KEY_IS_COVER_CAPTION);
                for (int index = 0; index < clipCount; index++) {
                    ClipInfo<?> clipInfo = track.getCaptionStickerClip(index);
                    if (clipInfo == null) {
                        return;
                    }
                    if (isCoverCaption((String) attachment, clipInfo)) {
                        if (clipInfo instanceof MeicamCaptionClip) {
                            ((MeicamCaptionClip) clipInfo).setText(content);
                        }
                    }
                }
            }
        }

        /**
         * 删除字幕，贴纸
         * Delete caption sicker.
         *
         * @param clipInfo the clip info 贴纸、字幕等实体
         */
        public void deleteCaptionSicker(ClipInfo<?> clipInfo) {
            if (clipInfo == null) {
                LogUtils.e("clipInfo is null");
                return;
            }
            Object attachment = clipInfo.getAttachment(CommonData.ATTACHMENT_KEY_IS_COVER_CAPTION);
            if (!(attachment instanceof String)) {
                LogUtils.e("Please set the String type attachment!");
                return;
            }
            for (MeicamTimeline meicamTimeline : bindTimelineList) {
                String projectId = meicamTimeline.getProjectId();
                MeicamStickerCaptionTrack meicamStickerCaptionTrack = captionTrackMap.get(projectId);
                if (meicamStickerCaptionTrack == null) {
                    return;
                }
                int clipCount = meicamStickerCaptionTrack.getClipCount();
                for (int index = clipCount - 1; index >= 0; index--) {
                    ClipInfo<?> clip = meicamStickerCaptionTrack.getCaptionStickerClip(index);
                    if (isCoverCaption((String) attachment, clip)) {
                        meicamStickerCaptionTrack.removeStickerCaptionClip(clip);
                    }
                }
                meicamTimeline.seekTimeline(NvsStreamingContext.getInstance(), meicamTimeline.getCurrentPosition(), NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER);
            }
        }

        /**
         * Bind timeline.
         * 将字幕数据绑定到timeline
         *
         * @param timeline   the timeline timeline
         * @param captionMap the caption map 字幕数据
         */
        public void bindTimeline(MeicamTimeline timeline, Map<String, ClipInfo<?>> captionMap) {
            if (timeline == null) {
                LogUtils.e("Timeline is null !");
                return;
            }
            String projectId = timeline.getProjectId();
            if (!bindTimelineList.contains(timeline)) {
                bindTimelineList.add(timeline);
            }
            timeline.loadCoverData(captionMap);
            captionTrackMap.put(projectId, timeline.findStickCaptionTrack(timeline.getStickerCaptionTrackCount() - 1));
        }

        /**
         * Translate caption.
         * 移动字幕
         *
         * @param captionClip the caption clip 字幕
         */
        public void translateCaption(ClipInfo<?> captionClip) {
            if (captionClip == null) {
                return;
            }
            MeicamTimeline currentTimeline = mEditorEngine.getCurrentTimeline();
            String projectId = currentTimeline.getProjectId();
            Object attachment = captionClip.getAttachment(CommonData.ATTACHMENT_KEY_IS_COVER_CAPTION);
            if (!(attachment instanceof String)) {
                LogUtils.e("Please set the String type attachment!");
                return;
            }
            float translationX = 0;
            float translationY = 0;
            if (captionClip instanceof MeicamCaptionClip) {
                translationX = ((MeicamCaptionClip) captionClip).getTranslationX();
                translationY = ((MeicamCaptionClip) captionClip).getTranslationY();
            } else if (captionClip instanceof MeicamCompoundCaptionClip) {
                translationX = ((MeicamCompoundCaptionClip) captionClip).getTranslationX();
                translationY = ((MeicamCompoundCaptionClip) captionClip).getTranslationY();
            }
            for (MeicamTimeline meicamTimeline : bindTimelineList) {
                if (TextUtils.equals(projectId, meicamTimeline.getProjectId())) {
                    continue;
                }
                MeicamStickerCaptionTrack meicamStickerCaptionTrack = captionTrackMap.get(meicamTimeline.getProjectId());
                if (meicamStickerCaptionTrack == null) {
                    return;
                }
                int clipCount = meicamStickerCaptionTrack.getClipCount();
                for (int index = 0; index < clipCount; index++) {
                    ClipInfo<?> clip = meicamStickerCaptionTrack.getCaptionStickerClip(index);
                    if (isCoverCaption((String) attachment, clip)) {
                        if (clip instanceof MeicamCaptionClip) {
                            ((MeicamCaptionClip) clip).setTranslationX(translationX);
                            ((MeicamCaptionClip) clip).setTranslationY(translationY);
                        } else if (clip instanceof MeicamCompoundCaptionClip) {
                            ((MeicamCompoundCaptionClip) clip).setTranslationX(translationX);
                            ((MeicamCompoundCaptionClip) clip).setTranslationY(translationY);
                        }
                    }
                }
            }
        }

        /**
         * Change param.
         * 更新参数值
         *
         * @param captionClip the caption clip 字幕
         */
        public void changeParam(ClipInfo<?> captionClip) {
            if (captionClip == null) {
                return;
            }
            MeicamTimeline currentTimeline = mEditorEngine.getCurrentTimeline();
            String projectId = currentTimeline.getProjectId();
            Object attachment = captionClip.getAttachment(CommonData.ATTACHMENT_KEY_IS_COVER_CAPTION);
            if (!(attachment instanceof String)) {
                LogUtils.e("Please set the String type attachment!");
                return;
            }
            for (MeicamTimeline meicamTimeline : bindTimelineList) {
                if (TextUtils.equals(projectId, meicamTimeline.getProjectId())) {
                    continue;
                }
                MeicamStickerCaptionTrack meicamStickerCaptionTrack = captionTrackMap.get(meicamTimeline.getProjectId());
                if (meicamStickerCaptionTrack == null) {
                    return;
                }
                int clipCount = meicamStickerCaptionTrack.getClipCount();
                for (int index = 0; index < clipCount; index++) {
                    ClipInfo<?> clip = meicamStickerCaptionTrack.getCaptionStickerClip(index);
                    if (isCoverCaption((String) attachment, clip)) {
                        if (clip instanceof MeicamCaptionClip) {
                            ((MeicamCaptionClip) clip).syncParam((MeicamCaptionClip) captionClip);
                        } else if (clip instanceof MeicamCompoundCaptionClip) {
                            ((MeicamCompoundCaptionClip) clip).syncParam((MeicamCompoundCaptionClip) captionClip);
                        }
                    }
                }
            }
        }

        /**
         * Sets text alignment.
         * 设置字幕对齐方式
         *
         * @param captionClip the caption clip 字幕
         */
        public void setTextAlignment(ClipInfo<?> captionClip) {
            if (captionClip == null) {
                return;
            }
            MeicamTimeline currentTimeline = mEditorEngine.getCurrentTimeline();
            String projectId = currentTimeline.getProjectId();
            Object attachment = captionClip.getAttachment(CommonData.ATTACHMENT_KEY_IS_COVER_CAPTION);
            if (!(attachment instanceof String)) {
                LogUtils.e("Please set the String type attachment!");
                return;
            }
            if (captionClip instanceof MeicamCaptionClip) {
                int textAlignment = ((MeicamCaptionClip) captionClip).getTextAlignment();
                for (MeicamTimeline meicamTimeline : bindTimelineList) {
                    if (TextUtils.equals(projectId, meicamTimeline.getProjectId())) {
                        continue;
                    }
                    MeicamStickerCaptionTrack meicamStickerCaptionTrack = captionTrackMap.get(meicamTimeline.getProjectId());
                    if (meicamStickerCaptionTrack == null) {
                        return;
                    }
                    int clipCount = meicamStickerCaptionTrack.getClipCount();
                    for (int index = 0; index < clipCount; index++) {
                        ClipInfo<?> clip = meicamStickerCaptionTrack.getCaptionStickerClip(index);
                        if (TextUtils.equals((String) attachment, (String) clip.getAttachment(CommonData.ATTACHMENT_KEY_IS_COVER_CAPTION))) {
                            if (clip instanceof MeicamCaptionClip) {
                                ((MeicamCaptionClip) clip).setTextAlignment(textAlignment);
                            }
                        }
                    }
                }
            }
        }

        /**
         * Rotation and scale caption.
         * 旋转和缩放字幕
         *
         * @param captionClip the caption clip 字幕
         */
        public void rotationAndScaleCaption(ClipInfo<?> captionClip) {
            if (captionClip == null) {
                return;
            }
            MeicamTimeline currentTimeline = mEditorEngine.getCurrentTimeline();
            String projectId = currentTimeline.getProjectId();
            Object attachment = captionClip.getAttachment(CommonData.ATTACHMENT_KEY_IS_COVER_CAPTION);
            if (!(attachment instanceof String)) {
                LogUtils.e("Please set the String type attachment!");
                return;
            }
            float scaleX = 0;
            float scaleY = 0;
            float rotation = 0;
            PointF assetAnchor = null;
            if (captionClip instanceof MeicamCaptionClip) {
                scaleX = ((MeicamCaptionClip) captionClip).getScaleX();
                scaleY = ((MeicamCaptionClip) captionClip).getScaleY();
                rotation = ((MeicamCaptionClip) captionClip).getRotation();
            } else if (captionClip instanceof MeicamCompoundCaptionClip) {
                scaleX = ((MeicamCompoundCaptionClip) captionClip).getScaleX();
                scaleY = ((MeicamCompoundCaptionClip) captionClip).getScaleY();
                rotation = ((MeicamCompoundCaptionClip) captionClip).getRotation();
                assetAnchor = ((MeicamCompoundCaptionClip) captionClip).getAssetAnchor();
            }
            for (MeicamTimeline meicamTimeline : bindTimelineList) {
                if (TextUtils.equals(projectId, meicamTimeline.getProjectId())) {
                    continue;
                }
                MeicamStickerCaptionTrack meicamStickerCaptionTrack = captionTrackMap.get(meicamTimeline.getProjectId());
                if (meicamStickerCaptionTrack == null) {
                    return;
                }
                int clipCount = meicamStickerCaptionTrack.getClipCount();
                for (int index = 0; index < clipCount; index++) {
                    ClipInfo<?> clip = meicamStickerCaptionTrack.getCaptionStickerClip(index);
                    if (TextUtils.equals((String) attachment, (String) clip.getAttachment(CommonData.ATTACHMENT_KEY_IS_COVER_CAPTION))) {
                        if (clip instanceof MeicamCaptionClip) {
                            ((MeicamCaptionClip) clip).setScaleX(scaleX);
                            ((MeicamCaptionClip) clip).setScaleY(scaleY);
                            ((MeicamCaptionClip) clip).setRotation(rotation);
                        } else if (clip instanceof MeicamCompoundCaptionClip) {
                            float scaleX1 = ((MeicamCompoundCaptionClip) clip).getScaleX();
                            float rotation1 = ((MeicamCompoundCaptionClip) clip).getRotation();
                            ((MeicamCompoundCaptionClip) clip).scaleCaption(scaleX / scaleX1, assetAnchor);
                            ((MeicamCompoundCaptionClip) clip).rotateCaption(rotation - rotation1, assetAnchor);
                        }
                    }
                }
            }
        }
    }

    private boolean isCoverCaption(String attachment, ClipInfo<?> clip) {
        return clip != null && TextUtils.equals(attachment, (String) clip.getAttachment(CommonData.ATTACHMENT_KEY_IS_COVER_CAPTION));
    }

    @NonNull
    @Override
    public MeicamCoverData clone() {
        MeicamCoverData clone;
        try {
            clone = (MeicamCoverData) super.clone();
            Set<Map.Entry<String, ClipInfo<?>>> entries = this.captionClipMap.entrySet();
            Map<String, ClipInfo<?>> captionClipMap = new LinkedHashMap<>();
            clone.setCaptionClipMap(captionClipMap);
            if (!entries.isEmpty()) {
                for (Map.Entry<String, ClipInfo<?>> entry : entries) {
                    ClipInfo<?> value = entry.getValue();
                    if (value != null) {
                        captionClipMap.put(entry.getKey(), (ClipInfo<?>) value.clone());
                    }
                }
            }
        } catch (Exception e) {
            return new MeicamCoverData();
        }
        return clone;
    }

    /**
     * The type Transform data.
     */
    public static class TransformData implements Serializable {
        private float transX;
        private float transY;
        private float scale;
        private float rotation;
        private RectF region = new RectF(-1, 1, 1, -1);
        private float[] rectSize = new float[]{1, 1};
        private float rectRatio = 1;

        /**
         * Instantiates a new Transform data.
         *
         * @param transX   the trans x
         * @param transY   the trans y
         * @param scale    the scale
         * @param rotation the rotation
         */
        public TransformData(float transX, float transY, float scale, float rotation, float[] rectSize) {
            this.transX = transX;
            this.transY = transY;
            this.scale = scale;
            this.rotation = rotation;
            this.rectSize = rectSize;
        }

        /**
         * Gets trans x.
         *
         * @return the trans x
         */
        public float getTransX() {
            return transX;
        }

        /**
         * Sets trans x.
         *
         * @param transX the trans x
         */
        public void setTransX(float transX) {
            this.transX = transX;
        }

        /**
         * Gets trans y.
         *
         * @return the trans y
         */
        public float getTransY() {
            return transY;
        }

        /**
         * Sets trans y.
         *
         * @param transY the trans y
         */
        public void setTransY(float transY) {
            this.transY = transY;
        }

        /**
         * Gets scale.
         *
         * @return the scale
         */
        public float getScale() {
            return scale;
        }

        /**
         * Sets scale.
         *
         * @param scale the scale
         */
        public void setScale(float scale) {
            this.scale = scale;
        }

        /**
         * Gets rotation.
         *
         * @return the rotation
         */
        public float getRotation() {
            return rotation;
        }

        /**
         * Sets rotation.
         *
         * @param rotation the rotation
         */
        public void setRotation(float rotation) {
            this.rotation = rotation;
        }

        public RectF getRegion() {
            return this.region;
        }

        public void setRegion(RectF region) {
            this.region = region;
        }

        public float[] getRectSize() {
            return rectSize;
        }

        public void setRectSize(float[] rectSize) {
            this.rectSize = rectSize;
        }

        public float getRectRatio() {
            return rectRatio;
        }

        public void setRectRatio(float rectRatio) {
            this.rectRatio = rectRatio;
        }
    }
}
