<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.FullScreenPreviewActivity">

    <FrameLayout
        android:id="@+id/fl_fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_marginBottom="@dimen/dp_px_165"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_play"
            android:layout_width="@dimen/dp_px_54"
            android:layout_height="@dimen/dp_px_54"
            android:layout_marginStart="@dimen/dp_px_54"
            android:layout_marginLeft="@dimen/dp_px_52"
            android:contentDescription="@null"
            app:srcCompat="@mipmap/control_bar_ic_play" />

        <TextView
            android:id="@+id/tv_play_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_px_21"
            android:layout_marginRight="@dimen/dp_px_21"
            android:textColor="@color/white_8" />

        <SeekBar
            android:id="@+id/seek_bar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tv_play_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_px_21"
            android:layout_marginRight="@dimen/dp_px_21"
            android:textColor="@color/white_8" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="@dimen/dp_px_54"
            android:layout_height="@dimen/dp_px_54"
            android:layout_marginEnd="@dimen/dp_px_52"
            android:layout_marginRight="@dimen/dp_px_52"
            android:contentDescription="@null"
            app:srcCompat="@drawable/pack_up" />
    </LinearLayout>


</FrameLayout>