package com.meishe.capturemodule.presenter;


import android.graphics.RectF;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;

import com.meicam.sdk.NvsARSceneManipulate;
import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsAssetPackageManager;
import com.meicam.sdk.NvsCaptureVideoFx;
import com.meicam.sdk.NvsColor;
import com.meicam.sdk.NvsLiveWindowExt;
import com.meicam.sdk.NvsMakeupEffectInfo;
import com.meicam.sdk.NvsRational;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsVideoFrameRetriever;
import com.meishe.base.bean.MediaData;
import com.meishe.base.constants.Constants;
import com.meishe.base.model.Presenter;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;
import com.meishe.capturemodule.CaptureActivity;
import com.meishe.capturemodule.R;
import com.meishe.capturemodule.bean.BeautyShapeDataItem;
import com.meishe.capturemodule.bean.CaptureFxModel;
import com.meishe.capturemodule.dialog.ItemListDialog;
import com.meishe.capturemodule.iview.ICaptureView;
import com.meishe.capturemodule.makeup.ColorData;
import com.meishe.capturemodule.makeup.FilterArgs;
import com.meishe.capturemodule.makeup.Makeup;
import com.meishe.capturemodule.makeup.MakeupArgs;
import com.meishe.capturemodule.makeup.MakeupData;
import com.meishe.capturemodule.makeup.MakeupEffectContent;
import com.meishe.capturemodule.makeup.MakeupManager;
import com.meishe.capturemodule.utils.CaptureDataHelper;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.EngineCallbackManager;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.observer.EngineCallbackObserver;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * <AUTHOR> LiuPanFeng
 * @CreateDate : 2021/2/17 14:15
 * @Description : 拍摄和录制
 * capture and record
 * @Copyright : www.meishesdk.com Inc. All rights reserved.
 */
public class CapturePresenter extends Presenter<ICaptureView> {
    private static final String TAG = CaptureActivity.class.getSimpleName();

    /**
     * 特效类型：滤镜 effect type filter
     */
    public static final String FX_TYPE_FILTER = CaptureFxModel.FX_TYPE_FILTER;
    /**
     * 特效类型：道具 effect type prop
     */
    public static final String FX_TYPE_PROP = CaptureFxModel.FX_TYPE_PROP;

    private EditorEngine mEditorEngine;

    /**
     * 流媒体上下文
     * Streaming context
     */
    private NvsStreamingContext mStreamingContext;
    /**
     * 美颜特效
     * Ar scene face effect
     */
    private NvsCaptureVideoFx mArSceneFaceEffect;
    /**
     * 清晰度对象
     * Definition
     */
    private NvsCaptureVideoFx mDefinition;
    private ArrayList<ItemListDialog.ItemInfo> mMoreOptionData;
    private int mCaptureResolutionGrade = NvsStreamingContext.VIDEO_CAPTURE_RESOLUTION_GRADE_HIGH;

    /**
     * 锐化对象
     * Sharpen
     */
    private NvsCaptureVideoFx mSharpen;
    private boolean mSupportFlash;
    private boolean mSupportAutoFocus;
    private boolean mSupportAutoExposure;
    private boolean mSupportZoom;
    private int mSupportMaxZoom;
    private Map<Integer, NvsStreamingContext.CaptureDeviceCapability> capabilities = new HashMap<>();
    private int mMinExpose;
    private int mMaxExpose;
    /**
     * 美颜数据
     * Shape data list
     */
    private List<BeautyShapeDataItem> mShapeDataList;

    private CaptureFxModel mFxModel = new CaptureFxModel();

    public CapturePresenter() {
        mEditorEngine = EditorEngine.getInstance();
        mStreamingContext = mEditorEngine.getStreamingContext();
        initBeautyAndShapeData();
    }


    /**
     * 获取当前引擎状态
     * Get the current engine status
     */
    public int getCurrentEngineState() {
        if (mStreamingContext != null) {
            return mStreamingContext.getStreamingEngineState();
        }
        return -1;
    }

    /**
     * 将采集预览输出连接到LiveWindow控件
     * Connect the capture preview output to the LiveWindow control
     *
     * @param nvsLiveWindow
     */
    public void connectLiveWindow(NvsLiveWindowExt nvsLiveWindow) {
        if (null == mStreamingContext) {
            return;
        }
        if (mStreamingContext.getCaptureDeviceCount() == 0) {
            return;
        }
        /*
         * 将采集预览输出连接到LiveWindow控件
         * Connect the capture preview output to the LiveWindow control
         * */
        if (!mStreamingContext.connectCapturePreviewWithLiveWindowExt(nvsLiveWindow)) {
            LogUtils.e("Failed to connect capture preview with livewindow!");
        }
    }

    /**
     * 获取设备数量
     * Get capture device count
     *
     * @return The device count
     */
    public int getCaptureDeviceCount() {
        return mStreamingContext.getCaptureDeviceCount();
    }


    public boolean startCapturePreview(int currentDeviceIndex, int captureResolutionGrade, float ratio) {
        if (mStreamingContext == null) {
            mStreamingContext = mEditorEngine.getStreamingContext();
        }
        if (mStreamingContext == null) {
            LogUtils.e("startCapturePreview mStreamingContext==null");
            return false;
        }
        if (mStreamingContext.startCapturePreview(currentDeviceIndex, captureResolutionGrade,
                NvsStreamingContext.STREAMING_ENGINE_CAPTURE_FLAG_DONT_USE_SYSTEM_RECORDER
                        | NvsStreamingContext.STREAMING_ENGINE_CAPTURE_FLAG_CAPTURE_BUDDY_HOST_VIDEO_FRAME
                        | NvsStreamingContext.STREAMING_ENGINE_CAPTURE_FLAG_STRICT_PREVIEW_VIDEO_SIZE
                        | NvsStreamingContext.STREAMING_ENGINE_CAPTURE_FLAG_LOW_PIPELINE_SIZE,
                getCaptureRatio(captureResolutionGrade, ratio))) {
            LogUtils.e("Failed to start capture preview!");
            return true;
        }
        return false;
    }

    private NvsRational getCaptureRatio(int grade, float ratio) {
        int maxValue = 720;
        if (grade == NvsStreamingContext.VIDEO_CAPTURE_RESOLUTION_GRADE_SUPER_HIGH) {
            maxValue = 1080;
        }
        NvsRational rational;
        if (ratio > 1) {
            rational = new NvsRational((int) (maxValue * ratio), maxValue);
        } else {
            rational = new NvsRational(maxValue, (int) (maxValue / ratio));
        }
        return rational;
    }


    /**
     * 初始化美颜特效对象
     * Initialize the beauty effect object
     */

    public void initBeautyAndShapeData() {
        if (mDefinition == null) {
            mDefinition = appendBuiltinCaptureVideoFx("Definition");
        }
        if (mSharpen == null) {
            mSharpen = appendBuiltinCaptureVideoFx("Sharpen");
        }
        if (mArSceneFaceEffect == null) {
            mArSceneFaceEffect = mStreamingContext.insertBuiltinCaptureVideoFx("AR Scene", 0);
        }
        if (mArSceneFaceEffect != null) {
            //支持的人脸个数，是否需要使用最小的设置
            // Do you need to use the minimum setting for the number of supported faces.
            mArSceneFaceEffect.setBooleanVal(Constants.MAX_FACES_RESPECT_MIN, true);
            //美颜开关 Beauty switch
            mArSceneFaceEffect.setBooleanVal("Beauty Effect", true);
            //美型开关 Beauty Shape switch
            mArSceneFaceEffect.setBooleanVal("Beauty Shape", true);
            //美型开关 Beauty Shape switch
            mArSceneFaceEffect.setBooleanVal("Face Mesh Internal Enabled", true);
            //高级美颜开关 Advanced Beauty switch
            mArSceneFaceEffect.setBooleanVal("Advanced Beauty Enable", true);
            // 高级美颜类型 Advanced Beauty Type
            mArSceneFaceEffect.setIntVal("Advanced Beauty Type", 0);
            //高级磨皮的强度设置值 Strength setting value for advanced grinding
            mArSceneFaceEffect.setFloatVal("Advanced Beauty Intensity", 0);

            mArSceneFaceEffect.setFloatVal("Beauty Strength", 0);

            mArSceneFaceEffect.setBooleanVal("Use Face Extra Info", true);

            if (mStreamingContext.isAndroidCameraPreferDualBufferAR()) {
                //这个返回true的情况下 必须使用双buffer
                // When this returns true, a double buffer must be used.
                mArSceneFaceEffect.setBooleanVal("Single Buffer Mode", false);
            }
            NvsARSceneManipulate arSceneManipulate = mArSceneFaceEffect.getARSceneManipulate();
            if (arSceneManipulate != null) {
                arSceneManipulate.setDetectionMode(NvsStreamingContext.HUMAN_DETECTION_FEATURE_SEMI_IMAGE_MODE);
            }
        }
    }

    /**
     * 暂时默认添加美妆
     * Temporarily default to adding makeup
     */
    private boolean isMarkUpSelected = true;

    public void setMarkUpSelected(boolean markUpSelected) {
        isMarkUpSelected = markUpSelected;
    }

    public boolean isMarkUpSelected() {
        return isMarkUpSelected;
    }

    /**
     * 设置美颜数据
     * Apply beauty data
     *
     * @param selectItem the beauty shape data item
     */

    public void applyBeautyData(BeautyShapeDataItem selectItem) {
        Log.d(TAG, selectItem.name + "====" + selectItem.beautyShapeId + "---" + selectItem.strength);
        int effectType = selectItem.effectType;
        if (effectType == BeautyShapeDataItem.EFFECT_TYPE_STRENGTH
                || effectType == BeautyShapeDataItem.EFFECT_TYPE_SMALL_SHAPE) {
            //微整形，磨皮 Micro plastic surgery, skin grinding
            setARSceneFloatVal(selectItem.beautyShapeId, selectItem.strength);
        } else if (effectType == BeautyShapeDataItem.EFFECT_TYPE_SHARPNESS) {
            //锐度 Sharpen
            setSharpenFloatVal(selectItem.beautyShapeId, selectItem.strength);
        } else if (effectType == BeautyShapeDataItem.EFFECT_TYPE_DEFINITION) {
            //清晰度 Definition
            setDefinitionFloatVal(selectItem.beautyShapeId, selectItem.strength);
        } else if (effectType == BeautyShapeDataItem.EFFECT_TYPE_MAKEUP && isMarkUpSelected) {
            //美妆 Makeup
            setMakeUp(selectItem);
        } else if (effectType == BeautyShapeDataItem.EFFECT_TYPE_SHAPE) {
            //美型 Beauty shape
            applyShapeData(selectItem);
        }
    }

    private boolean applyShapeData(BeautyShapeDataItem selectItem) {
        if (mArSceneFaceEffect == null || selectItem == null || TextUtils.isEmpty(selectItem.beautyShapeId)
                || mStreamingContext == null) {
            return false;
        }
        //1.安装 install
        String assetPath = selectItem.wrapFlag ? selectItem.getWarpPath() : selectItem.getFaceMeshPath();
        int assetType = selectItem.wrapFlag ? NvsAssetPackageManager.ASSET_PACKAGE_TYPE_WARP :
                NvsAssetPackageManager.ASSET_PACKAGE_TYPE_FACE_MESH;
        StringBuilder sb = new StringBuilder();
        int i = mStreamingContext.getAssetPackageManager().installAssetPackage(assetPath, selectItem.licPath, assetType, false, sb);
        if (i != NvsAssetPackageManager.ASSET_PACKAGE_MANAGER_ERROR_NO_ERROR && i != NvsAssetPackageManager.ASSET_PACKAGE_MANAGER_ERROR_ALREADY_INSTALLED) {
            return false;
        }
        mArSceneFaceEffect.setStringVal(selectItem.wrapFlag ?
                selectItem.warpId : selectItem.faceMeshId, sb.toString());
        mArSceneFaceEffect.setFloatVal(selectItem.wrapFlag ? selectItem.warpDegree
                : selectItem.faceDegree, selectItem.strength);
        Log.d("=====", "shape installCode:" + i + " wrapFlag:" + selectItem.wrapFlag + " id:" + sb.toString());
        return true;
    }

    public void setSharpenFloatVal(String key, double value) {
        if (mSharpen != null) {
            mSharpen.setFloatVal(key, value);
        }
    }

    public void setARSceneFloatVal(String key, double value) {
        if (mArSceneFaceEffect != null) {
            mArSceneFaceEffect.setFloatVal(key, value);
        }
    }

    public void setARSceneStringVal(String key, String value) {
        if (mArSceneFaceEffect != null) {
            mArSceneFaceEffect.setStringVal(key, value);
        }
        CaptureFxModel.FxInfo fxInfo = getFxInfo(CapturePresenter.FX_TYPE_PROP);
        fxInfo.setFxId(value);
        fxInfo.setFxMode(CaptureFxModel.FXMODE_PACKAGE);
    }

    /**
     * 设置整妆
     * Sets compose makeup.
     *
     * @param uuid the uuid
     */
    public void setComposeMakeup(String uuid) {
        if (mArSceneFaceEffect != null) {
            mArSceneFaceEffect.setFloatVal("Makeup Intensity", 1.0f);
            mArSceneFaceEffect.setIntVal("Makeup Custom Enabled Flag", NvsMakeupEffectInfo.MAKEUP_EFFECT_CUSTOM_ENABLED_FLAG_NONE);
            mArSceneFaceEffect.setStringVal("Makeup Compound Package Id", uuid);
        }
    }

    /**
     * 设置单妆
     * Sets single makeup.
     *
     * @param makeupId  the makeup id
     * @param intensity the intensity
     * @param uuid      the uuid
     */
    public void setSingleMakeup(String makeupId, double intensity, String uuid) {
        if (mArSceneFaceEffect != null) {
            mArSceneFaceEffect.setIntVal("Makeup Custom Enabled Flag",
                    NvsMakeupEffectInfo.MAKEUP_EFFECT_CUSTOM_ENABLED_FLAG_ALL);
//            mArSceneFaceEffect.setColorVal("Makeup " + makeupId + " Color",
//                    new NvsColor(0, 0, 0, 0));
            mArSceneFaceEffect.setFloatVal("Makeup " + makeupId + " Intensity", intensity);
            mArSceneFaceEffect.setStringVal("Makeup " + makeupId + " Package Id", uuid);
        }
    }


    /**
     * 设置清晰度
     * Sets definition float val.
     *
     * @param key   the key
     * @param value the value
     */
    public void setDefinitionFloatVal(String key, double value) {
        if (mDefinition != null) {
            mDefinition.setFloatVal(key, value);
        }
    }

    /**
     * 设置 开启美颜美型
     * Sets beauty switch checked.
     *
     * @param isOpen the is open
     */
    public void setBeautySwitchChecked(boolean isOpen) {
        if (mArSceneFaceEffect != null) {
            mArSceneFaceEffect.setBooleanVal("Beauty Effect", isOpen);
            mArSceneFaceEffect.setBooleanVal("Beauty Shape", isOpen);
        }
    }

    public void setMakeUp(BeautyShapeDataItem selectItem) {
        Makeup makeup = selectItem.getMakeup();
        if (makeup == null) {
            return;
        }
        clearAllCustomMakeup();
        clearMakeupFx();
        if (makeup.isIsCompose()) {
            setComposeMakeup(makeup.getUuid());
        } else {
            //这里整装和单妆互斥，所以需先移除整妆
            // Here, the whole makeup and single makeup are mutually exclusive, so the whole makeup
            // needs to be removed first.
            setARSceneStringVal("Makeup Compound Package Id", null);
            MakeupEffectContent makeupEffectContent = makeup.getEffectContent();
            if (makeupEffectContent == null) {
                return;
            }

            //添加效果包中带的滤镜
            // Add filters included in the effect pack.
            List<FilterArgs> filter = makeupEffectContent.getFilter();
            if (filter != null && filter.size() > 0) {
                for (FilterArgs filterArgs : filter) {
                    String packageId = filterArgs.getPackageId();
                    MakeupManager.getInstacne().putFx(packageId);
                    if (filterArgs.getIsBuiltIn() == 1) {
                        appendBuiltinCaptureVideoFx(packageId);
                    } else {
                        appendPackagedCaptureVideoFx(packageId);
                    }
                }
            }
            //添加美妆 Add make up
            List<MakeupArgs> makeupArgs = makeupEffectContent.getMakeupArgs();
            if (!makeupArgs.isEmpty()) {
                for (MakeupArgs args : makeupArgs) {
                    if (args == null) {
                        continue;
                    }
                    MakeupData makeupData = new MakeupData(-1, CaptureDataHelper.DEFAULT_MARKUP, new ColorData());
                    makeupData.setUuid(args.getUuid());
                    MakeupManager.getInstacne().addMakeupEffect(args.getMakeupId(), makeupData);
                    setSingleMakeup(args.getMakeupId(), selectItem.strength, args.getUuid());
                }
            }
        }
    }

    /**
     * 清理所有美妆
     * Clear make up.
     */
    public void clearMakeUp() {
        mArSceneFaceEffect.setIntVal("Makeup Custom Enabled Flag", NvsMakeupEffectInfo.MAKEUP_EFFECT_CUSTOM_ENABLED_FLAG_NONE);
        mArSceneFaceEffect.setStringVal("Makeup Compound Package Id", null);
        clearAllCustomMakeup();
        clearMakeupFx();
    }


    /**
     * Clear all custom makeup.
     * 清理所有的单装
     */
    private void clearAllCustomMakeup() {
        Map<String, MakeupData> makeupDataMap = MakeupManager.getInstacne().getCustomMakeupArgsMap();
        if (makeupDataMap == null) {
            return;
        }
        for (String key : makeupDataMap.keySet()) {
            resetCustomMakeup(key);
        }
        MakeupManager.getInstacne().clearCustomData();
    }

    /**
     * 重置单妆
     * Set make up
     *
     * @param makupId The makeup id
     */
    public void resetCustomMakeup(String makupId) {
        if ((mArSceneFaceEffect == null) || TextUtils.isEmpty(makupId)) {
            return;
        }
        mArSceneFaceEffect.setStringVal("Makeup " + makupId + " Package Id", null);
        mArSceneFaceEffect.setColorVal("Makeup " + makupId + " Color", new NvsColor(0, 0, 0, 0));
        mArSceneFaceEffect.setFloatVal("Makeup " + makupId + " Intensity", CaptureDataHelper.DEFAULT_MARKUP);
    }

    /**
     * 清除美妆中添加的美颜 美型 滤镜特效
     * Remove the beauty filter effects added to makeup
     */
    public void clearMakeupFx() {
        //滤镜 filter
        Set<String> fxSet = MakeupManager.getInstacne().getFxSet();
        if (fxSet != null && fxSet.size() > 0) {
            for (String fxName : fxSet) {
                removeFilterFxById(fxName);
            }
        }
        MakeupManager.getInstacne().clearData();
    }

    /**
     * 添加内建类型的拍摄视频特效
     * append Builtin Capture VideoFx
     *
     * @param s the video fx package id
     * @return the NvsCaptureVideoFx
     */
    public NvsCaptureVideoFx appendBuiltinCaptureVideoFx(String s) {
        return mStreamingContext.appendBuiltinCaptureVideoFx(s);
    }

    /**
     * 添加包类型的视频特效
     * append package capture video fx
     *
     * @param packageId The package id
     * @return The NvsCaptureVideoFx
     */
    public NvsCaptureVideoFx appendPackagedCaptureVideoFx(String packageId) {
        return mStreamingContext.appendPackagedCaptureVideoFx(packageId);
    }

    public NvsCaptureVideoFx appendBeautyCaptureVideoFx() {
        return mStreamingContext.appendBeautyCaptureVideoFx();
    }

    /**
     * 删除视频特效
     * delete video fx
     *
     * @param index the index of videoFx 特效 序列位置
     */
    public void removeCaptureVideoFx(int index) {
        mStreamingContext.removeCaptureVideoFx(index);
    }

    public void startAutoFocus(RectF rectF) {
        if (mSupportAutoFocus) {
            mStreamingContext.startAutoFocus(rectF);
        }
    }


   /* public boolean startRecording(String curRecordVideoPath) {
        return mStreamingContext.startRecording(curRecordVideoPath);
    }*/

    public boolean startRecording(String curRecordVideoPath, int flag) {
        return mStreamingContext.startRecording(curRecordVideoPath, flag);
    }

    public void stopRecording() {
        mStreamingContext.stopRecording();
    }

    public NvsVideoFrameRetriever createVideoFrameRetriever(String curRecordVideoPath) {
        return mStreamingContext.createVideoFrameRetriever(curRecordVideoPath);
    }


    public void removeAllFilterFx() {
        List<Integer> remove_list = new ArrayList<>();
        for (int i = 0; i < mStreamingContext.getCaptureVideoFxCount(); i++) {
            NvsCaptureVideoFx fx = mStreamingContext.getCaptureVideoFxByIndex(i);
            if (fx == null) {
                continue;
            }
            String name = fx.getBuiltinCaptureVideoFxName();
            if (name != null && !"Beauty".equals(name)
                    && !"Face Effect".equals(name)
                    && !"AR Scene".equals(name)
                    && !"Definition".equals(name)
                    && !"Sharpen".equals(name)) {
                remove_list.add(i);
            }
        }
        if (!remove_list.isEmpty()) {
            //这里倒着删，否则会出现移除错误的问题。
            // Delete here in reverse, otherwise there may be removal errors.
            for (int i = remove_list.size() - 1; i >= 0; i--) {
                mStreamingContext.removeCaptureVideoFx(remove_list.get(i));
            }
        }
        getFxInfo(CapturePresenter.FX_TYPE_FILTER).setFxId("");
    }


    public boolean removeFilterFxByName(String name) {
        for (int i = 0; i < mStreamingContext.getCaptureVideoFxCount(); i++) {
            NvsCaptureVideoFx fx = mStreamingContext.getCaptureVideoFxByIndex(i);
            String name1 = fx.getDescription().getName();
            if (name1.equals(name)) {
                mStreamingContext.removeCaptureVideoFx(i);
                return true;
            }
        }
        return false;
    }


    public boolean removeFilterFxById(String name) {
        for (int i = 0; i < mStreamingContext.getCaptureVideoFxCount(); i++) {
            NvsCaptureVideoFx fx = mStreamingContext.getCaptureVideoFxByIndex(i);
            String name1 = fx.getCaptureVideoFxPackageId();
            if (name1.equals(name)) {
                mStreamingContext.removeCaptureVideoFx(i);
                return true;
            }
        }
        return false;
    }


    public ArrayList<MediaData> getMediaData(ArrayList<String> filePaths) {
        if (CommonUtils.isEmpty(filePaths)) {
            return null;
        }
        ArrayList<MediaData> mediaDataArrayList = new ArrayList<>();
        for (int i = 0; i < filePaths.size(); i++) {
            String path = filePaths.get(i);
            MediaData mediaData = new MediaData();
            mediaData.setPath(path);
            NvsAVFileInfo avFileInfo = mStreamingContext.getAVFileInfo(path);
            if (avFileInfo == null) {
                return null;
            }
            int resourceType;
            if (path.endsWith(".mp4")) {
                resourceType = MediaData.TYPE_VIDEO;
            } else {
                resourceType = MediaData.TYPE_PHOTO;
            }
            mediaData.setDisplayName(FileUtils.getFileName(path));
            mediaData.setType(resourceType);

            mediaData.setDuration(avFileInfo.getDuration() / 1000L);

            mediaDataArrayList.add(mediaData);
        }

        return mediaDataArrayList;
    }


    public void showPropsToast(String sceneId) {
        NvsAssetPackageManager manager = mStreamingContext.getAssetPackageManager();
        if (manager == null) {
            return;
        }
        String packagePrompt = manager.getARSceneAssetPackagePrompt(sceneId);
        if (!TextUtils.isEmpty(packagePrompt)) {
            ToastUtils.make().setGravity(Gravity.CENTER, 0, 0).show(packagePrompt);
        }
    }


    public void release() {
        if (mStreamingContext != null) {
            mStreamingContext.removeAllCaptureVideoFx();
            mStreamingContext.stop();
            mStreamingContext = null;
        }
    }


    public void setCaptureDeviceCallback(NvsStreamingContext.CaptureDeviceCallback captureDeviceCallback) {
        mStreamingContext.setCaptureDeviceCallback(captureDeviceCallback);
    }

    public void unregisterCallbackObserver(EngineCallbackObserver observer) {
        EngineCallbackManager.get().unregisterCallbackObserver(observer);
    }

    public void registerCallbackObserver(EngineCallbackObserver observer) {
        EngineCallbackManager.get().registerCallbackObserver(observer);
    }

    public void setCaptureRecordingDurationCallback(NvsStreamingContext.CaptureRecordingDurationCallback captureDeviceCallback) {
        mStreamingContext.setCaptureRecordingDurationCallback(captureDeviceCallback);
    }

    public void setCaptureRecordingStartedCallback(NvsStreamingContext.CaptureRecordingStartedCallback captureDeviceCallback) {
        mStreamingContext.setCaptureRecordingStartedCallback(captureDeviceCallback);
    }

    public void setCaptureRecordingFrameReachedCallback(NvsStreamingContext.CaptureRecordingFrameReachedCallback callback) {
        mStreamingContext.setCaptureRecordingFrameReachedCallback(callback);
    }

    public List<BeautyShapeDataItem> getShapeDataList() {
        if (mShapeDataList == null) {
            mShapeDataList = new CaptureDataHelper().getBeautyDataListByType(Utils.getApp());
        }
        return mShapeDataList;
    }

    public List<ItemListDialog.ItemInfo> getMoreData() {
        if (mMoreOptionData == null) {
            mMoreOptionData = new ArrayList<>();
            ItemListDialog.ItemInfo itemInfo = new ItemListDialog.ItemInfo(new int[]{R.mipmap.ic_capture_delay_default,
                    R.mipmap.ic_captuer_delay_3, R.mipmap.ic_captuer_delay_7});
            itemInfo.tagList = new Object[]{0, 3, 7};
            mMoreOptionData.add(itemInfo);
            itemInfo = new ItemListDialog.ItemInfo(new int[]{R.mipmap.ic_capture_flash_off,
                    R.mipmap.ic_capture_flash_on});
            itemInfo.tagList = new Object[]{false, true};
            mMoreOptionData.add(itemInfo);
            itemInfo = new ItemListDialog.ItemInfo(new int[]{R.mipmap.ic_resolution_720,
                    R.mipmap.ic_resolution_1080});
            itemInfo.tagList = new Object[]{NvsStreamingContext.VIDEO_CAPTURE_RESOLUTION_GRADE_HIGH,
                    NvsStreamingContext.VIDEO_CAPTURE_RESOLUTION_GRADE_SUPER_HIGH};
            mMoreOptionData.add(itemInfo);
        }
        return mMoreOptionData;
    }

    public List<ItemListDialog.ItemInfo> getResolutionItemList() {
        List<ItemListDialog.ItemInfo> data = new ArrayList<>();
        ItemListDialog.ItemInfo itemInfo = new ItemListDialog.ItemInfo(new int[]{R.drawable.selector_capture_ratio_18v9});
        itemInfo.normalIcon = R.mipmap.ic_capture_ration_18v9_normal;
        itemInfo.tagList = new Object[]{CommonData.AspectRatio.ASPECT_18V9.getRatio()};
        data.add(itemInfo);
        itemInfo = new ItemListDialog.ItemInfo(new int[]{R.drawable.selector_capture_ratio_9v18});
        itemInfo.normalIcon = R.mipmap.ic_capture_ration_9v18_normal;
        itemInfo.tagList = new Object[]{CommonData.AspectRatio.ASPECT_9V18.getRatio()};
        data.add(itemInfo);
        itemInfo = new ItemListDialog.ItemInfo(new int[]{R.drawable.selector_capture_ratio_16v9});
        itemInfo.normalIcon = R.mipmap.ic_capture_ration_16v9_normal;
        itemInfo.tagList = new Object[]{CommonData.AspectRatio.ASPECT_16V9.getRatio()};
        data.add(itemInfo);
        itemInfo = new ItemListDialog.ItemInfo(new int[]{R.drawable.selector_capture_ratio_9v16});
        itemInfo.normalIcon = R.mipmap.ic_capture_ration_9v16_normal;
        itemInfo.tagList = new Object[]{CommonData.AspectRatio.ASPECT_9V16.getRatio()};
        data.add(itemInfo);
        itemInfo = new ItemListDialog.ItemInfo(new int[]{R.drawable.selector_capture_ratio_4v3});
        itemInfo.normalIcon = R.mipmap.ic_capture_ration_4v3_normal;
        itemInfo.tagList = new Object[]{CommonData.AspectRatio.ASPECT_4V3.getRatio()};
        data.add(itemInfo);
        itemInfo = new ItemListDialog.ItemInfo(new int[]{R.drawable.selector_capture_ratio_3v4});
        itemInfo.normalIcon = R.mipmap.ic_capture_ration_3v4_normal;
        itemInfo.tagList = new Object[]{CommonData.AspectRatio.ASPECT_3V4.getRatio()};
        data.add(itemInfo);
        itemInfo = new ItemListDialog.ItemInfo(new int[]{R.drawable.selector_capture_ratio_1v1});
        itemInfo.normalIcon = R.mipmap.ic_capture_ration_1v1_normal;
        itemInfo.tagList = new Object[]{CommonData.AspectRatio.ASPECT_1V1.getRatio()};
        data.add(itemInfo);
        return data;
    }

    public void addFxInfo(String key, CaptureFxModel.FxInfo value) {
        mFxModel.addFxInfo(key, value);
    }

    public CaptureFxModel.FxInfo getFxInfo(String key) {
        CaptureFxModel.FxInfo fxInfo = mFxModel.getFxInfo(key);
        if (fxInfo == null) {
            fxInfo = new CaptureFxModel.FxInfo();
            addFxInfo(key, fxInfo);
        }
        return fxInfo;
    }

    public int getCaptureGrade() {
        return mCaptureResolutionGrade;
    }

    public void setCaptureResolutionGrade(int captureResolutionGrade) {
        this.mCaptureResolutionGrade = captureResolutionGrade;
    }

    /**
     * Change flash.
     * 切换闪光
     */
    public void changeFlash() {
        if (mSupportFlash) {
            mStreamingContext.toggleFlash(!isFlashOn());
        }
    }

    private boolean isFlashOn() {
        return mStreamingContext.isFlashOn();
    }

    /**
     * Update settings with capability.
     * 获取采集设备能力，设置自动聚焦，曝光补偿，缩放
     *
     * @param deviceIndex the device index
     */
    public void updateSettingsWithCapability(int deviceIndex) {
        NvsStreamingContext.CaptureDeviceCapability capability;
        if (capabilities.containsKey(deviceIndex)) {
            capability = capabilities.get(deviceIndex);
        } else {
            capability = mStreamingContext.getCaptureDeviceCapability(deviceIndex);
            capabilities.put(deviceIndex, capability);
        }
        if (null == capability) {
            return;
        }
        mSupportFlash = capability.supportFlash;
        mSupportAutoFocus = capability.supportAutoFocus;
        mMinExpose = capability.minExposureCompensation;
        mMaxExpose = capability.maxExposureCompensation;
        mSupportAutoExposure = capability.supportExposureCompensation;
        mSupportZoom = capability.supportZoom;
        mSupportMaxZoom = capability.maxZoom;
        LogUtils.d("updateSettingsWithCapability: SupportFlash " + mSupportFlash +
                ",SupportAutoFocus" + mSupportAutoFocus + ", " +
                "SupportAutoExposure" + mSupportAutoExposure +
                ", minExpose = " + mMinExpose
                + ", maxExpose = " + mMaxExpose
                + ", mSupportZoom = " + mSupportZoom
                + ", mSupportMaxZoom = " + mSupportMaxZoom);
    }

    /**
     * Sets exposure compensation.
     * 设置曝光补偿，范围是 -1 到 1
     *
     * @param progress the progress
     */
    public void setExposureCompensation(float progress) {
        float middle = (mMaxExpose - mMinExpose) / 2F;
        mStreamingContext.setExposureCompensation((int) (middle * progress));
    }

    /**
     * 设置变焦
     * Sets zoom.
     *
     * @param zoom the zoom value
     */
    public void setZoom(int zoom) {
        mStreamingContext.setZoom(zoom);
    }

    public int getZoom() {
        return mStreamingContext.getZoom();
    }

    public boolean isSupportZoom() {
        return mSupportZoom;
    }

    public int getSupportMaxZoom() {
        return mSupportMaxZoom;
    }

    private int quickZoomTimes = 1;
    private int[] quickZoom = {0, 10, 20};

    public void setQuickZoomTimes(int quickZoomTimes) {
        this.quickZoomTimes = quickZoomTimes;
    }

    /**
     * 快速变焦
     * Get quick zoom value
     *
     * @return The value
     */
    public int getQuickZoom() {
        if (quickZoomTimes > quickZoom.length - 1) {
            quickZoomTimes = 0;
        }
        return quickZoom[quickZoomTimes++];
    }

    /**
     * Is recording boolean.
     * 是否正在录制
     *
     * @return the boolean
     */
    public boolean isRecording() {
        return getCurrentEngineState() == NvsStreamingContext.STREAMING_ENGINE_STATE_CAPTURERECORDING;
    }
}
