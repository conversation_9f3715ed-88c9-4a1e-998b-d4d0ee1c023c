package com.meishe.draft.db;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/14 20:51
 * @Description :本地草稿dao The dao for local draft.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
@Dao
public interface LocalDraftDao {
   @Insert
   void insertDraft(LocalDraftEntity... asset);

   @Update
   void updateDraft(LocalDraftEntity... asset);

   @Delete
   void deleteDraft(LocalDraftEntity... asset);

   @Query("DELETE  FROM LocalDraftEntity")
   void deleteAll();

   @Query("SELECT * FROM LocalDraftEntity WHERE id =  :uuid")
   LocalDraftEntity getDraft(String uuid);

   @Query("SELECT * FROM LocalDraftEntity")
   List<LocalDraftEntity> getDraft();
}
