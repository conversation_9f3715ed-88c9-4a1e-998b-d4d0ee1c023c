package com.meishe.myvideo.view.pop;

import android.content.Context;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.meishe.myvideo.R;
import com.meishe.third.pop.XPopup;
import com.meishe.third.pop.core.CenterPopupView;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>Z<PERSON>
 * @CreateDate :2020/12/3 16:36
 * @Description :删除草稿的弹窗 Delete the draft
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class DeleteDraftPop extends CenterPopupView {
    private EventListener mListener;

    /**
     * Create delete draft pop.
     * 创建删除草稿弹窗
     * @param context  the context 上下文
     * @param listener the listener 监听
     * @return the delete draft pop 删除草稿弹窗
     */
    public static DeleteDraftPop create(Context context, EventListener listener) {
        return (DeleteDraftPop) new XPopup.Builder(context)
                .asCustom(new DeleteDraftPop(context)
                        .setEventListener(listener));
    }

    public DeleteDraftPop(@NonNull Context context) {
        super(context);
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_delete_draft;
    }

    @Override
    protected int getPopupWidth() {
        return (int) getResources().getDimension(R.dimen.dp_px_660);
    }

    @Override
    protected int getPopupHeight() {
        return (int) getResources().getDimension(R.dimen.dp_px_375);
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        TextView tvDelete = findViewById(R.id.tv_delete);
        tvDelete.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mListener != null) {
                    mListener.onDelete();
                }
                dismiss();
            }
        });
        TextView tvCancel = findViewById(R.id.tv_cancel);
        tvCancel.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mListener != null) {
                    dismiss();
                    mListener.onCancel();
                }
            }
        });
    }

    /**
     * 设置事件监听
     * Set event listener
     *
     * @param listener EventListener the listener
     * @return the event listener
     */
    public DeleteDraftPop setEventListener(EventListener listener) {
        mListener = listener;
        return this;
    }

    /**
     * The interface Event listener.
     * 事件监听的接口
     */
    public interface EventListener {
        /**
         * On delete.
         * 删除
         */
        void onDelete();

        /**
         * On cancel.
         * 取消
         */
        void onCancel();
    }
}
