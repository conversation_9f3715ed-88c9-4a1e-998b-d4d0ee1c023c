package com.meishe.speaker;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.iflytek.cloud.ErrorCode;
import com.iflytek.cloud.InitListener;
import com.iflytek.cloud.RecognizerListener;
import com.iflytek.cloud.RecognizerResult;
import com.iflytek.cloud.SpeechConstant;
import com.iflytek.cloud.SpeechError;
import com.iflytek.cloud.SpeechRecognizer;
import com.iflytek.cloud.msc.util.FileUtil;
import com.iflytek.cloud.util.ResourceUtil;
import com.meicam.sdk.NvsMediaFileConvertor;
import com.meishe.base.utils.FileIOUtils;
import com.meishe.base.utils.GsonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.MediaTypeUtils;
import com.meishe.base.utils.Utils;
import com.meishe.engine.util.PathUtils;
import com.meishe.speaker.bean.Speech;
import com.meishe.speaker.bean.VoiceParam;

import java.util.ArrayList;
import java.util.Hashtable;
import java.util.List;

import static com.iflytek.cloud.SpeechError.TIP_ERROR_GROUP_EMPTY;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/3/1 15:38
 * @Description :讯飞语音听写帮助类 Voice dictation help class
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class VoiceDictationHelper {
    /**
     * 讯飞能识别的最长语音是60s
     * The longest speech that iFlytek can recognize is 60 seconds
     */
    private final long MAX_SPEECH_TIME = 10000000;
    /**
     * 最大并发量
     * Maximum concurrency
     */
    private final int MAX_THREAD_NUM = 4;
    /**
     * 使用本地语音文件
     * Use local voice files
     */
    private boolean mUseLocalAudio;
    private SpeechRecognizer mRecognizer;
    private NvsMediaFileConvertor mMediaConvert;
    private ConvertCallback mConvertCallback;
    private List<TaskInfo> mTaskList;

    /**
     * 当前正在识别的音频参数
     * The current voice parameter.
     */
    private VoiceParam mCurrentVoiceParam;

    public VoiceDictationHelper() {

    }

    /*private static class Holder {
        private static VoiceDictationHelper INSTANCE = new VoiceDictationHelper();
    }

    public static VoiceDictationHelper get() {
        return Holder.INSTANCE;
    }*/

    public void setRecognizer(SpeechRecognizer recognizer){
        mRecognizer = recognizer;
    }

    /**
     * 初始化
     * Init
     *
     * @param context the context
     */
    public void init(Context context) {
        if (mRecognizer == null) {
            mRecognizer = SpeechRecognizer.createRecognizer(context, new InitListener() {
                @Override
                public void onInit(int code) {
                    LogUtils.d("code=" + code);
                    if (code == ErrorCode.SUCCESS) {
                        setParam();
                    }
                    //"初始化失败，错误码：" + code+",请点击网址https://www.xfyun.cn/document/error-code查询解决方案";
                    //Initialization failed with error code: "+code+", please click on the website https://www.xfyun.cn/document/error-code Query Solution ";
                }
            });
        }
    }

    /**
     * 参数设置
     * Set params
     */
    private void setParam() {
        // 清空参数 Clear parameter.
        mRecognizer.setParameter(SpeechConstant.PARAMS, null);
        // 设置引擎 Set Engine.
        // mRecognizer.setParameter(SpeechConstant.ENGINE_TYPE, SpeechConstant.TYPE_LOCAL);
        mRecognizer.setParameter(SpeechConstant.ENGINE_TYPE, SpeechConstant.TYPE_CLOUD);
        // 设置返回结果格式 Format return results.
        mRecognizer.setParameter(SpeechConstant.RESULT_TYPE, "json");
        //mIat.setParameter(MscKeys.REQUEST_AUDIO_URL,"true");
        // 设置本地识别资源,如果是离线识别，需要设置
        // Set local recognition resources. If it is offline recognition, it needs to be set
        // mRecognizer.setParameter(ResourceUtil.ASR_RES_PATH, getLocalResource());

        //设置方言，默认即可,在线听写支持多种小语种，若想了解请下载在线听写能力，参看其speechDemo
        //Set the dialect by default. Online dictation supports multiple minor languages. If you want to learn more, please download the online dictation ability and refer to its speechDemo.
        mRecognizer.setParameter(SpeechConstant.ACCENT, "mandarin");
        // 设置语言
        //set language
        mRecognizer.setParameter(SpeechConstant.LANGUAGE, "zh_cn");

        // 设置语音前端点:静音超时时间，即用户多长时间不说话则当做超时处理
        //Set the endpoint before voice: mute timeout, which means that the user will be treated as timeout if they do not speak for a long time.
        mRecognizer.setParameter(SpeechConstant.VAD_BOS, "10000");

        // 设置语音后端点:后端点静音检测时间，即用户停止说话多长时间内即认为不再输入， 自动停止录音
        // Set the endpoint after voice: the backend point's mute detection time, which means that the user stops speaking for a certain amount of time, and it is considered that there is no longer input, automatically stopping recording.
        mRecognizer.setParameter(SpeechConstant.VAD_EOS, "10000");

        // 设置标点符号,设置为"0"返回结果无标点,设置为"1"返回结果有标点
        // Set punctuation mark to "0" to return results without punctuation, and set to "1" to return results with punctuation.
        mRecognizer.setParameter(SpeechConstant.ASR_PTT, "1");
        mRecognizer.setParameter(SpeechConstant.KEY_SPEECH_TIMEOUT, "30000");

        // 设置音频保存路径，保存音频格式支持pcm、wav，设置路径为sd卡请注意WRITE_EXTERNAL_STORAGE权限
        // Set the audio save path. The save audio format supports pcm and wav, and set the path to SD card. Please pay attention to WRITE_ EXTERNAL_ STORAGE permission.
        // mRecognizer.setParameter(SpeechConstant.AUDIO_FORMAT, "wav");
        //mRecognizer.setParameter(SpeechConstant.ASR_AUDIO_PATH, Environment.getExternalStorageDirectory() + "/msc/iat.wav");
    }

    /**
     * 获取离线资源
     */
    private String getLocalResource() {
        //识别通用资源
        //识别8k资源-使用8k的时候请解开注释
        //Identify common resources
        //Identify 8k resources - please untie comments when using 8k
        return ResourceUtil.generateResourcePath(Utils.getApp(), ResourceUtil.RESOURCE_TYPE.assets, "iat/common.jet") +
                ";" + ResourceUtil.generateResourcePath(Utils.getApp(), ResourceUtil.RESOURCE_TYPE.assets, "iat/sms_16k.jet");
    }

    /**
     * 使用录音进行听写
     * 注意;录音参数配置要符合讯飞要求才能听写出结果
     * Dictation using a tape recorder
     * Be careful; The recording parameter configuration must meet the requirements of
     * iFlytek in order to listen and write the results.
     *
     * @param listener the listener
     */
    public void startDictation(SpeechListener listener) {
        if (mRecognizer == null || isListening()) {
            //识别中，就别再次识别了。In recognition, don't recognize it again.
            LogUtils.e("isListening,return");
            return;
        }
        if (mUseLocalAudio) {
            setParam();
            mUseLocalAudio = false;
        }
        int errCode = mRecognizer.startListening(listener);
        if (errCode != ErrorCode.SUCCESS) {
            LogUtils.e("voice,errCode=" + errCode);
        }
    }

    /**
     * 使用本地录音文件听写
     * 注意：返回的结果已经处理了inPoint
     * Dictation using a local audio file
     *
     * @param paramList the param list
     * @param listener  the listener
     */
    public void startDictation(final List<VoiceParam> paramList, final SpeechListener listener) {
        if (mRecognizer == null || isListening()) {
            //识别中，就别再次识别了。In recognition, don't recognize it again.
            if (listener != null) {
                listener.onError(null);
            }
            return;
        }
        if (paramList == null || paramList.size() <= 0 || listener == null) {
            if (listener != null) {
                listener.onError(null);
            }
            return;
        }
        listener.onBeginOfSpeech();
        //适配android 11 后，路径带有content，根据后缀判断检查是无效的。
        // After adapting to Android 11, the path contains content, and the check is invalid based on the suffix.
        // if (checkValidity(paramList)) {
        mCurrentVoiceParam = paramList.get(0);
        startDictation(mCurrentVoiceParam, new SpeechListener() {
            private Speech speech;
            private int index = 0;
            private int firstNotNullIndex;

            @Override
            public void onSampleResult(Speech result) {
                if (speech == null) {
                    speech = result;
                    firstNotNullIndex = index;
                }
                if (index < paramList.size() && speech != null) {
                    //拼装每次的听写结果
                    // Assemble each dictation result.
                    List<Speech.Words> ws = speech.getWs();
                    //这里只是对整体的入点更改，不用考虑变速
                    // This is only a change to the overall input point, without considering the gear change.
                    if (index > firstNotNullIndex && result != null) {
                        //第一次不添加
                        // Not adding for the first time.
                        ws.addAll(result.getWs());
                    }
                }
                ++index;
            }

            @Override
            public void onEndOfSpeech() {
                //LogUtils.d("paramList.size=" + paramList.size() + ",index=" + index);
                if (index < paramList.size()) {
                    //继续下一个听写
                    // Continue with the next dictation.
                    mCurrentVoiceParam = paramList.get(index);
                    startDictation(mCurrentVoiceParam, this);
                } else {
                    if (speech == null) {
                        listener.onError(new SpeechError(TIP_ERROR_GROUP_EMPTY));
                    } else {
                        //所有结果听写完毕，回调。
                        // After all the results have been dictated, call back.
                        listener.onSampleResult(speech);
                        listener.onEndOfSpeech();
                    }
                }
            }

            @Override
            public void onError(SpeechError speechError) {
                ++index;
                if (index < paramList.size()) {
                    //继续下一个听写
                    // Continue with the next dictation.
                    mCurrentVoiceParam = paramList.get(index);
                    startDictation(mCurrentVoiceParam, this);
                } else {
                    //所有结果听写完毕，回调。
                    // After all the results have been dictated, call back.
                    listener.onSampleResult(speech);
                    listener.onEndOfSpeech();
                }
            }
        });
        //  } else {
        //      listener.onError(new SpeechError(TIP_ERROR_GROUP_EMPTY));
        // }
    }

    /**
     * 使用本地录音文件听写
     * 注意：返回的结果没有处理inPoint
     * Dictation using a local audio file
     * Note: The returned result did not handle inPoint
     *
     * @param param    the param
     * @param listener the listener
     */
    public void startDictation(VoiceParam param, final SpeechListener listener) {
        if (mRecognizer == null || isListening()) {
            //识别中，就别再次识别了。In recognition, don't recognize again.
            return;
        }
        if (param == null || TextUtils.isEmpty(param.getPath()) || listener == null) {
            return;
        }
        if (!mUseLocalAudio) {
            setParam();
            // 直接设置音频文件路径识别（要求设置文件在sdcard上的全路径）,先上传文件再识别速度较慢
            //Directly setting the audio file path recognition (requires setting the full path of the file on the SD card), uploading the file first and then recognizing it is slower.
            // mRecognizer.setParameter(SpeechConstant.AUDIO_SOURCE, "-2");
            //音频流 The audio
            mRecognizer.setParameter(SpeechConstant.AUDIO_SOURCE, "-1");
            mUseLocalAudio = true;
        }
        SpeechListener newListener = new SpeechListener() {
            @Override
            public void onSampleResult(Speech result) {
                listener.onSampleResult(result);
            }

            @Override
            public void onVolumeChanged(int i, byte[] bytes) {
                listener.onVolumeChanged(i, bytes);
            }

            @Override
            public void onBeginOfSpeech() {
                listener.onBeginOfSpeech();
            }

            @Override
            public void onEndOfSpeech() {
                clearListener();
                listener.onEndOfSpeech();
            }

            @Override
            public void onError(SpeechError speechError) {
                clearListener();
                listener.onError(speechError);
            }

        };
        //讯飞语音听写最多支持60秒时长,而且有各种限制，所以先转化一下。
        // IFlytek voice dictation supports a maximum duration of 60 seconds and has various limitations,
        // so convert it first.
        convertPcm(param, newListener);
    }

    /**
     * 转化成所需的Pcm文件，并用之进行语音听写。
     * Convert to the required PCM file and use it for voice dictation
     *
     * @param param    the  param
     * @param listener the listener
     */
    private void convertPcm(VoiceParam param, SpeechListener listener) {
        int totalNum = (int) Math.ceil((double) (param.getTrOutP() - param.getTrInP()) / MAX_SPEECH_TIME);
        if (mMediaConvert == null) {
            mMediaConvert = new NvsMediaFileConvertor();
            mTaskList = new ArrayList<>();
            mMediaConvert.setMeidaFileConvertorCallback(mConvertCallback = new ConvertCallback() {
                long tempTaskId = 0;

                @Override
                public void onFinish(final long taskId, String srcFile, final String dstFile, int errorCode) {
                    LogUtils.d("taskId = " + taskId + ",dstFile =" + dstFile + ", errorCode=" + errorCode + ",taskSize=" + mTaskList.size());
                    for (TaskInfo task : mTaskList) {
                        if (task.taskId == taskId) {
                            task.desPath = dstFile;
                            break;
                        }
                    }//语音听写不支持并发....Voice dictation does not support concurrency
                    if (isListening()) {
                        LogUtils.d("isListening ,wait");
                        return;
                    } else {
                        tempTaskId = taskId;
                        // LogUtils.d("start listener,taskId=" + taskId);
                    }
                    //写了并发逻辑，但是语音听写不支持并发....
                    // Written concurrency logic, but voice dictation does not support concurrency
                    int errCode = mRecognizer.startListening(new SpeechListener() {
                        @Override
                        public void onSampleResult(Speech result) {
                            int lastNum = mCurrentNum;
                            if (result != null) {
                                for (TaskInfo task : mTaskList) {
                                    if (task.taskId == tempTaskId) {
                                        task.speechList.add(result);
                                        task.finished = result.isLs();
                                        if (task.finished) {
                                            mCurrentNum++;
                                        }
                                        break;
                                    }
                                }
                            } else {
                                for (TaskInfo task : mTaskList) {
                                    if (task.taskId == tempTaskId) {
                                        mTaskList.remove(task);
                                        mCurrentNum++;
                                        break;
                                    }
                                }
                            }
                            //LogUtils.d("lastNum=" + lastNum + ",mCurrentNum=" + mCurrentNum + ",totalNum=" + mConvertTotalNum + ",tempTaskId=" + tempTaskId);
                            if (lastNum == mCurrentNum) {
                                //串行的，如果数量没变化，说明该听写还在继续
                                // Serial, if the quantity remains unchanged, it indicates
                                // that the dictation is still ongoing.
                                return;
                            }
                            if (mCurrentNum == mConvertTotalNum) {
                                if (mTaskList.size() <= 0) {
                                    mCurrentNum = 0;
                                    if (mListener != null) {
                                        mListener.onError(new SpeechError(TIP_ERROR_GROUP_EMPTY));
                                    }
                                    return;
                                }
                                TaskInfo taskInfo = mTaskList.get(0);
                                //全部听写完毕，拼装听写结果
                                // Complete all dictation and assemble the dictation results.
                                Speech speech = taskInfo.getSpeech();
                                long belongInPoint = taskInfo.belongInPoint;
                                long extraTime;
                                //取出第一次听写结果，后边的听写结果都合并到第一次听写结果中
                                // Take out the first dictation result, and merge the subsequent
                                // dictation results into the first dictation result.
                                List<Speech.Words> ws = speech.getWs();
                                String path = mTaskList.get(0).desPath;
                                if (!TextUtils.isEmpty(path)) {
                                    FileUtil.deleteFile(path);
                                }
                                if (ws == null || ws.size() <= 0) {
                                    mTaskList.clear();
                                    mCurrentNum = 0;
                                    if (mListener != null) {
                                        mListener.onError(new SpeechError(TIP_ERROR_GROUP_EMPTY));
                                    }
                                    return;
                                }
                                updateWordsBg(speech, 0, taskInfo.speed, belongInPoint);
                                for (int i = 1; i < mTaskList.size(); i++) {
                                    taskInfo = mTaskList.get(i);
                                    if (belongInPoint == taskInfo.belongInPoint) {
                                        //同一个入点，除了第一个分隔片段，其他都需要根据变速值更改额外入点
                                        // For the same entry point, except for the first separated segment,
                                        // all other additional entry points need to be changed based on the variable speed value.
                                        extraTime = (long) (taskInfo.inPoint / 1000 / 10 / taskInfo.speed);
                                    } else {
                                        belongInPoint = taskInfo.belongInPoint;
                                        extraTime = taskInfo.inPoint / 1000 / 10;
                                    }
                                    if (updateWordsBg(taskInfo.getSpeech(), extraTime, taskInfo.speed,
                                            taskInfo.belongInPoint)) {
                                        ws.addAll(taskInfo.getSpeech().getWs());
                                    }
                                    if (!TextUtils.isEmpty(taskInfo.desPath)) {
                                        FileUtil.deleteFile(taskInfo.desPath);
                                    }
                                }
                              /*  for (int i = 0; i < speech.getWs().size(); i++) {
                                    LogUtils.d("after,words=" + speech.getWs().get(i));
                                }*/
                                mTaskList.clear();//注意要先清空，再回调 Be sure to clear first and then call back
                                stopDictation();
                                mCurrentNum = 0;
                                if (mListener != null) {
                                    mListener.onSampleResult(speech);
                                    mListener.onEndOfSpeech();
                                }
                            } else if (mCurrentNum % MAX_THREAD_NUM == 0) {
                                //每个回合都完毕后，再进行下一轮，（若优化，可以完事一个就开启下次转化）
                                // After each round is completed, proceed to the next round
                                // (if optimized, you can complete one and start the next conversion)
                                int threadNum = mConvertTotalNum - mTaskList.size();
                                if (threadNum > MAX_THREAD_NUM) {
                                    threadNum = MAX_THREAD_NUM;
                                }
                                //这里需要用mCurrentVoiceParam， 不能用final 的 param
                                //Here is need mCurrentVoicePara, not the final pram.
                                startConvert(mCurrentVoiceParam, mTaskList, threadNum);
                            } else {
                                //语音听写不支持并发，所以这里串行处理一下...
                                // Voice dictation does not support concurrency, so here we will process it serially.
                                for (TaskInfo task : mTaskList) {
                                    if (!task.finished && !TextUtils.isEmpty(task.desPath)) {
                                        tempTaskId = task.taskId;
                                        //mRecognizer.setParameter(SpeechConstant.ASR_SOURCE_PATH, task.desPath);
                                        int errCode = mRecognizer.startListening(this);
                                        if (errCode != ErrorCode.SUCCESS) {
                                            LogUtils.e("audio,errCode=" + errCode);
                                        } else {
                                            //设置路径是上传文件进行识别
                                            // Setting the path involves uploading files for recognition.
                                            //mRecognizer.setParameter(SpeechConstant.ASR_SOURCE_PATH, dstFile);
                                            //读取音频流 Read audio stream
                                            byte[] bytes = FileIOUtils.readFile2BytesByStream(task.desPath);
                                            int length = bytes == null ? 0 : bytes.length;
                                            mRecognizer.writeAudio(bytes, 0, length);
                                            mRecognizer.stopListening();
                                        }
                                        //LogUtils.d("task=" + task);
                                        break;
                                    }
                                }
                            }
                        }

                        @Override
                        public void onError(SpeechError error) {
                            if (!TextUtils.isEmpty(dstFile)) {
                                FileUtil.deleteFile(dstFile);
                            }
                            LogUtils.e("onError=" + error.toString());
                            onSampleResult(null);
                        }
                    });
                    if (errCode != ErrorCode.SUCCESS) {
                        LogUtils.e("audio,errCode=" + errCode);
                    } else {
                        //设置路径是上传文件进行识别
                        // Setting the path involves uploading files for recognition.
                        //mRecognizer.setParameter(SpeechConstant.ASR_SOURCE_PATH, dstFile);
                        //读取音频流 Read audio stream
                        byte[] bytes = FileIOUtils.readFile2BytesByStream(dstFile);
                        int length = bytes == null ? 0 : bytes.length;
                        mRecognizer.writeAudio(bytes, 0, length);
                        mRecognizer.stopListening();
                    }
                }

                @Override
                public void notifyAudioMuteRage(long taskId, long startPts, long endPts) {

                }
            }, true);
        }
        int threadNum = totalNum;
        if (totalNum > MAX_THREAD_NUM) {
            threadNum = MAX_THREAD_NUM;
        }
        mTaskList.clear();
        listener.onBeginOfSpeech();
        //注意此处，因为callback只设了一次，所以如果参数设置成了final listener，listener永远是第一次的listener，
        // 因此这里每次都设置了listener
        //Note here that because the callback is only set once, if the parameter is set to final listener,
        // the listener will always be the first listener.Therefore, a listener is set every time here.
        mConvertCallback.mConvertTotalNum = totalNum;
        mConvertCallback.mCurrentNum = 0;
        mConvertCallback.setSpeechListener(listener);
        startConvert(param, mTaskList, threadNum);
        //LogUtils.d("param=" + param + ",threadNum=" + threadNum + ",totalNum=" + totalNum + ",taskSize=" + mTaskList.size());
    }

    /**
     * 开始分段转化
     * Start convert
     *
     * @param param     the param
     * @param taskList  the task list
     * @param threadNum the thread number
     */
    private void startConvert(VoiceParam param, List<TaskInfo> taskList, int threadNum) {
        final int totalNum = (int) Math.ceil((double) (param.getTrOutP() - param.getTrInP()) / MAX_SPEECH_TIME);
        Hashtable<String, Object> config = new Hashtable<>();
        config.put(NvsMediaFileConvertor.CONVERTOR_NO_VIDEO, true);
        config.put(NvsMediaFileConvertor.CONVERTOR_AUDIO_CHANNEL_MAP, "left");
        config.put(NvsMediaFileConvertor.CONVERTOR_CUSTOM_AUDIO_CHANNEL, 1);
        config.put(NvsMediaFileConvertor.CONVERTOR_CUSTOM_AUDIO_PCM_FILE, true);
        config.put(NvsMediaFileConvertor.CONVERTOR_CUSTOM_AUDIO_SAMPLE_RATE, 16000);
        int tempNum = 0;
        while (tempNum < threadNum) {
            TaskInfo taskInfo = new TaskInfo();
            taskList.add(taskInfo);
            taskInfo.index = taskList.size();
            taskInfo.belongInPoint = param.getInP();
            taskInfo.speed = param.getSpeed();
            taskInfo.trimInPoint = param.getTrInP() + MAX_SPEECH_TIME * (taskInfo.index - 1);
            taskInfo.inPoint = MAX_SPEECH_TIME * (taskInfo.index - 1);
            if (taskInfo.index == totalNum) {
                //最后一个 The last
                taskInfo.duration = (param.getTrOutP() - param.getTrInP()) - MAX_SPEECH_TIME * (totalNum - 1);
            } else {
                taskInfo.duration = MAX_SPEECH_TIME;
            }
            tempNum++;
            taskInfo.taskId = mMediaConvert.convertMeidaFile(param.getPath(),
                    getConvertFile(taskInfo.index + ""), false, taskInfo.trimInPoint, taskInfo.trimInPoint + taskInfo.duration, config);
            //LogUtils.d("taskInfo=" + taskInfo);
        }
    }

    private String getConvertFile(String num) {
        String name = num + "video_audio.pcm";
        return PathUtils.getAudioRecordFilePath() + "/" + name;
    }

    /**
     * 检查合法性
     * Check validity
     *
     * @param paramList the params
     */
    private boolean checkValidity(List<VoiceParam> paramList) {
        for (int i = 0; i < paramList.size(); i++) {
            VoiceParam voiceParam = paramList.get(i);
            if (!MediaTypeUtils.isAudioFileType(voiceParam.getPath())
                    && !MediaTypeUtils.isVideoFileType(voiceParam.getPath())) {
                paramList.remove(i);
                i--;
            }
        }
        return paramList.size() > 0;
    }

    /**
     * 更新文字的开始时间，用于语句拼接
     * Updates the start time of the text for statement concatenation
     *
     * @param speech  the speech
     * @param extraBg the extra begin time
     */
    private boolean updateWordsBg(Speech speech, long extraBg, double speed, long belongInPoint) {
        boolean success = false;
        if (speech != null && speech.getWs() != null && speech.getWs().size() > 0) {
            for (int j = 0; j < speech.getWs().size(); j++) {
                Speech.Words words = speech.getWs().get(j);
                if (words.isEmpty()) {
                    speech.getWs().remove(j);
                    j--;
                }
                if (j > 0 && speech.isPunctuation(words.getCw() == null ? null : words.getCw().get(0))) {
                    Speech.Words preWord = speech.getWs().get(j - 1);
                    //这里是因为语句拼接的时候，每句的最后一个标点的bg可能是0，如果是0会影响整体语句的断句，
                    //所以这里纠正一下。
                    //This is because during sentence concatenation, the bg of the last punctuation
                    // point in each sentence may be 0. If it is 0,
                    // it will affect the overall sentence break,So correct it here.
                    if (words.getBg() == 0 && preWord.getBg() > 0) {
                        success = true;
                        words.setBg((int) (preWord.getBg() / speed));
                        words.setBelongInPoint(belongInPoint);
                        continue;
                    }
                }
                success = true;
                words.setBg((int) (words.getBg() / speed + extraBg + belongInPoint / 1000 / 10));
                words.setBelongInPoint(belongInPoint);
                // LogUtils.d("words=" + words + ",speed=" + speed + ",extraBg=" + extraBg + ",belongInPoint=" + belongInPoint + ",j=" + j);
            }
        }
        return success;
    }

    /**
     * 停止听写
     * Stop dictation
     */
    public void stopDictation() {
        if (isListening()) {
            mRecognizer.stopListening();
        }
    }

    /**
     * 取消听写
     * Cancel dictation
     */
    public void cancelDictation() {
        if (mTaskList != null && mTaskList.size() > 0) {
            for (int i = 0; i < mTaskList.size(); i++) {
                TaskInfo taskInfo = mTaskList.get(i);
                mMediaConvert.cancelTask(taskInfo.taskId);
                mTaskList.remove(taskInfo);
                i--;
            }
        }
        if (isListening()) {
            mRecognizer.cancel();
        }
        clearListener();
    }

    /**
     * 是否正在听写中
     * is listening ?
     */
    public boolean isListening() {
        return mRecognizer != null && mRecognizer.isListening();
    }

    /**
     * 清除监听
     * Clear listener
     */
    private void clearListener() {
        if (mConvertCallback != null) {
            mConvertCallback.setSpeechListener(null);
        }
    }

    /**
     * 媒体转化辅助实体类
     * Media Conversion Assist Entity class
     */
    private class TaskInfo {
        private int index;
        private long taskId;
        private long inPoint;
        private long trimInPoint;
        private long duration;
        private double speed;
        private List<Speech> speechList = new ArrayList<>();
        private boolean finished;
        private String desPath;
        private long belongInPoint;
        private boolean assembled = false;

        private Speech getSpeech() {
            if (speechList.size() > 0) {
                Speech speech = speechList.get(0);
                List<Speech.Words> ws = speech.getWs();
                if (ws == null) {
                    return new Speech();
                }
                if (!assembled) {
                    for (int i = 1; i < speechList.size(); i++) {
                        Speech item = speechList.get(i);
                        if (item != null && item.getWs() != null) {
                            ws.addAll(item.getWs());
                        }
                    }
                    assembled = true;
                }
                return speech;
            }
            return new Speech();
        }

        @NonNull
        @Override
        public String toString() {
            return "TaskInfo(index=" + index + ",taskId=" + taskId + ",inPoint=" + inPoint
                    + ",duration=" + duration + ",finished=" + finished + ",speech=" + getSpeech()
                    + ",speed=" + speed + ",belongInPoint=" + belongInPoint + ")";
        }
    }

    /**
     * 讯飞语音转化监听类
     * IFlytek voice conversion monitoring class
     */
    public static abstract class SpeechListener implements RecognizerListener {

        public List<List<VoiceParam>> onPrepare(int type){
            return null;
        }

        public void onSampleResult(Speech result) {

        }

        @Override
        public void onVolumeChanged(int i, byte[] bytes) {

        }

        @Override
        public void onBeginOfSpeech() {

        }

        @Override
        public void onEndOfSpeech() {

        }

        @Override
        public void onResult(RecognizerResult result, boolean b) {
            //LogUtils.d("result=" + result.getResultString());
            if (!TextUtils.isEmpty(result.getResultString())) {
                onSampleResult(GsonUtils.fromJson(result.getResultString(), Speech.class));
            }
        }

        @Override
        public void onEvent(int eventType, int i1, int i2, Bundle bundle) {
           /* if (SpeechEvent.EVENT_SESSION_ID == eventType) {
                String sid = bundle.getString(SpeechEvent.KEY_EVENT_SESSION_ID);
                LogUtils.d("sid=" + sid);
            }*/
        }
    }

    private abstract class ConvertCallback implements NvsMediaFileConvertor.MeidaFileConvertorCallback {
        SpeechListener mListener;
        int mConvertTotalNum = 0;
        int mCurrentNum = 0;

        private void setSpeechListener(SpeechListener listener) {
            mListener = listener;
        }

        @Override
        public void onProgress(long l, float v) {

        }

        @Override
        public void notifyAudioMuteRage(long l, long l1, long l2) {

        }
    }
}
