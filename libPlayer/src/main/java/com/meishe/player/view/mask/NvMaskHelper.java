package com.meishe.player.view.mask;

import android.graphics.Bitmap;
import android.graphics.Matrix;
import android.graphics.Path;
import android.graphics.PointF;
import android.graphics.RectF;

import com.meicam.sdk.NvsMaskRegionInfo;
import com.meishe.engine.bean.MeicamMaskRegionInfo;
import com.meishe.engine.bean.MeicamPosition2D;
import com.meishe.engine.bean.NvMaskModel;
import com.meishe.engine.bean.Transform;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: Chu<PERSON>henGuang
 * @CreateDate: 2021/7/21 11:22
 * @Description:
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class NvMaskHelper {
    private static float centerPathRadius = 10;


    public static Path linePath(PointF center, PointF liveWindowSize, float rotation) {

        MeicamMaskRegionInfo regionInfo = lineRegionInfo(center, liveWindowSize, rotation);
        Path path = new Path();
        List<MeicamPosition2D> position2DS = regionInfo.getLocalRegionInfoArray().get(0).getPoints();
        MeicamPosition2D rightBottomPoint = position2DS.get(2);
        MeicamPosition2D leftBottomPoint = position2DS.get(4);

        path.moveTo(leftBottomPoint.x, leftBottomPoint.y);
        PointF leftPoint = new PointF(center.x - centerPathRadius, center.y);
        path.lineTo(leftPoint.x, leftPoint.y);

        path.moveTo(rightBottomPoint.x, rightBottomPoint.y);
        PointF rightPoint = new PointF(center.x + centerPathRadius, center.y);
        path.lineTo(rightPoint.x, rightPoint.y);

        path.moveTo(center.x, center.y);
        path.addCircle(center.x, center.y, centerPathRadius, Path.Direction.CW);
        path.close();
        return path;
    }
    /**
     * 线性五个点做区分
     * Linear five points for differentiation
     *
     * @param center the center center
     * @param liveWindowSize the liveWindow size
     * @param rotation the rotation
     * @return the MeicamMaskRegionInfo
     */
    private static MeicamMaskRegionInfo lineRegionInfo(PointF center, PointF liveWindowSize, float rotation) {
        float boxWidth = liveWindowSize.x * 3;
        float boxHeight = liveWindowSize.y * 3;

        PointF leftTopPoint = new PointF(center.x - boxWidth, -(center.y + boxHeight));

        PointF rightTopPoint = new PointF(center.x + boxWidth, -(center.y + boxHeight));

        PointF rightBottomPoint = new PointF(center.x + boxWidth, center.y);
        //中间点 为了区分 The middle point is used to distinguish.
        PointF infoCenterPoint = new PointF(center.x, center.y);

        PointF leftBottomPoint = new PointF(center.x - boxWidth, center.y);

        return buildPolygonMaskRegionInfo(new PointF[]{getPointByAngle(leftTopPoint, center, rotation),
                getPointByAngle(rightTopPoint, center, rotation),
                getPointByAngle(rightBottomPoint, center, rotation),
                getPointByAngle(infoCenterPoint, center, rotation),
                getPointByAngle(leftBottomPoint, center, rotation)});
    }

    protected static Path mirrorPath(PointF center, PointF maskSize, float rotation) {
        MeicamMaskRegionInfo regionInfo = mirrorRegionInfo(center, maskSize, rotation);
        List<MeicamPosition2D> position2DS = regionInfo.getLocalRegionInfoArray().get(0).getPoints();
        Path path = new Path();
        MeicamPosition2D firstValue = position2DS.get(0);
        path.moveTo(firstValue.x, firstValue.y);
        for (MeicamPosition2D position2D : position2DS) {
            path.lineTo(position2D.x, position2D.y);
        }
//        path.moveTo(position2DS.get(0).x, position2DS.get(0).y);
//        path.lineTo(position2DS.get(1).x, position2DS.get(1).y);
//        path.moveTo(position2DS.get(3).x, position2DS.get(3).y);
//        path.lineTo(position2DS.get(2).x, position2DS.get(2).y);
        path.moveTo(center.x, center.y);
        //这里添加一个圆 Add a circle here.
        path.addCircle(center.x, center.y, centerPathRadius, Path.Direction.CW);
        path.close();
        return path;
    }

    /**
     * 镜像六个点
     * Mirror six points
     *
     * @param center
     * @param maskSize
     * @param rotation
     * @return
     */
    private static MeicamMaskRegionInfo mirrorRegionInfo(PointF center, PointF maskSize, float rotation) {
        float boxWidth = maskSize.x;
        float boxHeight = maskSize.y;
        PointF leftTopPoint = new PointF(center.x - boxWidth, center.y - boxHeight * 0.5f);
        PointF topCenterPoint = new PointF(center.x, center.y - boxHeight * 0.5f);
        PointF rightTopPoint = new PointF(center.x + boxWidth, center.y - boxHeight * 0.5f);
        PointF rightBottomPoint = new PointF(center.x + boxWidth, center.y + boxHeight * 0.5f);
        PointF bottomCenterPoint = new PointF(center.x, center.y + boxHeight * 0.5f);
        PointF leftBottomPoint = new PointF(center.x - boxWidth, center.y + boxHeight * 0.5f);
        return buildPolygonMaskRegionInfo(new PointF[]{getPointByAngle(leftTopPoint, center, rotation),
                getPointByAngle(topCenterPoint, center, rotation),
                getPointByAngle(rightTopPoint, center, rotation),
                getPointByAngle(rightBottomPoint, center, rotation),
                getPointByAngle(bottomCenterPoint, center, rotation),
                getPointByAngle(leftBottomPoint, center, rotation)});
    }


    public static Path rectPath(PointF center, PointF maskSize, float cornerRadiusRate) {
        float cornerRadius = cornerRadiusRate * maskSize.y * 0.5f;
        if (maskSize.x < maskSize.y) {
            cornerRadius = cornerRadiusRate * maskSize.x * 0.5f;
        }
        float maskWidth = maskSize.x;
        float maskHeight = maskSize.y;
        Path path = new Path();
        //直接绘制一个圆形矩形 Draw a circular rectangle directly.
        path.addRoundRect(new RectF(center.x - maskWidth / 2F, center.y - maskHeight / 2F,
                center.x + maskWidth / 2F, center.y + maskHeight / 2F), cornerRadius, cornerRadius, Path.Direction.CCW);
        path.moveTo(center.x, center.y);
        //这里添加一个圆 Add a circle here.
        path.addCircle(center.x, center.y, centerPathRadius, Path.Direction.CW);
        path.close();
        return path;
    }

    public static MeicamMaskRegionInfo rectRegionInfo(PointF liveWindowSize, PointF assetResolution, PointF maskSize, NvMaskModel maskModel, float rotation) {
        float maskWidth = maskSize.x;
        float maskHeight = maskSize.y;
        maskWidth /= maskModel.transform.scaleX;
        maskHeight /= maskModel.transform.scaleY;
        PointF center = new PointF(liveWindowSize.x * 0.5f, liveWindowSize.y * 0.5f);
        float cornerRadiusRate = maskModel.cornerRadiusRate;
        float cornerRadius = cornerRadiusRate * maskHeight * 0.5f;
        if (maskWidth < maskHeight) {
            cornerRadius = cornerRadiusRate * maskWidth * 0.5f;
        }
        float controlPointDis = cornerRadius * 0.45f;
        MeicamMaskRegionInfo nvsMaskRegionInfo = new MeicamMaskRegionInfo();
        MeicamMaskRegionInfo.RegionInfo regionInfo = new MeicamMaskRegionInfo.RegionInfo(NvsMaskRegionInfo.MASK_REGION_TYPE_CUBIC_CURVE);

        MeicamMaskRegionInfo.Transform2D transform2D = regionInfo.getTransform2D();
        transform2D.setRotation(-rotation);
        float transformX = maskModel.transform.transformX / (liveWindowSize.x * 0.5F);
        float transformY = -maskModel.transform.transformY / (liveWindowSize.y * 0.5F);
        transform2D.setTranslation(new MeicamPosition2D(transformX, transformY));
        transform2D.setScale(new MeicamPosition2D(maskModel.transform.scaleX, maskModel.transform.scaleY));
        rotation = 0;
        //第一个点 First point
        PointF prePoint = new PointF(center.x - maskWidth * 0.5f, center.y + maskHeight * 0.5f - cornerRadius);
        PointF curPoint = new PointF(center.x - maskWidth * 0.5f, center.y - maskHeight * 0.5f + cornerRadius);
        PointF nextPoint = new PointF(center.x - maskWidth * 0.5f, center.y - maskHeight * 0.5f + controlPointDis);
        maskRegionInfoAddPoints(regionInfo, getPointByAngle(prePoint, center, rotation), getPointByAngle(curPoint, center, rotation), getPointByAngle(nextPoint, center, rotation));

        //第二点 Second point
        nextPoint = curPoint;
        curPoint = prePoint;
        prePoint = new PointF(center.x - maskWidth * 0.5f, center.y + maskHeight * 0.5f - controlPointDis);
        maskRegionInfoAddPoints(regionInfo, getPointByAngle(prePoint, center, rotation), getPointByAngle(curPoint, center, rotation), getPointByAngle(nextPoint, center, rotation));

        nextPoint = new PointF(center.x - maskWidth * 0.5f + controlPointDis, center.y + maskHeight * 0.5f);
        curPoint = new PointF(center.x - maskWidth * 0.5f + cornerRadius, center.y + maskHeight * 0.5f);
        prePoint = new PointF(center.x + maskWidth * 0.5f - cornerRadius, center.y + maskHeight * 0.5f);
        maskRegionInfoAddPoints(regionInfo, getPointByAngle(prePoint, center, rotation), getPointByAngle(curPoint, center, rotation), getPointByAngle(nextPoint, center, rotation));

        nextPoint = curPoint;
        curPoint = prePoint;
        prePoint = new PointF(center.x + maskWidth * 0.5f - controlPointDis, center.y + maskHeight * 0.5f);
        maskRegionInfoAddPoints(regionInfo, getPointByAngle(prePoint, center, rotation), getPointByAngle(curPoint, center, rotation), getPointByAngle(nextPoint, center, rotation));

        nextPoint = new PointF(center.x + maskWidth * 0.5f, center.y + maskHeight * 0.5f - controlPointDis);
        curPoint = new PointF(center.x + maskWidth * 0.5f, center.y + maskHeight * 0.5f - cornerRadius);
        prePoint = new PointF(center.x + maskWidth * 0.5f, center.y - maskHeight * 0.5f + cornerRadius);
        maskRegionInfoAddPoints(regionInfo, getPointByAngle(prePoint, center, rotation), getPointByAngle(curPoint, center, rotation), getPointByAngle(nextPoint, center, rotation));

        nextPoint = curPoint;
        curPoint = prePoint;
        prePoint = new PointF(center.x + maskWidth * 0.5f, center.y - maskHeight * 0.5f + controlPointDis);
        maskRegionInfoAddPoints(regionInfo, getPointByAngle(prePoint, center, rotation), getPointByAngle(curPoint, center, rotation), getPointByAngle(nextPoint, center, rotation));

        nextPoint = new PointF(center.x + maskWidth * 0.5f - controlPointDis, center.y - maskHeight * 0.5f);
        curPoint = new PointF(center.x + maskWidth * 0.5f - cornerRadius, center.y - maskHeight * 0.5f);
        prePoint = new PointF(center.x - maskWidth * 0.5f + cornerRadius, center.y - maskHeight * 0.5f);
        maskRegionInfoAddPoints(regionInfo, getPointByAngle(prePoint, center, rotation), getPointByAngle(curPoint, center, rotation), getPointByAngle(nextPoint, center, rotation));

        nextPoint = curPoint;
        curPoint = prePoint;
        prePoint = new PointF(center.x - maskWidth * 0.5f + controlPointDis, center.y - maskHeight * 0.5f);
        maskRegionInfoAddPoints(regionInfo, getPointByAngle(prePoint, center, rotation), getPointByAngle(curPoint, center, rotation), getPointByAngle(nextPoint, center, rotation));

        nvsMaskRegionInfo.addRegionInfo(regionInfo);
        return nvsMaskRegionInfo;
    }

    public static Path circlePath(PointF center, PointF size, float rotation) {
        float maskWidth = size.x;
        float maskHeight = size.y;
        Path path = new Path();

        RectF rectF = new RectF(center.x - maskWidth * 0.5f, center.y - maskHeight * 0.5f, center.x + maskWidth * 0.5f, center.y + maskHeight * 0.5f);
        path.addOval(rectF, Path.Direction.CW);
        path.moveTo(center.x, center.y);
        //这里添加一个圆 Add a circle here
        path.addCircle(center.x, center.y, centerPathRadius, Path.Direction.CW);
        path.close();
        return path;
    }

    public static MeicamMaskRegionInfo circleRegionInfo(PointF center, PointF maskSize, PointF assetSize, PointF liveWindrowSize, float rotation) {
        float widthPercent, heightPercent;
        widthPercent = maskSize.x * 1.0f / assetSize.x;
        heightPercent = maskSize.y * 1.0f / assetSize.y;
        center = mapViewToNormalized(center, liveWindrowSize, assetSize);
        //局部特效区域信息 Local special effects area information.
        MeicamMaskRegionInfo nvsMaskRegionInfo = new MeicamMaskRegionInfo();
        //设置类型Set type
        // 椭圆   MASK_REGION_TYPE_ELLIPSE2D
        // 多边形 MASK_REGION_TYPE_POLYGON
        MeicamMaskRegionInfo.RegionInfo regionInfo = new MeicamMaskRegionInfo.RegionInfo(NvsMaskRegionInfo.MASK_REGION_TYPE_ELLIPSE2D);
        //参数意义 Parameter Meaning:
        /*
         * 中心点坐标
         * 长半轴长 对应屏幕宽度的比例 [-1,1]区间
         * 短半轴长 对应屏幕高度度的比例 [-1,1]区间
         * 旋转角度
         */
        /*
         *Center point coordinates
         *The proportion of screen width corresponding to the length of the long half axis [-1,1] interval
         *The proportion of screen height corresponding to the length of the short half axis [-1,1] interval
         *Rotation angle
         */
        regionInfo.setEllipse2D(new MeicamMaskRegionInfo.Ellipse2D(new MeicamPosition2D(center.x, center.y), widthPercent, heightPercent, 0));
        MeicamMaskRegionInfo.Transform2D transform2D = new MeicamMaskRegionInfo.Transform2D();
        transform2D.setRotation(-rotation);
        transform2D.setAnchor(new MeicamPosition2D(center.x, center.y));
        regionInfo.setTransform2D(transform2D);
        nvsMaskRegionInfo.addRegionInfo(regionInfo);
        return nvsMaskRegionInfo;
    }

    public static Path startPath(PointF center, float width, float rotation) {
        MeicamMaskRegionInfo regionInfo = starRegionInfo(center, width, rotation);
        Path path = new Path();
        List<MeicamPosition2D> position2DS = regionInfo.getLocalRegionInfoArray().get(0).getPoints();
        if (position2DS.size() < 10) {
            return path;
        }
        MeicamPosition2D firstValue = position2DS.get(0);
        path.moveTo(firstValue.x, firstValue.y);
        for (MeicamPosition2D position2D : position2DS) {
            path.lineTo(position2D.x, position2D.y);
        }
        path.lineTo(firstValue.x, firstValue.y);
        path.addCircle(center.x, center.y, centerPathRadius, Path.Direction.CW);
        path.close();
        return path;
    }

    public static MeicamMaskRegionInfo starRegionInfo(PointF center, float width, float rotation) {
        //局部特效区域信息 Local special effects area information
        MeicamMaskRegionInfo nvsMaskRegionInfo = new MeicamMaskRegionInfo();
        //设置类型
        // 椭圆   MASK_REGION_TYPE_ELLIPSE2D
        // 多边形 MASK_REGION_TYPE_POLYGON
        // 贝塞尔曲线MASK_REGION_TYPE_CUBIC_CURVE+

        //Setting Type
        //Elliptical MASK_ REGION_ TYPE_ ELLIPSE2D
        //Polygonal MASK_ REGION_ TYPE_ POLYGON
        //Bézier curve MASK_ REGION_ TYPE_ CUBIC_ CURVE+
        MeicamMaskRegionInfo.RegionInfo regionInfo = new MeicamMaskRegionInfo.RegionInfo(NvsMaskRegionInfo.MASK_REGION_TYPE_POLYGON);

        //外圆
        float radius = width / 2.0f;
        float angel = (float) (Math.PI * 2 / 5);
        PointF[] outPoints = new PointF[5];
        //这里是获取五角星的五个定点的坐标点位置
        // Here are the coordinate points of the five fixed points of the pentagram.
        for (int i = 1; i < 6; i++) {
            float x = (float) (center.x - Math.sin(i * angel) * radius);
            float y = (float) (center.y - Math.cos(i * angel) * radius);
            outPoints[i - 1] = new PointF(x, y);
        }

        /// 越大越胖 The bigger, the fatter
        float radiusRate = 0.5f; //2/5
        //内圆 Inner circle
        float internalRadius = radius * radiusRate;
        float internalAngel = (float) (Math.PI * 2 / 5);
        PointF[] inPoints = new PointF[5];
        //这里是获取五角星的五个定点的坐标点位置
        // Here are the coordinate points of the five fixed points of the pentagram.
        for (int i = 1; i < 6; i++) {
            float x = (float) (center.x - Math.sin(i * internalAngel + Math.PI / 2 - Math.PI * 3 / 10) * internalRadius);
            float y = (float) (center.y - Math.cos(i * internalAngel + Math.PI / 2 - Math.PI * 3 / 10) * internalRadius);
            inPoints[i - 1] = new PointF(x, y);
        }

        //加入到一个集合中 一外一内的顺序
        // The order of adding one outside and one inside to a set.
        PointF[] allPoints = new PointF[10];
        for (int i = 0; i < 5; i++) {
            PointF out = getPointByAngle(outPoints[i], center, rotation);
            PointF in = getPointByAngle(inPoints[i], center, rotation);
            allPoints[i * 2] = out;
            allPoints[i * 2 + 1] = in;
        }
        List<MeicamPosition2D> position2DList = buildNvsPositionListFromPointFList(allPoints);

        regionInfo.setPoints(position2DList);
        nvsMaskRegionInfo.addRegionInfo(regionInfo);

        return nvsMaskRegionInfo;
    }

    public static Path heartPath(PointF center, float maskWidth, int angle) {
        float radius = maskWidth / 2;
        Path path = new Path();
        //通过三阶贝塞尔曲线绘制
        // Draw through the third order Bézier curve.
        PointF intersectionPoint = getPointByAngle(new PointF(center.x, center.y - radius * (2 * 1.0f / 6)), center, angle);
        path.moveTo(intersectionPoint.x, intersectionPoint.y);

        PointF prePoint = getPointByAngle(new PointF(center.x + 5 * 1.0f / 7 * radius, center.y - 0.8f * radius), center, angle);
        PointF curPoint = getPointByAngle(new PointF(center.x, center.y + radius), center, angle);
        PointF nextPoint = getPointByAngle(new PointF(center.x + 16 * 1.0f / 13 * radius, center.y + 0.1f * radius), center, angle);

        path.cubicTo(prePoint.x, prePoint.y, nextPoint.x, nextPoint.y, curPoint.x, curPoint.y);

        prePoint = getPointByAngle(new PointF(center.x - 16 * 1.0f / 13 * radius, center.y + 0.1f * radius), center, angle);
        curPoint = getPointByAngle(new PointF(center.x, center.y - radius * (2 * 1.0f / 6)), center, angle);
        nextPoint = getPointByAngle(new PointF(center.x - 5 * 1.0f / 7 * radius, center.y - 0.8f * radius), center, angle);
        path.cubicTo(prePoint.x, prePoint.y, nextPoint.x, nextPoint.y, curPoint.x, curPoint.y);

        path.moveTo(center.x, center.y);
        //这里添加一个圆
        // Add a circle here
        path.addCircle(center.x, center.y, centerPathRadius, Path.Direction.CW);
        path.close();
        return path;
    }

    public static MeicamMaskRegionInfo heartRegionInfo(PointF center, float maskWidth, float rotation) {
        float radius = maskWidth / 2;
        //局部特效区域信息
        // Local special effects area information.
        MeicamMaskRegionInfo nvsMaskRegionInfo = new MeicamMaskRegionInfo();
        //设置类型
        // 椭圆   MASK_REGION_TYPE_ELLIPSE2D
        // 多边形 MASK_REGION_TYPE_POLYGON
        // 贝塞尔曲线MASK_REGION_TYPE_CUBIC_CURVE+

        //Setting Type
        //Elliptical MASK_ REGION_ TYPE_ ELLIPSE2D
        //Polygonal MASK_ REGION_ TYPE_ POLYGON
        //Bézier curve MASK_ REGION_ TYPE_ CUBIC_ CURVE+
        MeicamMaskRegionInfo.RegionInfo regionInfo = new MeicamMaskRegionInfo.RegionInfo(NvsMaskRegionInfo.MASK_REGION_TYPE_CUBIC_CURVE);

        PointF topIntersectionPoint = new PointF(center.x, center.y - radius * (2 * 1.0f / 6));
        PointF bottomIntersectionPoint = new PointF(center.x, center.y + radius);

        PointF prePoint = getPointByAngle(new PointF(center.x + 5 * 1.0f / 7 * radius, center.y - 0.8f * radius), center, rotation);
        PointF curPoint = getPointByAngle(topIntersectionPoint, center, rotation);
        PointF nextPoint = getPointByAngle(new PointF(center.x - 5 * 1.0f / 7 * radius, center.y - 0.8f * radius), center, rotation);


        PointF prePoint1 = getPointByAngle(new PointF(center.x - 16 * 1.0f / 13 * radius, center.y + 0.1f * radius), center, rotation);
        PointF curPoint1 = getPointByAngle(bottomIntersectionPoint, center, rotation);
        PointF nextPoint1 = getPointByAngle(new PointF(center.x + 16 * 1.0f / 13 * radius, center.y + 0.1f * radius), center, rotation);

        PointF[] pointFS = new PointF[]{curPoint, nextPoint, prePoint, curPoint1, nextPoint1, prePoint1};
        List<MeicamPosition2D> nvsPosition2DS = buildNvsPositionListFromPointFList(pointFS);

        regionInfo.setPoints(nvsPosition2DS);
        nvsMaskRegionInfo.addRegionInfo(regionInfo);
        return nvsMaskRegionInfo;
    }

    /**
     * 构建局部特效区域  多边形区域
     *
     * @param pointFList 点位集合
     * @return
     */
    private static MeicamMaskRegionInfo buildPolygonMaskRegionInfo(PointF[] pointFList) {
        List<MeicamPosition2D> nvsPosition2DS = buildNvsPositionListFromPointFList(pointFList);
        //局部特效区域信息
        //Local special effects area information.
        MeicamMaskRegionInfo meicamMaskRegionInfo = new MeicamMaskRegionInfo();
        //设置类型
        // 椭圆   MASK_REGION_TYPE_ELLIPSE2D
        // 多边形 MASK_REGION_TYPE_POLYGON
        //Setting Type
        //Elliptical MASK_ REGION_ TYPE_ ELLIPSE2D
        //Polygonal MASK_ REGION_ TYPE_ POLYGON
        MeicamMaskRegionInfo.RegionInfo regionInfo = new MeicamMaskRegionInfo.RegionInfo(NvsMaskRegionInfo.MASK_REGION_TYPE_POLYGON);

        regionInfo.setPoints(nvsPosition2DS);
        meicamMaskRegionInfo.addRegionInfo(regionInfo);
        return meicamMaskRegionInfo;
    }

    /**
     * 添加集合点
     * Add points to mask region info
     *
     * @param info the info
     * @param prePoint the pre point
     * @param curPoint the current point
     * @param nextPoint the next point
     */
    private static void maskRegionInfoAddPoints(MeicamMaskRegionInfo.RegionInfo info, PointF prePoint, PointF curPoint, PointF nextPoint) {

        PointF[] pointArray = new PointF[3];
        pointArray[0] = curPoint;
        pointArray[1] = nextPoint;
        pointArray[2] = prePoint;
        info.getPoints().addAll(buildNvsPositionListFromPointFList(pointArray));
    }

    /**
     * 转换点位集合为sdk需要的参数
     * Build nvs position list from pointf list
     * @return the list of MeicamPosition2D
     */
    private static List<MeicamPosition2D> buildNvsPositionListFromPointFList(PointF[] pointFList) {
        List<MeicamPosition2D> nvsPosition2DS = new ArrayList<>();
        if (null != pointFList && pointFList.length > 0) {
            for (PointF pointF : pointFList) {
                nvsPosition2DS.add(new MeicamPosition2D(pointF.x, pointF.y));
            }
        }
        return nvsPosition2DS;
    }

    /**
     * 转换点位集合为sdk需要的参数
     * Map view to normalized
     * @return the pointF
     */
    private static PointF mapViewToNormalized(PointF pointF, PointF previewRect, PointF size) {
        PointF liveWindowCenterPoint = new PointF(previewRect.x * 0.5f, previewRect.y * 0.5f);
        float xValue = (pointF.x - liveWindowCenterPoint.x) / (size.x * 0.5f);
        float yValue = -(pointF.y - liveWindowCenterPoint.y) / (size.y * 0.5f);
        return new PointF(xValue, yValue);
    }

    /**
     * 转换点位集合为sdk需要的参数
     * map view to normalized
     *
     * @return the MeicamPosition2Dg
     */
    private static MeicamPosition2D mapViewToNormalized(MeicamPosition2D pointF, PointF previewRect, PointF size) {
        PointF liveWindowCenterPoint = new PointF(previewRect.x * 0.5f, previewRect.y * 0.5f);
        float xValue = (pointF.x - liveWindowCenterPoint.x) / (size.x * 0.5f);
        float yValue = -(pointF.y - liveWindowCenterPoint.y) / (size.y * 0.5f);
        pointF.x = xValue;
        pointF.y = yValue;
        return pointF;
    }


    public static PointF boxMaskSize(NvMaskModel maskModel, PointF liveWindowSize, PointF assetResolution, PointF assetSize) {
        int maskType = maskModel.maskType;

        if (maskType == MaskZoomView.MaskType.NONE) {
            return null;
        } else if (maskType == MaskZoomView.MaskType.LINE) {
            float boxWidth = liveWindowSize.x * 2.0f;
            float rate = assetSize.x / assetResolution.x;
            boxWidth += maskModel.transform.transformX * rate * 2.0f;
            boxWidth += maskModel.transform.transformY * rate * 2.0f;
            return new PointF(boxWidth, 1.0f);
        } else if (maskType == MaskZoomView.MaskType.MIRROR) {
            float boxWidth = liveWindowSize.x * 2.0f;
            float rate = assetSize.x / assetResolution.x;
            boxWidth += maskModel.transform.transformX * rate * 2.0f;
            boxWidth += maskModel.transform.transformY * rate * 2.0f;
            return new PointF(boxWidth, assetSize.y * 0.5f * maskModel.transform.scaleX * maskModel.transform.viewScale);
        } else {
            float assetAspectRatio = assetResolution.x * 1.0f / assetResolution.y;
            float maskSquareWidth = assetAspectRatio >= 1 ? assetSize.y : assetSize.x;
            maskSquareWidth = maskType == MaskZoomView.MaskType.CIRCLE ? maskSquareWidth * maskModel.circleRate : maskSquareWidth;
            float maskSquareHeight = maskType == MaskZoomView.MaskType.RECT ? maskSquareWidth / maskModel.rectRate : maskSquareWidth;
            return new PointF(maskSquareWidth * maskModel.horizontalScale * maskModel.transform.scaleX * maskModel.transform.viewScale, maskSquareHeight * maskModel.verticalScale * maskModel.transform.scaleY * maskModel.transform.viewScale);
        }
    }

    public static Path textPath(PointF center, PointF maskSize) {
        float halfWidth = maskSize.x * 0.5f;
        float halfHeight = maskSize.y * 0.5f;

        PointF leftTop = new PointF(center.x - halfWidth, center.y - halfHeight);
        PointF rightTop = new PointF(center.x + halfWidth, center.y - halfHeight);
        PointF rightBottom = new PointF(center.x + halfWidth, center.y + halfHeight);
        PointF leftBottom = new PointF(center.x - halfWidth, center.y + halfHeight);
        Path path = new Path();
        path.moveTo(leftTop.x, leftTop.y);
        path.lineTo(rightTop.x, rightTop.y);
        path.lineTo(rightBottom.x, rightBottom.y);
        path.lineTo(leftBottom.x, leftBottom.y);
        path.lineTo(leftTop.x, leftTop.y);
        path.close();
        //添加中间圆圈 Add middle circle
        path.addCircle(center.x, center.y, centerPathRadius, Path.Direction.CW);
        return path;
    }

    /**
     * 装载mask点
     * Prepare mask region points.
     *
     * @param maskModel       the mask model
     * @param size            the mask size
     * @param liveWindowSize  the live window size
     * @param assetSize       the asset size
     * @param assetResolution the asset resolution
     */
    public static void prepareMaskRegionPoints(NvMaskModel maskModel, PointF size, PointF liveWindowSize, PointF assetSize, PointF assetResolution, Transform propertyTransform) {
        if (size == null) {
            return;
        }
        //装载点的时候不考虑属性特效的缩放,view绘制的时候需要考虑,所以要重新生成变量，防止size值改变影响view的绘制
        //The size of the property is not considered when loading the point,
        // but when drawing the view,
        // so you need to regenerate the variable to prevent the value of size from affecting the drawing of the view
        PointF newSize = new PointF(size.x, size.y);
        float transformModelScale = Math.abs(propertyTransform.scaleX);
        newSize.x /= transformModelScale;
        newSize.y /= transformModelScale;

        PointF maskCenter = new PointF(liveWindowSize.x * 0.5f, liveWindowSize.y * 0.5f);
        //处理镜像 导致移动方向相反Handling the mirror results in the opposite direction of movement
        float rotation = maskModel.transform.rotation;
        if (propertyTransform.scaleX < 0) {
            rotation *= -1;
            maskCenter.x -= maskModel.transform.transformX;
        } else {
            maskCenter.x += maskModel.transform.transformX;
        }
        maskCenter.y += maskModel.transform.transformY;
        int maskType = maskModel.maskType;
        if (maskType == MaskZoomView.MaskType.LINE) {
            maskModel.regionInfo = NvMaskHelper.lineRegionInfo(maskCenter, liveWindowSize, rotation);
        } else if (maskType == MaskZoomView.MaskType.MIRROR) {
            maskModel.regionInfo = NvMaskHelper.mirrorRegionInfo(maskCenter, newSize, rotation);
        } else if (maskType == MaskZoomView.MaskType.RECT) {
            maskModel.regionInfo = NvMaskHelper.rectRegionInfo(liveWindowSize, assetSize, newSize, maskModel, rotation);
        } else if (maskType == MaskZoomView.MaskType.CIRCLE) {
            maskModel.regionInfo = NvMaskHelper.circleRegionInfo(maskCenter, newSize, assetSize, liveWindowSize, rotation);
        } else if (maskType == MaskZoomView.MaskType.STAR) {
            maskModel.regionInfo = NvMaskHelper.starRegionInfo(maskCenter, newSize.x, rotation);
        } else if (maskType == MaskZoomView.MaskType.HEART) {
            maskModel.regionInfo = NvMaskHelper.heartRegionInfo(maskCenter, newSize.x, rotation);
        }
        List<MeicamPosition2D> position2DS = maskModel.regionInfo.getLocalRegionInfoArray().get(0).getPoints();
        for (MeicamPosition2D position2D : position2DS) {
            mapViewToNormalized(position2D, liveWindowSize, assetSize);
        }
    }

    /**
     * 计算旋转角度后的坐标
     * Get point by angle
     *
     * @param p       the p point 目标点坐标
     * @param pCenter the p center 旋转中心点坐标，锚点
     * @param angle   the angle 旋转角度
     * @return Coordinate points corresponding to rotation angle 旋转角度后对应的坐标点
     */
    public static PointF getPointByAngle(PointF p, PointF pCenter, float angle) {


//        float l = (float) ((angle * Math.PI) / 180);
//
//
//        //sin/cos value
//        float cosv = (float) Math.cos(l);
//
//        float sinv = (float) Math.sin(l);
//
//        // calc new point
//        float newX = (float) ((p.x - pCenter.x) * cosv - (p.y - pCenter.y) * sinv + pCenter.x);
//        float newY = (float) ((p.x - pCenter.x) * sinv + (p.y - pCenter.y) * cosv + pCenter.y);
////        //Log.e(TAG,"X = "+newX +"  Y ="+newX +"  angle="+angle);
        return transformData(p, pCenter, 1.0f, angle);
    }

    public static PointF transformData(PointF point, PointF centerPoint, float scale, float degree) {
        float[] src = new float[]{point.x, point.y};
        PointF pointF = new PointF(point.x, point.y);
        Matrix matrix = new Matrix();
        matrix.setRotate(degree, centerPoint.x, centerPoint.y);
        matrix.mapPoints(src);
        matrix.setScale(scale, scale, centerPoint.x, centerPoint.y);
        matrix.mapPoints(src);
        pointF.x = Math.round(src[0]);
        pointF.y = Math.round(src[1]);
        return pointF;
    }


    public static void transformPath(Path path, PointF centerPoint, float degree) {
        Matrix matrix = new Matrix();
        matrix.setRotate(degree, centerPoint.x, centerPoint.y);
        path.transform(matrix);
    }

    public static void transformRect(RectF rect, PointF centerPoint, float degree) {
        Matrix matrix = new Matrix();
        matrix.setRotate(degree, centerPoint.x, centerPoint.y);
        matrix.mapRect(rect);
    }

    public static Bitmap transformBitmap(Bitmap bitmap, float degree) {
        Matrix matrix = new Matrix();
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();
        matrix.setRotate(degree, width / 2.0f, height / 2.0f);
        return Bitmap.createBitmap(bitmap, 0, 0, width, height, matrix, true);
    }

}
