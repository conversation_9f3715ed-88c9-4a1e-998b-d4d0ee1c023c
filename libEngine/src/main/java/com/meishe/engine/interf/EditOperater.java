package com.meishe.engine.interf;

import android.content.Context;

import com.meishe.engine.bean.ClipInfo;
import com.meishe.engine.bean.MeicamAudioClip;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamVideoClip;

/**
 * 编辑Timeline对象的方法
 * The edit operator
 */
public interface EditOperater {
    /**
     * 设置当前timeline
     * Set current timeline
     * <p>
     * * @param timeline
     *
     * @param timeline the timeline
     */
    void setCurrentTimeline(MeicamTimeline timeline);

    /**
     * 获取当前timeline
     * Get current timeline
     *
     * @return current timeline
     */
    MeicamTimeline getCurrentTimeline();

    /**
     * 获取编辑的VideoClip
     * Get edit video clip
     *
     * @param timeStamp  当前timeline的时间戳
     * @param trackIndex the track index
     * @return edit video clip
     */
    MeicamVideoClip getEditVideoClip(long timeStamp, int trackIndex);

    /**
     * 获取当前timeline的时间戳
     * Get current timeline position
     *
     * @return The long
     */
    long getCurrentTimelinePosition();

    /**
     * 改变timeline的速度
     * Change timeline speed
     *
     * @param speed 速度
     */
    void changeTimelineSpeed(float speed);

    /**
     * 改变单个Clip的速度
     * Change clip speed
     *
     * @param speed The speed
     */
    void changeClipSpeed(float speed);

    /**
     * 剪切clip
     * Cut clip
     *
     * @param clipInfo   The clip info
     * @param trackIndex The track index
     * @return the result code
     */
    int cutClip(MeicamVideoClip clipInfo, int trackIndex);

    /**
     * 改变音量
     * Change volume
     *
     * @param volume 音量
     */
    void changeVolume(float volume);


    /**
     * 改变不透明度
     * Change opacity.
     *
     * @param meicamVideoClip the meicam video clip
     * @param opacity         the opacity
     */
    void changeOpacity(MeicamVideoClip meicamVideoClip, float opacity);


    /**
     * 删除clip
     * Delete clip int.
     *
     * @param clipInfo   the clip info
     * @param trackIndex the track index
     * @param isPip      the is pip
     * @return the int
     */
    @Deprecated
    int deleteClip(MeicamVideoClip clipInfo, int trackIndex, boolean isPip);

    /**
     * 变声
     * Change voice
     *
     * @param fxId the fx id
     */
    void changeVoice(String fxId);

    /**
     * 切换画幅比例
     * Change ratio
     *
     * @param type The type
     */
    void changeRatio(int type);


    /**
     * 添加组合字幕
     * Add compound caption.
     *
     * @param clipInfo the clip info
     * @param uuid     the uuid
     */
    void addCompoundCaption(ClipInfo<?> clipInfo, String uuid);

    /**
     * 恢复timeline
     * Restore timeline.
     *
     * @param operateData the operate data
     * @param timeline    the timeline
     */
    void restoreTimeline(String operateData, MeicamTimeline timeline);


    /**
     * 应用全部图片背景，包括纯色背景，因为纯色也是以图片的形式添加的
     * Apply all image background
     *
     * @param imagePath The image path图片的路径
     * @param type      the type 类型
     */
    void applyAllImageBackground(String imagePath, int type);


    /**
     * 更新背景
     * Update background.
     *
     * @param type the type 背景类型
     */
    void updateBackground(int type);


    /**
     * 删除背景
     * Delete background.
     *
     * @param editVideoClip the edit video clip
     * @param isDelete      the is delete
     */
    void deleteBackground(MeicamVideoClip editVideoClip, boolean isDelete);


    /**
     * 更新颜色或者模糊背景
     * Update blur and color background.
     *
     * @param editVideoClip the edit video clip
     * @param value         the value value值
     * @param type          the type 类型
     */
    void updateBlurAndColorBackground(MeicamVideoClip editVideoClip, String value, int type);


    /**
     * 应用全部模糊背景
     * Apply all blur background.
     *
     * @param value the value value值
     * @param type  the type 类型
     */
    void applyAllBlurBackground(String value, int type);

    /**
     * 刷新数据
     * Refresh data.
     *
     * @param trackIndex the track index 轨道index
     * @param type       the type 轨道类型
     */
    void refreshData(int trackIndex, String type);

    /**
     * 音频分割
     * Audio editcut clip
     *
     * @param context the context
     * @return the int
     */
    int audioEditCutClip(Context context);

    /**
     * 音频变速
     * Change audio speed
     *
     * @param speed         the speed
     * @param isChangeVoice the is change voice
     */
    void audioEditChangeClipSpeed(float speed, boolean isChangeVoice);

    /**
     * 音频音量
     * Change audio volume
     *
     * @param volume the volume
     */
    void audioEditChangeVolume(float volume);

    /**
     * Audio edit delete clip.
     * 删除音频
     *
     * @param audioClip the audio clip
     */
    void audioEditDeleteClip(MeicamAudioClip audioClip);

    /**
     * Audio edit copy clip.
     * 复制音频
     *
     * @param context the context
     */
    void audioEditCopyClip(Context context);

    /**
     * 音频变声
     * Change audio voice
     *
     * @param fxId the fx id 变声特效id
     */
    void audioEditChangeVoice(String fxId);


    /**
     * 视频变声
     * Video edit change voice.
     *
     * @param videoClip the video clip 视频clip
     * @param fxId      the fx id 变声特效id
     */
    void videoEditChangeVoice(MeicamVideoClip videoClip, String fxId);

    /**
     * 音转场
     * Audio edit transition
     *
     * @param fadeIn  the fade in 淡入时长
     * @param fadeOut the fade out 淡出时长
     */
    void audioEditTransition(long fadeIn, long fadeOut);


    /**
     * 删除特效
     * Delete effect boolean.
     *
     * @param clip the clip data 特效clip
     * @return the boolean 是否删除成功
     */
    boolean deleteEffect(IClip clip);

    /**
     * 复制特效
     * Copy effect int.
     *
     * @param clip the clip data
     * @param context   the context
     * @return the int
     */
    int copyEffect(IClip clip, Context context);

    /**
     * 修改视频clip上的滤镜
     * Change clip filter.
     *
     * @param videoClip the video clip 视频clip
     * @param intensity the intensity 滤镜强度
     */
    void changeClipFilter(MeicamVideoClip videoClip, float intensity);


    /**
     * 删除clip上的滤镜
     * Remove clip filter.
     *
     * @param videoClip the video clip 视频clip
     */
    void removeClipFilter(MeicamVideoClip videoClip);

    /**
     * 应用滤镜到所有clip
     * Apply all filter.
     *
     * @param videoClip the video clip 视频clip
     */
    void applyAllFilter(MeicamVideoClip videoClip);


    /**
     *
     * Delete audio clip
     * <p>
     * 删除
     *
     * @param audioClip the audio clip 音频clip
     * @return boolean 是否删除成功
     */
    boolean deleteAudioClip(MeicamAudioClip audioClip);
}
