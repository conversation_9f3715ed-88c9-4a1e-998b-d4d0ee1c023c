package com.meishe.myvideo.view.pop;

import android.content.Context;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.meishe.myvideo.R;
import com.meishe.third.pop.XPopup;
import com.meishe.third.pop.core.CenterPopupView;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> Li<PERSON><PERSON><PERSON>hou
 * @CreateDate :2021/6/29 15:56
 * @Description :取消智能抠像弹窗 Cancel smart keyer pop
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CancelSmartKeyerPop extends CenterPopupView {
    private TextView mTvConfirm;
    private TextView mTvCancel;
    private EventListener mListener;

    public CancelSmartKeyerPop(@NonNull Context context) {
        super(context);
    }

    public static CancelSmartKeyerPop create(EventListener listener, Context context) {
        CancelSmartKeyerPop cancelSmartKeyerPop = (CancelSmartKeyerPop) new XPopup.Builder(context)
                .dismissOnBackPressed(false)
                .dismissOnTouchOutside(false)
                .asCustom(new CancelSmartKeyerPop(context));
        cancelSmartKeyerPop.setEventListener(listener);
        return cancelSmartKeyerPop;
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.pop_cancel_smart_keyer;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        mTvConfirm = findViewById(R.id.tv_confirm);
        mTvCancel = findViewById(R.id.tv_cancel);
        mTvConfirm.setOnClickListener(mOnClick);
        mTvCancel.setOnClickListener(mOnClick);
    }

    /**
     * 设置事件监听
     * Set event listener
     *
     * @param listener the listener监听
     */
    public void setEventListener(EventListener listener) {
        mListener = listener;
    }

    /**
     * 事件监听接口
     * The event listener interface
     */
    public interface EventListener {
        /**
         * 结果
         * Result
         *
         * @param confirm true confirm确定，false cancel取消
         */
        void onResult(boolean confirm);
    }

    private OnClickListener mOnClick = new OnClickListener() {
        @Override
        public void onClick(View v) {
            if (v.getId() == mTvCancel.getId()) {
                if (mListener != null) {
                    mListener.onResult(false);
                }
                dismiss();
            } else if (v.getId() == mTvConfirm.getId()) {
                mListener.onResult(true);
                dismiss();
            }
        }
    };
}
