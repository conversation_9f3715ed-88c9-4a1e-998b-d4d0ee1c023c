package com.meishe.business.assets.iview;

import com.meishe.base.model.IBaseView;
import com.meishe.engine.asset.bean.AssetInfo;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/1/8 17:39
 * @Description :资源文件view接口定义 The assets view interface definition
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface AssetsView extends IBaseView {
    /**
     * Return true if the view is active.
     * view是否是活动的
     *
     * @return Is the view active？ true：yes；false:no
     */
    boolean isActive();

    /**
     * On new data back
     * 首次加载数据返回
     *
     * @param list       the data list
     * @param subType    请求的次类型 the sub type
     * @param needUpdate Is need update or not.
     */
    void onNewDataBack(List<AssetInfo> list, int subType, boolean needUpdate);

    /**
     * On more data back
     * 加载更多数据返回
     *
     * @param list       the data list
     * @param subType    请求的次类型 the sub type
     * @param needUpdate Is need update or not.
     */
    void onMoreDataBack(List<AssetInfo> list, int subType, boolean needUpdate);

    /**
     * On request data error
     * 获取请求失败
     *
     * @param subType    请求的次类型 the sub type
     * @param needUpdate Is need update or not.
     */
    void onDataError(int subType, boolean needUpdate);

    /**
     * Return the item count
     * 返回item数量
     *
     * @return the item count
     */
    int getItemCount();

    /**
     * On download progress update
     * 下载进度刷新
     *
     * @param position the item position
     */
    void onDownloadProgress(int position);

    /**
     * On download finish
     * 下载完成
     *
     * @param position  the item position
     * @param assetInfo 资源文件信息 the asset information
     */
    void onDownloadFinish(int position, AssetInfo assetInfo);

    /**
     * On download error
     * 下载失败
     *
     * @param position the item position
     */
    void onDownloadError(int position);
}
