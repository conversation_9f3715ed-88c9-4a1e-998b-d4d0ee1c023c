<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp_px_168"
    android:layout_height="@dimen/dp_px_168">

    <ImageView
        android:id="@+id/iv_cover"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_px_24"
        android:contentDescription="@null" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_51"
        android:layout_gravity="bottom"
        android:background="@color/text_bg_color"
        android:gravity="center"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_30" />
</FrameLayout>