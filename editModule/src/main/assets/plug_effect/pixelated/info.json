[{"name": "纹理虚化", "enName": "cameraDistortion", "plugName": "Emptiness", "coverPath": "file:///android_asset/plug_effect/pixelated/emptiness/emptiness.png", "plugDesc": "可对图像按纹理大小进行虚化效果", "plugDescEn": "Image can be blurred by texture size effect", "effectPath": "plug_effect/pixelated/emptiness/info.json"}, {"name": "马赛克", "enName": "mosaic", "plugName": "Mosaic", "coverPath": "file:///android_asset/plug_effect/pixelated/mosaic/mosaic.png", "plugDesc": "可对图像进行网格像素化处理，并调整网格大小", "plugDescEn": "The image can be pixelated and the mesh size can be adjusted", "effectPath": "plug_effect/pixelated/mosaic/info.json"}, {"name": "色度噪点", "enName": "noise", "plugName": "Noise", "coverPath": "file:///android_asset/plug_effect/pixelated/noise/noise.png", "plugDesc": "可对图像的添加外部纹理图来实现噪波纹理效果", "plugDescEn": "External texture map can be added to the image to achieve noise ripple effect", "effectPath": "plug_effect/pixelated/noise/info.json"}, {"name": "飞点", "enName": "flyingDot", "plugName": "Flying Dot", "coverPath": "file:///android_asset/plug_effect/pixelated/flyingDot/flyingDot.png", "plugDesc": "可对图像添加漩涡效果", "plugDescEn": "You can add a vortex effect to the image", "effectPath": "plug_effect/pixelated/flyingDot/info.json"}]